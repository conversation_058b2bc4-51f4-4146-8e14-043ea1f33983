package com.estone.test;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.fastjson.JSON;
import com.estone.checkin.service.WhCheckInService;
import com.estone.common.util.*;
import com.estone.common.util.model.ApiResult;
import com.estone.common.util.model.ResultModel;
import com.estone.elasticsearch.model.EsSaleAccount;
import com.estone.elasticsearch.service.EsSaleAccountService;
import com.estone.sku.bean.WhSku;
import com.estone.sku.bean.WhSkuColorSize;
import com.estone.sku.bean.WhSkuQueryCondition;
import com.estone.sku.service.WhSkuService;
import com.estone.system.param.bean.SystemParam;
import com.estone.transfer.bean.TransferStock;
import com.estone.transfer.bean.TransferStockQueryCondition;
import com.estone.transfer.service.TransferStockService;
import com.estone.warehouse.bean.WhStock;
import com.estone.warehouse.bean.WhStockQueryCondition;
import com.estone.warehouse.service.WhAllocateLocationRuleService;
import com.estone.warehouse.service.WhStockService;
import com.estone.warehouse.util.EasyExcelUtils;
import com.whq.tool.component.Pager;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;

import javax.annotation.Resource;
import java.io.*;
import java.sql.Date;
import java.sql.*;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
public class TestJunit extends BaseJunit {

    @Resource
    private WhSkuService whSkuService;

    @Resource
    private EsSaleAccountService esSaleAccountService;
    @Resource
    private WhAllocateLocationRuleService  whAllocateLocationRuleService;
    @Resource
    private WhCheckInService whCheckInService;
    @Resource
    private WhStockService whStockService;
    @Resource
    private TransferStockService transferStockService;


    @Test
    public void ttt2() {
        String s = whAllocateLocationRuleService.matchLocation("6EE412421-BKW");
        System.out.println("===============================");
        System.out.println(s);
        //String a = whAllocateLocationRuleService.matchLocation("2S41072-W", null);
        System.out.println("===============================");
        //System.out.println(a);
    }

    /**
     * 修改最新上架时间
     * @throws Exception
     */
    @Test
    public void modifyUpTime() throws Exception {
        OutputStream outputStream = new FileOutputStream("D:\\download\\modifyUpTime.sql");
        OutputStream noneCheckInCreate = new FileOutputStream("D:\\download\\noneCheckInCreate.sql");
        OutputStream noneCheckInUpdate = new FileOutputStream("D:\\download\\noneCheckInUpdate.sql");
        int page = 1, size = 2000;
        List<WhStock> whStocks = null;
        do {
            Pager pager = new Pager(page, size);
            WhStockQueryCondition stockQuery = new WhStockQueryCondition();
            whStocks = whStockService.queryWhStocks(stockQuery, pager);
            if (CollectionUtils.isEmpty(whStocks)) {
                break;
            }
            String skus = whStocks.stream().map(c -> c.getSku()).distinct().collect(Collectors.joining("','"));

            List<Map<String, Object>> maps = DbTemplateUtils.executeSqlScript("select cii.sku, cii.sku_id, max(ci.up_time) as up_time from wh_check_in ci inner join wh_check_in_item cii on ci.in_id = cii.in_id  " +
                    "where sku in ('"+skus+"') and ci.up_time is not null and ci.status = 13 and exception_type in (1,3) " +
                    "group by sku, sku_id");
            if (CollectionUtils.isNotEmpty(maps)) {
                List<String> sqls = new ArrayList<>();
                Map<String, Timestamp> timeMap = new HashMap<>(maps.size() * 2);
                for (Map<String, Object> map : maps) {
                    Timestamp upTime = (Timestamp) map.get("up_time");
                    String sku = (String) map.get("sku");
                    Object sku_id =  map.get("sku_id");
                    if (sku_id != null){
                        timeMap.put(sku + sku_id, upTime);
                    } else {
                        timeMap.put(sku, upTime);
                    }
                }
                List<Integer> noneTimeIds = new ArrayList<>();
                for (WhStock stock : whStocks) {
                    Timestamp timestamp = timeMap.get(stock.getSku() + stock.getId());
                    if (timestamp == null) {
                        timestamp = timeMap.get(stock.getSku());
                        if (timestamp == null) {
                            noneTimeIds.add(stock.getId());
                            continue;
                        }
                    }
                    sqls.add("update frozen_stock set check_in_up_time = '"+DateUtils.formatDate(timestamp, "yyyy-MM-dd HH:mm:ss")+"' where stock_id = " + stock.getId() + ";");
                }
                if (CollectionUtils.isNotEmpty(sqls)) {
                    IOUtils.writeLines(sqls, "\n", outputStream);
                }
                if (CollectionUtils.isNotEmpty(noneTimeIds)) {
                    List<Map<String, Object>> maps1 = DbTemplateUtils.executeSqlScript("select stock_id from frozen_stock where stock_id in (" + StringUtils.join(noneTimeIds, ",") + ")");
                    List<Integer> existIds = Optional.ofNullable(maps1).orElse(new ArrayList<>()).stream().map(c -> (Integer)c.get("stock_id")).collect(Collectors.toList());
                    List<String> noneCheckInCreateSql = new ArrayList<>();
                    List<String> noneCheckInUpdateSql = new ArrayList<>();
                    List<Integer> addIds = new ArrayList<>();
                    for (Integer id : noneTimeIds) {
                        if (!existIds.contains(id)){
                            addIds.add(id);
                        }
                    }
                    noneCheckInUpdateSql.add("update frozen_stock set check_in_up_time = (select creation_date from wh_stock where id = frozen_stock.stock_id ) where stock_id in (" + StringUtils.join(existIds, ",") + ");");
                    IOUtils.writeLines(noneCheckInUpdateSql, "\n", noneCheckInUpdate);
                    noneCheckInCreateSql.add("insert into frozen_stock (sku, stock_id, check_in_up_time, creation_date, last_update_date) select sku, id, creation_date, now(), now() from wh_stock where id in ("+StringUtils.join(addIds, ",")+");");
                    IOUtils.writeLines(noneCheckInCreateSql, "\n", noneCheckInCreate);
                }
            }
            page++;
        } while (whStocks.size() >= size);

        int page2 = 1, size2 = 2000;
        List<TransferStock> transStocks = null;
        do {
            Pager pager = new Pager(page2, size2);
            TransferStockQueryCondition stockQuery = new TransferStockQueryCondition();
            transStocks = transferStockService.queryTransferStocks(stockQuery, pager);
            if (CollectionUtils.isEmpty(transStocks)) {
                break;
            }
            String skus = transStocks.stream().map(c -> c.getSku()).distinct().collect(Collectors.joining("','"));

            List<Map<String, Object>> maps = DbTemplateUtils.executeSqlScript("select cii.sku, cii.sku_id, max(ci.up_time) as up_time, pd.account_number from wh_check_in ci inner join wh_check_in_item cii on ci.in_id = cii.in_id  " +
                    "inner join wh_fba_purchase_data pd on ci.purchase_order_no = pd.purchaseOrderNo and cii.sku = pd.sku " +
                    "where cii.sku in ('"+skus+"') and ci.up_time is not null and ci.status = 13 and exception_type = 2 " +
                    "group by cii.sku, cii.sku_id, pd.account_number");
            if (CollectionUtils.isNotEmpty(maps)) {
                List<String> sqls = new ArrayList<>();
                Map<String, Timestamp> timeMap = new HashMap<>(maps.size() * 2);
                for (Map<String, Object> map : maps) {
                    Timestamp upTime = (Timestamp) map.get("up_time");
                    String sku = (String) map.get("sku");
                    Object sku_id =  map.get("sku_id");
                    String accountNumber = (String) Optional.ofNullable(map.get("account_number")).orElse("");
                    if (sku_id != null){
                        timeMap.put(sku + accountNumber + sku_id, upTime);
                    } else {
                        timeMap.put(sku + accountNumber, upTime);
                    }
                }
                List<Integer> noneTimeIds = new ArrayList<>();
                for (TransferStock transStock : transStocks) {
                    Timestamp timestamp = timeMap.get(transStock.getSku() + transStock.getStore() + transStock.getStockId());
                    if (timestamp == null) {
                        timestamp = timeMap.get(transStock.getSku() + transStock.getStore());
                        if (timestamp == null) {
                            timestamp = timeMap.get(transStock.getSku() + transStock.getStockId());
                            if (timestamp == null) {
                                timestamp = timeMap.get(transStock.getSku());
                                if (timestamp == null) {
                                    noneTimeIds.add(transStock.getId());
                                    continue;
                                }
                            }
                        }
                    }
                    sqls.add("update transfer_stock_time_relation set last_up_time = '"+DateUtils.formatDate(timestamp, "yyyy-MM-dd HH:mm:ss")+"' where stock_id = " + transStock.getId() + ";");
                }
                if (CollectionUtils.isNotEmpty(sqls)) {
                    IOUtils.writeLines(sqls, "\n", outputStream);
                }
                if (CollectionUtils.isNotEmpty(noneTimeIds)) {
                    List<Map<String, Object>> maps1 = DbTemplateUtils.executeSqlScript("select stock_id from transfer_stock_time_relation where stock_id in (" + StringUtils.join(noneTimeIds, ",") + ")");
                    List<Integer> existIds = Optional.ofNullable(maps1).orElse(new ArrayList<>()).stream().map(c -> (Integer)c.get("stock_id")).collect(Collectors.toList());
                    List<String> noneCheckInCreateSql = new ArrayList<>();
                    List<String> noneCheckInUpdateSql = new ArrayList<>();
                    List<Integer> addIds = new ArrayList<>();
                    for (Integer id : noneTimeIds) {
                        if (!existIds.contains(id)){
                            addIds.add(id);
                        }
                    }
                    noneCheckInUpdateSql.add("update transfer_stock_time_relation set last_up_time = (select creation_date from wh_transfer_stock where id = transfer_stock_time_relation.stock_id ) where stock_id in (" + StringUtils.join(existIds, ",") + ");");
                    IOUtils.writeLines(noneCheckInUpdateSql, "\n", noneCheckInUpdate);
                    noneCheckInCreateSql.add("insert into transfer_stock_time_relation (stock_id, last_up_time) select id, creation_date from wh_transfer_stock where id in ("+StringUtils.join(addIds, ",")+");");
                    IOUtils.writeLines(noneCheckInCreateSql, "\n", noneCheckInCreate);
                }
            }
            page2++;
        } while (transStocks.size() >= size2);

        outputStream.close();
        noneCheckInUpdate.close();
        noneCheckInCreate.close();
    }

    @Test
    public void testtt() throws Exception {
        InputStream inputStream = new FileInputStream("D:\\download\\无物流信息.xlsx");
        ResultModel<String> items = POIUtils.readExcel(new String[]{"平台单号"}, inputStream, row -> {
            String s = POIUtils.cellValue2Str(row.getCell(0));
            return s;
        }, false);
        Class.forName("com.mysql.jdbc.Driver");
        Connection connection = DriverManager.getConnection("****************************************************************************************************************************************", "readonly", "!QAZxsw2");
        Statement statement = connection.createStatement();
        // 1. 查询出log7有海外退件的数据
        Map<String,String>platMap = new HashMap<>();
        String platId = items.getList().stream().distinct().collect(Collectors.joining("','"));
        ResultSet log4ApvResult = statement.executeQuery(" select distinct log4.unique_id,apv.platform_order_id,apv.apv_no, apv.tracking_number from wh_apv apv" +
                                                        " inner join wh_unique_sku_log_4 log4 on apv.apv_no = log4.order_no " +
                                                        " where apv.platform_order_id in ('" + platId + "') ");
        Map<String, ApvInfo> apvMap = new HashMap<>();
        List<String> uniqueIdList = new ArrayList<>();
        List<String>apvNoList = new ArrayList<>();
        while(log4ApvResult.next()) {
            ApvInfo apv  = new ApvInfo();
            String unique_id = log4ApvResult.getString("unique_id");
            String apv_no = log4ApvResult.getString("apv_no");
            apvNoList.add(apv_no);
            uniqueIdList.add(unique_id);
            platMap.put(unique_id + apv_no, log4ApvResult.getString("platform_order_id"));
            apv.unique_id = unique_id;
            apv.platform_order_id = log4ApvResult.getString("platform_order_id");
            apv.tracking_number = log4ApvResult.getString("tracking_number");
            apv.apv_no = apv_no;
            apvMap.put(unique_id + apv_no, apv);
        }
        String ungueIdstr = uniqueIdList.stream().distinct().collect(Collectors.joining("','"));

        //2.根据Uniqve_1d查询出log7有海外退款的数据
        Map<String, Date> log7Map = new HashMap<>();
//        String log7Sql = "select unique_id,max(creation_date)as creation_date from wh_unique_sku_log_7 where unique_id in ('aaaaaa')group by unique_id";
        String log7Sql = "select unique_id,max(creation_date)as creation_date from wh_unique_sku_log_7 where unique_id in ('aaaaaa')group by unique_id";
        ResultSet Log7Result = statement.executeQuery(log7Sql.replace("aaaaaa",ungueIdstr));
        while (Log7Result.next()) {
            String unique_id = Log7Result.getString("unique_id");
            Date creation_date = Log7Result.getDate("creation_date");
            log7Map.put(unique_id, creation_date);
        }

        //3.根据log7.uniqve._ic查询Log4表
        String uniqueListstr = new ArrayList<>(log7Map.keySet()).stream().collect(Collectors.joining("','"));
        String apvNoListstr = apvNoList.stream().collect(Collectors.joining("','"));
        String stats = " select unique_id, max(creation_date) as creation_date, order_no from wh_unique_sku_log_4 where unique_id in ('aaaaaa') and order_no in ('apvNoList') group by unique_id, order_no ";
        ResultSet log4Result = statement.executeQuery(stats.replace("aaaaaa",uniqueListstr).replace("apvNoList",apvNoListstr));
        List<UniqueSku>log4List = new ArrayList<>();
        while (log4Result.next()) {
            UniqueSku sku = new UniqueSku();
            sku.apvNo = log4Result.getString("order_no");
            sku.date = log4Result.getDate("creation_date");
            sku.uniqueId = log4Result.getString("unique_id");
            log4List.add(sku);
        }
        Set<String> set = new HashSet<>();
        List<ApvInfo> apvList = new ArrayList<>();
        for (UniqueSku sku : log4List) {
            Date log7date = log7Map.get(sku.uniqueId);
            if(log7date != null && log7date.after(sku.getDate())){
                ApvInfo apvInfo = apvMap.get(sku.uniqueId + sku.apvNo);
                boolean add = set.add(apvInfo.apv_no + apvInfo.platform_order_id);
                apvInfo.returnDate = log7date;
                if(add) {
                    apvList.add(apvInfo);
                }
            }
        }
        List<List<String>> excelL = new ArrayList<>();
        for (ApvInfo apv : apvList) {
            List<String> downloadList = new ArrayList<String>();
            downloadList.add(POIUtils.transferObj2Str(apv.apv_no));
            downloadList.add(POIUtils.transferObj2Str(apv.platform_order_id));
            downloadList.add(POIUtils.transferObj2Str(apv.tracking_number));
            downloadList.add(POIUtils.transferObj2Str(apv.returnDate));
            excelL.add(downloadList);
        }
        ExcelWriter build = EasyExcel.write("无物流信息-"+ LocalDate.now().toString()+".xlsx").head(EasyExcelUtils.getHead(new String[]{"YSTN", "平台单号", "追踪号", "退仓时间"})).build();
        WriteSheet writeSheet = EasyExcel.writerSheet("第1页").build();
        build.write(excelL, writeSheet);
        //关流
        if (build != null) {
            build.finish();
        }
    }

    @Test
    public void testt22() throws Exception {
        Class.forName("com.mysql.jdbc.Driver");
        Connection connection = DriverManager.getConnection("****************************************************************************************************************************************", "readonly", "!QAZxsw2");
        Statement statement = connection.createStatement();

        //2.根据Uniqve_1d查询出log7有海外退款的数据
        Map<String, Date> log7Map = new HashMap<>();
//        String log7Sql = "select unique_id,max(creation_date)as creation_date from wh_unique_sku_log_7 where unique_id in ('aaaaaa')group by unique_id";
        String log7Sql = "select unique_id,max(creation_date)as creation_date from wh_unique_sku_log_7 where creation_date >= '2022-07-01 00:00:00' and creation_date < '2022-08-01 00:00:00' group by unique_id";
        ResultSet Log7Result = statement.executeQuery(log7Sql);
        while (Log7Result.next()) {
            String unique_id = Log7Result.getString("unique_id");
            Date creation_date = Log7Result.getDate("creation_date");
            log7Map.put(unique_id, creation_date);
        }

        //3.根据log7.uniqve._ic查询Log4表
        String uniqueListstr = new ArrayList<>(log7Map.keySet()).stream().collect(Collectors.joining("','"));
        String stats = " select log.unique_id, max(log.creation_date) as creation_date, log.order_no, apv.platform_order_id, apv.tracking_number from wh_unique_sku_log_4 log " +
                        "inner join wh_apv apv on apv.apv_no = log.order_no where log.unique_id in ('aaaaaa') and apv.platform = 15 group by unique_id, order_no ";
        ResultSet log4Result = statement.executeQuery(stats.replace("aaaaaa",uniqueListstr));
        Set<String> set = new HashSet<>();
        List<ApvInfo> apvList = new ArrayList<>();
        while (log4Result.next()) {
            UniqueSku sku = new UniqueSku();
            sku.apvNo = log4Result.getString("order_no");
            sku.date = log4Result.getDate("creation_date");
            sku.uniqueId = log4Result.getString("unique_id");
            String platform_order_id = log4Result.getString("platform_order_id");
            String tracking_number = log4Result.getString("tracking_number");
            // 比较海外退件时间
            Date log7date = log7Map.get(sku.uniqueId);
            if(log7date != null && log7date.after(sku.getDate())){
                if(set.add(platform_order_id + sku.apvNo)) {
                    ApvInfo apv = new ApvInfo();
                    apv.apv_no = sku.apvNo;
                    apv.platform_order_id = platform_order_id;
                    apv.tracking_number = tracking_number;
                    apv.returnDate = log7date;
                    apvList.add(apv);
                }
            }
        }

        List<List<String>> excelL = new ArrayList<>();
        for (ApvInfo apv : apvList) {
            List<String> downloadList = new ArrayList<String>();
            downloadList.add(POIUtils.transferObj2Str(apv.apv_no));
            downloadList.add(POIUtils.transferObj2Str(apv.platform_order_id));
            downloadList.add(POIUtils.transferObj2Str(apv.tracking_number));
            downloadList.add(POIUtils.transferObj2Str(apv.returnDate));
            excelL.add(downloadList);
        }
        ExcelWriter build = EasyExcel.write("虾皮退单-2022-07.xlsx").head(EasyExcelUtils.getHead(new String[]{"YSTN", "平台单号", "追踪号", "退仓时间"})).build();
        WriteSheet writeSheet = EasyExcel.writerSheet("第1页").build();
        build.write(excelL, writeSheet);
        //关流
        if (build != null) {
            build.finish();
        }
    }


    @Test
    public void testtt3() throws Exception {
        InputStream inputStream = new FileInputStream("D:\\download\\退货单历史数据.xlsx");
        ResultModel<String> items = POIUtils.readExcel(new String[]{"单号"}, inputStream, row -> {
            String s = POIUtils.cellValue2Str(row.getCell(0));
            return s;
        }, false);
        Class.forName("com.mysql.jdbc.Driver");
        Connection connection = DriverManager.getConnection("****************************************************************************************************************************************", "readonly", "!QAZxsw2");
        Statement statement = connection.createStatement();
        // 1. 查询出log7有海外退件的数据
        List<UniqueSku> log4List = new ArrayList<>();
        String platId = items.getList().stream().collect(Collectors.joining("','"));
        ResultSet log4ApvResult = statement.executeQuery(" select unique_id, creation_date, order_no from wh_unique_sku_log_4 where order_no in ('" + platId + "') ");
        List<String> uniqueIdList = new ArrayList<>();
        List<String> apvNoList = new ArrayList<>();
        while(log4ApvResult.next()) {
            String unique_id = log4ApvResult.getString("unique_id");
            String apv_no = log4ApvResult.getString("order_no");
            Timestamp date = log4ApvResult.getTimestamp("creation_date");
            apvNoList.add(apv_no);
            uniqueIdList.add(unique_id);
            UniqueSku sku = new UniqueSku();
            sku.setUniqueNo(unique_id);
            sku.setApvNo(apv_no);
            sku.setDate(new Date(date.getTime()));
            log4List.add(sku);
        }
        String ungueIdstr = uniqueIdList.stream().distinct().collect(Collectors.joining("','"));

        //2.根据Uniqve_1d查询出log7有海外退款的数据
        Map<String, Date> log7Map = new HashMap<>();
        String log7Sql = "select unique_id,max(creation_date)as creation_date from wh_unique_sku_log_7 where unique_id in ('aaaaaa')group by unique_id";
        ResultSet Log7Result = statement.executeQuery(log7Sql.replace("aaaaaa",ungueIdstr));
        while (Log7Result.next()) {
            String unique_id = Log7Result.getString("unique_id");
            Timestamp creation_date = Log7Result.getTimestamp("creation_date");
            log7Map.put(unique_id, new Date(creation_date.getTime()));
        }

        List<String> existList = new ArrayList<>();
        for (UniqueSku sku : log4List) {
            Date log7date = log7Map.get(sku.getUniqueNo());
            if(log7date != null && log7date.after(sku.getDate())){
                existList.add(sku.apvNo+"|"+DateUtils.dateToString(log7date, "yyyy-MM-dd hh:mm:ss"));
            }
        }

        String aaa = existList.stream().collect(Collectors.joining("\n"));
        System.out.println(aaa);
    }

    class UniqueSku{
        private String uniqueNo;
         private String apvNo;
         private Date date;
         private String uniqueId;

        public String getUniqueId() {
            return uniqueId;
        }

        public void setUniqueId(String uniqueId) {
            this.uniqueId = uniqueId;
        }

        public String getUniqueNo() {
            return uniqueNo;
        }

        public void setUniqueNo(String uniqueNo) {
            this.uniqueNo = uniqueNo;
        }

        public String getApvNo() {
            return apvNo;
        }

        public void setApvNo(String apvNo) {
            this.apvNo = apvNo;
        }

        public Date getDate() {
            return date;
        }

        public void setDate(Date date) {
            this.date = date;
        }
    }

    class ApvInfo{
        String unique_id;
        String platform_order_id;
        String apv_no;
        String tracking_number;
        Date returnDate;
    }

    @Test
    public void readdd() throws Exception {
        InputStream inputStream = new FileInputStream("D:\\download\\ManagementItemList_3999_20220630172417abdc5.xlsx");
        ResultModel<String> items = POIUtils.readExcel(new String[]{"子SKU"}, inputStream, row -> {
            String s = POIUtils.cellValue2Str(row.getCell(0));
            return s;
        }, false);
        List<String> list = items.getList();
        List<String> list2 = new ArrayList<>(list.size());
        list.forEach(c -> {list2.add("'"+ c + "',");});
        OutputStream outputStream = new FileOutputStream("D:\\download\\fzsku.txt");
        IOUtils.writeLines(list2, "", outputStream);
    }

    @Autowired
    private RedisTemplate redisTemplate;

    @Test
    public void te2() throws Exception {
        HashMap<String, WhSkuColorSize> sku_color_size1 = (HashMap<String, WhSkuColorSize>) redisTemplate.opsForHash().entries("SKU_COLOR_SIZE");
        InputStream inputStream = new FileInputStream("D:\\download\\SKU.txt");
        List<String> strings = IOUtils.readLines(inputStream);
        List<String> list2 = new ArrayList<>();
        for (String sku : strings) {
            WhSkuColorSize attr = sku_color_size1.get(sku);
            if(attr != null) {
                list2.add("update t_wh_sku set attr_json = '" + JSON.toJSONString(attr) +"' where sku = '" + sku + "';\n");
            }
        }
        OutputStream outputStream = new FileOutputStream("D:\\download\\修改外网sku属性"+ LocalDate.now().toString()+".txt");
        IOUtils.writeLines(list2, "", outputStream);
    }

    @Test
    public void xx() {
        SystemParam systemParam = CacheUtils.SystemParamGet("OMS_PARAM.CALC_OPTIMAL_LOGISTICS_URL");
        if (systemParam == null || org.apache.commons.lang.StringUtils.isBlank(systemParam.getParamValue())) {
            System.out.println(systemParam.getParamValue());
        }
        Map<String, Object> map = new HashMap<>();
        map.put("apvNo", "YSTN20052711001113");
        map.put("actualWeight", 25.0);
        String url = CacheUtils.SystemParamGet("OMS_PARAM.CALC_OPTIMAL_LOGISTICS_URL").getParamValue();
        ApiResult<Boolean> apiResult = HttpUtils.post(url, "", map, ApiResult.class);
        System.out.println(apiResult.getResult());
    }

    @Test
    public void assembleSql() {
        WhSkuQueryCondition query = new WhSkuQueryCondition();
        query.setWarehouseId(true);
        int totalCount = whSkuService.queryWhSkuCount(query);
        int pageSize = 450000;
        int totalPage = (totalCount + pageSize - 1) / pageSize;
        for (int pageNo = 1; pageNo <= totalPage; pageNo++) {
            System.out.println(System.currentTimeMillis());
            Pager pager = new Pager(pageNo, pageSize);
            List<WhSku> list = whSkuService.queryWhSkus(query, pager);
            System.out.println(System.currentTimeMillis());
            StringBuffer buf = new StringBuffer();
            list.forEach((whSku) -> {
                String sku = whSku.getSku();// sku
                String skuDecs = whSku.getSkuDecs();// 质检备注
                if (StringUtils.isBlank(skuDecs) || "null".equals(skuDecs)) {
                    skuDecs = "空";
                }
                String sql = "INSERT INTO wh_sku_qc_category (sku, qc_category_code, qc_category_remark, create_by, create_time, update_by, update_time) VALUES ('"
                        + sku + "', '8', '" + skuDecs.trim().replaceAll(System.getProperty("line.separator"), "")
                                .replaceAll("'", "").replaceAll("\"", "")
                        + "', '1', '2019-09-03 15:35:53', '1', '2019-09-03 15:35:53');";
                sql += System.getProperty("line.separator");
                buf.append(sql);
            });
            write(pageNo, buf.toString());
        }
    }

    public void write(int index, String content) {
        FileOutputStream fop = null;
        File file;
        try {
            file = new File("E:/data-" + index + ".sql");
            fop = new FileOutputStream(file);
            if (!file.exists()) {
                file.createNewFile();
            }
            byte[] contentInBytes = content.getBytes();
            fop.write(contentInBytes);
            fop.flush();
            fop.close();
        }
        catch (IOException e) {
            log.error(e.getMessage(), e);
        }
        finally {
            try {
                if (fop != null) {
                    fop.close();
                }
            }
            catch (IOException e) {
                log.error(e.getMessage(), e);
            }
        }
    }

    @Test
    public void getAccount(){
        EsSaleAccount cn1519301791uhnx = esSaleAccountService.getSaleAccountBySellerId("ES-zhenDEhsi","accountNumber");
        System.out.println(cn1519301791uhnx);
    }
}
