@font-face { 
	font-family: "iconfont"; 
	font-style: normal; 
	font-weight: normal; 
	src: url("iconfont/iconfont.eot?#iefix") format("embedded-opentype"), 
			url("iconfont/iconfont.svg#iconfont") format("svg"), 
			url("iconfont/iconfont.woff") format("font-woff"), 
			url("iconfont/iconfont.ttf") format("truetype"); 
}

.table-query-title{
    padding-left: 10px;
}

.table td{
    word-break: break-all !important;
}

.content{
	width: 100% !important;
}

/*��ҳ*/
ul.pages{border:0;padding:0;width:100%;margin: 0px;overflow:hidden;}
ul.pages li{list-style:none;float:left;border:1px solid #ccc;text-decoration:none;margin:0 5px 0 0;padding:0px 6px;line-height:20px;-moz-border-radius: 10px!important;-webkit-border-radius: 10px!important;border-radius: 10px!important;border-radius: 10px!important;}
ul.pages li.select{padding:0;border:0px;}
ul.pages li.page-number:hover{cursor:pointer;color:#fff;background-color:#4d90fe;}
ul.pages li.pgEmpty{border:1px solid #eee;color:#eee;}
ul.pages li.pgCurrent{color:#fff;background-color:#4d90fe;}
ul.pages li.count{margin-right:0px;padding: 0 8px;}
ul.pages li.count span{float: left;margin-right: 6px;}
ul.pages li.count input{margin-left: 6px;float:left;height: 18px;border:1px solid #ccc;margin:1px 0px 0px 1px;line-height: normal;width: 30px;border-radius: 11px!important;text-align: center;}
ul.pages li.go{border-left:0;}
ul.pages li.select select{border-radius: 10px!important;cursor: pointer;}
/*#pager .pages {
	height: 24px;
	background-color: #fafafa;
	background-image: -moz-linear-gradient(top, #fff, #f2f2f2);
	background-image: -webkit-gradient(linear, 0 0, 0 100%, from(#fff), to(#f2f2f2));
	background-image: -webkit-linear-gradient(top, #fff, #f2f2f2);
	background-image: -o-linear-gradient(top, #fff, #f2f2f2);
	background-image: linear-gradient(to bottom, #fff, #f2f2f2);
	background-repeat: repeat-x;
	filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#ffffffff', endColorstr='#fff2f2f2', GradientType=0);
	-webkit-box-shadow: 0 1px 4px rgba(0, 0, 0, 0.065);
	-moz-box-shadow: 0 1px 4px rgba(0, 0, 0, 0.065);
	box-shadow: 0 1px 4px rgba(0, 0, 0, 0.065);
}*/
/* �̶�ҳ�� */
#fixed-bottom {width:100%; z-index:9999; opacity:1;padding:5px 15px;
filter:alpha(opacity=60); _bottom:auto; _width:100%; _position:absolute;}

#fixed-bottom {width:100%; z-index:9999; opacity:1;padding:5px 15px;background: #fff;
	filter:alpha(opacity=60); _bottom:auto; _width:100%; _position:absolute;}
.fixed-bottom-height{position:fixed; bottom:0;}
.footer-copyright{text-align: center;width: 100%;margin-bottom: 40px;}
.col-md-12 h2{font-size: 18px;}

.nav-to-top{
	position: fixed;right: 400px;bottom: 40px;z-index: 9999;
}
.header.navbar.navbar-default {
	position: fixed;
	top: 0;
	z-index: 99999;
}
.search-nav{
	margin-top: 20px;
}
#fixedDiv{
	display: none;
	position: fixed;
	top: 58px;
	z-index: 9999;
}
#fixedTab td {
	vertical-align:middle;
}
#page{
	margin-top: 58px;
}
.ui-popup{
	margin-top: 58px !important;
}

@font-face {
	font-family: 'nimbussannovtcon';
	src:url("../fonts/nimbussannovtcon-bold.ttf");
}
