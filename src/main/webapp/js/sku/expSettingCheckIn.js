function ChangeValue(obj) {
    if ($dp.cal) {
        var objDiv = $(obj).parent().parent();
        var name = $(obj).attr('name');
        var days = $(objDiv).find("input[name='days']").val();
        var datetime = new Date(Date.parse($dp.cal.getDateStr().replace(/-/g, "/"))).getTime();
        if (name == 'proDate') {
            var expDate = $(objDiv).find("input[name='expDate']").val();
            if (days && days > 0) {
                var endDate = new Date(datetime + days * 24 * 3600 * 1000);
                $(objDiv).find("input[name='expDate']").val(endDate.format("yyyy-MM-dd"));
            } else if (expDate) {
                var expTime = new Date(Date.parse(expDate.replace(/-/g, "/"))).getTime();
                var day = Math.floor((expTime - datetime) / (24 * 3600 * 1000));
                $(objDiv).find("input[name='days']").val(day);
            }
        } else {
            var proDate = $(objDiv).find("input[name='proDate']").val();
            if (days && days > 0) {
                var startDate = new Date(datetime - days * 24 * 3600 * 1000);
                $(objDiv).find("input[name='proDate']").val(startDate.format("yyyy-MM-dd"));
            } else if (proDate) {
                var proTime = new Date(Date.parse(proDate.replace(/-/g, "/"))).getTime();
                var day = Math.floor((datetime - proTime) / (24 * 3600 * 1000));
                $(objDiv).find("input[name='days']").val(day);
            }
        }
        var endTime = $(objDiv).find("input[name='expDate']").val();
        days = $(objDiv).find("input[name='days']").val();
        if (days && days > 0 && endTime && endTime != ''){
            checkRestrictedStorage(objDiv, days, endTime);
        }
    }

}

function changeDays(obj) {
    var days = $(obj).val();
    var objDiv = $(obj).parent().parent();
    var proDate = $(objDiv).find("input[name='proDate']").val();
    var expDate = $(objDiv).find("input[name='expDate']").val();
    if (days && days > 0) {
        if (proDate) {
            var datetime = new Date(Date.parse(proDate.replace(/-/g, "/"))).getTime();
            var endDate = new Date(datetime + days * 24 * 3600 * 1000);
            $(objDiv).find("input[name='expDate']").val(endDate.format("yyyy-MM-dd"));
        } else if (expDate){
            var datetime = new Date(Date.parse(expDate.replace(/-/g, "/"))).getTime();
            var proDate = new Date(datetime - days * 24 * 3600 * 1000);
            $(objDiv).find("input[name='proDate']").val(proDate.format("yyyy-MM-dd"));
        }
        var endTime = $(objDiv).find("input[name='expDate']").val();
        if (days && days > 0 && endTime && endTime != ''){
            checkRestrictedStorage(objDiv, days, endTime);
        }
    }
}

function checkRestrictedStorage(objDiv, days, endTime) {
    var purchaseTrDiv = $(objDiv).parent().parent().prev().prev();
    var startTime = $(objDiv).find("input[name='proDate']").val();
    $(purchaseTrDiv).find("div[name='restricted-storage']").find("input[name='whCheckIn.days']").val(days);
    $(purchaseTrDiv).find("div[name='restricted-storage']").find("input[name='whCheckIn.proDate']").val(startTime);
    $(purchaseTrDiv).find("div[name='restricted-storage']").find("input[name='whCheckIn.expDate']").val(endTime);
    let sku = $(purchaseTrDiv).find("input[name='whCheckIn.whCheckInItem.sku']").val();
    let purchaseNo =$(purchaseTrDiv).find("div[name='restricted-storage']").find("[name='purchaseNo']").val();

    debugger
    let url = CONTEXT_PATH + "expWaitShipments/matchExpWait?days=" + days
        + "&endTime=" + endTime + " 00:00:00" + "&purchaseNo=" + purchaseNo + "&sku=" + sku;
    $.getJSON(url, function (response) {
        $(purchaseTrDiv).find("div[name='restricted-storage']").find("input[name='whCheckIn.expWaitId']").val("");
        $(purchaseTrDiv).find("div[name='restricted-storage']").find("input[name='whCheckIn.waitOffsetQty']").val("");
        $(purchaseTrDiv).find("div[name='restricted-storage']").find("input[name='whCheckIn.expFlag']").val("");
        $(purchaseTrDiv).find("div[name='restricted-storage']").find('span').text("");
        if(response.status === "200"){
            $(purchaseTrDiv).find("div[name='restricted-storage']").find("input[name='whCheckIn.restrictedStorage']").val("0");
            $(objDiv).next().text("");
            if(response.body.expWaitShipments){
                // SKU存在待发
                let waitOffsetQty = response.body.waitOffsetQuantity;
                let expWait = response.body.expWaitShipments;
                if(response.body.expFlag === 4){
                    // 保质期限制入库
                    $(objDiv).next().text("SKU即将过保质期，目前有"+ waitOffsetQty +"个待发，只能输入"+ waitOffsetQty +"个良品数量，如果良品数量不足，请直接提交异常单")
                }
                $(purchaseTrDiv).find("div[name='restricted-storage']").find("input[name='whCheckIn.expWaitId']").val(expWait.id);
                $(purchaseTrDiv).find("div[name='restricted-storage']").find("input[name='whCheckIn.waitOffsetQty']").val(waitOffsetQty);
                $(purchaseTrDiv).find("div[name='restricted-storage']").find("input[name='whCheckIn.expFlag']").val(response.body.expFlag);
            }
        } else {
            if(response.exceptionCode != undefined && response.exceptionCode === "IS_TRANSFER"){
                $(purchaseTrDiv).find("div[name='restricted-storage']").find('span').text(response.message);
            }else{
                customizeLayer(response.message, 'error');
            }
            $(purchaseTrDiv).find("div[name='restricted-storage']").find("input[name='whCheckIn.restrictedStorage']").val("1");
        }
    });
}
