$(window).scroll(function(){
    var s = $(window).scrollTop();
    if(s > 400){
        document.getElementById("fixedDiv").style.display = "block";
        document.getElementById("fixedDiv").style.width = $('#task-list-warp').css('width');
    }
    else {
        document.getElementById("fixedDiv").style.display = "none";
    }
});


// 获取表单元素
const form = document.querySelector('form');

// 添加submit事件监听器
form.addEventListener('submit', function(event) {
    event.preventDefault();
    App.blockUI();
    form.submit();
    return false;
});