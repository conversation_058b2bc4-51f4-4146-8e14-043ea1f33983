var xsdNumberTypes = ["xsd:byte", "xsd:decimal", "xsd:int", "xsd:integer", "xsd:long", "xsd:negativeInteger", "xsd:nonNegativeInteger", "xsd:nonPositiveInteger", 
	"xsd:positiveInteger", "xsd:short", "xsd:unsignedLong", "xsd:unsignedInt", "xsd:unsignedShort", "xsd:unsignedByte"];
var xsdDoubleTypes = ["xsd:decimal"];
var xsdStringTypes = ["xsd:ENTITIES", "xsd:ENTITY", "xsd:ID", "xsd:IDREF", "xsd:IDREFS language", "xsd:Name", "xsd:NCName", "xsd:NMTOKEN", "xsd:NMTOKENS",
	"xsd:normalizedString", "xsd:QName", "xsd:string", "xsd:token"];

// 获取根路径
function getContextPath() {
    var pathName = document.location.pathname;
    var index = pathName.substr(1).indexOf("/");
    var result = pathName.substr(0,index+2);
    return result;
}

function getWebContextPath() {
	return document.location.origin +"/wms-web/"
}
var WEB_CONTEXT_PATH = getWebContextPath();
var CONTEXT_PATH = getContextPath();
var PDF_CONTEXT_PATH = getContextPath().substr(0, getContextPath().length - 1) + "_pdf/";

var PDF_PMS_URL = "";

//校验正整数
function testNumber(num) {
	var goodChar = "0123456789"; 
	var c; 
	for(var i=0;i<num.length;i++){
		c=num.charAt(i);
		if(goodChar.indexOf(c) == -1){ 
			return false; 
		} 
	}
	
	if(Number(num) >= 2147483648) {
		return false;
	}
	
	return true;
}

//校验浮点数
function testDoubleNumber(num){
	var num=$.trim(num);
	if(num==null||num==""){
		return true;
	}
	var pointCount=0;
	for(var i=0;i<num.length;i++){
		if((num.charAt(i)<'0'||num.charAt(i)>'9')&&num.charAt(i)!='.'){
			return false;
		}
		else{
			if(num.charAt(i)=='.'){
				pointCount++;
			}
		}
	}
	if(pointCount>1){
		return false;
	}else if(pointCount==1&&$.trim(num).length==1){
		return false;
	}
	return true;
}

function generateForm(options) {
    var defaultOptions = {
        parent: "body",
        url: "",
        attrs: {}
    };

    options = $.extend(defaultOptions, options);
    var $form = $("<form style='display:none' target='' method='post' action='" + options.url + "'>");
    $(options.parent).append($form);
    if (options.attrs)
    {
        for ( var item in options.attrs)
        {
            $input = $("<input>");
            $input.attr("type", "hidden");
            $input.attr("name", item);
            $input.attr("value", options.attrs[item]);
            $form.append($input);
        }
    }

    return $form;
}

Date.prototype.format = function(f){
    var o ={
        "M+" : this.getMonth()+1,
        "d+" : this.getDate(),
        "h+" : this.getHours(),
        "m+" : this.getMinutes(),
        "s+" : this.getSeconds(),
        "q+" : Math.floor((this.getMonth()+3)/3),
        "S" : this.getMilliseconds()
    }
    
    if(/(y+)/.test(f)){
        f=f.replace(RegExp.$1,(this.getFullYear()+"").substr(4 - RegExp.$1.length));
    }
    
    for(var k in o){
        if(new RegExp("("+ k +")").test(f)){
            f = f.replace(RegExp.$1,RegExp.$1.length==1 ? o[k] : ("00"+ o[k]).substr((""+ o[k]).length));
        }
    }
    
    return f;
}

function long2Date(longVal, pattern){
    if(pattern == null || pattern == ""){
        pattern = "yyyy/MM/dd";
    }
    return new Date(longVal).format(pattern);
}

function long2FullDate(longVal, pattern){
    if(pattern == null || pattern == ""){
        pattern = "yyyy/MM/dd hh:mm:ss";
    }
    return new Date(longVal).format(pattern);
}

(function(window, $, undefined) {
    $.fn.extend({
        intBootstrapTable: function(options) {
            var defaultOptions = {
                    url: '',
                    queryParams: function(params){
                        return params;
                    },
                    dataType: "json",
                    method: "post",
                    pagination: true, 
                    pageNumber:1,
                    pageSize: 20, 
                    pageList: [10, 20, 50, 100],
                    search: false, 
                    queryParamsType: "limit",
                    sidePagination: "server",
                    icons: {
                        detailOpen: 'glyphicon-plus icon-arrow-right',
                        detailClose: 'glyphicon-minus icon-arrow-up'
                   }
            };
            
            this.bootstrapTable($.extend(defaultOptions, options));
            return this;
        },
        showDialog: function(options){
            var defaultOptions = {
                    title: "",
                    width: "52%",
                    height: "auto",
                    buttons: [],
                    modal: true,
                    hide : 'fade',
                    show : 'fade',
                    closeOnEscape : true
            };
            options = $.extend(defaultOptions, options);
            this.dialog({
                closeOnEscape : options.closeOnEscape,
                width : options.width,
                height : options.height,
                hide : options.hide,
                show : options.show,
                modal : options.modal,
                title : options.title,
                buttons : options.buttons
            });
        },
        initPager: function(options) {
    		var defaultOptions = {
    				total: 0,
    				pageNo: 1,
    				pageSize: 10,
    				jPageNo: $("#page-no"),
    				jPageSize: $("#page-size"),
    				jForm: $("#domain")
    		};
            
    		options = $.extend(defaultOptions, options);
            this.pager({
        		total : options.total,
        		pageNo : options.pageNo,
        		pageSize : options.pageSize,
        		pageClick : function(opts) {
        			options.jPageNo.val(opts.pageNo);
        			options.jPageSize.val(opts.pageSize);
        			App.blockUI();
        			options.jForm.submit();
        		}
        	});
            return this;
        }
    });
    
    $.extend({
    	// 初始化全选按钮功能
    	initCheckAll : function(options) {
    		var defaultOptions = {
    				checkAll: $("input[name='checkAll']"), // 全选框
    				itemIds: $("input[name='ids']") // 子选框
    		};
            
    		options = $.extend(defaultOptions, options);
            
    		options.checkAll.change(
    			function () {
    				var check = $(this).prop("checked");
    				options.itemIds.prop("checked", check);
    				options.itemIds.closest("tr").find("td").css("background-color", check ? "#B1DB87" : "");
    			}
    		);
    		
    		options.itemIds.change(function(){
    			var checkedLenght = options.itemIds.filter(":checked").length;
    			var length = options.itemIds.length;
    			options.checkAll.prop("checked", checkedLenght == length);
    			
    			// 改变背景色
    			$(this).closest("tr").find("td").css("background-color", $(this).prop("checked") ? "#B1DB87" : "");
    		});
        },
        error : function(message, callback) {
            var d = dialog({
                title: '错误',
                width: 400,
                content: '<img src="" /> ' + message,
                okValue: '确定',backdropOpacity: 0.3,
                ok: function () {
                    if (callback && $.isFunction(callback)) {
                        callback();
                    }
                }
            });
            d.showModal();
        },
        confirmDel : function(message, callback) {
            if(!message) {message = "您确定要删除信息吗?";}
            var d = dialog({
                title: '确认删除',
                width: 400,
                content: '<div style="float:left" ><img src="" /></div> <div style="float:left;width:380px;margin-left:15px">' + message + '</div>',
                okValue: '确定',
                backdropOpacity: 0.3,
                zIndex: 100000,
                ok: function () {
                    if (callback && $.isFunction(callback)) {
                        callback();
                    }
                },
                cancelValue: '取消',
                cancel: function () {}
            });
            d.showModal();
        },
        confirm : function(titel, message, callback) {
            var d = dialog({
                title: titel,
                width: 400,
                content: '<div style="float:left" ><img src="" /></div> <div style="float:left;width:380px;margin-left:15px">' + message + '</div>',
                okValue: '确定',
                backdropOpacity: 0.3,
                ok: function () {
                    if (callback && $.isFunction(callback)) {
                        callback();
                    }
                },
                cancelValue: '取消',
                cancel: function () {}
            });
            d.showModal();
        },
        success : function(message, callback) {
            var d = dialog({
                title: '成功',
                width: 400,
                content: '<div class="media"><a class="pull-left"><img src="" /></a><div class="media-body"> ' + message + '</div></div>',
                okValue: '确定',backdropOpacity: 0.3,
                ok: function () {
                    if (callback && $.isFunction(callback)) {
                        callback();
                    }
                }
            });
            d.showModal();
        },
	 	// 警告提示
		warningTip : function(msg) {
			toastr.options = {
		            closeButton: true,
		            debug: false,
		            timeOut: "3000",
		            positionClass: 'toast-top-center',
		            onclick: null
		    };
			toastr['warning'](msg, "警告");
		},
		
		// 错误提示
		errorTip : function(msg) {
			toastr.options = {
		            closeButton: true,
		            debug: false,
		            timeOut: "3000",
		            positionClass: 'toast-top-center',
		            onclick: null
		    };
			toastr['error'](msg, "错误");
		}
    })
})(window, $, undefined);

//从右边开始递归获取最右边的[]之间的数字
function recursionLastIndexOf(value, searchPrefixValue, searchSuffixValue) {
	var prefixIndex = value.lastIndexOf(searchPrefixValue);
    
    if(prefixIndex != -1) {
    	var suffixIndex = value.lastIndexOf(searchSuffixValue);
    	
    	var newValue = value.substring(prefixIndex + 1, suffixIndex);
    	
    	if(newValue != '' && !testNumber(newValue)) {
    		value = value.substring(0, prefixIndex);
    		return recursionLastIndexOf(value, searchPrefixValue, searchSuffixValue)
    	} else {
    		return prefixIndex;
    	}
    }
    
    return prefixIndex;
}

//刷新index
function refreshIndex(curIndex, obj) {
	if(obj) {
		
		var elements = obj.elements ? obj.elements : obj.getElementsByTagName('*');
		for(var i = 0, len = elements.length; i < len; i++) {
			var el = elements[i];
			
			var n = el.name, t = el.type, tag = el.tagName.toLowerCase();

		    if ((!n || el.disabled || t == 'reset' || t == 'button' ||
		        (t == 'submit' || t == 'image') 
		        )) {
		    	continue;
		    }
		    
		    var jqEl = $(el);
		    var oldName = jqEl.prop("name");
		    var prefixIndex = recursionLastIndexOf(oldName, "[", "]");
		    
		    if(prefixIndex != -1) {
		    	var suffixIndex = oldName.indexOf("]", prefixIndex);
		    	var	newName = oldName.substring(0, prefixIndex + 1) + curIndex + oldName.substring(suffixIndex);
		    	
			    jqEl.prop("name", newName);
		    }
		}
	}
}

// 数据加一个对比的方法
Array.prototype.contains = function (obj) {  
    var i = this.length;  
    while (i--) {  
        if (this[i] === obj) {  
            return true;  
        }  
    }  
    return false;  
}

//String加全局替换
String.prototype.replaceAll = function (obj, char) {
	var str = this;
	while (str.indexOf(obj) > -1){
		str = str.replace(obj, char);
	}
	return str;
}

// 弹出框
alert = function(msg, type, title) {
	toastr.options = {
            closeButton: true,
            debug: false,
            timeOut: "2000",
            positionClass: 'toast-top-center',
            onclick: null,
            onHidden : null
    };
	
	if (typeof msg == 'string') {
		msg = msg.replaceAll("\r", "<br/>");
		msg = msg.replaceAll("\n", "<br/>");
	}
	
	if (arguments.length == 1) {
		toastr['success'](msg);
	} else if (arguments.length == 2) {
		toastr[type](msg);
	} else {
		toastr[type](msg, title);
	}
}

//验证 http://my.oschina.net/duanlijun/blog/9950
jQuery.validator.setDefaults({
	errorElement: 'span', //default input error message container
    errorClass: 'help-block error-block', // 用此设定的样式来定义错误消息的样式。
    focusInvalid: false, // 当验证无效时，焦点跳到第一个无效的表单元素。当为false时，验证无效时，没有焦点响应。
    ignore: ".ignore-valid", //不校验
    highlight: function (element) {  // element出错时触发
    	if($(element).parent().hasClass("input-range")) {
    		$(element).parent().removeClass('has-success').addClass('has-error');
    	} else if($(element).parent("td").length > 0) {
    		$(element).parent('td').removeClass('has-success').addClass('has-error');
    	} else if($(element).parent("th").length > 0) {
    		$(element).parent('th').removeClass('has-success').addClass('has-error');
    	} else {
    		$(element).closest('td').removeClass('has-success').addClass('has-error'); // set error class to the control group
    		
    		$(element).closest('th').removeClass('has-success').addClass('has-error'); // set error class to the control group
    		
    		$(element).closest('.form-group').removeClass('has-success').addClass('has-error'); // set error class to the control group
    	}
    },

    unhighlight: function (element) { // element通过验证时触发
    	if($(element).parent().hasClass("input-range")) {
    		$(element).parent().removeClass('has-error');
    	} else if($(element).parent("td").length > 0) {
    		$(element).parent('td').removeClass('has-error');
    	} else if($(element).parent("th").length > 0) {
    		$(element).parent('th').removeClass('has-error');
    	} else {
    		$(element).closest('td').removeClass('has-error'); // set error class to the control group
    		
    		$(element).closest('th').removeClass('has-error'); // set error class to the control group
    		
    		$(element).closest('.form-group').removeClass('has-error'); // set error class to the control group
    	}
    },
    
    errorPlacement: function (error, element) { // 插入错误信息
    	if(element.attr("type") == 'checkbox' || element.attr("type") == 'radio') {
			if($(element).parent().parent().hasClass("has-error")) {
				var label = $("<label class='radio-inline'></label>");
				label.append(error);
	        	$(element).parent().parent().append(label);
			} else {
				$(element).parent().append(error);
			}
        } else if($(element).parent().hasClass("input-range")) {
        	error.insertAfter($(element).closest(".input-group"));
        } else if($(element).parent().hasClass("input-large")) {
        	error.insertAfter($(element).closest(".input-group"));
        } else {
        	$(element).parent().append(error);
        }
    }
});

function checkInputNumber(obj, isInteger, containsNegative){
	if(obj == null){
		return;
	}
	
	if(isInteger == null){
		isInteger = false;
	}
	if(containsNegative == null){
		containsNegative = false;
	}
	
	if(isInteger){
		//先把非数字的都替换掉，除了数字和.
		obj.value = obj.value.replace(/[^\d]/g,"");
	}else {
		var isNegative = false;
		if(containsNegative && obj.value.charAt(0) == "-"){
			isNegative = true;
		}
		//先把非数字的都替换掉，除了数字和.
		obj.value = obj.value.replace(/[^\d.]/g,"");
		//必须保证第一个为数字而不是.
		obj.value = obj.value.replace(/^\./g,"");
		//保证只有出现一个.而没有多个.
		obj.value = obj.value.replace(/\.{2,}/g,".");
		//保证.只出现一次，而不能出现两次以上
		obj.value = obj.value.replace(".","$#$").replace(/\./g,"").replace("$#$",".");
		if($(obj).attr("data-max") != '' && parseInt($(obj).attr("data-max")) < parseInt(obj.value)){
			obj.value = obj.value.substring(0, obj.value.length-1);
			if(obj.value>$(obj).attr("data-max")) obj.value = $(obj).attr("data-max");
		}
		//保留小数点后两位
		if(obj.value.indexOf(".") > 0){
			var num = obj.value.split(".");
			obj.value = num[1].length > 2 ? obj.value.substring(0, obj.value.length-1) : obj.value;
		}
		
		if(isNegative){
			obj.value = "-" + obj.value;
		}
	}
}

//自定义map
function Map() {         
	/** 存放键的数组(遍历用到) */       
	this.keys = new Array();        
	/** 存放数据 */        
	this.data = new Object();  
	
	Array.prototype.indexOf = function(val) {              
	    for (var i = 0; i < this.length; i++) {  
	        if (this[i] == val) return i;  
	    }  
	    return -1;  
	}; 
	
	Array.prototype.remove = function(val) {  
	    var index = this.indexOf(val);  
	    if (index > -1) {  
	        this.splice(index, 1);  
	    }  
	}; 
	
	/**         
	 * 放入一个键值对        
	 * @param {String} key        
	 * @param {Object} value        
	 */        
	this.put = function(key, value) {            
		 if(this.data[key] == null){                
			 this.keys.push(key);            
	     }             
		 this.data[key] = value;        
    };                
    /**
     * 获取某键对应的值        * @param {String} key        * @return {Object} value
     */        
    this.get = function(key) {            
    	 return this.data[key];        
    };                 
    /**         
     * 删除一个键值对        
     * @param {String} key        
     */        
     this.remove = function(key) {            
    	 this.keys.remove(key);            
    	 this.data[key] = null;        
     };                 
     /**         
      * 遍历Map,执行处理函数         *          
      * @param {Function} 回调函数 function(key,value,index){..}        
      */        
      this.each = function(fn){             
    	  if(typeof fn != 'function'){                
    		  return;            
    	  }             
    	  var len = this.keys.length;            
    	  
    	  for(var i=0;i<len;i++){                
    		  var k = this.keys[i];                
    		  fn(k,this.data[k],i);            
    	  }        
    	};                 
    	/**         
    	 * 获取键值数组(类似Java的entrySet())        
    	 * @return 键值对象{key,value}的数组       
    	 */        
    	 this.entrys = function() {             
    		 var len = this.keys.length;            
    		 var entrys = new Array(len);            
    		 for (var i = 0; i < len; i++) {                
    			 entrys[i] = {                     
					 key : this.keys[i],                    
					 value : this.data[this.keys[i]]                
    			 };            
    		 }             
    		 return entrys; 
        }
    	 
	 /**         
	  * 判断Map是否为空        
	  */        
	  this.isEmpty = function() {             
		 return this.keys.length == 0;        
	  };                
	  
	  /**         
	   * 获取键值对数量        
	   */        
	   this.size = function(){             
		  return this.keys.length;        
	   };                 
	   
	   /**         
	    * 重写toString         
	    */        
	    this.toString = function(){            
		   var s = "{";             
		    for(var i=0;i<this.keys.length;i++,s+=','){                
		    	var k = this.keys[i];                
		    	s += k+"="+this.data[k];            
		    }            
		    s+="}";            
		    
		    return s;        
		}; 
		
		/**
		 * 清空
		 */
		this.clear = function() {
	   		for (var i = 0; i < this.keys.length; i++) {                
	   			remove(this.keys[i]);      
   			};            
		};
}

//重置表单
function formReset(obj) {
	if(obj) {
		
		var form = $(obj).closest("form");
		
		if(form.length > 0) {
			
			form[0].reset();
			
			form.find(':input')
		       .not(':button,:submit,:reset,:hidden,:file')   //将myform表单中input元素type为button、submit、reset、hidden、file排除
		       .val('')  //将input元素的value设为空值
		       .removeAttr('checked')// 如果任何radio/checkbox/select inputs有checked or selected 属性，将其移除
		       .trigger("change"); //select2

			form.find('.mySelect')
				.removeAttr('value');//  属性，将其移除;

			form.find('.select-input')
				.val('');
			/*form.find(':input.form-reset')
		       .val('')
		       .removeAttr('checked');
		       */
		}
	}
}

//播放声音文件
function audioPlay(name){
	new Audio(CONTEXT_PATH + "audio/" + name + ".mp3").play();
}

//日志
function viewLog(id, type, allLog) {
	var diglog = dialog({
		title: id + ' 日志',
		width: 800,   
		url: CONTEXT_PATH + "system/logs/" + type + "/" + id + "?allLog=" + allLog,
	    cancelValue: '取消',
	    cancel: function () {}
	});
	diglog.show();
}

//全选
function checkAll(obj) {
	$("input[name='ids']").prop("checked", $(obj).is(':checked'));
}

// 获取选中的订单
function getCheckedApvs() {
	var checkedApvs = $("input[name='ids']:checked");
	return checkedApvs;
}

/**
 * 带查询条件的重定向
 * @param url
 * @param $formSelector
 * @param pageinfo 页面信息(避免不同页面加载查询条件)
 * <AUTHOR>
 * @returns
 */
function redirectWithSearch(url, $formSelector, pageinfo) {
	var searchObject = $formSelector.serializeJSON();
	var searchData = JSON.stringify(searchObject);
	sessionStorage.setItem("searchPageinfo",pageinfo);//当前页面标识
	sessionStorage.setItem("searchPageCurrent",$("#pager").find(".pgCurrent").text());//当前页码
	sessionStorage.setItem("searchData",searchData);//页面查询数据
	window.location.href = url;
}

/**
 * 刷新页面查询条件回填
 * @param pageinfo 页面信息(避免不同页面加载查询条件)
 * @param $formSelector
 * <AUTHOR>
 * @returns
 */
function searchReload(pageinfo, $formSelector) {
	var cookievalue = sessionStorage.getItem("searchData");
	var pageInfo = sessionStorage.getItem("searchPageinfo");
	var pageCurrent = sessionStorage.getItem("searchPageCurrent");
	if(cookievalue != null && cookievalue != ""){
		var searchObject = JSON.parse(sessionStorage.getItem("searchData"));
		if(pageInfo == pageinfo){
			for(var attr in searchObject){//页面回填
				var $selector = $formSelector.find("input[name='" + attr + "']");
				if($selector.length != 0){
					$formSelector.find("input[name='" + attr + "']").val(searchObject[attr]).trigger("change");
				}else{
					$selector = $formSelector.find("select[name='" + attr + "']");
					if(searchObject[attr] != "" && searchObject[attr] != null){
						$selector.find("option[value=" + searchObject[attr] + "]").attr("selected",true);
					}
				}
			}
			$("#page-no").val(pageCurrent).trigger("change");//修改当前页码
			$formSelector.submit();
		}
		sessionStorage.removeItem("searchData");
		sessionStorage.removeItem("searchPageinfo");
		sessionStorage.removeItem("searchPageCurrent");
	}
}

var lockUI = true;

$(document).ajaxStart(function(){
	if (lockUI) {
		App.blockUI();
	}
});

$(document).ajaxStop(function(){
	if (lockUI) {
		App.unblockUI();
	}
});

// 表单提交导出
function downloadByPostForm(params, url){
    //var url= CONTEXT_PATH + "checkins/download";
    var tempForm = document.createElement("form");
    tempForm.id="tempForm";
    tempForm.method="post";
    tempForm.action=url;
    tempForm.target="blank";
    handParams(tempForm, params);
    if (tempForm.attachEvent) {  // IE
        tempForm.attachEvent("onsubmit",function(){ window.open('about:blank','blank'); });
    } else if (tempForm.addEventListener) {  // DOM Level 2 standard
        tempForm.addEventListener("onsubmit",function(){ window.open('about:blank','blank'); });
    }
    document.body.appendChild(tempForm);
    if (document.createEvent) { // DOM Level 2 standard
        evt = document.createEvent("MouseEvents");
        evt.initMouseEvent("submit", true, true, window, 0, 0, 0, 0, 0, false, false, false, false, 0, null);
        tempForm.dispatchEvent(evt);
    } else if (tempForm.fireEvent) { // IE
        tempForm.fireEvent('onsubmit');
    }
    //必须手动的触发
    tempForm.submit();
    document.body.removeChild(tempForm);
}

function handParams(tempForm, params) {
    if(params){
        var data;
        if(!(params.indexOf("&") == -1)){
            var data = params.split('&');
        }else if(params.indexOf("=") != -1){
            data= new Array();
            data[0] = params;
        }
        if (data){
            for (var i = 0; i < data.length; i++) {
                var param = data[i];
                if (param.indexOf("=") != -1){
                    var arr = param.split('=');
                    var name = arr[0];
                    var value = arr[1];
                    // 条件中有空格的处理
                    if (value.indexOf("+") != -1){
                        value=value.replace(/\+/g," ");
                    }
                    // 特殊字符解码
                    value = decodeURIComponent(value,true);

                    //var name = param.slice(0,param.indexOf('='));
                    //var value = param.slice(param.indexOf('=')+1)

                    // 多个条件逗号分割处理
                    /*if (value.indexOf("%2C") != -1){
                        value=value.replace(/%2C/g,",")
                    }
                    // 条件中有等号的处理
                    if (value.indexOf("%3D") != -1){
                        value=value.replace(/%3D/g,"=")
                    }*/
                    var hideInput = document.createElement("input");
                    hideInput.type="hidden";
                    hideInput.name=name;
                    hideInput.value=value;
                    tempForm.appendChild(hideInput);
                }
            }
        }
    }
}

$.ajaxSetup({
	complete : function(XMLHttpRequest){
		var str = XMLHttpRequest.getResponseHeader("REDIRECT");
		if (typeof str != "undefined" && str != null && str != ""){
			var localPath = XMLHttpRequest.getResponseHeader("CONTEXTPATH");
			if (typeof localPath != "undefined" && localPath != null && localPath != ""){
				window.location.href = localPath;
			}
		}
	}
});

function getSkuByBarCode(obj) {
	var sku = obj.value.replace(/\s/g,'');
    $.ajax({
        async: false,
        url:CONTEXT_PATH + "barCodeSku/getSkuByBarCode",
        type:"GET",
        data:{barCode: sku},
        success:function(result){
            sku = result;
            $('#'+obj.id).val(sku);
        }
    });
    return sku;
}

function escapeJquery(srcString) {
    // 转义之后的结果
    var escapseResult = srcString;
    // javascript正则表达式中的特殊字符
    var jsSpecialChars = ["\\", "^", "$", "*", "?", ".", "+", "(", ")", "[",
        "]", "|", "{", "}"];
    // jquery中的特殊字符,不是正则表达式中的特殊字符
    var jquerySpecialChars = ["~", "`", "@", "#", "%", "&", "=", "'", "\"",
        ":", ";", "<", ">", ",", "/"];
    for (var i = 0; i < jsSpecialChars.length; i++) {
        escapseResult = escapseResult.replace(new RegExp("\\"
            + jsSpecialChars[i], "g"), "\\"
            + jsSpecialChars[i]);
    }
    for (var i = 0; i < jquerySpecialChars.length; i++) {
        escapseResult = escapseResult.replace(new RegExp(jquerySpecialChars[i],
            "g"), "\\" + jquerySpecialChars[i]);
    }
    return escapseResult;
}

/**
 * 自定义信息弹出框
 * @param message
 */
function customizeLayer(message) {
	layer.open({
		type: 1,
		skin: 'layui-layer-rim',
		area: ['500px', '500px'],
		shadeClose: true,
		content: "<div style='margin: 15px;font-size: 15px;'>" + message + "</div>",
		end: function (index) {
			layer.close(index);
			//location.reload();
		}
	});
}

function customizeLayer2(response) {
	var msg = response.message ? response.message : response.status == '200' ? "成功！" : "失败！";
	layer.alert(msg, {closeBtn: 0}, function (index) {
		layer.close(index);
		window.location.reload();
	});
}

function changeLocation(obj) {
	var stockId = $(obj).val();
	var quantity = stockIdMap.get(stockId).quantity;
	var expManageMap = stockIdMap.get(stockId).expManageMap;
	var notBatchStockMap = stockIdMap.get(stockId).notBatchStockMap;
	var afterSaleMap = stockIdMap.get(stockId).afterSaleMap;
	$(obj).parent().parent().find('td[id="data-quantity"]').text(quantity);
	var selectHtml = "";
	var afterSaleQty="";
	var afterSaleId="";
	var vendorCode="";
	var proDate="";
	var days="";
	if (expManageMap){
		proDate = new Date(expManageMap[0].proDate).format("yyyy-MM-dd");
		days = expManageMap[0].days;
		selectHtml = "<option disabled='disabled'>批次 &nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp| 到期时间 &nbsp&nbsp&nbsp| 剩余库存 </option>";
		for (var j = 0; j<expManageMap.length;j++) {
			selectHtml += "<option value='"+expManageMap[j].batchSelect+"'>" +expManageMap[j].batchSelect + "</option>" ;
		}
	}
	if (notBatchStockMap){
		selectHtml += "<option value='notBatch'>非保质期批次&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp|&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp|" + notBatchStockMap + "</option>" ;
	}
	if (afterSaleMap){
		afterSaleQty = afterSaleMap[0].quantity;
		afterSaleId = afterSaleMap[0].id;
		vendorCode = afterSaleMap[0].vendorCode;
	}
	$(obj).parent().parent().find('select[id="batchNo"]').html('');
	$(obj).parent().parent().find('select[id="batchNo"]').append(selectHtml);
	$(obj).parent().parent().find('input[name="proDate"]').val(proDate);
	$(obj).parent().parent().find('input[name="days"]').val(days);
	$(obj).parent().parent().find('input[name="afterSaleId"]').val(afterSaleId);
	$(obj).parent().parent().find('input[name="vendorCode"]').val(vendorCode);
	$(obj).parent().parent().find('td[id="data-afterSale"]').find('span').text(afterSaleQty);
}