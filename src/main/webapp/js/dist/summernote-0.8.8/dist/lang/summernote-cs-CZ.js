(function ($) {
  $.extend($.summernote.lang, {
    'cs-CZ': {
      font: {
        bold: 'Tučn<PERSON>',
        italic: 'Kurz<PERSON><PERSON>',
        underline: 'Podtržené',
        clear: 'Odstranit styl písma',
        height: 'Výška řádku',
        strikethrough: 'Přeškrtnuté',
        size: 'Velikost písma'
      },
      image: {
        image: '<PERSON><PERSON>r<PERSON><PERSON><PERSON>',
        insert: 'Vložit obrázek',
        resizeFull: 'Původní velikost',
        resizeHalf: 'Poloviční velikost',
        resizeQuarter: 'Čtvrteční velikost',
        floatLeft: 'Umístit doleva',
        floatRight: 'Umístit doprava',
        floatNone: 'Neobtékat textem',
        dragImageHere: 'Přetáhnout sem obrázek',
        selectFromFiles: 'Vybrat soubor',
        url: 'URL obrázku'
      },
      video: {
        video: 'Video',
        videoLink: 'Odkaz videa',
        insert: 'Vlo<PERSON>it video',
        url: 'URL videa?',
        providers: '(YouTube, Vimeo, Vine, Instagram, DailyMotion nebo Youku)'
      },
      link: {
        link: 'Odkaz',
        insert: 'Vytvořit odkaz',
        unlink: 'Zrušit odkaz',
        edit: 'Upravit',
        textToDisplay: 'Zobrazovaný text',
        url: 'Na jaké URL má tento odkaz vést?',
        openInNewWindow: 'Otevřít v novém okně'
      },
      table: {
        table: 'Tabulka'
      },
      hr: {
        insert: 'Vložit vodorovnou čáru'
      },
      style: {
        style: 'Styl',
        p: 'Normální',
        blockquote: 'Citace',
        pre: 'Kód',
        h1: 'Nadpis 1',
        h2: 'Nadpis 2',
        h3: 'Nadpis 3',
        h4: 'Nadpis 4',
        h5: 'Nadpis 5',
        h6: 'Nadpis 6'
      },
      lists: {
        unordered: 'Odrážkový seznam',
        ordered: 'Číselný seznam'
      },
      options: {
        help: 'Nápověda',
        fullscreen: 'Celá obrazovka',
        codeview: 'HTML kód'
      },
      paragraph: {
        paragraph: 'Odstavec',
        outdent: 'Zvětšit odsazení',
        indent: 'Zmenšit odsazení',
        left: 'Zarovnat doleva',
        center: 'Zarovnat na střed',
        right: 'Zarovnat doprava',
        justify: 'Zarovnat oboustranně'
      },
      color: {
        recent: 'Aktuální barva',
        more: 'Další barvy',
        background: 'Barva pozadí',
        foreground: 'Barva písma',
        transparent: 'Průhlednost',
        setTransparent: 'Nastavit průhlednost',
        reset: 'Obnovit',
        resetToDefault: 'Obnovit výchozí'
      },
      shortcut: {
        shortcuts: 'Klávesové zkratky',
        close: 'Zavřít',
        textFormatting: 'Formátování textu',
        action: 'Akce',
        paragraphFormatting: 'Formátování odstavce',
        documentStyle: 'Styl dokumentu'
      },
      history: {
        undo: 'Krok vzad',
        redo: 'Krok vpřed'
      }

    }
  });
})(jQuery);
