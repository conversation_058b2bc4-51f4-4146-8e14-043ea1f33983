/*!
 * jQuery twitter bootstrap wizard plugin
 * Examples and documentation at: http://github.com/VinceG/twitter-bootstrap-wizard
 * version 1.0
 * Requires jQuery v1.3.2 or later
 * Dual licensed under the MIT and GPL licenses:
 * http://www.opensource.org/licenses/mit-license.php
 * http://www.gnu.org/licenses/gpl.html
 * Authors: <AUTHORS>
 */
!function(e){var t=function(t,a){var t=e(t),n=this,i=e.extend({},e.fn.bootstrapWizard.defaults,a),r=null,o=null;this.rebindClick=function(e,t){e.unbind("click",t).bind("click",t)},this.fixNavigationButtons=function(){return r.length||(o.find("a:first").tab("show"),r=o.find("li:first")),e(i.previousSelector,t).toggleClass("disabled",n.firstIndex()>=n.currentIndex()),e(i.nextSelector,t).toggleClass("disabled",n.currentIndex()>=n.navigationLength()),n.rebindClick(e(i.nextSelector,t),n.next),n.rebindClick(e(i.previousSelector,t),n.previous),n.rebindClick(e(i.lastSelector,t),n.last),n.rebindClick(e(i.firstSelector,t),n.first),i.onTabShow&&"function"==typeof i.onTabShow&&i.onTabShow(r,o,n.currentIndex())===!1?!1:void 0},this.next=function(){return t.hasClass("last")?!1:i.onNext&&"function"==typeof i.onNext&&i.onNext(r,o,n.nextIndex())===!1?!1:($index=n.nextIndex(),$index>n.navigationLength()||o.find("li:eq("+$index+") a").tab("show"),void 0)},this.previous=function(){return t.hasClass("first")?!1:i.onPrevious&&"function"==typeof i.onPrevious&&i.onPrevious(r,o,n.previousIndex())===!1?!1:($index=n.previousIndex(),0>$index||o.find("li:eq("+$index+") a").tab("show"),void 0)},this.first=function(){return i.onFirst&&"function"==typeof i.onFirst&&i.onFirst(r,o,n.firstIndex())===!1?!1:t.hasClass("disabled")?!1:(o.find("li:eq(0) a").tab("show"),void 0)},this.last=function(){return i.onLast&&"function"==typeof i.onLast&&i.onLast(r,o,n.lastIndex())===!1?!1:t.hasClass("disabled")?!1:(o.find("li:eq("+n.navigationLength()+") a").tab("show"),void 0)},this.currentIndex=function(){return o.find("li").index(r)},this.firstIndex=function(){return 0},this.lastIndex=function(){return n.navigationLength()},this.getIndex=function(e){return o.find("li").index(e)},this.nextIndex=function(){return o.find("li").index(r)+1},this.previousIndex=function(){return o.find("li").index(r)-1},this.navigationLength=function(){return o.find("li").length-1},this.activeTab=function(){return r},this.nextTab=function(){return o.find("li:eq("+(n.currentIndex()+1)+")").length?o.find("li:eq("+(n.currentIndex()+1)+")"):null},this.previousTab=function(){return n.currentIndex()<=0?null:o.find("li:eq("+parseInt(n.currentIndex()-1)+")")},this.show=function(e){return t.find("li:eq("+e+") a").tab("show")},this.disable=function(e){o.find("li:eq("+e+")").addClass("disabled")},this.enable=function(e){o.find("li:eq("+e+")").removeClass("disabled")},this.hide=function(e){o.find("li:eq("+e+")").hide()},this.display=function(e){o.find("li:eq("+e+")").show()},this.remove=function(t){var a=t[0],n="undefined"!=typeof t[1]?t[1]:!1,i=o.find("li:eq("+a+")");if(n){var r=i.find("a").attr("href");e(r).remove()}i.remove()},o=t.find("ul:first",t),r=o.find("li.active",t),o.hasClass(i.tabClass)||o.addClass(i.tabClass),i.onInit&&"function"==typeof i.onInit&&i.onInit(r,o,0),i.onShow&&"function"==typeof i.onShow&&i.onShow(r,o,n.nextIndex()),n.fixNavigationButtons(),e('a[data-toggle="tab"]',o).on("click",function(t){var a=o.find("li").index(e(t.currentTarget).parent("li"));return i.onTabClick&&"function"==typeof i.onTabClick&&i.onTabClick(r,o,n.currentIndex(),a)===!1?!1:void 0}),e('a[data-toggle="tab"]',o).on("shown.bs.tab",function(t){$element=e(t.target).parent();var a=o.find("li").index($element);return $element.hasClass("disabled")?!1:i.onTabChange&&"function"==typeof i.onTabChange&&i.onTabChange(r,o,n.currentIndex(),a)===!1?!1:(r=$element,n.fixNavigationButtons(),void 0)})};e.fn.bootstrapWizard=function(a){if("string"==typeof a){var n=Array.prototype.slice.call(arguments,1);return 1===n.length&&n.toString(),this.data("bootstrapWizard")[a](n)}return this.each(function(){var n=e(this);if(!n.data("bootstrapWizard")){var i=new t(n,a);n.data("bootstrapWizard",i)}})},e.fn.bootstrapWizard.defaults={tabClass:"nav nav-pills",nextSelector:".wizard li.next",previousSelector:".wizard li.previous",firstSelector:".wizard li.first",lastSelector:".wizard li.last",onShow:null,onInit:null,onNext:null,onPrevious:null,onLast:null,onFirst:null,onTabChange:null,onTabClick:null,onTabShow:null}}(jQuery);