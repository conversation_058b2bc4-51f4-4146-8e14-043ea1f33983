/*
 * Translated default messages for the jQuery validation plugin.
 * Locale: SK (Slovak; slovenčina, slovenský jazyk)
 */
(function ($) {
	$.extend($.validator.messages, {
		required: "<PERSON><PERSON>né zadať.",
		maxlength: $.validator.format("Maximálne {0} znakov."),
		minlength: $.validator.format("Minimálne {0} znakov."),
		rangelength: $.validator.format("Minimálne {0} a Maximálne {0} znakov."),
		email: "E-mailová adresa musí byť platná.",
		url: "URL musí byť platný.",
		date: "<PERSON><PERSON><PERSON> byť dátum.",
		number: "Mus<PERSON> byť číslo.",
		digits: "<PERSON><PERSON><PERSON>e obsahovať iba číslice.",
		equalTo: "Dva hodnoty sa musia rovnať.",
		range: $.validator.format("Mus<PERSON> byť medzi {0} a {1}."),
		max: $.validator.format("Nemô<PERSON>e byť viac ako{0}."),
		min: $.validator.format("Nemôže byť menej ako{0}."),
		creditcard: "Číslo platobnej karty musí byť platné."
	});
}(j<PERSON><PERSON>y));