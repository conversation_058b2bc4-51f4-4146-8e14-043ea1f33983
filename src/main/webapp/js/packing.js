var jitPrinter;
var jitPrinterTag;
var jitPrinter75Tag;
var jitPrinter150;
// 缓存校验唯一码重复性
function checkSkuUuIdStorageCache(uuIdCacheKey, uuid){
    uuid = uuid.trim();
    if(uuid.indexOf("=") == -1){
        return true;
    }
    var storage = new WebStorageCache();
    var uuIdArr = storage.get(uuIdCacheKey);
    if (uuIdArr) {
        if (uuIdArr.indexOf(uuid) == -1){
            uuIdArr = uuIdArr  + "," + uuid;
        }else {
            return false;
        }
    } else {
        uuIdArr = uuid;
    }
    storage.set(uuIdCacheKey, uuIdArr , {exp : 5 * 60 * 60});
    return true;
}

// 清空唯一码缓存缓存
function initSkuUuIdStorageCache(uuIdCacheKey) {
    var storage = new WebStorageCache();
    //删除所有超时缓存
    storage.deleteAllExpires();
    storage.delete(uuIdCacheKey);
}

// 退件、返架缓存校验唯一码重复性
function checkRetrunUuIdStorageCache(uuIdCacheKey, uuid){
    uuid = uuid.trim();
    if(uuid.indexOf("=") == -1){
        return true;
    }
    var storage = new WebStorageCache();
    var uuIdArr = storage.get(uuIdCacheKey);
    if (uuIdArr) {
        if (uuIdArr.indexOf(uuid) != -1){
            return false;
        }
    }
    return true;
}

// 添加缓存唯一码
function addUuIdStorageCache(uuIdCacheKey, uuid){
    uuid = uuid.trim();
    if(uuid.indexOf("=") != -1){
        var storage = new WebStorageCache();
        var uuIdArr = storage.get(uuIdCacheKey);
        if (uuIdArr) {
            if (uuIdArr.indexOf(uuid) == -1){
                uuIdArr = uuIdArr  + "," + uuid;
                storage.set(uuIdCacheKey, uuIdArr , {exp : 5 * 60 * 60});
            }
        }else {
            storage.set(uuIdCacheKey, uuid , {exp : 5 * 60 * 60});
        }
    }
}

// 包装绑定APV
function bindingApvAndUuIdlist(apvNo, uuIdCacheKey){
    var storage = new WebStorageCache();
    var uuIdArr = storage.get(uuIdCacheKey);
    if (uuIdArr){
        var r = $.ajax({
            type : "POST",
            url :CONTEXT_PATH+"apv/packs/packAndBindingApv" ,
            data : {"uuIdArr": uuIdArr, "apvNo":apvNo},
            timeout : 100000,
            beforeSend : function() {
            },
            success : function(r) {

            },
            error : function() {
            }
        });
    }
}
function initScanTaskYSTSkuKeyQtyCache(){
    var storage = new WebStorageCache();
    storage.delete("scanTaskYSTSkuKey");
}
/**
 * 记录扫描任务唯一码数量
 * @param apvNo
 * @param taskNo
 * @param sku
 * @param needQty
 * @returns {number}
 */
function addScanTaskYSTSkuKeyQtyCache(apvNo, taskNo, sku, needQty) {
    debugger
    var storage = new WebStorageCache();
    var uuIdCacheKey = apvNo + "_" + taskNo + "_" + sku;
    var scanTaskYSTSkuKey = "scanTaskYSTSkuKey";
    var options = JSON.parse(storage.get(scanTaskYSTSkuKey));
    if (!options) {
        options = new Array();
    }
    //查找uuIdCacheKey是否存在
    var isExist = false;
    var qty = 0;
    for (var i = 0; i < options.length; i++) {
        var key = options[i].apvNo + "_" + options[i].taskNo + "_" + options[i].sku;
        if (key.toUpperCase() == uuIdCacheKey.toUpperCase()) {
            isExist = true;
            qty = options[i].qty;
            options[i].qty = qty + 1;
            break;
        }
    }
    qty = qty + 1;
    if (qty > needQty) {
        return qty;
    }
    if (isExist) {
        storage.set(scanTaskYSTSkuKey, JSON.stringify(options), {exp: 5 * 60 * 60});
        return qty;
    }
    var skuValue = {apvNo: apvNo,taskNo:taskNo,sku:sku,needQty:needQty, qty: qty};
    options.push(skuValue);
    storage.set(scanTaskYSTSkuKey, JSON.stringify(options), {exp: 5 * 60 * 60});
    return qty;
}

/**
 * 打印jit货品标签
 * @param apvNo
 * @param sku
 */
function printJitTag(apvNo, sku, copies, skuBarcode,local){
    var url = CONTEXT_PATH + "fba/packs/printJitTag?apvNo=" + apvNo + "&sku=" + sku;
    if (local){
        url = CONTEXT_PATH + "fba/packs/printLocalJitTag?apvNo=" + apvNo + "&sku=" + sku;
    }
    if (skuBarcode) {
        url = url + "&skuBarcode=" + skuBarcode;
    }
    $.ajax({
        url: url,
        type:"GET",
        async: false,
        success : function(response){
            $('#print_tag').html(response);
            var printUrl = $("input[name='printUrl']").val();
            var scanError = $("#scan-error").text();
            if (scanError){
                layer.alert(scanError);
                return false;
            }
            if (!copies) {
                copies = $("input[name='printCopies']").val();
            }
            if (printUrl) {
                debugger;
                printUrl = window.location.origin + printUrl;
                var pageLength = "60mm";
                var pageWidth = "60mm";
                printPdfCopies(printUrl, jitPrinter75Tag, pageLength, pageWidth, copies);
            } else if (local) {
                var map = new Map();
                $("input[name='base64']").each(function () {
                    var key = $(this).val();
                    var num = map.get(key);
                    if (num === undefined) {
                        num = 0;
                    }
                    num++;
                    map.put(key, num);
                })
                map.each(function (k, v) {
                    printCopies(k, jitPrinterTag, null, v, null);
                });
                // 打印合并后sku标签
                var mergePdfList = [];
                $("input[name='base64Sku']").each(function () {
                    var pdf = $(this).val();
                    mergePdfList.push(pdf);
                });
                copies = $("input[name='printCopies']").val();
                printMergeCopies(mergePdfList, jitPrinter75Tag, copies);
            }
        }
    });
}



/**
 * 用于合并打印
 * @param temuCodeUrls 合并打印的pdf列表
 * @param printerTag 打印机
 * @param copies 打印份数
 */
function printMergeCopies(mergePdfList,printerTag,copies){
    var LODOP = getLodop();
    LODOP.SET_PRINT_PAGESIZE(0, '70mm', '60mm', 'Note'); // 设置纸张大小
    $(mergePdfList).each(function(index,item){
        // 缩放pdf的内容，将2张pdf弄到一个页面上
        if (index == 0){
            LODOP.ADD_PRINT_PDF("1mm","0mm","70mm","20mm", item);
        }
        if (index == 1){
            LODOP.ADD_PRINT_PDF("21.39mm","0mm","70mm","40mm", item);
        }
    });
    LODOP.SET_PRINT_STYLEA(0,"PDFScaleMode",0);
    if (LODOP.SET_PRINTER_INDEX(printerTag)) {
        if(copies === undefined){
            copies = 1;
        }
        LODOP.SET_PRINT_COPIES(copies); // 打印份数
        LODOP.PRINT(); // 静默打印
        // LODOP.PRINT_DESIGN();
    }
}

function getPrinterList() {
    var LODOP = getLodop();
    let printeList=LODOP.Printers.list;
    $("#printerTag").html("");
    $("#printer").html("");
    $("#printer75Tag").html("");
    $("#printer150").html("");
    for (var i = 0; i < printeList.length; i++) {
        if (jitPrinter && jitPrinter==printeList[i].name){
            $("#printer").append("<option selected='selected' value='" + printeList[i].name + "'>" + printeList[i].name + "</option>");
        }else{
            $("#printer").append("<option  value='" + printeList[i].name + "'>" + printeList[i].name + "</option>");
        }
        if (jitPrinterTag && jitPrinterTag==printeList[i].name){
            $("#printerTag").append("<option selected='selected' value='" + printeList[i].name + "'>" + printeList[i].name + "</option>");
        }else{
            $("#printerTag").append("<option  value='" + printeList[i].name + "'>" + printeList[i].name + "</option>");
        }

        if (jitPrinter75Tag && jitPrinter75Tag==printeList[i].name){
            $("#printer75Tag").append("<option selected='selected' value='" + printeList[i].name + "'>" + printeList[i].name + "</option>");
        }else{
            $("#printer75Tag").append("<option  value='" + printeList[i].name + "'>" + printeList[i].name + "</option>");
        }
        if (jitPrinter150 && jitPrinter150==printeList[i].name){
            $("#printer150").append("<option selected='selected' value='" + printeList[i].name + "'>" + printeList[i].name + "</option>");
        }else{
            $("#printer150").append("<option  value='" + printeList[i].name + "'>" + printeList[i].name + "</option>");
        }
    }
}

function initPrinter() {
    var storage = new WebStorageCache();
    if (storage.get("jit_printer")) {
        jitPrinter = storage.get("jit_printer");
    }
    if (storage.get("jit_printerTag")) {
        jitPrinterTag = storage.get("jit_printerTag");

    }
    if (storage.get("jit_printer75Tag")) {
        jitPrinter75Tag = storage.get("jit_printer75Tag");
    }
    if (storage.get("jit_printer150")) {
        jitPrinter150 = storage.get("jit_printer150");
    }
}

function openPrintDialog() {
    dialog({
        title: '打印机配置',
        content: $('#openPrintDialog'),
        width: 600,
        top: 0,
        okValue: '确定',
        dragStart: function () {
        },
        ok: function () {
            var printerTag = $("#printerTag").val();
            var printer75Tag = $("#printer75Tag").val();
            var printer = $("#printer").val();
            var printer150 = $("#printer150").val();
            var storage = new WebStorageCache();
            storage.set("jit_printer75Tag", printer75Tag);
            storage.set("jit_printer", printer);
            storage.set("jit_printerTag", printerTag);
            storage.set("jit_printer150", printer150);
            window.location.reload();
        },
        cancelValue: '关闭',
        cancel: function () {
            return;
        }
    }).showModal();
}

function printGpsrTag(apvNo, sku) {
    printLocalGpsrTag(apvNo, sku, true);
}

function printLocalGpsrTag(apvNo, sku, local){
    var url = CONTEXT_PATH + "apv/packs/printGpsrTag?apvNo=" + apvNo;
    if (!local){
        url = CONTEXT_PATH + "fba/packs/printGpsrTag?apvNo=" + apvNo;
    }
    if (sku) {
        url = url + "&sku=" + sku;
    }
    $.ajax({
        url: url,
        type:"GET",
        success : function(response){
            $('#print_gpsr_tag').html(response);
            $('#print_gpsr_tag').find("#print_content").find(".gpsr-tag-print").each(function () {
                var copies = $(this).find("input[name='printQty']").val();
                var printSku = $(this).find("input[name='printSku']").val();
                var printHtml = $(this).html();
                var smtTagPdf = $(this).find("input[name='printUrl']").val();
                if (smtTagPdf && smtTagPdf.indexOf("pdf/gpsr") === -1) {
                    //layer.alert("标签打印失败,GPSR标签获取失败！");
                    return false;
                }
                if (printHtml === undefined && smtTagPdf === undefined){
                    //layer.alert("标签打印失败,GPSR标签获取失败！");
                    return false;
                }
                if (sku && sku === printSku) {
                    doPrintGpsrTag(printHtml, copies, smtTagPdf);
                    return false;
                }
                doPrintGpsrTag(printHtml, copies, smtTagPdf);
            })
        }
    });
}

function doPrintGpsrTag(html, copies, printPdf) {
    var pageLength = "70mm";//纸张长
    if (printPdf){
        pageWidth = "60mm";
    }
    var pageWidth = "60mm";//纸张宽
    var LODOP = getLodop();
    LODOP.SET_PRINT_PAGESIZE(0, pageLength, pageWidth, 'Note'); // 设置纸张大小
    if (printPdf) {
        LODOP.ADD_PRINT_PDF("2mm", 0, pageLength, pageWidth, window.location.origin + printPdf);
        LODOP.SET_PRINT_STYLEA(0, "PDFScalMode", 2);
    } else {
        LODOP.ADD_PRINT_HTM("2mm", 0, pageLength, pageWidth, html);
    }
    if (LODOP.SET_PRINTER_INDEX(jitPrinter75Tag)) {
        if (copies === undefined) {
            copies = 1;
        }
        LODOP.SET_PRINT_COPIES(copies); // 打印份数
        //LODOP.PREVIEW(); // 静默打印
        LODOP.PRINT(); // 静默打印
    }
}


function omsPrint(apvNo,printUrl){
    debugger
    if (!printUrl){
        printUrl = CONTEXT_PATH+"apv/packs/oms/mg/print?apvNo=";
    }
    $.getJSON(CONTEXT_PATH+"apv/packs/print/tip?apvNo="+apvNo, function(data) {
        if (data && data.message) {
            customizeLayer(data.message);
        }
        $.ajax({
            url: printUrl + apvNo,
            timeout : 100000,
            async: false,
            success : function(response){
                if (response.status == '200') {
                    var waybillSize = response.body.waybillSize;
                    var attachmentLabelUrl = response.body.attachmentLabelUrl;
                    var url = response.body.url;
                    var pdfUrl = response.body.pdfUrl;
                    var dtoJson = response.body.dtoJson;
                    var trackingNumber = response.body.trackingNumber;

                    if (dtoJson) {
                        doPrint(jitPrinter150, trackingNumber, dtoJson);
                    } else if (pdfUrl) {
                        document.getElementById('shippingOrderFrame').src = CONTEXT_PATH + pdfUrl;
                        doOmsPrint(waybillSize, pdfUrl);
                    } else if (url) {
                        if (url.indexOf("YST") === 0) {
                            document.getElementById('shippingOrderFrame').src = WEB_CONTEXT_PATH+"print-waybill/"+apvNo;
                        } else {
                            document.getElementById('shippingOrderFrame').src = url;
                            doOmsPrint(waybillSize, url);
                        }
                    } else {
                        layer.alert("获取面单失败！", {closeBtn: 0}, function (index) {
                            layer.close(index);
                        });
                    }
                    if (attachmentLabelUrl){
                        var attachmentLabelContent = "<iframe src=\"javascript:void(0)\" frameborder=\"0\" marginheight=\"0\" marginwidth=\"0\" frameborder=\"0\" scrolling=\"auto\" id=\"attachmentLabelContent\" name=\"attachmentLabelContent\" width=\"100%\" height=\"900px\"></iframe>";
                        $('#attachment_label').append(attachmentLabelContent);
                        document.getElementById('attachmentLabelContent').src = CONTEXT_PATH + "apv/packs/oms/pdfFile?printUrl=" + attachmentLabelUrl;
                        if (waybillSize && (waybillSize.indexOf('15') != -1 || waybillSize == '2')) {
                            document.getElementById('attachmentLabelContent').addEventListener('load', handleAttachIframeLoad);
                        } else {
                            doOmsPrint(waybillSize, attachmentLabelUrl);
                        }
                    }
                } else {
                    layer.alert(response.message, {closeBtn: 0}, function (index) {
                        layer.close(index);
                    });
                }
            },
            error:function(){
                layer.alert('获取面单失败！');
            }
        });
    });

}

// 用于打印面单加载完成后才进行打印操作，防止因网络问题，面单没有加载完就进行了打印调用从而导致打印不出面单
function handleIframeLoad(){
    setTimeout(function () {
        document.getElementById('shippingOrderFrame').contentWindow.print();
        document.getElementById('shippingOrderFrame').removeEventListener('load', handleIframeLoad);
    }, 200);
}

function handleAttachIframeLoad(){
    setTimeout(function () {
        document.getElementById('attachmentLabelContent').contentWindow.print();
        document.getElementById('attachmentLabelContent').removeEventListener('load', handleAttachIframeLoad);
    }, 200);
}

function doOmsPrint(waybillSize, printUrl) {
    debugger
    var pageLength = "100mm";
    var pageWidth = "100mm";
    var printerName = jitPrinter;
    var  allUrl = printUrl;
    if (printUrl.indexOf("http") > -1 && printUrl.indexOf(".pdf") > -1) {
        if (waybillSize && (waybillSize.indexOf('15') != -1 || waybillSize == '2')){
            document.getElementById('shippingOrderFrame').src = CONTEXT_PATH + "apv/packs/oms/pdfFile?printUrl=" + printUrl;
        }
    } else {
        allUrl = window.location.origin + CONTEXT_PATH + printUrl;
    }
    if (waybillSize && (waybillSize.indexOf('15') != -1 || waybillSize == '2')) {
        document.getElementById('shippingOrderFrame').addEventListener('load', handleIframeLoad);
    } else {
        printPdfCopies(allUrl, printerName, pageLength, pageWidth, 1);
    }
}