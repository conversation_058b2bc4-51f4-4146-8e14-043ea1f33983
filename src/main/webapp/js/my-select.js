!function($) {
    'use strict';

    $.fn['selectlt'] = function(method) {
        var methods = {
            init: function(option) {
                option = $.extend({}, {
                    selectedValue: undefined,
                    isfiltration: false, // 是否开启条件搜索
                    isdisabled: false,   // 是否禁用
                    data: []
                }, option);

                return this.each(function() {
                    var $element = $(this),
                        $valCol, $valInput, $selectList, $ul, backupsUlHtml, $select = $element.find('select'),
                        $selectOptions = $select.find('option');

                    // 获取到默认值
                    var defaultVal = $element.attr('value');
                    console.log(defaultVal);


                    $element.empty();
                    $valCol = $('<div>').addClass('val-col');
                    $valInput = $('<input type="text" readonly value="'+defaultVal+'">');

                    $selectList = $('<div class="select-list-b">');
                    $ul = $('<ul>');

                    if ($element.is('[placeholder]')) {
                        var selectArrtPlaceholderText = $element.attr('placeholder');
                        $valInput.attr('placeholder', selectArrtPlaceholderText);
                    }

                    // 处理数据渲染
                    if (option.data.length > 0) {
                        renderVisibleItems(option.data, $ul);
                    }

                    $selectOptions.each(function() {
                        var $el = $(this);
                        var _text = $el.text();
                        var _value = $el.val();
                        var $li = $('<li data-value=' + _value + '>' + _text + '</li>');

                        if ($el.is(':selected') && (_value != '' && _value != null)) {
                            $li.addClass('active');
                            $valInput.val(_text);
                        }
                        $ul.append($li);
                    });

                    backupsUlHtml = $ul.html(); // 备份原始 HTML
                    $valCol.append($valInput).append($select);
                    if (option.isfiltration) {
                        $selectList.addClass('f').append($('<input type="text" class="filtrate-input">'));
                    }
                    $selectList.append($ul);
                    $element.append($valCol).append($selectList);
                    $element.unbind();

                    // 处理点击事件
                    $element.on('click', 'li', function(e) {
                        e.stopPropagation();
                        var $el = $(this);
                        var selectedValue = $el.data('value');
                        var selectedText = $el.text();
                        if (!$el.hasClass('active')) {
                            $el.siblings().removeClass('active');
                            $el.addClass('active');
                            $select.val(selectedValue);
                            $valInput.val(selectedText);
                        }
                        $element.removeClass('st-dropdown');
                        $element.trigger('select-change', {
                            'el': $select,
                            'data': {
                                text: selectedText,
                                value: selectedValue
                            }
                        });
                    });
                    // 回显
                    if (defaultVal) {
                        // $element.find('li[data-value=' + option.selectedValue + ']').trigger('click');
                        // 设置默认值,并且从列表中找到对应的li,并且触发点击事件
                        const  $li =  $element.find('li[data-value=' + defaultVal + ']');
                        if($li.length>0){
                            $li.trigger('click');
                        }else{
                            // 如果没有从data中查看是否有对应的值,如果有则设置默认值,否则设置为空
                            var isHas = option.data.find(item=>item.id==defaultVal);
                            if(isHas){
                                // 更新插入到第一条一条li
                                $ul.prepend($('<li data-value=' + isHas.id + '>' + isHas.text + '</li>'));
                                $element.find('li[data-value=' + isHas.id + ']').trigger('click');
                            }else{
                                // $element.find('li[data-value=""][0]').trigger('click');
                                // 情况ul
                                $ul.empty();
                                // 插入一条无数据,禁止点击
                                $ul.append($('<li data-value="" style="color: #ccc;cursor: not-allowed; pointer-events: none;">无数据</li>'));
                            }
                        }
                    }

                    // 处理点击事件
                    if (!option.isdisabled) {
                        $element.on('click', function(e) {
                            e.stopPropagation();
                            $(this).toggleClass('st-dropdown');
                            var elementHeight = $(this).outerHeight() + 5;
                            $(this).find('.select-list-b').css('top', elementHeight);
                        });

                        // 处理过滤输入
                        $element.on('keyup', '.filtrate-input', debounce(function(e) {
                            e.stopPropagation();
                            var keyword = $(this).val().trim();
                            filterItems(keyword, backupsUlHtml, $ul, option.data);
                        }, 300));

                        $element.on('click', '.select-list-b', function(e) {
                            e.stopPropagation();
                        });

                        $(document).on('click', function(e) {
                            var target = $(e.target);
                            if (target.closest('.select-lt').length === 0) {
                                $('.select-lt').removeClass('st-dropdown');
                            } else if (target.closest($element).length === 1) {
                                $element.addClass('st-dropdown');
                            }
                        });
                    }
                });
            },
            setSelected(val) {
                $(this).find('li[data-value=' + val + ']').trigger('click');
            },
            getSelected(fuc) {
                var $selectOption = $(this).find('select option:selected');
                var data = {
                    text: $selectOption.text(),
                    value: $selectOption.val()
                };
                if (typeof fuc === 'function') {
                    fuc(data);
                }
                return data;
            }
        };

        function renderVisibleItems(data, $ul) {
            const visibleItemCount = 50; // 每次渲染的可视项数量
            const itemsToRender = data.slice(0, visibleItemCount);
            $ul.empty().html(itemsToRender.map(item => `<li data-value='${item.id}'>${item.text}</li>`).join(''));
        }

        function filterItems(keyword, backupsUlHtml, $ul, originalData) {
            $ul.empty().html(backupsUlHtml); // 恢复原始 HTML
            if (keyword !== '') {
                const filteredData = originalData.filter(item => {
                   return (item.text+'').toLowerCase().includes(keyword.toLowerCase())
                }
                   
                );
                renderVisibleItems(filteredData, $ul); // 渲染匹配项
            }
        }

        function debounce(func, delay) {
            let timeoutId;
            return function() {
                const context = this;
                const args = arguments;

                // 清除之前的定时器
                clearTimeout(timeoutId);
                
                // 设置新的定时器
                timeoutId = setTimeout(() => {
                    func.apply(context, args);
                }, delay);
            };
        }

        if (methods[method]) {
            return methods[method].apply(this, Array.prototype.slice.call(arguments, 1));
        } else if (typeof method === 'object' || !method) {
            return methods.init.apply(this, arguments);
        } else {
            $.error('Method ' + method + ' does not exist!');
        }
    };

}(jQuery);