<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">
<html>
<head>
	<title>易世通达仓库管理系统</title>
	<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
	<#include "/common/include.html">
	<style type="text/css">
		.modal-body input {
			width: 240px;
		}
		.top_bar{
			position:fixed;top:0px;
		}
		.rule-pick-label{
			width: 110px;
		}
		#add_modal .modal-dialog,#update_modal .modal-dialog{
			width: 1400px;
		}
		.pick-content-button{
			margin-top: 20px;
		}
        .rule-pick-label input{
            width: 40px;
        }
	</style>
</head>
<body>
<@header method="header" active="14020000" ><#include "/ftl/header.ftl"></@header>

<div id="page" style="background-color: rgb(231, 237, 248)">
	<div class="row">
		<div class="col-md-12" style="padding: 0">
			<ul class="page-breadcrumb breadcrumb" style="background-color: white;">
				<li><a href="#">系统配置</a></li>
				<li class="active">返架区域管理</li>
			</ul>
		</div>
	</div>

	<div class="container-fluid" style="background-color: white;border: none">
	<div class="row">
		<div class="col-md-12">
		<#assign query = domain.query />
			<form action="${CONTEXT_PATH}rule/areaSetting/search?type=RETURN"
					   class="form-horizontal form-bordered form-row-stripped"
					   method="post" modelAttribute="domain" name="returnAreaForm" id ="domain">
                <!-- 分页信息 -->
                <input id="page-no" type="hidden" name="page.pageNo" value="1">
                <input id="page-size" type="hidden" name="page.pageSize" value="${domain.page.pageSize}">

				<div class="form-body">
					<div class="form-group">
						<#if util('local') == '美景仓'>
							<label class="control-label col-md-1">仓库</label>
							<div class="col-md-3">
								<input class="form-control" name="query.warehouseId" type="text" value="${query.warehouseId}" onchange="reFlashAisle()">
							</div>
						</#if>

						<label class="control-label col-md-1">通道</label>
						<div class="col-md-3">
							<input class="form-control" id="returnAisleStr" name="query.returnAisleStr" type="text" value="${query.returnAisleStr}">
						</div>
					</div>
				</div>
				
				<div class="pick-content-button">
                    <@header method="auth" authCode="RETURN_SHELF_ZONE_MANAGE_ADD">
					<div class="pull-left" style="margin-left: 10px;">
                        <button type="button" class="btn  btn-default" onclick="setRule()">
                            <i class="icon-plus"></i> 添加
                        </button>
					</div>
                    </@header>
					<div class="col-md-offset-10" style="text-align: right">
						<button type="button" class="btn default" onclick="formReset()">
							<i class="icon-refresh"></i> 重置
						</button>
						<button type="submit" class="btn blue">
							<i class="icon-search"></i> 查询
						</button>
					</div>
				</div>
			</form>
		</div>
		<br/>
	</div>
	<div class="row">
		<div id="fixedDiv" class="col-md-12">
			<table class="table table-striped table-bordered table-hover table-condensed" id="fixedTab">
				<colgroup>
					<col width="15%" />
					<col width="15%" />
					<col width="45%" />
					<col width="10%" />
					<col width="10%" />
				</colgroup>
				<thead>
					<tr>
						<th>仓库</th>
						<th>名称</th>
						<th>通道</th>
						<th>操作</th>
						<th>日志</th>
					</tr>
				</thead>
			</table>
		</div>
		<div class="col-md-12" id="by-list-warp">
			<table class="table table-striped table-bordered table-hover table-condensed" id="by-list">
				<colgroup>
					<col width="15%" />
					<col width="15%" />
					<col width="45%" />
					<col width="10%" />
					<col width="10%" />
				</colgroup>
				<thead>
					<tr>
						<th>仓库</th>
						<th>名称</th>
						<th>通道</th>
						<th>操作</th>
						<th>日志</th>
					</tr>
				</thead>
				<tbody>
					<#list domain.whRuleAreaSettings as whRuleAreaSetting>
						<tr>
							<td>
								<input type="hidden" id ="${whRuleAreaSetting.id}_warehouseId" value="${whRuleAreaSetting.warehouseId}"/>
								<#if whRuleAreaSetting.warehouseId == 1>
									汉海达仓
								<#elseif whRuleAreaSetting.warehouseId == 2>
									美景仓
								<#else>
									${whRuleAreaSetting.warehouseId}
								</#if>
							</td>
							<td>${whRuleAreaSetting.area }</td>
							<td>${whRuleAreaSetting.aisle }</td>
							<td>
								<button type="button" class="btn btn-xs btn-info" onclick="setRule(${whRuleAreaSetting.id})">修改</button>
								<button type="button" class="btn btn-xs btn-info" onclick="deleteRule(${whRuleAreaSetting.id})">删除</button>
							</td>
							<td>
								<button type="button" class="btn btn-xs btn-info" onclick="viewLog(${whRuleAreaSetting.id}, 'whRuleReturnArea')">日志</button>
							</td>
						</tr>
					</#list>
					<tr>
						<td>${util('local')}未关联库区通道</td>
						<td></td>
						<td colspan="3">${domain.notRelateLocationRegionList}</td>
					</tr>
					<#if util('local') == '美景仓'>
						<tr>
							<td>汉海达未关联库区通道</td>
							<td></td>
							<td colspan="3">${domain.oldNotRelateLocationRegionList}</td>
						</tr>
					</#if>
				</tbody>
			</table>
		</div>
	</div>
    <div id="fixed-bottom">
        <div id="pager"></div>
    </div>
</div>
<div class="modal fade ui-popup" id="add_modal"  role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">
	<div class="modal-dialog">
		<div class="modal-content">
			<div class="modal-header">
				<button type="button" class="close" data-dismiss="modal" aria-hidden="true"></button>
				<h4 class="modal-title" id="myModalLabel">设置配置</h4>
				<input id="ruleId" type="hidden" value=""/>
			</div>
			<div class="modal-body form-horizontal portlet">
				<!--<div class="form-group">
					<label class="control-label col-md-2"><span class="required">*</span>仓库:</label>
					<div class="col-md-9">
						<input style="width: 50px" autocomplete="off" name="warehouseId" value="1" checked="checked" type="radio"><span>美景</span>
						<input style="width: 50px;margin-left: 50px;" autocomplete="off" name="warehouseId" value="2" type="radio"><span>汉海达</span>
					</div>
				</div>-->
				<div class="form-group">
					<label class="control-label col-md-2"><span class="required">*</span>返架区域名称:</label>
					<div class="col-md-9">
						<input class="form-control" id="returnArea" name="query.returnArea" type="text" value="">
					</div>
				</div>
				<div class="form-group">
					<label class="control-label col-md-2"><span class="required">*</span>通道:</label>
					<div class="col-md-9">
                    <div id="add-modal-select" style="width:1021px;padding-top: 5px;max-height: 200px;overflow-y: scroll;"></div>
                    <div id="add-modal-region-list" style="padding-top: 20px;max-height: 200px;overflow-y: scroll;"></div>
                </div>
				</div>
			</div>
			<div class="modal-footer">
				<button type="button" class="btn btn-primary" onclick="saveRuleReturn()">保存</button>
				<button type="button" class="btn btn-default" data-dismiss="modal">取消</button>
			</div>
		</div>
	</div>
</div>
<#include "/common/footer.html">
</div>
<script type="text/javascript" src="${CONTEXT_PATH}js/datepicker/WdatePicker.js"></script>
<script type="text/javascript" src="${CONTEXT_PATH}js/table-thead-fixed.js"></script>
<script type="text/javascript">
	function formReset() {
		$("#returnAisleStr").val("");
		$("#domain").submit();
	}
    var localWarehouse = "${util('local')}";
    // 分页
    var total = "${domain.page.totalCount}";
    var pageNo = "${domain.page.pageNo}";
    var pageSize = "${domain.page.pageSize}";
    $("#pager").initPager({ total : total, pageNo : pageNo, pageSize : pageSize});

    var localLocationRegionSelectJson = new Array();

    var localLocationRegionList = ${domain.locationRegionList};
    for (var i = 0; i < localLocationRegionList.length; i++) {
        var obj = {
            id: localLocationRegionList[i],
            text: localLocationRegionList[i]
        };
        localLocationRegionSelectJson.push(obj);
    }
    var oldLocationRegionSelectJson = new Array();
    var oldLocationRegionList = ${domain.oldLocationRegionList};
    for (var i = 0; i < oldLocationRegionList.length; i++) {
        var obj = {
            id: oldLocationRegionList[i],
            text: oldLocationRegionList[i]
        };
        oldLocationRegionSelectJson.push(obj);
    }
    var localAlreadyLocationRegionList = ${domain.alreadyLocationRegionList};
    var oldAlreadyLocationRegionList = ${domain.oldAlreadyLocationRegionList};
    function reFlashAisle() {
        var warehouseId = $("input[name='query.warehouseId']").val();
        if(warehouseId == 1){
            $("input[name='query.returnAisleStr']").select2({
                data : oldLocationRegionSelectJson,
                placeholder : "通道",
                multiple: true,
                allowClear : true
            });
		}else if(warehouseId == 2){
            $("input[name='query.returnAisleStr']").select2({
                data : localLocationRegionSelectJson,
                placeholder : "通道",
                multiple: true,
                allowClear : true
            });
		}else if(warehouseId == ''){
            oldAndNew();
		}
    }

    $("input[name='query.warehouseId']").select2({
        data : [{"id":"1", "text":"汉海达"},{"id":"2", "text":"美景"}],
        placeholder : "仓库",
        allowClear : true
    });

    $("input[name='query.returnAisleStr']").select2({
        data : localLocationRegionSelectJson,
        placeholder : "通道",
        multiple: true,
        allowClear : true
    });
    if (localWarehouse == '美景仓'){
        oldAndNew();
	}

	function oldAndNew() {
        var json = new Array();
        var localLocationRegionList = ${domain.locationRegionList};
        for (var i = 0; i < localLocationRegionList.length; i++) {
            var obj = {
                id: localLocationRegionList[i],
                text: localLocationRegionList[i]
            };
            json.push(obj);
        }
        for (var i = 0; i < oldLocationRegionList.length; i++) {
            var obj = {
                id: oldLocationRegionList[i],
                text: oldLocationRegionList[i]
            };
            json.push(obj);
        }

        $("input[name='query.returnAisleStr']").select2({
            data : json,
            placeholder : "通道",
            multiple: true,
            allowClear : true
        });
    }
    function setRule(id){
        if(id == null || id == ''){
            if (localWarehouse == '美景仓' && $("input[name='query.warehouseId']").val() == ''){
                layer.alert("请选择仓库!",'error');
                return false;
			}
		}

        var alreadyLocationRegionList = localAlreadyLocationRegionList;
        var locationRegionList = localLocationRegionList;
        if (id == null || id == ''){
            if (localWarehouse == '美景仓' && $("input[name='query.warehouseId']").val() == '1'){
				alreadyLocationRegionList = oldAlreadyLocationRegionList;
				locationRegionList = oldLocationRegionList;
            }
		}else{
            if (localWarehouse == '美景仓' && $("#" + id + "_warehouseId").val() == '1'){
                alreadyLocationRegionList = oldAlreadyLocationRegionList;
                locationRegionList = oldLocationRegionList;
            }
		}

        var updateLocationRegionList = [];
        $.post(CONTEXT_PATH + "rule/areaSetting/update", { "whRuleAreaSettingId": id }, function(data){
            var selectLocationList = new Array();
            if (data.status == 200) {
                if(data.body.whRuleAreaSetting && data.body.whRuleAreaSetting.aisle){
                	updateLocationRegionList = data.body.whRuleAreaSetting.aisle.split(',');
				}
                var html = "";
                // 遍历本仓所有通道
                for (var i = 0; i < locationRegionList.length; i++) {
                    var flag = false;
                    var flag2 = false;
                    var locationRegion = locationRegionList[i];
                    // 遍历本仓所有已选
                    for (var j = 0; j < alreadyLocationRegionList.length; j++) {
                        var alreadyLocationRegion = alreadyLocationRegionList[j];

                        if (locationRegion == alreadyLocationRegion) {
                            flag = true;
							// 遍历本条返架区域中的通道
                            for (var k = 0; k < updateLocationRegionList.length; k++) {
                                var updateLocationRegion = updateLocationRegionList[k];
                                if (alreadyLocationRegion == updateLocationRegion) {
                                    flag2 = true;
                                    break;
                                }
                            }
                        }
                    }
                    // 已用
                    if (flag == true) {
						//本通道用过
                        if (flag2 == true) {
                            selectLocationList.push(locationRegionList[i]);
                            html += "<label class='rule-pick-label'><input type='checkbox' checked='checked' onclick='addRegionHtml(this)' name='categoryList' value='" + locationRegionList[i] + "'/>" + locationRegionList[i] + "</label>";
                        } else{
                            // 其他区域用过
                            html += "<label class='rule-pick-label'><input type='checkbox' checked='checked' disabled='disabled' name='categoryList' value='" + locationRegionList[i] + "'/>" + locationRegionList[i] + "</label>";
                        }
                    } else {
                        // 未用
                        html += "<label class='rule-pick-label'><input type='checkbox' onclick='addRegionHtml(this)' name='categoryList' value='" + locationRegionList[i] + "'/>" + locationRegionList[i] + "</label>";
                    }
                }
                $("#add-modal-region-list").html(html);
                if(data.body.whRuleAreaSetting){
                	$("#returnArea").val(data.body.whRuleAreaSetting.area);
                	$('#ruleId').val(data.body.whRuleAreaSetting.id);
				}
                $("#add_modal").modal('show');
            } else {
                customizeLayer(data.message);
            }
            var selectHtml = selectLocationList.join(",");
            $("#add-modal-select").html(selectHtml);
        });
    }

    function addRegionHtml(obj) {
        var selectLocationHtml = $("#add-modal-select").text();
        var selectLocationList;
        if (selectLocationHtml) {
            selectLocationList = selectLocationHtml.split(',');
        } else {
            selectLocationList = new Array();
        }
        var checked = $(obj).is(':checked');
        var text = $(obj).val();
        if (checked) {
            selectLocationList.push(text);
        } else {
            var index = selectLocationList.indexOf(text);
            if (index > -1) {
                selectLocationList.splice(index, 1);
            }
        }
        var html = selectLocationList.join(",");
        $("#add-modal-select").html(html);
    }
    
    function saveRuleReturn() {
		var type = $('#ruleId').val() ? "UPDATE" : "ADD";
        var returnArea = $("#returnArea").val().trim();
        if(!returnArea){
            layer.alert("请输入返架区域名称!",'error');
            return false;
        }

        var checkedDatas = new Array();
        $("#add-modal-region-list").find("input[name='categoryList']:checked").each(function() {
            var disabled = $(this).attr('disabled');
            if (disabled != 'disabled') {
                var v = $(this).val();
                checkedDatas.push(v);
            }
        });

        if(checkedDatas.length == 0) {
            layer.alert("请选择通道",'error');
            return false;
        }

        var returnAisle="";
        for (var i = 0; i < checkedDatas.length; i++) {
            returnAisle += checkedDatas[i];
            if (i!=checkedDatas.length-1) {
                returnAisle += ",";
            }
        }

        $.post(CONTEXT_PATH + "rule/areaSetting/saveWhRuleReturnArea", {
            settingType: "RETURN",
            type:type,
            "whRuleAreaSetting.id": $('#ruleId').val(),
            "whRuleAreaSetting.area": returnArea,
            "whRuleAreaSetting.aisle": returnAisle,
            "whRuleAreaSetting.warehouseId": type=='ADD' ? $("input[name='query.warehouseId']").val() : ""
        }, function(data){
            if (data.status == 200) {
				layer.confirm(data.message,{
					icon: 1,
					btn: ['确定']
				},function () {
					location.reload();
				})
            } else {
                customizeLayer(data.message);
            }
        });
    }

    function deleteRule(id){
        if (confirm("确定是否删除？")) {
            $.post(CONTEXT_PATH + "rule/areaSetting/delete", { "whRuleAreaSettingId": id }, function(data){
                if (data.status == 200) {
					layer.confirm(data.message,{
						icon: 1,
						btn: ['确定']
					},function () {
						location.reload();
					})
                } else {
                    customizeLayer(data.message);
                }
            });
        }
    }
    $('#add_modal').on('hide.bs.modal', function () {
        window.location.reload();
    });
</script>
</body>
</html>