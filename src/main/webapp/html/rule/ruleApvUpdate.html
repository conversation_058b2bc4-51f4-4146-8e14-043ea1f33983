<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">
<html>
<head>
<title>易世通达仓库管理系统</title>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
	<#include "/common/include.html">
	<link rel="stylesheet" href="${CONTEXT_PATH }js/ztree/zTreeStyle.css" type="text/css">
	<style type="text/css">
	
.modal-body input {
	width: 240px;
}
.top_bar{
    position:fixed;top:0px;
}
ul.ztree {width:350px;min-height:500px;overflow-y:auto;overflow-x:auto;}
</style>
</head>
<body>
	<@header method="header" active="19000000" ><#include "/ftl/header.ftl"></@header>
	<div id="page" class="container-fluid" style="margin-left: 188px">
		<div class="row">
			<div class="col-md-12">
				<h2>规则设置</h2>
				<ul class="page-breadcrumb breadcrumb">
					<li><a href="#">库存匹配优先策略设置</a> </li>
					<li class="active">修改 </li>
				</ul>
			</div>
		</div>
			<div class="row">
				<div class="col-md-12">
					<#assign whRuleApv = domain.whRuleApv/>
					<form target="" id="role-form" name="mainForm" method="post" action="${CONTEXT_PATH}rule/apvs/update"> 
						<table class="table table-bordered table-condensed" id="role-create">
							<thead>
								<colgroup>
									<col width="200px;"/>
									<col />
								</colgroup>
							</thead>
							<tbody>
								<tr>
									<td class="form-label">
										<label class="control-label">优先级别</label>
									</td>
									<td>
										<input type="hidden" name="whRuleApv.id" value="${whRuleApv.id}" />
										<input class="form-control input-medium" type="number" name="whRuleApv.level" value="${whRuleApv.level}" />
									</td>
								</tr>
							
								<tr>
									<td class="form-label">
										<label class="control-label" style="height: 40px;line-height: 40px;">任务类型<span class="required">*</span></label>
									</td>
									<td>
										<div class="radio-list">
											<label class="radio-inline">
		                                    	<div class="radio">
		                                    		<span>
		                                    			<input type="radio" name="whRuleApv.type" value="1" ${(whRuleApv.type == 1) ? string('checked', '')} />
		                                    			 平台订单出库
		                                    		</span>
		                                    	</div>
		                                    </label>
		                                    <label class="radio-inline">
		                                    	<div class="radio"><span><input type="radio" name="whRuleApv.type" value="2" ${(whRuleApv.type == 2) ? string('checked' , '')} /> 海外仓调拨出库</span></div> 
		                                    </label>
	                                    </div>
									</td>
								</tr>
								
								<tr>
									<td class="form-label">
										<label class="control-label">平台名称</label>
									</td>
									<td>
										<input class="form-control input-medium" type="text" name="whRuleApv.platform" value="${whRuleApv.platform}" />
									</td>
								</tr>
								
								<tr>
									<td class="form-label">
										<label class="control-label">下单时间</label>
									</td>
									<td>
										<div class="input-group input-large date-picker input-daterange">
		                                    <input type="number" class="form-control" maxlength="3" name="whRuleApv.startDay" value="${whRuleApv.startDay}">
		                                    <span class="input-group-addon">-</span>
		                                    <input type="number" class="form-control" maxlength="3" name="whRuleApv.endDay" value="${whRuleApv.endDay}">
		                                 </div>
									</td>
								</tr>
								
								<tr>
									<td class="form-label">
										<label class="control-label">出货次序</label>
									</td>
									<td>
										<select class="form-control input-medium" name="whRuleApv.sortType">
											<option value="1" ${(whRuleApv.sortType == 1) ? string('selected' , '')}>后进先出</option>
											<option value="2" ${(whRuleApv.sortType == 2) ? string('selected' , '')}>先进先出</option>
										</select>
									</td>
								</tr>
									
								<tr>
									<td class="form-label">
										<label class="control-label">国家级别</label>
									</td>
									<td>
										<input class="form-control input-medium" type="text" name="whRuleApv.country" value="${whRuleApv.country}" />
									</td>
								</tr>
								
								<tr>
									<td class="form-label">
										<label class="control-label">物流级别</label>
									</td>
									<td>
										<input class="form-control input-medium" type="text" name="whRuleApv.logistics" value="${whRuleApv.logistics}" />
									</td>
								</tr>
							</tbody>
						</table>
						
						<div class="form-actions fluid">
                           <div class="col-md-offset-3 col-md-9">
                           		<button type="submit" class="btn green">
									<i class="icon-save"></i> 提交
								</button>
								
								<a class="btn btn-default" href='${CONTEXT_PATH}rule/apvs'>
									<i class="m-icon-swapleft"></i> 返回
								</a>
                           </div>
                        </div>
						
					</form>
				</div>
			</div>
		</div>
		<#include "/common/footer.html">
	</div>
	
	<script type="text/javascript">
	
		$.getJSON(CONTEXT_PATH + "apvs/getSaleChannel", function(json){
			if (json) {
				$("input[name='whRuleApv.platform']").select2({
					data : json,
					placeholder : "平台",
					multiple: true,
					allowClear : true
				});
			} else {
				$("input[name='whRuleApv.platform']").attr("placeholder", "没有权限数据").attr("readonly", true);
			}
		});
		
		$("input[name='whRuleApv.country']").select2({
			data : [{"id": "Saudi Arabia", "text": "沙特"}, {"id": "SA", "text": "沙特CODE"}, {"id": "United Arab Emirates", "text": "阿联酋"}, {"id": "AE", "text": "阿联酋CODE"}],
			placeholder : "国家",
			multiple: true,
			allowClear : true
		});
		
		$.getJSON(CONTEXT_PATH + "apvs/getShippingMethod", function(json){
			if (json) {
				$("input[name='whRuleApv.logistics']").select2({
					data : json,
					placeholder : "运输方式",
					multiple: true,
					allowClear : true
				});
			} else {
				$("input[name='whRuleApv.logistics']").attr("placeholder", "没有权限数据").attr("readonly", true);
			}
		});
		
		// 修改角色
		function createRole() {
			
			var requestParam = $("#role-form").serialize();
			
			var treeObj = $.fn.zTree.getZTreeObj("permission-tree");
			var nodes = treeObj.getCheckedNodes(true);
			
			//requestParam.permissions = [];
			
			if (nodes.length > 0) {
				
				for(var i = 0; i < nodes.length; i++) {
					
					var permissionCode = nodes[i].id;
					
					// 数据权限的唯一id是value
					/* if(nodes[i].type == "DATA") {
						permissionCode = nodes[i].value;
					} */
					
					var per = "&role.permissions["+ i +"].permissionCode=" + permissionCode;
					
					requestParam = requestParam + per;
					
					//requestParam.permissions.push({"permissionCode":permissionCode, "parentId":nodes[i].pId});
				}
			}
			
			$.post(CONTEXT_PATH + "rule/apvs/create", requestParam, function(data){
				var msg = "操作成功";
				
				toastr.options = {
			            closeButton: true,
			            debug: false,
			            timeOut: "5000",
			            positionClass: 'toast-top-center'
			    };
				toastr['success'](msg, "创建角色");
				
				setTimeout(jump, 1000);
			});
			
			function jump(){
				location.href = CONTEXT_PATH + "rule/apvs";
			}
			
		}
		
	</script>
</body>
</html>