<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">
<html>
<head>
	<meta content="text/html; charset=utf-8" http-equiv="Content-type">
	<title>Print</title>
	<style>
		@media print {
			.printbtn {
				display: none;
			}
		}

	</style>
</head>

<body style="padding: 0px; margin: 20px">
	<div class="printbtn">
		<button onclick="myPreview(true);">打印预览</button>
		&nbsp;
		<button onclick="print(${(domain.whApvGridList)?size});">打印</button>
		<input type="hidden" id="print-size" value="${(domain.whApvGridList)?size}">
		&nbsp;
		<button onclick="myPrintDesign();">打印设计</button>
		&nbsp;
	</div>
	<h5 style="color: red">大小 100*100</h5>
	<h5>
		选择打印机&nbsp;&nbsp;
		<select id="printer"></select>
	</h5>
<form id="print_content">
	<#if (domain.whApvGridList)?? && (domain.whApvGridList)?size gt 0>
		<div id="print-item-0">
			<style>
				table,td,th {
					border: 1px solid black;
					border-collapse: collapse;
					text-align: center;
					color: #000;
				}
			</style>
			<#list domain.whApvGridList as grid>
				<div style="width:98mm; height:99mm;text-align: center;margin-top: 0mm;">
					<div style="width: 98mm;height:2mm;text-align:center;">
					</div>
					<table class="table table-bordered table-condensed">
						<thead>
						<tr class="">
							<th>SKU</th>
							<th>SKU标题</th>
							<th>购买数量</th>
							<th>拣货数量</th>
						</tr>
						</thead>
						<tbody>
						<#list grid.gridItems as gridItem>
							<tr>
								<td>${gridItem.itemSku }</td>
								<td>${gridItem.skuName }</td>
								<td>${gridItem.itemQuantity }</td>
								<td>${gridItem.pickQuantity }</td>
							</tr>
						</#list>
						</tbody>
					</table>
				</div>
				<div id="print-item-${grid_index }"></div>
			</#list>

		</div>

	<#else >
		<h4 style="color: red;"><strong>参数错误！${domain.errorMsg}</strong></h4>
	</#if>
</form>

<object width="0" height="0" classid="clsid:2105C259-1E0C-4534-8141-A753534CB4CA" id="LODOP_OB">
	<embed width="0" height="0" type="application/x-print-lodop" id="LODOP_EM">
</object>

<!-- 打印插件 -->
<script type="text/javascript" src="${CONTEXT_PATH}js/LodopFuncs.js"></script>
<script src="${CONTEXT_PATH }js/assets/plugins/jquery-1.10.2.min.js" type="text/javascript" ></script>
<script src="${CONTEXT_PATH }js/print.js" type="text/javascript" ></script>
<script language="javascript">
	pageLength = "100mm";//纸张长
	pageWidth = "100mm";//纸张宽

	function print(size) {
		var pageSize = 20;
		var length = Math.ceil(size/(pageSize));
		for (var i = 1; i <= length; i++) {
			var start = (i-1)*pageSize + 1;
			var end = i*pageSize;
			doPrint(start, end);
		}
	};

	// 分页打印
	function doPrint(start, end) {

		LODOP = getLodop(document.getElementById('LODOP_OB'), document.getElementById('LODOP_EM'));
		LODOP.PRINT_INIT("打印");
		getPrinter();
		var innerHtml = "";

		$("[id^='print-item-']").each(function(i, obj) {
			var index = i + 1;
			if($.trim(end) == "") {
				debugger
				if(parseInt(start) == index) {
					innerHtml = $(obj).html();
					return false;
				}
			} else {
				if(index >= parseInt(start) && index <= parseInt(end)) {
					innerHtml = innerHtml + $(obj).html();
				}
			}

		});
		LODOP.ADD_PRINT_HTM(0, 0, pageLength, pageWidth, innerHtml);
		LODOP.SET_PRINT_STYLEA(0,"TableRowThickNess",100);
		LODOP.PRINT();
	};
	function myPreview() {
		CreatePrintPage();
		LODOP.PREVIEW();
	};
	function myPrintDesign() {
		CreatePrintPage();
		LODOP.PRINT_DESIGN();
	};
	function CreatePrintPage() {
		LODOP = getLodop(document.getElementById('LODOP_OB'), document.getElementById('LODOP_EM'));
		LODOP.PRINT_INIT("打印");
		getPrinter();
		LODOP.SET_PRINT_PAGESIZE(1,pageLength,pageWidth,"");
		LODOP.ADD_PRINT_HTM(0,0,"100%","100%",document.getElementById("print_content").innerHTML);
		LODOP.SET_PRINT_STYLEA(0,"TableRowThickNess",100);
	};

</script>


</body>
</html>