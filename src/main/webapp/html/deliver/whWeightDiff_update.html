<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">
<html>

	<head>
		<title>易世通达仓库管理系统</title>
		<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
		<#include "/common/include.html">
		<style type="text/css">
			.modal-body input {
				width: 240px;
			}
			
			.top_bar {
				position: fixed;
				top: 0px;
			}
			
			#task-list td {
				vertical-align: middle;
			}
		</style>
	</head>

	<body>
		<@header method="header" active="14030000"><#include "/ftl/header.ftl"></@header>

		<div id="page" style="background-color: rgb(231, 237, 248)">
			<div class="row">
				<div class="col-md-12" style="padding: 0">
					<ul class="page-breadcrumb breadcrumb" style="background-color: white;">
						<li>
							<a href="#">扫描交运</a>
						</li>
						<li class="active">结袋卡重量差配置</li>
						<li class="active">编辑</li>
					</ul>
				</div>
			</div>
			<!-- 内容 -->
			<div class="container-fluid" style="background-color: white;border: none">
				<!-- END PAGE HEADER-->
				<div class="row" style="padding-top: 80px;height: 760px">
					<div class="col-md-12">
						<form id="submit-form" action="${CONTEXT_PATH}scan/whWeightDiff/update" class="form-horizontal form-bordered form-row-stripped" method="post" modelAttribute="domain" name="whWeightDiffForm">
							<div class="form-body" style="width: 800px;margin: 0 auto">
								<div style="margin-bottom: 10px">
									<button type="submit" class="btn blue">
								<i class="icon-save"></i> 保存
							</button>
									<a class="btn btn-default" href='${CONTEXT_PATH}scan/whWeightDiff'>
										<i class="m-icon-swapleft"></i> 返回
									</a>
								</div>
								<table class="table table-striped table-bordered table-hover table-condensed mt10" id="item-list">
									<colgroup>
										<col width="30%" />
										<col width="40%" />
										<col width="30%" />
									</colgroup>
									<thead>
										<tr>
											<th>下限(≧)</th>
											<th></th>
											<th>上限(≦)</th>
										</tr>
									</thead>
									<tbody>
										<tr>
											<td>
												<input class="form-control input-xsmall item-miniMum" onblur="checkNumber(this)" placeholder="≧" name="whWeightDiff.miniMum" value="${domain.whWeightDiff.miniMum }" />
											</td>
											<td>
												——
											</td>
											<td>
												<input class="form-control input-xsmall item-maxiMum" onblur="checkNumber(this)" placeholder="≦" name="whWeightDiff.maxiMum" value="${domain.whWeightDiff.maxiMum }" />
											</td>
										</tr>
									</tbody>
								</table>
							</div>
						</form:form>
					</div>
					<br/>
				</div>

			</div>
			<#include "/common/footer.html">
		</div>
		<script type="text/javascript" src="${CONTEXT_PATH}js/datepicker/WdatePicker.js"></script>
		<script type="text/javascript" src="${CONTEXT_PATH}js/jquery.region.js"></script>
		<script type="text/javascript">
			$(document).ready(function() {
				var submitForm = $('#submit-form');
				// 表单校验
				submitForm.validate({
					rules: {

					},
					messages: {

					},
					invalidHandler: function(event, validator) {
						App.scrollTo(submitForm.find(".has-success:eq(0)"), -50);
					},
					submitHandler: function(form) {

						var miniMum = $("input[name='domain.miniMum").val();
						var maxiMum = $("input[name='domain.maxiMum").val();

						if(miniMum == '') {
							layer.alert("上限不能为空!", 'error');
							return false;
						}
						if(maxiMum == '') {
							layer.alert("下限不能为空!", 'error');
							return false;
						}

						var miniMum = miniMum * 1; // 转double
						var maxiMum = maxiMum * 1; // 转double
						if(miniMum >= maxiMum) {
							layer.alert("上限不能大于或等于区间下限!", 'error');
							return false;
						}

						App.blockUI();
						form.submit();
						return false;
					},
					/* 重写错误显示消息方法,以layer.alert方式弹出错误消息 */
					showErrors: function(errorMap, errorList) {
						var msg = "";
						$.each(errorList, function(i, v) {
							msg += (v.message + "\r\n");
						});
						if(msg != "") layer.alert(msg, 'error');
					},
					/* 失去焦点时不验证 */
					onfocusout: false
				});

			}); // end ready

			function checkNumber(obj) {
				var $this = $(obj);
				if($this.val() == '') {
					$this.val("");
					return;
				}
				/* var reg = /^\+?[1-9][0-9]*$/;
				if(!reg.test($this.val())) {
					layer.alert("请输入正确的正整数", 'error');
					$this.val("");
					return;
				} */
				var reg = /(^[\-0-9][0-9]*(.[0-9]+)?)$/;
				if(!reg.test($this.val())) {
					layer.alert("请输入正确重量", 'error');
					$this.val("");
					return;
				} 
				/* if(!/^[0-9,.]*$/.test($this.val())) {
					layer.alert("请输入正确重量", 'error');// 非负
					$this.val("");
					return;
				} */
			}
		</script>
	</body>

</html>