<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<title></title>
<script type="text/javascript">
	var cookieCode = null;
	var c_name = "CORE_USERLOGINSESSION";
	cookieCode = getCookie(c_name);
	if (cookieCode != null && cookieCode != "") {
		// 生产环境
		var url1="http://************/wms/syncCookie?cookieCode="+cookieCode;
		//var url2="http://***************/wms/syncCookie?cookieCode="+cookieCode;
        var url2="http://************/wms/syncCookie?cookieCode="+cookieCode;
		
		// 测试环境
		var url3="http://************/wms/syncCookie?cookieCode="+cookieCode;
		var url4="http://************/wms/syncCookie?cookieCode="+cookieCode;

		var localhostPaht=document.location.host;
		var urlArray;
		if (url1.indexOf(localhostPaht) != -1 || url2.indexOf(localhostPaht) != -1) {// 生产环境时
			urlArray = [url1,url2];
		}else if (url3.indexOf(localhostPaht) != -1 || url4.indexOf(localhostPaht) != -1) {// 测试环境时
			urlArray = [url3, url4];
		}else {// 本地开发环境时
			urlArray = [url3, url4];
		}
		for (var i = 0; i < urlArray.length; i++) {
			if (urlArray[i].indexOf(localhostPaht) == -1) {
				syncCookie(urlArray[i]);
			}
		}
	}
	
	function syncCookie(url){
		var script = document.createElement("script");
		script.type = "text/javascript";
		script.src = url;
		document.getElementsByTagName('head')[0].appendChild(script);
	}

	function getCookie(c_name) {
		if (document.cookie.length > 0) {
			c_start = document.cookie.indexOf(c_name + "=")
			if (c_start != -1) {
				c_start = c_start + c_name.length + 1
				c_end = document.cookie.indexOf(";", c_start)
				if (c_end == -1)
					c_end = document.cookie.length
				return unescape(document.cookie.substring(c_start, c_end))
			}
		}
		return ""
	}
</script>
</head>
<body>
</body>
</html>