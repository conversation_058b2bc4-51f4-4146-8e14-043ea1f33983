<style type="text/css">
    #grid-info-table {
        width: 100%;
        margin-top: 10px;
    }
    #grid-info-table #btn td{
        border: none;
        background-color: transparent;
        padding-left: 0px;
    }
    #grid-info-table #fbaNo td {
        height: 36px;
        text-align: center;
    }
    #grid-info-table .br td {
        height: 15px;
        border: none;
        background-color: transparent;
    }
    #grid-info-table td {
        border: 1px solid #bcbcbc;
        width: 140px;
        height: 50px;
        padding-left: 10px;
        background-color: #F9F9F9;
        font-size: 18px;
    }
    #grid-info-table td.form-label {
        text-align: center;
        background-color: #d7d7d7;
        font-weight: bold;
        font-size: 14px;
    }
    #grid-info-table td.red {
        color: red;
    }
    .size-bold {
        font-weight: bold;
        font-size: 30px;
    }
    .finish-tr td{
        border: none !important;
        background: none !important;
    }
    .finish-tr td button{
        height: 60px;
    }
    
</style>
<#assign fnSkuItem = domain.fnSkuItem >
<input type="hidden" id="taskNo" value="${domain.taskNo}"/>
<input type="hidden" id="taskBoxNo" value="${domain.boxNo}"/>
<#if (fnSkuItem)!>
    <!-- 不为空操作 -->
    <div style="border-bottom: 1px #CCC dotted; height: 700px;" class="scan_success">
        <div class="scan_success_sub" style="height: 700px">
            <input type="hidden" id="fbaId" value="${fnSkuItem.fbaId }" />
            <input type="hidden" id="fbaNo" value="${fnSkuItem.fbaNo }" />
            <input type="hidden" id="quantity" value="${fnSkuItem.quantity }" />
            <input type="hidden" id="allotQuantity" value="${fnSkuItem.allotQuantity }" />
            <input type="hidden" id="amazonSite" value="${fnSkuItem.site }" />
            <input type="hidden" id="lastFnSku" value="${domain.lastFnSku}"/>
            <input type="hidden" id="suitFlag" value="${fnSkuItem.suitFlag}"/>
            <table id="grid-info-table">
                <tbody>
                    <tr class="br"><td colspan="6"></td></tr>
                    <tr style="height: 50px;">
                        <td class="form-label">发货单号</td>
                        <td class="size-bold">${fnSkuItem.fbaNo}</td>
                        <td class="form-label">FNSKU</td>
                        <td class="size-bold" id="fnSku">${fnSkuItem.fnSku}</td>
                        <td class="form-label">SKU</td>
                        <td class="size-bold">${fnSkuItem.productSku}</td>
                    </tr>
                    <tr class="br"><td colspan="6"></td></tr>
                    <tr style="height: 100px;">
                        <td class="form-label">分配数量</td>
                        <td class="size-bold" id="allot_quantity">${fnSkuItem.allotQuantity}</td>
                        <td class="form-label">拣货数量</td>
                        <td class="size-bold" id="pick_quantity">${fnSkuItem.pickQuantity}</td>
                        <td class="form-label">已播数量</td>
                        <td class="size-bold red" id="check_grid_quantity">${fnSkuItem.gridQuantity}</td>
                    </tr>
                    <tr class="br"><td colspan="6"></td></tr>
                    <tr style="height: 50px;" class="finish-tr">
                        <td colspan="3">
                            <button id="fn-sku-finish" type="button" class="btn btn-info btn-modal" onclick="fnSkuFinish();">
                                FNSKU播种完成
                            </button>
                        </td>
                        <td colspan="3">
                            <button type="button" class="btn btn-info btn-modal" onclick="printSysFnSku();">
                                打印FSKU标签
                            </button>
                        </td>
                    </tr>

                </tbody>
            </table>
        </div>
    </div>
<#elseif (domain.taskNo)!>

<#elseif (domain.errorMsg)!>
    <div id = "scan-error" style="font-size:15px ;font-weight:bold" class="error scan_error">${domain.errorMsg}</div>
<#else >
    <div id = "scan-error" style="font-size:15px ;font-weight:bold" class="error scan_error">sku不属于当前拣货任务</div>
</#if>