<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">
<html>
<head>
<title>易世通达仓库管理系统</title>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<#include "/common/include.html">
</head>
<body class="page-header-fixed page-full-width">
	<div id="order-log-area" style="max-height: 405px;overflow: auto">
		<table class="table table-condensed table-bordered  table-striped">
			<colgroup>
				<col width="15%"/>
				<col width="55%"/>
				<col width="30%"/>
			</colgroup>
			<thead>
				<tr>
					<th>箱号</th>
					<th>发货单号</th>
					<th>操作</th>
				</tr>
			</thead>
			<tbody id="printXiangmai-body">
				<#list (domain.boxMap)?keys as key>
					<#assign item = domain.boxMap[key]>
					<tr>
						<td style="vertical-align: middle;text-align:center">${key}</td>
						<td style="vertical-align: middle;text-align:center">${item.tag}</td>
						<td style="vertical-align: middle;text-align:center"><a type="button" class="btn btn-xs btn-info" onclick="printSMTXiangmaiSingle('${item.fbaId}','${item.tag}')">打印箱唛</a></td>
					</tr>
				</#list>
			</tbody>
		</table>
	</div>
</body>
<!-- END BODY -->
<script type="text/javascript" src="${CONTEXT_PATH}js/datepicker/WdatePicker.js"></script>
<script type="text/javascript" src="${CONTEXT_PATH}js/jquery.form.js"></script>
<script type="text/javascript" src="${CONTEXT_PATH}js/table-thead-fixed.js"></script>
<script type="text/javascript" src="${CONTEXT_PATH}js/assets/plugins/jquery.region.js"></script>
<script type="text/javascript" src="${CONTEXT_PATH}js/LodopFuncs.js"></script>
<script type="text/javascript" src="${CONTEXT_PATH }js/print.js?v=${.now?datetime}"></script>
<script>
	function printSMTXiangmaiSingle(id,consignOrderNo) {
		$.ajax({
			url: CONTEXT_PATH + 'fba/allocation/localPrintXiangmai?id=' + id+'&consignOrderNo='+consignOrderNo,
			type: "GET",
			success : function(response){
				if (response.status == '200') {
					var pdfUrl = response.body.pdfUrl;
					debugger;

					/*if (!pdfUrl) {
						pdfUrl = window.location.origin + CONTEXT_PATH + response.message;
					}*/
					window.open(response.message);
					var boxNumber = response.location;
					printXiangMaiPdf(pdfUrl, 1, boxNumber);

				} else {
					layer.alert(response.message, {closeBtn: 0}, function (index) {
						layer.close(index);
					});
				}
			},
			error:function(){
				layer.alert('扫描失败，请重新扫描');
			}
		});
	}

	function printXiangMaiPdf(message, copies, jitBoxNumber){
		var LODOP = getLodop();
		LODOP.SET_PRINT_PAGESIZE(0, '100mm', '100mm', 'Note'); // 设置纸张大小
		LODOP.ADD_PRINT_PDF(0, 0, '100mm', '100mm', message);
		if (jitBoxNumber) {
			LODOP.ADD_PRINT_TEXT(18, 304, 70, 41, jitBoxNumber);
			LODOP.SET_PRINT_STYLEA(0, "FontSize", 22);
		}
		LODOP.SET_PRINT_STYLEA(0,"PDFScalMode",2);
		if(copies === undefined){
			copies = 1;
		}
		LODOP.SET_PRINT_COPIES(copies); // 打印份数
		// LODOP.PRINT_DESIGN();
		LODOP.PRINT(); // 静默打印
	}


</script>
</html>