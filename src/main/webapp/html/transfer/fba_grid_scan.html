<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">
<html>
<head>
    <title>易世通达仓库管理系统</title>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <#include "/common/include.html">
    <style type="text/css">
        .modal-body input {
            width: 240px;
        }

        .form-control {
            height: 42px;
        }
    </style>
</head>
<body>
<@header method="header" active="15060000"><#include "/ftl/header.ftl"></@header>
<div id="page" style="background-color: rgb(231, 237, 248)">
    <div class="row">
        <div class="col-md-12" style="padding: 0">
            <ul class="page-breadcrumb breadcrumb" style="background-color: white;">
                <li><a href="#">发货管理</a></li>
                <li class="active">FBA播种</li>
            </ul>
        </div>
    </div>
    <!-- 内容 -->
    <div class="container-fluid" style="background-color: white;border: none">
        <div class="row">
            <div class="form-body">
                <div class="form-group">
                    <div style="margin: 10px 0px;">
                        <label class="control-label col-md-2"
                               style="text-align: right; line-height: 42px">拣货任务号/篮筐号</label>
                        <div class="col-md-2">
                            <input type="text" class="form-control input-medium" name="box" id="box"
                                   onkeypress="if(event.keyCode==13) { inputBoxNoOrTaskNo(this); return false;}">
                            <input type="hidden" value="" id="scanBoxNo"/>
                        </div>
                        <label class="control-label col-md-2"
                               style="text-align: right; line-height: 42px">SKU/唯一码</label>
                        <div class="col-md-2">
                            <input type="text" class="form-control input-medium" name="sku" id="sku" onpaste="return false"
                                   onkeypress="if(event.keyCode==13) { inputNext(this); return false;}">
                        </div>
                    </div>
                </div>
                <div style="margin: 70px 0 15px;border-bottom: 1px solid #ccc"></div>

                <div style="height: 700px;" class="col-md-12">
                    <div id="scan_box_datas" class="col-md-4"></div>
                    <div class="col-md-6" style="margin-left: 50px;">
                        <h2>当前扫描：<span id="sku-history" style="font-weight:bold;color:red;"></span></h2>
                        <h2>扫描结果：<span id="scanResult" style="font-weight:bold;color:green;display: none;">成功</span>
                        </h2>
                        <div id="check_scan_datas" style="display: none;"></div>
                    </div>

                </div>

            </div>

            <form action="" method="post" target="_self" id="print_form"></form>
            <iframe style="width:1px;height:1px;border: 0 none;" name="printHtml" id="printHtml"></iframe>
        </div>
    </div>
    <#include "/common/footer.html">

</div>
<script type="text/javascript" src="${CONTEXT_PATH }js/web-storage-cache.js"></script>
<script type="text/javascript" src="${CONTEXT_PATH }js/jquery.sound.js?v=${.now?datetime}"></script>

<script type="text/javascript">

    var gridDiffBox = false;

    $(document).ready(function () {
        pageInit();
        $('#scanBoxNo').val('');
        $('#box').val('');
        $('#box').focus();

    }); // end ready


    // 初始化
    function pageInit() {
        $('#sku').val('');
        $('#check_scan_datas').html('');
    }


    //周转筐 触发
    function inputBoxNoOrTaskNo(obj) {
        let fnSkuFinishAttr = $('#fn-sku-finish').attr('disabled');
        var pickQuantity = $("#pick_quantity").html();
        var gridQuantity = $("#check_grid_quantity").html();
        if (pickQuantity && gridQuantity && pickQuantity == gridQuantity && !fnSkuFinishAttr){
            $('#sku').val('');
            layer.alert("FNSKU已播种完成，请先点击FNSKU播种完成");
            return;
        }

        if ($('#grid_amount').html()){
            layer.alert("请先点击拣货任务播种完成，再播下一个任务！");
            return;
        }

        var boxNo = $.trim(obj.value);
        if (!boxNo || boxNo.replace(/\s/g, '') == '') {
            layer.alert("请输入拣货任务号/篮筐号!");
            return;
        }

        var r = $.ajax({
            url: CONTEXT_PATH + "fba/grid/scanTaskNo",
            data: {box: boxNo},
            timeout: 100000,
            beforeSend: function () {
                App.blockUI();
            },
            success: function (response) {

                App.unblockUI();
                $("#scan_box_datas").html(response);
                var errorMsg = $("#scan_box_datas").find("#scan-box-error").html();
                if (errorMsg){
                    layer.alert(errorMsg, {closeBtn: 0}, function (index) {
                        layer.close(index);
                        $('#box').val('');
                        $('#box').select().focus();
                    });
                    return false;
                }
                $('#sku').select().focus();
                $('#scanBoxNo').val(boxNo);

            },
            error: function () {
                App.unblockUI();
                layer.alert('扫描失败，请重新扫描/');
            }

        });
    }


    // 扫描SKU
    function inputNext(obj) {
        let fnSkuFinishAttr = $('#fn-sku-finish').attr('disabled');
        var pickQuantity = $("#pick_quantity").html();
        var gridQuantity = $("#check_grid_quantity").html();
        if (pickQuantity && gridQuantity && pickQuantity == gridQuantity && !fnSkuFinishAttr){
            $('#sku').val('');
            layer.alert("FNSKU已播种完成，请先点击FNSKU播种完成");
            return;
        }
        var realSku = $.trim($('#sku').val());
        var uuid = realSku;
        $("#sku-history").text(uuid);
        //防止查询大数据
        if (!realSku || realSku.trim() == '') {
            layer.alert("请输入有效sku!");
            return;
        }

        // 周转筐
        var box = $('#scanBoxNo').val();

        if (!(realSku.indexOf("=") == -1)) {
            realSku = realSku.split('=')[0];
            $('#sku').val(realSku);
        }


        //核对提交数据
        var r = $.ajax({
            type: "get",
            async: false,
            url: CONTEXT_PATH + "fba/grid/grid/sku",
            data: {box: box, sku: realSku, uuid: uuid},
            timeout: 100000,
            beforeSend: function () {
                App.blockUI(null, null, 10000);
            },
            success: function (response) {
                App.unblockUI();
                $('#sku').val("");
                $('#sku').focus();
                var checkScanDatas = $("#check_scan_datas").html();
                $("#check_scan_datas").html(response);
                var errorMsg = $("#check_scan_datas").find("#scan-error").html();
                if (errorMsg) {
                    $("#check_scan_datas").html(checkScanDatas);
                    if (errorMsg.indexOf("重复扫描") != -1){
                        audioPlay('chongfu');
                        $('#scanResult').text('重复');
                    }else {
                        audioPlay('error');
                        $('#scanResult').text('失败');
                    }
                    $('#scanResult').css('color', 'red');
                    $('#scanResult').css('display', 'inline-block');
                    layer.open({
                        content: errorMsg,
                        yes: function (index, layero) {
                            $('#sku').val("");
                            $('#sku').focus();
                            layer.close(index);
                        },
                        cancel: function () {
                            $('#sku').val("");
                            $('#sku').focus();
                        }
                    });
                    return false;
                }
                audioPlay("success");
                $("#check_scan_datas").css('display', 'block');
                $('#scanResult').text('成功');
                $('#scanResult').css('color', 'green');
                $('#scanResult').css('display', 'inline-block');

                var gridAmount = $('#grid_amount').html();
                var gridAmountDiff = $('#grid_amount_diff').html();
                var gridSkuQuantity = $('#' + realSku).html();
                $('#grid_amount').html(parseInt(gridAmount) + 1);
                $('#grid_amount_diff').html(parseInt(gridAmountDiff) - 1);
                $('#' + realSku).html(parseInt(gridSkuQuantity) + 1);

                var pickQuantity = $("#pick_quantity").html();
                var gridQuantity = $("#check_grid_quantity").html();

                var suitFlag = $("#suitFlag").val();

                if (suitFlag && suitFlag === '1') {
                    if (gridQuantity === '1') {
                        layer.alert("套装");
                    }
                    setTimeout(audioPlay("taozhuang"), 1000);
                }

                if (pickQuantity && gridQuantity && pickQuantity == gridQuantity){
                    layer.alert("FNSKU已播种完成，请点击FNSKU播种完成");
                }
            },
            error: function () {
                App.unblockUI();
                $('#scanResult').text('失败');
                $('#scanResult').css('color', 'red');
                $('#scanResult').css('display', 'inline-block');
                layer.alert('扫描失败，请重新扫描');
            }
        });
    }
    
    function taskFinish() {
        var box = $('#bindBoxNo').val();
        var scanBoxNo = $('#scanBoxNo').val();
        if (box == null || box == ''){
            box = scanBoxNo;
        }

        $.ajax({
            url: CONTEXT_PATH + "fba/grid/complete",
            type: "POST",
            data: {box: $.trim(box)},
            timeout: 100000,
            beforeSend: function () {
                App.blockUI();
            },
            success: function (response) {

                App.unblockUI();

                if (response.status == '200') {
                    layer.alert("拣货任务播种完成！", {closeBtn: 0}, function (index) {
                        $("#check_scan_datas").html('');
                        $("#scan_box_datas").html('');
                        $('#scanBoxNo').val('');
                        $('#box').val('');
                        $('#box').focus();
                        layer.close(index);
                    });

                } else if (response.exceptionCode == '13') {
                    //播种少播
                    layer.alert(response.message, {closeBtn: 0}, function (index) {
                        layer.close(index);
                        bindDiffBox(box);
                    });
                } else if (response.exceptionCode == '11') {
                    //播种少拣
                    layer.alert(response.message, {closeBtn: 0}, function (index) {
                        layer.close(index);
                        bindStockOut(box);
                    });
                }
                else{
                    layer.alert(response.message, {closeBtn: 0}, function (index) {
                        layer.close(index);
                        $('#sku').select().focus();
                    });
                }
            },
            error: function () {
                App.unblockUI();
                layer.alert('fnsku播种完成失败！');
            }

        });
    }
    
    function fnSkuFinish() {

        var box = $('#bindBoxNo').val();
        var fbaNo = $("#fbaNo").val();
        var fnSku = $("#fnSku").html();
        var allotQuantity = $("#allotQuantity").val();
        var pickQuantity = $("#pick_quantity").html();
        var gridQuantity = $("#check_grid_quantity").html();

        var scanBoxNo = $('#scanBoxNo').val();
        if (box == null || box === ''){
            box = scanBoxNo;
        }

        if (gridQuantity && pickQuantity && allotQuantity) {
            normalGridComplete(box, fbaNo, fnSku);
        }

    }

    function printSysFnSku(){
        var fbaId = $("#fbaId").val();
        var fnSku = $("#fnSku").html();
        var amazonSite = $("#amazonSite").val();
        var siteArry = ["DE", "IT", "FR", "ES", "PL", "NL", "SE"];
        if (amazonSite && siteArry.includes(amazonSite)) {
            window.open(window.location.origin + "/wms-web/transferWhManagement/transitWarehouse/skuPrintingForEuropeanSites?id=" + fbaId + "&fnSku=" + fnSku);
        } else {
            var diglog = dialog({
                title: '打印SKU标签',
                width: 350,
                url: CONTEXT_PATH + "fba/allocation/toPrintSKU?id=" + fbaId + "&fnSku=" + fnSku,
                okValue: '打印',
                ok: function () {
                    var printWindow = $(this.iframeNode.contentWindow.document.body);
                    var body = printWindow.find("#printSku-body");

                    var items = body.find(".print-content");
                    if(items == null || items.length == 0){
                        layer.alert("没有要打印的SKU", "error");
                        return false;
                    }
                    var whFbaAllocation = {};
                    var allocationItems= [];
                    var ineffectivenessQuantity = true;
                    var result = false;
                    items.each(function () {
                        var fnSku = $(this).find('input[name="fnSku"]').val();
                        var sellSkuName = $(this).find('input[name="sellSkuName"]').val();
                        var productSku = $(this).find('input[name="productSku"]').val();
                        var productBarcode = $(this).find('input[name="productBarcode"]').val();
                        var quantity = $(this).find('input[name="quantity"]').val();
                        var allocationItem = {};
                        if(quantity == ""){
                            layer.alert("打印数量不能为空", "error");
                            result = false;
                            return false;
                        }
                        if(ineffectivenessQuantity && quantity != 0){
                            ineffectivenessQuantity = false;
                        }
                        allocationItem.fnSku = fnSku;
                        allocationItem.quantity = quantity;
                        allocationItem.sellSkuName = sellSkuName;
                        allocationItem.productSku = productSku;
                        allocationItem.productBarcode = productBarcode;

                        allocationItems.push(allocationItem);
                        result = true;
                    });
                    if(ineffectivenessQuantity){
                        layer.alert("打印数量不能全为0", "error");
                        return false;
                    }
                    whFbaAllocation.items = allocationItems;
                    if(result){
                        $.ajax({
                            url: CONTEXT_PATH + "fba/allocation/printSKU",
                            type: "POST",
                            data: {id: fbaId,allocationStr:JSON.stringify(whFbaAllocation)},
                            success: function(data){
                                var printSkuWindow=window.open();
                                printSkuWindow.document.write(data);
                                printSkuWindow.focus();
                            }
                        });
                    }
                },
                cancelValue: '取消',
                cancel: function () {}
            });
            diglog.show();
        }
    }


    function normalGridComplete(box, fbaNo, fnSku) {
        $.ajax({
            url: CONTEXT_PATH + "fba/grid/complete",
            type: "POST",
            data: {box: box, fbaNo: fbaNo, fnSku: fnSku},
            timeout: 100000,
            beforeSend: function () {
                App.blockUI();
            },
            success: function (response) {

                App.unblockUI();

                if (response.status == '200') {
                    //打印FNSKU标签
                    printFbaNoAndFnSku(fbaNo, fnSku);
                    layer.alert("FNSKU播种完成！", {closeBtn: 0}, function (index) {
                        //$("#check_scan_datas").html('');
                        $('#fn-sku-finish').attr('disabled', true);
                        layer.close(index);
                        $('#sku').select().focus();
                    });

                } else if (response.exceptionCode == '13') {
                    //播种少播
                    layer.alert(response.message, {closeBtn: 0}, function (index) {
                        layer.close(index);
                        bindDiffBox(box,fbaNo, fnSku);
                    });
                } else if (response.exceptionCode == '11') {
                    //播种少拣
                    layer.alert(response.message, {closeBtn: 0}, function (index) {
                        layer.close(index);
                        bindStockOut(box,fbaNo, fnSku);
                    });
                }
                else{
                    layer.alert(response.message, {closeBtn: 0}, function (index) {
                        layer.close(index);
                        $('#sku').select().focus();
                    });
                }
            },
            error: function () {
                App.unblockUI();
                layer.alert('fnsku播种完成失败！');
            }

        });
    }

    function bindDiffBox(box, fbaNo, fnSku) {
        var boxCayi = '';
        var diglog = dialog({
            title: '绑定播种差异周转筐',
            width: 350,
            height: 100,
            url: CONTEXT_PATH + "single/batch/scans/binding/gridDiff",
            okValue: '确定',
            ok: function () {
                var exportWindow = $(this.iframeNode.contentWindow.document.body);
                var submitForm = exportWindow.find("#submit-form");
                boxCayi = $(submitForm).find("#grid-diff-box").val();

                if (!boxCayi || boxCayi == '' || boxCayi.indexOf('BZCY') == -1) {
                    layer.alert("请绑定播种差异周转筐!");
                    $(submitForm).find("#grid-diff-box").val('');
                    return false;
                }
                bindingAndFinish(boxCayi, null, box,fbaNo, fnSku, diglog);
            },
        });
        diglog.show();
    }

    function bindStockOut(box, fbaNo, fnSku) {
        var diglog = dialog({
            title: '绑定播种异常周转筐',
            width: 350,
            height: 100,
            url: CONTEXT_PATH + "single/batch/scans/binding/stockout",
            okValue: '确定',
            ok: function () {
                var exportWindow = $(this.iframeNode.contentWindow.document.body);

                var submitForm = exportWindow.find("#submit-form");

                var stockoutBox = $(submitForm).find("#stockout-box").val();

                if (!stockoutBox || stockoutBox == '' || stockoutBox.indexOf('BZYC') == -1) {
                    layer.alert("请绑定播种异常周转筐!");

                    $(submitForm).find("#stockout-box").val('');
                    return false;
                }
                bindingAndFinish(null, stockoutBox, box,fbaNo, fnSku, diglog);
            },
        });
        diglog.show();
    }

    function bindingAndFinish(boxCayi, stockoutBox, box,fbaNo, fnSku, diglog) {
        var result;
        $.ajax({
            url : CONTEXT_PATH + "fba/grid/binding/stockout",
            type : 'post',
            async: false,//使用同步的方式,true为异步方式
            data : {boxCayi : $.trim(boxCayi), stockoutBox : $.trim(stockoutBox), box : box, fbaNo: fbaNo, fnSku: fnSku},//这里使用json对象
            success : function(data){
                if(data.status == 200){
                    layer.alert("绑定成功！");
                    if (fbaNo && fnSku) {
                        $('#fn-sku-finish').attr('disabled', true);
                    } else {
                        $('#fn-sku-finish').attr('disabled', true);
                        $("#scan_box_datas").html('');
                        $('#scanBoxNo').val('');
                        $('#box').val('');
                        $('#box').focus();
                    }

                    result = "sussecc";

                    if (diglog != null){
                        setTimeout(function () {
                            diglog.close().remove();
                        }, 100);
                    }

                }else{
                    layer.alert(data.message);
                    return false;
                }
            },
            fail:function(){
            }
        });

        if(result && result == 'sussecc'){
            return true;
        }else{
            return false;
        }
    }

    function printFbaNoAndFnSku(fbaNo, fnSku) {

        if (fbaNo && fnSku) {
            var printPageUrl = CONTEXT_PATH + "fba/grid/printFbaNoAndFnSku?fbaNo=" + fbaNo + "&fnSku=" + fnSku;
            /*window.open(printPageUrl);*/
            $('#printHtml').attr('src', printPageUrl);
            //自动打印
            setTimeout(IframeOnloadPrint, 100);
        }

    }

    // 这里Go
    var printed = false;

    function IframeOnloadPrint() {
        var iframe = document.getElementById("printHtml");
        // 加载完iframe后执行
        if (iframe.attachEvent) {
            iframe.attachEvent("onload", function () {
                printed = true;
                myPrint();
            });
        } else {
            iframe.onload = function () {
                printed = true;
                setTimeout(myPrint, 500);/*延时0.5秒打印*/
                return;
            };
        }

        printed = false;
    }

    /** 打印 **/
    var LODOP; //声明为全局变量
    function myPrint() {
        App.unblockUI();

        //先判断 内页中是否有 打印 方法 有的话直接调用
        try {
            if (typeof (eval(window.frames["printHtml"].myPrint)) == 'function') {
                return window.frames["printHtml"].myPrint();
            }
        } catch (e) {
        }

        try {
            CreatePrintPage();
            LODOP.PRINT();
        } catch (e) {

        }
    };
</script>
</body>
</html>