<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">
<html>

	<head>
		<title>易世通达仓库管理系统</title>
		<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
		<#include "/common/include.html">
		<style type="text/css">
			.modal-body input {
				width: 240px;
			}
		</style>
	</head>

	<body>
		<@header method="header" active="15040000"><#include "/ftl/header.ftl"></@header>
		<div id="page" style="background-color: rgb(231, 237, 248)">
			<div class="row">
				<div class="col-md-12" style="padding: 0">
					<ul class="page-breadcrumb breadcrumb" style="background-color: white;">
						<li>
							<a href="#">海外仓</a>
						</li>
						<li class="active">中转仓发货</li>
					</ul>
				</div>
			</div>

			<!-- 内容 -->
			<div class="container-fluid" style="background-color: white;border: none">
				<div class="row">
					<div class="col-md-12">
						<#assign query = domain.query>
						<form action="${CONTEXT_PATH}transit/shipment/search" class="form-horizontal form-bordered form-row-stripped" method="post" modelAttribute="domain" name="transitShipmentForm" id="domain">
							<!-- 分页信息 -->
							<input id="page-no" type="hidden" name="page.pageNo" value="1">
							<input id="page-size" type="hidden" name="page.pageSize" value="${domain.page.pageSize}">

							<div class="form-body">
								<div class="form-group">

									<label class="control-label col-md-1">结袋卡号</label>
									<div class="col-md-3">
										<input class="form-control" name="query.bagNo" type="text" placeholder="多个查询以逗号分开" value="${query.bagNo}">
									</div>

									<label class="control-label col-md-1">发货单号</label>
									<div class="col-md-3">
										<input class="form-control" name="query.apvNo" type="text" placeholder="请输入发货单号" value="${query.apvNo}">
									</div>

									<label class="control-label col-md-1">状态</label>
									<div class="col-md-3">
										<input class="form-control" name="query.status" type="text" value="${query.status}">
									</div>
								</div>
								<div class="form-group">
									<label class="control-label col-md-1">扫描人</label>
									<div class="col-md-3">
										<input class="form-control" name="query.scanner" type="text" value="${query.scanner}">
									</div>

									<label class="control-label col-md-1">核重人</label>
									<div class="col-md-3">
										<input class="form-control" name="query.checkWeightUser" type="text" value="${query.checkWeightUser}">
									</div>

									<label class="control-label col-md-1">装车人</label>
									<div class="col-md-3">
										<input class="form-control" name="query.loadUser" type="text" value="${query.loadUser}">
									</div>
								</div>


								<div class="form-group">
									<label class="control-label col-md-1">扫描时间</label>
									<div class="col-md-3">
										<div class="input-group">
											<input class="form-control Wdate" type="text" name="query.fromScanDate" placeholder="" readonly="readonly" value="${query.fromScanDate }" onfocus="WdatePicker({startDate:'%y-%M-%d 00:00:00',dateFmt:'yyyy-MM-dd HH:mm:ss',alwaysUseStartDate:true})">
											<span class="input-group-addon">到</span>
											<input class="form-control Wdate" type="text" name="query.toScanDate" placeholder="" readonly="readonly" value="${query.toScanDate }" onfocus="WdatePicker({startDate:'%y-%M-%d 23:59:59',dateFmt:'yyyy-MM-dd HH:mm:ss',alwaysUseStartDate:true})">
										</div>
									</div>

									<label class="control-label col-md-1">装车时间</label>
									<div class="col-md-3">
										<div class="input-group">
											<input class="form-control Wdate" type="text" name="query.fromLoadDate" placeholder="" readonly="readonly" value="${query.fromLoadDate }" onfocus="WdatePicker({startDate:'%y-%M-%d 00:00:00',dateFmt:'yyyy-MM-dd HH:mm:ss',alwaysUseStartDate:true})">
											<span class="input-group-addon">到</span>
											<input class="form-control Wdate" type="text" name="query.toLoadDate" placeholder="" readonly="readonly" value="${query.toLoadDate }" onfocus="WdatePicker({startDate:'%y-%M-%d 23:59:59',dateFmt:'yyyy-MM-dd HH:mm:ss',alwaysUseStartDate:true})">
										</div>
									</div>
									<label class="control-label col-md-1">发货平台</label>
									<div class="col-md-3">
										<input class="form-control" name="query.platform" type="text" placeholder="发货平台" value="${query.platform}">
									</div>
								</div>
                                <div class="form-group">
                                    <label class="control-label col-md-1">包裹号</label>
                                    <div class="col-md-3">
                                        <input class="form-control" name="query.packageSnStr" type="text" placeholder="包裹号,多个查询用逗号分割" value="${query.packageSnStr}">
                                    </div>
                                    <label class="control-label col-md-1">快递单号</label>
                                    <div class="col-md-3">
                                        <input class="form-control" name="query.expressOrderNos" type="text" placeholder="快递单号,多个查询用逗号分割" value="${query.expressOrderNos}">
                                    </div>
									<label class="control-label col-md-1">分拣筐号</label>
									<div class="col-md-3">
										<input class="form-control" name="query.pickBoxNo" type="text" digits="true" placeholder="TEMU分拣筐号" value="${query.pickBoxNo}">
									</div>
                                </div>
								<div class="form-group">
									<label class="control-label col-md-1">物流方式</label>
									<div class="col-md-3">
										<input class="form-control" name="query.logisticsCompanyCode" type="text" placeholder="物流方式" value="${query.logisticsCompanyCode}">
									</div>
								</div>
							</div>
							<div>
								<div class="pull-left" style="margin-left: 10px;line-height:40px;">
									<@header method="auth" authCode="GEN_JYM_EXPRESS">
										<button type="button" class="btn btn-default" onclick="genJymExpress()">
											<i class="icon-shopping-cart"></i> 加运美下单
										</button>
									</@header>
								</div>
								<div class="pull-left" style="margin-left: 10px;line-height:40px;">
									<button type="button" class="btn btn-default" onclick="printJymRetry(null,null,null)">
										<i class="icon-shopping-cart"></i> 加运美打印重试
									</button>
									<button type="button" class="btn btn-default" onclick="genKyExpress()">
										<i class="icon-shopping-cart"></i> 跨越下单
									</button>
									选择打印机&nbsp;&nbsp;
									<select id="printer" onchange="changePrinter()"></select>
								</div>
								<div class="col-md-offset-8" style="text-align: right">
									<button type="button" class="btn btn-default" onclick="formReset(this)">
										<i class="icon-refresh"></i> 重置
									</button>
									<button type="submit" class="btn blue">
										<i class="icon-search"></i> 查询
									</button>
									<button type="button" class="btn btn-default" onclick="download(1)">
										<i class="icon-download"></i> 下载结袋卡
									</button>

									<button type="button" class="btn btn-default" onclick="download(2)">
										<i class="icon-download"></i> 下载扫描详情
									</button>

								</div>
							</div>
						</form>
					</div>
					<br/>
				</div>

				<div class="row">
					<div class="col-md-12">
						<!-- 内容 -->
						<table class="table table-bordered table-hover table-condensed" id="scanShipment-list">
							<colgroup>
								<col width="5%" />
								<col width="10%" />
								<col width="7%" />
								<col width="8%" />
								<col width="5%" />
								<col width="5%" />
								<col width="5%" />
								<col width="5%" />
								<col width="5%" />
								<col width="10%" />
								<col width="10%" />
								<col width="7%" />
								<col width="7%" />
								<col width="4%" />
							</colgroup>
							<thead>
								<tr>
									<th><input type="checkbox" id="check-all" name="checkAll">全选</th>
									<th>结袋卡号</th>
									<th>物流方式</th>
									<th>扫描单数</th>
									<th>扫描总重量(g)</th>
									<th>实际总重量(g)</th>
									<th>重量差(g)</th>
									<th>结袋包材</th>
									<th>状态</th>
									<th>扫描人/时间</th>
									<th>装车人/时间</th>
									<th>快递方式</th>
									<th>快递单号</th>
									<th>操作</th>
								</tr>
							</thead>
							<tbody>
									<#list domain.whScanShipments as whScanShipment>
									<#if ( whScanShipment.minScope?? && whScanShipment.maxScope??) &&(whScanShipment.weightDiff lt whScanShipment.minScope || whScanShipment.weightDiff gt whScanShipment.maxScope) >
										<tr class="" style="color: red">
										<#else>
										<tr>
									</#if>
									
										<td>
											<input type="checkbox" value="${whScanShipment.id}" name="ids"> ${whScanShipment.id}
										</td>
										<td>
											${whScanShipment.bagNo}
											<#if whScanShipment.temuReceiveInfo??>
												<br/>${whScanShipment.temuReceiveInfo}
											</#if>
											<#if whScanShipment.pickBoxNo??>
												<br/><p style="color: red;">分拣筐号: ${whScanShipment.pickBoxNo}</p>
											</#if>
										</td>
										<td>${whScanShipment.logisticsCompanyCode}</td>
										<td>${whScanShipment.scanApvQuantity}</td>
										<td class="systemTotalWeight_${whScanShipment.id}">${whScanShipment.systemTotalWeight}</td>
										<td>${whScanShipment.actualTotalWeight}</td>
										<td>${whScanShipment.weightDiff}</td>
										<td>
											<#list [3,5,7] as statusValue>
												<#if whScanShipment.status == statusValue>
													<#if whScanShipment.packagingMaterialName??>${whScanShipment.packagingMaterialName}</#if>
													<#if whScanShipment.packagingMaterialWeight??>-${whScanShipment.packagingMaterialWeight}g</#if>
												</#if>
											</#list>
										</td>
										<td>${whScanShipment.statusName}</td>
										<td>${util('name',whScanShipment.scanner)} </br> ${whScanShipment.scanDate }</td>

										<td>${util('name',whScanShipment.loadUser)} </br> ${whScanShipment.loadDate }</td>

										<td>${whScanShipment.expressCompany }</td>
										<td class="expressOrderNo_${whScanShipment.id}">${whScanShipment.expressOrderNo}</td>
										<td><button type="button" class="btn btn-info btn-xs" onclick="viewLog(${whScanShipment.id}, 'scanShipment')" style="margin: 5px">日志</button>
											<#if whScanShipment.packageMethod?? && (whScanShipment.packageMethod == 4 || whScanShipment.packageMethod == 7)>
												<button type="button" class="btn btn-info btn-xs" onclick="printJitLanShou(${whScanShipment.id},'${whScanShipment.bagNo}')" style="margin: 5px">打印JIT揽收面单</button>
											</#if>
                                            <#if whScanShipment.bagNo?? && whScanShipment.bagNo?lower_case?contains("temu")>
                                                <button type="button" class="btn btn-info btn-xs" onclick="showDetails('${whScanShipment.id}')" style="margin: 5px">明细</button>
                                            </#if>
											<#if whScanShipment.expressCompany?? && whScanShipment.expressCompany?contains("极兔") && whScanShipment.expressOrderNo??>
												<button type="button" class="btn btn-info btn-xs" onclick="printJituPdf('${whScanShipment.expressOrderNo}')" style="margin: 5px">打印极兔面单</button>
											</#if>
											<#if whScanShipment.logisticsCompanyCode?? && whScanShipment.logisticsCompanyCode?contains("极兔") && whScanShipment.expressOrderNo == ''>
												<button type="button" class="btn btn-info btn-xs" onclick="genJtExpress('${whScanShipment.id}')" style="margin: 5px">极兔下单</button>
											</#if>
											<#if whScanShipment.logisticsCompanyCode?? && whScanShipment.logisticsCompanyCode?contains("跨越") && whScanShipment.expressOrderNo??>
												<button type="button" class="btn btn-info btn-xs" onclick="printKyeRetry('${whScanShipment.expressOrderNo}')" style="margin: 5px">打印跨越面单</button>
											</#if>
											<#if whScanShipment.platform?? && whScanShipment.platform?contains("ASN")>
												<button type="button" class="btn btn-info btn-xs" onclick="printAsnLanShou(${whScanShipment.id})" style="margin: 5px">打印揽收单</button>
												<button type="button" class="btn btn-info btn-xs" onclick='printPocketCard("${whScanShipment.bagNo}")' style="margin: 5px">打印结袋卡</button>
											</#if>
											<#if whScanShipment.platform?? && whScanShipment.platform?contains("ASN")>
												<button type="button" class="btn btn-info btn-xs" onclick="printAsnXiangmai('${whScanShipment.bagNo}')" style="margin: 5px">打印箱唛</button>
											</#if>

										</td>
									</tr>
								</#list>
							</tbody>
						</table>
						<!-- 内容end -->
					</div>
				</div>
				<div id="fixed-bottom">
					<div id="pager"></div>
				</div>
			</div>

			<#include "/common/footer.html">
		</div>
		<div style="display: none" id="printWindow">
			<iframe src="javascript:void(0);" frameborder="0" marginheight="0" marginwidth="0" scrolling="auto"
					id="printFrame" name="printFrame" width="100%" height="600px" hidden></iframe>
		</div>
		<object width="0" height="0" classid="clsid:2105C259-1E0C-4534-8141-A753534CB4CA" id="LODOP_OB">
			<embed width="0" height="0" type="application/x-print-lodop" id="LODOP_EM">
		</object>

		<script type="text/javascript" src="${CONTEXT_PATH}js/LodopFuncs.js"></script>
		<script type="text/javascript" src="${CONTEXT_PATH}js/datepicker/WdatePicker.js"></script>
		<script type="text/javascript" src="${CONTEXT_PATH}js/jquery.form.js"></script>
		<!--<script type="text/javascript" src="${CONTEXT_PATH}js/table-thead-fixed.js"></script>-->
		<script type="text/javascript" src="${CONTEXT_PATH}js/assets/plugins/jquery.region.js"></script>
		<script type="text/javascript" src="${CONTEXT_PATH}js/print.js"></script>
		<script type="text/javascript">
			// 分页
			var total = "${domain.page.totalCount}";
			var pageNo = "${domain.page.pageNo}";
			var pageSize = "${domain.page.pageSize}";
			$("#pager").initPager({ total: total, pageNo: pageNo, pageSize: pageSize });

			var heights = $("body").height();
			if(heights > 910) {
				$("#fixed-bottom").addClass("fixed-bottom-height")
			}
			window.onload = function () {
				printerList();
			};

			// 状态
			var statuses = ${domain.statuses};
			$("input[name='query.status']").select2({
				data : statuses,
				placeholder : "状态",
				allowClear : true
			});

			var collectCompanyCodeJson = [{"id":"TEMU","text":"TEMU"},{"id":"SMTJIT","text":"SMTJIT"},{"id":"SHEIN","text":"SHEIN"},{"id":"ASN","text":"ASN"}];
			$("input[name='query.platform']").select2({
				data : collectCompanyCodeJson,
				placeholder : "发货平台",
				allowClear : true
			});

			// 物流公司
			var shippingCompanyArray = ${domain.shippingCompanySelect };
			$("input[name='query.logisticsCompanyCode']").select2({
				data: shippingCompanyArray,
				placeholder: "物流公司",
				multiple: false,
				allowClear: true
			});

			// 全选
			var checkAll = $("input[name='checkAll']");

			// 子选项
			var itemIds = $("input[name='ids']");
			checkAll.change(
				function() {
					itemIds.prop("checked", $(this).prop("checked"));
					itemIds.each(function() {
						var f = $(this).is(":checked");
						var checkClass = $(this).prop("class");
						$("." + checkClass).each(function() {
							$(this).prop("checked", f);
						})
					})
				}
			);
			// 获取选中的结袋卡
			function getCheckedIds() {
				var checkedIds = $("input[name='ids']:checked");
				return checkedIds;
			}

			// 打开明细
			function showDetails(id){
			    if (!id){
			        layer.alert("id为空!","error");
			        return;
                }
                window.open(CONTEXT_PATH + "/transit/shipment/showDetails?id=" + id);
            }

			// 扫描人
			$.getJSON(CONTEXT_PATH + "system/saleusers/userByRoleName?roleName=扫描交运", function(json) {
				if(json) {
					$("input[name='query.scanner']").select2({
						data: json,
						placeholder: "扫描人",
						allowClear: true
					});
                    $("input[name='query.checkWeightUser']").select2({
                        data: json,
                        placeholder: "核重人",
                        allowClear: true
                    });
                    $("input[name='query.loadUser']").select2({
                        data: json,
                        placeholder: "装车人",
                        allowClear: true
                    });
				} else {
					$("input[name='query.scanner']").attr("placeholder", "该角色没有相关人员!").attr("readonly", true);
                    $("input[name='query.checkWeightUser']").attr("placeholder", "该角色没有相关人员!").attr("readonly", true);
                    $("input[name='query.loadUser']").attr("placeholder", "该角色没有相关人员!").attr("readonly", true);
				}
			});
			


            // 导出
			function download(type) {
				if(getCheckedIds().length == 0) {
					var param = $("#domain").serialize();
					if (type==1) {
						window.open(CONTEXT_PATH + "transit/shipment/download?" + param);
					}else if (type==2) {
						window.open(CONTEXT_PATH + "transit/shipment/downloadDetail?" + param);
					}
				} else {
					downloadByPost(type);
				}
			}

			// 超过500条不能用GET请求
			function downloadByPost(type) {
				var checkedDatas = getCheckedIds();
				var ids = "";
				for(var i = 0; i < checkedDatas.length; i++) {
					var check = checkedDatas[i];
					var id = $(check).val();
					ids += id;
					if(i != checkedDatas.length - 1) {
						ids += ",";
					}
				}
				var url = "";
				if (type==1) {
					url = CONTEXT_PATH + "transit/shipment/download";
				}else if (type==2) {
					url = CONTEXT_PATH + "transit/shipment/downloadDetail";
				}
				var tempForm = document.createElement("form");
				tempForm.id = "tempForm";
				tempForm.method = "post";
				tempForm.action = url;
				tempForm.target = "blank";
				var hideInput = document.createElement("input");
				hideInput.type = "hidden";
				hideInput.name = "ids";
				hideInput.value = ids;
				tempForm.appendChild(hideInput);
				if(tempForm.attachEvent) { // IE 
					tempForm.attachEvent("onsubmit", function() { window.open('about:blank', 'blank'); });
				} else if(tempForm.addEventListener) { // DOM Level 2 standard  
					tempForm.addEventListener("onsubmit", function() { window.open('about:blank', 'blank'); });
				}
				document.body.appendChild(tempForm);
				if(document.createEvent) { // DOM Level 2 standard  
					evt = document.createEvent("MouseEvents");
					evt.initMouseEvent("submit", true, true, window, 0, 0, 0, 0, 0, false, false, false, false, 0, null);
					tempForm.dispatchEvent(evt);
				} else if(tempForm.fireEvent) { // IE  
					tempForm.fireEvent('onsubmit');
				}
				//必须手动的触发        
				tempForm.submit();
				document.body.removeChild(tempForm);
			}

			function printJitLanShou(id,bagNo) {
				var url = CONTEXT_PATH+"transit/shipment/getJitLanShou?id=" + id+"&bagNo="+bagNo;
				$.get(url, function(data){
					if (data.status == 200) {
						$('#printFrame').attr('src', data.message);
						window.open(data.message);
						/*document.getElementById("printFrame").onload = function() {
							setTimeout(function() {
								document.getElementById("printFrame").contentWindow.print();
							}, 500);
						};*/
					} else {
						customizeLayer(data.message);
					}
				});
			}

			function printAsnLanShou(id) {
				var url = CONTEXT_PATH+"transit/shipment/getAsnLanShou?id=" + id;
				$.get(url, function(data){
					if (data.status == 200) {
						$('#printFrame').attr('src', data.message);
						window.open(data.message);
					} else {
						customizeLayer(data.message);
					}
				});
			}

			function printPocketCard(bagNo){
				$.post(CONTEXT_PATH + "scan/deliverOrder/printPocketCard?bagNo="+bagNo, function(data){
					if (data) {
						window.open(CONTEXT_PATH+data);
					}
				});
			}

			function printAsnXiangmai(bagNo) {
				debugger;
				var url = CONTEXT_PATH+"transit/shipment/printAsnXiangmai?bagNo=" + bagNo;
				$.get(url, function(data){
					if (data.status == '200') {
						var pdfUrlList = data.body.basePdfUrlList;
						debugger;
						for(var i = 0; i < pdfUrlList.length; i++) {
							printXiangMaiPdf(pdfUrlList[i], 1, null);
						}
					} else {
						layer.alert(data.message, {closeBtn: 0}, function (index) {
							layer.close(index);
						});
					}
				});
			}
			function printXiangMaiPdf(message, copies, jitBoxNumber){
				var LODOP = getLodop();
				LODOP.SET_PRINT_PAGESIZE(0, '100mm', '100mm', 'Note'); // 设置纸张大小
				LODOP.ADD_PRINT_PDF(0, 0, '100mm', '100mm', message);
				if (jitBoxNumber) {
					LODOP.ADD_PRINT_TEXT(18, 304, 70, 41, jitBoxNumber);
					LODOP.SET_PRINT_STYLEA(0, "FontSize", 22);
				}
				LODOP.SET_PRINT_STYLEA(0,"PDFScalMode",2);
				if(copies === undefined){
					copies = 1;
				}
				LODOP.SET_PRINT_COPIES(copies); // 打印份数
				LODOP.PRINT(); // 静默打印
			}

			// 加运美下单
			function genJymExpress(){
				let checkedIds = getCheckedIds();
				if(checkedIds.length == 0) {
					layer.alert("请选择要操作的数据", 'error');
					return;
				}
				$.post(CONTEXT_PATH + "transit/shipment/createJymExpress", checkedIds.serialize(), function(data){
					if (data.status == 200) {
						let expressNo = data.body.expressNo;
						let expressInfo = data.body.expressInfo;
						let weightMap = data.body.weightMap;
						let codeArr = expressNo.split(';');
						codeArr.forEach(function (billNo){
							expressInfo.billCode = billNo
							expressInfo.feeWeight = weightMap[billNo];
							printJymExpressPdf(expressInfo, 'false');
						})
						window.location.reload();
					} else {
						customizeLayer(data.message);
					}
				});
			}

			// 加运美打印重试
			function printJymRetry(billNo, subBillNo, weight){
				//临时修改。
				let checkedIds = getCheckedIds();
				if(checkedIds.length == 0) {
					layer.alert("请选择要操作的数据", 'error');
					return;
				}
				var bool=false;
				checkedIds.each(function (index,item){
					debugger;
					//获取快递单号
					var billNos=$("td[class='expressOrderNo_"+ $(item).val()+"']").text();
					var systemTotalWeight=$("td[class='systemTotalWeight_"+ $(item).val()+"']").text()/1000;
					//判断billNo是不包含JYM
					billNo=billNos;
					if (billNos.indexOf("JYM") == -1){
						//去除后面三位 前面加上JYM
						billNo="JYM"+billNos.substring(0,billNo.length-3);
					}
					bool=true;
					$.get(CONTEXT_PATH + "transit/shipment/printJymRetry?billNo="+billNo, function(data){
						if (data.status == 200) {
							let expressInfo = data.body.expressInfo;
							expressInfo.billCode = billNos
							expressInfo.feeWeight = systemTotalWeight;
							printJymExpressPdf(expressInfo, 'false');
						} else {
							console.log(data.message);
						}
					});
				})
				if (!bool){
					layer.alert("请勾选主单号条目重试", 'error');
				}

			}

			function printJituPdf(expressNo) {
				if (expressNo && expressNo.length > 0) {
					var url = CONTEXT_PATH + "getPdfBase64?billCode=" + expressNo;
					$.get(url, function (data) {
						if (data.indexOf("pdf") != -1) {
							var pdfUrl = CONTEXT_PATH + data.split("static/")[1];
							$('#printFrame').attr('src', pdfUrl);
							document.getElementById("printFrame").onload = function () {
								document.getElementById("printFrame").contentWindow.print();
							};
						} else {
							customizeLayer(data);
						}
					});
				}
			}

			// 极兔处理历史数据
			function genJtExpress(shipmentId){
				$.get(CONTEXT_PATH + "transit/shipment/genJtExpress?shipmentId="+shipmentId, function(data){
					if (data.status == 200) {
						window.location.reload();
					} else {
						console.log(data.message);
					}
				});
			}

			// 跨越下单
			function genKyExpress(){
				let checkedIds = getCheckedIds();
				if(checkedIds.length == 0) {
					layer.alert("请选择要操作的数据", 'error');
					return;
				}
				$.ajax({
					url: CONTEXT_PATH + "transit/shipment/generateKyExpress",
					type: "POST",
					data: checkedIds.serialize(),
					success: function(data) {
						if (data.status == 200) {
							if (data.message != undefined && data.message != ''){
								let taskId = data.message;
								$.get(CONTEXT_PATH + "transit/shipment/getKeyPrintUrl?taskId=" + taskId, function (res){
									if (res.status == 200 && res.message != undefined && res.message != ''){
										debugger
										LODOP = getLodop();
										getPrinter();
										// LODOP.SET_PRINT_PAGESIZE(0, '100mm', '120mm', 'Note'); // 设置纸张大小
										LODOP.ADD_PRINT_PDF('1mm', '0mm', '100%', '100%', res.message);
										// LODOP.ADD_PRINT_URL(0, 0, "100%", "100%", res.message);
										LODOP.PRINT();
										// LODOP.PREVIEW();
									} else {
										if (res.message != undefined && res.message != ''){
											customizeLayer(res.message);
										}
									}
									setTimeout(function() {
										window.location.reload();
									}, 1500);
								});
							}
						} else {
							if (data.message != undefined && data.message != ''){
								customizeLayer(data.message);
							}
						}
					},
					beforeSend : function() {
						App.blockUI();
					},
					complete:function () {
						App.unblockUI();
					}
				});
			}

			// 跨越打印重试
			function printKyeRetry(expressNo) {
				if (expressNo == undefined || expressNo == ''){
					layer.alert("物流单号为空", 'error');
					return;
				}

				$.ajax({
					url: CONTEXT_PATH + "transit/shipment/genKyePdfRetry",
					type: "POST",
					data: {expressNo: expressNo},
					success: function(res) {
						if (res.status == 200 && res.message != undefined && res.message != ''){
							debugger
							LODOP = getLodop();
							getPrinter();
							// LODOP.SET_PRINT_PAGESIZE(0, '100%', '100%', 'Note'); // 设置纸张大小
							LODOP.ADD_PRINT_PDF('1mm', '0mm', '100%', '100%', res.message);
							// LODOP.ADD_PRINT_URL(0, 0, "100%", "100%", res.message);
							LODOP.PRINT();
							// LODOP.PREVIEW();
						} else {
							if (res.message != undefined && res.message != ''){
								customizeLayer(res.message);
							}
						}
					},
					beforeSend : function() {
						App.blockUI();
					},
					complete:function () {
						App.unblockUI();
					}
				});
			}

			//选择打印机后存储到cookie
			function changePrinter() {
				var Days = 300;
				var exp = new Date();
				//设置cookie过期时间
				exp.setTime(exp.getTime() + Days * 24 * 60 * 60 * 1000);
				document.cookie = "printn=" + $("#printer").val() + ";expires=" + exp.toGMTString();
			}

		</script>
	</body>

</html>