<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">
<html>
<head>
	<meta content="text/html; charset=utf-8" http-equiv="Content-type">
	<title>Print</title>
	<style>
		@media print {
			.printbtn {
				display: none;
			}
		}
	</style>
</head>

<body style="padding: 0px; margin: 20px">

	<div class="printbtn">
		<button onclick="myPreview();">打印预览</button>
		&nbsp;
		<button onclick="myPagePrint2(1);">打印</button>
		<input type="hidden" id="print-size" value="1">
		&nbsp;
		<button onclick="myPrintDesign();">打印设计</button>
		&nbsp;
	</div>
	<h5>
		选择打印机&nbsp;&nbsp;
		<select id="printer"></select>
	</h5>

<form id="print_content">
    <#if (domain.boxNo)??>
	<div id="print-item-0">
		<!-- 70*30mm -->
		<div style="width:69mm; height:29mm;font-weight: 300; text-align: center;margin-top: 0mm;">
			<div style="width: 68mm;height: 1mm;"></div>
			<div style="width: 68mm;height:8mm;text-align:center;">
				<img src="${CONTEXT_PATH}servlet/barcode?keycode=${domain.fbaNo}&width=180&height=30">
			</div>
			<div style="width: 68mm;height:8mm;text-align:center;font-size: 18px;">
				${domain.fbaNo}
			</div>
			<div style="width: 68mm;height: 12mm;display: flex;justify-content:center;margin-top:-1mm;">
				<div style="width:20mm;height:11mm;border:1px solid black;float:left;">
					<p style="font-size:18px;font-weight:400;margin:3mm;">FBA</p>
				</div>
				<div style="width:30mm;height:11mm;text-align: center;border:1px solid black;border-left:0;float:left;">
					<p style="font-size:14px;margin:0mm;">
						<span style="font-family:'Arial Normal', 'Arial', sans-serif;font-weight:400;">箱号/箱数</span>
					</p>
					<p style="font-size:24px;margin:-1mm;">
						<span style="font-family:'Arial Negreta', 'Arial Normal', 'Arial', sans-serif;font-weight:600;">${domain.boxNo}/${domain.boxNum}</span>
					</p>
				</div>
			</div>
		</div>
		<!-- 单排 -->
		<p style="page-break-after: always; margin: 0; padding: 0; line-height: 1px; font-size: 1px;clear: both;">&nbsp;</p>
	</div>

</div>
    <#else >
        <h4 style="color: red;"><strong>参数错误！</strong></h4>
    </#if>
</form>

<object width="0" height="0" classid="clsid:2105C259-1E0C-4534-8141-A753534CB4CA" id="LODOP_OB">
	<embed width="0" height="0" type="application/x-print-lodop" id="LODOP_EM">
</object>

<!-- 打印插件 -->
<script type="text/javascript" src="${CONTEXT_PATH}js/LodopFuncs.js"></script>
<script src="${CONTEXT_PATH }js/assets/plugins/jquery-1.10.2.min.js" type="text/javascript" ></script>
<script src="${CONTEXT_PATH }js/print.js" type="text/javascript" ></script>
<script language="javascript">
    pageLength = "70mm";//纸张长
    pageWidth = "30mm";//纸张宽
    window.onload = function () {
        printerList();
    };
</script>
</body>
</html>