<!DOCTYPE html>
<html lang="en" class="no-js">
<head>
<meta charset="utf-8" />
<title>ERP</title>

<style type="text/css">
</style>

</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <div class="col-md-12">
                <form class="form-horizontal" id="submit-form" name="submitForm" action="${CONTEXT_PATH}picking/mergeApv/createPickingTask" method="post">
                    <div class="form-body">
                        <div class="form-group">
                            <div class="col-md-3">
                                <input type="radio" checked="checked" name="exportType" value="1">
                                <label class="control-label col-md-1">所有已分配订单</label>
                                <input type="radio" name="exportType" value="3" style="margin-left: 50px;">
                                <label class="radio-inline">当前选择</label>
                                <br/>
                                <br/>
                                <input type="radio" checked="checked" name="splitRegionFlag" value=true>
                                <label class="control-label col-md-1">分楼层生成合单</label>
                                <input type="radio" name="splitRegionFlag" value=false style="margin-left: 50px;">
                                <label class="radio-inline">不分楼层</label>
                                <br/>
                                <br/>
                                <input type="radio" checked="checked" name="splitSaleChannelFlag" value=true>
                                <label class="control-label col-md-1">分平台生成合单</label>
                                <input type="radio" name="splitSaleChannelFlag" value=false style="margin-left: 50px;">
                                <label class="radio-inline">不分平台</label>
                                <#if domain.locationCount??>
                                <br/>
                                <h4><i class="icon-lock"></i>单品任务货位数&nbsp;&nbsp;<input type="text"  name="locationCount" id="locationCount" value="${domain.locationCount}" style="width: 148px;"></h4>
                            </#if>
                            </div>
                        </div>
                </form>
            </div>
        </div>
    </div>
	<script type="text/javascript">
        var locationCount = document.getElementById("locationCount");
        var exportType = document.getElementsByName('exportType');
        var splitRegionFlag = document.getElementsByName('splitRegionFlag');
        var splitSaleChannelFlag = document.getElementsByName('splitSaleChannelFlag');


        // 绑定事件
        if(locationCount){
            locationCount.addEventListener('input', function() {
                postMessage();
            });
        }

        for (var i = 0; i < exportType.length; i++) {
            exportType[i].addEventListener('change', function() {
                postMessage();
            });
        }

        for (var i = 0; i < splitRegionFlag.length; i++) {
            splitRegionFlag[i].addEventListener('change', function() {
                postMessage();
            });
        }

        for (var i = 0; i < splitSaleChannelFlag.length; i++) {
            splitSaleChannelFlag[i].addEventListener('change', function() {
                postMessage();
            });
        }

        // 向上层父窗口传递数据
        function postMessage() {
            var locationCountVal = null;
            if (locationCount){
                locationCountVal = locationCount.value;
            }
            var exportTypeVal = '';
            for (var i = 0; i < exportType.length; i++) {
                if (exportType[i].checked) {
                    exportTypeVal = exportType[i].value;
                    break;
                }
            }
            var splitRegionFlagVal = '';
            for (var i = 0; i < splitRegionFlag.length; i++) {
                if (splitRegionFlag[i].checked) {
                    splitRegionFlagVal = splitRegionFlag[i].value;
                    break;
                }
            }
            var splitSaleChannelFlagVal = '';
            for (var i = 0; i < splitSaleChannelFlag.length; i++) {
                if (splitSaleChannelFlag[i].checked) {
                    splitSaleChannelFlagVal = splitSaleChannelFlag[i].value;
                    break;
                }
            }
            var taskInfo = {
                locationCount: locationCountVal,
                exportType: exportTypeVal,
                splitRegionFlag: splitRegionFlagVal,
                splitSaleChannelFlag: splitSaleChannelFlagVal
            };
            window.parent.postMessage(taskInfo, '*');
        }
	</script>

</body>
</html>