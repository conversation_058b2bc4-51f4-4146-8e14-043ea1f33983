<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">
<html>
<head>
<title>易世通达仓库管理系统</title>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
	<#include "/common/include.html">
	<link href="${CONTEXT_PATH}js/assets/plugins/jquery-file-upload/css/jquery.fileupload-ui.css" rel="stylesheet" type="text/css"/>
	<style type="text/css">
.modal-body input {
	width: 240px;
}
.top_bar{
    position:fixed;top:0px;
}
#task-list td {
	vertical-align:middle;
}
</style>
</head>
<body>
	<@header method="header" active="11020000"><#include "/ftl/header.ftl"></@header>

	<div id="page" style="background-color: rgb(231, 237, 248)">
		<div class="row">
			<div class="col-md-12" style="padding: 0">
				<ul class="page-breadcrumb breadcrumb" style="background-color: white;">
					<li><a href="#">仓库管理</a></li>
					<li class="active">仓库盘点</li>
				</ul>
			</div>
		</div>

		<!-- 内容 -->
		<div class="container-fluid" style="background-color: white;border: none">
		<!-- END PAGE HEADER-->
		<div class="row">
			<div class="col-md-12">
				<form action="${CONTEXT_PATH}warehouse/inventorys/search"
					class="form-horizontal form-bordered form-row-stripped"
					method="post" modelAttribute="domain" name="checkInForm" id="domain">
					<!-- 分页信息 -->
					<input id="page-no" type="hidden" name="page.pageNo" value="1">
					<input id="page-size" type="hidden" name="page.pageSize" value="${domain.page.pageSize}">
					<div class="form-body">
						<div class="form-group">
							<label class="control-label col-md-1">盘点类型</label>
							<div class="col-md-3">
								<select name="query.type" class="form-control">
									<option value="">所有</option>
									<option value="1" <#if domain.query.type == 1>selected</#if>>普通盘点</option>
									<option value="2" <#if domain.query.type == 2>selected</#if>>FBA盘点</option>
									<option value="3" <#if domain.query.type == 3>selected</#if>>导入盘点</option>
									<option value="4" <#if domain.query.type == 4>selected</#if>>库存调拨盘点</option>
									<option value="5" <#if domain.query.type == 5>selected</#if>>订单调拨盘点</option>
								</select>
							</div>
							<label class="control-label col-md-1">状态</label>
							<div class="col-md-3">
								<select name="query.status" class="form-control">
									<option value="">所有</option>
									<option value="1" <#if domain.query.status == 1>selected</#if>>未调整</option>
									<option value="3" <#if domain.query.status == 3>selected</#if>>部分调整</option>
									<option value="5" <#if domain.query.status == 5>selected</#if>>已调整</option>
									<option value="7" <#if domain.query.status == 7>selected</#if>>已废弃</option>
								</select>
							</div>
							<label class="control-label col-md-1">盘点时间</label>
							<div class="col-md-3">
								<div class="input-group">
									<input class="form-control Wdate" type="text" id="startTime" name="query.startTime" placeholder="" readonly="readonly" value="${domain.query.startTime}" onfocus="WdatePicker({startDate:'%y-%M-%d 00:00:00',dateFmt:'yyyy-MM-dd HH:mm:ss',alwaysUseStartDate:true})">
									<span class="input-group-addon">到</span>
									<input class="form-control Wdate" type="text" id="endTime"  name="query.endTime" placeholder="" readonly="readonly" value="${domain.query.endTime}" onfocus="WdatePicker({startDate:'%y-%M-%d 23:59:59',dateFmt:'yyyy-MM-dd HH:mm:ss',alwaysUseStartDate:true})">
								</div>
							</div>
						</div>
						<div class="form-group">
							<label class="control-label col-md-1">SKU</label>
	                        <div class="col-md-3">
	                            <input class="form-control" type="text" name="query.sku" placeholder="请输入SKU" value="${domain.query.sku}"/>
	                        </div>
						</div>
					</div>
					<div>
						<div class="pull-left" style="margin-bottom: 10px;">
								<!-- 注释说明：盘点功能合并需要 -->
<!--							<span class="btn btn-default fileinput-button">-->
<!--								<span class="icon-plus"> 导入手动加减库存盘点单</span>-->
<!--								<input type="file" name="file" onchange="fileinputSubmit(this)" />-->
<!--							</span>-->
							<!--<a class="btn btn-default" href="${CONTEXT_PATH}file/execl/inventory_import.xlsx">
								<i class="icon-download"></i> 下载范例Excel
							</a>-->
							<#--<a class="btn btn-default blue" href="${CONTEXT_PATH}warehouse/inventorys/toAddAllocation">-->
								<#--<i class="icon-plus"></i>调拨盘点-->
							<#--</a>-->
						</div>
						<div class="col-md-offset-10" style="text-align: right">
							<button type="button" class="btn btn-default" onclick="formReset(this)">
								<i class="icon-refresh"></i> 重置
							</button>
							<button type="submit" class="btn blue">
								<i class="icon-search"></i> 查询
							</button>
                            <@header method="auth" authCode="CHECKE_INVENTORY_DOWNLOAD">
							<button type="button" class="btn btn-default" onclick="downloadInventorys()">
								<i class="icon-download"></i> 导出
							</button>
                            </@header>
						</div>
					</div>
				</form>
			</div>
			<br/>
		</div>
		
		<div class="row">
			<div id="fixedDiv" class="col-md-12">
				<table class="table table-striped table-bordered table-hover table-condensed" id="fixedTab">
					<colgroup>
						<col width="5%" />
						<col width="5%" />
						<col width="15%" />
						<col width="15%" />
						<col width="20%" />
						<col width="30%" />
						<col width="10%" />
						<col width="10%" />
					</colgroup>
					<thead>
					<tr>
						<th>
							<label class="checkbox-inline">
								全选/反选
							</label>
						</th>
						<th>编号</th>
						<th>盘点时间</th>
						<th>盘点类型</th>
						<th>盘点人</th>
						<th>备注</th>
						<th>盘点明细</th>
					</tr>
					</thead>
				</table>
			</div>
			<div class="col-md-12" id="task-list-warp">
				<table class="table table-striped table-bordered table-hover table-condensed" id="task-list">
					<colgroup>
						<col width="5%" />
						<col width="5%" />
						<col width="15%" />
						<col width="15%" />
						<col width="10%" />
						<col width="20%" />
						<col width="30%" />
						<col width="10%" />
						<col width="10%" />
					</colgroup>
					<thead>
						<tr>
							<th>
								<label class="checkbox-inline">
                                   	<input type="checkbox" name="checkAll"> 全选/反选
                                   </label>
							</th>
							<th>编号</th>
							<th>盘点时间</th>
							<th>盘点类型</th>
							<th>盘点状态</th>
							<th>盘点人</th>
							<th>备注</th>
							<th>盘点明细</th>
						</tr>
					</thead>
					<tbody>
						<#list domain.whInventorys as whInventory>
							<tr>
								<td>
									<label class="checkbox-inline"> 
									<input class="${whInventory.id}" name="query.ids" type="checkbox" value="${whInventory.id}">
									</label>
								</td>
								<td>${whInventory.id}</td>
								<td>${whInventory.creationDate}</td>
								<td>${util('enumName', 'com.estone.warehouse.enums.InventoryType', whInventory.type)}</td>
								<td>${whInventory.queryStatus}</td>
								<td>${util('name',whInventory.createdBy)}</td>
								<td>${whInventory.remark}</td>
								<td><a href="${CONTEXT_PATH}warehouse/inventorys/${(whInventory.type == 3)?string('uploadDetail','detail')}?whInventory.id=${whInventory.id}&whInventory.type=${whInventory.type}">明细</a></td>
							</tr>
						</#list>
					</tbody>
				</table>
			</div>
		</div>
		
		<div class="modal fade ui-popup" id="exportModal" tabindex="-1" role="dialog" aria-labelledby="exportLabel" aria-hidden="true">
		    <div class="modal-dialog">
		        <div class="modal-content">
		            <div class="modal-header">
		                <button type="button" class="close" data-dismiss="modal" aria-hidden="true"></button>
		                <h4 class="modal-title" id="myModalLabel">导出库存盘点</h4>
		            </div>
		            <div class="modal-body">
		            	<div class="form-group">
		            		<label class="radio-inline">
								<input type="radio" style="width: 25px;" name="exportType" value="1">所有
							</label>
							<label class="radio-inline">
								<input type="radio" style="width: 25px;" name="exportType" value="2">当前页
							</label>
							<label class="radio-inline">
								<input type="radio" style="width: 25px;" name="exportType" value="3">当前选择
							</label>
						</div>
		            </div>
		            <div class="modal-footer">
		                <button type="button" class="btn btn-default" data-dismiss="modal">关闭</button>
		                <button type="button" class="btn btn-primary" onclick="exportInventorys()">导出</button>
		            </div>
		        </div>
		    </div>
		</div>
		
		<div id="fixed-bottom">
			<div id="pager"></div>
		</div>
	</div>
	<#include "/common/footer.html">
	</div>
	<script type="text/javascript" src="${CONTEXT_PATH}js/datepicker/WdatePicker.js"></script>
	<script type="text/javascript" src="${CONTEXT_PATH}js/jquery.form.js"></script>
	<script type="text/javascript" src="${CONTEXT_PATH}js/table-thead-fixed.js"></script>
	<script type="text/javascript">
		// 全选
		var checkAll = $("#task-list").find("input[name='checkAll']");
		// 子选项
		var inventoryIds = $("#task-list").find("input[name='query.ids']");
		
		checkAll.change(
		  function () {
			  inventoryIds.prop("checked", $(this).prop("checked"));
			  
			  inventoryIds.each(function(){
				  var f = $(this).is(":checked");
					var checkClass = $(this).prop("class");
					$("." + checkClass).each(function(){
						$(this).prop("checked",f);
					})
			  })
		  }
		);
		
		inventoryIds.change(function(){
			var checkedLenght = inventoryIds.filter(":checked").length;
			var length = inventoryIds.length;
			checkAll.prop("checked", checkedLenght == length);
		});
		
		
		// 分页
		var total = "${domain.page.totalCount}";
		var pageNo = "${domain.page.pageNo}";
		var pageSize = "${domain.page.pageSize}";
		$("#pager").initPager({ total : total, pageNo : pageNo, pageSize : pageSize});

        var heights = $("body").height();
        if(heights>910){
            $("#fixed-bottom").addClass("fixed-bottom-height")
        }
        
        function downloadInventorys() {
        	$("#exportModal").modal({
                keyboard: true
            });
        }
        
        function exportInventorys() {
        	var exportType = $("input[name='exportType']:checked").val();
        	if (null == exportType) {
        		layer.alert("请选择导出条件", "error");
        		return false;
        	}
        	
        	//还原分页
	    	$("#page-no").val("${domain.page.pageNo}");
        	var domain = $('#domain').serialize();
        	$("#page-no").val("1");
        	var params = domain + "&exportType=" + exportType;
        	
        	var inventoryIds = $("#task-list").find("input[name='query.ids']:checked");
        	// 导出当前选择
	    	if(exportType == 3) {
	        	if (inventoryIds.length <= 0) {
	        		layer.alert("请勾选需要导出的数据!");
	        		return false;
	        	} else if (inventoryIds.length > 300) {
	        		layer.alert("勾选数据不能超出300条!");
	        		return false;
	        	}
	        	inventoryIds = inventoryIds.serialize();
	        	params = inventoryIds + "&exportType=" + exportType;
	    	}
        	postExcelFile(params, CONTEXT_PATH + "warehouse/inventorys/download?");
        	$("#exportModal").modal("hide");
        }
        
		function postExcelFile(params, url) {
			var form = document.createElement("form");
			form.style.display = 'none';
			form.action = url + params;
			form.method = "post";
			document.body.appendChild(form);
			form.submit();
			form.remove();
		}
        
        /**
    	 * 限制文件类型
    	 * @param target
    	 */
    	function fileinputSubmit(target) {
    		
    		//检测上传文件的类型 
    		var filename = target.value;

    		var ext, idx;
    		if (filename == '') {
    			$("#submit-upload").attr("disabled", true);
    			layer.alert("请选择需要上传的文件!");
    			return;
    		} else {
    			idx = filename.lastIndexOf(".");
    			if (idx != -1) {
    				ext = filename.substr(idx + 1).toUpperCase();
    				ext = ext.toLowerCase();

    				if (ext != 'xls' && ext != 'xlsx') {
    					layer.alert("只能上传.Excel类型的文件!");
    					return;
    				}
    			} else {
    				layer.alert("只能上传.Excel类型的文件!");
    				return;
    			}
    		}
    		
    		var r = confirm("确定上传" + filename + "?");
    		
    		if(!r) {
    			return;
    		}
    		
    		var uploadUrl = CONTEXT_PATH + "warehouse/inventorys/upload";
    		
    		var searchUrl = $("#domain").attr("action");
    		
    		$("#domain").attr("action", uploadUrl);
    		
    		$("#domain").ajaxSubmit(function(data) {
    			if (data.status == 200) {
					layer.alert("成功！");
    				setTimeout(function() {
            			window.location.reload();
        			}, 1000);
    			} else {
    				customizeLayer(data.message, "error");
    			}
    			
    			$("#domain").attr("action", searchUrl);
    		});
    		
    		$("#domain").attr("action", searchUrl);
    	}
	</script>
</body>
</html>