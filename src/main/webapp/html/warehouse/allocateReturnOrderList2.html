<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">
<html>
<head>
    <title>易世通达仓库管理系统</title>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <#include "/common/include.html">
        <link href="${CONTEXT_PATH}js/assets/plugins/jquery-file-upload/css/jquery.fileupload-ui.css" rel="stylesheet" type="text/css"/>
        <style type="text/css">
            .modal-body input {
                width: 240px;
            }
            .control-label {
                margin-top: 2px;
            }
            .form-bordered .control-label {
                padding-top: 14px;
            }
            .form-horizontal .control-label {
                text-align: right;
            }
            .col-md-1 {
                padding-left: 10px;
                padding-right: 0px;
                width: 5%;
                font-size: 12px;
            }
            .col-md-2 {
                padding-left: 10px;
                padding-right: 0px;
                width: 7.5%;
                font-size: 12px;
            }
            .form-control {
                height: 30px;
                padding: 0px 4px;
                border: 1px solid #ddd;
                color: #000;
                font-size: 10px;
                font-weight: normal;
            }
            .form-bordered .form-group > div {
                padding: 4px 10px 0;
            }
            /*启用table滚动条*/
            .my-div-table{
                overflow-x: auto;
                overflow-y: auto;
                height: 654px;
                width: auto;
                padding-bottom: 52px;
                /*width:1920px;*/
            }
            /*禁用body滚动条*/
            body{
                overflow-x: hidden;
                overflow-y: hidden;
            }
            #fixedTab tr th{
                font-size: 12px;
            }
            #task-list tbody tr td{
                vertical-align: middle;
                text-align: center;
            }
        </style>
        </head>
<body>
<@header method="header" active="11020000"><#include "/ftl/header.ftl"></@header>

<div id="page" style="background-color: rgb(231, 237, 248)">
    <div class="row">
        <div class="col-md-12" style="padding: 0">
            <ul class="page-breadcrumb breadcrumb" style="background-color: white;">
                <li><a href="#">仓库管理</a></li>
                <li class="active">调拨返架列表</li>
            </ul>
        </div>
    </div>

    <!-- 内容 -->
    <div class="container-fluid" style="background-color: white;border: none">
        <div class="row">
            <div class="col-md-12">
                <#assign query = domain.query>
                    <form action="${CONTEXT_PATH}warehouse/allocateReturnOrder2/search" class="form-horizontal form-bordered form-row-stripped"
                          method="post" modelAttribute="domain" name="allocateReturnOrderForm" id="domain">
                        <!-- 分页信息 -->
                        <input id="page-no" type="hidden" style="display:none" name="page.pageNo" value="1">
                        <input id="page-size" type="hidden" name="page.pageSize" value="${domain.page.pageSize}">

                        <div class="form-body">
                            <div class="form-group">

                                <label class="control-label col-md-1">返架列表状态：</label>
                                <div class="col-md-2">
                                    <input class="form-control" name="query.status" type="text" value="${query.status}">
                                </div>

                                <label class="control-label col-md-1">SKU返架状态：</label>
                                <div class="col-md-2">
                                    <input class="form-control" name="query.returnStatus" type="text" value="${query.returnStatus}">
                                </div>

                                <label class="control-label col-md-1">匹配差异：</label>
                                <div class="col-md-2">
                                    <select name="query.mateDiff" class="form-control">
                                        <option vlaue=""></option>
                                        <#list domain.selects as status>
                                            <#if query.mateDiff?? && query.mateDiff?string ("true","false") == status[0]>
                                                <option selected="selected" value="${status[0]}">${status[1]}
                                                </option>
                                                <#else>
                                                    <option value="${status[0]}">${status[1]}</option>
                                            </#if>
                                        </#list>
                                    </select>
                                </div>

                                <label class="control-label col-md-1">总返架差异：</label>
                                <div class="col-md-2">
                                    <select name="query.returnDiff" class="form-control">
                                        <option vlaue=""></option>
                                        <#list domain.selects as status>
                                            <#if query.returnDiff?? && query.returnDiff?string ("true","false") == status[0]>
                                                <option selected="selected" value="${status[0]}">${status[1]}
                                                </option>
                                                <#else>
                                                    <option value="${status[0]}">${status[1]}</option>
                                            </#if>
                                        </#list>
                                    </select>
                                </div>

                                <label class="control-label col-md-1">扫描差异：</label>
                                <div class="col-md-2">
                                    <select name="query.scanDiff" class="form-control">
                                        <option vlaue=""></option>
                                        <#list domain.selects as status>
                                            <#if query.scanDiff?? && query.scanDiff?string ("true","false") == status[0]>
                                                <option selected="selected" value="${status[0]}">${status[1]}
                                                </option>
                                                <#else>
                                                    <option value="${status[0]}">${status[1]}</option>
                                            </#if>
                                        </#list>
                                    </select>
                                </div>

                                <label class="control-label col-md-1">返架列表号：</label>
                                <div class="col-md-2">
                                    <input class="form-control" type="text" name="query.orderNo" placeholder="多个查询逗号间隔" value="${query.orderNo}">
                                </div>

                                <label class="control-label col-md-1">调拨返架单号：</label>
                                <div class="col-md-2">
                                    <input class="form-control" type="text" name="query.returnNo" placeholder="多个查询逗号间隔" value="${query.returnNo}">
                                </div>

                                <label class="control-label col-md-1">SKU：</label>
                                <div class="col-md-2">
                                    <input class="form-control" type="text" name="query.sku" placeholder="多个查询逗号分开" value="${query.sku }">
                                </div>
                            </div>

                            <div class="form-group">
                                <label class="control-label col-md-1">签收时间：</label>
                                <div class="col-md-2">
                                    <input class="form-control Wdate" type="text" name="query.fromReceiveDate" placeholder="" readonly="readonly" value="${query.fromReceiveDate }"
                                           onfocus="WdatePicker({startDate:'%y-%M-%d 00:00:00',dateFmt:'yyyy-MM-dd HH:mm:ss',alwaysUseStartDate:true})">
                                </div>

                                <label class="control-label col-md-1">到</label>
                                <div class="col-md-2">
                                    <input class="form-control Wdate" type="text" name="query.toReceiveDate" placeholder="" readonly="readonly" value="${query.toReceiveDate }"
                                           onfocus="WdatePicker({startDate:'%y-%M-%d 23:59:59',dateFmt:'yyyy-MM-dd HH:mm:ss',alwaysUseStartDate:true})">
                                </div>
                            </div>

                        </div>
                        <div>
                            <div class="pull-left">
                                <div class="btn-group">
                                    <span class="btn btn-default fileinput-button">
									<span class="icon-upload"> 导入</span>
									<input type="file" name="file" onchange="fileinputSubmit(this)" />
								    </span>
                                    <a class="btn btn-default" href="${CONTEXT_PATH}file/execl/allocate_return_import.xlsx">
                                        <i class="icon-download"></i> 下载范例Excel
                                    </a>
                                </div>
                            </div>
                            <div class="col-md-offset-12" style="text-align: right">
                                <button type="button" class="btn btn-default" onclick="downloadCheck()">
                                    <i class="icon-download"></i> 导出明细
                                </button>
                                <button type="button" onclick="formReset(this)" class="btn btn-default">
                                    <i class="icon-refresh"></i> 重置
                                </button>
                                <button type="submit" class="btn blue">
                                    <i class="icon-search"></i> 查询
                                </button>
                            </div>
                        </div>
                    </form>
            </div>
        </div>
        <br/>
    </div>

    <div class="row">
        <div id="myFixedDiv" class="col-md-12">
            <table class="table table-striped table-bordered table-hover table-condensed" id="fixedTab">
                <colgroup>
                    <col width="4%"/>
                    <col width="7%"/>
                    <col width="4%"/>
                    <col width="7%"/>
                    <col width="7%"/>

                    <col width="7%"/>
                    <col width="7%"/>
                    <col width="7%"/>
                    <col width="4%"/>
                    <col width="4%"/>
                    <col width="4%"/>
                    <col width="4%"/>
                    <col width="4%"/>

                    <col width="7%"/>
                    <col width="4%"/>
                    <col width="4%"/>
                    <col width="4%"/>
                    <col width="4%"/>
                    <col width="7%"/>
                </colgroup>
                <thead>
                <tr>
                    <th><input type="checkbox" id="check-all" name="checkAll">编号</th>
                    <th>调拨返架<br/>列表号</th>
                    <th>状态</th>
                    <th>签收人/日期</th>
                    <th>操作</th>

                    <th>SKU</th>
                    <th>SKU名称</th>
                    <th>库位</th>
                    <th>总件数</th>
                    <th>匹配件数</th>
                    <th>匹配差异</th>
                    <th>新仓扫<br/>描件数</th>
                    <th>扫描差异</th>

                    <th>返架单号</th>
                    <th>返架单<br/>件数</th>
                    <th>返架单<br/>上架件数</th>
                    <th>上架差异</th>
                    <th>返架单<br/>sku状态</th>
                    <th>sku最后修改日期</th>
                </tr>
                </thead>
            </table>
        </div>
        <div class="col-md-12 my-div-table" id="task-list-warp">
            <!-- 内容 -->
            <table class="table table-striped table-bordered table-hover table-condensed" id="task-list">
                <colgroup>
                    <col width="4%"/>
                    <col width="7%"/>
                    <col width="4%"/>
                    <col width="7%"/>
                    <col width="7%"/>

                    <col width="7%"/>
                    <col width="7%"/>
                    <col width="7%"/>
                    <col width="4%"/>
                    <col width="4%"/>
                    <col width="4%"/>
                    <col width="4%"/>
                    <col width="4%"/>

                    <col width="7%"/>
                    <col width="4%"/>
                    <col width="4%"/>
                    <col width="4%"/>
                    <col width="4%"/>
                    <col width="7%"/>
                </colgroup>
                <!--<thead>
                <tr>
                    <th><input type="checkbox" id="check-all" name="checkAll">编号</th>
                    <th>调拨返架列表号</th>
                    <th>状态</th>
                    <th>签收人/日期</th>
                    <th>操作</th>

                    <th>SKU</th>
                    <th>SKU名称</th>
                    <th>库位</th>
                    <th>总件数</th>
                    <th>匹配件数</th>
                    <th>匹配差异</th>
                    <th>新仓扫描件数</th>
                    <th>扫描差异</th>

                    <th>返架单号</th>
                    <th>返架单件数</th>
                    <th>返架单上架件数</th>
                    <th>上架差异</th>
                    <th>返架单sku状态</th>
                    <th>sku最后修改日期</th>
                </tr>
                </thead>-->
                <tbody>
                <#list domain.allocateReturnOrders as order>
                    <tr>
                        <td rowspan="${order.rowspan}">
                            <input type="checkbox" value="${order.id}" name="ids">
                            ${order.id}
                        </td>
                        <td rowspan="${order.rowspan}">${order.orderNo }</td>
                        <td rowspan="${order.rowspan}" id="${order.id}_status">${order.statusName }</td>
                        <td rowspan="${order.rowspan}">
                            ${util('name',order.receiveBy)}<br/>
                            ${order.receiveDate }
                        </td>
                        <td rowspan="${order.rowspan}">
                            <#if order.status == 5 || order.status == 7>
                                <a class="btn btn-xs btn-default" target="_blank" href="${CONTEXT_PATH}warehouse/allocateReturnOrder2/createReturn?id=${order.id }">
                                    <i class="icon-plus"></i>返架
                                </a>
                            </#if>
                            <button type="button" class="btn btn-info btn-xs"
                                    onclick="viewLog(${order.id}, 'allocateReturnOrder')">
                                日志
                            </button>
                        </td>
                        <#list order.allocateReturnOrderItems as orderItem>
                            <#if orderItem_index != 0></tr><tr></#if>
                            <td rowspan="${orderItem.rowspan}">${orderItem.sku }</td>
                            <td rowspan="${orderItem.rowspan}">${orderItem.whSku.name }</td>
                            <td rowspan="${orderItem.rowspan}">${orderItem.whSku.locationNumber }</td>
                            <td rowspan="${orderItem.rowspan}">${orderItem.quantity }</td>
                            <td rowspan="${orderItem.rowspan}">${orderItem.mateQuantity }</td>
                            <td rowspan="${orderItem.rowspan}">${orderItem.mateQuantity - orderItem.quantity }</td>
                            <td rowspan="${orderItem.rowspan}">${orderItem.scanCount }</td>
                            <td rowspan="${orderItem.rowspan}">
                                <#if orderItem.scanCount??>
                                    ${orderItem.scanCount - orderItem.quantity}
                                </#if>
                            </td>
                            <#if orderItem.whReturnItems?? && orderItem.whReturnItems?size!=0 >
                                <#list orderItem.whReturnItems as returnItem>
                                    <#if returnItem_index != 0></tr><tr></#if>
                                    <td>${returnItem.returnNo }</td>
                                    <td>${returnItem.quantity }</td>
                                    <td>${returnItem.completeQuantity }</td>
                                    <td>${returnItem.completeQuantity - returnItem.quantity }</td>
                                    <td>${returnItem.statusName }</td>
                                    <td>${returnItem.lastUpdateDate }</td>
                                </#list>
                                <#else>
                                    <td></td>
                                    <td></td>
                                    <td></td>
                                    <td></td>
                                    <td></td>
                                    <td></td>
                            </#if>
                        </#list>
                    </tr>
                </#list>
                </tbody>
            </table>
            <!-- 内容end -->
        </div>
    </div>
    <div id="fixed-bottom">
        <div id="pager"></div>
    </div>
</div>


<script type="text/javascript" src="${CONTEXT_PATH}js/datepicker/WdatePicker.js"></script>
<script type="text/javascript" src="${CONTEXT_PATH}js/jquery.form.js"></script>
<script type="text/javascript" src="${CONTEXT_PATH}js/table-thead-fixed.js"></script>
<script type="text/javascript">
    document.getElementById("fixedTab").style.width = $('#task-list').css('width');
    // 分页
    var total = "${domain.page.totalCount}";
    var pageNo = "${domain.page.pageNo}";
    var pageSize = "${domain.page.pageSize}";
    $("#pager").initPager({total: total, pageNo: pageNo, pageSize: pageSize});

    var heights = $("body").height();
    if (heights > 910) {
        $("#fixed-bottom").addClass("fixed-bottom-height")
    }

    var statusArray =  ${domain.statuses};
    $("input[name='query.status']").select2({
        data: statusArray,
        placeholder: "状态",
        //multiple: true,
        allowClear: true
    });

    var returnStatusArray =  ${domain.returnStatus};
    $("input[name='query.returnStatus']").select2({
        data: returnStatusArray,
        placeholder: "SKU返架状态",
        //multiple: true,
        allowClear: true
    });

    // 全选
    var checkAll = $("input[name='checkAll']");

    // 子选项
    var itemIds = $("input[name='ids']");
    checkAll.change(
        function () {
            itemIds.prop("checked", $(this).prop("checked"));
            itemIds.each(function () {
                var f = $(this).is(":checked");
                var checkClass = $(this).prop("class");
                $("." + checkClass).each(function () {
                    $(this).prop("checked", f);
                })
            })
        }
    );

    // 获取选中的入库单
    function getCheckedIds() {
        var checkedIds = $("input[name='ids']:checked");
        return checkedIds;
    }

    /**
     * 限制文件类型
     * @param target
     */
    function fileinputSubmit(target) {

        //检测上传文件的类型
        var filename = target.value;

        var ext, idx;
        if (filename == '') {
            $("#submit-upload").attr("disabled", true);
            layer.alert("请选择需要上传的文件!");
            return;
        } else {
            idx = filename.lastIndexOf(".");
            if (idx != -1) {
                ext = filename.substr(idx + 1).toUpperCase();
                ext = ext.toLowerCase();

                if (ext != 'xls' && ext != 'xlsx') {
                    layer.alert("只能上传.Excel类型的文件!");
                    return;
                }
            } else {
                layer.alert("只能上传.Excel类型的文件!");
                return;
            }
        }

        var r = confirm("确定上传" + filename + "?");

        if(!r) {
            return;
        }

        var uploadUrl = CONTEXT_PATH + "warehouse/allocateReturnOrder2/upload";

        var searchUrl = $("#domain").attr("action");

        $("#domain").attr("action", uploadUrl);

        $("#domain").ajaxSubmit(function(data) {
            if (data.status == 200) {
                alert("成功！");
            } else {
                customizeLayer(data.message, "error");
            }

            $("#domain").attr("action", searchUrl);
        });

        $("#domain").attr("action", searchUrl);

        setTimeout(function () {
            location.href = CONTEXT_PATH + "warehouse/allocateReturnOrder2";
        }, 2000);
    }

    // 下载
    function downloadCheck() {
        var checkedDatas = getCheckedIds();
        if (checkedDatas.length == 0) {
            var param = $("#domain").serialize();
            if (total > 100000) {
                layer.alert("导出数据不能超过100000条！", "error");
                return;
            }
            window.open(CONTEXT_PATH + "warehouse/allocateReturnOrder2/download?" + param);
        } else {
            downloadCheckByPost();
        }
    }


    // 超过500条不能用GET请求
    function downloadCheckByPost() {
        var checkedDatas = getCheckedIds();
        var ids = "";
        for (var i = 0; i < checkedDatas.length; i++) {
            var check = checkedDatas[i];
            var id = $(check).val();
            ids += id;
            if (i != checkedDatas.length - 1) {
                ids += ",";
            }
        }
        var url = CONTEXT_PATH + "warehouse/allocateReturnOrder2/download";
        var tempForm = document.createElement("form");
        tempForm.id = "tempForm";
        tempForm.method = "post";
        tempForm.action = url;
        tempForm.target = "blank";
        var hideInput = document.createElement("input");
        hideInput.type = "hidden";
        hideInput.name = "ids";
        hideInput.value = ids;
        tempForm.appendChild(hideInput);
        if (tempForm.attachEvent) {  // IE
            tempForm.attachEvent("onsubmit", function () {
                window.open('about:blank', 'blank');
            });
        } else if (tempForm.addEventListener) {  // DOM Level 2 standard
            tempForm.addEventListener("onsubmit", function () {
                window.open('about:blank', 'blank');
            });
        }
        document.body.appendChild(tempForm);
        if (document.createEvent) { // DOM Level 2 standard
            evt = document.createEvent("MouseEvents");
            evt.initMouseEvent("submit", true, true, window, 0, 0, 0, 0, 0, false, false, false, false, 0, null);
            tempForm.dispatchEvent(evt);
        } else if (tempForm.fireEvent) { // IE
            tempForm.fireEvent('onsubmit');
        }
        //必须手动的触发
        tempForm.submit();
        document.body.removeChild(tempForm);
    }


</script>
</body>
</html>