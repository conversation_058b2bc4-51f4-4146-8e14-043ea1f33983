<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">
<html>
<head>
<title>易世通达仓库管理系统</title>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
	<#include "/common/include.html">
	<style type="text/css">
.modal-body input {
	width: 240px;
}
.top_bar{
    position:fixed;top:0px;
}
#task-list td {
	vertical-align:middle;
}
</style>
</head>
<body>
	<@header method="header" active="11020000"><#include "/ftl/header.ftl"></@header>
	<div id="page" style="background-color: rgb(231, 237, 248)">
		<div class="row">
			<div class="col-md-12">
				<ul class="page-breadcrumb breadcrumb">
					<li><a href="#">仓库</a> <i class="icon-angle-right"></i></li>
					<li><a href="#">盘点明细</a></li>
				</ul>
			</div>
		</div>
			<!-- 注释说明：盘点功能合并需要  -->
		<div class="row">
			<div class="col-md-12">	
			
					<table class="table table-striped table-bordered table-hover table-condensed">
						<colgroup>
							<col width="10%" />
							<col width="20%" />
							<col width="20%" />
							<col width="10%" />
							<col width="10%" />
							<col width="10%" />
							<col width="10%" />
							<col width="5%" />
							<col width="10%" />
						</colgroup>
						<thead>
							<tr>
								<th><label class="checkbox-inline"><input type="checkbox" onclick="checkAll(this);"> 编    号</label></th>
								<th>SKU</th>
								<th>名称</th>
								<th>库位</th>
								<th>库存数量</th>
								<th>盘点数量</th>
								<th>差异数量</th>
								<th>状态</th>
								<th>操作</th>
							</tr>
						</thead>
						<tbody>
							<#list domain.whInventoryItems as whInventoryItem>
								<tr>
									<td>
										<label class="checkbox-inline">
										<#if whInventoryItem.status != 10 && whInventoryItem.status != 9 && whInventoryItem.differencesNum != 0>
											<input type="checkbox" name="ids" value="${whInventoryItem.id}" />
										</#if>
											${whInventoryItem.id}
										</label>
									</td>
									<td>${whInventoryItem.sku}</td>
									<td>${whInventoryItem.skuName}</td>
									<td>${whInventoryItem.skuLocation}</td>
									<td>${whInventoryItem.skuNum}</td>
									<td>${whInventoryItem.inventoryNum}</td>
									<td>${whInventoryItem.differencesNum}</td>
									<td>
										<#if whInventoryItem.status != null>
											${util('enumName', 'com.estone.warehouse.enums.InventoryItemStatus', whInventoryItem.status)}
										<#else>
											未调整
										</#if>
									</td>
									<td>
										<#if whInventoryItem.status != 10 && whInventoryItem.status != 9 && whInventoryItem.differencesNum != 0>
											<button type="button" class="btn btn-xs btn-default" onclick="discard(this)" value="${whInventoryItem.id}">废弃</button>
										</#if>
										<button type="button" class="btn btn-xs btn-info" onclick="viewLog(${whInventoryItem.id}, 'inventoryItem')">日志</button>
									</td>
								</tr>
							</#list>
						</tbody>
					</table>
			</div>
		</div>

	</div>
	<#include "/common/footer.html">
	<script type="text/javascript" src="${CONTEXT_PATH}js/datepicker/WdatePicker.js"></script>
	<script type="text/javascript">
		//全选
		function checkAll(obj) {
			$("input[name='ids']").prop("checked", $(obj).is(':checked'));
		}

		function discard($this){
			if (confirm("是否废弃该条明细？")) {
				App.blockUI();
				$.getJSON("${CONTEXT_PATH}warehouse/inventorys/discard", "id=" + $this.value, function(data) {
					App.unblockUI();
					if (data.status == 200) {
						layer.alert("废弃成功！");
						setTimeout(function() {
							location.reload();
						}, 200);
					}
				})
			}
		}
	</script>
</body>
</html>