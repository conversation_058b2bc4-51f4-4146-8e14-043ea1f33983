<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">
<html>
<head>
<title>易世通达仓库管理系统</title>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
	<#include "/common/include.html">
	<style type="text/css">
.modal-body input {
	width: 240px;
}
#header-div{
 	margin-left: 400px;
}
#task-list td {
	vertical-align:middle;
}
</style>
</head>
<body>
	<@header method="header" active="18030000"><#include "/ftl/header.ftl"></@header>
	<div id="page" style="background-color: rgb(231, 237, 248)">
		<div class="row">
			<div class="col-md-12" style="padding: 0">
				<ul class="page-breadcrumb breadcrumb" style="background-color: white;">
					<li><a href="#">库存管理</a></li>
					<li class="active">耗材采购需求管理</li>
					<li class="active">创建耗材采购需求单</li>
				</ul>
			</div>
		</div>
		<!-- 内容 -->
		<div class="container-fluid" style="background-color: white;border: none;">
		<!-- END PAGE HEADER-->
		<div class="row" >
			<div class="col-md-9" id="header-div">

					<div class="form-body" >
						<div class="form-group" style="margin-left: 100px;margin-top: 10px">
							<label class="control-label col-md-1" >耗材型号</label>
							<div class="col-md-3" style="margin-left: -20px">
								<input class="form-control" id="materialArticleNumber"  type="text" value="${query.materialArticleNumber}"  onkeypress="if(event.keyCode==13) { getMaterialStock(this); return false;}" tabindex="4">
							</div>
						</div>
						<div class="form-group" style="margin-left: 550px;margin-top: -45px">
							<div class="col-md-2">
								<div style="border-radius: 8px !important;width: 100%;" type="button" class="btn blue" onclick="getMaterialStock();">
									<i class="icon-plus"></i> 提交
								</div>
							</div>
						</div>
						<div class="form-group" style="margin-top: 120px;">
							<div class="col-md-7">
								<table class="table table-striped table-bordered table-hover table-condensed" id="task-list">
									<thead>
										<tr>
											<th>序号</th>
											<th>耗材型号</th>
											<th>耗材名称</th>
											<th>单位</th>
											<th>箱规</th>
											<th>库存</th>
											<th>需采数量</th>
											<th>备注</th>
											<th>操作</th>
										</tr>
									</thead>
									<tbody id="tbody">
									</tbody>
								</table>
							</div>
						</div>
					</div>

				<div class="col-md-9" style="margin-bottom: 20px;margin-left: 330px">
					<button class="btn blue" id="confirmMove">
						<i class="icon-bar"></i> 提交采购需求
					</button>
				</div>
			</div>
			<br/>
		</div>
	</div>

	<#include "/common/footer.html">
	</div>
	<script type="text/javascript" src="${CONTEXT_PATH}js/datepicker/WdatePicker.js"></script>
	<script type="text/javascript">


        function getMaterialStock() {
            var materialArticleNumber = $('#materialArticleNumber').val();
			debugger;
            if(materialArticleNumber == ''){
                layer.alert('请填写耗材号或耗材名称！','error');
                return;
			}
            $.post(CONTEXT_PATH + "materialPurchase/queryMaterialPurchase", {materialArticleNumber:materialArticleNumber}, function(data) {
                if (data.status == 200) {
                	debugger;
					var articleNumberList=new Array();
					$("[id=data-materialArticleNumber]").each(function(){
						articleNumberList.push($(this).text());
					});
					var index = articleNumberList.length;
					var materialPurchaseList= data.body.materialPurchaseList;
					for (var i = 0; i<materialPurchaseList.length;i++) {
						var number=$.inArray(materialPurchaseList[i].materialArticleNumber, articleNumberList);
						if (number==-1){
							var html = "<tr>" +
									"<input type='text' id='data-taskNo'  value="+materialPurchaseList[i].taskNo+">"+
									"<td id='data-index'>"+parseInt(index+1)+"</td>"+
									"<td id='data-materialArticleNumber'>"+materialPurchaseList[i].materialArticleNumber+"</td>"+
									"<td id='data-name'>"+materialPurchaseList[i].name+"</td>"+
									"<td id='data-unit'>"+materialPurchaseList[i].unit+"</td>"+
									"<td id='data-boxSpecification'>"+materialPurchaseList[i].boxSpecification+"</td>"+
									"<td>"+materialPurchaseList[i].surplusQuantity+"</td>"+
									"<td><input type='number' id='data-quantity' style='width:100px;' value="+materialPurchaseList[i].quantity+"></td>"+
									"<td><input  id='data-remark' style='width:200px;' value="+materialPurchaseList[i].remark+"></td>"+
									"<td onclick='onRemove(this)'><a>删除</a></td></tr>";
						}
					}
					$("#tbody").append(html);
                } else {
                    layer.alert(data.message, "error");
                }
                $('#materialArticleNumber').val('');
            });
        }

        function onRemove(obj){
            if(confirm("是否删除该条SKU?")){
                $(obj).parent().remove();
                // 重新编排
                $("#tbody").find('tr').each(function (indexValue) {
					indexValue++;
					$(this).find('td[id="data-index"]').text(indexValue);
                });
            }
        }

		$('#confirmMove').on("click",function () {
            var purchaseItems = [];
            var bool=false;
            $('#tbody').find('tr').each(function (index) {
                var item = {};
                var dataIndex = $(this).find('td[id="data-index"]').text();
                var materialArticleNumber = $(this).find('td[id="data-materialArticleNumber"]').text();
				var quantity = $(this).find('td>input[id="data-quantity"]').val();
				if (quantity == null || quantity=='' ||quantity==0) {
					layer.alert('第'+dataIndex+'个耗材采购需求单的出库数量为空或为零！','error');
					bool=true;
					return ;
				}
				item.taskNo=$(this).find('input[id="data-taskNo"]').val();
				item.materialArticleNumber = materialArticleNumber;
				item.quantity = quantity;
				item.name = $(this).find('td[id="data-name"]').text();
				item.unit = $(this).find('td[id="data-unit"]').text();
				item.boxSpecification = $(this).find('td[id="data-boxSpecification"]').text();
				item.remark = $(this).find('td>input[id="data-remark"]').val();
				purchaseItems.push(item);
				bool=true;
			});
            if(purchaseItems.length>0){
				var param = {
					purchaseItems:JSON.stringify(purchaseItems),
				};
				$.post(CONTEXT_PATH + "materialPurchase/create", param, function(data) {
					debugger;
					if (data.status == 200) {
						layer.alert("成功！");
						setTimeout(function() {
							location.href = CONTEXT_PATH + "materialPurchase";
						}, 1500);
					} else {
						customizeLayer(data.message, "error");
					}
				});
			}
            if (!bool){
				layer.alert("没有可生成的数据！", "error");
			}
        });

	</script>
</body>
</html>