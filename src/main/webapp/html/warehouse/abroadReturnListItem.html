<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">
<html>
<head>
<title>易世通达仓库管理系统</title>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
	<#include "/common/include.html">
	<style type="text/css">
.modal-body input {
	width: 240px;
}
.top_bar{
    position:fixed;top:0px;
}
#task-list td {
	vertical-align:middle;
}
</style>
</head>
<body>
<@header method="header" active="17020000"><#include "/ftl/header.ftl"></@header>

	<div id="page" style="background-color: rgb(231, 237, 248)">
		<div class="row">
			<div class="col-md-12" style="padding: 0">
				<ul class="page-breadcrumb breadcrumb" style="background-color: white;">
					<li><a href="#">入库管理</a></li>
					<li class="active">海外退件</li>
					<li class="active">海外退件明细</li>
				</ul>
			</div>
		</div>
		<!-- 内容 -->
		<div class="container-fluid" style="background-color: white;border: none">
		<#assign query=domain.query>
		<!-- END PAGE HEADER-->
		<div class="row">
			</br>
		</div>
		
		<div class="row">
			<div class="col-md-12">	
			
					<table>
						<tr>
							<td>海外退件单号 :</td>
							<td style="width:200px;">${ domain.whAbroadReturn.returnNo}</td>
							<td>周转筐:</td>
							<td>${ domain.whAbroadReturn.boxNo}</td>
						</tr>
					</table>
					<table class="table table-striped table-bordered table-hover table-condensed" id="task-list">
						<colgroup>
							<col width="4%" />
							<col width="8%" />
							<col width="8%" />
							<col width="8%" />
							<col width="8%" />
							<col width="8%" />
							<col width="6%" />
							<col width="8%" />
							<col width="8%" />
							<col width="8%" />
							<col width="8%" />
							<col width="8%" />
							<col width="10%" />
						</colgroup>
						<thead>
							<tr>
								<th><input type="checkbox"  id="check-all" name="checkAll">全选</th>
								<th>sku</th>
								<th>名称</th>
								<th>库位</th>
								<th>销售属性</th>
								<th>数量</th>
								<th>待上架数量</th>
								<th>已上架数量</th>
								<th>状态</th>
								<th>修改日期</th>
							</tr>
						</thead>
						<tbody>
							<#list domain.whAbroadReturn.whAbroadReturnItems as whAbroadReturnItem>
								<tr>
									<td>
										<input type="checkbox" value="${whAbroadReturnItem.id}" name="id">
									</td>
									<td>${whAbroadReturnItem.sku}</td>
									<td>${whAbroadReturnItem.whSku.name}</td>
									<td>${whAbroadReturnItem.whSku.locationNumber}</td>
									<td>${whAbroadReturnItem.saleAttribute}</td>
									<td>${whAbroadReturnItem.quantity}</td>
									<td>${whAbroadReturnItem.quantity-whAbroadReturnItem.completeQuantity}</td>
									<td>${whAbroadReturnItem.completeQuantity}</td>
									<td>${whAbroadReturnItem.statusName}</td>
									<td>${whAbroadReturnItem.lastUpdateDate }</td>
									
								</tr>
							</#list>
						</tbody>
					</table>
			</div>
		</div>
	</div>
	
	<#include "/common/footer.html">
	</div>
	<script type="text/javascript" src="${CONTEXT_PATH}js/datepicker/WdatePicker.js"></script>
	<script type="text/javascript">   
	</script>
</body>
</html>