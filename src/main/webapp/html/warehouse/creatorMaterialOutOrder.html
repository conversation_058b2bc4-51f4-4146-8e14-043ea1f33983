<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">
<html>
<head>
<title>易世通达仓库管理系统</title>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
	<#include "/common/include.html">
	<style type="text/css">
.modal-body input {
	width: 240px;
}
#header-div{
 	margin-left: 400px;
}
#task-list td {
	vertical-align:middle;
}
</style>
</head>
<body>
	<@header method="header" active="18050000"><#include "/ftl/header.ftl"></@header>
	<div id="page" style="background-color: rgb(231, 237, 248)">
		<div class="row">
			<div class="col-md-12" style="padding: 0">
				<ul class="page-breadcrumb breadcrumb" style="background-color: white;">
					<li><a href="#">库存管理</a></li>
					<li class="active">耗材出库</li>
					<li class="active">创建耗材出库单</li>
				</ul>
			</div>
		</div>
		<!-- 内容 -->
		<div class="container-fluid" style="background-color: white;border: none;">
		<!-- END PAGE HEADER-->
		<div class="row" >
			<div class="col-md-9" id="header-div">
				<form action="${CONTEXT_PATH}stockMove/create"
					class="form-horizontal form-bordered form-row-stripped"
					method="post" modelAttribute="domain" name="checkInForm" id="domain">
					<div class="form-body" >
						<div class="form-group" style="margin-left: 180px">
							<label class="control-label col-md-1">领取人</label>
							<div class="col-md-3">
								<input class="form-control" id="receiveBy"  type="text"  name="query.receiveBy">
							</div>
						</div>
						<div class="form-group" style="margin-left: 180px">
							<label class="control-label col-md-1">耗材型号</label>
							<div class="col-md-3">
								<input class="form-control" id="materialArticleNumber"  type="text" value="${query.materialArticleNumber}"  onkeypress="if(event.keyCode==13) { inputMaterialArticleNumber(this); return false;}" tabindex="4">
							</div>
						</div>
						<div class="form-group" style="margin-left: 180px">
							<label class="control-label col-md-1">出库数量</label>
							<div class="col-md-3">
								<input class="form-control"  id="quantity" type='number'   value="${query.quantity}">

							</div>
							<label class="control-label col-md-1">可用库存</label>
							<div class="col-md-3" style="font-size: 18px;margin-top: 2px;color: red" id="materialStocks"></div>
						</div>
						<div class="form-group" style="margin-left: 300px">
							<div class="col-md-2">
								<div style="border-radius: 8px !important;width: 100%;" type="button" class="btn blue" onclick="getMaterialStock();">
									<i class="icon-plus"></i> 提交
								</div>
							</div>
						</div>
						<div class="form-group" style="margin-top: 20px;">
							<div class="col-md-7">
								<table class="table table-striped table-bordered table-hover table-condensed" id="task-list">
									<thead>
										<tr>
											<th>序号</th>
											<th>领取人</th>
											<th>耗材型号</th>
											<th>耗材名称</th>
											<th>单位</th>
											<th>出库数量</th>
											<th>操作</th>
										</tr>
									</thead>
									<tbody id="tbody">
									</tbody>
								</table>
							</div>
						</div>
					</div>
				</form>
				<div class="col-md-9" style="margin-bottom: 20px;margin-left: 330px">
					<button class="btn blue" id="confirmMove">
						<i class="icon-bar"></i> 确认出库
					</button>
				</div>
			</div>
			<br/>
		</div>
	</div>
	
		<div id="fixed-bottom">
			<div id="pager"></div>
		</div>
	<#include "/common/footer.html">
	</div>
	<script type="text/javascript" src="${CONTEXT_PATH}js/datepicker/WdatePicker.js"></script>
	<script type="text/javascript">

		// 领取人
		$.getJSON(CONTEXT_PATH + "system/saleusers/userByRole", function (json) {
			if (json) {
				$("input[name='query.receiveBy']").select2({
					data: json,
					placeholder: "领取人",
					allowClear: true
				});
			} else {
				$("input[name='query.receiveBy']").attr("placeholder", "该角色没有相关人员!").attr("readonly", true);
			}
		});

        function getMaterialStock() {
            var materialArticleNumber = $('#materialArticleNumber').val();
			var quantity = $('#quantity').val();
			var receiveBy = $('#receiveBy').val();
			var materialStocks = $('#materialStocks').html();
			debugger;

			if (receiveBy==''|| receiveBy==null){
				layer.alert('领取人不能为空！','error');
				return;
			}

			if (materialStocks==''|| materialStocks==null){
				layer.alert('请先查询耗材可用库存，填写耗材号后回车','error');
				return;
			}

			if (quantity<=0){
				layer.alert('出库数量不能小于等于零','error');
				return;
			}
			if (parseInt(quantity)>parseInt(materialStocks)){
				layer.alert('出库数量不能大于可用库存','error');
				return;
			}
			debugger;
            if(materialArticleNumber == ''){
                layer.alert('耗材号不能为空！','error');
                return;
			}
            $.post(CONTEXT_PATH + "packagingMaterialInventory/generateMaterialStocks", {materialArticleNumber:materialArticleNumber,quantity:quantity}, function(data) {
                if (data.status == 200) {
                	debugger;
                    var articleNumberList=new Array();
                    $("[id=data-materialArticleNumber]").each(function(){
						articleNumberList.push($(this).text());
                    });
					var index = articleNumberList.length;
					var materialManagement = data.body.materialManagement;
					var number=$.inArray(materialArticleNumber, articleNumberList);
					if (number==-1){
						if(materialManagement!=null && materialManagement!=''){
							var html = "<tr>" +
									"<td id='data-index'>"+parseInt(index+1)+"</td>"+
									"<td id='data-receiveBy'>"+receiveBy+"</td>"+
									"<td id='data-materialArticleNumber'>"+materialManagement.materialArticleNumber+"</td>"+
									"<td >"+materialManagement.name+"</td>"+
									"<td>"+materialManagement.unit+"</td>"+
									"<td id='data-quantity'>"+quantity+"</td>"+
									"<td onclick='onRemove(this)'><a>删除</a></td></tr>";

							$("#tbody").append(html);
						}else{
							var html = "<tr>" +
									"<td id='data-index'>"+parseInt(index+1)+"</td>"+
									"<td id='data-receiveBy'>"+receiveBy+"</td>"+
									"<td id='data-materialArticleNumber'>"+materialArticleNumber+"</td>"+
									"<td ></td>"+
									"<td></td>"+
									"<td id='data-quantity'>"+quantity+"</td>"+
									"<td onclick='onRemove(this)'><a>删除</a></td></tr>";
							$("#tbody").append(html);
						}

					}
                } else {
                    layer.alert(data.message, "error");
                }
                $('#materialArticleNumber').val('');
				$('#quantity').val('');
				$('#materialStocks').html('');
            });
        }

        function onRemove(obj){
            if(confirm("是否删除该条SKU?")){
                $(obj).parent().remove();
                // 重新编排
                $("#tbody").find('tr').each(function (indexValue) {
					indexValue++;
					$(this).find('td[id="data-index"]').text(indexValue);
                });
            }
        }

		$('#confirmMove').on("click",function () {
			debugger;
            var stockItems = [];
            var skus = '';
            var result = false;
            $('#tbody').find('tr').each(function (index) {
            	debugger;
                var item = {};
                var dataIndex = $(this).find('td[id="data-index"]').text();
                var materialArticleNumber = $(this).find('td[id="data-materialArticleNumber"]').text();
                var quantity = $(this).find('td[id="data-quantity"]').text();
				var receiveBy = $(this).find('td[id="data-receiveBy"]').text();

				if (quantity == null || quantity=='' ||quantity==0) {
					layer.alert('第'+dataIndex+'个耗材出库单的出库数量为空或为零！','error');
					result = false;
					return false;
				}
				if (receiveBy == null || receiveBy=='' ||receiveBy==0) {
					layer.alert('第'+dataIndex+'个耗材出库的领取人不能为空！','error');
					result = false;
					return false;
				}

				item.materialArticleNumber = materialArticleNumber;
				item.quantity = quantity;
				item.receiveBy = receiveBy;
				stockItems.push(item);
                result = true;
			});
            if(result){
				var param = {
					stockItemsStr:JSON.stringify(stockItems),
				};
				$.post(CONTEXT_PATH + "materialOutOrder/create", param, function(data) {
					debugger;
					if (data.status == 200) {
						layer.alert("成功！");
						setTimeout(function() {
							location.href = CONTEXT_PATH + "materialOutOrder";
						}, 1500);
					} else {
						customizeLayer(data.message, "error");
					}
				});
			}
            if (skus==''&& !result){
				layer.alert("没有可生成的数据！", "error");
			}
        });

		//加载SKU数据，输入SKU触发
		function inputMaterialArticleNumber(obj){
			var materialArticleNumber = $('#materialArticleNumber').val();
			if (materialArticleNumber == null || materialArticleNumber =='') {
				layer.alert("请输入耗材型号!", 'error');
				return ;
			}
			debugger;
			$.post(CONTEXT_PATH + "packagingMaterialInventory/queryMaterialStocks", {materialArticleNumber:materialArticleNumber}, function(data) {
				if (data.status == 200) {
					debugger;
					var message=data.message;
					$('#materialStocks').html(message);
					$('#materialArticleNumber').val(materialArticleNumber);
                    $('#quantity').focus();
					debugger;
				}
				else{
					$('#materialStocks').html('');
					layer.alert(data.message, "error");
				}
			});

		}


	</script>
</body>
</html>