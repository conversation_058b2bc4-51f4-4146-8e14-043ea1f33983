<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">
<html>
<head>
    <title>易世通达仓库管理系统</title>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <#include "/common/include.html">
        <style type="text/css">
            .modal-body input {
                width: 240px;
            }
        </style>
        </head>
<body>
<@header method="header" active="11020000"><#include "/ftl/header.ftl"></@header>

<div id="page" style="background-color: rgb(231, 237, 248)">
    <div class="row">
        <div class="col-md-12" style="padding: 0">
            <ul class="page-breadcrumb breadcrumb" style="background-color: white;">
                <li><a href="#">仓库管理</a></li>
                <li class="active">计算每日出入库汇总</li>
            </ul>
        </div>
    </div>
    <div class="container-fluid" style="background-color: white;border: none">
            <div class="row">
                <div class="col-md-9">
                    <form action="${CONTEXT_PATH}warehouse/dayOfStock" class="form-horizontal form-bordered form-row-stripped"
                          method="post" modelAttribute="domain" name="dayOfStockForm" id="domain">
                        <div class="form-body">
                            <div class="form-group">
                                <label class="control-label col-md-1" ></label>
                                <div class="col-md-3"></div>

                                <label class="control-label col-md-1" style="width: auto;" >统计日期</label>
                                <div class="col-md-5">
                                    <div>
                                        <input class="form-control Wdate" type="text" id="startDate" name="startDate" placeholder="" readonly="readonly" value=""
                                               onfocus="WdatePicker({startDate:'%y-%M-%d',dateFmt:'yyyy-MM-dd',alwaysUseStartDate:true})">
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-offset-1" style="text-align: center">
                            <button type="button" class="btn blue" id="save_return" onclick="calculateDayOfStock(1)">
                                <i class="icon-search"></i> 统计选择当天
                            </button>
                            <button type="button" class="btn blue" id="save_return" onclick="calculateDayOfStock(2)">
                                <i class="icon-search"></i> 统计选择以后所有
                            </button>
                        </div>
                    </form>
                </div>

            </div>
    </div>

    <div id="fixed-bottom">
        <div id="pager"></div>
    </div>
</div>
<script type="text/javascript" src="${CONTEXT_PATH}js/datepicker/WdatePicker.js"></script>
<script type="text/javascript" src="${CONTEXT_PATH }js/web-storage-cache.js"></script>
<script type="text/javascript">


    $(document).ready(function(){

    }); // end ready



    function calculateDayOfStock(type) {

        var startDate = $('#startDate').val().trim();

        if(!startDate || startDate == ''){
            layer.alert("请选择日期!",'error');
            return ;
        }
        var url = '';
        if (type == 1){
            url = CONTEXT_PATH+"dayOfStock/calculateDayOfStock";
        }
        else if (type == 2){
            url = CONTEXT_PATH+"dayOfStock/calculateDayOfStockAll";
        }else {
            return;
        }
        $.ajax({
            url:url,
            type:"POST",
            data:{
                startDate:startDate
            },
            success:function(result){
                if(result.status == "200"){
                    alert("统计成功!");
                }else{
                    customizeLayer(result.message,'error');
                }
            },
            error : function() {
                layer.alert("操作失败!",'error');
            }
        });


    }




</script>
</body>
</html>