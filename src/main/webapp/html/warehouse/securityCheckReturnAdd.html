<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">
<html>

	<head>
		<title>易世通达仓库管理系统</title>
		<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
		<#include "/common/include.html">
		<style type="text/css">
			.modal-body input {
				width: 240px;
			}
			
			.top_bar {
				position: fixed;
				top: 0px;
			}
			
			#task-list td {
				vertical-align: middle;
			}
		</style>
	</head>

	<body>
		<@header method="header" active="11020000"><#include "/ftl/header.ftl"></@header>
		<div id="page">
			<div class="row">
				<div class="col-md-12">
					<ul class="page-breadcrumb breadcrumb">
						<li>
							<a href="#">仓库管理</a>
						</li>
						<li class="active">安检退回</li>
						<li class="active">新增安检退回</li>
					</ul>
				</div>
			</div>
			<div class="container-fluid">
				<h2 class="header-title">安检退回</h2>
				<#assign whSecurityCheckReturn=domain.whSecurityCheckReturn>
				<!-- END PAGE HEADER-->
				<div class="row">
					<div class="col-md-12">
						<form action="${CONTEXT_PATH}warehouse/securityCheckReturn/update" class="form-horizontal form-bordered form-row-stripped" method="post" modelAttribute="domain" name="whSecurityCheckReturnForm" id="domain">
							<div class="form-body">
								<input type="hidden" name="whSecurityCheckReturn.id" value="${whSecurityCheckReturn.id}">
								<input type="hidden" id="warehouse" name="warehouse" value="${whSecurityCheckReturn.warehouse}">
								<div class="form-group">
									<label class="control-label col-md-1" style="width:130px;"></label>
									<div class="col-md-3"></div>
									<label class="control-label col-md-1" style="width:130px;">安检退回单号</label>
									<div class="col-md-3">
										<div>
											<input type="text" name="whSecurityCheckReturn.returnNumber" readonly class="form-control" value="${whSecurityCheckReturn.returnNumber}">
										</div>
									</div>
								</div>

								<!-- 编辑或查看 -->
								<#if (whSecurityCheckReturn.id)??>
									<div class="form-group">
										<label class="control-label col-md-1" style="width:130px;"></label>
										<div class="col-md-3"></div>
										<#if whSecurityCheckReturn.returnType == 1>
											<label class="control-label col-md-1" style="width:130px;">货代退回重发</label>
										</#if>
										<#if whSecurityCheckReturn.returnType == 2>
											<label class="control-label col-md-1" style="width:130px;">货代包裹退回</label>
										</#if>
										<div class="col-md-3">
											<div>
												<#if domain.viewOrUpdate == 'toView'>
													<textarea rows="5" wrap="off" readonly class="form-control" name="whSecurityCheckReturn.returnApvNoArr">${whSecurityCheckReturn.returnApvNoArr}</textarea>
												</#if>
												<#if domain.viewOrUpdate == 'toUpdate'>
													<textarea rows="5" wrap="off" placeholder="请输入退回单号,多个单号逗号分割" class="form-control" name="whSecurityCheckReturn.returnApvNoArr">${whSecurityCheckReturn.returnApvNoArr}</textarea>
												</#if>
											</div>
										</div>
									</div>
								<#else>
									<!-- 新增 -->
									<div class="form-group">
										<input type="hidden" name="whSecurityCheckReturn.returnApvNoArr" value="">
										<input type="hidden" name="whSecurityCheckReturn.returnType" value="">
										<input type="hidden" name="whSecurityCheckReturn.oldWarehouseApvNoArr" value="">
										<label class="control-label col-md-1" style="width:130px;"></label>
										<div class="col-md-3"></div>
										<label class="control-label col-md-1" style="width:130px;">货代退回重发</label>
										<div class="col-md-3">
											<div>
												<textarea rows="5" wrap="off" placeholder="请输入退回单号,多个单号逗号分割" class="form-control" id="returnApvNoArr1"></textarea>
											</div>
										</div>
									</div>

									<div class="form-group">
										<label class="control-label col-md-1" style="width:130px;"></label>
										<div class="col-md-3"></div>
										<label class="control-label col-md-1" style="width:130px;">货代包裹退回</label>
										<div class="col-md-3">
											<div>
												<textarea rows="5" wrap="off" placeholder="请输入退回单号,多个单号逗号分割" class="form-control" id="returnApvNoArr2"></textarea>
											</div>
										</div>
									</div>
								</#if>

							</div>
							<div class="col-md-offset-1" style="text-align: center">
								<#if domain.viewOrUpdate == 'toView'>
									<a class="btn btn-default" href="${CONTEXT_PATH}warehouse/securityCheckReturn">
										<span class="icon-back"></span> 返回
									</a>
								<#else>
									<button type="submit" class="btn blue">
										<i class="icon-save"></i> 保存
									</button>
								</#if>
							</div>
						</form>
					</div>
					<br/>
				</div>
			</div>

			<div id="fixed-bottom">
				<div id="pager"></div>
			</div>
			<#include "/common/footer.html">
		</div>
		<script type="text/javascript" src="${CONTEXT_PATH}js/datepicker/WdatePicker.js"></script>
		<script type="text/javascript">
			var flag = false;
			var userForm = $('#domain');
			userForm.validate({
				rules: {},
				submitHandler: function(form) {

					var id = $("input[name='whSecurityCheckReturn.id']").val();
					var warehouse = $("input[name='warehouse']").val();
					var returnApvNoArr = $("textarea[name='whSecurityCheckReturn.returnApvNoArr']").val();
					var apvNoArrCheck; 
					if(id == '') {
						var returnApvNoArr1 = $("#returnApvNoArr1").val();
						var returnApvNoArr2 = $("#returnApvNoArr2").val();
						if(returnApvNoArr1 != '' && returnApvNoArr2 != '') {
							layer.alert("只能输入一种退回类型！", 'error');
							return false;
						}
						if(returnApvNoArr1 == '' && returnApvNoArr2 == '') {
							layer.alert("请输入要退回的单号！", 'error');
							return false;
						}
						if(returnApvNoArr1 != '') {
							$("input[name='whSecurityCheckReturn.returnType']").val(1);
							$("input[name='whSecurityCheckReturn.returnApvNoArr']").val(returnApvNoArr1);
							apvNoArrCheck = returnApvNoArr1;
						}
						if(returnApvNoArr2 != '') {
							$("input[name='whSecurityCheckReturn.returnType']").val(2);
							$("input[name='whSecurityCheckReturn.returnApvNoArr']").val(returnApvNoArr2);
							apvNoArrCheck = returnApvNoArr2;
						}
					} else {
						if(returnApvNoArr == '') {
							layer.alert("单号不能为空！", 'error');
							return false;
						}
						apvNoArrCheck = returnApvNoArr;
					}
					//if(!flag) {
						//layer.alert("等待请校验单号！", 'error');
						//return false;
						var apvNoArr = apvNoArrCheck;
						if(apvNoArr == '') {
							layer.alert("单号不能为空！", 'error');
							return false;
						}
						$.ajax({
							url: CONTEXT_PATH + "warehouse/securityCheckReturn/checkApvNos",
							type: "POST",
							data: { apvNoArr: apvNoArr, id: id, warehouse:warehouse},
							success: function(json) {
								if(json.status == '500') {
									customizeLayer(json.message + '', 'error');
								} else if(json.status == '200') {
									if (json.message!=null && json.message!='') {
										$("input[name='whSecurityCheckReturn.oldWarehouseApvNoArr']").val(json.message);
									}
									flag = true;
									App.blockUI();
									form.submit();
									return false;
								}
							},
							error: function() {
								layer.alert("单号校验失败！", 'error');
							}
						});
					//}
					/* if(flag) {
						App.blockUI();
						form.submit();
						return false;
					} */
					/* setTimeout(function(){
					}, 500);*/
				},
				/* 重写错误显示消息方法,以layer.alert方式弹出错误消息 */
				showErrors: function(errorMap, errorList) {
					var msg = "";
					$.each(errorList, function(i, v) {
						msg += (v.message + "\r\n");
					});
					if(msg != "") layer.alert(msg, 'error');
				},
				/* 失去焦点时不验证 */
				onfocusout: false
			});

			function checkApvNos(obj) {
				var apvNoArr = $(obj).val();
				if(apvNoArr == '') {
					layer.alert("单号不能为空！", 'error');
					return false;
				}
				$.ajax({
					url: CONTEXT_PATH + "warehouse/securityCheckReturn/checkApvNos",
					type: "POST",
					data: { apvNoArr: apvNoArr },
					success: function(json) {
						if(json.status == '500') {
							customizeLayer(json.message + '', 'error');
						} else if(json.status == '200') {
							layer.alert("单号校验成功！");
							flag = true;
						}
					},
					error: function() {
						layer.alert("单号校验失败！", 'error');
					}
				});

			}
		</script>
	</body>

</html>