<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">
<html>
<head>
	<title>易世通达仓库管理系统</title>
	<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
	<#include "/common/include.html">
	<link href="${CONTEXT_PATH}js/assets/plugins/jquery-file-upload/css/jquery.fileupload-ui.css" rel="stylesheet" type="text/css"/>
	<style type="text/css">
		.top_bar{
			position:fixed;top:0px;
		}
		.modal-body .form-group {
			margin-bottom: 20px;
		}
		#location-list td {
			vertical-align:middle;
			text-align:center;
		}

	</style>
</head>
<body>
<@header method="header" active="11030000"><#include "/ftl/header.ftl"></@header>

<div id="page" style="background-color: rgb(231, 237, 248)">
	<div class="row">
		<div class="col-md-12" style="padding: 0">
			<ul class="page-breadcrumb breadcrumb" style="background-color: white;">
				<li><a href="#">库位管理</a></li>
				<li class="active">入库异常库位列表</li>
			</ul>
		</div>
	</div>

	<!-- 内容 -->
	<div class="container-fluid" style="background-color: white;border: none">
	<div class="row">
		<div class="col-md-12">
		<#assign query=domain.query>
			<form action="${CONTEXT_PATH}whExLocation/search"
					   class="form-horizontal form-bordered form-row-stripped"
					   method="post" modelAttribute="domain" name="whExLocationForm" id ="domain">
				<!-- 分页信息 -->
				<input id="page-no" type="hidden" name="page.pageNo" value="1">
				<input id="page-size" type="hidden" name="page.pageSize" value="${domain.page.pageSize}">
				<input id="warehouseType-id" type="hidden" name="locationWarehouseType" value="${domain.locationWarehouseType}">
				<div class="form-body">
					<div class="form-group">
						<label class="control-label col-md-1">区域</label>
						<div class="col-md-3">
							<select name="query.locationRegion" class="form-control" id = "locationRegion-id">
								<option vlaue=""></option>
								<#list domain.locationRegionList as locationRegion>
									<#if query.locationRegion == locationRegion>
										<option selected="selected" value="${locationRegion}">${locationRegion}</option>
									<#else>
										<option value="${locationRegion}">${locationRegion}</option>
									</#if>
								</#list>
							</select>
						</div>
						<label class="control-label col-md-1">货架</label>
						<div class="col-md-3">
                            <input class="form-control" autocomplete="off" type="text" name="query.locationAisle" value="${query.locationAisle}">
						</div>
						<label class="control-label col-md-1">库位</label>
						<div class="col-md-3">
							<input class="form-control" autocomplete="off" type="text" name="query.locationStr" placeholder="支持逗号分隔查询" value="${query.locationStr }">
						</div>
						<label class="control-label col-md-1">库位状态</label>
						<div class="col-md-3">
							<select name="query.locationStatus" class="form-control" id = "locationStatus-id">
								<option vlaue=""></option>
								<#list domain.locationStatusList as locationStatus>
									<#if query.locationStatus == locationStatus.code>
										<option selected="selected" value="${locationStatus.code}">${locationStatus.getName()}</option>
									<#else>
										<option value="${locationStatus.code}">${locationStatus.getName()}</option>
									</#if>
								</#list>
							</select>
						</div>

						<label class="control-label col-md-1">品类上限</label>
						<div class="col-md-3">
							<input class="form-control" autocomplete="off" type="number" min=0 name="query.categoryLimit" placeholder="请输入品类上限" value="${query.categoryLimit }" onkeyup="if(this.value.length==1){this.value=this.value.replace(/[^0-9]/g,'')}else{this.value=this.value.replace(/\D/g,'')}" onafterpaste="if(this.value.length==1){this.value=this.value.replace(/[^0-9]/g,'')}else{this.value=this.value.replace(/\D/g,'')}">
						</div>
						<label class="control-label col-md-1">实际品类数</label>
						<div class="col-md-3">
							<input class="form-control" autocomplete="off" type="number" min=0 name="query.categoryActual" placeholder="请输入实际品类数" value="${query.categoryActual}" onkeyup="if(this.value.length==1){this.value=this.value.replace(/[^0-9]/g,'')}else{this.value=this.value.replace(/\D/g,'')}" onafterpaste="if(this.value.length==1){this.value=this.value.replace(/[^0-9]/g,'')}else{this.value=this.value.replace(/\D/g,'')}">
						</div>
					</div>
				</div>
				<div>
					<div class="pull-left" style="margin-left: 10px;">
							<button type="button" class="btn  btn-default" onclick="checkNegative();">
								反选
							</button>
							<button type="button" class="btn  btn-default" onclick="checkAll();">
								全选
							</button>
                            <@header method="auth" authCode="CHECKIN_EXCEPTION_SHELF_MANAGE_ADD_LOCATION">
							<button type="button" class="btn  btn-default" onclick="addLocation()">
								<i class="icon-pickingTask"></i> 添加库位
							</button>
                            </@header>
                            <@header method="auth" authCode="CHECKIN_EXCEPTION_SHELF_OPEN">
							<button type="button" class="btn  btn-default" onclick="batchModifyLocation(1)">
								<i class="icon-pickingTask"></i> 开启
							</button>
                            </@header>
                            <@header method="auth" authCode="CHECKIN_EXCEPTION_SHELF_CLOSED">
                            <button type="button" class="btn  btn-default" onclick="batchModifyLocation(2)">
								<i class="icon-pickingTask"></i> 关闭
							</button>
                            </@header>
							<@header method="auth" authCode="CHECKIN_EXCEPTION_SHELF_DELETE">
							<button type="button" class="btn  btn-default" onclick="batchModifyLocation(3)">
								<i class="icon-pickingTask"></i> 删除
							</button>
							</@header>
					</div>		
					<div class="col-md-offset-10" style="text-align: right">
						<button type="button" class="btn btn-default" onclick="formReset(this)">
							<i class="icon-refresh"></i> 重置
						</button>
						<button type="submit" class="btn blue">
							<i class="icon-search"></i> 查询
						</button>
                        <@header method="auth" authCode="CHECKIN_EXCEPTION_SHELF_DOWNLOAD">
						<button type="button" class="btn btn-default" onclick="downloadLocation()">
							<i class="icon-download"></i> 导出
						</button>
                        </@header>
					</div>
				</div>
			</form>
		</div>
		<br/>
	</div>

	<div class="row">
		<div id="fixedDiv" class="col-md-12">
			<table class="table table-striped table-bordered table-hover table-condensed" id="fixedTab">
				<colgroup>
					<col width="5%"/>
					<col width="5%"/>
					<col width="5%"/>
					<col width="15%"/>
					<col width="10%"/>
					<col width="10%"/>
					<col width="5%"/>
					<col width="5%"/>
					<col width="5%"/>
					<col width="5%"/>
					<col width="10%"/>
					<col width="10%"/>
					<col width="10%"/>
				</colgroup>
				<thead>
					<tr>
						<th>选择</th>
						<th>区域</th>
						<th>货架</th>
						<th>库位</th>
						<th>品类上限</th>
						<th>实际品类</th>
						<th>长(cm)</th>
						<th>宽(cm)</th>
						<th>高(cm)</th>
						<th>体积(m³)</th>
						<th>库位类型</th>
						<th>状态</th>
						<th>操作</th>
					</tr>
				</thead>
			</table>
		</div>
		<div class="col-md-12" id="lcoation-list-warp">
			<table class="table table-striped table-bordered table-hover table-condensed" id="lcoation-list">
				<colgroup>
					<col width="5%"/>
					<col width="5%"/>
					<col width="5%"/>
					<col width="15%"/>
					<col width="10%"/>
					<col width="10%"/>
					<col width="5%"/>
					<col width="5%"/>
					<col width="5%"/>
					<col width="5%"/>
					<col width="10%"/>
					<col width="10%"/>
					<col width="10%"/>
				</colgroup>
				<thead>
					<tr>
						<th>选择</th>
						<th>区域</th>
						<th>货架</th>
						<th>库位</th>
						<th>品类上限</th>
						<th>实际品类</th>
						<th>长(cm)</th>
						<th>宽(cm)</th>
						<th>高(cm)</th>
						<th>体积(m³)</th>
						<th>库位类型</th>
						<th>状态</th>
						<th>操作</th>
					</tr>
				</thead>
				<tbody>
					<#list domain.whExLocations as exLocation>
						<tr>
							<td><input type="checkbox" name="locationIds" value="${exLocation.id}" /></td>
							<td>${exLocation.locationRegion}</td>
							<td>${exLocation.locationAisle}</td>
							<td>${exLocation.location}</td>
							<td>${exLocation.categoryLimit}</td>
							<td>${exLocation.categoryActual}</td>
							<td>${exLocation.locationLength}</td>
							<td>${exLocation.locationWidth}</td>
							<td>${exLocation.locationHeight}</td>
							<td>${exLocation.locationVolume}</td>
							<td>
								<span class="btn btn-sm">
									${util('enumName',"com.estone.warehouse.enums.LocationType", exLocation.locationType)}
								</span>
							</td>
							<td>
								<span class="btn btn-sm">
									${util('enumName',"com.estone.warehouse.enums.LocationStatus", exLocation.locationStatus)}
								</span>
							</td>
							<td>
								<button type="button" class="btn btn-xs btn-info" onclick="editLocation('${exLocation.id}','${exLocation.location}','${exLocation.categoryLimit}','${exLocation.locationLength}','${exLocation.locationWidth}','${exLocation.locationHeight}','${exLocation.locationStatus}')">编辑</button>
								<button type="button" class="btn btn-xs btn-info" onclick="viewLog(${exLocation.id}, 'whExLocation')">日志</button>
							</td>
						</tr>
					</#list>
				</tbody>
			</table>
		</div>
	</div>
		<div id="fixed-bottom">
			<div id="pager"></div>
		</div>
</div>

	<div class="modal fade ui-popup" id="add_modal" style="overflow:hidden;" tabindex="-1" role="dialog" aria-labelledby="organization-add-label" aria-hidden="true">
		<div class="modal-dialog">
			<div class="modal-content">
				<div class="modal-header">
					<button type="button" class="close" data-dismiss="modal" aria-hidden="true"></button>
					<h4 class="modal-title" id="expert-modal-label">新建库位</h4>
					<input type="hidden" name="query.id" type="text">
				</div>
				<div class="modal-body form-horizontal portlet" style="height:300px;">
					<input type="hidden" id="warehouseType" name="warehouseType" value="${domain.locationWarehouseType}"/>
					<div class="form-group">
						<div class="col-md-11 br-div"></div>
						<div class="col-md-12">
							<div class="line-height-label">
								<div class="col-md-3">
									<label class="control-label col-md-11"><span class="required">*</span>库位类型:</label>
								</div>
								<div class="col-md-7">
									<select name="query.locationType" class="form-control" id = "add_modal_locationType_id">
										<option selected="selected" value="4">入库异常</option>
									</select>
								</div>
								<div class="col-md-1"><span class="help-block">&nbsp;</span></div>
							</div>
						</div>
					</div>
					
					<div class="form-group">
						<div class="col-md-11 br-div"></div>
						<div class="col-md-12">
							<div class="line-height-label">
								<div class="col-md-3">
									<label class="control-label col-md-11"><span class="required">*</span>库位号:</label>
								</div>
								<div class="col-md-7">
									<input autocomplete="off" class="form-control" placeholder="库位号" name="query.location" type="text">
								</div>
								<div class="col-md-1"><span class="help-block">&nbsp;</span></div>
							</div>
						</div>
						<div class="col-md-11 br-div"></div>
						<div class="col-md-12">
							<div class="line-height-label">
								<div class="col-md-3"></div>
								<div class="col-md-7" style="color: grey">例：A01-01-01</div>
								<div class="col-md-1"><span class="help-block">&nbsp;</span></div>
							</div>
						</div>
					</div>
					
					<div class="form-group">
						<div class="col-md-11 br-div"></div>
						<div class="col-md-12">
							<div class="line-height-label">
								<div class="col-md-3">
									<label class="control-label col-md-11"><span class="required">*</span>品类上限:</label>
								</div>
								<div class="col-md-7">
									<input autocomplete="off" class="form-control" placeholder="品类上限" name="query.categoryLimit" type="number">
								</div>
								<div class="col-md-1"><span class="help-block">&nbsp;</span></div>
							</div>
						</div>
					</div>
					
					<div class="form-group">
						<div class="col-md-11 br-div"></div>
						<div class="col-md-12">
							<div class="line-height-label">
								<div class="col-md-3">
									<label class="control-label col-md-11">长*宽*高(cm):</label>
								</div>
								<div class="col-md-8">
									<input autocomplete="off" style="margin-right: 20px;" class="form-control input-inline input-xsmall"
										   placeholder="长cm" name="query.locationLength" aria-invalid="false"
										   type="text">
									<input autocomplete="off" style="margin-right: 20px;" class="form-control input-inline input-xsmall"
										   placeholder="宽cm" name="query.locationWidth" aria-invalid="false"
										   type="text">
									<input autocomplete="off" style="margin-right: 20px;" class="form-control input-inline input-xsmall"
										   placeholder="高cm" name="query.locationHeight" aria-invalid="false"
										   type="text">
								</div>
								<div class="col-md-1"><span class="help-block">&nbsp;</span></div>
							</div>
						</div>
					</div>	
					
					<div class="form-group">
						<div class="col-md-11 br-div"></div>
						<div class="col-md-12">
							<div class="line-height-label">
								<div class="col-md-3">
									<label class="control-label col-md-11"><span class="required" aria-required="true">*</span>状态:</label>
								</div>
								<div class="col-md-8">
									<label class="radio-inline" title="启动">
										<input id="add_modal_locationStatus" class="input-xsmall" autocomplete="off" name="query.locationStatus" value="1" checked="checked" type="radio">启动
									</label>
									<label class="radio-inline" title="关闭">
										<input id="add_modal_locationStatus_close" class="input-xsmall" autocomplete="off" name="query.locationStatus" value="2" type="radio">关闭
									</label>
								</div>
							</div>
						</div>
					</div>
				</div>	
				<div class="modal-footer">
					<button type="button" class="btn btn-primary" onclick="saveLocation()">保存</button>
					<button type="button" class="btn btn-default" data-dismiss="modal">取消</button>
				</div>
			</div>
		</div>
	</div>

<#include "/common/footer.html">
</div>
<script type="text/javascript" src="${CONTEXT_PATH}js/datepicker/WdatePicker.js"></script>
<script type="text/javascript" src="${CONTEXT_PATH}js/table-thead-fixed.js"></script>
<script type="text/javascript" src="${CONTEXT_PATH}js/jquery.form.js"></script>
<script type="text/javascript">

	var locationStorageIds="";
    // 分页
    var total = "${domain.page.totalCount}";
	var pageNo = "${domain.page.pageNo}";
	var pageSize = "${domain.page.pageSize}";
	$("#pager").initPager({ total : total, pageNo : pageNo, pageSize : pageSize});

    var locationAisleSelectJson = ${domain.locationAisleSelectJson};
    $("input[name='query.locationAisle']").select2({
        data : locationAisleSelectJson,
        placeholder : "货架",
        multiple: true,
        allowClear : true
    });

    var heights = $("body").height();
    if(heights>910){
        $("#fixed-bottom").addClass("fixed-bottom-height")
    }
    
  	//全选
	function checkAll() {
		var checkBox = $("#lcoation-list").find(":checkbox");
		checkBox.prop("checked", !checkBox.prop("checked"));
	}

	// 反选
	function checkNegative() {
		var checkBox = $("#lcoation-list").find(":checkbox");
		checkBox.each(function() {
			if($(this).attr('checked')){
	            $(this).removeAttr('checked');
	        }else{
	            $(this).attr('checked','checked');
	        }
		});
	}
	
	// 获取选中的任务
	function getCheckedLocationIds() {
		var checkedLocationIds = $("input[name='locationIds']:checked");
		return checkedLocationIds;
	}
	
	//打开添加库位页面
	function addLocation(){
		$("#add_modal").modal('show');
		$("#add_modal_locationStatus").click();
	}

	$('#add_modal').on('hide.bs.modal', function () {
		window.location.reload();
	});

    function editLocation(id,location,limit,length,width,height,status) {
    	console.log(obj);
		$("#add_modal").modal('show');
		$("#add_modal .modal-title").text("编辑库位");
		if (status && status == 1){
			$("#add_modal_locationStatus").click();
		}else {
			$("#add_modal_locationStatus_close").click();
		}
		$("#add_modal input[name='query.id']").val(id);//编号
		$("#add_modal input[name='query.location']").val(location);//库位
		$("#add_modal input[name='query.location']").attr("disabled", "disabled");
		$("#add_modal input[name='query.categoryLimit']").val(limit);//库位品类上限
		$("#add_modal input[name='query.locationLength']").val(length);//库位长
		$("#add_modal input[name='query.locationWidth']").val(width);//库位宽
		$("#add_modal input[name='query.locationHeight']").val(height);//库位高
	}
	
	//添加库位方法
	function saveLocation() {
		var warehouseType = $("#warehouseType").val();//库位仓库类型
		var id = $("#add_modal input[name='query.id']").val();//编号
		var locationType = $("#add_modal_locationType_id option:selected").val();//库位类型
		var location = $("#add_modal input[name='query.location']").val();//库位
		var categoryLimit = $("#add_modal input[name='query.categoryLimit']").val();//库位品类上限
		var locationLength = $("#add_modal input[name='query.locationLength']").val();//库位长
		var locationWidth = $("#add_modal input[name='query.locationWidth']").val();//库位宽
		var locationHeight = $("#add_modal input[name='query.locationHeight']").val();//库位高
		var locationStatus = $("#add_modal input[name='query.locationStatus']:checked").val();//库位状态

		if (!warehouseType || !locationType || !location || !categoryLimit || !locationStatus) {
			layer.alert("必填数据请填写完整！");
			return false;
		}

		var checkNewLocation = new RegExp("^[A-Za-z0-9]{3,4}(-[A-Za-z0-9]{2}){2}$");
		if (!checkNewLocation.test(location)) {
			layer.alert("库位不符合库位规则！");
			return false;
		}


		var checkInteger = new RegExp("^(0|[0-9]*[1-9][0-9]*)$");
		if (!checkInteger.test(categoryLimit)) {
			layer.alert("品类上限必须为正整数！");
			return false;
		}
		if (categoryLimit <= 0) {
			layer.alert("品类上限必须为正整数！");
			return false;
		}

		//对值进行判断
		if (categoryLimit.length > 8) {
			layer.alert("品类上限太大！");
			return false;
		}

		if (locationLength != "") {
			if (!checkInteger.test(locationLength)) {
				layer.alert("库位长必须为正整数！");
				return false;
			}
			if (locationWidth == "" || locationHeight == "") {
				layer.alert("库位长宽高必须同时填写或者同时置空！");
				return false;
			}
		}
		if (locationWidth != "") {
			if (!checkInteger.test(locationWidth)) {
				layer.alert("库位宽必须为正整数！");
				return false;
			}
			if (locationLength == "" || locationHeight == "") {
				layer.alert("库位长宽高必须同时填写或者同时置空！");
				return false;
			}
		}
		if (locationHeight != "") {
			if (!checkInteger.test(locationHeight)) {
				layer.alert("库位高必须为正整数！");
				return false;
			}
			if (locationWidth == "" || locationLength == "") {
				layer.alert("库位长宽高必须同时填写或者同时置空！");
				return false;
			}
		}

		//对值进行判断
		if (locationHeight.length > 8 || locationWidth.length > 8 || locationLength.length > 8) {
			layer.alert("库位长宽高数值太大！");
			return false;
		}

		var handleType = id == null || id == '' ? 'ADD' : 'UPDATE';

		$.post(CONTEXT_PATH + "whExLocation/editLocation?type="+handleType, {
			"whExLocation.warehouseType": warehouseType,
			"whExLocation.locationType": locationType,
			"whExLocation.location": location.toUpperCase(),
			"whExLocation.categoryLimit": categoryLimit,
			"whExLocation.locationLength": locationLength,
			"whExLocation.locationWidth": locationWidth,
			"whExLocation.locationHeight": locationHeight,
			"whExLocation.locationStatus": locationStatus
		}, function (data) {
			if (data.status == 200) {
				layer.alert(data.message, function (index) {
					$("#add_modal").modal('hide');
					layer.close(index);
					window.location.reload();

				});
			} else {
				layer.alert(data.message);
			}
		});
	}

	function batchModifyLocation(type) {
		var checkedDatas = getCheckedLocationIds();
		if (checkedDatas.length == 0) {
			layer.alert("请勾选要操作的库位", "error");
			return false;
		}

		var typeText = "";
		if (type == 1) {
			typeText = "开启";
		} else if (type == 2) {
			typeText = "关闭";
		} else {
			typeText = "删除";
		}

		var r = confirm("确定" + typeText + "库位？");

		if (r) {
			var locationIds = checkedDatas.serialize();
			$.post(CONTEXT_PATH + "whExLocation/batchModifyLocation?" + locationIds + "&type=" + type, null, function (data) {
				layer.alert(data.message, function (index) {
					layer.close(index);
					window.location.reload();
				});
			});
		}

	}

	function downloadLocation(){
		var checkedDatas = getCheckedLocationIds();

		var diglog = dialog({
			title: '导出',
			width: 350,
			height:100,
			url: CONTEXT_PATH + "warehouse/locations/downloadmode",
			okValue: '确定',
			ok: function () {

				var exportWindow = $(this.iframeNode.contentWindow.document.body);

				var submitForm = exportWindow.find("#submit-form");

				var exportType = submitForm.find("input[name='exportType']:checked").val();


				var submitFormParam = $('#domain').serialize();
				// 导出当前选择
				if (exportType == 3) {
					if (checkedDatas.length == 0) {
						layer.alert("请选择要操作的数据!");
						return false;
					}
					var locationIdStr = "";
					for (var i = 0; i < checkedDatas.length; i++) {
						var checkedData = checkedDatas[i];
						var locationId = $(checkedData).val();
						locationIdStr += locationId;
						if (i != checkedDatas.length - 1) {
							locationIdStr += ",";
						}
					}
					submitFormParam = submitFormParam + "&query.locationIdStr=" + locationIdStr;
				}

				submitFormParam = submitFormParam + "&exportType=" + exportType;
				//还原分页
				$("#page-no").val("${domain.page.pageNo}");

				downloadByPostForm(submitFormParam, CONTEXT_PATH + "whExLocation/download?");

				$("#page-no").val("1");

				setTimeout(function () {
					diglog.close().remove();
				}, 100);

				return true;
			},
			cancelValue: '取消',
			cancel: function () {}
		});
		diglog.show();
	}

</script>
</body>
</html>