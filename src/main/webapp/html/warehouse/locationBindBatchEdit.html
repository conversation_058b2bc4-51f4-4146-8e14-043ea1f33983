<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">
<html>
<head>
<title>易世通达仓库管理系统</title>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
	<#include "/common/include.html">
	<style type="text/css">
	
.modal-body input {
	width: 240px;
}

.xx {
	width: 70px;
}

.top_bar{
    position:fixed;top:0px;
}
</style>
</head>
<body>
<@header method="header" active="15000000"><#include "/ftl/header.ftl"></@header>

	<div id="page" class="container-fluid">
		<!-- BEGIN PAGE HEADER-->
		<div class="row">
			<div class="col-md-12">
				<!-- BEGIN PAGE TITLE & BREADCRUMB-->
				<h2>库位</h2>
				<ul class="page-breadcrumb breadcrumb">
					<li><a href="#">库位</a> <i class="icon-angle-right"></i></li>
					<li><a href="#">库位绑定</a> <i class="icon-angle-right"></i></li>
				</ul>
				<!-- END PAGE TITLE & BREADCRUMB-->
			</div>
		</div>
		<#assign query=domain.query>
		<!-- END PAGE HEADER-->
		<div class="row">
			<div class="portlet-body form" style="display: block;">
				
			</div>
			<br />
			<div class="row">
				<div class="col-md-12">
				
					<div class="table-toolbar">
						<h1>
							<#if domain.query.warehouse == 2>
								新仓
							<#else>
								旧仓
							</#if>
						</h1>
						<div class="btn-group">
							<button type="button" class="btn btn-sm green" onclick="addBind(${domain.query.warehouse},${domain.query.roleId});">
								<i class="icon-plus"></i> 添加库位号绑定 （${domain.role.roleName }）
							</button>
						</div>
					</div>	
				
					<form class="form-horizontal" role="form" id="warehouse-form" action="${CONTEXT_PATH}location/nubmers/bind/batch/update" method="post">
	
						
						<div class="form-body">
							<table class="table table-bordered table-condensed table-hover" >
								<colgroup>
									<col width="150">
									<col>
								</colgroup>
								
								<#assign begin="0">
								<#assign maxCount="6">
								
								<thead>
									<tr>
										<th>${domain.role.roleName }</th>
										<#list >
											<#assign begin=begin+1/>
											<#if x \g 6>
												<#break>
											<#else>
												<th></th>
											</#if>
										</#list>
										<th>操作</th>
									</tr>
								</thead>
						
								<tbody>
									<#list domain.locationNumberBinds as locationNumberBind>
										<tr id="tr-bind-${locationNumberBind_index}">
											<input type="hidden" name="locationNumberBinds[${locationNumberBind_index}].roleId" value="${locationNumberBind.roleId }">
											<input type="hidden" name="locationNumberBinds[${locationNumberBind_index}].numberId" value="${locationNumberBind.numberId }">
											<input type="hidden" name="locationNumberBinds[${locationNumberBind_index}].warehouse" value="${locationNumberBind.warehouse }">
											
											<td class="form-label">
												<select class="form-control user-id" name="locationNumberBinds[${locationNumberBind_index}].packingPerson" onchange="changeBindPerson(this,${locationNumberBind_index})" id="user-${locationNumberBind_index}">
													<option value="">请选择</option>
													<#list domain.saleUsers as saleUser>
														<#if saleUser.username == locationNumberBind.packingPerson>
															<option value="${saleUser.username}" selected="selected">${saleUser.username}-${saleUser.name }</option>
														<#else>
															<option value="${saleUser.username}">${saleUser.username}-${saleUser.name }</option>
														</#if>
													</#list>
												</select>
												<input value="${locationNumberBind.packingPerson }"  id="befor-user-${locationNumberBind_index}" type="hidden"/>
											</td>
											
											<#assign begin="0">
											<#assign maxCount="6">
											
											<#list >
												<#assign begin=begin+1/>
												<#if x \g 6>
													<#break>
												<#else>
													<td>
														<span class="control-inline"></span>
														<input type="text" style="width: 70px;" class="form-control input-inline" placeholder="" name="locationNumberBinds[${locationNumberBind_index}].matchingRuleList[${begin }].locationNumberStart" value="${locationNumberBind.matchingRuleList[begin].locationNumberStart}"  />
														
														
														<#if (locationNumberBind.matchingRuleList[begin].locationNumberStart)! && locationNumberBind.matchingRuleList[begin].locationNumberStart == locationNumberBind.matchingRuleList[begin].locationNumberEnd>
														<#else>
															<span class="control-inline"> - </span>
															<input type="text" style="width: 70px;"  class="form-control input-inline" placeholder="" name="locationNumberBinds[${locationNumberBind_index}].matchingRuleList[${begin}].locationNumberEnd" value="${locationNumberBind.matchingRuleList[begin].locationNumberEnd}" />
														</#if>
													</td>
												</#if>
											</#list>
											
											<td>
												<a class="btn btn-link" href="javascript:void(0)" onclick="deleteBind(${locationNumberBind_index})">
													删除
												</a>
											</td>
										</tr>
									</#list>
								</tbody>	
							</table>

							<div class="form-actions fluid">
								<div class="col-md-offset-3 col-md-9">
									<button type="submit" class="btn green">
										<i class="icon-save"></i> 提交
									</button>
									<a type="button" class="btn btn-default" href='${CONTEXT_PATH}location/nubmers/bind'>
										<i class="m-icon-swapleft"></i> 返回
									</a>
								</div>
							</div>
							
						</div>	
					</form>

				</div>
			</div>
		</div>

	</div>
	<#include "/common/footer.html">
	<script type="text/javascript" src="${CONTEXT_PATH}js/datepicker/WdatePicker.js"></script>
	
	<script type="text/javascript">
	
	
		jQuery(document).ready(function() {
			
			init();

			bindSelect2();
		});
		
		function init(){
			var submitForm = $('#warehouse-form');
			submitForm.validate({
				submitHandler : function(form) {
					var isValidate = true;
					$(form).find('select').each(function(){
						if($(this).val() ==''){
							$(this).after('<span style="color:red;">该项为必填项!</span>');
							isValidate = false;
						}
					});
					if(!isValidate)return;
					App.blockUI();

					form.submit();
				}
			});
		}
		
		// 绑定select2
		function bindSelect2() {
			// 快速编辑
			$("#warehouse-form").find(".user-id").select2({
				allowClear : true
			});
		}
		
		
		function changeBindPerson(obj, index){
			
			
			var befor = $("#befor-user-" + index).val();
			
			if(befor != '' && !confirm("是否更换库维员?")) {
				//还原
				$("#user-" + index).val(befor).select2();
			}else{
				$("#befor-user-" + index).val($(obj).val());
			}
		}
		
		function addBind(warehouse,roleId) {
			// 局部刷新
			$.render({
				type : 'post',  
				url : CONTEXT_PATH + "location/nubmers/bind/add/bind?warehouse=" + warehouse + "&roleId=" + roleId,
				// 提交区域
				regoinid : "warehouse-form",
				// 刷新区域
				targetid : "warehouse-form",
				onAfter : function() {
					window.scrollTo(0, document.body.scrollHeight);
					
					bindSelect2();
					
					init();
				}
			});
		}

		function deleteBind(index) {
			
			
			if(confirm("确定删除?")) {
				$("#tr-bind-" + index).remove();
				
				// 重新编排
				$("#warehouse-form").find("tbody tr").each(function(i){
					setIndex(this, 0, i);
				});
			}
		}
		
	</script>
</body>
</html>