<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">
<html>
<head>
    <title>易世通达仓库管理系统</title>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <#include "/common/include.html">
    <style type="text/css">
        .modal-body input {
            width: 240px;
        }
        .top_bar{
            position:fixed;top:0px;
        }
        #task-list td {
            vertical-align:middle;
        }
    </style>
</head>
<body>
<@header method="header" active="13010000"><#include "/ftl/header.ftl"></@header>

<div id="page" style="background-color: rgb(231, 237, 248)">
    <div class="row">
        <div class="col-md-12" style="padding: 0">
            <ul class="page-breadcrumb breadcrumb" style="background-color: white;">
                <li><a href="#">仓库报表</a></li>
                <li class="active">
                    <a href="${CONTEXT_PATH}collectCode/shopeeCollectCode">虾皮揽收码</a>
                </li>
                <li class="active">揽收码绑定详情</li>
            </ul>
        </div>
    </div>
    <!-- 内容 -->
    <div class="container-fluid" style="background-color: white;border: none">
        <#assign query=domain.query>
        <!-- END PAGE HEADER-->
        <div class="row">
            </br>
        </div>
        <div class="row">
            <div class="col-md-12">

                <table>
                    <tr>
                        <td>揽收码 :</td>
                        <td style="width:200px;">${domain.collectCode}</td>
                    </tr>
                </table>

                <div class="row">
                    <div class="col-md-12">
                        <form action="${CONTEXT_PATH}collectCode/shopeeCollectCode/details?id=${domain.shopeeCollectCodeId}"
                              class="form-horizontal form-bordered form-row-stripped" enctype="multipart/form-data"
                              method="post" modelAttribute="domain" name="shopeeCollectCodeItemListForm" id="domain">

                            <!-- 分页信息 -->
                            <input id="page-no" type="hidden" name="page.pageNo" value="1">
                            <input id="page-size" type="hidden" name="page.pageSize" value="${domain.page.pageSize}">
                            <input type="hidden" name="query.type" value="3">
                            <input type="hidden" name="query.shopeeCollectCodeId" value="${domain.shopeeCollectCodeId}">
                            <div class="form-body">
                                <div class="form-group">

                                    <label class="control-label col-md-1">发货单号</label>
                                    <div class="col-md-3">
                                        <input class="form-control" name="query.deliverNo" type="text" value="${domain.query.deliverNo}">
                                    </div>

                                    <label class="control-label col-md-1">绑定状态</label>
                                    <div class="col-md-3">
                                        <select name="query.bindingStatus" class="form-control" value="${domain.query.bindingStatus}">
                                            <option vlaue=""></option>
                                            <option value="1" <#if domain.query.bindingStatus == 1>selected</#if>>成功</option>
                                            <option value="2" <#if domain.query.bindingStatus == 2>selected</#if>>失败</option>
                                            <option value="0" <#if domain.query.bindingStatus == 0>selected</#if>>未同步</option>
                                        </select>
                                    </div>
                                </div>
                            </div>

                            <div>

                                <div class="col-md-offset-10" style="text-align: right;margin-right: 10px">
                                    <button type="button" class="btn btn-default" onclick="formReset(this)">
                                        <i class="icon-refresh"></i> 重置
                                    </button>
                                    <button type="submit" class="btn blue">
                                        <i class="icon-search"></i> 查询
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>
                    <br/>
                </div>

                <table class="table table-striped table-bordered table-hover table-condensed" id="task-list">
                    <thead>
                    <tr>
                        <th>发货单号</th>
                        <th>订单号</th>
                        <th>绑定结果</th>
                        <th>同步时间</th>
                    </tr>
                    </thead>
                    <tbody>
                    <#list domain.shopeeCollectCodeItems as shopeeCollectCodeItem>
                    <tr>
                        <td>${shopeeCollectCodeItem.deliverNo}</td>
                        <td>${shopeeCollectCodeItem.orderNo}</td>
                        <td>${util('enumName','com.estone.collectCode.enums.BindingStatus',shopeeCollectCodeItem.bindingResult+'')}</td>
                        <td>${shopeeCollectCodeItem.synTime}</td>
                    </tr>
                    </#list>
                    </tbody>
                </table>

                <div id="fixed-bottom">
                    <div id="pager"></div>
                </div>
            </div>
        </div>
    </div>

    <#include "/common/footer.html">
</div>
<script type="text/javascript" src="${CONTEXT_PATH}js/datepicker/WdatePicker.js"></script>
<script type="text/javascript">
    // 分页
    var total = "${domain.page.totalCount}";
    var pageNo = "${domain.page.pageNo}";
    var pageSize = "${domain.page.pageSize}";
    $("#pager").initPager({ total : total, pageNo : pageNo, pageSize : pageSize});

    var heights = $("body").height();
    if(heights>910){
        $("#fixed-bottom").addClass("fixed-bottom-height")
    }

    function reloadPage(){
        location.reload()
    }
</script>
</body>
</html>