<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">
<html>
<head>
	<title>易世通达仓库管理系统</title>
	<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
	<#include "/common/include.html">
	<style type="text/css">
		.modal-body input {
			width: 240px;
		}
		.top_bar{
			position:fixed;top:0px;
		}
		#task-list td {
			vertical-align:middle;
		}
	</style>
</head>
<body>
<@header method="header" active="11060000"><#include "/ftl/header.ftl"></@header>

<div id="page" style="background-color: rgb(231, 237, 248)">
	<div class="row">
		<div class="col-md-12" style="padding: 0">
			<ul class="page-breadcrumb breadcrumb" style="background-color: white;">
				<li><a href="#">调拨拣货任务</a>
				<li class="active">调拨拣货任务详情列表</li>
			</ul>
		</div>
	</div>

	<div class="container-fluid" style="background-color: white;border: none">
	<h2 class="header-title">拣货任务详情列表</h2>
	<div class="row">
		<div class="col-md-12">
			<table class="table table-striped table-bordered table-hover table-condensed" id="task-list">
				<colgroup>
					<col width="10%" />
					<col width="10%" />
					<col width="20%" />
					<col width="30%" />
					<col width="5%" />
					<col width="5%" />
					<col width="5%" />
					<col width="15%" />
				</colgroup>
				<thead>
					<tr>
						<th>调拨拣货任务号</th>
						<th>调拨单号</th>
						<th>sku</th>
						<th>名称</th>
						<th>需拣数量</th>
						<th>已拣数量</th>
						<th>差异数量</th>
						<th>货位</th>
					</tr>
				</thead>
				<tbody>
					<#list domain.whAllocationPickTaskItems as pickingTaskItem>
						<tr>
							<td>${pickingTaskItem.whAllocationPickTask.taskNo }</td>
							<td>${pickingTaskItem.whAllocationPickTask.allocationNo }</td>
							<td>${pickingTaskItem.sku }（${util('wh',pickingTaskItem.whSku.warehouseId)}）</td>
							<td>${pickingTaskItem.whSku.name }</td>
							<td>${pickingTaskItem.quantity }</td>
							<td>${pickingTaskItem.pickQuantity }</td>
							<td>${pickingTaskItem.pickQuantity - pickingTaskItem.quantity}</td>
							<td>${pickingTaskItem.whSku.locationNumber }</td>
						</tr>
					</#list>
				</tbody>
			</table>
		</div>
	</div>
</div>
<div id="fixed-bottom">
	<div id="pager"></div>
</div>
<#include "/common/footer.html">
</div>
<script type="text/javascript" src="${CONTEXT_PATH}js/datepicker/WdatePicker.js"></script>
<script type="text/javascript">
    var heights = $("body").height();
    if(heights>910){
        $("#fixed-bottom").addClass("fixed-bottom-height")
    }
</script>
</body>
</html>