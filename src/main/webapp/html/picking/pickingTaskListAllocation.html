<!DOCTYPE html>
<html lang="en" class="no-js">
<head>
	<meta charset="utf-8" />
	<title>ERP</title>
	<#include "/common/include.html">
	<style type="text/css">

	</style>

</head>

<body>
<!-- BEGIN CONTAINER -->
<div class="container-fluid" style="min-width: 280px;">

		<!--<div class="col-md-12">-->
			<form class="form-horizontal" id="submit-form" name="submitForm" action="" method="post">
						<!--<label class="control-label col-md-1">拣货人员：</label>-->
						<!--<div class="col-md-3">-->
							<input id ="pickingUser" class="form-control" name="query.pickingUser" type="text" value="${query.pickingUser}">
						<!--</div>-->
			</form>
		<!--</div>-->
		<!-- end col -->

	<!-- end row -->
</div>
<!-- END CONTAINER -->
<script type="text/javascript">

	// 领取人
	$.getJSON(CONTEXT_PATH + "system/saleusers/allUser", function(json) {
		if(json) {
			$("input[name='query.pickingUser']").select2({
				data: json,
				placeholder: "拣货人员",
				allowClear: true
			});
		} else {
			$("input[name='query.pickingUser']").attr("placeholder", "没有可拣货人员!").attr("readonly", true);
		}
	});
</script>

<!-- END JAVASCRIPTS -->
</body>
<!-- END BODY -->
</html>