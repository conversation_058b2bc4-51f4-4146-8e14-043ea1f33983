<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">
<html>
<head>
    <title>易世通达仓库管理系统</title>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <#include "/common/include.html">
    <style type="text/css">
        .table tbody>tr>th,td{
            text-align: center;
            vertical-align: middle !important;
        }
        .page-info{
            background-color: #f2f2f2;
        }
        .page-table{
            background-color: white;
        }
        .content-title{
            margin-bottom: 60px;
            width: 60%;
        }
        .content-title label{
            text-align: right;
            margin-top: 0px !important;
        }
        .readonly-row{
            padding: 20px 20px 0px 20px;
        }
    </style>
</head>
<body>
<@header method="header" active="11030000"><#include "/ftl/header.ftl"></@header>

<div id="page" style="background-color: rgb(231, 237, 248)">
    <div class="row">
        <div class="col-md-12" style="padding: 0">
            <ul class="page-breadcrumb breadcrumb" style="background-color: white;">
                <li><a href="#">库位管理</a></li>
                <li><a href="${CONTEXT_PATH}location/check">库位校验任务</a></li>
                <li class="active">库位校验任务详情</li>
            </ul>
        </div>
    </div>

    <!-- 内容 -->
    <div class="container-fluid" style="background-color: white;border: none">
        <div class="row">
            <div class="row readonly-row">
                <div class="col-md-12 page-info">
                    <div class="form-body">
                        <#assign task = domain.locationCheckTask>
                        <#if task.id?? && (task.itemList)??>
                            <div class="content-title">
                                <h4><strong>校验任务编号${task.taskNo}</strong></h4>
                                <table class="table table-condensed table-bordered table-striped page-table">
                                    <thead>
                                    <tr>
                                        <th>库位</th>
                                        <th>SKU</th>
                                        <th>校验时库位库存</th>
                                        <th>校验结果</th>
                                        <th>校验时间</th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    <#list task.itemList as item >
                                        <tr>
                                            <td>${item.locationNumber}</td>
                                            <td>${item.sku}</td>
                                            <td>${item.quantity}</td>
                                            <td>${util('enumName',"com.estone.picking.enums.LocationCheckItemStatus", item.status)}</td>
                                            <td>${item.checkDate}</td>
                                        </tr>
                                    </#list>
                                    </tbody>
                                </table>
                            </div>
                        </#if>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <#include "/common/footer.html">
</body>
</html>