<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">
<html>
<head>
<title>易世通达仓库管理系统</title>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
	<#include "/common/include.html">
	<style type="text/css">
	
.modal-body input {
	width: 240px;
}
.top_bar{
    position:fixed;top:0px;
}

.userlist {max-height:120px;overflow-y:auto;}

</style>
</head>
<!-- END HEAD -->
<!-- BEGIN BODY -->
<body class="">
	
	<!-- BEGIN CONTAINER -->
	<div class="container-fluid">

		<!-- BEGIN PAGE -->
		<div class="">
			<!-- BEGIN PAGE CONTENT-->
			<div class="row">
				<div class="col-md-8">
					<form id="submit-form" action="${CONTEXT_PATH}system/menus/createAuth" class="form-horizontal form-bordered form-row-stripped" method="post" modelAttribute="domain" name="createAuthForm">
						<div class="form-body">
							<input type="hidden" name="menu.menuId" value="${domain.menu.menuId}"/>
							<div class="form-group">
								<label class="control-label col-sm-4">按钮名称：<span class="required" aria-required="true">*</span>:</label>
								<div class="col-sm-8">
									<input class="form-control input-medium" placeholder="按钮名称" name="menu.menuName" type="text" value="${domain.menu.menuName}"/>
								</div>
							</div>
							<div class="form-group">
								<label class="control-label col-sm-4">菜单code：<span class="required" aria-required="true">*</span>:</label>
								<div class="col-sm-8">
									<input class="form-control input-medium" placeholder="格式：模块名称_按钮名称" name="menu.menuCode" type="text" value="${domain.menu.menuCode}"/>
								</div>
							</div>
							<div class="form-group">
								<label class="control-label col-sm-4">parent_code：<span class="required" aria-required="true">*</span>:</label>
								<div class="col-sm-8">
									<input class="form-control input-medium" placeholder="parentId" name="menu.parentId" type="text" value="${domain.menu.parentId}" readonly="readonly"/>
								</div>
							</div>
							<div class="form-group">
								<label class="control-label col-sm-4">url：</label>
								<div class="col-sm-8">
									<input class="form-control input-medium" placeholder="url" name="menu.url" type="text" value="${domain.menu.url}"/>
								</div>
							</div>
						</div>
					</form>
				</div>
				<br />
			</div>
			<!-- END PAGE CONTENT-->
		</div>
		<!-- END PAGE -->
	</div>
	<!-- END CONTAINER -->


	<script type="text/javascript">
		jQuery(document).ready(function() {
			var submitForm = $('#submit-form');
			submitForm.validate({
				rules: {
	                "menu.menuName": {
	                	required: true
	                },
	                "menu.menuCode": {
	                	required: true
	                },
	                "menu.parentId": {
	                	required: true
	                },
	                "menu.url": {
	                	required: true
	                }
	            },
	            messages: {
	                "menu.menuName": {
	                	required: "请输入权限名称"
	                },
	                "menu.menuCode": {
	                	required: "请输入权限code"
	                },
	                "menu.parentId": {
	                	required: "parent_id is null"
	                },
	                "menu.url": {
	                	required: "请输入权限url"
	                }
	            },
	            submitHandler: function (form) {
					App.blockUI();
	            	form.submit();
	            	return false;
	            }
	        });
		});
		
		var menuId = $('input[name="menu.menuId"]').val();
		if (menuId != null && menuId != '' && menuId != undefined) {
			$('input[name="menu.menuCode"]').attr("readonly","readonly");
		}
	</script>
	<!-- END JAVASCRIPTS -->
</body>
<!-- END BODY -->
</html>