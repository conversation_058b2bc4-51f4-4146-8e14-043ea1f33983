<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">
<html>
<head>
	<title>易世通达仓库管理系统</title>
	<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
	<#include "/common/include.html">
	<style type="text/css">
		.modal-body input {
			width: 240px;
		}
		.top_bar{
			position:fixed;top:0px;
		}
		#task-list td {
			vertical-align:middle;
		}
	</style>
</head>
<body>
<@header method="header" active="12030000" ><#include "/ftl/header.ftl"></@header>

<div id="page" style="background-color: rgb(231, 237, 248)">
	<div class="row">
		<div class="col-md-12" style="padding: 0">
			<ul class="page-breadcrumb breadcrumb" style="background-color: white;">
				<li><a href="#">核对拣货数量</a></li>
			</ul>
		</div>
	</div>

	<!-- 内容 -->
	<div class="container-fluid" style="background-color: white;border: none">

	<!-- END PAGE HEADER-->
	<div class="row">
		<div class="col-md-12">
			<div class="form-body">
				<div class="form-group">
					<label class="control-label col-md-1">拣货任务号</label>
					<div class="col-md-3">
						<input class="form-control" type="text" name="taskNo" placeholder="请输入任务号" value="" onkeypress="if(event.keyCode==13) { inputnext(this); return false;}" id="task-taskNo"/>
					</div>
					
					<div style="display: none;" id="taskType1">
						<label class="control-label col-md-1">SKU</label>
						<div class="col-md-3">
							<input class="form-control" type="text" name="sku" placeholder="请输入SKU" value="" id="task-sku" onkeypress="if(event.keyCode==13) { inputsku(this); return false;}" />
						</div>
						
						<label class="control-label col-md-1">数量</label>
						<div class="col-md-3">
							<input class="form-control" type="text" name="checkQuantity" placeholder="请输入数量" value="" id="task-checkQuantity"/>
						</div>
						<div style="margin-top: 20px;float: right;margin-bottom: 20px;">
							<button onclick="update()" type="button" class="btn blue">
								<i class="icon-search"></i> 提交
							</button>
						</div>
					</div>
					
					<div style="display: none;" id="taskType2">
						<label class="control-label col-md-1">周转框</label>
						<div class="col-md-3">
							<input class="form-control" type="text" name="boxNo" placeholder="请输入周转框" value="" id="task-boxNo"/>
						</div>
						<button onclick="update()" type="button" class="btn blue">
							<i class="icon-search"></i> 提交
						</button>
					</div>
					
					<div style="display: none;" id="taskType3">
						<button onclick="update()" type="button" class="btn blue">
							<i class="icon-search"></i> 提交
						</button>
					</div>
				</div>
			</div>
		</div>
		<br/>
	</div>
		
	<div class="row" style="display: none;" id="taskType4">
	<div class="col-md-12">
		<table class="table table-striped table-bordered table-hover table-condensed" id="task-list">
				<colgroup>
					<col width="10%" />
					<col width="10%" />
					<col width="10%" />
				</colgroup>
				<thead>
					<tr>
						<th>发货单数量</th>
						<th>应拣数量</th>
						<th>已拣数量</th>
					</tr>
				</thead>
				<tbody>
					<tr>
						<td id="orderQuantity"></td>
						<td id="quantity"></td>
						<td id="checkQuantity"></td>
					</tr>
				</tbody>
			</table>
		</div>
	</div>	
</div>
<#include "/common/footer.html">
</div>
<script type="text/javascript">    

$(document).ready(function(){
	input_init();
});
// 初始化
function input_init(){
	$('#task-taskNo').val('');
	$('#task-taskNo').focus();
	$("#task-sku").val('');
	$("#task-boxNo").val('');
	$("#task-checkQuantity").val('');
}

function inputnext(obj) {
	if(!obj.value || obj.value.replace(/\s/g,'') == ''){
		layer.alert("请扫描有效的任务号!");
		return ;
	}
	
	var taskNo = obj.value.replace(/\s/g,'');
	$.get(CONTEXT_PATH + "picking/checks/queryPickingQuantity?taskNo="+taskNo, function(data){
		if (data.status == 200) {
			$("#orderQuantity").text("");
			$("#quantity").text("");
			$("#checkQuantity").text("");
			var whPickingTask = JSON.parse(data.message);
			var taskType = whPickingTask.taskType;
			if (taskType == 5 || taskType == 6 ) {
				$("#taskType1").css("display", "block");
				$("#task-sku").select().focus();
				$("#task-sku").attr("title", whPickingTask.sku);
				$("#task-sku").attr("taskSkuType", whPickingTask.taskType);
				$("#taskType2").css("display", "none");
				$("#taskType3").css("display", "none");
				$("#taskType4").css("display", "block");
			} else{
				$("#taskType1").css("display", "none");
				$("#taskType2").css("display", "none");
				$("#taskType3").css("display", "none");
				$("#taskType4").css("display", "none");
                layer.alert("该任务类型不允许操作!");
                input_init();
                return false;
			}
			$("#orderQuantity").text(whPickingTask.orderQuantity);
			$("#quantity").text(whPickingTask.quantity);
			$("#checkQuantity").text(whPickingTask.checkQuantity);
		} else {
			customizeLayer(data.message);
		}
	});
}

function inputsku(obj) {
	if(!obj.value || obj.value.replace(/\s/g,'') == ''){
		layer.alert("请扫描有效的任务号!");
		return ;
	}
	
	var sku = obj.value.replace(/\s/g,'');
	
	if(!(sku.indexOf("=") == -1)) {
		var realSku = sku.split('=')[0];
		$("#task-sku").val(realSku);
		sku = realSku;
	}
	
	if($(obj).attr("title") == sku){
		layer.alert("SKU扫描成功！");
		$("#task-checkQuantity").select().focus();
	} else {
		layer.alert("SKU扫描失败！[" + sku + "]", "error");
		$(obj).val("");
	}
}

function update() {
	var taskNo = $('#task-taskNo').val();
	
	var sku = $('#task-sku').val().replace(/\s/g,'');
	
	var boxNo = $('#task-boxNo').val();
	
	var checkQuantity = $("#task-checkQuantity").val();
	
	if(checkQuantity != null && checkQuantity != ""){
		var re = /^[0-9]+.?[0-9]*$/;
	　　	if (!re.test(checkQuantity)) {
	　　		layer.alert("请输入数字！");
	　　　	document.getElementById(checkQuantity).value = "";
	　　　	return false;
	　　	}
	}
	
	var taskSku = $("#task-sku").attr("title");
	var taskSkuType = $("#task-sku").attr("taskSkuType");
	if ((taskSkuType == 5 || taskSkuType == 6) && sku != taskSku) {
		layer.alert("输入的sku与该拣货任务的sku不一致！");
		return false;
	}
	
	$.get(CONTEXT_PATH + "picking/checks/checkPickingQuantity?taskNo="+taskNo+"&checkQuantity="+checkQuantity+"&sku="+sku+"&boxNo="+boxNo, function(data){
		if (data.status == 200) {
			$("#orderQuantity").text("");
			$("#quantity").text("");
			$("#checkQuantity").text("");
			input_init();
			layer.alert(data.message);
		} else {
			customizeLayer(data.message);
		}
	});
}

</script>
</body>
</html>