<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">
<html>
    <head>
        <meta content="text/html; charset=utf-8" http-equiv="Content-type">
        <title>Print</title>
        <style>
            @media print {
                .printbtn {
                    display: none;
                }
            }

            .fullscreen {
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                border: none;
            }
        </style>
    </head>

    <body style="padding: 0px; margin: 20px">

        <!--<div class="printbtn">
            <button onclick="myPreview();">打印预览</button> &nbsp;
            <button onclick="myPrint();">打印</button>  &nbsp;
            <button onclick="myPrintDesign();">打印设计</button>
        </div>-->
        <form id="print_content">
            <div id="print-item-0">
                <#if (domain.errorMsg)??>
                    <h1 style="color:red;font-size:48px;">${domain.errorMsg}</h1>
                <#else>
                    <iframe src="${domain.printUrl}" class="fullscreen" />
                </#if>
            </div>
        </form>

        <#--<object width="0" height="0" classid="clsid:2105C259-1E0C-4534-8141-A753534CB4CA" id="LODOP_OB">
            <embed width="0" height="0" type="application/x-print-lodop" id="LODOP_EM">
        </object>

        <!-- 打印插件 &ndash;&gt;
        <script type="text/javascript" src="${CONTEXT_PATH}js/LodopFuncs.js"></script>
        <script src="${CONTEXT_PATH }js/assets/plugins/jquery-1.10.2.min.js" type="text/javascript" ></script>
        <script language="javascript">
            var LODOP; //声明为全局变量
            function CheckIsInstall() {
                try {
                    var LODOP = getLodop(document.getElementById('LODOP_OB'),
                        document.getElementById('LODOP_EM'));
                    if ((LODOP != null) && (typeof (LODOP.VERSION) != "undefined"))
                        return LODOP.VERSION;
                } catch (err) {
                    //alert("Error:本机未安装或需要升级!");
                }
                return false;
            }
            function myPrint() {
                CreatePrintPage();
                LODOP.PRINT();
            };
            function myPreview() {
                CreatePrintPage();
                LODOP.PREVIEW();
            };
            function myPrintDesign() {
                CreatePrintPage();
                LODOP.PRINT_DESIGN();
            };
            function CreatePrintPage() {
                LODOP = getLodop(document.getElementById('LODOP_OB'), document
                    .getElementById('LODOP_EM'));
                LODOP.PRINT_INIT("打印");

                try {
                    if (typeof (eval(CreatePrintPageWithImage)) == 'function') {
                        return CreatePrintPageWithImage();
                    }
                } catch (e) {
                }

                LODOP.ADD_PRINT_HTM(0, 0, "210mm", "297mm", document
                    .getElementById('print_content').innerHTML);
            };

        </script>-->
    </body>
</html>