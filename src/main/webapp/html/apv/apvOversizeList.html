<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">
<html>
<head>
    <title>易世通达仓库管理系统</title>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <link href="${CONTEXT_PATH}js/assets/plugins/jquery-file-upload/css/jquery.fileupload-ui.css" rel="stylesheet" type="text/css"/>
    <#include "/common/include.html">
        <style type="text/css">
            #editVolumeAndWeightModal .modal-body input {
                width: 100%;
            }
            .control-label {
                margin-top: 2px;
            }
            .form-bordered .control-label {
                padding-top: 14px;
            }
            .form-horizontal .control-label {
                text-align: right;
            }
            .col-md-1 {
                padding-left: 10px;
                padding-right: 0px;
                width: 5%;
                font-size: 12px;
            }
            .col-md-2 {
                padding-left: 10px;
                padding-right: 0px;
                width: 7.5%;
                font-size: 12px;
            }
            .form-control {
                height: 30px;
                padding: 0px 4px;
                border: 1px solid #ddd;
                color: #000;
                font-size: 10px;
                font-weight: normal;
            }
            .form-bordered .form-group > div {
                padding: 4px 10px 0;
            }
            /*启用table滚动条*/
            .my-div-table{
                overflow-x: auto;
                overflow-y: auto;
                height: 654px;
                width: auto;
                padding-bottom: 38px;
                /*width:1920px;*/
            }
            /*禁用body滚动条*/
            body{
                overflow-x: hidden;
                overflow-y: hidden;
            }
            #box_info_table tr th{
                width:160px;
            }
            .form-control2 {
                height: 35px;
                width: 30px;
                padding: 0px 4px;
                border: 1px solid #ddd;
                color: #000;
                font-size: 16px;
                font-weight: normal;
            }
        </style>
</head>
<body>
<@header method="header" active="12010000"><#include "/ftl/header.ftl"></@header>

<div id="page" style="background-color: rgb(231, 237, 248)">
    <div class="row">
        <div class="col-md-12" style="padding: 0">
            <ul class="page-breadcrumb breadcrumb" style="background-color: white;">
                <li><a href="#">出库管理</a></li>
                <li class="active">超体积发货管理</li>
            </ul>
        </div>
    </div>

    <!-- 内容 -->
    <div class="container-fluid" style="background-color: white;border: none">
        <div class="row">
            <div class="col-md-12">
                <#assign query = domain.query>
                    <form action="${CONTEXT_PATH}apvOversize/search" class="form-horizontal form-bordered form-row-stripped"
                          method="post" modelAttribute="domain" name="apvOversizeForm" id="domain">
                        <!-- 分页信息 -->
                        <input id="page-no" type="hidden" style="display:none" name="page.pageNo" value="1">
                        <input id="page-size" type="hidden" name="page.pageSize" value="${domain.page.pageSize}">

                        <div class="form-body">
                            <div class="form-group">
                                <label class="control-label col-md-1">发货单号：</label>
                                <div class="col-md-2">
                                    <input class="form-control" type="text" name="query.apvNo" placeholder="多个查询逗号分开" value="${query.apvNo}">
                                </div>

                                <label class="control-label col-md-1">DD号：</label>
                                <div class="col-md-2">
                                    <input class="form-control" type="text" name="query.salesRecordNumbers" placeholder="多个查询逗号分开" value="${query.salesRecordNumbers }">
                                </div>

                                <label class="control-label col-md-1">追踪单号：</label>
                                <div class="col-md-2">
                                    <input class="form-control" type="text" name="query.trackingNumbers" placeholder="多个查询逗号分开" value="${query.trackingNumbers }">
                                </div>

                                <label class="control-label col-md-1">物流方式：</label>
                                <div class="col-md-2">
                                    <input class="form-control" name="query.logisticsCompany" type="text" value="${query.logisticsCompany}">
                                </div>

                                <label class="control-label col-md-1">是否量体积：</label>
                                <div class="col-md-2">
                                    <select name="query.oversizeVolume" class="form-control">
                                        <option vlaue=""></option>
                                        <option value="true" <#if query.oversizeVolume?? && query.oversizeVolume == true>selected</#if>>是</option>
                                        <option value="false" <#if query.oversizeVolume?? && query.oversizeVolume == false>selected</#if>>否</option>
                                    </select>
                                </div>

                                <label class="control-label col-md-1">是否称重：</label>
                                <div class="col-md-2">
                                    <select name="query.oversizeWeight" class="form-control">
                                        <option vlaue=""></option>
                                        <option value="true" <#if query.oversizeWeight?? && query.oversizeWeight == true>selected</#if>>是</option>
                                        <option value="false" <#if query.oversizeWeight?? && query.oversizeWeight == false>selected</#if>>否</option>
                                    </select>
                                </div>

                                <label class="control-label col-md-1">是否可发货：</label>
                                <div class="col-md-2">
                                    <input class="form-control" name="query.oversizeStatusStr" type="text" value="${query.oversizeStatusStr}">
                                </div>
                                <label class="control-label col-md-1">发货单状态：</label>
                                <div class="col-md-2">
                                    <input class="form-control" name="query.status" type="text" value="${query.status}">
                                </div>
                            </div>

                            <div class="form-group">
                                <label class="control-label col-md-1">发货类型：</label>
                                <div class="col-md-2">
                                    <select name="query.shipStatusToStr" class="form-control">
                                        <option vlaue=""></option>
                                        <option value="0" <#if query.shipStatusToStr?? && query.shipStatusToStr == '0'>selected</#if>>普通</option>
                                        <option value="18" <#if query.shipStatusToStr?? && query.shipStatusToStr == '18'>selected</#if>>超体积</option>
                                        <option value="19" <#if query.shipStatusToStr?? && query.shipStatusToStr == '19'>selected</#if>>装箱订单</option>
                                        <option value="16,17" <#if query.shipStatusToStr?? && query.shipStatusToStr == '16,17'>selected</#if>>优选仓</option>
                                    </select>
                                </div>

                                <label class="control-label col-md-1">标签：</label>
                                <div class="col-md-2">
                                    <select name="query.buyerCheckout" class="form-control">
                                        <option vlaue=""></option>
                                        <option value="OVERSIZE" <#if query.buyerCheckout?? && query.buyerCheckout == 'OVERSIZE'>selected</#if>>超体积拦截</option>
                                        <option value="OVERWEIGHT" <#if query.buyerCheckout?? && query.buyerCheckout == 'OVERWEIGHT'>selected</#if>>超重拦截</option>
                                    </select>
                                </div>

                                <label class="control-label col-md-1">
                                    <select name="query.dateType" class="form-control" style="width:100px;float:right;border:none;margin-top:-9px;margin-right:-5px;text-align: right;">
                                        <option value="1" ${(query.dateType == 1)?string('selected', '')}>推单时间</option>
                                        <option value="2" ${(query.dateType == 2)?string('selected', '')}>分配时间</option>
                                        <option value="3" ${(query.dateType == 3)?string('selected', '')}>合单完成时间</option>
                                        <option value="4" ${(query.dateType == 4)?string('selected', '')}>包装完成时间</option>
                                        <option value="5" ${(query.dateType == 5)?string('selected', '')}>销售确认时间</option>
                                        <option value="6" ${(query.dateType == 6)?string('selected', '')}>拦截时间</option>
                                    </select>
                                </label>
                                <div class="col-md-2">
                                    <input class="form-control Wdate" type="text" name="query.fromDateValue" placeholder="" readonly="readonly" value="${query.fromDateValue }"
                                           onfocus="WdatePicker({startDate:'%y-%M-%d 00:00:00',dateFmt:'yyyy-MM-dd HH:mm:ss',alwaysUseStartDate:true})">
                                </div>
                                <label class="control-label col-md-1" style="font-weight: bolder">到</label>
                                <div class="col-md-2">
                                    <input class="form-control Wdate" type="text" name="query.toDateValue" placeholder="" readonly="readonly" value="${query.toDateValue }"
                                           onfocus="WdatePicker({startDate:'%y-%M-%d 23:59:59',dateFmt:'yyyy-MM-dd HH:mm:ss',alwaysUseStartDate:true})">
                                </div>
                            </div>

                        </div>
                        <div>
                            <div class="pull-left">
                                <div class="btn-group">
                                    <@header method="auth" authCode="OVERSIZE_INPORT">
                                        <div style="border-radius: 4px !important;" type="button" class="btn btn-default" onclick="impModal();">
                                            <i class="icon-plus"></i> 导入尺寸、重量
                                        </div>
                                    </@header>
                                    <@header method="auth" authCode="EXPRESS_BATCH_PUSH">
                                        <a class="btn btn-default" onclick="batchPush()">
                                            批量推送
                                        </a>
                                    </@header>
                                </div>
                            </div>
                            <div class="col-md-offset-12" style="text-align: right">
                                <@header method="auth" authCode="OVERSIZE_DOWNLOAD_DETAIL">
                                    <button type="button" class="btn btn-default" onclick="downloadCheck()">
                                        <i class="icon-download"></i> 导出明细
                                    </button>
                                </@header>
                                <button type="button" onclick="formReset(this)" class="btn btn-default">
                                    <i class="icon-refresh"></i> 重置
                                </button>
                                <button type="submit" class="btn blue">
                                    <i class="icon-search"></i> 查询
                                </button>
                            </div>
                        </div>
                    </form>
            </div>
        </div>
        <br/>
    </div>

    <div class="row">
        <div id="myFixedDiv" class="col-md-12">
            <table class="table table-striped table-bordered table-hover table-condensed" id="fixedTab">
                <colgroup>
                    <col width="6%"/>
                    <col width="10%"/>
                    <col width="10%"/>
                    <col width="7%"/>
                    <col width="5%"/>
                    <col width="6%"/>
                    <col width="10%"/>
                    <col width="7%"/>
                    <col width="3%"/>
                    <col width="5%"/>
                    <col width="5%"/>
                    <col width="10%"/>
                    <col width="10%"/>
                    <col width="6%"/>
                </colgroup>
                <thead>
                    <tr>
                        <th><input type="checkbox" id="check-all" name="checkAll">编号</th>
                        <th>发货单号</th>
                        <th>DD号</th>
                        <th>体积</th>
                        <th>称重重量(g)</th>
                        <th>是否可发货</th>
                        <th>SKU/时间</th>
                        <th>库位</th>
                        <th>数量</th>
                        <th>发货类型</th>
                        <th>标签</th>
                        <th>备注</th>
                        <th>物流方式</th>
                        <th>操作</th>
                    </tr>
                </thead>
            </table>
        </div>
        <div class="col-md-12 my-div-table" id="task-list-warp">
            <!-- 内容 -->
            <table style="background-color: #ffffff" class="table table-striped table-bordered table-hover table-condensed" id="task-list">
                <colgroup>
                    <col width="6%"/>
                    <col width="10%"/>
                    <col width="10%"/>
                    <col width="7%"/>
                    <col width="5%"/>
                    <col width="6%"/>
                    <col width="10%"/>
                    <col width="7%"/>
                    <col width="3%"/>
                    <col width="5%"/>
                    <col width="5%"/>
                    <col width="10%"/>
                    <col width="10%"/>
                    <col width="6%"/>
                </colgroup>
                <tbody>
                <#list domain.whApvs as apv>
                    <tr class="caret_tr_hidden" id="caret_tr_hidden_${apv.id}" onclick="showOrHiddenTr('${apv.id}')">
                        <td>
                            <input type="checkbox" value="${apv.id}" name="ids">
                            ${apv.id}<br/>
                        </td>
                        <td>${apv.apvNo }</td>
                        <td>${apv.salesRecordNumber }</td>
                        <#if apv.apvOversize ??>
                            <#if apv.apvOversize.length?? >
                                <td>
                                    ${apv.apvOversize.length } * ${apv.apvOversize.width } * ${apv.apvOversize.hight }
                                </td>
                            <#else>
                                <td></td>
                            </#if>
                            <td>${apv.apvOversize.weight }</td>
                            <td>${apv.apvOversize.statusName }</td>
                        <#else>
                            <td></td>
                            <td></td>
                            <td></td>
                        </#if>
                        <td>
                            付款：${apv.paidDate}<br/>
                            推单：${apv.creationDate}<br/>
                            <#if apv.apvOversize ?? && apv.apvOversize.interceptDate>拦截：${apv.apvOversize.interceptDate }</#if>
                            <#if apv.apvOversize ?? && apv.apvOversize.confirmDate>销售确认：${apv.apvOversize.confirmDate }</#if>
                        </td>
                        <td></td>
                        <td></td>
                        <td>
                            <span class='label label-sm label-info'>
                                <!--${util('enumName', 'com.estone.apv.enums.ApvTypeEnum', apv.apvType)}-->
                                ${util('enumName', 'com.estone.apv.common.ApvOrderType', apv.shipStatus)}
                            </span>
                        </td>
                        <td>${util('enumName', 'com.estone.apv.enums.ApvTaxTypeEnum', apv.buyerCheckout)}</td>
                        <td>${apv.apvOversize.remark}</td>
                        <td>
                            <dl>
                                <dd>${apv.logisticsCompany }</dd>
                                <dd>${apv.trackingNumber }</dd>
                            </dl>
                        </td>
                        <td>
                            <#if apv.apvOversize?? && apv.apvOversize.id??>
                                <#if (apv.apvOversize.status?? && apv.apvOversize.status==0) || apv.status == 16>
                                    <button type="button" class="btn btn-info btn-xs" onclick="showEditModal('${apv.apvNo}', '${apv.trackingNumber}', '${apv.apvOversize.length}'
                                    , '${apv.apvOversize.width}', '${apv.apvOversize.hight}', '${apv.apvOversize.weight}', '${apv.apvOversize.remark}')">
                                        量尺寸、称重
                                    </button>
                                </#if>
                            <#else>
                                <button type="button" class="btn btn-info btn-xs" onclick="showEditModal('${apv.apvNo}', '${apv.trackingNumber}', '${apv.apvOversize.length}'
                                , '${apv.apvOversize.width}', '${apv.apvOversize.hight}', '${apv.apvOversize.weight}', '${apv.apvOversize.remark}')">
                                    量尺寸、称重
                                </button>
                            </#if>
                            <button type="button" style="margin-left:10px;" class="btn btn-info btn-xs"
                                    onclick="viewLog(${apv.id}, 'whapv')">
                                日志
                            </button>
                        </td>
                    </tr>
                        <#list apv.whApvItems as apvItem>
                            <tr class="tr-order-${apv.id}" id="tr-order-item-${apvItem.id }">
                                <td></td>
                                <td></td>
                                <td colspan="4" <#if '4' == apvItem.itemDesc && '4' == apv.status>style="color: red;"</#if>>${apvItem.whSku.name }</td>
                                <td>${apvItem.sku }（${util("wh", apvItem.whSku.warehouseId)}）</td>
                                <td>${apvItem.whSku.locationNumber }</td>
                                <td>${apvItem.saleQuantity }</td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                            </tr>
                        </#list>
                    </#list>
                </tbody>
            </table>
            <!-- 内容end -->
            <div style="height: 50px;width: 100%;"></div>
        </div>
    </div>

    <div id="fixed-bottom">
        <div id="pager"></div>
    </div>

    <div style="margin-top: 100px" class="modal fade" id="editVolumeAndWeightModal" tabindex="-1" role="dialog" aria-labelledby="editVolumeAndWeightModalLabel" aria-hidden="true">
        <div class="modal-dialog" style="width: 800px;height: 740px;">
            <div class="modal-content">
                <div class="modal-header">
                    <h4 class="modal-title" id="editVolumeAndWeightModalLabel">量尺寸、称重</h4>
                </div>
                <div class="modal-body">
                    <table>
                        <colgroup>
                            <col width="8%"/>
                            <col width="5%"/>
                            <col width="10%"/>
                            <col width="5%"/>
                            <col width="10%"/>
                            <col width="5%"/>
                            <col width="10%"/>
                        </colgroup>
                        <tr>
                            <td style="text-align: right;"><label class="control-label">发货单号：</label></td>
                            <td colspan="2"><input class="form-control2" readonly type="text" name="apvNo"/></td>
                            <td style="text-align: right;"><label class="control-label">追踪号：</label></td>
                            <td colspan="2"><input class="form-control2" readonly type="text" name="trackingNo"/></td>
                            <td></td>
                        </tr>
                        <tr style="height: 20px;"></tr>
                        <tr>
                            <td style="text-align: right;"><label class="control-label">包装尺寸：</label></td>
                            <td style="text-align: right;"><label class="control-label">长(cm) </label></td>
                            <td><input class="form-control2" type="number" name="length"/></td>
                            <td style="text-align: right;"><label class="control-label"> 宽(cm) </label></td>
                            <td><input class="form-control2" type="number" name="width"/></td>
                            <td style="text-align: right;"><label class="control-label"> 高(cm) </label></td>
                            <td><input class="form-control2" type="number" name="hight"/></td>
                        </tr>
                        <tr style="height: 20px;"></tr>
                        <tr>
                            <td style="text-align: right;"><label class="control-label">包裹重量：</label></td>
                            <td colspan="2"><input class="form-control2" type="number" name="weight"/></td>
                            <td style="font-size: 18px;"> g</td>
                        </tr>
                        <tr style="height: 20px;"></tr>
                        <tr>
                            <td style="text-align: right;"><label class="control-label">备注：</label></td>
                            <td colspan="5"><textarea class="description" style="width: 500px;" rows="5" name="remark"></textarea></td>
                        </tr>
                    </table>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal">
                        取消
                    </button>
                    <button type="button" class="btn btn-primary" id="saveData">
                        确认
                    </button>
                </div>
            </div><!-- /.modal-content -->
        </div><!-- /.modal-dialog -->
    </div><!-- /.modal -->

    <!-- 打印弹窗 -->
    <div class="modal fade ui-popup" id="print_modal" tabindex="-1" role="dialog" aria-labelledby="organization-add-label" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">

            </div>
        </div>
    </div>

    <div style="margin-top: 100px" class="modal fade" id="importLoadInfoModal" tabindex="-1" role="dialog" aria-labelledby="importLoadInfoModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content" style="width: 600px">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-hidden="true"></button>
                    <h4 class="modal-title" id="importLoadInfoModalLabel">导入尺寸、重量</h4>
                </div>
                <div class="modal-body">
                    <form id ="importLoad">
                        <div class="form-body" style="padding: 20px">
                            <span class="btn btn-default fileinput-button" style="margin-left: 5px;">
                                <span class="icon-plus">上传文件</span>
                                <input type="file" name="file" onchange="uploadVolumeAndWeight(this)" />
                            </span>
                            <!--<span style="color: red;margin-left: 20px;">注意：当表格中的SKU已经存在时将会执行覆盖操作。</span>-->
                            <div style="margin-top: 40px">
                                <h5 style="margin: 20px 0px;"><a href="${CONTEXT_PATH}file/execl/apv_oversize_import.xlsx">《超体积发货单尺寸重量导入模板》.xlsx</a></h5>
                            </div>
                        </div>
                    </form>
                </div>
            </div><!-- /.modal-content -->
        </div><!-- /.modal-dialog -->
    </div>

</div>

<script type="text/javascript" src="${CONTEXT_PATH}js/pages/pms.js?v=${.now?datetime}"></script>
<script type="text/javascript" src="${CONTEXT_PATH}js/datepicker/WdatePicker.js"></script>
<script type="text/javascript" src="${CONTEXT_PATH}js/table-thead-fixed.js"></script>
<script type="text/javascript" src="${CONTEXT_PATH}js/jquery.form.js"></script>
<script type="text/javascript">
    document.getElementById("fixedTab").style.width = $('#task-list').css('width');
    // 分页
    var total = "${domain.page.totalCount}";
    var pageNo = "${domain.page.pageNo}";
    var pageSize = "${domain.page.pageSize}";
    $("#pager").initPager({total: total, pageNo: pageNo, pageSize: pageSize});

    var heights = $("body").height();
    if (heights > 910) {
        $("#fixed-bottom").addClass("fixed-bottom-height")
    }

    var statusArray =  ${domain.oversizeStatusJson};
    $("input[name='query.oversizeStatusStr']").select2({
        data: statusArray,
        placeholder: "是否可发货",
        multiple: true,
        allowClear: true
    });


    var apvStatusArray = ${domain.statusSelectJson};
    $("input[name='query.status']").select2({
        data : apvStatusArray,
        placeholder : "发货单状态",
        allowClear : true
    });

    // 全选
    var checkAll = $("input[name='checkAll']");

    // 子选项
    var itemIds = $("input[name='ids']");
    checkAll.change(
        function () {
            itemIds.prop("checked", $(this).prop("checked"));
            itemIds.each(function () {
                var f = $(this).is(":checked");
                var checkClass = $(this).prop("class");
                $("." + checkClass).each(function () {
                    $(this).prop("checked", f);
                })
            })
        }
    );

    // 获取选中的入库单
    function getCheckedIds() {
        var checkedIds = $("input[name='ids']:checked");
        return checkedIds;
    }

    // 下载
    function downloadCheck() {
        var param = $("#domain").serialize();
        debugger;
        var checkedDatas = getCheckedIds();
        if(checkedDatas.length > 0) {
            param = param + "&" +checkedDatas.serialize();
        }
        $.post(CONTEXT_PATH + "apvOversize/download", param, function(data){
            if (data.status == 200) {
                if (data.message==null || data.message==''){
                    layer.alert('成功',function (index) {
                        layer.close(index);
                        diglog.close().remove();
                        location.reload();
                    });
                }else{
                    customizeLayer(data.message);
                }
            } else {
                customizeLayer(data.message);
            }
        });
    }

    // 导入SKU
    function impModal() {
        $("#importLoadInfoModal").modal('show');
    }

    function uploadVolumeAndWeight(target) {

        //检测上传文件的类型
        var filename = target.value;

        var ext, idx;
        if (filename == '') {
            $("#submit-upload").attr("disabled", true);
            layer.alert("请选择需要上传的文件!");
            return;
        } else {
            idx = filename.lastIndexOf(".");
            if (idx != -1) {
                ext = filename.substr(idx + 1).toUpperCase();
                ext = ext.toLowerCase();

                if (ext != 'xls' && ext != 'xlsx') {
                    layer.alert("只能上传Excel文件!");
                    return;
                }
            } else {
                layer.alert("只能上传Excel文件!");
                return;
            }
        }

        var r = confirm("确定上传" + filename + "?");

        if(!r) {
            return;
        }

        var uploadUrl = CONTEXT_PATH + "apvOversize/importVolumeAndWeight";

        var searchUrl = $("#importLoad").attr("action");

        $("#importLoad").attr("action", uploadUrl);

        $("#importLoad").ajaxSubmit(function(data) {
            if (data.status == 200) {
                layer.confirm('成功!',{
                    icon: 1,
                    btn: ['确定']
                },function () {
                    window.location.reload();
                })
            } else {
                customizeLayer(data.message, "error");
            }
        });
        $(target).val(null);
        $("#importLoad").attr("action", searchUrl);
    }

    function showOrHiddenTr(apvId){
        let tr = $('#caret_tr_hidden_' + apvId);
        if(tr.hasClass('caret_tr_hidden')){
            tr.removeClass('caret_tr_hidden').addClass('caret_tr_show');
            $('.caret_tr_' + apvId).removeAttr('hidden');
        }else if(tr.hasClass('caret_tr_show')){
            tr.removeClass('caret_tr_show').addClass('caret_tr_hidden');
            $('.caret_tr_' + apvId).attr('hidden', 'hidden');
        }
    }

    function showEditModal(apvNo, trackingNo, length, width, hight, weight, remark){
        $("#editVolumeAndWeightModal").find("input[name='apvNo']").val(apvNo);
        $("#editVolumeAndWeightModal").find("input[name='trackingNo']").val(trackingNo);
        $("#editVolumeAndWeightModal").find("input[name='length']").val(length);
        $("#editVolumeAndWeightModal").find("input[name='width']").val(width);
        $("#editVolumeAndWeightModal").find("input[name='hight']").val(hight);
        $("#editVolumeAndWeightModal").find("input[name='weight']").val(weight);
        $("#editVolumeAndWeightModal").find("textarea[name='remark']").val(remark);
        $("#editVolumeAndWeightModal").modal('show');
    }

    $('#saveData').on('click',function () {
        //3.设置提交按钮失效，以实现防止按钮重复点击
        var reg = /^\+?[1-9][0-9]*$|^[+]{0,1}(\d+\.\d{1,2})$/;
        var reg2 = /^\+?[1-9][0-9]*$|^[+]{0,1}(\d+\.\d{1,3})$/;
        var apvNo = $("#editVolumeAndWeightModal").find("input[name='apvNo']").val();
        var length = $("#editVolumeAndWeightModal").find("input[name='length']").val();
        var width = $("#editVolumeAndWeightModal").find("input[name='width']").val();
        var hight = $("#editVolumeAndWeightModal").find("input[name='hight']").val();
        var weight = $("#editVolumeAndWeightModal").find("input[name='weight']").val();
        var remark = $("#editVolumeAndWeightModal").find("textarea[name='remark']").val();
        if(length != undefined && length != '' && !reg.test(length)){
            layer.alert('长度格式错误！支持小数点后两位', 'error');
            return false;
        }
        if(width != undefined && length != '' && !reg.test(width)){
            layer.alert('宽度格式错误！支持小数点后两位', 'error');
            return false;
        }
        if(hight != undefined && length != '' && !reg.test(hight)){
            layer.alert('高度格式错误！支持小数点后两位', 'error');
            return false;
        }
        // 校验重量格式
        if(weight != undefined && length != '' && !reg2.test(weight)){
            layer.alert('包裹重量格式错误！支持小数点后三位', 'error');
            return false;
        }
        $('#saveData').attr("disabled", true);

        var data = {
            apvNo : apvNo,
            length : length,
            width : width,
            hight : hight,
            weight : weight,
            remark : remark
        }

        $.ajax({
            url: CONTEXT_PATH + 'apvOversize/updateVolumeAndWeight',
            type: "POST",
            contentType: "application/json;charset=utf-8",
            dataType : "json",
            data: JSON.stringify(data),
            success: function(response) {
                if (response.status == '500') {
                    customizeLayer(response.message, 'error');
                } else{
                    if (response.message) {
                        customizeLayer(response.message);
                    } else {
                        layer.alert('修改成功！');
                    }
                }
            },
            error:function () {
                layer.alert("系统异常，操作失败!",'error');
            }
        });
        setTimeout(function() {
            //提交完成后按钮重新设置有效
            $('#saveData').removeAttr('disabled');
            $("#editVolumeAndWeightModal").modal('hide');
            window.location.reload();
        }, 1500);
    });

    function batchPush() {
        let checkedIds = getCheckedIds();
        if(checkedIds.length === 0) {
            layer.alert("请选择要操作的数据",'error');
            return;
        }
        $.ajax({
            url: CONTEXT_PATH + 'apvOversize/batchPush',
            type: "POST",
            data: checkedIds.serialize(),
            success: function(response) {
                if (response.status == '500') {
                    customizeLayer(response.message, 'error');
                } else{
                    alert('批量推送成功！');
                    window.location.reload();
                }
            },
            error:function () {
                layer.alert("系统异常，操作失败!",'error');
            }
        });
    }

</script>
</body>
</html>