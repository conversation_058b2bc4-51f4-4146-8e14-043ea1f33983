<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">
<html>
<head>
	<title>易世通达仓库管理系统</title>
	<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
	<#include "/common/include.html">
	<style type="text/css">
		.modal-body input {
			width: 240px;
		}
		.input-medium{
			height: 30px;
		}
		.panel-title2 > h1 {
			margin-top: 0;
			margin-bottom: 0;
		}

	</style>
</head>
<body>
	<@header method="header" active="12050000"><#include "/ftl/header.ftl"></@header>
	<div id="page" style="background-color: rgb(231, 237, 248)">
		<div class="row">
			<div class="col-md-12" style="padding: 0">
				<ul class="page-breadcrumb breadcrumb" style="background-color: white;">
					<li><a href="#">发货管理</a></li>
					<li class="active">多品合单扫描</li>
				</ul>
			</div>
		</div>
		<#include "/common/pack_bgcolor_selector.html">

		<div class="container-fluid" style="background-color: white;border: none">
			<div class="row">
				<input  type="hidden" value="" id="apv-no-now"/>
				<input  type="hidden" value="" id="sku-now"/>
				<input  type="hidden" value="" id="apv-no-now-fail"/>
				<input  type="hidden" value="" id="ship-service-now"/>
				<input  type="hidden" value="" id="orderid"/>
				<div class="col-md-12" style="height: 50px">
					<div class="col-md-3" style="margin-top: 20px">
						<label style="margin-top: 5px;">SKU唯一码</label>
						<input type="text" style="float:right;width: 80%" class="form-control" name="sku" id="sku" onpaste="return false" onkeypress="if(event.keyCode==13) { inputsku(this); return false;}">
					</div>
					<div id="fo-flag" class="col-md-4" style="margin-top: 20px;text-align: center">

					</div>
					<div class="col-md-5">
						<div style="display: inline-block;margin-left: 10px" class="panel-title2" id="panel-floor"></div>
						<div style="display: inline-block;margin-left: 10px" class="panel-title2" id="panel-title"><h1 style="color:blue;font-size:42px;">成功:<b>0</b></h1></div>
						<div style="display: inline-block;margin-left: 8%;margin-right: 8%;" class="panel-title2" id="panel-title-piece"><h1 style="color:red;font-size:42px;">计数:<b>0</b></h1></div>
						<button type="button" class="btn red" onclick="failPrint()"><i class="icon-print"></i> 打印失败面单</button>
						<button  type="button" class="btn red" onclick="openPrintDialog()">打印机配置</button>
					</div>
				</div>
				<hr>
				<div id="check_scan_datas" class="border-gray p5 col-md-12"></div>
			</div>
			<div id="print_tag" style="width:100%;">
			</div>
			<div id="print_gpsr_tag" style="display: none;"></div>
			<div class="row">
				<div class="col-md-3"></div>
				<div class="col-md-6">
					<iframe src="javascript:void(0);" frameborder="0" marginheight="0" marginwidth="0" frameborder="0" scrolling="auto" id="shippingOrderFrame" name="shippingOrderFrame" width="100%" height="800px"></iframe>
					<iframe src="javascript:void(0)" frameborder="0" marginheight="0" marginwidth="0" frameborder="0" scrolling="auto" id="oms_print_content" name="oms_print_content" width="100%" height="900px"></iframe>
				</div>
				<div class="col-md-3"></div>
			</div>
			<form action="" method="post" target="_self" id="print_form" style="display: none"></form>
			<iframe style="width:1px;height:1px;border: 0 none;" name="printHtml" id="printHtml"></iframe>
		</div>
		<#include "/common/footer.html">
	</div>
	<script type="text/javascript" src="${CONTEXT_PATH }js/LodopFuncs.js"></script>
	<script type="text/javascript" src="${CONTEXT_PATH }js/web-storage-cache.js"></script>
	<script type="text/javascript" src="${CONTEXT_PATH }js/jquery.sound.js?v=${.now?datetime}"></script>
   	<script type="text/javascript" src="${CONTEXT_PATH }js/pages/pack-style.js?v=${.now?datetime}"></script>
	<script type="text/javascript" src="${CONTEXT_PATH }js/packing.js?v=${.now?datetime}"></script>
	<script type="text/javascript" src="${CONTEXT_PATH}js/print.js?v=${.now?datetime}"></script>
	<script type="text/javascript">
		window.onload = function () {
			getPrinterList();
		};
		var printUrl = "${CONTEXT_PATH }apv/packs/oms/mg/print?apvNo=";

        var uuIdCacheKey = 'CHECK_PACKING_FOR_UUID_CACHE_KEY_MM'+ '_' +  new Date().getTime();
		var cacheKey = "single_warehouse_check_success";
		var pieceCacheKey = "single_warehouse_check_express_piece_success";
		//syt页面缓存
		var cacheSYTKey="SYT_check_success";

		
		//拣货类型 单品多件和 多品多件 快递和FBA 面单不同
		var pickType ;
		//发货类型
		var shipStatus ;

		//物流公司 EMS需要特殊模板
		var logisticsCompany;

		var gpsrPlatform;
		var waybillSize;

		$(document).ready(function(){
            initSkuUuIdStorageCache(uuIdCacheKey);
			initScanTaskYSTSkuKeyQtyCache();
			pageInit();
			
	  		var storage = new WebStorageCache();
	  		
	  		if (storage.get(cacheKey)) {
	  			lastSuc = storage.get(cacheKey);
	  			$('#panel-title').html('<h1  style="color:blue;font-size:42px;">成功：<b>'+lastSuc+'</b></h1>');
	  		}
	  		
	  		if (storage.get(pieceCacheKey)) {
	  			lastSuc = storage.get(pieceCacheKey);
	  			$('#panel-title-piece').html('<h1  style="color:red;font-size:42px;">计数:<b>'+lastSuc+'</b></h1>');
	  		}
			initPrinter();
	  		$('#orderid').focus();
	  		
	    }); // end ready
	    
		// 初始化
		function pageInit() {
			$('#orderid').val('');
			$('#sku').val('');
			$('#sku').select().focus();
			$('#check_scan_datas').html('');
		}

		//检查数量手动录入是否勾选
		function QuantityON(){
			return false;
		}

        // 添加唯一码包装日志
        function addWhUniqueSkuLog(uuid, apvNo) {
		    if (apvNo){
            	addUuIdStorageCache(uuIdCacheKey, uuid);
			}
            var r = $.ajax({
                type : "get",
                url :CONTEXT_PATH+"apv/packs/addWhUniqueSkuLog" ,
				data : {uuid : uuid, apvNo: apvNo, packType: 9},
                timeout : 100000,
                beforeSend : function() {
                },
                success : function(responese) {

                },
                error : function() {
                }
            });
        }

		/*
		  新增包装异常记录
		  match参数用于记录是否要匹配发货单，false表示不匹配发货单，用于兼容以前的接口
		  true表示要匹配发货单，并且进行记录
		 */
		function addPackExceptionRecord(uuid, apvNo, match){
			if(!match && apvNo != undefined && apvNo != ''){
				return;
			}
			let taskNo = $('#orderid').val();
			let datas = {uuid: uuid, taskNo: taskNo, scanPage: 9, packExceptionType: 2};
			if (match && apvNo != undefined && apvNo != '' && apvNo != null){
			    datas = {uuid: uuid, taskNo: taskNo, scanPage: 9, packExceptionType: 2, errorMsg:":"+apvNo}
            }
			$.ajax({
				type: "POST",
				url: CONTEXT_PATH+"apv/packs/createPackExceptionUuidItem",
				data: datas,
				success: function(response) {
				},
				error:function () {
				}
			});
		}

        // 校验唯一码重复扫描
        function checkScanPackingUniqueSku(sku, uuid) {
            var r = $.ajax({
                type : "get",
                url :CONTEXT_PATH+"apv/packs/checkScanPackingUniqueSku" ,
                data : {uuid : uuid, type: 1},
                timeout : 100000,
                beforeSend : function() {
                    App.blockUI();
                },
                success : function(responese) {
                    debugger;
                    App.unblockUI();
                    if (responese.status == '500') {
                        layer.alert(responese.message, {closeBtn: 0},function (index) {
							layer.close(index);
							audioPlay('error');
							$('#sku').val("");
							$('#sku').focus();
						});
                    } else {
                        // 前端缓存校验是否重复扫描
                        if(!checkRetrunUuIdStorageCache(uuIdCacheKey, uuid)){
                            audioPlay('error');
                            layer.alert("唯一码重复扫描！", {closeBtn: 0},function (index) {
                            	layer.close(index);
								$('#sku').val("");
								$('#sku').focus();
							});
                            return;
                        }
                        var bindingApvNo = responese.message;
                    	if(bindingApvNo == '' || bindingApvNo == null){
                            audioPlay('error');
                            layer.alert('唯一码播种未绑定!');
                            addWhUniqueSkuLog(uuid, '');
                            addPackExceptionRecord(uuid, bindingApvNo, true);
                            return;
                        }
						var apvNo = $("#apv-no-now").val();
                        if (bindingApvNo == 'SKU'){
							if (!apvNo || apvNo == ''){
								layer.alert('还未匹配到发货单，请先扫描唯一码!');
								return;
							}
                            bindingApvNo = "";
							secondScan(sku, uuid, bindingApvNo,apvNo);
						}else{
                        	var apvBool=false;
							var inThisTask = true;
							$("#check_scan_yst_tr").find('td').each(function (){
								var text = $(this).text();
								if (text!=null && text!="" &&text==bindingApvNo){
									inThisTask=false;
								}

								var color = $(this).attr("style");
								if (color==undefined || color==null || color==''){
									apvBool=true;
								}

							});
							if(apvBool && inThisTask){
								createPackExceptionUuidItem(uuid, responese.message);
								var bool = confirm("上个波次任务还未完成包装，是否继续开始新波次的包装!");
								if (bool){
									inputUniqueKey(sku,uuid,bindingApvNo);
								}
								$('#sku').val("");
								return;
							}
							if (apvNo) {
								secondScan(sku, uuid, bindingApvNo, apvNo);
							} else {
								inputUniqueKey(sku, uuid, bindingApvNo);
							}
						}
                    }
                },
                error : function() {
                    debugger;
                    App.unblockUI();
                    layer.alert('扫描失败，请重新扫描', {closeBtn: 0},function (index) {
						layer.close(index);
						audioPlay('error');
						$('#sku').val("");
						$('#sku').focus();
					});
                }
            });
        }

		// 保存唯一码包装异常信息
		function createPackExceptionUuidItem(uuid, apvNo){
			let errorMsg = 'apvNo:' + apvNo;
			let taskNo = $('#orderid').val();
			$.ajax({
				url: CONTEXT_PATH+"apv/packs/createPackExceptionUuidItem",
				type: "POST",
				data: {uuid: uuid, taskNo: taskNo, scanPage: 9, errorMsg: errorMsg, packExceptionType: 1},
				success: function(response) {
				},
				error:function () {
				}
			});
		}


		function elementWalker1(apvNo){
			var childs = $("#check_scan_yst_tr").find('td');
			var arrSty=[];
			for(var i = childs.length - 1; i >= 0; i--) {
				var outerText = childs[i].outerText;
				if (typeof (outerText) == "undefined" || outerText==null || outerText=="") {
					continue;
				}
				var replace = outerText.replaceAll("\n",'').trim();

				if (replace==apvNo) {
					childs[i].style.color="black";
				}

				var attr = childs[i].style.color;

				if(attr=="black"){
					arrSty.push(replace);
				}
			}

		}


		//扫描唯一键
		function inputUniqueKey(sku, uuid, bindingApvNo){

			var uniqueKey = $.trim(uuid);

			var r = $.ajax({
				url : CONTEXT_PATH + "single/batch/scans/check/uniqueKey",
				data : {uniqueKey : uniqueKey},
				timeout : 90 * 1000,
				success : function(response){
					$("#check_scan_datas").html(response);
					const errorElement = $(response).find("#scan-error");
					if (response.length > 230 && errorElement.length === 0 ){
						// 第一次扫描初始化
						initSkuUuIdStorageCache(uuIdCacheKey);
						initScanTaskYSTSkuKeyQtyCache();
						isFocus = false;
						// 扫描成功
						$('#sku').select().focus();

						var apvNo = $('#check_scan_datas').find("input[name='apvNo']").val();
						shipStatus = $('#check_scan_datas').find("input[name='shipStatus']").val();

						gpsrPlatform = $('#check_scan_datas').find("input[name='gpsrPlatform']").val();

						// 拣货类型
						pickType = $('#check_scan_datas').find("input[name='location']").val();

						logisticsCompany = $(response).find("input[name='logisticsCompany']").val();

						//保存apvNo
						$("#apv-no-now").val(apvNo);

						$("#sku-now").val(sku);
						//暂时记录失败
						$("#apv-no-now-fail").val(apvNo);

						//记录任务号
						$("#orderid").val($('#check_scan_datas').find("input[name='taskNo']").val());

						var orderOrigin = $('#check_scan_datas').find("input[name='shipService']").val();

						waybillSize = $('#check_scan_datas').find("input[name='waybillSize']").val();
						var specialSkuStr = $('#check_scan_datas').find("input[name='specialSkuStr']").val();
						var oudaiHtml = "";
						if (specialSkuStr && specialSkuStr == 'EUR') {
							oudaiHtml = "<button class='btn-modal btn yellow' style='float: left;margin-left: 20%;width: 100px;'>欧代订单</button>";
						} else if (specialSkuStr == 'UK') {
							oudaiHtml = "<button class='btn-modal btn yellow' style='float: left;margin-left: 20%;width: 100px;'>英代订单</button>";
						}
						var sizeHtml = "<p style='margin-right: 30%;font-size: large;'>面单尺寸：" + waybillSize + "</p>";

						$("#fo-flag").html(oudaiHtml + sizeHtml);

						$("#ship-service-now").val(orderOrigin);
						document.getElementById('oms_print_content').src = '';

						checkIn(sku, uuid, bindingApvNo);

					}else {
						audioPlay('error');
						const errorMessage = errorElement.length ? errorElement.text().trim() : "未匹配到单据，请重试";
						layer.alert(errorMessage, {
							closeBtn: false,
							skin: 'layui-layer-lan'
						}, function(index) {
							layer.close(index);
							$('#sku').select().focus();
						});

					}

				},
				error:function(){
					layer.alert('扫描失败，请重新扫描');
				}
			});

		}

		function secondScan(sku, uuid, bindingApvNo,apvNo){
			var skuNow = $("#sku-now").val();
			var suitSku = $('#check_scan_datas').find('#check_suit_sku').text();
			var exist = $("#check_scan_datas").find(".check_suit_sku_list").html();
			var allScan = true;
			if (exist){
				$("#check_scan_datas").find(".check_suit_sku_list").each(function (i) {
					var checkQty = parseInt($(this).find('.check_quantity').text());
					var needQty = parseInt($(this).find('.need_quantity').text());
					if (checkQty == 0 || checkQty != needQty) {
						allScan = false;
						return false;
					}
				});
			}

			if (skuNow && skuNow == sku && !allScan) {
				checkIn(sku, uuid, bindingApvNo);

			}  else {
				var suitSkuNeedQty = parseInt($('[id="check_suit_sku_'+suitSku+'_need"]').text());
				var suitSkuCheckQty = parseInt($('[id="check_suit_sku_'+suitSku+'_check"]').text());
				var scanSuitSku = $('#check_task_suit_sku_'+sku).val();
				if (suitSku && suitSkuNeedQty != suitSkuCheckQty && $('#check_suit_sku').parent().html().indexOf(sku) == -1) {
					layer.alert('请先扫描套装商品', {closeBtn: 0}, function (index) {
						layer.close(index);
						audioPlay('error');
						$('#sku').val("");
						$('#sku').focus();
					});
					return false;
				}


				if (!suitSku) {
					pSuitSku = null;
				} else {
					var pSuitSku = suitSku;
					if (allScan && suitSkuNeedQty === suitSkuCheckQty) {
						pSuitSku = null;
					}
					if (scanSuitSku && scanSuitSku != suitSku && scanSuitSku != '普通') {
						pSuitSku = scanSuitSku;
					}
				}
				var uniqueKey = $.trim(uuid);

				var r = $.ajax({
					url : CONTEXT_PATH + "single/batch/scans/check/uniqueKey",
					data: {uniqueKey: uniqueKey, apvNo: apvNo, suitSku: pSuitSku, beforeSku: skuNow},
					timeout : 90 * 1000,
					success : function(response){

						var skuHtml = $(response).find("#check_scan_datas_d").html();
						var errorMsg = $(response).find(".scan_error").html();

						if (errorMsg || !skuHtml) {
							errorMsg = errorMsg ? errorMsg : 'SKU不存在或已检查完毕';
                            audioPlay('error');
							layer.alert(errorMsg, {closeBtn: 0}, function (index) {
								layer.close(index);
								clear();
								$('#sku').select().focus();
								addWhUniqueSkuLog(uuid, '');
                                addPackExceptionRecord(uuid, '', false);
							});
							return;
						}
						if (!suitSku) {
							$("#check_scan_datas").find("#check_scan_datas_d").html(skuHtml);
						} else if (allScan) {
							suitSku = $.trim(suitSku.toUpperCase());
							var suitSkuCheckQty = parseInt($('[id="check_suit_sku_'+suitSku+'_check"]').text());
							$("#check_scan_datas").find("#check_scan_datas_d").html(skuHtml);
							$('[id="check_suit_sku_'+suitSku+'_check"]').html(suitSkuCheckQty);
						}
						// 扫描成功
						$('#sku').select().focus();
						$("#sku-now").val(sku);
						checkIn(sku, uuid, bindingApvNo);
					},
					error:function(){
						layer.alert('扫描失败，请重新扫描');
                        addWhUniqueSkuLog(uuid, '');
                        addPackExceptionRecord(uuid, bindingApvNo, true);
					}
				});
			}
		}

		//输入SKU触发
		function inputsku(obj){
			if (!jitPrinter || !jitPrinterTag || !jitPrinter75Tag) {
				layer.alert("请先配置打印机",'error');
				return
			}
			var sku = obj.value.replace(/\s/g,'');
            var uuid = sku.trim();

			//订单Id,切分二维码. 如果存在的话
            if(!(sku.indexOf("=") == -1)){
                var realSku = sku.split('=')[0];
                $('#sku').val(realSku);
                sku = realSku;
            }

            if (QuantityON()){

			}else {
				//checkIn(sku, uuid);
                checkScanPackingUniqueSku(sku, uuid);
			}
		}
		
		//核对sku和数量 .正确(待检区 消失   已检查区  对应的sku 数量增加...)
		function checkIn(sku, uuid, bindingApvNo){
			var apvNo = $('#check_scan_datas').find("input[name='apvNo']").val();
			// 校验播种和包装是否一致
			if(bindingApvNo != '' && bindingApvNo != null && bindingApvNo != apvNo){
                audioPlay('error');
				layer.alert('与播种绑定的APV不一致!', 'error');
				return;
			}

			sku = $.trim(sku.toUpperCase());
			
			if(completeCheck()){
                addWhUniqueSkuLog(uuid, '');
                addPackExceptionRecord(uuid, '', false);
				layer.alert('检查完毕');
				if (gpsrPlatform){
					audioPlay('gpsr');
				}else{
					audioPlay('success');
				}
				return;
			}
			var taskNo = $('#check_scan_datas').find("input[name='taskNo']").val();

			var suitSku = $('#check_scan_datas').find('#check_suit_sku').text();

			if (suitSku){
				suitSku = $.trim(suitSku.toUpperCase());
				var needQty = parseInt($('[id="check_need_qty_' + sku + '"]').text());
				var qty = parseInt($('[id="check_quantity_' + sku + '"]').text());
				var allNew = qty === 0 ? true : false;

				var scanQty = addScanSuitSkuQtyCache(apvNo,taskNo,sku,needQty,suitSku,allNew);
				if (scanQty > needQty){
					layer.alert('拿多了？',{closeBtn: 0},function (index) {
						layer.close(index);
						clear();
						audioPlay('error');
						addWhUniqueSkuLog(uuid, '');
						addPackExceptionRecord(uuid, '', false);
					});
					return;
				}

				//扫描数量
				$('[id="check_quantity_'+sku+'"]').html(scanQty);

				var skuQty = $('[id="check_task_apv_'+sku+'"]').html();
				$('[id="check_task_apv_'+sku+'"]').html(parseInt(skuQty)-1);

				if (parseInt(skuQty) == 1){
					//移除class red属性
					$('[id="check_task_apv_'+sku+'"]').removeClass('red');
				}
				var allScan = true;
				$(".check_suit_sku_list").each(function (i) {
					var checkQty = parseInt($(this).find('.check_quantity').text());
					var needQty = parseInt($(this).find('.need_quantity').text());
					if (checkQty != needQty) {
						allScan = false;
						return false;
					}
				});
				var suitSkuCheckQty = parseInt($('[id="check_suit_sku_'+suitSku+'_check"]').text());
				var suitSkuNeedQty = parseInt($('[id="check_suit_sku_'+suitSku+'_need"]').text());
				if (allScan){
					$('[id="check_suit_sku_'+suitSku+'_check"]').html(parseInt(suitSkuCheckQty)+1);
					suitSkuCheckQty += 1;
				}
				if (suitSkuCheckQty == suitSkuNeedQty){
					var skuBarcode = $('#check_scan_datas').find('#skuBarcode').val();
					//打印JIT货品标签
					printJitTag(apvNo, sku, suitSkuNeedQty, skuBarcode, true);

				}
				addWhUniqueSkuLog(uuid, apvNo);
				addPackExceptionRecord(uuid, apvNo, false);
				if (gpsrPlatform){
					audioPlay('gpsr');
				}else{
					audioPlay('success');
				}
				// 检查是否完成
				clear();
			} else {
				var needQty = parseInt($('[id="check_need_qty_' + sku + '"]').text());
				var scanQty = addScanTaskYSTSkuKeyQtyCache(apvNo, taskNo, sku, needQty);
				if (needQty == scanQty && (shipStatus == '22' || shipStatus == '23')) {
					var skuBarcode = $('#check_scan_datas').find('#skuBarcode').val();
					if (skuBarcode && skuBarcode.indexOf(',') > -1) {
						async function print() {
							const barcodes = skuBarcode.split(',');
							for (let i = 0; i < barcodes.length; i++) {
								const barCode = barcodes[i];
								await printJitTag(apvNo, sku, null, barCode, true);
							}
						}
						print();

					} else {
						//打印JIT货品标签
						printJitTag(apvNo, sku, null, skuBarcode, true);
					}
				} else if (gpsrPlatform && needQty === scanQty) {
					printGpsrTag(apvNo, sku);
				}

				if (scanQty > needQty){
					layer.alert('拿多了？',{closeBtn: 0},function (index) {
						layer.close(index);
						clear();
						audioPlay('error');
						addWhUniqueSkuLog(uuid, '');
						addPackExceptionRecord(uuid, '', false);
					});
					return;
				}

				//扫描数量
				$('[id="check_quantity_'+sku+'"]').html(scanQty);

				var skuQty = $('[id="check_task_apv_'+sku+'"]').html();
				$('[id="check_task_apv_'+sku+'"]').html(parseInt(skuQty)-1);

				if (parseInt(skuQty) == 1){
					//移除class red属性
					$('[id="check_task_apv_'+sku+'"]').removeClass('red');
				}

				addWhUniqueSkuLog(uuid, apvNo);
				addPackExceptionRecord(uuid, apvNo, false);
				if (gpsrPlatform){
					audioPlay('gpsr');
				}else{
					audioPlay('success');
				}
				// 检查是否完成
				clear();
			}


		}
		
		
		//是否已经检查完(检查完后清空页面) 
		function completeCheck() {
			var td = $('.check_task_sku_quantity');
			var flag = true;
			for (var i = 0; i < td.length; i++) {
				if (td[i].innerHTML != 0) {
					flag = false;
					break;
				}
			}
			return flag;
		}
		
		//清空核对区
		function clear(){
			if (completeCheck()){
				calsf();

				var apvNo = $("#apv-no-now").val();

				elementWalker1(apvNo);
			}else{
				$('#sku').val("");
				$('#sku').focus();
			}
		};
		
		
		// 统计扫描成功和失败的数量
	    function calsf(){
	    	
	    	// 记录后台
	    	var apvId = $('#check_scan_datas').find("input[name='apvId']").val();
	    	
	    	var quantity = $('#check_scan_datas').find("input[name='totalSaleQuantity']").val();
	    	
	    	var boxNo = $("#orderid").val();
	    	
	    	// PASS
			$.get("${CONTEXT_PATH}single/batch/scans/check/pass", {"apvId" : apvId, "totalQuantity" : quantity, "boxNo" : boxNo}, function(response) {
				if(response.status == '200'){
					
			    	// 计件
					calsfPiece(quantity);
			    	
			    	
					printApvNo($("#apv-no-now").val());
					
					$("#sku").val('');

					//最后一个扫描完成
					if(response.message == boxNo){
						$("#orderid").val('');
						$("#sku").val('');
						$('#sku').select().focus();
					}
					$("#apv-no-now").val('')
		      	} else {

				  	var msg = "数据提交失败!请重试。";
					if(response.message){
                        msg += response.message;
					}
					toastr.options = {
				            closeButton: true,
				            debug: false,
				            timeOut: "3000",
				            positionClass: 'toast-top-center',
				            onclick: null
				    };
					toastr['error'](msg, "错误");
				  	
		      	}
			});
	    	
	    }
		
	 	// 计数
		function calsfPiece(quantity){
	 		
			var storage = new WebStorageCache();
      		
			//成功数
      		var lastSuc = 0;
      		if (storage.get(cacheKey)) {
      			lastSuc = storage.get(cacheKey);
      		}
      		
      		var suc = parseInt(1) + lastSuc;
      		
      		storage.set(cacheKey, suc, {exp : 5 * 60 * 60});
	    	
	    	$('#panel-title').html('<h1  style="color:blue;font-size:48px;">成功：<b>'+suc+'</b></h1>');
	    	
	    	
	    	//件数
			var unit = 0;
      		
      		if (storage.get(pieceCacheKey)) {
      			unit = storage.get(pieceCacheKey);
      			unit = parseInt(unit) + parseInt(quantity);
      		} else {
      			unit = quantity;
      		}
      		
      		storage.set(pieceCacheKey, unit , {exp : 5 * 60 * 60});
      		
	    	$('#panel-title-piece').html('<h1  style="color:red;font-size:48px;">计数:<b>'+unit+'</b></h1>');
	    }
		
		$(function(){
			$('#orderid').focus();
		});

		//打印标签
		function printApvNo(apvNo){
            if (apvNo){
                bindingApvAndUuIdlist(apvNo, uuIdCacheKey);
                initSkuUuIdStorageCache(uuIdCacheKey);
            }
			if(pickType == '9' || shipStatus == '2' || shipStatus == '12' || shipStatus == '14' || shipStatus == '20'){
				//document.getElementById('oms_print_content').src = printUrl + apvNo;
				omsPrint(apvNo,printUrl);
				setTimeout(failPrint(), 500);
			} else if(pickType == '12') {
				//FBA
				failPrint();

			} else if (shipStatus == '22' || shipStatus == '23') {
				var r= $.ajax({
					url: CONTEXT_PATH + "fba/packs/localPrintXiangmai?apvNo=" + apvNo,
					timeout : 100000,
					async: false,
					success : function(response){
						if(response.status == '200'){
							var jitPdfUrl = response.body.jitPdfUrl;
							if (!jitPdfUrl) {
								jitPdfUrl = window.location.origin + CONTEXT_PATH + response.message;
								document.getElementById('shippingOrderFrame').src = CONTEXT_PATH + response.message;
							} else {
								document.getElementById('shippingOrderFrame').src = response.message;
							}
							var jitBoxNumber = response.location;
							printCopies(jitPdfUrl,null,jitPrinter,1,jitBoxNumber);
						}else{
							layer.alert(response.message, {closeBtn: 0}, function (index) {
								layer.close(index);
							});
						}
					},
					error:function(){
						layer.alert('扫描失败，请重新扫描');
					}
				});
			} else {
				omsPrint(apvNo,printUrl);
			}
			$('#sku').val("");
			$("#sku").focus();
		}
		
		//打印失败面单
		function failPrint(){
			var apvNo = $("#apv-no-now-fail").val();
			if(apvNo){
				var printHtml='';
				var pageLength = "100mm";
				var pageWidth = "100mm";
				if (waybillSize && (waybillSize.indexOf('15') != -1 || waybillSize == '2')) {
					pageWidth = "150mm";
				}
				if (pickType == '9' || shipStatus == '2' || shipStatus == '12' || shipStatus == '14' || shipStatus == '20') {
					document.getElementById('oms_print_content').src = CONTEXT_PATH + "apv/packs/expressPrint?apvNo=" + apvNo;
					document.getElementById("oms_print_content").onload = function() {
						printHtml = document.getElementById("oms_print_content").contentWindow.document.getElementById('print_content').innerHTML;
						if (printHtml && printHtml.length > 0){
							printHtmlCopies(printHtml,pageLength,pageWidth, 1);
						}
					};
				} else {
					document.getElementById('shippingOrderFrame').src = CONTEXT_PATH + "apvs/failPrint?apvNo=" + apvNo;
					document.getElementById("shippingOrderFrame").onload = function() {
						printHtml = document.getElementById("shippingOrderFrame").contentWindow.document.getElementById('print_content').innerHTML;
						if (printHtml && printHtml.length > 0){
							printHtmlCopies(printHtml,pageLength,pageWidth, 1);
						}
					};
				}
			}
			$('#sku').focus();
		}
		
		// 手动打印
		function manualPrint() {
			// 自动打印
			var apvNo = $("#apv-no-now").val();
			printApvNo(apvNo);
			$("#sku").focus();
		}

		/**
		 *
		 * @param apvNo
		 * @param taskNo
		 * @param sku
		 * @param needQty
		 * @param suitSku
		 * @returns {number}
		 */
		function addScanSuitSkuQtyCache(apvNo, taskNo, sku, needQty,suitSku,allScan) {
			var storage = new WebStorageCache();
			var uuIdCacheKey = apvNo + "_" + taskNo + "_" + suitSku + "_" + sku;
			var scanTaskYSTSkuKey = "ScanLocalSuitSkuKey";
			var options = JSON.parse(storage.get(scanTaskYSTSkuKey));
			if (!options) {
				options = new Array();
			}
			//查找uuIdCacheKey是否存在
			var isExist = false;
			var qty = 0;
			for (var i = 0; i < options.length; i++) {
				var key = options[i].apvNo + "_" + options[i].taskNo + "_" + options[i].suitSku + "_" + options[i].sku;
				if (key.toUpperCase() == uuIdCacheKey.toUpperCase()) {
					isExist = true;
					qty = options[i].qty;
					if (allScan){
						qty = 0;
					}
					options[i].qty = qty + 1;
					break;
				}
			}
			qty = qty + 1;
			if (qty > needQty) {
				return qty;
			}
			if (isExist) {
				storage.set(scanTaskYSTSkuKey, JSON.stringify(options), {exp: 5 * 60 * 60});
				return qty;
			}
			var skuValue = {apvNo: apvNo,taskNo:taskNo,sku:sku,suitSku:suitSku,needQty:needQty, qty: qty};
			options.push(skuValue);
			storage.set(scanTaskYSTSkuKey, JSON.stringify(options), {exp: 5 * 60 * 60});
			return qty;
		}
	</script>
</body>
</html>