<style type="text/css">
    #pcak-info-table {
        width: 100%;
        margin-top: 10px;
    }
    #pcak-info-table #apvNo td {
        height: 36px;
        text-align: center;
    }
    #pcak-info-table .br td {
        height: 15px;
        border: none;
        background-color: transparent;
    }
    #pcak-info-table td {
        border: 1px solid #bcbcbc;
        width: 140px;
        height: 50px;
        padding-left: 10px;
        background-color: #F9F9F9;
        font-size: 18px;
    }
    #pcak-info-table td.form-label {
        text-align: center;
        background-color: #d7d7d7;
        font-weight: bold;
        font-size: 14px;
    }
    td.red {
        color: red;
    }
    .size-blod {
        font-weight: bold;
        font-size: 30px;
    }
    .hide-green td.red.size-blod {
        color: green;
    }
    .uncheck {
        width: 100%;
    }
    .uncheck th {
        text-align: center;
        background-color: #d7d7d7;
        font-weight: bold;
        font-size: 14px;
    }
    #check_scan_datas,#check_scan_yst{
        float: left;
    }
    #check_scan_yst{
        background-color: #f2f2f2;
    }
    .check_scan_yst_table{
        width: 70%;
        margin-top: 30px;
    }
    .check_scan_yst_table>thead{
        background-color: lightskyblue;
        font-weight: bold;
        font-size: 20px;
    }
    .check_scan_yst_table>tbody{
        font-size: 17px;
    }
    .check_sku{
        width: 50%;
    }
</style>
<div>
	<div id="check_scan_datas" class="border-gray p5 col-md-9">
		<#assign whApv = domain.whApv>
		<#assign skuMap = domain.skuMap>
        <#assign suitSkuMap = domain.localSuitSkuMap>
        <#if (skuMap)!>
            <div class="form-group">
                <div class="col-md-4" style="background-color: #f2f2f2;">
                    <table class="table-condensed table-bordered notice uncheck " style="background-color: white; width: 80%;margin-left: 10%;margin-top: 30px;margin-bottom: 20%;">
                        <tbody>
                        <tr>
                            <th align="center">SKU</th>
                            <th align="center">订单数量</th>
                            <th align="center">差异数量</th>
                            <th align="center">搭配耗材</th>
                        </tr>
                        <#list skuMap?keys as skuKey>
                            <tr class="show-red">
                                <td align="center" id="check_task_sku_${skuKey}">
                                    ${skuKey}
                                </td>
                                <td align="center">${skuMap[skuKey].saleQuantity}</td>
                                <td align="center" class="check_task_sku_quantity red" id="check_task_apv_${skuKey?upper_case?trim}">${skuMap[skuKey].saleQuantity}</td>
                                <#if (skuMap[skuKey].whSku.matchMaterialsName)?? && skuMap[skuKey].whSku.matchMaterialsName?index_of("充气条") != -1 >
                                    <td align="center" style="background-color: red;color: white;">${skuMap[skuKey].whSku.matchMaterialsName}</td>
                                <#elseif (skuMap[skuKey].whSku.matchMaterialsName)?? && skuMap[skuKey].whSku.matchMaterialsName?index_of("珍珠棉") != -1>
                                    <td align="center" style="background-color: #009DD9;color: white;">${skuMap[skuKey].whSku.matchMaterialsName}</td>
                                <#elseif (skuMap[skuKey].whSku.matchMaterialsName)?? && skuMap[skuKey].whSku.matchMaterialsName?index_of("充气袋") != -1>
                                    <td align="center" style="background-color: #FFA500FF;color: white;">${skuMap[skuKey].whSku.matchMaterialsName}</td>
                                <#else>
                                    <td align="center">${skuMap[skuKey].whSku.matchMaterialsName}</td>
                                </#if>
                            </tr>
                        </#list>
                        </tbody>
                    </table>
                    <#if (suitSkuMap)!>
                        <table class="table-condensed table-bordered notice uncheck " style="background-color: white; width: 80%;margin-left: 10%;margin-top: 30px;margin-bottom: 20%;">
                            <tbody>
                            <tr>
                                <th align="center">套装SKU</th>
                                <th align="center">套装数量</th>
                                <th align="center">套装明细</th>
                            </tr>
                            <#list suitSkuMap?keys as suitSku>
                                <#assign items = suitSkuMap[suitSku]>
                                <#list items as suitItem>
                                    <tr class="show-red">
                                        <#if suitItem_index ==0>
                                            <td rowspan="${items ? size}" align="center" id="check_task_suit_sku_${suitSku}">
                                                ${suitSku}
                                            </td>
                                            <td rowspan="${items ? size}" align="center">${suitItem.suitNum}</td>
                                        </#if>
                                        <td align="center">
                                            ${suitItem.sku}*${suitItem.suitQty}
                                            <#if suitSku != '普通'>
                                                <input type="hidden" id="check_task_suit_sku_${suitItem.sku}" value="${suitSku}">
                                            </#if>
                                        </td>
                                    </tr>
                                </#list>
                            </#list>
                            </tbody>
                        </table>
                    </#if>
                </div>
                <#if (domain.whApvItem)!>
                    <#assign item = domain.whApvItem>
                    <#assign sku = item.sku?upper_case?trim>
                    <div id="check_scan_datas_d" class="col-md-8">
                        <input type="hidden" name="apvId" value="${domain.whApv.id }" />
                        <input type="hidden" name="apvNo" value="${domain.whApv.apvNo }" />
                        <input type="hidden" name="shipService" value="${domain.whApv.shipService }" />
                        <input type="hidden" name="shipStatus" value="${domain.whApv.shipStatus }" />
                        <input type="hidden" name="totalSaleQuantity" value="${domain.whApv.totalSaleQuantity }" />
                        <input type="hidden" name="logisticsType" value="${domain.whApv.logisticsType }" />

                        <input type="hidden" name="logisticsCompany" value="${domain.whApv.logisticsCompany }" />
                        <input type="hidden" name="taskNo" value="${domain.taskNo }" />
                        <input type="hidden" name="location" value="${domain.location }" />
                        <input type="hidden" name="waybillSize" value="${domain.waybillSize }" />
                        <input type="hidden" name="specialSkuStr" value="${whApv.buyerCheckout}" />
                        <#if whApv.buyerCheckoutList?seq_contains('GPSR')>
                            <input type="hidden" name="gpsrPlatform" value="${whApv.platform}"/>
                        </#if>
                        <table class="table table-condensed">
                            <tbody>
                                <tr>
                                    <td class="error" style="border: none">
                                        <table id="pcak-info-table">
                                            <tbody>
                                                <tr class="br"><td colspan="4"></td></tr>
                                                <#if whApv.buyerCheckoutList?seq_contains('GPSR')>
                                                    <tr class="br"><td colspan="5"><span style="color: red;font-size: 30px;font-weight: bold; margin-left: 20px">GPSR包装</span> </td></tr>
                                                </#if>
                                                <tr id="apvNo">
                                                    <td class="form-label">发货单号</td>
                                                    <#if whApv.buyerCheckoutList?seq_contains('RFP')>
                                                        <td colspan="2"  class="size-blod">${whApv.apvNo}</td>
                                                        <td  class="size-blod">
                                                            <span style="background-color: red;border-right: none;font-weight:900; font-size: 20px;width: 200px;line-height: 30px;">
                                                                ${util('enumName', 'com.estone.apv.enums.ApvTaxTypeEnum', 'RFP')}
                                                            </span>
                                                        </td>
                                                    <#else>
                                                        <td colspan="3"  class="size-blod">${whApv.apvNo}</td>
                                                    </#if>
                                                </tr>
                                                <tr class="br"><td colspan="4"></td></tr>
                                                <tr class="br"><td colspan="4"></td></tr>
                                            </tbody>
                                        </table>
                                        <#if (suitSkuMap)! && item.spu??>
                                        <#assign suitItems = suitSkuMap[item.spu]>
                                        <table class="table-condensed table-bordered notice uncheck ">
                                            <tbody>
                                            <tr class="show-red">
                                                <td style="vertical-align: middle;text-align: center;background: orange;font-weight: bolder;">套装SKU</td>
                                                <td align="center" style="vertical-align: middle;" id="check_suit_sku">${item.spu? upper_case ? trim}</td>
                                                <td style="vertical-align: middle;text-align: center;background: orange;font-weight: bolder;"><input type="hidden" id="skuBarcode" value="${item.skuBarcode}">套装明细</td>
                                                <td align="center" style="vertical-align: middle;">
                                                    <#list suitItems as suitItem>
                                                        <dd>${suitItem.sku}*${suitItem.suitQty}</dd>
                                                    </#list>
                                                </td>
                                            </tr>
                                            </tbody>
                                        </table>
                                        <table class="table-condensed notice uncheck " style="margin-top: 10px;">
                                            <tbody>
                                            <td align="center" class="size-blod">
                                                <table class="table-condensed table-bordered notice uncheck ">
                                                    <tbody>
                                                    <tr>
                                                        <th align="center" style="background: orange;">套装订单数量</th>
                                                        <th align="center" style="background: orange;">套装扫描数量</th>
                                                        <th align="center"></th>
                                                    </tr>
                                                    <tr class="show-red">
                                                        <td align="center" class="size-blod red" id="check_suit_sku_${item.spu? upper_case ? trim}_need">${item.suitNum}</td>
                                                        <td align="center" class="size-blod" id="check_suit_sku_${item.spu? upper_case ? trim}_check">0</td>
                                                        <td align="center">
                                                            <table class="table-condensed table-bordered notice uncheck ">
                                                                <tbody>
                                                                <#list suitItems as suitItem>
                                                                    <tr class="form-label check_suit_sku_list">
                                                                        <td>
                                                                            <table class="table-condensed table-bordered notice uncheck ">
                                                                                <tbody>
                                                                                <tr>
                                                                                    <th align="center">SKU</th>
                                                                                    <th align="center">
                                                                                        单个套装数量
                                                                                    </th>
                                                                                    <th align="center">
                                                                                        单个套装扫描数量
                                                                                    </th>
                                                                                </tr>
                                                                                <tr class="show-red">
                                                                                    <td align="center" class="check_sku size-blod" id="check_sku_${suitItem.sku? upper_case ? trim}">
                                                                                        ${suitItem.sku? upper_case ? trim}
                                                                                        <a class="btn yellow" href="javascript:void(0);" onclick="createPackagingErrorInformation('${sku}')">包装信息纠错</a>
                                                                                    </td>
                                                                                    <td align="center" class="size-blod need_quantity" id="check_need_qty_${suitItem.sku ? upper_case ? trim}">${suitItem.suitQty }</td>
                                                                                    <td align="center" class="check_quantity size-blod red"id="check_quantity_${suitItem.sku ? upper_case ? trim}">0</td>
                                                                                </tr>
                                                                                </tbody>
                                                                            </table>
                                                                            <table id="pcak-info-table">
                                                                                <tbody>
                                                                                <tr class="br">
                                                                                    <td colspan="4"></td>
                                                                                </tr>
                                                                                <tr>
                                                                                    <td class="form-label">
                                                                                        产品特性
                                                                                    </td>
                                                                                    <td class="size-blod">
                                                                                                <span class="size-blod">
                                                                                                    <#list domain.whSkuWithPmsInfos as skuWithPms>
                                                                                                        <#if skuWithPms.sku == suitItem.sku>
                                                                                                            ${skuWithPms.skuLabelName }
                                                                                                            <#if skuWithPms.tag?? && skuWithPms.tag?index_of("磁性")!=-1>
                                                                                                                <#if skuWithPms.skuLabelName??>
                                                                                                                    <br></#if>${skuWithPms.tag }
                                                                                                            </#if>
                                                                                                        </#if>
                                                                                                    </#list>
                                                                                                </span>
                                                                                        <#if domain.skuTagMap?? && domain.skuTagMap[suitItem.sku]??>
                                                                                            <span style="margin-left: 20px;color: red;">,${domain.skuTagMap[suitItem.sku]}</span>
                                                                                        </#if>
                                                                                    </td>
                                                                                    <td class="form-label">
                                                                                        包材
                                                                                    </td>
                                                                                    <td class="size-blod">${suitItem.whSku.packagingName}</td>
                                                                                </tr>
                                                                                <tr>
                                                                                    <td class="form-label">
                                                                                        辅助耗材
                                                                                    </td>
                                                                                    <#if (suitItem.whSku.matchMaterialsName)?? && suitItem.whSku.matchMaterialsName?index_of("充气条") != -1 >
                                                                                        <td colspan="3"
                                                                                            class="size-blod"
                                                                                            style="font-size: 25px;background-color: red;color: white;">${suitItem.whSku.matchMaterialsName}</td>
                                                                                    <#elseif (suitItem.whSku.matchMaterialsName)?? && suitItem.whSku.matchMaterialsName?index_of("珍珠棉") != -1>
                                                                                        <td colspan="3"
                                                                                            class="size-blod"
                                                                                            style="font-size: 25px;background-color: #009DD9;color: white;">${suitItem.whSku.matchMaterialsName}</td>
                                                                                    <#elseif (suitItem.whSku.matchMaterialsName)?? && suitItem.whSku.matchMaterialsName?index_of("充气袋") != -1>
                                                                                        <td colspan="3"
                                                                                            class="size-blod"
                                                                                            style="font-size: 25px;background-color: #FFA500FF;color: white;">${suitItem.whSku.matchMaterialsName}</td>
                                                                                    <#else>
                                                                                        <td colspan="3"
                                                                                            class="size-blod"
                                                                                            style="font-size: 25px;">${suitItem.whSku.matchMaterialsName}</td>
                                                                                    </#if>
                                                                                </tr>
                                                                                </tbody>
                                                                            </table>
                                                                        </td>
                                                                    </tr>
                                                                </#list>
                                                                </tbody>
                                                            </table>
                                                        </td>
                                                    </tr>
                                                    </tbody>
                                                </table>
                                            </td>
                                            </tbody>
                                        </table>
                                        <#else >
                                        <table class="table-condensed table-bordered notice uncheck ">
                                            <tbody>
                                                <tr>
                                                    <th align="center">SKU</th>
                                                    <th align="center">订单数量</th>
                                                    <th align="center">扫描数量</th>
                                                </tr>
                                                <tr class="show-red">
                                                    <td class="check_sku" id="check_sku_${sku}">
                                                        ${sku}
                                                        <input type="hidden" id="skuBarcode" value="${item.skuBarcode}">
                                                        <a class="btn yellow" href="javascript:void(0);" onclick="createPackagingErrorInformation('${sku}')">包装信息纠错</a>
                                                    </td>
                                                    <td align="center" class="size-blod" id="check_need_qty_${sku}">${item.saleQuantity }</td>
                                                    <td align="center" class="check_quantity size-blod red" id="check_quantity_${sku}">0</td>
                                                </tr>
                                            </tbody>
                                        </table>
                                        <table id="pcak-info-table">
                                            <tbody>
                                                <tr class="br"><td colspan="4"></td></tr>
                                                <tr>
                                                    <td class="form-label">产品特性</td>
                                                    <td class="size-blod">
                                                        <span class="size-blod">
                                                            <#list domain.whSkuWithPmsInfos as skuWithPms>
                                                                <#if skuWithPms.sku == item.sku>
                                                                    ${skuWithPms.skuLabelName }
                                                                    <#if skuWithPms.tag?? && skuWithPms.tag?index_of("磁性")!=-1>
                                                                        <#if skuWithPms.skuLabelName??>
                                                                            <br></#if>${skuWithPms.tag }
                                                                    </#if>
                                                                </#if>
                                                            </#list>
                                                        </span>
                                                        <#if domain.skuTagMap?? && domain.skuTagMap[item.sku]??>
                                                            <span style="margin-left: 20px;color: red;">,${domain.skuTagMap[item.sku]}</span>
                                                        </#if>
                                                    </td>
                                                    <td class="form-label">包材</td>
                                                    <td class="size-blod">${item.whSku.packagingName}</td>
                                                </tr>
                                                <tr>
                                                    <td class="form-label">辅助耗材</td>
                                                    <#if (item.whSku.matchMaterialsName)?? && item.whSku.matchMaterialsName?index_of("充气条") != -1 >
                                                        <td colspan="3" class="size-blod" style="font-size: 25px;background-color: red;color: white;">${item.whSku.matchMaterialsName}</td>
                                                    <#elseif (item.whSku.matchMaterialsName)?? && item.whSku.matchMaterialsName?index_of("珍珠棉") != -1>
                                                        <td colspan="3" class="size-blod" style="font-size: 25px;background-color: #009DD9;color: white;">${item.whSku.matchMaterialsName}</td>
                                                    <#elseif (item.whSku.matchMaterialsName)?? && item.whSku.matchMaterialsName?index_of("充气袋") != -1>
                                                        <td colspan="3" class="size-blod" style="font-size: 25px;background-color: #FFA500FF;color: white;">${item.whSku.matchMaterialsName}</td>
                                                    <#else>
                                                        <td colspan="3" class="size-blod" style="font-size: 25px;">${item.whSku.matchMaterialsName}</td>
                                                    </#if>
                                                </tr>
                                            </tbody>
                                        </table>
                                        </#if>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </#if>
            </div>
        <#elseif (domain.errorMsg)!>
            <div style="font-size:15px ;font-weight:bold" class="error scan_error" id="scan-error">${domain.errorMsg}</div>
        <#else >
            <div style="font-size:15px ;font-weight:bold" class="error scan_error" id="scan-error">产品:${param['sku'] }  不存在或者不在[待包装]状态中!</div>
        </#if>
	</div>
    <div id="check_scan_yst" class="col-md-3">
        <table class="check_scan_yst_table" border="1" align="center">
            <thead>
            <tr align="center">
                <td>同波次订单信息
                    <br/>
                    <span style="font-size: 14px;">红色：未包装 ；  黑色：包装完成</span>
                </td>
            </tr>
            </thead>
            <tbody align="center" id="check_scan_yst_tr">
            <#if (domain)!>
                <#assign apvList = domain.apvList>
                <#if (apvList)!>
                    <#list apvList as apv>
                        <tr id="${apv.status}" align="center">
                            <#if apv.status gt 15>
                                <td>${apv.apvNo}</td>
                            <#else>
                                <td style="color: red">${apv.apvNo}</td>
                            </#if>
                        </tr>
                    </#list>
                </#if>
            </#if>
            </tbody>
        </table>
    </div>
</div>
<script type="text/javascript">
    function createPackagingErrorInformation(sku){
        var content = `<span>SKU:`+sku+`</span>
                           <br/>
                           <br/>
                           <div>
                               <h4 class="modal-title" id="selectDownloadHeadersModalLabel">错误信息：</h4>
                               <div id="table-header" style="width:330px;">
                                   <label class="radio-inline" style="width:100px;">
                                       <input type="checkbox" name="questionFields" value="1">包材
                                   </label>
                                   <label class="radio-inline" style="width:100px;">
                                       <input type="checkbox" name="questionFields" value="2">搭配包材
                                   </label>
                                   <label class="radio-inline" style="width:100px;">
                                       <input type="checkbox" name="questionFields" value="3">包装图片
                                   </label>
                               </div>
                           </div>`;
        var diglog = dialog({
            title: '包装信息纠错',
            width: 350,
            height:100,
            content:content,
            okValue: '确定',
            ok: function () {
                var questionFields = [];
                var questions = $("input[name='questionFields']:checked");
                if (!questions || questions.length == 0){
                    layer.alert("未选择错误类型!","error");
                    return;
                }
                $.each(questions,function(index,item){
                    var val = $(item).val();
                    questionFields.push(val);
                });
                var questionStr = "";
                for (var i = 0; i < questionFields.length; i++) {
                    var questionField = questionFields[i];
                    questionStr += questionField;
                    if (i != questionFields.length - 1) {
                        questionStr += ",";
                    }
                }
                $.ajax({
                    url: CONTEXT_PATH + "/error/information/create",
                    type: "post",
                    data: {sku:sku,questionFields:questionStr},
                    success:function(response){
                        if (response.status == '500') {
                            customizeLayer(response.message, 'error');
                        } else{
                            alert('已创建纠错信息!');
                        }
                    },
                    error:function () {
                        layer.alert("系统异常，操作失败!",'error');
                    }
                });
            },
            cancelValue: '取消',
            cancel: function () {}
        });
        diglog.show();
    }
</script>