<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">
<html>
<head>
	<title>易世通达仓库管理系统</title>
	<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
	<#include "/common/include.html">
	<link rel="stylesheet" type="text/css" href="${CONTEXT_PATH}css/my-select.css?v=${.now?datetime}">
	<style type="text/css">
		.modal-body input {
			width: 240px;
		}
		.top_bar{
			position:fixed;top:0px;
		}
		#task-list td {
			vertical-align:middle;
		}
		.exception-modal{
			margin-top: 10px;
		}
	</style>
</head>
<body>
<@header method="header" active="10010000"><#include "/ftl/header.ftl"></@header>

<div id="page" style="background-color: rgb(231, 237, 248)">
	<div class="row">
		<div class="col-md-12" style="padding: 0">
			<ul class="page-breadcrumb breadcrumb" style="background-color: white;">
				<li><a href="#">到货管理</a></li>
				<li class="active">快递查询</li>
			</ul>
		</div>
	</div>
	<!-- 内容 -->
	<div class="container-fluid" style="background-color: white;border: none">
	<div class="row">
		<div class="col-md-12">
			<#assign query = domain.query>
			<form action="${CONTEXT_PATH}expressRecords/search"
					   class="form-horizontal form-bordered form-row-stripped"
					   method="post" modelAttribute="domain" name="expressRecordForm" id ="domain">
				<!-- 分页信息 -->
				<input id="page-no" type="hidden" name="page.pageNo" value="1"> 
				<input id="page-size" type="hidden" name="page.pageSize" value="${domain.page.pageSize}">
				<div class="form-body">
					<div class="form-group">
						<label class="control-label col-md-1">签收人</label>
						<div class="col-md-3">
							<input class="form-control" name="query.createdBy" type="text" value="${query.createdBy}">
						</div>
						<label class="control-label col-md-1">拆分人</label>
						<div class="col-md-3">
							<input class="form-control" name="query.splitUser" type="text" value="${query.splitUser}">
						</div>
						<label class="control-label col-md-1">收货时间</label>
						<div class="col-md-3">
							<div class="input-group">
								<input class="form-control Wdate" type="text" name="query.fromCreationDate" placeholder="" readonly="readonly" value="${query.fromCreationDate }" onfocus="WdatePicker({startDate:'%y-%M-%d 00:00:00',dateFmt:'yyyy-MM-dd HH:mm:ss',alwaysUseStartDate:true})">
								<span class="input-group-addon">到</span>
								<input class="form-control Wdate" type="text" name="query.toCreationDate" placeholder="" readonly="readonly" value="${query.toCreationDate }" onfocus="WdatePicker({startDate:'%y-%M-%d 23:59:59',dateFmt:'yyyy-MM-dd HH:mm:ss',alwaysUseStartDate:true})">
							</div>
						</div>
					</div>

					<div class="form-group">
						<label class="control-label col-md-1">拆分时间</label>
						<div class="col-md-3">
							<div class="input-group">
								<input class="form-control Wdate" type="text" name="query.fromSplitDate" placeholder="" readonly="readonly" value="${query.fromSplitDate }" onfocus="WdatePicker({startDate:'%y-%M-%d 00:00:00',dateFmt:'yyyy-MM-dd HH:mm:ss',alwaysUseStartDate:true})">
								<span class="input-group-addon">到</span>
								<input class="form-control Wdate" type="text" name="query.toSplitDate" placeholder="" readonly="readonly" value="${query.toSplitDate }" onfocus="WdatePicker({startDate:'%y-%M-%d 23:59:59',dateFmt:'yyyy-MM-dd HH:mm:ss',alwaysUseStartDate:true})">
							</div>
						</div>
						
						<label class="control-label col-md-1">是否拆分</label>
						<div class="col-md-3">
							<select name="query.isSplitStr" class="form-control">
								<option vlaue=""></option>
								<#list domain.isSplitList as status >
									<#if query.isSplitStr == status[0]>
											<option selected="selected" value="${status[0]}">${status[0]} - ${status[1]}</option>
										<#else>
											<option value="${status[0]}">${status[0]} - ${status[1]}</option>
									</#if>
								</#list>
							</select>
						</div>
						
						<label class="control-label col-md-1">快递单号</label>
						<div class="col-md-3">
							<input class="form-control" type="text" name="query.trackingNumber" placeholder="请输入快递单号（多个以英文逗号隔开）" value="${query.trackingNumber }">
						</div>
					</div>

					<div class="form-group">
						<label class="control-label col-md-1">是否绑定采购单</label>
						<div class="col-md-3">
							<select name="query.hasPurchaseOrderNoStr" class="form-control">
								<option vlaue=""></option>
								<#list domain.hasPurchaseOrderNoList as status>
									<#if query.hasPurchaseOrderNoStr == status[0]>
											<option selected="selected" value="${status[0]}">${status[0]} - ${status[1]}</option>
										<#else>
											<option value="${status[0]}">${status[0]} - ${status[1]}</option>
									</#if>
								</#list>
							</select>
						</div>
						
						<label class="control-label col-md-1">采购单号</label>
						<div class="col-md-3">
							<input class="form-control" type="text" name="query.purchaseOrderNo" placeholder="请输入采购单号（仅支持单个单号模糊匹配查询）" value="${query.purchaseOrderNo }">
						</div>
						
						<label class="control-label col-md-1">件数</label>
						<div class="col-md-3">
							<input class="form-control" type="text" name="query.quantity" placeholder="请输入件数" value="${query.quantity }">
						</div>
					</div>
					
					<div id="expand-area">
						<div class="form-group">
							<label class="control-label col-md-1">是否扫描</label>
							<div class="col-md-3">
								<select class="form-control" id = "checkInScanStatus" name="query.checkInScanStatus" value="${query.checkInScanStatus}">
									<option vlaue=""></option>
									<option <#if query.checkInScanStatus == true>selected</#if> value="true">是</option>
									<option <#if query.checkInScanStatus?? && query.checkInScanStatus == false>selected</#if> value="false">否</option>
								</select>
							</div>

							<label class="control-label col-md-1">是否备注</label>
							<div class="col-md-3">
								<select name="query.hasCommentStr" class="form-control">
									<option vlaue=""></option>
									<#list domain.hasCommentList as status>
										<#if query.hasCommentStr == status[0]>
											<option selected="selected" value="${status[0]}">${status[0]} - ${status[1]}</option>
											<#else>
												<option value="${status[0]}">${status[0]} - ${status[1]}</option>
										</#if>
									</#list>
								</select>
							</div>

							<label class="control-label col-md-1">周转框</label>
							<div class="col-md-3">
								<input class="form-control" type="text" name="query.boxNo" placeholder="请输入周转框（多个以英文逗号隔开）" value="${query.boxNo }">
							</div>
						</div>

						<div class="form-group">
							<label class="control-label col-md-1">类型</label>
							<div class="col-md-3">
								<select name="query.purchaseType" class="form-control">
									<option vlaue=""></option>
									<#list domain.purchaseTypeList as status>
										<#if query.purchaseType == status[0]>
											<option selected="selected" value="${status[0]}">${status[0]} - ${status[1]}</option>
											<#else>
												<option value="${status[0]}">${status[0]} - ${status[1]}</option>
										</#if>
									</#list>
								</select>
							</div>

							<label class="control-label col-md-1">快递公司</label>
							<div class="col-md-3">
								<input class="form-control" type="text" name="query.shippingCpn" value="${query.shippingCpn }">
							</div>
							<label class="control-label col-md-1">是否本仓包裹</label>
							<div class="col-md-3">
								<select name="query.localWarehouseBag" class="form-control">
									<option value=""></option>
									<option value="true" ${(query.localWarehouseBag?? && query.localWarehouseBag == true)?string('selected', '')}>是</option>
									<option value="false" ${(query.localWarehouseBag?? && query.localWarehouseBag == false)?string('selected', '')}>否</option>
								</select>
							</div>
						</div>

					</div>
				</div>
				
				<div>
					<div class="pull-left" >
                        <@header method="auth" authCode="EXPRESS_DELIVERY_BATCH_REFRESH_PURCHASE_ORDERS">
						<div class="btn-group">
							<button type="button" class="btn btn-default dropdown-toggle" data-toggle="dropdown">
								批量刷新采购单 <i class="icon-angle-down"></i>
							</button>
							<ul class="dropdown-menu">
								<li><a href="#" onclick="batchUpdatePurchaseOrderNo()">批量刷新采购单</a></li>
							</ul>
						</div>
                        </@header>
                        <@header method="auth" authCode="EXPRESS_DELIVERY_SOLO_GENERATE_EXCEPTION">
                            <button type="button" class="btn  blue" id="createExceptionOrder">
                                单个生成异常
                            </button>
                        </@header>
						<@header method="auth" authCode="EXPRESS_DELIVERY_REJUECT_RECEIVE">
                            <button type="button" class="btn  blue" id="rejectPurchaseExpressRecord">
                                拒收
                            </button>
						</@header>
						<@header method="auth" authCode="EXPRESS_DELIVERY_UNBIND_ORDER">
							<button type="button" class="btn  blue" id="unbindExpressRecord">
								解绑快递单
							</button>
						</@header>
                        <@header method="auth" authCode="EXPRESS_DELIVERY_BATCH_REMARKS">
                            <button type="button" id="batchComment" class="btn blue">批量备注</button>
                        </@header>
						<@header method="auth" authCode="EXPRESS_DELIVERY_PARCEL_UNBIND">
							<button type="button" id="parcelUnbind" class="btn blue">未称重包裹解绑</button>
						</@header>
					</div>
					<div class="col-md-offset-12" style="text-align: right">
						<button type="button" id="expandFn" class="btn blue">收起</button>
						<button type="submit" class="btn blue">
							<i class="icon-search"></i> 查询
						</button>
						<button type="button" onclick="formReset(this)" class="btn btn-default">
							<i class="icon-refresh"></i> 重置
						</button>
                        <@header method="auth" authCode="EXPRESS_DELIVERY_DOWNLOAD">
						<button type="button" class="btn btn-default" onclick="downloadRecord()">
							<i class="icon-download"></i> 导出
						</button>
                        </@header>
					</div>
				</div>
			</form>
		</div>
		<br/>
	</div>

	<div class="row">
		<div id="fixedDiv" class="col-md-12">
			<table class="table table-striped table-bordered table-hover table-condensed" id="fixedTab">
				<colgroup>
					<col width="4%" />
					<col width="5%" />
					<col width="6%" />
					<col width="6%" />
					<col width="4%" />
					<col width="5%" />
					<col width="5%" />
					<col width="3%" />
					<col width="3%" />
					<col width="5%" />
					<col width="5%" />
					<col width="5%" />
					<col width="5%" />
					<col width="5%" />
					<col width="5%" />
					<col width="5%"/>
					<col width="4%" />
					<col width="5%" />
					<col width="5%" />
					<col width="4%" />
					<col width="5%" />
					<col width="6%" />
				</colgroup>
				<thead>
				<tr>
					<th>全选</th>
					<th>图片</th>
					<th>快递单号</th>
					<th>采购单号</th>
					<th>快递公司</th>
					<th>采购员</th>
					<th>入库异常</th>
					<th>实重(kg)</th>
					<th>件数</th>
					<th>周转框<br><span style="color: red">领取人</span></th>
					<th>签收人</th>
					<th>签收时间</th>
					<th>领取人</th>
					<th>领取时间</th>
					<th>扫描人</th>
					<th>扫描时间 </th>
					<th>扫描状态 </th>
					<th>拆分人</th>
					<th>拆分时间 </th>
					<th>标签</th>
					<th>备注</th>
					<th>操作</th>
				</tr>
				</thead>
			</table>
		</div>
		<div class="col-md-12" id="task-list-warp">
			<!-- 内容 -->
			<table class="table table-striped table-bordered table-hover table-condensed" id="task-list">
				<colgroup>
					<col width="4%" />
					<col width="5%" />
					<col width="6%" />
					<col width="6%" />
					<col width="4%" />
					<col width="5%" />
					<col width="5%" />
					<col width="3%" />
					<col width="3%" />
					<col width="5%" />
					<col width="5%" />
					<col width="5%" />
					<col width="5%" />
					<col width="5%" />
					<col width="5%" />
					<col width="5%"/>
					<col width="4%" />
					<col width="5%" />
					<col width="5%" />
					<col width="4%" />
					<col width="5%" />
					<col width="6%" />
				</colgroup>
				<thead>
				<tr>
					<th><input type="checkbox"  id="check-all" name="checkAll">全选</th>
					<th>图片</th>
					<th>快递单号</th>
					<th>采购单号</th>
					<th>快递公司</th>
					<th>采购员</th>
					<th>入库异常</th>
					<th>实重(kg)</th>
					<th>件数</th>
					<th>周转框<br><span style="color: red">领取人</span></th>
					<th>签收人</th>
					<th>签收时间</th>
					<th>领取人</th>
					<th>领取时间</th>
					<th>扫描人</th>
					<th>扫描时间 </th>
					<th>扫描状态 </th>
					<th>拆分人</th>
					<th>拆分时间 </th>
					<th>标签</th>
					<th>备注</th>
					<th>操作</th>
				</tr>
				</thead>
				<tbody>
				<#list domain.whPurchaseExpressRecords as expressRecord >
					<tr>
						<td>
							<#if expressRecord.status != 1>
								<input type="checkbox" value="${expressRecord.id}" name="ids">
								${expressRecord.id}
							<#else >
								${expressRecord.id}<br><span style="color: red">拒收单</span>
							</#if>
						</td>
						<td><#if expressRecord.imageUrl??><img src="${expressRecord.imageUrl}" width="50px" height="50px;" onclick="enlarge(this)" /></#if></td>
						<td name="trackingNumber">${expressRecord.trackingNumber}
                        <br/>
							<#if expressRecord.serialNumber == 1 && expressRecord.flags?contains("TJ")>
								(物流、
								<span style="color: red;font-size: 14px;font-weight: 600">特急</span>)
							<#else>
								<#if expressRecord.serialNumber == 1>
									(物流)
								</#if>
								<#if expressRecord.flags?contains("TJ")>
									<span style="color: red;font-size: 14px;font-weight: 600">(特急)</span>
								</#if>
							</#if>
							<#if expressRecord.scanMark?? && expressRecord.scanMark == 1>
								<br/><span style="color: orange;font-size: 14px;font-weight: 600">(非本仓包裹)</span>
							</#if>
                        </td>
						<td name="purchaseOrderNo">${expressRecord.purchaseOrderNo}</td>
						<td>${expressRecord.shippingCpn}</td>
						<td>${expressRecord.purchaseUserName}</td>
						<td>
							<#if expressRecord.checkinExceptions??>
								<#list expressRecord.checkinExceptions as whCheckInException>
									${whCheckInException.exceptionTypeName}/${whCheckInException.exceptionStatusName}
									<br/>
								</#list>
							</#if>
						</td>
						<td>${expressRecord.weight}</td>
						<td>${expressRecord.quantity}</td>
						<td>
						    ${expressRecord.boxNo}<br>
							<#if expressRecord.boxNo??>
								<span style="color: red">${util('name',expressRecord.receiveUser)}</span>
							</#if>
						</td>
						<td>${util('name',expressRecord.createdBy)}</td>
						<td>${expressRecord.creationDate }</td>
						<td>${util('name',expressRecord.receiveUser)}</td>
						<td>${expressRecord.receiveDate }</td>
						<td>${util('name',expressRecord.checkInScanner)}</td>
						<td>${expressRecord.checkInScanTime }</td>
						<td>${(expressRecord.checkInScanStatus== true)?string('已扫描','未扫描')}</td>
						<td>${util('name',expressRecord.splitUser)}</td>
						<td>${expressRecord.splitDate }</td>
						<td>
							<#if expressRecord.logisticsMark??>
								该包裹有补发配件
							</#if>
						</td>
						<td>${expressRecord.comment }</td>
						<td>
							<button type="button" class="btn btn-xs btn-info" onclick="viewLog(${expressRecord.id}, 'expressRecord')">日志</button>
							<#if expressRecord.status != 1>
								<a class="btn btn-xs btn-default" onclick="editFunc(this)" url="${CONTEXT_PATH}expressRecords/update?whPurchaseExpressRecordId=${expressRecord.id }">
									<span class="icon-edit"></span>  编辑
								</a>
								<#if !expressRecord.purchaseOrderNo >
									<button type="button" class="btn btn-info btn-xs" onclick="updatePurchaseOrderNo('${expressRecord.id}')">刷新采购单</button>
								</#if>
							</#if>
						</td>
					</tr>
				</#list>
				</tbody>
			</table>
			<!-- 内容end -->
		</div>
	</div>
	<div id="fixed-bottom">
		<div id="pager"></div>
	</div>
</div>

	<div id="dialog-form" title="生成异常" style="display: none">
		<form>
			<fieldset>
				<div class="form-group">
					<label class="control-label col-md-4 exception-modal">不良品周转码</label>
					<div class="col-md-6 exception-modal">
						<input type="text" class="form-control" name="exceptionBoxNo" id="exceptionBoxNo"
							   value="" onkeypress="if(event.keyCode==13) { checkBox(this,'exception'); return false;}"tabindex="4">
						<span style="color: red;font-size: 20px;" class="span-boxNo"></span>
					</div>
				</div>
				<div class="form-group">
					<label class="control-label col-md-4 exception-modal">采购员</label>
					<input class="select-input" id="purchaseUser" name="purchaseUser" type="hidden" value="">
					<div class="col-md-6 exception-modal mySelect select-lt" placeholder="采购员" value=""></div>
				</div>
			</fieldset>
		</form>
	</div>
    <div id="reject-form" title="拒收" style="display: none">
        <form>
            <fieldset>
                <div class="form-group">
                    <label class="control-label col-md-4 exception-modal">拒收原因</label>
                    <div class="col-md-6 exception-modal">
                        <input class="form-control" id="rejectComment" name="rejectComment" type="text" value="">
                    </div>
                </div>
            </fieldset>
        </form>
    </div>
	<div style="display:none;" id="shippingMethodList">
		<#if domain.shippingMethodList?? >
			[{"id":"", "text":""}<#list domain.shippingMethodList as item>,{"id":"${item}", "text":"${item}"}</#list>]
		</#if>
	</div>
	<div id="parcelUnbind_head" title="未称重包裹解绑" style="display: none">
		<form>
			<fieldset>
				<div class="form-group">
					<label class="control-label col-md-3 exception-modal">解绑人</label>
					<div class="col-md-6 exception-modal">
						<input class="form-control" id="parcelUnbind_user" name="parcelUnbind" type="text" value="">
					</div>
				</div>
			</fieldset>
		</form>
	</div>
	<#include "/common/footer.html">
</div>
<div style="display:none;" id="purchase-user">
	[{"id":"", "text":""}<#list domain.purchaseUsersList as purchaseUser>,{"id":"${purchaseUser.id}", "text":"${purchaseUser.userName} - ${purchaseUser.employeeName}"}</#list>]
</div>
<script type="text/javascript" src="${CONTEXT_PATH}js/datepicker/WdatePicker.js"></script>
<script type="text/javascript" src="${CONTEXT_PATH}js/table-thead-fixed.js"></script>
<script type="text/javascript" src="${CONTEXT_PATH}js/assets/plugins/jquery.serializejson.min.js"></script>
<script type="text/javascript" src="${CONTEXT_PATH}js/prompt-message.js"></script>
<script type="text/javascript" src="${CONTEXT_PATH}js/my-select.js?v=${.now?datetime}"></script>
<script type="text/javascript">
    $('#expandFn').on('click', function (event) {
        if ($(this).hasClass('expand')) {
            $(this).text('收起');
            $('#expand-area').slideDown(200);
        } else {
            $(this).text('展开');
            $('#expand-area').slideUp(200);
        }
        $(this).toggleClass('expand');
        event.preventDefault();
    });

    var purchaseUserArry = jQuery.parseJSON($('#purchase-user').text());
	var mySelect = $('.mySelect').selectlt({
		isdisabled:false,
		isfiltration:true,
		selectedValue:'0',
		data:purchaseUserArry
	});
	//选中事件
	mySelect.on('select-change', function (e, data) {
		console.log('选中值:', data.data.value);
		$("input[name='purchaseUser']").val(data.data.value);
	});

	var shippingMethodArr = jQuery.parseJSON($("#shippingMethodList").text());
	$("input[name='query.shippingCpn']").select2({
		data : shippingMethodArr,
		placeholder : "快递公司",
		allowClear : true
	});

    // 分页
    var total = "${domain.page.totalCount}";
	var pageNo = "${domain.page.pageNo}";
	var pageSize = "${domain.page.pageSize}";
	$("#pager").initPager({ total : total, pageNo : pageNo, pageSize : pageSize});
    var heights = $("body").height();
    if(heights > 910) {
        $("#fixed-bottom").addClass("fixed-bottom-height")
    }

	// 签收人 =快递收货页面权限
	$.getJSON(CONTEXT_PATH + "system/saleusers/userByPermissionCode?permissionCode=10010100", function(json){
		if (json) {
			$("input[name='query.createdBy']").select2({
				data : json,
				placeholder : "签收人",
				allowClear : true
			});
		} else {
			$("input[name='query.createdBy']").attr("placeholder", "该角色没有相关人员!").attr("readonly", true);
		}
	});

	// 拆分人 =采购入库页面权限
	$.getJSON(CONTEXT_PATH + "system/saleusers/userByPermissionCode?permissionCode=10020100", function(json){
		if (json) {
			$("input[name='query.splitUser']").select2({
				data : json,
				placeholder : "拆分人",
				allowClear : true
			});
		} else {
			$("input[name='query.splitUser']").attr("placeholder", "该角色没有相关人员!").attr("readonly", true);
		}
	});
	// 解绑人
	$.getJSON(CONTEXT_PATH + "system/saleusers/userByRole?roleId=51", function(json){
		if (json) {
			$("input[name='parcelUnbind']").select2({
				data : json,
				placeholder : "解绑人",
				allowClear : true
			});
		} else {
			$("input[name='parcelUnbind']").attr("placeholder", "该角色没有相关人员!").attr("readonly", true);
		}
	});

	
	// 全选
	var checkAll = $("input[name='checkAll']");

	// 子选项
	var itemIds = $("input[name='ids']");
	checkAll.change(
	  function () {
		  itemIds.prop("checked", $(this).prop("checked"));
		  itemIds.each(function(){
			  var f = $(this).is(":checked");
				var checkClass = $(this).prop("class");
				$("." + checkClass).each(function(){
					$(this).prop("checked",f);
				})
		  })
	  }
	);
	
	// 获取选中的记录
	function getCheckedIds() {
		var checkedIds = $("input[name='ids']:checked");
        return checkedIds;
	}

	function getCheckedIdsStr() {
        var checkedIds = $("input[name='ids']:checked");
        var checkIds = "";
        for (var i = 0; i < checkedIds.length; i++) {
            var outId = checkedIds[i].value;
            if (i == 0) {
                checkIds += outId;
            } else {
                checkIds += "," + outId;
            }
        }
        return checkIds;
    }

	// 更新采购单号
	function updatePurchaseOrderNo(id) {
		window.location.href = CONTEXT_PATH + "expressRecords/batchUpdatePurchaseOrderNo?ids=" + id;
	}
	
	//批量刷新采购单
	function batchUpdatePurchaseOrderNo(){
		var checkedDatas = getCheckedIds();
		if(checkedDatas.length == 0) {
			getErrorInfoAlert("请选择要操作的签收记录");
			return;
		}
		var ids = checkedDatas.serialize();
		window.location.href = CONTEXT_PATH + "expressRecords/batchUpdatePurchaseOrderNo?" + ids;
	}

	$('#rejectPurchaseExpressRecord').on('click',function () {
        if (getCheckedIdsStr().length == 0) {
			getErrorInfoAlert("请先选择收货单！");
            return false;
        }

        dialog({
            title: '拒收',
            content: $('#reject-form'),
            width: 400,
            top: 0,
            okValue: '确认',
            ok: function () {
                var rejectComment = $('#rejectComment').val();

                if(!rejectComment){
					getErrorInfoAlert("请输入拒收原因！");
                    return false;
                }
                var params = {
                    ids: getCheckedIdsStr(),
                    rejectComment: rejectComment
                };
                $.post(CONTEXT_PATH +"expressRecords/batchRejectExpressRecord", params, function(data) {
                    if(data.status == 200){
						layer.alert("成功！");
                    }else {
						getErrorInfoAlert(data.message);
                    }
                    setTimeout(function () {
                        window.location.reload();
                    }, 2000);
                });
            },
            cancelValue: '取消',
            cancel: function () {
                window.location.reload();
            }
        }).showModal();
    });

	$('#unbindExpressRecord').on('click',function () {
		debugger;
		var checkedDatas = getCheckedIds();
		if (checkedDatas.length == 0) {
			getErrorInfoAlert("请先选择收货单！");
			return false;
		}
		if (checkedDatas.length > 1){
			getErrorInfoAlert("每次只能选择一个物流收货单解绑！");
			return false;
		}
		var id = checkedDatas.serialize();
		$.getJSON(CONTEXT_PATH + "expressRecords/unbindExpressRecord?" + id, function(result){
			if(result.status == 200) {
				//setTimeout( function(){
				layer.alert(result.message, "success");
				//}, 3000 );
				setTimeout(function () {
					window.location.reload();
				}, 2000);
			} else {
				getErrorInfoAlert(result.message);
			}
		});
	});
	
	// 导出
	function downloadRecord(){
		var param = $("#domain").serialize();
		var checkedDatas = getCheckedIds();
		if(checkedDatas.length > 0) {
			param = param + "&" +checkedDatas.serialize();
		}
		$.post(CONTEXT_PATH + "expressRecords/download", param, function(data){
			if (data.status == 200) {
				if (data.message==null || data.message==''){
					layer.alert('成功',function (index) {
						layer.close(index);
						diglog.close().remove();
						location.reload();
					});
				}else{
					customizeLayer(data.message);
				}
			} else {
				customizeLayer(data.message);
			}
		});
	}

	$('#createExceptionOrder').on('click',function () {
        if (getCheckedIds().length == 0) {
			getErrorInfoAlert("请先选择一个收货单！");
            return false;
        }
        if (getCheckedIds().length > 1) {
			getErrorInfoAlert("只可单个条目生成异常！");
            return false;
        }

        var Check = $("table input[type=checkbox]:checked");//在table中找input下类型为checkbox属性为选中状态的数据
        var purchaseOrderNo = '';
        var trackingNumber = '';
        var id = '';
        Check.each(function () {//遍历
            var row = $(this).parent("td").parent("tr");//获取选中行
            purchaseOrderNo = row.find("[name='purchaseOrderNo']").html();
            trackingNumber = row.find("[name='trackingNumber']").html();
            id = row.find("[name='ids']").val();
        });
       if(purchaseOrderNo != ''){
		   getErrorInfoAlert("已匹配到采购单的不能生成异常单！");
           return false;
	   }
        dialog({
            title: '生成异常',
            content: $('#dialog-form'),
            width: 400,
            top: 0,
            okValue: '确认',
            ok: function () {
                var purchaseUser = $('#purchaseUser').val();
                var exceptionBoxNo = $('#exceptionBoxNo').val();
                var boxNoText = $('#exceptionBoxNo').next(".span-boxNo").text();


                if(exceptionBoxNo == ''){
					getErrorInfoAlert("请输入不良品周转码！");
                    return false;
                }

                if(exceptionBoxNo != '' && boxNoText == ''){
					getErrorInfoAlert("请先绑定周转码！");
                    return false;
                }

                if(exceptionBoxNo != '' && boxNoText != '' && exceptionBoxNo != boxNoText){
					getErrorInfoAlert("输入的周转码与绑定的周转码不一致！");
                    return false;
                }
                if(purchaseUser == ''){
					getErrorInfoAlert("请输入相关采购员！");
                    return false;
                }
                var params = {
                    exceptionBoxNo: exceptionBoxNo,
                    purchaseUser:purchaseUser,
                    trackingNumber:trackingNumber,
                    id:id
                };
                $.post(CONTEXT_PATH+"expressRecords/createExceptionOrder", params, function(data) {
                    if(data.status == 200){
                        alert(data.message);
                    }else {
						getErrorInfoAlert(data.message);
                    }
                    setTimeout(function () {
                        window.location.reload();
                    }, 2000);
                });
            },
            cancelValue: '取消',
            cancel: function () {
                window.location.reload();
            }
        }).showModal();
    });

    // 校验周转码
    function checkBox(obj,type){

        if(!obj.value || obj.value.replace(/\s/g,'') == ''){
			getErrorInfoAlert("请输入周转码!");
            return ;
        }
        var boxNo = obj.value.replace(/\s/g,'');
        if(boxNo.length < 4){
			getErrorInfoAlert("请输入正确的周转码!");
            return ;
        }
        var flag = hasUsedBoxNo(boxNo);
        if(flag){
			getErrorInfoAlert("该周转码已锁定，请重新输入!");
            return ;
        }

        if(obj.id == $('#exceptionBoxNo').attr("id")){
            $('#exceptionBoxNo').val('');// 清空中转码
        }

        if(boxNo==""){
			getErrorInfoAlert("请输入周转码！");
            return false;
        } else{
            // 验证有效性
            $.ajax({
                url : CONTEXT_PATH + "checkin/scans/createCheckIn/checkBox",
                data : {boxNo : boxNo, type:type},
                success : function(json){
                    if (json.status == '500') {
						getErrorInfoAlert(json.message + '');
                    } else if (json.status == '200') {
                        //buildInStock(parentIndex, index, boxNo);
                        if(obj.id == $('#exceptionBoxNo').attr("id")){
                            $('#exceptionBoxNo').val(boxNo);
                            $('#exceptionBoxNo').next(".span-boxNo").text(boxNo);
                        }
                    }
                },
                error:function(){
					getErrorInfoAlert('校验失败，请重新扫描周转码!');
                }
            });
        }

        function hasUsedBoxNo(inputBoxNo){
            var flag = false;
            var boxNoSpans = $('.span-boxNo');
            for (var i = 0; i < boxNoSpans.length; i++) {
                var boxNoSpan = boxNoSpans[i];
                var boxNo = $(boxNoSpan).text();
                if(boxNo !=null && boxNo!='' && boxNo == inputBoxNo){
                    flag = true;
                    break;
                }
            }
            return flag;
        }

    }
    
    /*编辑保存查询条件*/
    function editFunc($this){
    	redirectWithSearch($this.getAttribute("url"), $("#domain"), "expressrecords_search");
    }
    
	window.onload=function(){
		var pageinfo = sessionStorage.getItem("searchPageinfo");
		if(null != pageinfo){
			searchReload("expressrecords_search", $("#domain"));
		}
	}
	/*编辑保存查询条件*/


    //批量备注
    $("#batchComment").click(function () {
        let checkedIds = getCheckedIds();

        if(checkedIds.length == 0) {
			getErrorInfoAlert("请勾选要操作的快递单");
            return;
        }

        let ids = [];
        checkedIds.each(function() {
            if ($(this).is(":checked")) {
                var s= $(this).val();
                ids.push(s);
                ableMany=true;
            }
        });

        var diglog = dialog({
            title:"批量修改快递单备注",
            width: 400,
            height: 150,
            url: CONTEXT_PATH + "expressRecords/batchComment",
            okValue: '确定',
            ok: function () {

                var batchCommentWindow = $(this.iframeNode.contentWindow.document.body);
                var submitForm = batchCommentWindow.find("#submit-form");

                var comment = submitForm.find("#comment").val();

                if(comment) {
                    $.ajax({
                        url: CONTEXT_PATH + "expressRecords/batchComment",
                        type: "POST",
                        data: {ids: ids.toString(),comment:comment},
                        success: function(data){
                            if (data.status == 200) {
                                alert(data.message);
                                window.location.reload();
                            } else {
								getErrorInfoAlert(data.message);
                            }
                        }
                    });
                }
                setTimeout(function() {
                    diglog.close().remove();
                }, 100);
                return false;
            },
            cancelValue:'取消',
            cancel: function () {}

        });
        diglog.show();
    })

	//批量备注
	$("#parcelUnbind").click(function () {
		var diglog = dialog({
			title:"未称重包裹解绑",
			width: 400,
			height: 50,
			content: $('#parcelUnbind_head'),
			okValue: '确定',
			ok: function () {
				var unbindUser = $("#parcelUnbind_user").val();
				if (!unbindUser){
					layer.alert("请选择需要解绑的用户","error");
				}
				$.ajax({
					url: CONTEXT_PATH + "expressRecords/parcelUnbind",
					type: "POST",
					data: {unbindUser: unbindUser},
					success: function(data){
						if (data.status == 200) {
							layer.alert(data.message);
							setTimeout(function() {
								window.location.reload();
							}, 1000);
						} else {
							layer.alert(data.message,"error");
						}
					}
				});
				return false;
			},
			cancelValue:'取消',
			cancel: function () {}

		});
		diglog.show();
	})

	// 产品图放大
	function enlarge(obj){
		var url = $(obj).attr("src");
		var content = `<img style='width:600px;height:600px;' src='`+url+`'/>`
		layer.open({
			type: 1,
			title: false,
			closeBtn:0,
			area: ['600px','600px'],
			offset:'auto',
			fix: true,
			maxmin: false,
			shade:0.4,
			shadeClose:true,
			content: content
		});
	}
</script>
</body>
</html>