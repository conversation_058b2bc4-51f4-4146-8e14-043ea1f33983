<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">
<html lang="en" class="no-js">

	<head>
		<title>易世通达仓库管理系统</title>
		<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
		<#include "/common/include.html">
		<link href="${CONTEXT_PATH}css/deliver.css" rel="stylesheet">
		<style type="text/css">
			.modal-body input {
				width: 240px;
			}
			
			.top_bar {
				position: fixed;
				top: 0px;
			}
			
			#task-list td {
				vertical-align: middle;
			}
			.my-dropdown{
				width: 90%;
				position: absolute;
				margin-left: 10px;
				top: 60%;
				text-align: center;
			}
			.my-dropdown li{
				line-height: 30px;
			}
			.my-dropdown li:hover{
				cursor: pointer;
				color: #0b94ea;
			}
			.my-dropdown div{
				width: 30%;
				float: left;
			}
			#dialog-form{
				margin-bottom: 60px;
			}
			#dialog-form tr{
				line-height: 60px;
			}
		</style>
	</head>

	<body>
		<@header method="header" active="10020000"><#include "/ftl/header.ftl"></@header>

		<div id="page" style="background-color: rgb(231, 237, 248)">
			<div class="row">
				<div class="col-md-12" style="padding: 0">
					<ul class="page-breadcrumb breadcrumb" style="background-color: white;">
						<li>
							<a href="#">入库管理</a>
						</li>
						<li class="active">调拨入库</li>
					</ul>
				</div>
			</div>

			<!-- 内容 -->
			<div class="container-fluid" style="background-color: white;border: none">
				<div class="row">
					<div class="col-md-12">
						<div class="w300px mt10" style="display: inline-block;padding: 20px">

							<div class="mt50">
								<h3 style="margin-bottom: 20px">扫描箱号(生成调拨入库单)</h3>
								<div id="input_orderid" style="margin-top: 10px">
									<label style="margin-left: 30px">箱号</label>
									<input type="text" style="display: inline-block;width: 260px !important;" class="form-control input-medium" name="orderid" id="bagNo" value="" onkeypress="if(event.keyCode==13) { inputnext(this); return false;}" tabindex="4">
								</div>
								<div>
									<span id="span-boxNo" style="margin-top: 10px"></span>
								</div>
								<div id="input_orderid_sku" style="margin-top: 10px">
									<label style="margin-left: 15px">唯一码</label>
									<input type="text" style="display: inline-block;width: 260px !important;" class="form-control input-medium" name="sku" id="sku" value="" onkeypress="if(event.keyCode==13) { inputsku(this); return false;}" tabindex="4">
								</div>
							</div>
						</div>
						<div style="margin-left:180px;width:360px;display: inline-block" class="fl panel-title2" id="panel-scan-title">
							<h3>当前扫描：</h3>
							<h3><span id="scan-history" style="font-weight:bold;color:red;"></span></h3>
							<h3>扫描结果：
								<span id="error" style="font-weight:bold;color:red;"></span>
								<span id="success" style="font-weight:bold;color:green;"></span>
							</h3>
							<h3>
								<span id="errorMes" style="color:red;"></span>
							</h3>
						</div>
						<div style="margin-left:180px;width:260px;display: inline-block" class="fl panel-title2" id="panel-title">
							<h1 style="color:blue;font-size:48px;">成功:<b>0</b></h1></div>
						<div style="width:260px;display: inline-block" class="fr panel-title2" id="panel-title-piece">
							<h1 style="color:red;font-size:48px;">计数:<b>0</b></h1>
						</div>

						<div>
							<div id="check_scan_datas" class="border-gray p5">

							</div>
						</div>
					</div>
				</div>
				<!-- end row -->
			</div>
			<#include "/common/footer.html">
		</div>
		</div>
		<object width="0" height="0" classid="clsid:2105C259-1E0C-4534-8141-A753534CB4CA" id="LODOP_OB">
	  <embed width="0" height="0" type="application/x-print-lodop" id="LODOP_EM">
   	</object>

		<!-- 	打印插件 -->
		<script type="text/javascript" src="${CONTEXT_PATH}js/LodopFuncs.js"></script>
		<script type="text/javascript" src="${CONTEXT_PATH}js/web-storage-cache.js"></script>
		<script type="text/javascript" src="${CONTEXT_PATH}js/jquery.sound.js?v=${.now?datetime}"></script>
		<script type="text/javascript" src="${CONTEXT_PATH}js/assets/plugins/jquery.region.js"></script>
		<script type="text/javascript" src="${CONTEXT_PATH}js/prompt-message.js"></script>
		<script type="text/javascript">
			var uuid_array = new Array();
			var diglog = null;

			var cacheKey = "check_express_success";
			var pieceCacheKey = "check_express_piece_success";
			jQuery(document).ready(function() {

				pageInit();

				var storage = new WebStorageCache();

				if(storage.get(cacheKey)) {
					lastSuc = storage.get(cacheKey);
					$('#panel-title').html('<h1  style="color:blue;font-size:48px;">成功：<b>' + lastSuc + '</b></h1>');
				}

				if(storage.get(pieceCacheKey)) {
					lastSuc = storage.get(pieceCacheKey);
					$('#panel-title-piece').html('<h1  style="color:red;font-size:48px;">计数:<b>' + lastSuc + '</b></h1>');
				}

			});

			// 初始化
			function pageInit() {
				$('#bagNo').val('');
				$('#bagNo').focus();
				$('#check_scan_datas').html('');
			}

			//加载快递单数据，输入快递单号触发
			function inputnext(obj) {

				if(!obj.value || obj.value.replace(/\s/g, '') == '') {
					getErrorInfoAlert("请输入调拨箱号!");
					return;
				}

				var bagNo = obj.value.replace(/\s/g, '');

				if(bagNo.length < 7) {
					getErrorInfoAlert("请输入正确的调拨箱号!");
					return;
				}
				if (bagNo.indexOf('XH') == -1) {
					getErrorInfoAlert("请输入正确的调拨箱号!");
					return;
				}

				// 获取快递单相关入库单
				var r = $.ajax({
					url: CONTEXT_PATH + "allocationCheckin/scans/allocationScan",
					data: { bagNo: bagNo },
					timeout: 30000,
					beforeSend: function() {
						App.blockUI();
					},
					success: function(response) {
						App.unblockUI();

						var responseHtml = $(response).find("#check_scan_datas").html();
						$("#check_scan_datas").html(responseHtml);

						$('#bagNo').val("");
						$('#sku').focus();
					},
					error: function() {
						App.unblockUI();
						getErrorInfoAlert('扫描失败，请重新扫描');
						$('#bagNo').val("");
						$('#bagNo').focus();
					}
				});

			}

			// 第一次扫描SKU
			function inputsku(obj){
				debugger;
				var uuidSku = obj.value.replace(/\s/g, '');

				if (!uuidSku || uuidSku.trim() == '' || uuidSku.indexOf("=") < 0) {
					layer.alert("请输入有效唯一码!",function (index) {
						layer.close(index);
						$('#sku').val('');
						$('#sku').focus();
					});
					return false;
				}
				$("#scan-history").text(uuidSku);
				if (uuid_array && uuid_array.contains(uuidSku)){
					$("#error").text("失败");
					$("#errorMes").text("失败信息：唯一码重复扫描");
					$('#sku').val('');
					$('#sku').focus();
					return false;
				}
				var allocationOrderNo = $('#check_scan_datas').find("input[name='whAllocationCheckIn.allocationOrderNo']").val();

				var realSku = uuidSku.split('=')[0];
				var transSku = realSku;
				//如果realSku包含小数点，转义
				if (realSku.indexOf('.') > -1) {
					transSku = realSku.replace('.', '\\.');
				}
				var currentTr = $('#check_scan_datas').find(".allocation-order").find('.allocation-tr-'+transSku);
				// 获取良品数量
				var quantity = currentTr.find("input[name='whAllocationCheckIn.whAllocationCheckInItem.quantity']").val();
				if (quantity == null || quantity == '') {
					quantity = 0;
				}
				quantity = parseInt(quantity);
				// 获取异常数量
				var exceptionQuantity = currentTr.find("input[name='whAllocationCheckIn.whAllocationCheckInItem.exceptionQuantity']").val();
				if (exceptionQuantity == null || exceptionQuantity == '') {
					exceptionQuantity = 0;
				}
				exceptionQuantity = parseInt(exceptionQuantity);
				var instockedQuantity = parseInt(currentTr.find(".instocked-quantity").text());// 已入库数量
				var allocationQuantity = parseInt(currentTr.find(".allocation-quantity").text());// 装箱数量

				//当前入库数+已入库数量 >装箱数量
				if((1 + instockedQuantity + quantity + exceptionQuantity) > allocationQuantity) {
					$("#error").text("失败");
					$("#errorMes").text("失败信息：良品数+异常数+已入库数量 > 装箱数量");
					$('#sku').val('');
					$('#sku').focus();
					return false;
				}


				$.ajax({
					url: CONTEXT_PATH + "allocationCheckin/scans/uuid",
					type: "GET",
					data: {
						orderNo: allocationOrderNo,
						uuidSku: uuidSku
					},
					success: function (result) {
						if (result.status == "200") {
							quantity = parseInt(quantity) + 1;
							currentTr.find("input[name='whAllocationCheckIn.whAllocationCheckInItem.quantity']").val(quantity);
							uuid_array.push(uuidSku);
							$("#error").text("成功");
							$("#errorMes").text('');
						} else {
							$("#error").text("失败");
							$("#errorMes").text("失败信息：" + result.message);
						}
						$('#sku').val("");
						$('#sku').focus();
					},
					error: function () {
						$("#error").text("失败");
						$("#errorMes").text("失败信息：系统异常");
						$('#sku').val("");
						$('#sku').focus();
					}
				});
			}


			// 统计扫描成功和失败的数量
			function calsf() {

				var storage = new WebStorageCache();
				// 一开始没有缓冲，所以这次数量算1
				var lastSuc = 0;
				if(storage.get(cacheKey)) {
					lastSuc = storage.get(cacheKey);
					lastSuc++;
				} else {
					lastSuc = 1;
				}
				storage.set(cacheKey, lastSuc, { exp: 5 * 60 * 60 });
				$('#panel-title').html('<h1 style="color:blue;font-size:48px;">成功：<b>' + lastSuc + '</b></h1>');
			}

			// 计数
			function calsfPiece(quantity) {
				if(!quantity) {
					quantity = 1;
				}
				var storage = new WebStorageCache();
				var lastSuc = 0;
				if(storage.get(pieceCacheKey)) {
					lastSuc = storage.get(pieceCacheKey);
					lastSuc = lastSuc + quantity;
				} else {
					lastSuc = quantity;
				}
				storage.set(pieceCacheKey, lastSuc, { exp: 5 * 60 * 60 });
				$('#panel-title-piece').html('<h1  style="color:red;font-size:48px;">计数:<b>' + lastSuc + '</b></h1>');
			}

			// 扫描框获取焦点
			$("body").click(function(e) {
				if(e.target.nodeName.toUpperCase() == "INPUT" || e.target.nodeName.toUpperCase() == "TEXTAREA" || e.target.nodeName.toUpperCase() == "SELECT") {

				} else {
					//$('#bagNo').focus();
				}
			});

			/** 打印 **/
			var LODOP; //声明为全局变量
			function myPrint() {
				App.unblockUI();

				//先判断 内页中是否有 打印 方法 有的话直接调用
				try{
					if(typeof(eval(window.frames["printHtml"].myPrint))=='function'){
						return window.frames["printHtml"].myPrint();
					}
				}catch(e){
				}

				try{
					CreatePrintPage();
					LODOP.PRINT();
				}
				catch(e){

				}
			};

			function myPrint2() {
				if(confirm("是否确认要手动打印？")) {
					try{
						if(typeof(eval(window.frames["printHtml"].myPrint))=='function'){
							return window.frames["printHtml"].myPrint();
						}
					}catch(e){
					}
					CreatePrintPage();
					LODOP.PRINT();
				}
			};

			function myPreview() {

				//先判断 内页中是否有 打印 方法 有的话直接调用
				try{
					if(typeof(eval(window.frames["printHtml"].myPreview))=='function'){
						return window.frames["printHtml"].myPreview();
					}
				}catch(e){
				}
				CreatePrintPage();
				LODOP.PREVIEW();
			}

			function CreatePrintPage() {
				LODOP=getLodop(document.getElementById('LODOP_OB'),document.getElementById('LODOP_EM'));
				LODOP.PRINT_INIT("打印运单");

				//打印全部
				LODOP.ADD_PRINT_HTM(0,0,"100mm","100mm",GetIframeInnerHtml(window.frames["printHtml"]));
			};

			function CheckLodopIsInstall() {
				LODOP=getLodop(document.getElementById('LODOP_OB'),document.getElementById('LODOP_EM'));
				LODOP.PRINT_INIT("打印运单");
				try{
					var LODOP=getLodop(document.getElementById('LODOP_OB'),document.getElementById('LODOP_EM'));
					if ((LODOP!=null)&&(typeof(LODOP.VERSION)!="undefined")){
						// $('#print_info').text("本机已成功安装过Lodop控件!\n 版本号:"+LODOP.VERSION);
					}
				}catch(err){
					$('#print_info').text("Error:本机未安装Lodop 或需要升级!");
				}
			}

			function GetIframeInnerHtml(objIFrame) {
				var iFrameHTML = objIFrame.document.getElementById('print_content').innerHTML;
			}

			// 这里Go
			var printed = false;
			function IframeOnloadPrint(){
				debugger;
				var iframe=document.getElementById("printHtml");
				// 加载完iframe后执行
				if (iframe.attachEvent){
					iframe.attachEvent("onload", function(){
						printed = true;
						myPrint();
					});
				} else {
					iframe.onload = function(){
						printed = true;
						setTimeout(myPrint, 200);
						return;
					};
				}

				printed = false;
			}

			// 手动打印
			function manualPrint() {
				var r = confirm("确定手动打印?");
				if(!r) {
					return;
				}

				// 自动打印
				myPrint();
			}

		</script>
		<!-- END JAVASCRIPTS -->
	</body>
	<!-- END BODY -->

</html>