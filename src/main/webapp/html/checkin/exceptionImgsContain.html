<div class="form-group">
    <div class="img-set-btn">
        <button type="button" class="icon-zoom-in" id="zoom-in" onclick="imgToSize(100)"></button>
        <button type="button" class="icon-zoom-out" id="zoom-out" onclick="imgToSize(-100);"></button>
        <button type="button" class="icon-refresh" id="refresh" onclick="reSet()"></button>
    </div>
    <div class="skuImgMain"><img id="rotImg" src="${domain.exceptionImages[0]}"></div>
    <ol class="skuImgOthersOl">
        <#list domain.exceptionImages as image>
            <div class="skuImgOthers"><img src="${image}" onerror="javascript:this.parentElement.style.display='none'"/></div>
        </#list>
    </ol>
</div>