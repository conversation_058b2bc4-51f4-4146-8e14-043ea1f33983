<#if domain.whPurchaseOrders??>
    <#list domain.whPurchaseOrders as whPurchaseOrder>
        <#list whPurchaseOrder.items as item>
            <tr class="purchase-detail">
                <td>
                    <table class="table table-striped table-bordered table-hover table-condensed">
                        <colgroup>
                            <col width="20%" />
                            <col width="20%" />
                            <col width="10%" />
                            <col width="20%" />
                            <col width="15%" />
                            <col width="15%" />
                        </colgroup>
                        <thead>
                            <tr>
                                <th>采购单号</th>
                                <th>快递单号</th>
                                <th>状态</th>
                                <th>耗材名称</th>
                                <th>采购数量</th>
                                <th>已入库数量</th>
                            </tr>
                        </thead>
                        <tbody>
                        <tr>
                            <td>${whPurchaseOrder.purchaseOrderNo}</td>
                            <td>
                                <#list whPurchaseOrder.purchaseToExpress as express>
                                    ${express.expressId?join(',')}
                                </#list>
                            </td>
                            <td>${util('enumName', 'com.estone.checkin.enums.WmsPurchaseOrderStatus', whPurchaseOrder.purchaseStatus)}</td>
                            <td>${item.materialName}</td>
                            <td>${item.quantity}</td>
                            <td>${item.inNum}</td>
                        </tr>
                        </tbody>
                    </table>

                </td>
            </tr>
        </#list>
    </#list>
</#if>

