<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">
<html>
<head>
	<title>易世通达仓库管理系统</title>
	<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
	<#include "/common/include.html">
    <link href="${CONTEXT_PATH}css/sku/sku-imgs.css?v=20191024" rel="stylesheet" type="text/css"/>
	<style type="text/css">
		.modal-body input {
			width: 240px;
		}
		#task-list td {
			vertical-align:middle;
		}
		.scan-a {
			width: 120px;
			height: 120px;
			line-height: 100px;
			text-align: center;
			margin-bottom: 50px;
			font-size:48px;
		}
		#single-build-btn{
			width: 100px;
			height: 100px;
			font-size: x-large;
			color: white;
			background-color: limegreen;
		}
		#item-list{
			margin-bottom: 50px;
		}
		.qc-packing{
			color: red;
			font-size: 20px;
			font-weight: 600;
            line-height: 3;
		}
		.qc-quantity{
			font-size: 40px;
			color: red;
			font-weight: bold;
			font-weight: 600;
		}
        .img-view{
            width: 45%;
        }
        .img-content{
            float: right;
        }
        .img-set-btn{
            width: 60%;
            right: 20px;
        }
		#button-left{
			text-align: center;
			position:absolute;
			top:50%;
		}
		#button-right{
			text-align: center;
			position:absolute;
			top:50%;
			right:0;
		}
	</style>
</head>

<body>
<@header method="header" active="10030000"><#include "/ftl/header.ftl"></@header>

<div id="page" style="background-color: rgb(231, 237, 248)">
	<div class="row">
		<div class="col-md-12" style="padding: 0">
			<ul class="page-breadcrumb breadcrumb" style="background-color: white;">
				<li><a href="#">入库管理</a></li>
				<li class="active">QC质检(调拨)</li>
			</ul>
		</div>
	</div>

	<!-- 内容 -->
	<div class="container-fluid" style="background-color: white;border: none">
		<div class="row">
			<div class="col-md-12">
				<div style="float:left;margin-top: 15px;text-align: center;">
					<span class="control-inline">SKU</span>
					<input type="text" class="form-control input-large input-inline" id="uuidSku" onkeypress="if(event.keyCode==13) { inputnext(this); return false;}" tabindex="4">
					<input type="hidden" id="hidden-uuidSku" value="">
				</div>
				<div style="float:left;margin-top: 15px;text-align: center;">
					<span class="control-inline">QC加工装袋</span>
					<select id="isQcPacking" name="isQcPacking" class="form-control input-large input-inline">
						<option value=""></option>
						<option value="9">超级加工</option>
						<option value="7">重加工</option>
						<option value="5">一般加工</option>
						<option value="3">轻加工</option>
						<option value="0">否</option>
					</select>
				</div>
				<div id="panel-title" style="margin-right: 100px;"><h1 style="color:blue;font-size:48px;">成功：<strong id="success-count">0</strong></h1></div>
				<div class="clearfix" style="border-bottom: 1px solid #ccc; margin: 10px 0"></div>

				<div>
					<div id="check_scan_datas" class="border-gray p5 col-md-12" style="min-height:700px;">

					</div>
				</div>
			</div>
		</div><!-- end row -->

	</div>
	<#include "/common/footer.html">
</div>
<script type="text/javascript" src="${CONTEXT_PATH}js/LodopFuncs.js"></script>
<script type="text/javascript" src="${CONTEXT_PATH}js/web-storage-cache.js"></script>
<script type="text/javascript" src="${CONTEXT_PATH}js/jquery.sound.js?v=${.now?datetime}"></script>
<script type="text/javascript" src="${CONTEXT_PATH}js/prompt-message.js"></script>
<script type="text/javascript" src="${CONTEXT_PATH}js/assets/plugins/bootstrap-editable/bootstrap-editable/js/bootstrap-editable.js"></script>


<script type="text/javascript">

    var diglog = null;
    var cacheKey = "check_product_success";
    $(document).ready(function(){

        pageInit();

        var storage = new WebStorageCache();

        if (storage.get(cacheKey)) {
            lastSuc = storage.get(cacheKey);
            $('#panel-title').html('<h1  style="color:blue;font-size:48px;">成功：<strong id="success-count">'+lastSuc+'</strong></h1>');
        }


    });

    // 初始化
    function pageInit() {
        $('#uuidSku').val('');
        $('#hidden-uuidSku').val('');
        //$('#uuidSku').focus();
        $('#boxNo').focus();
        $('#check_scan_datas').html('');
    }

    //加载入库单数据，输入周转码触发
    function inputBoxNo(obj){
        // 先判断上次扫描的有没有QC提交
        var currentBoxNo = $('#hidden-boxNo').val();
        if (currentBoxNo !='') {
			getErrorInfoAlert("当前产品还未QC，请先QC!");
            $('#boxNo').val("");
            return ;
        }

        if(!obj.value || obj.value.replace(/\s/g,'') == ''){
			getErrorInfoAlert("请输入周转码!");
            $('#boxNo').val("");
           // $('#boxNo').focus();
            return ;
        }

        var boxNo = $.trim(obj.value);
        var r= $.ajax({
            url : CONTEXT_PATH + "allocationCheck/skus/getCheckIn",
            data : {boxNo : boxNo},
            success : function(response){
                if (response.status == '500') {
					getErrorInfoAlert(response.message);
                    $('#boxNo').val("");
                   // $('#boxNo').focus();
                    return;
                } else if (response.status == '200') {
                    var whAllocationCheckInItem = JSON.parse(response.message);

                    $('#hidden-boxNo').val(boxNo);
                    $('#boxNo-span').text(boxNo);
                    $('#hidden-boxSku').val(whAllocationCheckInItem.sku);
                    $('#hidden-inId').val(whAllocationCheckInItem.inId);
                    $('#boxNo').val("");
                    $('#uuidSku').val("");
                    $('#uuidSku').focus();
                }
            },
            error:function(){
				getErrorInfoAlert('扫描失败，请重新扫描!');
            }
        });
    }

    //加载SKU数据，输入SKU触发
    function inputnext(obj){
        if(!obj.value || obj.value.replace(/\s/g,'') == ''){
			getErrorInfoAlert("请扫描sku唯一码!");
            $('#uuidSku').val("");
            return ;
        }
		var uuidSku = $.trim(obj.value);
		if(uuidSku.indexOf("=") == -1){
			layer.alert("扫描的不是唯一码，请扫描唯一码！",'error');
			$('#uuidSku').val("");
			return ;
		}

		var sku = uuidSku.split('=')[0];

        var r= $.ajax({
            url : CONTEXT_PATH + "allocationCheck/skus/query/sku",
            data : {uuidSku : uuidSku},
            success : function(response){
                var responseHtml = $(response).find("#check_scan_datas").html();
                $("#check_scan_datas").html(responseHtml);

                $('#hidden-uuidSku').val(sku);
                $('#uuidSku').val("");
                $('#uuidSku').focus();

            },
            error:function(){
				getErrorInfoAlert('扫描失败，请重新扫描!');
            }
        });

    }

    // 统计扫描成功和失败的数量
    function calsf(){

        var storage = new WebStorageCache();

        var lastSuc = 1;

        if (storage.get(cacheKey)) {
            lastSuc = storage.get(cacheKey);
            lastSuc ++;
        }

        storage.set(cacheKey, lastSuc , {exp : 5 * 60 * 60});
        $('#panel-title').html('<h1  style="color:blue;font-size:48px;">成功：<strong id="success-count">'+lastSuc+'</strong></h1>');
    }

    $("body").click(function(e){
        if(e.target.nodeName.toUpperCase() == "INPUT" || e.target.nodeName.toUpperCase() == "TEXTAREA" || e.target.nodeName.toUpperCase() == "SELECT") {

        } else {
            $('#uuidSku').focus();
        }
    });
</script>
<!-- END JAVASCRIPTS -->
</body>
</html>