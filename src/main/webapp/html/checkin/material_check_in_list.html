<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">
<html>
<head>
    <title>易世通达仓库管理系统</title>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <#include "/common/include.html">
    <#include "/common/webSocket.html">
    <style type="text/css">
        .modal-body input {
            width: 240px;
        }
        .check-in-detail th{
            background-color:#E0E0E0 !important;
        }
    </style>
</head>
<body>
<@header method="header" active="18040000"><#include "/ftl/header.ftl"></@header>

<div id="page" style="background-color: rgb(231, 237, 248)">
    <div class="row">
        <div class="col-md-12" style="padding: 0">
            <ul class="page-breadcrumb breadcrumb" style="background-color: white;">
                <li><a href="#">入库管理</a></li>
                <li class="active">耗材入库单管理</li>
            </ul>
        </div>
    </div>

    <!-- 内容 -->
    <div class="container-fluid" style="background-color: white;border: none">
        <div class="row">
            <div class="col-md-12">
                <#assign query = domain.query>
                <form action="${CONTEXT_PATH}materialCheckIn/search"
                      class="form-horizontal form-bordered form-row-stripped"
                      method="post" modelAttribute="domain" name="materialCheckInForm" id ="domain">
                    <!-- 分页信息 -->
                    <input id="page-no" type="hidden" name="page.pageNo" value="1">
                    <input id="page-size" type="hidden" name="page.pageSize" value="${domain.page.pageSize}">

                    <div class="form-body">
                        <div class="form-group">
                            <label class="control-label col-md-1">耗材货号</label>
                            <div class="col-md-2">
                                <input class="form-control" type="text" name="query.articleNumber" placeholder="多个使用，分隔查询" value="${query.articleNumber }">
                            </div>

                            <label class="control-label col-md-1">快递单号</label>
                            <div class="col-md-2">
                                <input class="form-control" type="text" name="query.trackingNumber" placeholder="多个使用，分隔查询" value="${query.trackingNumber }">
                            </div>
                            <label class="control-label col-md-1">采购单号</label>
                            <div class="col-md-2">
                                <input class="form-control" type="text" name="query.purchaseOrderNo" placeholder="多个使用，分隔查询" value="${query.purchaseOrderNo }">
                            </div>
                            <label class="control-label col-md-1">入库时间</label>
                            <div class="col-md-2">
                                <div class="input-group">
                                    <input class="form-control Wdate" type="text" name="query.fromCreateDate" placeholder="" readonly="readonly" value="${query.fromCreateDate }" onfocus="WdatePicker({startDate:'%y-%M-%d 00:00:00',dateFmt:'yyyy-MM-dd HH:mm:ss',alwaysUseStartDate:true})">
                                    <span class="input-group-addon">到</span>
                                    <input class="form-control Wdate" type="text" name="query.toCreateDate" placeholder="" readonly="readonly" value="${query.toCreateDate }" onfocus="WdatePicker({startDate:'%y-%M-%d 23:59:59',dateFmt:'yyyy-MM-dd HH:mm:ss',alwaysUseStartDate:true})">
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="control-label col-md-1">入库人</label>
                            <div class="col-md-2">
                                <input class="form-control" name="query.createUser" type="text" value="${query.createUser}">
                            </div>
                            <label class="control-label col-md-1">审核主管</label>
                            <div class="col-md-2">
                                <input class="form-control" name="query.checkManager" type="text" value="${query.checkManager}">
                            </div>
                            <label class="control-label col-md-1">审核质控</label>
                            <div class="col-md-2">
                                <input class="form-control" name="query.qcUser" type="text" value="${query.qcUser}">
                            </div>
                            <label class="control-label col-md-1">入库单状态</label>
                            <div class="col-md-2">
                                <input class="form-control" name="query.status" type="text" value="${query.status}">
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="control-label col-md-1">入库单号</label>
                            <div class="col-md-2">
                                <input class="form-control" name="query.inIdStr" type="text" value="${query.inIdStr}">
                            </div>
                            <label class="control-label col-md-1">耗材供应商</label>
                            <div class="col-md-2">
                                <input class="form-control" name="query.supplier" placeholder="多个查询之间用,隔开。支持单个模糊查询" type="text" value="${query.supplier}">
                            </div>
                            <label class="control-label col-md-1">包裹收货时间</label>
                            <div class="col-md-2">
                                <div class="input-group">
                                    <input class="form-control Wdate" type="text" name="query.fromPackageAcceptanceDate" placeholder="" readonly="readonly" value="${query.fromPackageAcceptanceDate }" onfocus="WdatePicker({startDate:'%y-%M-%d 00:00:00',dateFmt:'yyyy-MM-dd HH:mm:ss',alwaysUseStartDate:true})">
                                    <span class="input-group-addon">到</span>
                                    <input class="form-control Wdate" type="text" name="query.toPackageAcceptanceDate" placeholder="" readonly="readonly" value="${query.toPackageAcceptanceDate }" onfocus="WdatePicker({startDate:'%y-%M-%d 23:59:59',dateFmt:'yyyy-MM-dd HH:mm:ss',alwaysUseStartDate:true})">
                                </div>
                            </div>
                        </div>
                    </div>
                    <div>
                        <div class="pull-left" >
                            <div class="btn-group">
                                <@header method="auth" authCode="BATCH_PUSH_MATERIAL_CHECK_IN_INFO_TO_EPMS">
                                    <button type="button" class="btn btn-default" onclick="batchPushMaterialCheckIn()">
                                        批量推送入库单信息到采购
                                    </button>
                                </@header>
                            </div>
                        </div>
                        <div class="col-md-offset-12" style="text-align: right">
                            <button type="button" onclick="formReset(this)" class="btn btn-default">
                                <i class="icon-refresh"></i> 重置
                            </button>
                            <button type="submit" class="btn blue">
                                <i class="icon-search"></i> 查询
                            </button>
                            <@header method="auth" authCode="MATERIAL_CHECKIN_DOWNLOAD">
                                <button type="button" class="btn btn-default" onclick="downloadMaterialCheckIn()">
                                    <i class="icon-download"></i> 导出
                                </button>
                            </@header>
                        </div>
                    </div>
                </form>
            </div>
            <br/>
        </div>

        <div class="row">
            <div id="fixedDiv" class="col-md-12">
                <table class="table table-striped table-bordered table-hover table-condensed" id="fixedTab">
                    <colgroup>
                        <col width="5%" />
                        <col width="10%" />
                        <col width="10%" />
                        <col width="5%" />
                        <col width="10%" />
                        <col width="5%" />
                        <col width="5%" />
                        <col width="5%" />
                        <col width="5%" />
                        <col width="10%" />
                        <col width="10%" />
                        <col width="10%" />
                        <col width="5%" />
                        <col width="10%" />
                        <col width="5%" />
                    </colgroup>
                    <thead>
                    <tr>
                        <th>入库单号</th>
                        <th>快递单号</th>
                        <th>采购单号</th>
                        <th>耗材货号</th>
                        <th>耗材名称</th>
                        <th>单位</th>
                        <th>入库数量</th>
                        <th>供应商</th>
                        <th>包裹收货时间</th>
                        <th>创建人/创建时间</th>
                        <th>审核主管/审核时间</th>
                        <th>审核质控/审核时间</th>
                        <th>单据状态</th>
                        <th>备注</th>
                        <th>操作</th>
                    </tr>
                    </thead>
                </table>
            </div>
            <div class="col-md-12" id="task-list-warp">
                <!-- 内容 -->
                <table class="table table-striped table-bordered table-hover table-condensed" id="task-list">
                    <colgroup>
                        <col width="5%" />
                        <col width="7%" />
                        <col width="7%" />
                        <col width="5%" />
                        <col width="10%" />
                        <col width="5%" />
                        <col width="5%" />
                        <col width="5%" />
                        <col width="5%" />
                        <col width="10%" />
                        <col width="10%" />
                        <col width="10%" />
                        <col width="5%" />
                        <col width="10%" />
                        <col width="11%" />
                    </colgroup>
                    <thead>
                    <tr>
                        <th><input type="checkbox"  id="check-all" name="checkAll">入库单号</th>
                        <th>快递单号</th>
                        <th>采购单号</th>
                        <th>耗材货号</th>
                        <th>耗材名称</th>
                        <th>单位</th>
                        <th>入库数量</th>
                        <th>供应商</th>
                        <th>包裹收货时间</th>
                        <th>创建人/创建时间</th>
                        <th>审核主管/审核时间</th>
                        <th>审核质控/审核时间</th>
                        <th>单据状态</th>
                        <th>备注</th>
                        <th>操作</th>
                    </tr>
                    </thead>
                    <tbody>
                    <#list domain.materialCheckIns as checkIn>
                        <tr>
                            <td>
                                <input type="checkbox" value="${checkIn.inId}" name="inIds">
                                ${checkIn.inId}
                            </td>
                            <td>${checkIn.trackingNumber}</td>
                            <td>${checkIn.purchaseOrderNo}</td>
                            <td>
                                <#if checkIn.checkInItem??>${checkIn.checkInItem.articleNumber }</#if>
                            </td>
                            <td>
                                <#if checkIn.checkInItem?? && checkIn.checkInItem.material??>${checkIn.checkInItem.material.name }</#if>
                            </td>
                            <td>
                                <#if checkIn.checkInItem?? && checkIn.checkInItem.material??>${checkIn.checkInItem.material.unit }</#if>
                            </td>
                            <td>
                                <#if checkIn.checkInItem??>${checkIn.checkInItem.quantity }</#if>
                            </td>
                            <td>
                                <#if checkIn.checkInItem??>${checkIn.checkInItem.supplier}</#if>
                            </td>
                            <td>
                                <#if checkIn.checkInItem??>${checkIn.checkInItem.packageAcceptanceDate }</#if>
                            </td>
                            <td>${util('name',checkIn.createUser)}<br>${checkIn.createDate}</td>
                            <td>${util('name',checkIn.checkManager)}<br>${checkIn.checkTime}</td>
                            <td>${util('name',checkIn.qcUser)}<br>${checkIn.qcTime}</td>
                            <td>${util('enumName', 'com.estone.checkin.enums.MaterialCheckInStatus', checkIn.status)}</td>
                            <td>${checkIn.comment}</td>
                            <td>
                                <#if checkIn.status == 1 || checkIn.status == 7>
                                    <button type="button" class="btn yellow btn-info btn-xs" onclick="editOrder(${checkIn.inId},${checkIn.checkInItem.quantity })">编辑</button>
                                </#if>
                                <#if checkIn.status == 1>
                                    <@header method="auth" authCode="MATERIAL_MANAGER_CHECK_ORDER">
                                        <button type="button" class="btn blue btn-info btn-xs" onclick="checkOrder(null,${checkIn.inId})">主管审核</button>
                                    </@header>
                                </#if>
                                <#if checkIn.status == 3>
                                    <@header method="auth" authCode="MATERIAL_QC_CHECK_ORDER">
                                        <button type="button" class="btn blue btn-info btn-xs" onclick="checkOrder(null,${checkIn.inId})">质控审核</button>
                                    </@header>
                                </#if>
                                <#if checkIn.status == 7>
                                    <button type="button" class="btn red btn-info btn-xs" onclick="checkOrder('SCRAP',${checkIn.inId})">废弃</button>
                                </#if>
                                <button type="button" class="btn btn-info btn-xs" onclick="viewLog(${checkIn.inId}, 'materialCheckIn')">日志</button>
                            </td>
                        </tr>
                    </#list>
                    </tbody>
                </table>
                <!-- 内容end -->
            </div>
        </div>
        <div id="fixed-bottom">
            <div id="pager"></div>
        </div>
    </div>
    <#include "/common/footer.html">
</div>

<script type="text/javascript" src="${CONTEXT_PATH}js/datepicker/WdatePicker.js"></script>
<script type="text/javascript" src="${CONTEXT_PATH}js/table-thead-fixed.js"></script>
<script type="text/javascript" src="${CONTEXT_PATH}js/jquery.form.js"></script>
<script type="text/javascript">
    // 分页
    var total = "${domain.page.totalCount}";
    var pageNo = "${domain.page.pageNo}";
    var pageSize = "${domain.page.pageSize}";
    $("#pager").initPager({ total : total, pageNo : pageNo, pageSize : pageSize});

    var heights = $("body").height();
    if(heights>910){
        $("#fixed-bottom").addClass("fixed-bottom-height")
    }

    // 全选
    var checkAll = $("input[name='checkAll']");

    // 子选项
    var itemIds = $("input[name='inIds']");
    checkAll.change(
        function () {
            itemIds.prop("checked", $(this).prop("checked"));
            itemIds.each(function(){
                var f = $(this).is(":checked");
                var checkClass = $(this).prop("class");
                $("." + checkClass).each(function(){
                    $(this).prop("checked",f);
                })
            })
        }
    );


    var statuses = ${domain.statuses};
    $("input[name='query.status']").select2({
        data : statuses,
        placeholder : "入库单状态",
        allowClear : true
    });

    $.getJSON(CONTEXT_PATH + "system/saleusers/userByRole?roleId=", function(json){
        if (json) {
            $("input[name='query.createUser']").select2({
                data : json,
                placeholder : "入库人",
                allowClear : true
            });
            $("input[name='query.checkManager']").select2({
                data : json,
                placeholder : "审核主管",
                allowClear : true
            });
            $("input[name='query.qcUser']").select2({
                data : json,
                placeholder : "审核质控",
                allowClear : true
            });
        } else {
            $("input[name='query.createUser']").attr("placeholder", "该角色没有相关人员!").attr("readonly", true);
            $("input[name='query.checkManager']").attr("placeholder", "该角色没有相关人员!").attr("readonly", true);
            $("input[name='query.qcUser']").attr("placeholder", "该角色没有相关人员!").attr("readonly", true);
        }
    });

    function checkOrder(type, id) {
        //废弃
        if (type == "SCRAP") {
            doCheckOrder(type, null, id);
        } else {
            var diglog = dialog({
                title:"耗材入库单审核",
                width: 500,
                height: 150,
                url: CONTEXT_PATH + "packagingMaterialInventory/materialInventoryAudit",
                okValue: '确定',
                ok: function () {
                    var batchCommentWindow = $(this.iframeNode.contentWindow.document.body);
                    var submitForm = batchCommentWindow.find("#submit-form");
                    var auditValue = submitForm.find("input[name='auditValue']:checked").val();
                    var remark = submitForm.find("#remark").val();
                    if ((auditValue!=null && auditValue!='' && auditValue=="false") && (remark==null || remark=='' || remark == undefined)){
                        layer.alert("驳回时需要填写备注！","error");
                        return ;
                    }
                    if(submitForm) {
                        var type = "PASS";
                        if(auditValue!=null && auditValue!='' && auditValue=="false"){
                            type = "RETURN_DOWN";
                        }
                        doCheckOrder(type,remark,id);
                    }
                    setTimeout(function() {
                        diglog.close().remove();
                    }, 100);
                    return false;
                },
                cancelValue:'取消',
                cancel: function () {}

            });
            diglog.show();
        }
    }

    function doCheckOrder(type, remark, id) {
        $.ajax({
            url: CONTEXT_PATH + "materialCheckIn/checkOrder",
            type: "POST",
            data: {type: type, remark: remark, id: id},
            success: function (result) {
                customizeLayer2(result);
            }
        });
    }

    function editOrder(id,quantity) {
        layer.prompt({
            formType: 0,
            value: quantity,
            title: '修改入库单数量',
            maxlength: 140,
        }, function(value, index, elem){
            $.ajax({
                url: CONTEXT_PATH + "materialCheckIn/editOrder",
                type: "POST",
                data: {id:id, quantity:value},
                success: function (result) {
                    customizeLayer2(result);
                }
            });
        });
    }
    // 获取选中的id
    function getCheckedIds() {
        var checkedIds = $("input[name='inIds']:checked");
        return checkedIds;
    }

    //批量推送信息到采购
    function batchPushMaterialCheckIn() {
        var checkedDatas = getCheckedIds();
        if(checkedDatas.length == 0) {
            layer.alert("请选择要操作的入库单");
            return;
        }
        var inIds = "";
        for(var i = 0; i < checkedDatas.length; i++) {
            var checkIn = checkedDatas[i];
            var inId = $(checkIn).val();
            inIds += inId;
            if(i != checkedDatas.length - 1) {
                inIds += ",";
            }
        }
        $.ajax({
            url:CONTEXT_PATH +"materialCheckIn/batchPush",
            type:"POST",
            data:{inIds:inIds},
            success : function(response){
                customizeLayer2(response);
            }
        });

    }

    function downloadMaterialCheckIn(){
        var checkedIds = getCheckedIds();
        var diglog = dialog({
            title: '导出',
            width: 800,
            height:300,
            url: CONTEXT_PATH + "apvs/downloadmode?type=1",
            okValue: '确定',
            ok: function () {
                var exportWindow = $(this.iframeNode.contentWindow.document.body);

                var submitForm = exportWindow.find("#submit-form");

                var exportType = submitForm.find("input[name='exportType']:checked").val();

                var submitFormParam = submitForm.serialize();

                // 导出当前选择
                if(exportType == 3) {
                    if(checkedIds.length == 0) {
                        layer.alert("请选择要操作的数据");
                        return false;
                    }else if (checkedIds.length > 300) {
                        layer.alert("选择数量不能超过300!");
                        return false;
                    }

                    submitFormParam = submitFormParam + "&" +checkedIds.serialize();
                }

                //还原分页
                $("#page-no").val("${domain.page.pageNo}");

                var action = document.materialCheckInForm.action;
                var target = document.materialCheckInForm.target;
                var method = document.materialCheckInForm.method;
                document.materialCheckInForm.action= CONTEXT_PATH + "materialCheckIn/download?" + submitFormParam;
                document.materialCheckInForm.target="_blank";
                document.materialCheckInForm.method="POST";
                document.materialCheckInForm.submit();
                document.materialCheckInForm.target=target;
                document.materialCheckInForm.action=action;
                document.materialCheckInForm.method=method;

                $("#page-no").val("1");

                setTimeout(function () {
                    diglog.close().remove();
                }, 100);

                return true;
            },
            cancelValue: '取消',
            cancel: function () {}
        });
        diglog.show();
    }
</script>
</body>
</html>