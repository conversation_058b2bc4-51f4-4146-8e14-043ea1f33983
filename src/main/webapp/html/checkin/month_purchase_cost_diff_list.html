<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">
<html>
<head>
	<title>易世通达仓库管理系统</title>
	<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
	<#include "/common/include.html">
	<style type="text/css">
		.modal-body input {
			width: 240px;
		}
		.check-in-detail th{
			background-color:#E0E0E0 !important;
		}
	</style>
</head>
<body>
<@header method="header" active="13010000"><#include "/ftl/header.ftl"></@header>

<div id="page" style="background-color: rgb(231, 237, 248)">
	<div class="row">
		<div class="col-md-12" style="padding: 0">
			<ul class="page-breadcrumb breadcrumb" style="background-color: white;">
				<li><a href="#">仓库报表</a></li>
				<li><a href="#">入库</a></li>
				<li class="active">每月采购运费差异</li>
			</ul>
		</div>
	</div>

	<!-- 内容 -->
	<div class="container-fluid" style="background-color: white;border: none">
		<div class="row">
			<div class="col-md-12">
				<#assign query = domain.query>
				<form autocomplete="off" action="${CONTEXT_PATH}monthCostDiff/search"
						   class="form-horizontal form-bordered form-row-stripped"
						   method="post" modelAttribute="domain" name="monthCostDiffForm" id ="domain">
					<!-- 分页信息 -->
					<input id="page-no" type="hidden" name="page.pageNo" value="1">
					<input id="page-size" type="hidden" name="page.pageSize" value="${domain.page.pageSize}">

					<div class="form-body">
						<div class="form-group">
							<label class="control-label col-md-1">月份</label>
							<div class="col-md-2">
								<input class="form-control Wdate" type="text" name="query.countMonth" placeholder="" readonly="readonly" value="${query.countMonth }" onfocus="WdatePicker({startDate:'%y-%M',dateFmt:'yyyy-MM',alwaysUseStartDate:true})">
							</div>
							<label class="control-label col-md-1">采购单号</label>
							<div class="col-md-2">
								<input class="form-control" type="text" name="query.purchaseOrderNo" placeholder="请输入采购单号,多个用英文逗号分割" value="${query.purchaseOrderNo }">
							</div>
							<label class="control-label col-md-1">是否统计汇总</label>
							<div class="col-md-2">
								<select class="form-control" name="query.doCount" value="${query.doCount}">
									<option value="false">否</option>
									<option <#if query.doCount == true>selected</#if> value="true">是</option>
								</select>
							</div>
							<label class="control-label col-md-1">是否过滤差异为0数据</label>
							<div class="col-md-2">
								<select class="form-control" name="query.filterZero" value="${query.filterZero}">
									<option value="false">否</option>
									<option <#if query.filterZero == true>selected</#if> value="true">是</option>
								</select>
							</div>

						</div>

					</div>
					<div>
						<div class="col-md-offset-12" style="text-align: right">
                            <@header method="auth" authCode="PURCHASE_DIFF_PER_MONTH_DOWNLOAD">
							<button type="button" class="btn btn-default" onclick="downloadCostDiffList()">
								<i class="icon-download"></i> 导出
							</button>
                            </@header>
							<button type="button" onclick="formReset(this)" class="btn btn-default">
								<i class="icon-refresh"></i> 重置
							</button>
							<button type="submit" class="btn blue">
								<i class="icon-search"></i> 查询
							</button>
						</div>
					</div>
				</form>
			</div>
			<br/>
		</div>

		<div class="row">
			<div id="fixedDiv" class="col-md-12">
				<table class="table table-striped table-bordered table-hover table-condensed" id="fixedTab">
					<colgroup>
						<col width="20%" />
						<col width="20%" />
						<col width="20%" />
						<col width="20%" />
						<col width="20%" />
					</colgroup>
					<thead>
					<tr>
						<th>月份</th>
						<th>采购单号</th>
						<th>核销完成时间</th>
						<th>分摊运费差异</th>
						<th>详细</th>
					</tr>
					</thead>
				</table>
			</div>
			<div class="col-md-12" id="task-list-warp">
				<!-- 内容 -->
				<table class="table table-striped table-bordered table-hover table-condensed" id="task-list">
					<colgroup>
						<col width="20%" />
						<col width="20%" />
						<col width="20%" />
						<col width="20%" />
						<col width="20%" />
					</colgroup>
					<thead>
					<tr>
						<th>月份</th>
						<th>采购单号</th>
						<th>核销完成时间</th>
						<th>分摊运费差异</th>
						<th>详细</th>
					</tr>
					</thead>
					<tbody>
					<#list domain.monthPurchaseCostDiffs as diff>
						<tr>
							<td>${diff.countMonth}</td>
							<td>${diff.purchaseOrderNo}</td>
							<td>${diff.finishTime}</td>
							<td>${diff.costDiff}</td>
							<td>
								<button type="button" class="btn btn-info btn-xs" onclick="getDetail(${diff.PId})">详情</button>
							</td>
						</tr>
					</#list>
					<#if domain.query.doCount == 'true'>
						<tr style="color: red">
							<td>汇总</td>
							<td>${domain.orderTotal}单</td>
							<td></td>
							<td>${domain.diffTotal}</td>
							<td></td>
						</tr>
					</#if>
					</tbody>
				</table>
				<!-- 内容end -->
			</div>
		</div>
		<div id="fixed-bottom">
			<div id="pager"></div>
		</div>
	</div>
    
	<#include "/common/footer.html">
</div>

<script type="text/javascript" src="${CONTEXT_PATH}js/datepicker/WdatePicker.js"></script>
<script type="text/javascript" src="${CONTEXT_PATH}js/table-thead-fixed.js"></script>
<script type="text/javascript">

    // 分页
    var total = "${domain.page.totalCount}";
	var pageNo = "${domain.page.pageNo}";
	var pageSize = "${domain.page.pageSize}";
	$("#pager").initPager({ total : total, pageNo : pageNo, pageSize : pageSize});

    var heights = $("body").height();
	if(heights>910){
        $("#fixed-bottom").addClass("fixed-bottom-height")
	}

	// 状态
	/*var statusJson = ${domain.statusJson};
	$("input[name='query.finishStatus']").select2({
		data : statusJson,
		placeholder : "是否已完结",
		allowClear : true
	});*/

	/*明细*/
	function getDetail(id) {
		window.open(CONTEXT_PATH + "costApportion/detail?id=" + id);
	}

	// 导出
	function downloadCostDiffList() {
		var params = $('#domain').serialize();
		downloadByPostForm(params, "download?");
	}
</script>
</body>
</html>