<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">
<html>
<head>
<title>易世通达仓库管理系统</title>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
	<#include "/common/include.html">
	<style type="text/css">
.modal-body input {
	width: 240px;
}
.top_bar{
    position:fixed;top:0px;
}
#task-list td {
	vertical-align:middle;
}
</style>
</head>
<body>
<@header method="header" active="11020000"><#include "/ftl/header.ftl"></@header>

	<div id="page" style="background-color: rgb(231, 237, 248)">
		<div class="row">
			<div class="col-md-12" style="padding: 0">
				<ul class="page-breadcrumb breadcrumb" style="background-color: white;">
					<li><a href="#">库存管理</a></li>
					<li class="active">外借单管理</li>
					<li class="active">外借出库单明细</li>
				</ul>
			</div>
		</div>
		<!-- 内容 -->
		<div class="container-fluid" style="background-color: white;border: none">
		<#assign query=domain.query>
		<!-- END PAGE HEADER-->
		<div class="row">
			</br>
		</div>
		
		<div class="row">
			<div class="col-md-12">	
			
					<table>
						<tr>
							<td>外借单号 :</td>
							<td style="width:200px;height: 60px">${ domain.whLend.lendCheckOutCode}</td>
							<td>接收人:</td>
							<td style="width:200px;">${ domain.whLend.acceptBy}-${ domain.whLend.acceptByName}</td>
							<td>外借原因:</td>
							<td>${ domain.whLend.lendReason}</td>
						</tr>
						<tr>
							<td>是否需要归还 :</td>
							<td style="width:200px;height: 60px">${ (domain.whLend.isNeedReturn==1)?string('是' , '否')}</td>
							<td>计划归还日期:</td>
							<td style="width:200px;">${ domain.whLend.planReturnTime}</td>
							<td></td>
							<td></td>
						</tr>
					</table>
					<table class="table table-striped table-bordered table-hover table-condensed" id="task-list">
						<colgroup>
							<col width="10%" />
							<col width="10%" />
							<col width="15%" />
							<col width="15%" />
							<col width="10%" />
							<col width="10%" />
							<col width="10%" />
							<col width="10%" />
							<col width="10%" />
						</colgroup>
						<thead>
							<tr>
								<th>编号</th>
								<th>sku</th>
								<th>库存ID</th>
								<th>名称</th>
								<th>报废批次/到期时间</th>
								<th>外借数量</th>
								<th>拣货数量</th>
								<th>单价</th>
								<th>金额</th>
							</tr>
						</thead>
						<tbody>
							<#list domain.whLend.items as item>
								<tr>
									<td>${item.id}</td>
									<td>${item.sku}</td>
									<td>${item.stockId}</td>
									<td>${item.name}</td>
									<td>${item.batchNoInfo}</td>
									<td>${item.quantity}</td>
									<td>${item.pickQuantity!0}</td>
									<td>${item.price}</td>
									<td>${item.amount}</td>
								</tr>
							</#list>
							<tr>
								<td>汇总</td>
								<td>${domain.whLend.skuSpecies}</td>
								<td colspan="3"></td>
								<td>${domain.whLend.skuTotal}</td>
								<td>${domain.whLend.skuPickingTotal}</td>
								<td>${domain.totalPrice}</td>
								<td>${domain.totalAmount}</td>
							</tr>
						</tbody>
					</table>
			</div>
		</div>
	</div>
	
	<#include "/common/footer.html">
	</div>
	<script type="text/javascript" src="${CONTEXT_PATH}js/datepicker/WdatePicker.js"></script>
	<script type="text/javascript">   
	</script>
</body>
</html>