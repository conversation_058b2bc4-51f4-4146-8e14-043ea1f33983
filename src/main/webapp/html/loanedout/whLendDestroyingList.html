<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">
<html>

	<head>
		<title>易世通达仓库管理系统</title>
		<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
		<#include "/common/include.html">
		<style type="text/css">
			.modal-body input {
				width: 240px;
			}

			.top_bar {
				position: fixed;
				top: 0px;
			}

			.btn-xs {
				padding: 0px 5px;
			}

			#task-list td {
				vertical-align: middle;
			}

			#add_modal{
			    margin-top:50px;overflow:hidden;
			}
		</style>
	</head>

	<body>
		<@header method="header" active="11020000"><#include "/ftl/header.ftl"></@header>

		<div id="page" style="background-color: rgb(231, 237, 248)">
			<div class="row">
				<div class="col-md-12" style="padding: 0">
					<ul class="page-breadcrumb breadcrumb" style="background-color: white;">
						<li>
							<a href="#">库存管理</a>
						</li>
						<li class="active">外借单核销</li>
					</ul>
				</div>
			</div>
			<!-- 内容 -->
			<div class="container-fluid" style="background-color: white;border: none">
				<div class="row">
					<div class="col-md-12 col-new-wms-8">
						<#assign query=domain.query>
						<form action="${CONTEXT_PATH}whLendDestroying/search" class="form-horizontal form-bordered form-row-stripped" method="post" modelAttribute="domain" name="whAsnForm" id="domain">
							<!-- 分页信息 -->
							<input id="page-no" type="hidden" name="page.pageNo" value="1">
							<input id="page-size" type="hidden" name="page.pageSize" value="${domain.page.pageSize}">
							<div class="form-body">
								<div class="form-group">
									<label class="control-label col-md-1" style="width:40px">SKU:</label>
									<div class="col-md-2" style="width:200px">
										<input class="form-control" type="text" name="query.sku" placeholder="多个逗号分开" value="${query.sku }">
									</div>
									<label class="control-label col-md-1" style="width:40px">状态:</label>
									<div class="col-md-2" style="width:150px">
										<input class="form-control" type="text" name="query.status" value="${query.status}">
									</div>
									<label class="control-label col-md-1" style="width:65px">外借单号:</label>
									<div class="col-md-2" style="width:300px">
										<input class="form-control" type="text" name="query.lendCheckOutCode" placeholder="多个逗号分开" value="${query.lendCheckOutCode }">
									</div>
									<label class="control-label col-md-1" style="width:65px">创建时间:</label>
									<div class="col-md-2" style="width:150px">
                                        <input class="form-control Wdate" type="text" name="query.fromCreationDate" placeholder="" readonly="readonly" value="${query.fromCreationDate }" onfocus="WdatePicker({startDate:'%y-%M-%d 00:00:00',dateFmt:'yyyy-MM-dd HH:mm:ss',alwaysUseStartDate:true})">
									</div>
                                    <label class="control-label col-md-1" style="width:25px">到</label>
                                    <div class="col-md-2" style="width:150px">
                                        <input class="form-control Wdate" type="text" name="query.toCreationDate" placeholder="" readonly="readonly" value="${query.toCreationDate }" onfocus="WdatePicker({startDate:'%y-%M-%d 23:59:59',dateFmt:'yyyy-MM-dd HH:mm:ss',alwaysUseStartDate:true})">
                                    </div>
									<label class="control-label col-md-1" style="width:90px">计划归还时间:</label>
									<div class="col-md-2" style="width:150px">
										<input class="form-control Wdate" type="text" name="query.fromPlanReturnTime" placeholder="" readonly="readonly" value="${query.fromPlanReturnTime }" onfocus="WdatePicker({startDate:'%y-%M-%d 00:00:00',dateFmt:'yyyy-MM-dd HH:mm:ss',alwaysUseStartDate:true})">
									</div>
									<label class="control-label col-md-1" style="width:25px">到</label>
									<div class="col-md-2" style="width:150px">
										<input class="form-control Wdate" type="text" name="query.toPlanReturnTime" placeholder="" readonly="readonly" value="${query.toPlanReturnTime }" onfocus="WdatePicker({startDate:'%y-%M-%d 23:59:59',dateFmt:'yyyy-MM-dd HH:mm:ss',alwaysUseStartDate:true})">
									</div>

									<label class="control-label col-md-1" style="width:90px">是否存在超时:</label>
									<div class="col-md-2" style="width:100px">
										<select class="form-control" name="query.isOverTime" value="${query.isOverTime}">
											<option ></option>
											<option <#if query.isOverTime == 0>selected</#if> value="0">否</option>
										<option <#if query.isOverTime == 1>selected</#if> value="1">是</option>
									</select>
									</div>
								</div>
                                <div class="form-group">
									<label class="control-label col-md-1" style="width:50px">库存ID:</label>
									<div class="col-md-2" style="width:200px">
										<input class="form-control" type="text" name="query.stockIdStr" placeholder="多个逗号分开" value="${query.stockIdStr }">
									</div>

                                </div>
							</div>

							<div>
								<div class="col-md-offset-10" style="text-align: right">
									<button type="button" class="btn default" onclick="formReset(this)">
										<i class="icon-refresh"></i> 重置
									</button>
                                    <@header method="auth" authCode="WH_LEND_DESTORYING_DOWNLOAD">
                                    <button type="button" class="btn btn-default" onclick="download()">
                                        <i class="icon-download"></i>导出
                                    </button>
                                    </@header>
									<button type="submit" class="btn blue">
										<i class="icon-search"></i> 查询
									</button>
								</div>
							</div>
						</form>
					</div>
					<br/>
				</div>

				<div class="row">
					<div id="fixedDiv" class="col-md-12">
						<table class="table table-striped table-bordered table-hover table-condensed" id="fixedTab">
							<colgroup>
								<col width="5%" />
								<col width="10%" />
								<col width="5%" />
								<col width="5%" />
								<col width="5%" />
								<col width="5%" />
								<col width="5%" />
								<col width="10%" />
								<col width="10%" />
								<col width="10%" />
								<col width="10%" />
								<col width="10%" />
								<col width="10%" />
							</colgroup>
							<thead>
								<tr>
									<th>选择</th>
									<th>关联出库单号</th>
									<th>sku</th>
									<th>库存ID</th>
									<th>核销数量</th>
									<th>出库单价</th>
									<th>出库金额</th>
									<th>核销状态</th>
									<th>创建人</th>
									<th>创建时间</th>
									<th>接收人</th>
									<th>计划归还时间</th>
									<th>操作</th>
								</tr>
							</thead>
						</table>
					</div>
					<div class="col-md-12" id="task-list-warp">
						<table class="table table-striped table-bordered table-hover table-condensed" id="task-list">
							<colgroup>
								<col width="5%" />
								<col width="10%" />
								<col width="5%" />
								<col width="5%" />
								<col width="5%" />
								<col width="5%" />
								<col width="5%" />
								<col width="10%" />
								<col width="10%" />
								<col width="10%" />
								<col width="10%" />
								<col width="10%" />
								<col width="10%" />
							</colgroup>
							<thead>
									<th><label class="checkbox-inline"><input type="checkbox" id="check-all" name="checkAll"> 选择</label></th>
									<th>关联出库单号</th>
									<th>sku</th>
									<th>库存ID</th>
									<th>核销数量</th>
									<th>出库单价</th>
									<th>出库金额</th>
									<th>核销状态</th>
									<th>创建人</th>
									<th>创建时间</th>
									<th>接收人</th>
									<th>计划归还时间</th>
									<th>操作</th>
								</tr>
								</tr>
							</thead>
							<tbody>
								<#list domain.whLendDestroyings as whLendDestroying>
										<tr>
											<td>
												<label class="checkbox-inline"><input type="checkbox" value="${whLendDestroying.id}" name="ids"> ${whLendDestroying.id}</label>
											</td>
											<td>${whLendDestroying.lendCheckOutCode}</td>
											<td>${whLendDestroying.sku}</td>
											<td>${whLendDestroying.stockId}</td>
											<td>${whLendDestroying.destroyQuantity}</td>
											<td>${whLendDestroying.price}</td>
											<td>${whLendDestroying.amount}</td>
											<td>${util('enumName', 'com.estone.loanedout.enums.LendDestroyingStatus', whLendDestroying.status)}</td>
											<td>
                                                <#if "pms" == whLendDestroying.source?lower_case>
                                                    ${whLendDestroying.createdNoAndName}
                                                <#else>
                                                    ${util('name',whLendDestroying.createdBy)}
                                                </#if>
                                            </td>
											<td>${whLendDestroying.creationDate}</td>
											<td>${whLendDestroying.acceptBy}-${whLendDestroying.acceptByName}</td>
											<td>${whLendDestroying.planReturnTime}</td>
											<td>
												<a class="btn btn-xs btn-default" onclick="viewLog(${whLendDestroying.id}, 'whLendDestroy')">
													日志
												</a>
											</td>
										</tr>
								</#list>
							</tbody>
						</table>
					</div>
				</div>
				<div id="fixed-bottom">
					<div id="pager"></div>
				</div>
			</div>

			<#include "/common/footer.html">
		</div>


		<script type="text/javascript" src="${CONTEXT_PATH}js/datepicker/WdatePicker.js"></script>
		<script type="text/javascript" src="${CONTEXT_PATH}js/jquery.form.js"></script>
		<script type="text/javascript" src="${CONTEXT_PATH}js/table-thead-fixed.js"></script>
		<script type="text/javascript" src="${CONTEXT_PATH}js/assets/plugins/jquery.region.js"></script>
		<script type="text/javascript">
			// 分页
			var total = "${domain.page.totalCount}";
			var pageNo = "${domain.page.pageNo}";
			var pageSize = "${domain.page.pageSize}";

			$("#pager").initPager({ total: total, pageNo: pageNo, pageSize: pageSize });
			var heights = $("body").height();
			if(heights > 910) {
				$("#fixed-bottom").addClass("fixed-bottom-height")
			}

			// 全选
			var checkAll = $("input[name='checkAll']");
			// 子选项
			var itemIds = $("input[name='ids']");
			checkAll.change(
				function() {
					itemIds.prop("checked", $(this).prop("checked"));
					itemIds.each(function() {
						var f = $(this).is(":checked");
						var checkClass = $(this).prop("class");
						$("." + checkClass).each(function() {
							$(this).prop("checked", f);
						})
					})
				}
			);
			// 获取选中的记录
			function getCheckedIds() {
				var checkedIds = $("input[name='ids']:checked");
				return checkedIds;
			}

			// 状态
			var resultJson = ${domain.statusJson};
			$("input[name='query.status']").select2({
				data : resultJson,
				placeholder : "状态",
				allowClear : true
			});


            function download() {
                var params = $('#domain').serialize();
                var ids = getCheckedIds();
                var checkIds = "";
                for (var i = 0; i < ids.length; i++) {
                    var outId = ids[i].value;
                    if (i == 0) {
                        checkIds += outId;
                    } else {
                        checkIds += "," + outId;
                    }
                }
                if (ids.length != 0){
                    params = params +"&ids="+checkIds;
                }
                $.post(CONTEXT_PATH + "whLendDestroying/download", params, function(data){
                    if (data.status == 200) {
                        if (data.message==null || data.message==''){
                            layer.alert('成功',function (index) {
                                layer.close(index);
                                diglog.close().remove();
                                location.reload();
                            });
                        }else{
                            customizeLayer(data.message);
                        }
                    } else {
                        customizeLayer(data.message);
                    }
                });
            }
		</script>
	</body>

</html>