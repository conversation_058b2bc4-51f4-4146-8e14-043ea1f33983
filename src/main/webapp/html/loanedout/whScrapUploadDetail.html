<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">
<html>
<head>
<title>易世通达仓库管理系统</title>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
	<#include "/common/include.html">
	<style type="text/css">
.modal-body input {
	width: 240px;
}
.top_bar{
    position:fixed;top:0px;
}
#task-list td {
	vertical-align:middle;
}
		input {border :none;}
</style>
</head>
<body>
<@header method="header" active="11020000"><#include "/ftl/header.ftl"></@header>

	<div id="page" style="background-color: rgb(231, 237, 248)">
		<div class="row">
			<div class="col-md-12" style="padding: 0">
				<ul class="page-breadcrumb breadcrumb" style="background-color: white;">
					<li><a href="#">库存管理</a></li>
					<li class="active">报废单管理</li>
					<li class="active"> 报废单明细</li>
				</ul>
			</div>
		</div>
		<!-- 内容 -->
		<div class="container-fluid" style="background-color: white;border: none">
		<#assign query=domain.query>
		<!-- END PAGE HEADER-->
		<div class="row">
			</br>
		</div>
		
		<div class="row">
			<div class="col-md-12">
					<table>
						<tr>
							<td>报废单号 :</td>
							<td style="width:200px;height: 60px">${ domain.whScrap.scrapNumber}</td>
							<input type="hidden" id="whScrapId" value="${domain.whScrap.id}"/>
						</tr>

					</table>
					<table class="table table-striped table-bordered table-hover table-condensed" id="task-list">
						<colgroup>
							<col width="10%" />
							<col width="10%" />
							<col width="10%" />
							<col width="20%" />
							<col width="10%" />
							<col width="10%" />
							<col width="10%" />
							<col width="10%" />
							<col width="10%" />
						</colgroup>
						<thead>
							<tr>
								<th>编号</th>
								<th>SKU</th>
								<th>库存ID</th>
								<th>名称</th>
								<th>报废批次/到期时间</th>
								<th>报废数量</th>
								<th>报废单价</th>
								<th>报废金额</th>
								<th>
									回收金额 &nbsp;&nbsp;&nbsp;
									<a class="btn btn-xs btn-default" onclick="updateScrapAmount()">
										<span id="editAmont">编辑</span>
									</a>
								</th>
							</tr>
						</thead>
						<tbody>
							<#list domain.whScrap.items as whScrapDetail>
								<tr>
									<td>${whScrapDetail.id}</td>
									<td>${whScrapDetail.sku}</td>
									<td>${whScrapDetail.stockId}</td>
									<td>${whScrapDetail.name}</td>
									<td>${whScrapDetail.batchNoInfo}</td>
									<td>${whScrapDetail.scrapQuantity }</td>
									<td>${whScrapDetail.scrapPrice }</td>
									<td>${whScrapDetail.scrapAmount }</td>
									<#if whScrapDetail_index == 0 >
										<td rowspan="${domain.whScrap.items?size}" id="getRecoveryAmounts"><input name = "whScrap.recoveryAmount" type="text" readonly="readonly" value="${domain.whScrap.recoveryAmount}" style="text-align: center"> </td>
									</#if>
								</tr>
							</#list>
							<tr>
								<td>汇总</td>
								<td>${domain.whScrap.skuNumber}</td>
								<td></td>
								<td></td>
								<td></td>
								<td>${domain.whScrap.number}</td>
								<td></td>
								<td>${domain.totalAmount}</td>
								<td>${domain.whScrap.recoveryAmount}</td>
							</tr>
						</tbody>
					</table>
			</div>
		</div>
	</div>
	
	<#include "/common/footer.html">
	</div>
	<script type="text/javascript" src="${CONTEXT_PATH}js/datepicker/WdatePicker.js"></script>
	<script type="text/javascript" src="${CONTEXT_PATH}js/jquery.form.js"></script>
	<script type="text/javascript" src="${CONTEXT_PATH}js/assets/plugins/jquery.region.js"></script>
	<script type="text/javascript">
		function updateScrapAmount(){
			debugger;
			var editAmont = $("#editAmont").text();

			if(editAmont != '保存'){
				$("input[name='whScrap.recoveryAmount']").removeAttr('readonly');
				$("#editAmont").text("保存");
			} else {
				var recoveryAmount = $("input[name='whScrap.recoveryAmount']").val();
				var whScrapId = $("#whScrapId").val();
				var reg = /^\+?[1-9][0-9]*$|^[+]{0,1}(\d+\.\d{1,10})$/;
				if(!reg.test(recoveryAmount)){
					layer.alert("请输入正确的回收金额，支持正数！", 'error');
					result = false;
					return false;
				}
				$.ajax({
					url:CONTEXT_PATH +"scrap/editRecoveryAmount?id="+whScrapId+"&recoveryAmount="+recoveryAmount,
					type:"GET",
					success : function(response){
						var message = '';
						if (response.message != null) {
							message = response.message
						}
						if (response.status == '500') {
							layer.alert('修改失败：'+message, 'error');
							return;
						} else if (response.status == '200') {
							$("input[name='whScrap.recoveryAmount']").attr('readonly', 'readonly');
							$("#editAmont").text("编辑");
							alert('修改成功：'+message);
							setTimeout(function() {
								window.location.reload();
							}, 1500);
						}
					},
					error:function(){
						layer.alert('操作失败!', 'error');
					}
				});
			}
		}

	</script>
</body>
</html>