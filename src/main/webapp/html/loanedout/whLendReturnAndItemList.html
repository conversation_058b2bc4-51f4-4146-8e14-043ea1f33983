<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">
<html>
<head>
<title>易世通达仓库管理系统</title>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
	<#include "/common/include.html">
	<style type="text/css">
.modal-body input {
	width: 240px;
}
.top_bar{
    position:fixed;top:0px;
}
#task-list td {
	vertical-align:middle;
}
</style>
</head>
<body>
<@header method="header" active="11020000"><#include "/ftl/header.ftl"></@header>

	<div id="page" style="background-color: rgb(231, 237, 248)">
		<div class="row">
			<div class="col-md-12" style="padding: 0">
				<ul class="page-breadcrumb breadcrumb" style="background-color: white;">
					<li><a href="#">库存管理</a></li>
					<li class="active"> <a href="${CONTEXT_PATH}whLendReturn/create">外借单归还管理</a></li>
					<li class="active">外借单归还详情</li>
				</ul>
			</div>
		</div>
		<!-- 内容 -->
		<div class="container-fluid" style="background-color: white;border: none">
		<#assign query=domain.query>
		<!-- END PAGE HEADER-->
		<div class="row">
			</br>
		</div>
		
		<div class="row">
			<div class="col-md-12">	
					<div style="font-size: 18px">外借单归还详情</div>
					<br/>
					<table class="table table-striped table-bordered table-hover table-condensed" id="task-list">
						<colgroup>
							<col width="10%" />
							<col width="8%" />
							<col width="7%" />
							<col width="15%" />
							<col width="15%" />
							<col width="9%" />
							<col width="9%" />
							<col width="9%" />
							<col width="9%" />
							<col width="9%" />
						</colgroup>
						<thead>
							<tr>
								<th>关联出库单号</th>
								<th>sku</th>
								<th>库存ID</th>
								<th>名称</th>
								<th>报废批次/到期时间</th>
								<th>外借数量</th>
								<th>拣货数量</th>
								<th>归还数量</th>
								<th>核销数量</th>
								<th>未归还数量</th>
							</tr>
							</thead>
						<tbody>
							<#list domain.whLendReturns as whLendReturn>
								<tr>
									<#if whLendReturn_index=0 >
										<td rowspan="${domain.whLendReturns?size}">
											${whLendReturn.lendCheckOutCode}</span>
										</td>
									</#if>
									<td>${whLendReturn.sku}</td>
									<td>${whLendReturn.stockId}</td>
									<td>${whLendReturn.name}</td>
									<td>${whLendReturn.batchNoInfo}</td>
									<#if whLendReturn.quantity! >
									<td>
										${whLendReturn.quantity}
									</td>
									<#else >
										<td>0</td>
									</#if>
									<#if whLendReturn.pickQuantity! >
										<td>
											${whLendReturn.pickQuantity}
										</td>
									<#else >
										<td>0</td>
									</#if>

									<#if whLendReturn.returnQuantity! >
										<td>
											${whLendReturn.returnQuantity}
										</td>
									<#else >
										<td>0</td>
									</#if>
									<#if whLendReturn.destroyQuantity! >
										<td>
											${whLendReturn.destroyQuantity}
										</td>
									<#else >
										<td>0</td>
									</#if>
									<#if whLendReturn.noReturnQuantity! >
										<td>
											${whLendReturn.noReturnQuantity}
										</td>
			                        <#else >
										<td>0</td>
									</#if>
								</tr>
							</#list>
						</tbody>
					</table>
			</div>
		</div>
	</div>
	
	<#include "/common/footer.html">
	</div>
	<script type="text/javascript" src="${CONTEXT_PATH}js/datepicker/WdatePicker.js"></script>
	<script type="text/javascript">   
	</script>
</body>
</html>