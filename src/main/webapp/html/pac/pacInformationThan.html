<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">
<html>
<head>
    <title>易世通达仓库管理系统</title>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <#include "/common/include.html">
    <link href="${CONTEXT_PATH}js/assets/plugins/jquery-file-upload/css/jquery.fileupload-ui.css" rel="stylesheet"
          type="text/css"/>
    <style type="text/css">
        .modal-body input {
            width: 240px;
        }

        #task-list td {
            vertical-align: middle;
        }

        .group-inline {
            color: red;
        }

    </style>
</head>
<body>
<@header method="header" active="16030100"><#include "/ftl/header.ftl">
</@header>

<div id="page" style="background-color: rgb(231, 237, 248)">
    <div class="row">
        <div class="col-md-12" style="padding: 0">
            <ul class="page-breadcrumb breadcrumb" style="background-color: white;">
                <li>
                    <a href="#">优选仓</a>
                </li>
                <li class="active">SKU信息对比</li>
            </ul>
        </div>
    </div>
    <!-- 内容 -->
    <div class="container-fluid" style="background-color: white;border: none">

        <!-- BEGIN PAGE HEADER-->
        <#assign query=domain.query>
        <!-- END PAGE HEADER-->
        <div class="row">
            <div class="col-md-12">
                <form action="${CONTEXT_PATH}pacInformationThan/search"
                      class="form-horizontal form-bordered form-row-stripped"
                      method="post" modelAttribute="domain" name="pacInformationForm" id="domain">
                    <!-- 分页信息 -->
                    <input id="page-no" type="hidden" name="page.pageNo" value="1">
                    <input id="page-size" type="hidden" name="page.pageSize" value="${domain.page.pageSize}">
                    <div class="form-body">
                        <div class="form-group">
                            <label class="control-label col-md-1">SKU</label>
                            <div class="col-md-3">
                                <input class="form-control" type="text" name="query.skuSplit" placeholder="多个查询请以英文逗号分开"
                                       value="${query.skuSplit}">
                            </div>
                            <label class="control-label col-md-1">商品ID</label>
                            <div class="col-md-3">
                                <input class="form-control" type="text" name="query.itemIdSplit"
                                       placeholder="多个查询请以英文逗号分开"
                                       value="${query.itemIdSplit }">
                            </div>
                            <label class="control-label col-md-1">是否差异</label>
                            <div class="col-md-3">
                                <select name="query.check" class="form-control">

                                    <#if (query.check == '1')>
                                    <option vlaue=""></option>
                                    <option selected="selected" value="1">否</option>
                                    <option value="0">有</option>
                                    <#else><#if (query.check == '0')>
                                    <option vlaue=""></option>
                                    <option value="1">否</option>
                                    <option selected="selected" value="0">有</option>
                                    <#else>
                                    <option selected="selected" vlaue=""></option>
                                    <option value="1">否</option>
                                    <option value="0">有</option>
                                    </#if>
                                    </#if>

                            </select>
                        </div>

                        </div>

                    </div>
                    <div>
                        <div class="pull-left" style="margin-bottom: 10px;">
                            <@header method="auth" authCode="SKU_MESSAGE_CONTRAST_SYNC_MESSAGE_TO_CAINIAO">
                            <button type="button" class="btn  btn-default" onclick="passBack()">
                                <i class="icon-pickingTask"></i> 同步信息到菜鸟
                            </button>
                            </@header>

<#--                            <button type="button" class="btn  btn-default" onclick="gettingData()">-->
<#--                                <i class="icon-pickingTask"></i> 数据比对-->
<#--                            </button>-->
                        </div>
                        <div class="col-md-offset-12" style="text-align: right">
                            <@header method="auth" authCode="SKU_MESSAGE_CONTRAST_DOWNLOAD">
                            <button type="button" class="btn btn-default" onclick="downloadStocks()">
                                <i class="icon-download"></i> 导出
                            </button>
                            </@header>
                            <button type="button" class="btn btn-default" onclick="formReset(this)">
                                <i class="icon-refresh"></i> 重置
                            </button>
                            <button type="submit" class="btn blue">
                                <i class="icon-search"></i> 查询
                            </button>
                        </div>
                    </div>
                </form>
            </div>
            <br/>
        </div>

        <div class="row">
            <div class="col-md-12" id="task-list-warp">
                <table class="table table-striped table-bordered table-hover table-condensed" id="task-list">
                    <thead>
                    <tr>
                        <th><label class="checkbox-inline"><input type="checkbox" name="checkAll"> 全选</label></th>
                        <th>图片</th>
                        <th>SKU</th>
                        <th>商品ID</th>
                        <th>商品条码</th>
                        <th>商品名称</th>
                        <th>系统净重(g)</th>
                        <th>系统尺寸-长</br>(CM)</th>
                        <th>系统尺寸-宽</br>(CM)</th>
                        <th>系统尺寸-高</br>(CM)</th>

                        <th>包裹重量(g)</th>
                        <th>包裹尺寸-长</br>(CM)</th>
                        <th>包裹尺寸-宽</br>(CM)</th>
                        <th>包裹尺寸-高</br>(CM)</th>

                        <th>菜鸟净重(g)</th>
                        <th>菜鸟尺寸-长</br>(CM)</th>
                        <th>菜鸟尺寸-宽</br>(CM)</th>
                        <th>菜鸟尺寸-高</br>(CM)</th>
                        <th>是否差异</th>
                        <th>同步信息时间</th>
                    </tr>
                    </thead>
                    <tbody>
                    <#list domain.itemInfoEntity as stock>
                    <tr>
                        <td>
                            <label class="checkbox-inline">
                                <input class="${stock.id}" name="ids" type="checkbox" value="${stock.id}">
                                ${stock.id}
                            </label>
                        </td>
                        <td><img src="${stock.imageUrl}" width="50px" height="50px;" onclick="enlarge(this)"/></td>
                        <td>${stock.itemCode}</td>
                        <td>${stock.itemId}</td>
                        <td>${stock.barCode}</td>
                        <td>${stock.name}</td>
                        <#if (stock.sysWeight != '')>
                            <td>${stock.sysWeight}</td>
                        <#else>
                            <td>/</td>
                        </#if>
                        <#if (stock.sysLength != '')>
                            <td>${stock.sysLength}</td>
                        <#else>
                            <td>/</td>
                        </#if>
                        <#if (stock.sysWidth != '')>
                            <td>${stock.sysWidth}</td>
                        <#else>
                            <td>/</td>
                        </#if>
                        <#if (stock.sysHeight != '')>
                            <td>${stock.sysHeight}</td>
                        <#else>
                            <td>/</td>
                        </#if>
                        <#if (stock.packWeight != '')>
                            <td>${stock.packWeight}</td>
                        <#else>
                            <td>/</td>
                        </#if>
                        <#if (stock.packLength != '')>
                            <td>${stock.packLength}</td>
                        <#else>
                            <td>/</td>
                        </#if>
                        <#if (stock.packWidth != '')>
                            <td>${stock.packWidth}</td>
                        <#else>
                            <td>/</td>
                        </#if>
                        <#if (stock.packHeight != '')>
                            <td>${stock.packHeight}</td>
                        <#else>
                            <td>/</td>
                        </#if>
                        <#if (stock.weight != '')>
                            <td>${stock.weight}</td>
                        <#else>
                            <td>/</td>
                        </#if>
                        <#if (stock.length != '')>
                            <td>${stock.length}</td>
                        <#else>
                            <td>/</td>
                        </#if>
                        <#if (stock.width != '')>
                            <td>${stock.width}</td>
                        <#else>
                            <td>/</td>
                        </#if>
                        <#if (stock.height != '')>
                            <td>${stock.height}</td>
                        <#else>
                            <td>/</td>
                        </#if>
                        <#if (stock.check == '1')>
                            <td>无差异</td>
                        <#else> <#if (stock.check == '0')>
                            <td>有差异</td>
                        <#else>
                            <td>/</td>
                        </#if></#if>

                    <td>${stock.syncTime}</td>
                    </tr>
                </#list>
                </tbody>
                </table>
            </div>
        </div>


        <div id="fixed-bottom">
            <div id="pager">
                <ur class="pages">
                    <li class="select"><select>
                        <option>10</option>
                    </select>
                </ur>
            </div>
        </div>
    </div>

    <#include "/common/footer.html">
    <img id="enlarge" style='position:absolute;width:800px;height:800px;top:10%;left:15%;display:none;'/>
</div>
<script type="text/javascript" src="${CONTEXT_PATH}js/datepicker/WdatePicker.js"></script>
<script type="text/javascript" src="${CONTEXT_PATH}js/access.js?v=${.now?datetime}"></script>
<script type="text/javascript" src="${CONTEXT_PATH}js/jquery.form.js"></script>
<script type="text/javascript" src="${CONTEXT_PATH}js/assets/plugins/jquery.region.js"></script>
<script type="text/javascript">
    // 分页
    var total = "${domain.page.totalCount}";
    var pageNo = "${domain.page.pageNo}";
    var pageSize = "${domain.page.pageSize}";

    $("#pager").initPager({total: total, pageNo: pageNo, pageSize: pageSize});
    var heights = $("body").height();
    if (heights > 910) {
        $("#fixed-bottom").addClass("fixed-bottom-height")
    }
    // 分页
    var total = "${domain.page.totalCount}";
    var pageNo = "${domain.page.pageNo}";
    var pageSize = "${domain.page.pageSize}";
    $("#pager").initPager({total: total, pageNo: pageNo, pageSize: pageSize});

    var heights = $("body").height();
    if (heights > 910) {
        $("#fixed-bottom").addClass("fixed-bottom-height")
    }



    // 产品图放大
    function enlarge(obj) {
        var url = $(obj).attr("src");
        $("#enlarge").attr("src", url);
        $("#enlarge").show(300);
    }

    $("#enlarge").click(function () {
        $("#enlarge").hide(100);
    });
    // 全选
    var checkAll = $("input[name='checkAll']");
    // 子选项
    var itemIds = $("input[name='ids']");
    checkAll.change(
        function () {
            itemIds.prop("checked", $(this).prop("checked"));
            itemIds.each(function () {
                var f = $(this).is(":checked");
                var checkClass = $(this).prop("class");
                $("." + checkClass).each(function () {
                    $(this).prop("checked", f);
                })
            })
        }
    );

    function getCheckedTaskIds() {
        var checkedTaskIds = $("input[name='ids']:checked");
        return checkedTaskIds;
    }

    //回传菜鸟
    function passBack() {

        var checkedDatas = getCheckedTaskIds();

        if (checkedDatas.length == 0) {
            layer.alert("请选择要操作的数据", 'error');
            return;
        }

        var taskIds = checkedDatas.serialize();

        var r = confirm("确定是否同步信息到菜鸟");
        if (r) {
            $.post(CONTEXT_PATH + "pacInformationThan/passBackCaiNiao?" + taskIds, function (data) {
                if (data.status == 200) {
                    alert(data.message);
                    setTimeout(function () {
                        window.location.reload();
                    }, 1000);
                } else {
                    customizeLayer(data.message);
                }
            });
        }

    }

    // 获取选中的记录
    function getCheckedIds() {
        var checkedIds = $("input[name='ids']:checked");
        return checkedIds;
    }


    // 导出
    function downloadStocks() {
        var checkedDatas = getCheckedIds();

        var diglog = dialog({
            title: '导出',
            width: 350,
            height: 100,
            url: CONTEXT_PATH + "transit/return/downloadmode1",
            okValue: '确定',
            ok: function () {

                var exportWindow = $(this.iframeNode.contentWindow.document.body);
                var submitForm = exportWindow.find("#submit-form");
                var exportType = submitForm.find("input[name='exportType']:checked").val();
                var submitFormParam = submitForm.serialize();
                // 导出当前选择
                if (exportType == 3) {
                    if (checkedDatas.length == 0) {
                        layer.alert("请选择要操作的数据!");
                        return false;
                    } else if (checkedDatas.length > 300) {
                        layer.alert("选择数量不能超过300!");
                        return false;
                    }

                    submitFormParam = submitFormParam + "&" + checkedDatas.serialize();

                }

                //还原分页
                $("#page-no").val("${domain.page.pageNo}");


                var action = document.pacInformationForm.action;
                var target = document.pacInformationForm.target;
                var method = document.pacInformationForm.method;
                document.pacInformationForm.action = CONTEXT_PATH + "pacInformationThan/download?" + submitFormParam;
                document.pacInformationForm.target = "_blank";
                document.pacInformationForm.method = "POST";
                document.pacInformationForm.submit();
                document.pacInformationForm.target = target;
                document.pacInformationForm.action = action;
                document.pacInformationForm.method = method;

                $("#page-no").val("1");

                setTimeout(function () {
                    diglog.close().remove();
                }, 100);

                return true;
            },
            cancelValue: '取消',
            cancel: function () {
            }
        });
        diglog.show();
    }

    function loading() {
        var mask_bg = document.createElement('div')
        mask_bg.id = 'mask_bg'
        mask_bg.style.position = 'absolute'
        mask_bg.style.top = '0px'
        mask_bg.style.left = '0px'
        mask_bg.style.width = '100%'
        mask_bg.style.height = '100%'
        mask_bg.style.backgroundColor = '#777'
        mask_bg.style.opacity = 0.6
        mask_bg.style.zIndex = 10001
        document.body.appendChild(mask_bg)
        var mask_msg = document.createElement('div')
        mask_msg.style.position = 'absolute'
        mask_msg.style.top = '35%'
        mask_msg.style.left = '42%'
        mask_msg.style.backgroundColor = 'white'
        mask_msg.style.border = '#336699 1px solid'
        mask_msg.style.textAlign = 'center'
        mask_msg.style.fontSize = '1.1em'
        mask_msg.style.fontWeight = 'bold'
        mask_msg.style.padding = '0.5em 3em 0.5em 3em'
        mask_msg.style.zIndex = 10002
        mask_msg.innerText = '正在同步...'
        mask_bg.appendChild(mask_msg)
    }

    function loaded() {
        var mask_bg = document.getElementById('mask_bg')
        if (mask_bg != null) mask_bg.parentNode.removeChild(mask_bg)
    }

    //数据比对
    function gettingData() {

        var checkedDatas = getCheckedTaskIds();
        var diglog = dialog({
            title: '数据比对',
            width: 350,
            height: 100,
            url: CONTEXT_PATH + "transit/return/downloadmode1",
            okValue: '比对',
            ok: function () {
                loading();
                var uri = "pacInformationThan/gainData";
                var domain = {};
                var page = {};
                page.pageNo =  ${domain.page.pageNo};
                page.pageSize = ${domain.page.pageSize};
                page.totalCount = ${domain.page.totalCount};
                domain.page = page;

                var query = {};
                query.skuSplit = $("input[name='query.skuSplit']")[0].value;
                query.itemIdSplit = $("input[name='query.itemIdSplit']")[0].value;
                query.check = $("select[name='query.check']")[0].value;
                domain.query = query;

                var exportWindow = $(this.iframeNode.contentWindow.document.body);
                var submitForm = exportWindow.find("#submit-form");
                var exportType = submitForm.find("input[name='exportType']:checked").val();

                // 导出当前选择
                if (exportType == 3) {
                    if (checkedDatas.length == 0) {
                        layer.alert("请选择要操作的数据!");
                        return false;
                    } else if (checkedDatas.length > 300) {
                        layer.alert("选择数量不能超过300!");
                        return false;
                    }
                    uri = uri + "?" + checkedDatas.serialize();
                }
                var param = {
                    domain: JSON.stringify(domain),
                    exportType: exportType,
                };
                $.post(CONTEXT_PATH + uri, param, function (data) {
                    if (data.status == 200) {
                        loaded();
                        layer.alert("成功！");
                        setTimeout(function () {
                            // location.href = CONTEXT_PATH + "skuMoveForm";
                            loaded();
                            diglog.close().remove();
                        }, 3000);
                    } else {
                        loaded();
                        customizeLayer(data.message, "error");
                        // window.location.reload();
                    }
                    location.reload();
                });
                // return true;
            },
            cancelValue: '取消',
            cancel: function () {
            }
        });
        diglog.show();
    }

    function downAscpStocks() {
        var checkedDatas = getCheckedIds();
        if (checkedDatas.length == 0) {
            var param = $("#domain").serialize();

            window.open(CONTEXT_PATH + "pacStoct/downloadASCP?" + param);
        } else {
            var ids = checkedDatas.serialize();
            window.open(CONTEXT_PATH + "pacStoct/downloadASCP?" + ids);
        }
    }

    /**
     * 限制文件类型
     * @param target
     */
    function importPacInventory(target) {

        //检测上传文件的类型
        var filename = target.value;

        var ext, idx;
        if (filename == '') {
            $("#submit-upload").attr("disabled", true);
            layer.alert("请选择需要上传的文件!");
            return;
        } else {
            idx = filename.lastIndexOf(".");
            if (idx != -1) {
                ext = filename.substr(idx + 1).toUpperCase();
                ext = ext.toLowerCase();

                if (ext != 'xls' && ext != 'xlsx') {
                    layer.alert("只能上传.Excel类型的文件!");
                    return;
                }
            } else {
                layer.alert("只能上传.Excel类型的文件!");
                return;
            }
        }

        var r = confirm("确定上传" + filename + "?");

        if (!r) {
            return;
        }

        var uploadUrl = CONTEXT_PATH + "pacStoct/upload";

        var searchUrl = $("#domain").attr("action");

        $("#domain").attr("action", uploadUrl);

        $("#domain").ajaxSubmit(function (data) {
            if (data.status == 200) {
                alert("成功！");
                setTimeout(function () {
                    window.location.reload();
                }, 1000);
            } else {
                customizeLayer(data.message, "error");
            }

            $("#domain").attr("action", searchUrl);
        });

        $("#domain").attr("action", searchUrl);
    }
</script>
</body>
</html>