<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">
<html>
<head>
<meta content="text/html; charset=utf-8" http-equiv="Content-type">
	<title>Print</title>
	<style>
		@media print {
			.printbtn {
				display: none;
			}
		}
	</style>
</head>

<body style="padding: 0px; margin: 20px;">
    <div style="height: 900px;width: 100%;position: absolute;z-index: 9999;background-color: black;opacity: 0.1;">
		<input type="hidden" id="tracking-number"/>
	</div>
	<div id="iframe-container"></div>
	<div id="attachment_label"></div>
	<script src="${CONTEXT_PATH }js/assets/plugins/jquery-1.10.2.min.js" type="text/javascript" ></script>
	<script type="text/javascript" src="${CONTEXT_PATH}js/dist/layer/layer.js"></script>
	<script type="text/javascript" src="${CONTEXT_PATH }js/cn-print.js?v=${.now?datetime}"></script>
	<script type="text/javascript">
		jQuery(document).ready(function() {
            $.getJSON("${CONTEXT_PATH}apv/packs/print/tip?apvNo=${apvNo}", function(data) {
                if (data && data.message) {
                    customizeLayer(data.message);
                }
            });
			var print_content = "<iframe src=\"javascript:void(0)\" frameborder=\"0\" marginheight=\"0\" marginwidth=\"0\" frameborder=\"0\" scrolling=\"auto\" id=\"print_content\" name=\"print_content\" width=\"100%\" height=\"900px\"></iframe>";
			var attachmentLabelContent = "<iframe src=\"javascript:void(0)\" frameborder=\"0\" marginheight=\"0\" marginwidth=\"0\" frameborder=\"0\" scrolling=\"auto\" id=\"attachmentLabelContent\" name=\"attachmentLabelContent\" width=\"100%\" height=\"900px\"></iframe>";

			var shippingOrderFrame = "<iframe src=\"javascript:void(0)\" frameborder=\"0\" marginheight=\"0\" marginwidth=\"0\" frameborder=\"0\" scrolling=\"auto\" id=\"shippingOrderFrame\" name=\"shippingOrderFrame\" width=\"100%\" height=\"900px\"></iframe>";
			var pdfUrl = "${pdfUrl}";
			var attachmentLabelUrl = "${attachmentLabelUrl}";
			debugger;
			if (pdfUrl){
				$('#iframe-container').append(print_content);
                document.getElementById('print_content').src = "${CONTEXT_PATH}" + pdfUrl;
				setTimeout(function() {
					document.getElementById('print_content').contentWindow.print();
				}, 500);

				if (attachmentLabelUrl){
					$('#attachment_label').append(attachmentLabelContent);
					document.getElementById('attachmentLabelContent').src = "${CONTEXT_PATH }apv/packs/oms/pdfFile?printUrl=" + attachmentLabelUrl;
					setTimeout(function() {
						document.getElementById('attachmentLabelContent').contentWindow.print();
					}, 500);
				}

                return false;
            }
			if ("${apiResult.success}" != "true") {
				layer.alert("${apiResult.errorMsg}");
				return false;
			} else {
				var result = '${apiResult.result}';
				if (result.indexOf("dtoJson") < 0){
					$('#iframe-container').append(print_content);
					var data = JSON.parse(JSON.stringify(${apiResult.result}));
					var url = data.url;
					var attachmentLabelUrl=data.attachmentLabelUrl;
					if (url.indexOf("YST") == 0) {
						//document.getElementById('print_content').src = "${util('sysparam','OMS_PARAM.PRINT_OWN_URL')}" + url;
						// 自画面单
						document.getElementById('shippingOrderFrame').src = WEB_CONTEXT_PATH+"print-waybill/${apvNo}";
					} else {
						document.getElementById('print_content').src = "${CONTEXT_PATH }apv/packs/oms/pdfFile?printUrl=" + url;
						setTimeout(function() {
							document.getElementById('print_content').contentWindow.print();
						}, 500);
					}
					if (attachmentLabelUrl){
						$('#attachment_label').append(attachmentLabelContent);
							document.getElementById('attachmentLabelContent').src = "${CONTEXT_PATH }apv/packs/oms/pdfFile?printUrl=" + attachmentLabelUrl;
							setTimeout(function() {
								document.getElementById('attachmentLabelContent').contentWindow.print();
							}, 500);
					}
					return false;
				}

				if (result.indexOf("dtoJson") >= 0) {
					$('#iframe-container').append(shippingOrderFrame);
					var data = JSON.parse(JSON.stringify(${apiResult.result}));
					var trackingNumber = data.trackingNumber;
					var dto_json = data.dtoJson;
					//菜鸟云打印
					$('#tracking-number').val(trackingNumber);
					doPrint(1,trackingNumber,dto_json);
				}
			}
		});
	</script>
</body>
</html>