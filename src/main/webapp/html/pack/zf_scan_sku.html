<style type="text/css">
    #pcak-info-table {
        width: 100%;
        margin-top: 10px;
    }
    #pcak-info-table #btn td{
        border: none;
        background-color: transparent;
        padding-left: 0px;
    }
    #pcak-info-table #apvNo td {
        height: 36px;
        text-align: center;
    }
    #pcak-info-table .br td {
        height: 15px;
        border: none;
        background-color: transparent;
    }
    #pcak-info-table td {
        border: 1px solid #bcbcbc;
        width: 140px;
        height: 50px;
        padding-left: 10px;
        background-color: #F9F9F9;
        font-size: 18px;
    }
    #pcak-info-table td.form-label {
        text-align: center;
        background-color: #d7d7d7;
        font-weight: bold;
        font-size: 14px;
    }
    #pcak-info-table td.red {
        color: red;
    }
    .size-blod {
        font-weight: bold;
        font-size: 30px;
    }
    .skuImgMain>img {
        width: 500px;
        height: 500px;
        float: left;
    }
</style>
<#assign apv = domain.whApv >

<#if (apv)! && apv.status == '6'>
		<!-- 不为空操作 -->
		<div style="border-bottom: 1px #CCC dotted; height: 700px;" class="scan_success">
            <div class="scan_success_sub col-md-6" style="height: 700px">
                <input type="hidden" name="apvId" value="${apv.id }" />
                <input type="hidden" name="apvNo" value="${apv.apvNo }" />
                <input type="hidden" name="waybillSize" value="${domain.waybillSize }" />
                <input type="hidden" name="totalSaleQuantity" value="${apv.totalSaleQuantity }" />
                <input type="hidden" name="logisticsType" value="${apv.logisticsType }" />
                <input type="hidden" name="logisticsCompany" value="${apv.logisticsCompany }" />
                <#if apv.buyerCheckoutList?seq_contains('GPSR')>
                    <input type="hidden" name="gpsrPlatform" value="${apv.platform}"/>
                </#if>
                <table id="pcak-info-table">
                    <tbody>
	                  <#list apv.whApvItems as item>
                      <#assign sku = item.sku?upper_case?trim >
                      <tr id="btn">
                          <td colspan="4">
                              <a class="btn yellow" href="javascript:void(0);" onclick="revoke(${apv.id}, '${apv.apvNo }','${apv.apvType }')">撤销 <i class="icon-undo"></i></a>
                              <#if apv.buyerCheckoutList?seq_contains('GPSR')>
                                  <span style="color: red;font-size: 30px;font-weight: bold; margin-left: 20px">GPSR包装</span>
                              </#if>
                          </td>
                      </tr>
                      <tr class="br"><td colspan="5"></td></tr>
                      <tr id="apvNo">
                          <td class="form-label">发货单号</td>
                          <#if apv.buyerCheckoutList?seq_contains('RFP')>
                              <td colspan="2" class="size-blod">${apv.apvNo}</td>
                              <td class="fire-apv-td">
                                  <span style="background-color: red;border-right: none;font-weight:900; font-size: 30px;width: 200px;line-height: 80px;">
                                      ${util('enumName', 'com.estone.apv.enums.ApvTaxTypeEnum', 'RFP')}
                                  </span>
                              </td>
                          <#else>
                            <td colspan="4" class="size-blod">${apv.apvNo}</td>
                          </#if>
                      </tr>
                      <tr class="br"><td colspan="4"></td></tr>
                      <tr>
                          <td class="form-label">应发数量</td>
                          <td class="size-blod">${item.saleQuantity}</td>
                          <td class="form-label">未扫描数量</td>
                          <td class="check_quantity size-blod red" id="check_quantity">${item.saleQuantity}</td>
                      </tr>
                      <tr class="br"><td colspan="4"></td></tr>
                      <#if apv.buyerCheckoutList?seq_contains('EUR')>
                          <tr>
                              <td style="background-color: #ffdf25;border-right: none;font-weight:900; font-size: 30px;width: 200px;line-height: 80px;" class="fire-apv-td">
                                  ${util('enumName', 'com.estone.apv.enums.ApvTaxTypeEnum', 'EUR')}标签
                              </td>
                              <td style="background-color: #ffdf25;border-left: none;" colspan="4">
                                  <div>
                                      <div style="text-align: center;float: left;">
                                          <div style="float: left;margin-top: 5px;">
                                              <div style="font-weight: 800;border: 1px solid #000000;float: left;width: 60px;line-height: 40px;">EC</div>
                                              <div style="font-weight: 800;border: 1px solid #000000;float: left;width: 60px;border-left: none;line-height: 40px;">REP</div>
                                          </div>
                                          <div style="float: left;margin-left: 2mm;">
                                              <!-- MAC90178-BK 10px会越界-->
                                              <div style="font-weight: 600;font-size: 12px; text-align: left;">SHUNSHUN GmbH</div>
                                              <div style="font-weight: 600;font-size: 12px;text-align: left;">Römeräcker 9 Z2021,76351</div>
                                              <div style="font-weight: 600;font-size: 11px; text-align: left;">Linkenheim-Hochstetten,Germany</div>
                                          </div>
                                      </div>
                                  </div>
                              </td>
                          </tr>
                          <tr class="br"><td colspan="5"></td></tr>
                      </#if>

                      <tr>
                          <td class="form-label">SKU</td>
                          <td class="size-blod check_sku" id="check_sku_${sku}">${item.sku}</td>
                          <td class="form-label">商品名称</td>
                          <td colspan="2">${item.whSku.name}</td>
                      </tr>
                      <tr>
                          <td class="form-label">袋子</td>
                          <td class="size-blod">${item.whSku.packagingName}</td>
                          <td class="form-label">辅助耗材</td>
                          <#if (item.whSku.matchMaterialsName)?? && item.whSku.matchMaterialsName?index_of("充气条") != -1 >
                              <td colspan="2" class="size-blod" style="font-size: 25px;background-color: red;color: white;">${item.whSku.matchMaterialsName}</td>
                          <#elseif (item.whSku.matchMaterialsName)?? && item.whSku.matchMaterialsName?index_of("珍珠棉") != -1>
                              <td colspan="2" class="size-blod" style="font-size: 25px;background-color: #009DD9;color: white;">${item.whSku.matchMaterialsName}</td>
                          <#elseif (item.whSku.matchMaterialsName)?? && item.whSku.matchMaterialsName?index_of("充气袋") != -1>
                              <td colspan="2" class="size-blod" style="font-size: 25px;background-color: #FFA500FF;color: white;">${item.whSku.matchMaterialsName}</td>
                          <#else>
                              <td colspan="2" class="size-blod" style="font-size: 25px;">${item.whSku.matchMaterialsName}</td>
                          </#if>
                      </tr>
                      <tr>
                          <td class="form-label">是否需要原包装</td>
                          <td colspan="4" class="red">
                            <#list domain.whSkuWithPmsInfos as skuWithPms>
                                <#if skuWithPms.sku == item.sku>
                                    <#if (skuWithPms.isOriginalPackage)! && skuWithPms.isOriginalPackage == true>
                                            需要原包装
                                    <#else>
                                            不需要原包装
                                    </#if>
                                    <br><span style="color: black">包装备注：</span>${skuWithPms.packingMemo }
                                </#if>
                            </#list>
                          </td>
                      </tr>
                      <tr>
                          <td class="form-label">商品特性</td>
                          <td colspan="4" class="red size-blod">
                            <#list domain.whSkuWithPmsInfos as skuWithPms>
                                <#if skuWithPms.sku == item.sku>
                                    ${skuWithPms.skuLabelName }
                                    <#if skuWithPms.tag?? && skuWithPms.tag?index_of("磁性")!=-1>
                                        <br>${skuWithPms.tag }
                                    </#if>
                                </#if>
                            </#list>
                            <#if domain.skuTagMap?? && domain.skuTagMap[item.sku]??>
                                <span style="margin-left: 20px;">,${domain.skuTagMap[item.sku]}</span>
                            </#if>
                          </td>
                      </tr>
                      <tr>
                          <td class="form-label">耗材图片</td>
                          <td colspan="4">
                              <#if (item.whSku.matchMaterialPictureList)!>
                                  <#list item.whSku.matchMaterialPictureList as image>
                                    <div style="float: left;margin-left: 10px;">
                                         <img alt="产品缩略图" border="0" width="80px" height="74px" src="${image}"
                                              onclick="enlarge(this)"
                                              onerror="javascript:this.parentElement.style.display='none'"/>
                                    </div>
                                  </#list>
                              </#if>
                          </td>
                      </tr>
                      <tr>
                          <td colspan="5">
                              <a class="btn yellow" href="javascript:void(0);" onclick="createPackagingErrorInformation('${item.sku}')">包装信息纠错</a>
                          </td>
                      </tr>
                      </#list>
                    </tbody>
                </table>


            </div>

            <div class="col-md-4">
                <table style="margin-left: 50px;">
                    <thead>
                    <tr>
                        <th colspan="2">
                            <h2 style="text-align: center;font-size: 25px;font-weight: bolder;">标准包装图片</h2>
                        </th>
                    </tr>
                    </thead>
                    <tbody>
                    <#if (apv.whApvItems[0].whSku.packImageList)!>
                        <tr>
                            <td class="col-md-10">
                                <div class="skuImgMain"><img src="${apv.whApvItems[0].whSku.packImageList[0]}" onclick="enlarge(this)"></div>
                            </td>
                        </tr>
                    <#else>
                        <tr>
                            <td class="col-md-10"></td>
                            <td class="col-md-2"></td>
                        </tr>
                    </#if>
                    </tbody>
                </table>
            </div>
            <img id="enlarge" style='position:fixed;width:800px;height:800px;top:10%;right:15%;display:none;'/>

            <script type="text/javascript">
                function createPackagingErrorInformation(sku){
                    var content = `<span>SKU:`+sku+`</span>
                                   <br/>
                                   <br/>
                                   <div>
                                       <h4 class="modal-title" id="selectDownloadHeadersModalLabel">错误信息：</h4>
                                       <div id="table-header" style="width:330px;">
                                           <label class="radio-inline" style="width:100px;">
                                               <input type="checkbox" name="questionFields" value="1">包材
                                           </label>
                                           <label class="radio-inline" style="width:100px;">
                                               <input type="checkbox" name="questionFields" value="2">搭配包材
                                           </label>
                                           <label class="radio-inline" style="width:100px;">
                                               <input type="checkbox" name="questionFields" value="3">包装图片
                                           </label>
                                       </div>
                                   </div>`;
                    var diglog = dialog({
                        title: '包装信息纠错',
                        width: 350,
                        height:100,
                        content:content,
                        okValue: '确定',
                        ok: function () {
                            var questionFields = [];
                            var questions = $("input[name='questionFields']:checked");
                            if (!questions || questions.length == 0){
                                layer.alert("未选择错误类型!","error");
                                return;
                            }
                            $.each(questions,function(index,item){
                                var val = $(item).val();
                                questionFields.push(val);
                            });
                            var questionStr = "";
                            for (var i = 0; i < questionFields.length; i++) {
                                var questionField = questionFields[i];
                                questionStr += questionField;
                                if (i != questionFields.length - 1) {
                                    questionStr += ",";
                                }
                            }
                            $.ajax({
                                url: CONTEXT_PATH + "/error/information/create",
                                type: "post",
                                data: {sku:sku,questionFields:questionStr},
                                success:function(response){
                                    if (response.status == '500') {
                                        customizeLayer(response.message, 'error');
                                    } else{
                                        alert('已创建纠错信息!');
                                    }
                                },
                                error:function () {
                                    layer.alert("系统异常，操作失败!",'error');
                                }
                            });
                        },
                        cancelValue: '取消',
                        cancel: function () {}
                    });
                    diglog.show();
                }


                $("#enlarge").click(function() {$("#enlarge").hide(100);});
                // 产品图放大
                function enlarge(obj){
                    var url = $(obj).attr("src");
                    $("#enlarge").attr("src", url);
                    $("#enlarge").show(300);
                }
            </script>
        </div>
<#else>
    <#if (domain.errorMsg)!>
        <!-- 为空操作 -->
        <div id = "scan-error" style="font-size:15px ;font-weight:bold" class="error scan_error">产品:${domain.query.sku}  ${domain.errorMsg}</div>
    <#else>
        <!-- 为空操作 -->
        <div  id = "scan-error" style="font-size:15px ;font-weight:bold" class="error scan_error">产品:${domain.query.sku}  不存在或者不是缺货订单已分配状态中!</div>
    </#if>

</#if>