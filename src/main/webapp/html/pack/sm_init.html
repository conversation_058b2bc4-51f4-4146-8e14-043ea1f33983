<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">
<html>
<head>
    <title>易世通达仓库管理系统</title>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
	<#include "/common/include.html">
    <style type="text/css">
        .modal-body input {
            width: 240px;
        }
        #bindBoxModal{
            margin-top: 280px !important;
        }
    </style>
</head>
<body>
	<@header method="header" active="12050000" ><#include "/ftl/header.ftl"></@header>
<div id="page" style="background-color: rgb(231, 237, 248)">
    <div class="row">
        <div class="col-md-12" style="padding: 0">
            <ul class="page-breadcrumb breadcrumb" style="background-color: white;">
                <li><a href="#">发货扫描</a>
                <li class="active">多件合单扫描</li>
            </ul>
        </div>
    </div>
		<#include "/common/pack_bgcolor_selector.html">

    <!-- 内容 -->
    <div class="container-fluid" style="background-color: white;border: none">
        <div class="row">
            <div>
                <div class=" panel layout-panel layout-panel-west" >
                    <div class="panel-tool"></div>
                </div>
                <div >
                    <h3 style="display: inline-block">扫描区域</h3>
                    <input  type="hidden" value="" id="apv-no-now"/>
                    <input  type="hidden" value="" id="ship-service-now"/>

                    <label>拣货任务号/周转筐</label>
                    <input type="text" class="input-mini" name="orderid" id="orderid" onkeypress="if(event.keyCode==13) { inputorderid(this); return false;}"tabindex="4">

                    <label style="margin-left: 10px">SKU</label>
                    <input type="text" class="input-mini" name="apvid" id="apvid" onpaste="return false" onkeypress="if(event.keyCode==13) { inputnext(this); return false;}"tabindex="4">

                    <label style="margin-left: 10px">SKU</label>
                    <input type="text" class="input-mini" name="sku" id="sku" onpaste="return false" onkeypress="if(event.keyCode==13) { inputsku(this, 0); return false;}">

                    <div style="display: inline-block;margin-left: 10px" class="panel-title2" id="panel-title"><h1  style="color:blue;font-size:48px;">成功:<b>0</b></h1></div>
                    <div style="display: inline-block;margin-left: 10px" class="panel-title2" id="panel-floor"></div>
                    <div style="display: inline-block;margin: 0 10px" class="panel-title2" id="panel-title-piece"><h1  style="color:red;font-size:48px;">计数:<b>0</b></h1></div>

                    <!--<button type="button" class="btn red" onclick="rePrint()">
                        <i class="icon-print"></i> 重新打印
                    </button>-->
                    <button type="button" class="btn red" onclick="failPrint()">
                        <i class="icon-print"></i> 打印失败面单
                    </button>
                    <button  type="button" class="btn red" onclick="openPrintDialog()">打印机配置</button>
                    <span id="check-quantity" style="font-size: 36px;font-weight: 900;color: red;margin-left: 20px"></span>
                </div>

                <div class="panel-header" style="overflow: hidden;">

                </div>
                <div style="height: 700px;" class="col-md-12">
                    <div id="check_scan_datas" class="col-md-6"></div>
                    <div id="print-waybill" class="col-md-6">
                        <div style="margin-left: 20px;">
                            <label style="margin-top: 5px">面单尺寸：<span style="font-weight: bold;" id="waybillSize-now"></span></label>
                        </div>
                        <div class="row">
                            <div class="col-md-12">
                                <iframe src="javascript:void(0);" frameborder="0" marginheight="0" marginwidth="0" frameborder="0" scrolling="auto" id="shippingOrderFrame" name="shippingOrderFrame" width="50%" height="500px"></iframe>
                                <iframe src="javascript:void(0)" frameborder="0" marginheight="0" marginwidth="0" frameborder="0" scrolling="auto" id="oms_print_content" name="oms_print_content" width="45%" height="500px"></iframe>
                            </div>
                            <div id="print_tag" style="width:100%;">
                            </div>
                            <div id="print_gpsr_tag" style="display: none;"></div>
                        </div>
                    </div>
                </div>

                <div class="modal fade ui-popup" id="bindBoxModal" tabindex="-1" role="dialog" aria-labelledby="bindBoxModalLabel" aria-hidden="true">
                    <div class="modal-dialog">
                        <div class="modal-content">
                            <div class="modal-header">
                                <button type="button" class="close" data-dismiss="modal" aria-hidden="true"></button>
                            </div>
                            <div class="modal-body form-horizontal portlet">
                                <table style="height: 100px;">
                                    <tr>
                                        <td>包装异常周转筐：</td>
                                        <td>
                                            <input type="text" class="form-control"  name="lessBoxNo">
                                        </td>
                                    </tr>
                                </table>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-info btn-modal" onclick="saveLessInfo()">确定</button>
                                <button type="button" class="btn btn-default btn-modal" data-dismiss="modal">取消</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
	<#include "/common/footer.html">

</div>
<script type="text/javascript" src="${CONTEXT_PATH }js/LodopFuncs.js"></script>
<script type="text/javascript" src="${CONTEXT_PATH }js/web-storage-cache.js"></script>
<script type="text/javascript" src="${CONTEXT_PATH }js/jquery.sound.js?v=${.now?datetime}"></script>
<script type="text/javascript" src="${CONTEXT_PATH }js/pages/pack-style.js?v=${.now?datetime}"></script>
<script type="text/javascript" src="${CONTEXT_PATH }js/packing.js?v=${.now?datetime}"></script>
<script type="text/javascript" src="${CONTEXT_PATH}js/print.js?v=${.now?datetime}"></script>

<script type="text/javascript">
    window.onload = function () {
        getPrinterList();
    };
    var printUrl = "${CONTEXT_PATH}apv/packs/oms/mg/print?apvNo=";

    var uuIdCacheKey = 'CHECK_PACKING_FOR_UUID_CACHE_KEY_SM'+ '_' +  new Date().getTime();
    var cacheKey = "basket_check_success";
    var pieceCacheKey = "basket_check_piece_success";

    //拣货类型 单品多件和 多品多件 快递和FBA 面单不同
    var pickType ;

    var taskNo ;

    //物流公司 EMS需要特殊模板
    var logisticsCompany;

    // 上一次扫描的发货单
    var lastApvId;

    var shipStatus;
    var gpsrPlatform;

    var waybillSize;

    $(document).ready(function(){
        initSkuUuIdStorageCache(uuIdCacheKey);
        pageInit();

        $('#orderid').val('');
        $('#orderid').select().focus();

        var storage = new WebStorageCache();

        if (storage.get(cacheKey)) {
            lastSuc = storage.get(cacheKey);
            $('#panel-title').html('<h1  style="color:blue;font-size:48px;">成功：<b>'+lastSuc+'</b></h1>');
        }

        if (storage.get(pieceCacheKey)) {
            lastSuc = storage.get(pieceCacheKey);
            $('#panel-title-piece').html('<h1  style="color:red;font-size:48px;">计数:<b>'+lastSuc+'</b></h1>');
        }
        initPrinter();
        $('#orderid').focus();

    }); // end ready


    // 初始化
    function pageInit() {
        $('#apvid').val('');

        $('#apvid').select().focus();

        $('#sku').val('');
        // 完成之后不清空显示
        if (!completeCheck()){
            $('#check_scan_datas').html('');
        }
    }


    //周转筐 触发
    function inputorderid(obj){

        if(!obj.value || obj.value.replace(/\s/g,'') == ''){
            layer.alert("请输入拣货任务号/周转筐!");
            return ;
        }

        var orderId = obj.value.replace(/\s/g,'');

        orderId = $.trim(orderId);

        var url = CONTEXT_PATH + "apv/packs/sm/box/scan";
        //掃描少貨周轉筐
        if (orderId && orderId.indexOf("PKYCA") >= 0) {
            url = CONTEXT_PATH + "apv/packs/sm/pkycBox/scan";
        }

        var r= $.ajax({
            url : url,
            data : {box : orderId},
            timeout : 100000,
            success : function(response){

                if(response.status == '200'){
                    $('#apvid').select().focus();
                    pickType = response.message;
                    if (response.body && response.body.whPickingTask){
                        var whPickingTask = response.body.whPickingTask;
                        taskNo = whPickingTask.taskNo;
                    }

                } else {
                    layer.alert(response.message, {closeBtn: 0},function (index) {
                        layer.close(index);
                        $('#orderid').val('');
                        // 找不到订单
                        $('#orderid').select().focus();
                    });
                }
            },
            error:function(){
                layer.alert('扫描失败，请重新扫描');
            }
        });


    }


    // 第一次扫描SKU
    function inputnext(obj){
        if (!jitPrinter || !jitPrinterTag || !jitPrinter75Tag) {
            layer.alert("请先配置打印机",'error');
            return
        }

        var orderid = $("#orderid").val();

        if(!obj.value || obj.value.replace(/\s/g,'') == ''){
            layer.alert("请扫描sku!");
            return ;
        }

        var apvId = obj.value.replace(/\s/g,'');
        apvId = $.trim(apvId);

        //兼容SKU编码
        //apvId = getSkuByBarCode(obj);

        var uuid = apvId.trim();
        if(!(apvId.indexOf("=") == -1)){
            var realSku = apvId.split('=')[0];
            $('#apvid').val(realSku);
            apvId = realSku;
        }

        var url = CONTEXT_PATH + "apv/packs/sm/check/sku";
        //掃描少貨周轉筐
        if (orderid && orderid.indexOf("PKYCA") >= 0) {
            url = CONTEXT_PATH + "apv/packs/sm/check/exceptionSku";
        }

        var r= $.ajax({
            url : url,
            data : {sku : apvId, lastApvId : lastApvId, orderid: orderid, uuid:uuid},
            timeout : 90 * 1000,
            success : function(response){
                $("#check_scan_datas").html(response);
                if (orderid && orderid.indexOf("PKYCA") >= 0){
                    $('#less-scan-btn').attr("disabled", true);
                }
                const errorElement = $(response).find("#scan-error");
                if (response.length > 230 && errorElement.length === 0){
                    var errorMsg = $("#check_scan_datas").find("#scan-error").html();
                    if(errorMsg){
                        layer.alert(errorMsg, {closeBtn: 0},function (index) {
                            layer.close(index);
                            $('#apvid').val("");
                            $('#apvid').focus();
                            addWhUniqueSkuLog(uuid, '');
                        });
                        return;
                    }

                    // 第一次扫描初始化
                    initSkuUuIdStorageCache(uuIdCacheKey);
                    //checkSkuUuIdStorageCache(uuIdCacheKey, uuid);
                    // 扫描成功
                    $('#sku').select().focus();

                    isFocus = false;
                    shipStatus = $(response).find("input[name='shipStatus']").val();
                    gpsrPlatform = $(response).find("input[name='gpsrPlatform']").val();
                    waybillSize = $(response).find("input[name='waybillSize']").val();

                    // 自动扫第一个
                    $("#sku").val(apvId);
                    inputsku(document.getElementById("sku"), 1);

                    // 记录最后一次扫描的订单
                    lastApvId = $(response).find("input[name='apvId']").val();

                    var apvNo = $(response).find("input[name='apvNo']").val();

                    logisticsCompany = $(response).find("input[name='logisticsCompany']").val();

                    shipStatus = $(response).find("input[name='shipStatus']").val();
                    waybillSize = $(response).find("input[name='waybillSize']").val();
                    $("#waybillSize-now").html(waybillSize);
                    addWhUniqueSkuLog(uuid, apvNo);

                    //保存apvNo
                    $("#apv-no-now").val(apvNo);

                    // 要核对的数量
                    var quantity = $(response).find("input[name='totalSaleQuantity']").val();
                    $("#check-quantity").text(quantity);
                    if (gpsrPlatform){
                        audioPlay('gpsr');
                    }else{
                        audioPlay('success');
                    }

                }else {
                    // 找不到订单
                    $('#apvid').select().focus();
                    addWhUniqueSkuLog(uuid, '');
                    audioPlay('error');


                    const errorMessage = errorElement.length ? errorElement.text().trim() : "未匹配到单据，请重试";
                    layer.alert(errorMessage, {
                        closeBtn: false,
                        skin: 'layui-layer-lan'
                    }, function(index) {
                        layer.close(index);
                        $('#apvid').select().focus();
                    });

                }

            },
            error:function(){
                layer.alert('扫描失败，请重新扫描');
                audioPlay('error');
            }
        });
    }

    // 添加唯一码包装日志
    function addWhUniqueSkuLog(uuid, apvNo) {
        if (apvNo){
            addUuIdStorageCache(uuIdCacheKey, uuid);
        }
        var r = $.ajax({
            type : "get",
            url :CONTEXT_PATH+"apv/packs/addWhUniqueSkuLog" ,
            data : {uuid : uuid, apvNo: apvNo, packType: 7},
            timeout : 100000,
            beforeSend : function() {
            },
            success : function(responese) {

            },
            error : function() {
            }
        });
        addPackExceptionRecord(uuid, apvNo);
    }

    // 新增包装异常记录-未匹配发货单
    function addPackExceptionRecord(uuid, apvNo){
        if(apvNo != undefined && apvNo != ''){
            return;
        }
        $.ajax({
            type: "POST",
            url: CONTEXT_PATH+"apv/packs/createPackExceptionUuidItem",
            data: {uuid: uuid, taskNo: taskNo, scanPage: 7, packExceptionType: 2},
            success: function(response) {
            },
            error:function () {
            }
        });
    }

    // 校验唯一码重复扫描
    function checkScanPackingUniqueSku(sku, uuid) {
        var r = $.ajax({
            type : "get",
            url :CONTEXT_PATH+"apv/packs/checkScanPackingUniqueSku" ,
            data : {uuid : uuid},
            timeout : 100000,
            beforeSend : function() {
                App.blockUI();
            },
            success : function(responese) {
                App.unblockUI();
                if (responese.status == '500') {
                    layer.alert(responese.message, {closeBtn: 0},function (index) {
                        layer.close(index);
                        audioPlay('error');
                        $('#sku').val("");
                        $('#sku').focus();
                    });
                    createPackExceptionUuidItem(uuid, responese.message);
                } else {
                    // 前端缓存校验是否重复扫描
                    if(!checkRetrunUuIdStorageCache(uuIdCacheKey, uuid)){
                        layer.alert("唯一码重复扫描！",{closeBtn: 0}, function (index) {
                            layer.close(index);
                            audioPlay('error');
                            $('#sku').val("");
                            $('#sku').focus();
                        });
                        return;
                    }
                    checkIn(sku, uuid);
                }
            },
            error : function() {
                App.unblockUI();
                layer.alert('扫描失败，请重新扫描', {closeBtn: 0},function (index) {
                    layer.close(index);
                    audioPlay('error');
                    $('#sku').val("");
                    $('#sku').focus();
                });
            }
        });
    }

    // 保存唯一码包装异常信息
    function createPackExceptionUuidItem(uuid, errorMsg){
        if(errorMsg == undefined || errorMsg.indexOf('唯一码已绑定发货单') == -1){
            return;
        }
        $.ajax({
            url: CONTEXT_PATH+"apv/packs/createPackExceptionUuidItem",
            type: "POST",
            data: {uuid: uuid, taskNo: taskNo, scanPage: 7, errorMsg: errorMsg, packExceptionType: 1},
            success: function(response) {
            },
            error:function () {
            }
        });
    }

    //再次扫描SKU
    function inputsku(obj, type){
        var sku = obj.value;
        var uuid = sku.trim();

        //订单Id,切分二维码. 如果存在的话
        if(!(sku.indexOf("=") == -1)){
            var realSku = sku.split('=')[0];
            $('#sku').val(realSku);
            sku = realSku;
        }
        //checkIn(sku, uuid);
        // 第一次不校验
        if (type!=1){
            checkScanPackingUniqueSku(sku, uuid);
        }else {
            checkIn(sku, uuid);
        }
    }

    //核对sku和数量 正确(对应的sku数量增加)
    function checkIn(sku, uuid){
        sku = $.trim(sku.toUpperCase());
        if(completeCheck()){
            layer.alert('检查完毕',{closeBtn: 0},function (index) {
                layer.close(index);
                addWhUniqueSkuLog(uuid, '');
            });
            return;
        }
        if ($('[id="check_sku_'+sku+'"]').length==0) {
            sku = "JR" + sku;
            if ($('[id="check_sku_' + sku + '"]').length == 0) {
                clear();
                $('#sku').select().focus();
                layer.alert("SKU不存在或已检查完毕",{closeBtn: 0},function (index) {
                    layer.close(index);
                    addWhUniqueSkuLog(uuid, '');
                    audioPlay('error');
                });
                return;
            }
        }
        var apvNo = $('#check_scan_datas').find("input[name='apvNo']").val();
        var allCheck = true;
        $('.pack-info-table').each(function () {
            // 未扫描数量
            var check_quantity = parseInt( $(this).find('#check_quantity').text());
            //套装未扫描数量
            var suitCheckQuantity = parseInt($(this).find('#suit_check_quantity').text());
            var suitNum = parseInt($(this).find('#skuSuitNeedNum').val());

            if (check_quantity == 0){
                if (!completeCheck()){
                    allCheck = false;
                    return;
                }
                layer.alert('拿多了?',{closeBtn: 0},function (index) {
                    layer.close(index);
                    clear();
                    audioPlay('error');
                    addWhUniqueSkuLog(uuid, '');
                });
                return false;
            }

            var checkQty = check_quantity - 1;
            // 减少数量
            $(this).find('#check_quantity').html(checkQty);

            if (suitNum && (checkQty == 0 || checkQty % suitNum === 0)) {
                // 减少数量
                $(this).find('#suit_check_quantity').html(suitCheckQuantity - 1);
            }
            if (check_quantity > 1) {
                allCheck = false;
                return false;
            }
            // 未扫描数量减少之后是否完成
            if (check_quantity == 1){
                audioPlay('finish');
                if (shipStatus && (shipStatus == '22' || shipStatus == '23')){
                    var skuBarcode = $(this).find('#skuBarcode').val();
                    //打印JIT货品标签
                    printJitTag(apvNo, sku, null, skuBarcode, true);
                }

                if (gpsrPlatform){
                    printGpsrTag(apvNo,sku);
                }

                if (!completeCheck()) {
                    allCheck = false;
                    return false;
                } else {
                    allCheck = true;
                }
            }
        });
        addWhUniqueSkuLog(uuid, apvNo);
        $('#sku').val("");
        $('#sku').focus();
        if (!allCheck) return;
        // 检查是否完成
        clear();
    }


    function completeCheck(){
        var allFinish = true;
        $('.pack-info-table').each(function () {
            var checkQuantity = parseInt($(this).find('#check_quantity').text());
            if (checkQuantity != 0) {
                allFinish = false;
                return false;
            }
        });
        return allFinish;
    }

    //清空核对区
    function clear(){
        if (completeCheck()){

            calsf();

            pageInit();
        }else{
            $('#sku').val("");
            $('#sku').focus();
        }
    };

    // 统计扫描成功和失败的数量
    function calsf(){

        var storage = new WebStorageCache();

        var lastSuc = 0;
        if (storage.get(cacheKey)) {
            lastSuc = storage.get(cacheKey);
        }

        var suc = parseInt(1) + lastSuc;

        storage.set(cacheKey, suc, {exp : 5 * 60 * 60});

        $('#panel-title').html('<h1  style="color:blue;font-size:48px;">成功：<b>'+suc+'</b></h1>');

        // 记录后台
        var apvId = $('#check_scan_datas').find("input[name='apvId']").val();

        var quantity = $('#check_scan_datas').find("input[name='totalSaleQuantity']").val();

        var boxNo = $("#orderid").val();

        var sku = $('#apvid').val();

        var orderid = $("#orderid").val();

        var url = "${CONTEXT_PATH}apv/packs/sm/check/pass";
        var param = {"apvId" : apvId, "totalQuantity" : quantity, "boxNo" : boxNo};
        //掃描少貨周轉筐
        if (orderid && orderid.indexOf("PKYCA") >= 0) {
            url = CONTEXT_PATH + "smPacking/exception/check/pass";
            param = {"apvId" : apvId, "apvNo" : $("#apv-no-now").val(), "boxNo" : boxNo,"sku":sku};
        }
        // PASS
        $.get(url, param, function(response) {
            if(response.status == '200'){

                // 计件
                calsfPiece(quantity);

                printApvNo($("#apv-no-now").val());

                //最后一个扫描完成
                if(response.message == boxNo){
                    $("#orderid").val('');
                    $('#orderid').select().focus();
                }

            } else {
                layer.alert("数据提交失败!请重试。");

            }
        });

    }

    // 计数
    function calsfPiece(quantity){

        if(!quantity) {
            quantity = 1;
        }

        var storage = new WebStorageCache();

        var lastSuc = 0;

        if (storage.get(pieceCacheKey)) {
            lastSuc = storage.get(pieceCacheKey);
            lastSuc = parseInt(lastSuc) + parseInt(quantity);
        } else {
            lastSuc = quantity;
        }

        storage.set(pieceCacheKey, lastSuc , {exp : 5 * 60 * 60});

        $('#panel-title-piece').html('<h1  style="color:red;font-size:48px;">计数:<b>'+lastSuc+'</b></h1>');
    }


    function printApvNo(apvNo){
        if (apvNo){
            bindingApvAndUuIdlist(apvNo, uuIdCacheKey);
            initSkuUuIdStorageCache(uuIdCacheKey);
        }
            debugger
        if (pickType == '8') {
            omsPrint(apvNo,printUrl);
            setTimeout(failPrint(), 500);
        } else if (pickType == '11') {
            //FBA
            failPrint();
        } else if (shipStatus && (shipStatus == '22' || shipStatus == '23')) {
            var r= $.ajax({
                url: CONTEXT_PATH + "fba/packs/localPrintXiangmai?apvNo=" + apvNo,
                timeout : 100000,
                async: false,
                success : function(response){
                    if(response.status == '200'){
                        var jitPdfUrl = response.body.jitPdfUrl;
                        if (!jitPdfUrl) {
                            jitPdfUrl = window.location.origin + CONTEXT_PATH + response.message;
                            document.getElementById('shippingOrderFrame').src = CONTEXT_PATH + response.message;
                        } else {
                            document.getElementById('shippingOrderFrame').src = response.message;
                        }
                        var jitBoxNumber = response.location;
                        printCopies(jitPdfUrl,null,jitPrinter,1,jitBoxNumber);
                    }else{
                        layer.alert(response.message, {closeBtn: 0}, function (index) {
                            layer.close(index);
                        });
                    }
                },
                error:function(){
                    layer.alert('扫描失败，请重新扫描');
                }
            });
        } else {
            omsPrint(apvNo,printUrl);
        }
        $('#apvid').val('');
        $('#apvid').focus();
        isFocus = true;
        setTimeout(focusSku, 1000);
    }

    var isFocus = false;
    $("body").click(function() {isFocus = false;});
    function focusSku(){
        if (isFocus && !$('#apvid').val()) {
            $('#apvid').focus();
            setTimeout(focusSku, 1000);
        }
    }


    // 重新打印
    function rePrint() {
        printApvNo($("#apv-no-now").val());
    }

    //打印失败面单
    function failPrint(){
        var apvNo = $("#apv-no-now").val();
        if (apvNo) {
            //快递
            var printHtml = '';
            var pageLength = "100mm";
            var pageWidth = "100mm";
            if (waybillSize && (waybillSize.indexOf('15') != -1 || waybillSize == '2')) {
                pageWidth = "150mm";
            }
            if (pickType == '8') {
                document.getElementById('oms_print_content').src = CONTEXT_PATH + "apv/packs/expressPrint?apvNo=" + apvNo;
                document.getElementById("oms_print_content").onload = function () {
                    printHtml = document.getElementById("oms_print_content").contentWindow.document.getElementById('print_content').innerHTML;
                    if (printHtml && printHtml.length > 0) {
                        printHtmlCopies(printHtml, pageLength, pageWidth, 1);
                    }
                };
            } else {
                document.getElementById('shippingOrderFrame').src = CONTEXT_PATH + "apvs/failPrint?apvNo=" + apvNo;
                document.getElementById("shippingOrderFrame").onload = function () {
                    printHtml = document.getElementById("shippingOrderFrame").contentWindow.document.getElementById('print_content').innerHTML;
                    if (printHtml && printHtml.length > 0) {
                        printHtmlCopies(printHtml, pageLength, pageWidth, 1);
                    }
                };
            }

        }
        $('#apvid').val('');
        $('#apvid').focus();
    }

    function bindBox() {
        $("#bindBoxModal").modal('show');
    }

    function saveLessInfo() {
        var apvNo = $('#check_scan_datas').find("input[name='apvNo']").val();

        var apvId = $('#check_scan_datas').find("input[name='apvId']").val();

        var quantity = $('#check_scan_datas').find("input[name='totalSaleQuantity']").val();

        var sku = $('#apvid').val();

        var lessBoxNo = $("#bindBoxModal").find("input[name='lessBoxNo']").val();

        var lessQuantity = parseInt($('#check_quantity').text());

        if (!lessBoxNo || lessBoxNo == '' || lessBoxNo.indexOf('PKYCA') == -1){
            layer.alert("请输入正确的周转筐！");
            return false;
        }

        if (!apvNo || apvNo == ''){
            layer.alert("发货单号参数为空！");
            return false;
        }
        if (!quantity || quantity == 0){
            layer.alert("没有应发数量！");
            return false;
        }
        if (!check_quantity || check_quantity == 0){
            layer.alert("sku已经扫描完成！");
            return false;
        }

        var paramData = {
            apvNo: apvNo,
            apvId: apvId,
            boxNo: lessBoxNo,
            sku: sku,
            quantity: quantity,
            lessQuantity: lessQuantity,
            pickTaskNo: taskNo
        };
        $.ajax({
            url : CONTEXT_PATH + "smPacking/exception/binding/stockOut",
            type: "POST",
            contentType: "application/json;charset=utf-8",
            data: JSON.stringify(paramData),
            success: function(response) {
                if(response.status == "200"){
                    layer.alert("绑定成功",{closeBtn: 0},function (index) {
                        layer.close(index);
                        $("#bindBoxModal").modal('hide');
                        $('#check_scan_datas').children().remove();
                        $('#apvid').val('');
                        $('#apvid').focus;
                    });
                }else {
                    layer.alert("绑定失败：" + response.message);
                }
            },
            error:function () {
                layer.alert("系统异常，绑定失败!",'error');
            }
        });
    }

</script>
</body>
</html>