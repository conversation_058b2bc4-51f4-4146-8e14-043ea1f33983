<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">
<html>
<head>
	<title>易世通达仓库管理系统</title>
	<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
	<#include "/common/include.html">
	<style type="text/css">
		.modal-body input {
			width: 240px;
		}
		#Ajaxlog div:first-child {
			color: red;
			font-weight: bold;
		}
	</style>
</head>
<body>
	<@header  method="header" active="12050000" ><#include "/ftl/header.ftl"></@header>
	<div id="page" style="background-color: rgb(231, 237, 248)">
		<div class="row">
			<div class="col-md-12" style="padding: 0">
				<ul class="page-breadcrumb breadcrumb" style="background-color: white;">
					<li><a href="#">发货扫描</a>
					<li class="active">单件合单扫描</li>
				</ul>
			</div>
		</div>
		<#include "/common/pack_bgcolor_selector.html">

		<!-- 内容 -->
		<div class="container-fluid" style="background-color: white;border: none">
		<div class="row">
			<div id="scan-area">
				<div class="w300px panel layout-panel layout-panel-west" style="width: 250px; left: 0px; top: 0px;">
					<div class="panel-tool"></div>
				</div>
				<div class="panel-body layout-body mt10" data-options="region:'west',title:'扫描区'," title="" id="input_scan">
					<h3 style="display: inline-block">扫描区域</h3>
					<select class="input-medium" id = "waybillSize-id" disabled="true">
						<option value="1">100*100</option>
						<option value="2">100*150</option>
					</select>
					
					<label>拣货任务号/周转筐</label>
					<input type="text" class="input-mini" name="scanbox" id="scanbox" onkeypress="if(event.keyCode==13) { inputscanbox(this); return false;}"tabindex="4">
					
					<label style="margin-left: 15px">SKU</label>
					<input class="input-medium" type="text" tabindex="4" onpaste="return false" onkeypress="if(event.keyCode==13) { inputnext(this); return false;}" id="sku" name="sku">
                    <button type="button" class="btn red" onclick="failPrint()">
						<i class="icon-print"></i> 打印失败面单
					</button>
					<button  type="button" class="btn red" onclick="openPrintDialog()">打印机配置</button>
					<span id="panel-title" style="display: inline-block;margin-left: 20px"><h1  style="color:blue;font-size:18px;">成功:核对区（成功：0，错误：0）  </h1></span>
				</div>
			</div>
			
			<div id="check-area" style="padding: 10px">
				<div class="panel-header" style="overflow: hidden;">
					<div style="width:90px;" class="fl panel-title2" id="panel-floor"></div>
					
				</div>
				<div style="height: 700px;" class="col-md-12">
					<div id="print-waybill" class="col-md-3">
						<div class="row">
							<div class="col-md-12">
								<iframe src="javascript:void(0);" frameborder="0" marginheight="0" marginwidth="0"
										frameborder="0" scrolling="auto" id="shippingOrderFrame"
										name="shippingOrderFrame" width="100%" height="500px"></iframe>
							</div>
							<div id="print_tag" style="width:100%;">
							</div>
							<div id="print_gpsr_tag" style="display: none;"></div>
						</div>
					</div>
				  <div id="scan_datas" class="col-md-8"></div>
                  <div id="Ajaxlog" class="col-md-1" style="margin-left:-20px;">
                  	<div class="col-md-12"></div>
                  	<div class="col-md-12"></div>
                  	<div class="col-md-12"></div>
                  	<div class="col-md-12"></div>
                  	<div class="col-md-12"></div>
                  	<div class="col-md-12"></div>
                  	<div class="col-md-12"></div>
                  	<div class="col-md-12"></div>
                  	<div class="col-md-12"></div>
                  	<div class="col-md-12"></div>
                  </div>
				</div>
			</div>
		</div>
	</div>
	
	<#include "/common/footer.html">
	</div>

	<script type="text/javascript" src="${CONTEXT_PATH }js/LodopFuncs.js"></script>
	<script type="text/javascript" src="${CONTEXT_PATH }js/web-storage-cache.js"></script>
	<script type="text/javascript" src="${CONTEXT_PATH }js/jquery.sound.js?v=${.now?datetime}"></script>
	<script type="text/javascript" src="${CONTEXT_PATH }js/pages/pack-style.js?v=${.now?datetime}"></script>
	<script type="text/javascript" src="${CONTEXT_PATH }js/packing.js?v=${.now?datetime}"></script>
	<script type="text/javascript" src="${CONTEXT_PATH}js/print.js?v=${.now?datetime}"></script>
	
	<script type="text/javascript">
		window.onload = function () {
			getPrinterList();
		};
			var printUrl = "${CONTEXT_PATH }apv/packs/oms/mg/print?apvNo=";

			$("#waybillSize-id").val("${domain.waybillSize}");
			
			//记录扫描的apvNo
			var apvNo;
            //记录扫描的apvNo对应的trackingNumber(追踪号)
            var trackingNumber;
			
			//扫描拣货任务号 或者周转筐 得到的拣货任务号
			var scanTaskNo;

			var shipStatus;

			var gpsrPlatform;

			var cacheKey = "scan_success";
			$(document).ready(function(){
				input_init();
				
		  		var storage = new WebStorageCache();
		  		if (storage.get(cacheKey)) {
		  			lastSuc = storage.get(cacheKey);
		  			$('#panel-title').html('<h1  style="color:blue;font-size:48px;">成功：<b>'+lastSuc+'</b></h1>');
		  		}
				initPrinter();
			});
			
			// 初始化
			function input_init(){
				$('#sku').val('');
				$('#sku').focus();
			}
			
			//扫描框
			function inputscanbox(obj){
				
				if(!obj.value || obj.value.replace(/\s/g,'') == ''){
					layer.alert("请输入周转框 或者 拣货任务号!");
					return ;
				}
				
				var sacnBoxNo = obj.value.replace(/\s/g,'');
				
				sacnBoxNo = $.trim(sacnBoxNo);
				
				//校验拣货类型
				var pickType = "1,31";
				
				 var r= $.ajax({
		        	url : CONTEXT_PATH + "apv/packs/ss/box/scan",
					data : {box : sacnBoxNo, pickType : pickType},
		            timeout : 100000,
		            success : function(response){
		            	
		            	if(response.status == '200'){
		            		scanTaskNo = response.message;
		            		$('#sku').select().focus();
		            		
		            	}else{
							layer.alert(response.message, {closeBtn: 0}, function (index) {
								layer.close(index);
								$('#scanbox').val('');
								// 找不到订单
								$('#scanbox').select().focus();
							});

		            	}
		             },
		             error:function(){
						 layer.alert('扫描失败，请重新扫描');
		             }
		        }); 
				
			}
			
		 
			//扫描 sku
			function inputnext(obj) {
				if (!jitPrinter || !jitPrinterTag || !jitPrinter75Tag) {
					layer.alert("请先配置打印机",'error');
					return
				}
				input_submit(obj);
				input_init();
			}
			
			// 统计扫描成功数量
		    function calsf(){
		    	
		  		var storage = new WebStorageCache();
		  		
		  		var lastSuc = 0;
		  		if (storage.get(cacheKey)) {
		  			lastSuc = storage.get(cacheKey);
		  		}
		  		
		  		var suc = 1 + lastSuc;
		  		
		  		storage.set(cacheKey, suc, {exp : 5 * 60 * 60});
		    	
		    	$('#panel-title').html('<h1  style="color:blue;font-size:48px;">成功：<b>'+suc+'</b></h1>');
		    }
			
			//获取打印和已经扫描的数据
			function input_submit(obj) {
				
				var val = $('#sku').val();
				//防止查询大数据
				if(!val || val.trim() == ''){
					layer.alert("请输入有效sku!");
					return ;
				}

				//兼容SKU编码

                //val = getSkuByBarCode(obj);

				var uuid = val.trim();

                if(!(val.indexOf("=") == -1)){
                    var realSku = val.split('=')[0];
                    $('#sku').val(realSku);
                }

                var sku = $('#input_scan').find('input,select').serialize();
                sku = sku +"&uuid=" + uuid ;

                //面单尺寸
                var waybillSize = $("#waybillSize-id").val();
				
				if(!scanTaskNo || scanTaskNo.trim() == ''){
					sku = sku +"&waybillSize=" + waybillSize ;
				}else{
					sku = sku +"&waybillSize=" + waybillSize + "&scanTaskNo=" + scanTaskNo;
				}
				sku=sku+"&isJit="+false;
				var date = new Date();
				//核对提交数据
				var r = $.ajax({
					type : "get",
					url :CONTEXT_PATH +"apv/packs/ss/scan/sku?time=" + date,
					data : sku,
					timeout : 100000,
					async : false,
					beforeSend : function() {
						App.blockUI(null, null, 500);
						
						$("#sku").attr("disabled", true);
					},
					success : function(r) {
						
						$('#scan_datas').html(r);
						
						if($(r).find(".scan_success_sub").length == 1) {
							//统计数量(成功和失败)
					        calsf();
					        
					        apvNo = $(r).find("input[name='apvNo']").val();
					        trackingNumber = $(r).find("input[name='trackingNumber']").val();
					        shipStatus = $(r).find("input[name='shipStatus']").val();
							gpsrPlatform = $(r).find("input[name='gpsrPlatform']").val();
							debugger;
							if (gpsrPlatform){
								audioPlay('gpsr');
							}else{
								audioPlay('success');
							}

                            var options = {
                                year: 'numeric',
                                month: '2-digit',
                                day: '2-digit',
                                hour: '2-digit',
                                minute: '2-digit',
                                second: '2-digit',
                                hour12: false
                            };
					        var content = '<div class="col-md-12">' + apvNo +'<br/>('+trackingNumber+')'
                                +"<br/><span style='margin-left:36px;white-space: nowrap;'>"+date.toLocaleString('zh-CN', options).replace(/\//g, "-");
                                + '<span/></div>';

					        $("#Ajaxlog").prepend(content);
					        $("#Ajaxlog div:last-child").remove();

                            addWhUniqueSkuLog(uuid, apvNo);

                            if ($(r).find("input[name='signRefund']").val()) {
                                //切换了物流类型 打印失败面单并提示
                                failPrint();
								layer.alert("该出库单已更改为快递出库，请将包裹送到对应处理人或组长！");
                            } else {
                                //打印面单
                                printApvNo(apvNo);
                            }
					        
					        input_init();
							
						} else {
							App.unblockUI();
                            addWhUniqueSkuLog(uuid, '');
							audioPlay('error');
							const errorElement = $(r).find("#scan-error");
							const errorMessage = errorElement.length ? errorElement.text().trim() : "未匹配到单据，请重试";
							layer.alert(errorMessage, {
								closeBtn: false,
								skin: 'layui-layer-lan'
							}, function(index) {
								layer.close(index);
								$('#sku').select().focus();
							});
						}
						
						// 防止重复扫
				        setTimeout(removeDisabled, 1000);
					},
					error : function() {
						layer.alert('扫描失败，请重新扫描/' + sku);
					}
				});
			}
			
			function removeDisabled() {
				$("#sku").removeAttr("disabled");
				
				$('#sku').focus();
			}
			
		 	function printApvNo(apvNo){
				var sku = $('#sku').val();

				if (gpsrPlatform){
					printGpsrTag(apvNo,null);
				}
				if (shipStatus && (shipStatus == '22' || shipStatus == '23')) {
					var skuBarcode = $('#scan_datas').find('#skuBarcode').val();
					//打印JIT货品标签
					printJitTag(apvNo, sku, null, skuBarcode, true);

					var r= $.ajax({
						url: CONTEXT_PATH + "fba/packs/localPrintXiangmai?apvNo=" + apvNo,
						timeout : 100000,
						async: false,
						success : function(response){
							if(response.status == '200'){
								var jitPdfUrl = response.body.jitPdfUrl;
								if (!jitPdfUrl) {
									jitPdfUrl = window.location.origin + CONTEXT_PATH + response.message;
									document.getElementById('shippingOrderFrame').src = CONTEXT_PATH + response.message;
								} else {
									document.getElementById('shippingOrderFrame').src = response.message;
								}
								var jitBoxNumber = response.location;
								printCopies(jitPdfUrl,null,jitPrinter,1,jitBoxNumber);
							}else{
								layer.alert(response.message, {closeBtn: 0}, function (index) {
									layer.close(index);
								});
							}
						},
						error:function(){
							layer.alert('扫描失败，请重新扫描');
						}
					});
				} else {
					omsPrint(apvNo, printUrl);
					$('#sku').focus();
					isFocus = true;
					setTimeout(focusSku, 1000);
				}

			}
			
			var isFocus = false;
			$("body").click(function() {isFocus = false;});
		    function focusSku(){
		    	if (isFocus) {
		    		$('#sku').focus();
			    	setTimeout(focusSku, 1000);
				}
		    }

            // 撤销已经打印的订单
            function revoke(apvId, apvNo) {
                if(confirm("确定要撤销已经打印的订单？")) {
                    var r = $.ajax({
                        type : "get",
                        url :CONTEXT_PATH+"apv/packs/revoke" ,
                        data : {"apvId": apvId,"apvNo":apvNo},
                        timeout : 100000,
                        beforeSend : function() {
                        },
                        success : function(r) {

                            $("#scan-apv-" + apvId).remove();
                            // $('#printHtml').attr('src', '');
                            $('#sku').focus();

                            subtractsf();

                            var msg = "修改成功";
							layer.alert(msg);

                        },
                        error : function() {
                        }
                    });
                }
            }

            // 撤销减数量
            function subtractsf(){

                var storage = new WebStorageCache();

                var lastSuc = 0;
                if (storage.get(cacheKey)) {

                    var lastSuc = storage.get(cacheKey);

                    var suc = lastSuc - 1;

                    if(suc < 0) {
                        suc = 0;
                    }

                    storage.set(cacheKey, suc, {exp : 5 * 60 * 60});

                    $('#panel-title').html('<h1  style="color:blue;font-size:48px;">成功：<b>'+suc+'</b></h1>');
                }
            }
			
		 	//打印失败发货单
			function failPrint(){
				if(apvNo){
					document.getElementById('shippingOrderFrame').src = CONTEXT_PATH + "apvs/failPrint?apvNo=" + apvNo;
					document.getElementById("shippingOrderFrame").onload = function() {
						var printHtml = document.getElementById("shippingOrderFrame").contentWindow.document.getElementById('print_content').innerHTML;
						if (printHtml && printHtml.length > 0) {
							var waybillSize = $("#waybillSize-id").val();
							var pageLength = "100mm";
							var pageWidth = "100mm";
							if (waybillSize && (waybillSize.indexOf('15') != -1 || waybillSize == '2')) {
								pageWidth = "150mm";
							}
							printHtmlCopies(printHtml,pageLength,pageWidth, 1);
						}
					};
				}
				$('#sku').focus();
			}

            // 添加唯一码包装日志
            function addWhUniqueSkuLog(uuid, apvNo) {
				let val = $("#waybillSize-id").val();
				if(val == 1){
					val = 2;
				}else {
					val = 3;
				}
				var r = $.ajax({
					type : "get",
					url :CONTEXT_PATH+"apv/packs/addWhUniqueSkuLog" ,
					data : {uuid : uuid, apvNo: apvNo, type: 1, packType: val},
					timeout : 100000,
					beforeSend : function() {
					},
					success : function(responese) {

					},
					error : function() {
					}
				});
				addPackExceptionRecord(uuid, apvNo, val);
            }

			// 新增包装异常记录-未匹配发货单
			function addPackExceptionRecord(uuid, apvNo, packPage){
				if(apvNo != undefined && apvNo != ''){
					return;
				}
				$.ajax({
					type: "POST",
					url: CONTEXT_PATH+"apv/packs/createPackExceptionUuidItem",
					data: {uuid: uuid, taskNo: scanTaskNo, scanPage: packPage, packExceptionType: 2},
					success: function(response) {
					},
					error:function () {
					}
				});
			}
	</script>
</body>
</html>