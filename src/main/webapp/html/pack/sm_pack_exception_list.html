<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">
<html>
<head>
	<title>易世通达仓库管理系统</title>
	<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
	<#include "/common/include.html">
	<style type="text/css">
		.modal-body input {
			width: 240px;
		}
		.top_bar{
			position:fixed;top:0px;
		}
		#task-list td {
			vertical-align:middle;
		}

	</style>
</head>
<body>
<@header method="header" active="12050000" ><#include "/ftl/header.ftl"></@header>
<div id="page" style="background-color: rgb(231, 237, 248)">
	<div class="row">
		<div class="col-md-12" style="padding: 0">
			<ul class="page-breadcrumb breadcrumb" style="background-color: white;">
				<li><a href="#">出库管理</a>
				<li class="active">包装</li>
				<li class="active">多件包装异常查询</li>
			</ul>
		</div>
	</div>

	<!-- 内容 -->
	<div class="container-fluid" style="background-color: white;border: none">
	<div class="row">
		<div class="col-md-12">
		<#assign query = domain.query>
			<form action="${CONTEXT_PATH}smPacking/exception/search"
					   class="form-horizontal form-bordered form-row-stripped"
					   method="post" modelAttribute="domain" name="smPackingExceptionForm" id ="domain">
				<!-- 分页信息 -->
				<input id="page-no" type="hidden" name="page.pageNo" value="1">
				<input id="page-size" type="hidden" name="page.pageSize" value="${domain.page.pageSize}">
				<div class="form-body">
					<div class="form-group">
						<label class="control-label col-md-1">创建时间</label>
						<div class="col-md-2">
							<div class="input-group">
								<input class="form-control Wdate" type="text" name="query.fromCreatedDate" placeholder="" readonly="readonly" value="${query.fromCreatedDate }" onfocus="WdatePicker({startDate:'%y-%M-%d 00:00:00',dateFmt:'yyyy-MM-dd HH:mm:ss',alwaysUseStartDate:true})">
								<span class="input-group-addon">到</span>
								<input class="form-control Wdate" type="text" name="query.toCreatedDate" placeholder="" readonly="readonly" value="${query.toCreatedDate }" onfocus="WdatePicker({startDate:'%y-%M-%d 23:59:59',dateFmt:'yyyy-MM-dd HH:mm:ss',alwaysUseStartDate:true})">
							</div>
						</div>
						<label class="control-label col-md-1">单据当前状态</label>
						<div class="col-md-2">
							<input class="form-control" type="text" name="query.statusStr"  value="${query.statusStr}">
						</div>
						<label class="control-label col-md-1">周转筐</label>
						<div class="col-md-2">
							<input class="form-control" type="text" name="query.boxNo" placeholder="多个查询请以逗号分开" value="${query.boxNo }">
						</div>
						
						<label class="control-label col-md-1">YSTN</label>
						<div class="col-md-2">
							<input class="form-control" type="text" name="query.apvNo" placeholder="多个查询请以逗号分开" value="${query.apvNo }">
						</div>

					</div>
					<div class="form-group">
						<label class="control-label col-md-1">发货单类型</label>
						<div class="col-md-2">
							<input class="form-control" name="query.apvType" type="text" value="${query.apvType}">
						</div>
						<label class="control-label col-md-1">是否生成补货任务</label>
						<div class="col-md-2">
							<select name="query.isTask" class="form-control" value="${query.isTask}">
								<option value="" <#if (query.isTask)??>selected</#if>></option>
								<option value="true" <#if query.isTask == true>selected</#if>>是</option>
								<option value="false" <#if (query.isTask)?? && query.isTask == false>selected</#if>>否</option>
							</select>
						</div>
					</div>
				</div>
				<div>
					<div class="pull-left" style="text-align: left">
						<@header method="auth" authCode="CREATE_SM_PICKING_TASK">
							<button type="button" class="btn  btn-default" onclick="createPickingTask()">
								<i class="icon-pickingTask"></i> 生成拣货任务
							</button>
						</@header>
				    </div>
					<div class="col-md-offset-12" style="text-align: right">
                        <@header method="auth" authCode="MULTIPLY_PICKING_EXCEPTION_DOWNLOAD_DETAIL">
						<button type="button" class="btn btn-default" onclick="downloadPickingTask('item')">
							<i class="icon-download"></i> 导出明细
						</button>
                        </@header>
						<button type="button" class="btn btn-default" onclick="formReset(this)">
							<i class="icon-refresh"></i> 重置
						</button>
						<button type="submit" class="btn blue">
							<i class="icon-search"></i> 查询
						</button>

					</div>
				</div>
			</form>
		</div>
		<br/>
	</div>

	<div class="row">
		<div id="fixedDiv" class="col-md-12">
			<table class="table table-striped table-bordered table-hover table-condensed" id="fixedTab">
				<colgroup>
					<col width="4%">
					<col width="10%">
					<col width="5%">
					<col width="6%">
					<col width="4%">
					<col width="7%">
					<col width="7%">
					<col width="4%">
					<col width="4%">
					<col width="4">
					<col width="4%">
					<col width="4%">
					<col width="8%">
					<col width="8%">
					<col width="10%">
					<col width="5%">
					<col width="5%">
				</colgroup>
				<thead>
					<tr>
						<th><input type="checkbox" id="check-all" name="checkAll">编号</th>
                        <th>YSTN</th>
						<th>发货类型</th>
                        <th>周转筐</th>
						<th>周转筐绑定状态</th>
                        <th>SKU</th>
                        <th>SKU库位</th>
                        <th>待发</th>
                        <th>包装扫描数量</th>
                        <th>缺货数量</th>
						<th>是否生成补货任务</th>
						<th>拣货数量</th>
						<th>原拣货人/原拣货时间</th>
						<th>拣货人/拣货时间</th>
                        <th>绑定人/绑定时间</th>
                        <th>单据当前状态</th>
						<th>日志</th>
					</tr>
				</thead>
			</table>
		</div>
		<div class="col-md-12" id="task-list-warp">
			<table class="table table-striped table-bordered table-hover table-condensed" id="task-list">
				<colgroup>
					<col width="4%">
					<col width="10%">
					<col width="5%">
					<col width="6%">
					<col width="4%">
					<col width="7%">
					<col width="7%">
					<col width="4%">
					<col width="4%">
					<col width="4">
					<col width="4%">
					<col width="4%">
					<col width="8%">
					<col width="8%">
					<col width="10%">
					<col width="5%">
					<col width="5%">
				</colgroup>
				<thead>
					<tr>
						<th><input type="checkbox" id="check-all" name="checkAll">编号</th>
						<th>YSTN</th>
						<th>发货类型</th>
						<th>周转筐</th>
						<th>周转筐绑定状态</th>
						<th>SKU</th>
						<th>SKU库位</th>
						<th>待发</th>
						<th>包装扫描数量</th>
						<th>缺货数量</th>
						<th>是否生成补货任务</th>
						<th>拣货数量</th>
						<th>原拣货人/原拣货时间</th>
						<th>拣货人/拣货时间</th>
						<th>绑定人/绑定时间</th>
						<th>单据当前状态</th>
						<th>日志</th>
					</tr>
				</thead>
				<tbody>
					<#list domain.smPackingExceptions as exception >
						<tr>
							<td><input type="checkbox" class="ids" value="${exception.id}" name="ids"> ${exception.id } </td>
							<td>${exception.apvNo } </td>
							<td>${util('enumName', 'com.estone.apv.common.ApvOrderType', exception.orderType)} </td>
							<td>${exception.boxNo } </td>
							<td>${util('enumName', 'com.estone.apv.enums.BoxStatusEnum', exception.boxStatus)} </td>
							<td>${exception.sku } </td>
							<td>${exception.locationNumber } </td>
							<td>${exception.quantity } </td>
                            <td>${exception.scanQuantity } </td>

							<td>${exception.lessQuantity } </td>

							<#if (exception.isTask??) && (exception.isTask) == true>
								<td>是</td>
							<#else>
								<td>否</td>
						    </#if>
							<td>${exception.pickQuantity }</td>
							<td>${util('name',exception.firstPickBy)} <br/> ${exception.firstPickDate} </td>
							<#if (exception.boxStatus?? && exception.boxStatus!=1)>
								<td>${util('name',exception.pickBy)} <br/> ${exception.pickDate} </td>
							<#else>
								<td></td>
							</#if>
							<td>${util('name',exception.bindUser)}/${exception.bindDate} </td>
							<#if (exception.apvStatus??)>
								<td>${util('enumName', 'com.estone.apv.common.ApvStatus', exception.apvStatus)} </td>
							<#else>
								<td>${util('enumName', 'com.estone.apv.common.ApvStatus', exception.orderStatus)} </td>
							</#if>
							<td> <button type="button" class="btn btn-xs btn-info" onclick="viewLog(${exception.id}, 'smPackingException')">日志</button></td>
						</tr>
					</#list>
				</tbody>
			</table>
		</div>
	</div>
		<div id="fixed-bottom">
			<div id="pager"></div>
		</div>
</div>


<#include "/common/footer.html">
</div>
<script type="text/javascript" src="${CONTEXT_PATH}js/datepicker/WdatePicker.js"></script>
<script type="text/javascript" src="${CONTEXT_PATH}js/table-thead-fixed.js"></script>
<script type="text/javascript">
    // 分页
    var total = "${domain.page.totalCount}";
	var pageNo = "${domain.page.pageNo}";
	var pageSize = "${domain.page.pageSize}";
	$("#pager").initPager({ total : total, pageNo : pageNo, pageSize : pageSize});

    var heights = $("body").height();
    if(heights>910){
        $("#fixed-bottom").addClass("fixed-bottom-height")
    }


	var statusSelectJson = ${domain.statusSelectJson};
	$("input[name='query.statusStr']").select2({
		data : statusSelectJson,
		placeholder : "状态",
		multiple: true,
		allowClear : true
	});

	var apvType = ${domain.apvType};
	$("input[name='query.apvType']").select2({
		data : apvType,
		placeholder : "发货类型",
		multiple: true,
		allowClear : true
	});

	// 全选
	var checkAll = $("input[name='checkAll']");

	// 子选项
	var itemIds = $("input[name='ids']");
	checkAll.change(
			function() {
				itemIds.prop("checked", $(this).prop("checked"));
				itemIds.each(function() {
					var f = $(this).is(":checked");
					var checkClass = $(this).prop("class");
					$("." + checkClass).each(function() {
						$(this).prop("checked", f);
					})
				})
			}
	);

	// 获取选中的任务
	function getCheckedIds() {
		var checkedIds = $("input[name='ids']:checked");
		return checkedIds;
	}
	
	//导出
	// 下载
	function downloadPickingTask(downloadFor){

		var diglog = dialog({
			title: '导出',
			width: 350,
			height:100,
			url: CONTEXT_PATH + "picking/tasks/downloadmode",
			okValue: '确定',
		    ok: function () {

				var exportWindow = $(this.iframeNode.contentWindow.document.body);

				var submitForm = exportWindow.find("#submit-form");

				var exportType = submitForm.find("input[name='exportType']:checked").val();

				var params = $('#domain').serialize();

				// 导出当前选择
				if(exportType == 3) {
					debugger;
					var ids = getCheckedIds();
					if(ids.length == 0) {
						layer.alert("请选择要操作的数据!");
						return false;
					} else if (ids.length > 300) {
						layer.alert("选择数量不能超过300!");
						return false;
					}
					params = params + "&" +ids.serialize();
				}
				params = params + "&exportType=" + exportType
				downloadByPostForm(params, CONTEXT_PATH + "smPacking/exception/download");
				$("#page-no").val("1");

				setTimeout(function () {
					diglog.close().remove();
				}, 100);

				return true;
			},
			cancelValue:'取消',
			cancel: function () {}
		});
		diglog.show();
	}

	// 生成拣货任务
	function createPickingTask() {
		var ids = getCheckedIds();
		if(ids.length == 0) {
			layer.alert("请选择要操作的数据!");
			return false;
		}
		$.get(CONTEXT_PATH + "smPacking/exception/createPickingTask", ids.serialize(), function(data){
			if (data.status == 200) {
				if (data.message==null || data.message==''){
					layer.alert('成功',function (index) {
						layer.close(index);
						location.reload();
					});
				}else{
					customizeLayer(data.message);
				}
			} else {
				customizeLayer(data.message);
			}

		});
	}

</script>
</body>
</html>