<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">
<html>
    <head>
        <title>易世通达仓库管理系统</title>
        <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
        <#include "/common/include.html">
        <link href="${CONTEXT_PATH}css/step.css?v=${.now?datetime}" rel="stylesheet" type="text/css"/>
        <style type="text/css">
            .content-title {
                margin-bottom: 60px;
            }

            .content-title label {
                text-align: right;
                margin-top: 0px !important;
            }

            .readonly-row {
                padding: 20px 20px 0px 20px;
            }

            .write-row {
                background: white;
                padding: 20px 20px 0px 20px;
            }

            .btn-float a {
                margin: 10px 20px;
            }

            .table-striped td img {
                width: 80px;
                height: 80px;
            }

            .table tbody > tr > th, td {
                text-align: center;
                vertical-align: middle !important;
            }

            .page-info {
                background-color: #f2f2f2;
            }

            .content-title-last {
                height: 10px;
            }

            .page-table {
                background-color: white;
                width: 70%;
            }

        </style>
    </head>
    <body>
        <@header method="header" active="11050000"><#include "/ftl/header.ftl">
        </@header>

        <div id="page" style="background-color: rgb(231, 237, 248)">
            <div class="row">
                <div class="col-md-12" style="padding: 0">
                    <ul class="page-breadcrumb breadcrumb" style="background-color: white;">
                        <li><a href="#">移库任务</a></li>
                        <li><a href="#">SKU移库</a></li>
                        <li class="active">移库任务详情</li>
                    </ul>
                </div>
            </div>

            <!-- 内容 -->
            <div class="container-fluid" style="background-color: white;border: none">
                <#assign moveForm = domain.moveForm>
                <div class="write-row">
                    <h4><strong>移库单号${moveForm.moveId}详情</strong></h4>
                </div>

                <div class="row readonly-row">
                    <div class="col-md-12 page-info">
                        <div class="form-body" style="padding-top: 30px">
                            <div class="form-group content-title" style="margin-bottom: 20px">
                                <div class="source">
                                    <div class="el-steps el-steps--horizontal">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row readonly-row">
                    <div class="col-md-12 page-info">
                        <div class="form-body">
                            <div class="row">
                                <h5><strong>基本信息</strong></h5>
                                <label class="control-label col-md-1">移库单号：</label>
                                <div class="col-md-3">
                                    ${moveForm.moveId}
                                </div>
                                <label class="control-label col-md-1">SKU数量：</label>
                                <div class="col-md-3">
                                    ${moveForm.skuNum}
                                </div>
                                <label class="control-label col-md-1">PCS：</label>
                                <div class="col-md-3">
                                    ${moveForm.planNum}
                                </div>
                            </div>
                            <div class="row">
                                <label class="control-label col-md-1">差异PCS：</label>
                                <div class="col-md-3">
                                    ${moveForm.diffNum}
                                </div>
                                <label class="control-label col-md-1">周转框：</label>
                                <div class="col-md-3">
                                    ${moveForm.boxNo}
                                </div>
                                <label class="control-label col-md-1">当前状态：</label>
                                <div class="col-md-3">
                                    ${moveForm.stateCh}
                                </div>

                            </div>

                            <div class="row">
                                <label class="control-label col-md-1">创建人/创建时间：</label>
                                <div class="col-md-3">
                                    <#if moveForm.creationId == '' >
                                    <#else >
                                        ${util('name',moveForm.creationId)}<br/>${moveForm.creationTime}
                                    </#if>
                                </div>
                                <label class="control-label col-md-1">拣货人/完成时间：</label>
                                <div class="col-md-3">
                                    <#if moveForm.pickingId == '' >
                                    <#else >
                                        ${util('name',moveForm.pickingId)}<br/>${moveForm.pickingTime}
                                    </#if>
                                </div>
                                <label class="control-label col-md-1">上架人/完成时间：</label>
                                <div class="col-md-3">
                                    <#if moveForm.putawayId == '' >
                                    <#else >
                                        ${util('name',moveForm.putawayId)}<br/>${moveForm.putawayTime}
                                    </#if>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="write-row">
                </div>
            </div>
            <div class="container-fluid" style="background-color: white;border: none">
                <div class="row readonly-row">
                    <div class="col-md-12 page-info">
                        <div class="form-body">

                            <div class="col-md-12 content-title">
                                <h5><strong>SKU信息</strong></h5>
                                <table class="table table-condensed table-bordered  table-striped page-table">
                                    <tbody>
                                    <tr>
                                        <th>SKU</th>
                                        <th>SKU名称</th>
                                        <th>库位</th>
                                        <th>目标库位</th>
                                        <th>需迁库存</th>
                                        <th>拣货数量</th>
                                        <th>上架数量</th>
                                        <th>差异数量</th>
                                        <th>是否完成上架</th>
                                    </tr>
                                    <#list domain.moveFormInfos as item>
                                        <tr>
                                            <td>${item.sku}</td>
                                            <td>${item.name}</td>
                                            <td>${item.oldLocation}</td>
                                            <td>${item.newLocation}</td>
                                            <td>${item.planNum}</td>
                                            <td>${item.boxNum}</td>
                                            <td>${item.realityNum}</td>
                                            <td>${item.diffNum}</td>
                                            <td>${item.upStatusStr}</td>
                                        </tr>
                                    </#list>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="write-row">
                </div>
            </div>

        </div>
        <div class="write-row">
        </div>
        </div>
        <#include "/common/footer.html">
        </div>

        <script type="text/javascript" src="${CONTEXT_PATH}js/datepicker/WdatePicker.js"></script>
        <script type="text/javascript" src="${CONTEXT_PATH}js/jquery.region.js"></script>
        <script type="text/javascript" src="${CONTEXT_PATH}js/jquery.form.js"></script>
        <script type="text/javascript" src="${CONTEXT_PATH}js/prompt-message.js"></script>
        <script type="text/javascript" src="${CONTEXT_PATH}js/estoneSteps.js?v=${.now?datetime}"></script>
        <script type="text/javascript">

            var orderStatus = ${domain.moveForm.state};
            var step_index = 0;
            // 状态步骤图
            var statusJson = ${domain.statusJson};
            var array = new Array();
            if (orderStatus == '6') {
                var stepContent = {};
                stepContent.title = statusJson[0].text;
                stepContent.description = "";
                array[0] = stepContent;

                var stepContent2 = {};
                stepContent2.title = "废弃";
                stepContent2.description = "";
                array[1] = stepContent2;
                step_index = 2;
            } else {
                for (var i = 0; i < statusJson.length - 2; i++) {
                    if (statusJson[i].id == orderStatus) {
                        step_index = i;
                    }
                    var stepContent = {};
                    stepContent.title = statusJson[i].text;
                    stepContent.description = "";
                    array[i] = stepContent;
                }
            }


            $(".el-steps").step({
                index: step_index,
                title: array
            });

        </script>
    </body>
</html>