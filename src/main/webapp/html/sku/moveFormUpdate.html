<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">
<html>
    <head>
        <title>易世通达仓库管理系统</title>
        <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
        <#include "/common/include.html">
        <style type="text/css">
            .modal-body input {
                width: 240px;
            }
            .top_bar{
                position:fixed;top:0px;
            }
            #task-list td {
                vertical-align:middle;
            }
        </style>
    </head>
    <body>
        <@header method="header" active="11030200"><#include "/ftl/header.ftl"></@header>
    <div id="page" style="background-color: rgb(231, 237, 248)">
        <div class="row">
            <div class="col-md-12" style="padding: 0">
                <ul class="page-breadcrumb breadcrumb" style="background-color: white;">
                    <li><a href="#">移库任务</a></li>
                    <li class="active">SKU移库</li>
                    <li class="active">修改移库任务</li>
                </ul>
            </div>
        </div>
        <!-- 内容 -->
        <div class="container-fluid" style="background-color: white;border: none">
            <#assign moveForm = domain.moveForm>
            <div class="write-row">
                <h4><strong>移库单号${moveForm.moveId}详情</strong></h4>
            </div>
            <!-- END PAGE HEADER-->
            <div class="row">
                <div class="col-md-9">
                    <form action="${CONTEXT_PATH}skuMoveForm/update"
                          class="form-horizontal form-bordered form-row-stripped"
                          method="post" modelAttribute="domain" name="skuMoveForm" id="domain">
                        <div class="form-body">

                            <div class="form-group" style="margin-top: 50px;">
                                <div class="col-md-5">库存移库SKU</div>
                            </div>
                            <div class="form-group">
                                <div class="col-md-5">
                                    <div id="input_sku">
                                        <input style="height:35px;border-radius: 8px !important;" onkeypress="if(event.keyCode==13) { getSkuStock(); return false;}"
                                               class="form-control" placeholder="请输入SKU" id="sku" name="sku" type="text">
                                    </div>
                                </div>
                                <div class="col-md-2">
                                    <div style="border-radius: 8px !important;width: 100%;" type="button" class="btn blue" onclick="getSkuStock();">
                                        <i class="icon-plus"></i> 添加SKU
                                    </div>
                                </div>
                            </div>

                            <div class="form-group" style="margin-top: 20px;">
                                <div class="col-md-7">
                                    <table class="table table-striped table-bordered table-hover table-condensed" id="task-list">

                                        <thead>
                                        <tr>
                                            <th>序号</th>
                                            <th>SKU</th>
                                            <th>SKU名称</th>
                                            <th>当前库位</th>
                                            <th>迁移库存</th>
                                            <th>目标库位</th>
                                            <th>操作</th>
                                        </tr>
                                        </thead>
                                        <tbody id="tbody">
                                        <span  style='display: none;' id='data-moveFormId'>${moveForm.id}</span>
                                        <#list domain.moveFormInfos as entity>
                                        <tr>
                                            <td id='data-index'>${entity_index + 1}</td>
                                            <td id='data-sku'>${entity.sku}</td>
                                            <td id='data-name'>${entity.name}</td>
                                            <td id='data-oldLocation'>${entity.oldLocation}</td>
                                            <td id='data-quantity'>${entity.planNum}</td>
                                            <td><input type='text' id='data-newLocation' style='width:160px;' value = '${entity.newLocation}'></td>
                                            <td onclick='onRemove(this)'><i class='icon-remove'></i></td>


                                        </#list>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </form>
                    <div class="col-md-9" style="margin-bottom: 20px;">
                        <button class="btn blue" id="confirmMove">
                            <i class="icon-search"></i> 确认
                        </button>
                    </div>
                </div>
                <br/>
            </div>
        </div>

        <div id="fixed-bottom">
            <div id="pager"></div>
        </div>
        <#include "/common/footer.html">
    </div>
    <script type="text/javascript" src="${CONTEXT_PATH}js/datepicker/WdatePicker.js"></script>
    <script type="text/javascript">


        function getSkuStock() {
            var sku = $('#sku').val();
            if(sku == ''){
                layer.alert('sku不能为空！','error');
                return;
            }
            $.ajax({
                url:CONTEXT_PATH+"location/moves/skuDetailMove",
                type:"POST",
                data:{
                    sku:sku.trim(),
                    moveType: "1"
                },
                success:function(response){
                    if (response.status == "200" && response.body) {
                        var sku_array = new Array();
                        $("[id=data-sku]").each(function () {
                            sku_array.push($(this).text());
                        });
                        var i = sku_array.length;
                        if (i > 49) {
                            layer.alert("最多只能添加50条！", "error")
                            return false;
                        }

                        response.body.moveList.forEach(function (moveInfo){
                            var number = $.inArray(moveInfo.sku, sku_array);
                            if (number == -1) {
                                var option = "<tr><td id='data-index'>" + parseInt(i + 1) + "</td>" +
                                    "<td id='data-sku'>" + moveInfo.sku + "</td>" +
                                    "<td id='data-name'>" + moveInfo.name + "</td>" +
                                    "<td id='data-oldLocation' onchange='changeLocation(this)'><input name='"+moveInfo.sku+"_locationSku' type='text'/></td>" +
                                    "<td id='data-quantity'></td>" +
                                    "<td><input type='text' id='data-newLocation' style='width:160px;'></td>" +
                                    "<td onclick='onRemove(this)'><i class='icon-remove'></i></td></tr>";
                                $("#tbody").append(option);
                                $("input[name='"+moveInfo.sku+"_locationSku']").select2({
                                    data: moveInfo.locationSelectJson,
                                    placeholder: "请选择库位",
                                    multiple: false,
                                    allowClear: false
                                });
                                $("input[name='"+moveInfo.sku+"_locationSku']").select2("val", moveInfo.locationSelectJson[0].id);
                                $("input[name='"+moveInfo.sku+"_locationSku']").parent().next().text(moveInfo.locationSelectJson[0].id);
                            }
                        })
                        $('#sku').val("");
                        if(response.message != undefined && response.message != ''){
                            customizeLayer(response.message, 'error');
                        }
                    } else {
                        customizeLayer("操作失败：" + response.message, 'error');
                    }
                }
            });
        }

        function onRemove(obj){
            if(confirm("是否删除该条SKU?")){
                $(obj).parent().remove();
                // 重新编排
                $("#tbody").find('tr').each(function (indexValue) {
                    indexValue++;
                    $(this).find('td[id="data-index"]').text(indexValue);
                });
            }
        }

        $('#confirmMove').on("click",function () {
            var domain = {};
            var moveFormInfos = [];
            var id = $('#data-moveFormId').text();
            $('#tbody').find('tr').each(function (index) {
                var moveFormInfo = {};
                var sku = $(this).find('td[id="data-sku"]').text();
                var name = $(this).find('td[id="data-name"]').text();
                var oldLocation = $(this).find('td[id="data-oldLocation"]').text();
                var planNum = $(this).find('td[id="data-quantity"]').text();
                var newLocation = $(this).find('input[id="data-newLocation"]')[0].value;

                moveFormInfo.sku = sku;
                moveFormInfo.name = name;
                moveFormInfo.oldLocation = oldLocation;
                moveFormInfo.planNum = planNum;
                moveFormInfo.newLocation = newLocation;
                moveFormInfos.push(moveFormInfo);
            });
            domain.moveFormInfos = moveFormInfos;

            var param = {
                moveFormInfos:JSON.stringify(moveFormInfos),
                id:id
            };
            $.post(CONTEXT_PATH + "skuMoveForm/update", param, function (data) {
                if (data.status == 200) {
                    layer.alert("成功！");
                    setTimeout(function () {
                        location.href = CONTEXT_PATH + "skuMoveForm";
                    }, 1000);
                } else {
                    customizeLayer(data.message, "error");
                    // window.location.reload();
                }
            });
        });
    </script>
    </body>
</html>