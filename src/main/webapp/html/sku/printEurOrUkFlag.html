<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">
<html>
<head>
	<meta content="text/html; charset=utf-8" http-equiv="Content-type">
	<title>Print</title>
	<style>
		@media print {
			.printbtn {
				display: none;
			}
		}
	</style>
</head>

<body style="padding: 0px; margin: 20px">

	<div class="printbtn">
		<button onclick="myPreview();">打印预览</button>
		&nbsp;
		<button onclick="myPagePrint2(${domain.printList ? size});">打印</button>
		<input type="hidden" id="print-size" value="${domain.printList ? size}">
		&nbsp;
		<button onclick="myPrintDesign();">打印设计</button>
		&nbsp;
	</div>
	<h5>
		选择打印机&nbsp;&nbsp;
		<select id="printer"></select>
	</h5>

<form id="print_content">
	<#if (domain.printList)?? && (domain.printList)?size gt 0>
		<div id="print-item-0">
			<#list domain.printList as printNo>
			<!-- 32*18mm -->
			<div style="width:31.69mm; height:17.6mm;font-weight: 300; text-align: center; float: left; margin-top: 0mm;">
				<div style="width: 10mm;height: 16mm;margin-top: 1mm; margin-left: 1mm; float: left;">
					<div style="font-weight: 600;height: 7mm; border: 1pt solid #000000;float: left;width: 10mm;line-height: 7mm;">EC</div>
					<div style="font-weight: 600;height: 7mm;border: 1pt solid #000000;float: left;width: 10mm; border-top: none;line-height: 7mm;">REP</div>
				</div>
				<div style="width: 16mm;height: 16mm;margin-top: 1.5mm;float: left;margin-left: 2mm;margin-right: 1mm">
					<!-- MAC90178-BK 10px会越界-->
                    <div style="font-weight: 500;font-size: 5pt; text-align: left;">Apex CE Specialists GmbH Tel : +353212066339</div>
                    <div style="font-weight: 500;font-size: 5pt;text-align: left;">Nordrhein-Westfalen Habichtweg 1 Neuss</div>
                    <div style="font-weight: 500;font-size: 5pt; text-align: left;">Germany,41468 <EMAIL></div>
				</div>
			</div>
			<!-- 单排 -->
			<p style="page-break-after: always; margin: 0; padding: 0; line-height: 1px; font-size: 1px;clear: both;">&nbsp;</p>
		</div><div id="print-item-${printNo_index }">
			</#list>
		</div>
	</#if>

	</div>
</form>

<object width="0" height="0" classid="clsid:2105C259-1E0C-4534-8141-A753534CB4CA" id="LODOP_OB">
	<embed width="0" height="0" type="application/x-print-lodop" id="LODOP_EM">
</object>

<!-- 打印插件 -->
<script type="text/javascript" src="${CONTEXT_PATH}js/LodopFuncs.js"></script>
<script src="${CONTEXT_PATH }js/assets/plugins/jquery-1.10.2.min.js" type="text/javascript" ></script>
<script src="${CONTEXT_PATH }js/print.js" type="text/javascript" ></script>
<script language="javascript">
	// 分页打印
	function myPagePrint2(size) {
		var pageSize = 20;
		var length = Math.ceil(size/(pageSize));
		for (var i = 1; i <= length; i++) {
			var start = (i-1)*pageSize + 1;
			var end = i*pageSize;
			myPagePrint3(start, end);
		}
	};

	// 分页打印
	function myPagePrint3(start, end) {

		LODOP = getLodop(document.getElementById('LODOP_OB'), document
				.getElementById('LODOP_EM'));
		LODOP.PRINT_INIT("打印");

		var innerHtml = "";

		$("[id^='print-item-']").each(function(i, obj) {

			var index = i + 1;
			if($.trim(end) == "") {
				if(parseInt(start) == index) {
					innerHtml = $(obj).html();
					return false;
				}
			} else {
				if(index >= parseInt(start) && index <= parseInt(end)) {
					innerHtml = innerHtml + $(obj).html();
				}
			}

		});
		LODOP.ADD_PRINT_HTM(0, 0, "32mm", "18mm", innerHtml);// 单排
		// LODOP.ADD_PRINT_HTM(0, 0, "102mm", "12mm", innerHtml);// 三排
		LODOP.PRINT();
	};
</script>
</body>
</html>