<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">
<html>
<head>
<title>易世通达仓库管理系统</title>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
	<#include "/common/include.html">
	<style type="text/css">
.modal-body input {
	width: 240px;
}
.top_bar{
    position:fixed;top:0px;
}
#task-list td {
	vertical-align:middle;
}
</style>
</head>
<body>
	<@header method="header" active="11050000"><#include "/ftl/header.ftl"></@header>
	<div id="page" style="background-color: rgb(231, 237, 248)">
		<div class="row">
			<div class="col-md-12" style="padding: 0">
				<ul class="page-breadcrumb breadcrumb" style="background-color: white;">
					<li><a href="#">资产管理</a></li>
					<li class="active">移库信息</li>
					<li class="active">单个移库</li>
				</ul>
			</div>
		</div>
		<!-- 内容 -->
		<div class="container-fluid" style="background-color: white;border: none">
			<#assign whReturn=domain.locationMoveInfo>
		<!-- END PAGE HEADER-->
		<div class="row">
			<div class="col-md-9">
				<form action="${CONTEXT_PATH}location/moves/saveMoveLocation"
					class="form-horizontal form-bordered form-row-stripped"
					method="post" modelAttribute="domain" name="checkInForm" id="domain">
					<div class="form-body">
<#--						<div class="form-group">-->
<#--							<label class="control-label col-md-1" ></label>-->
<#--							<div class="col-md-3"></div>-->
<#--							<label class="control-label col-md-1" >移库类型</label>-->
<#--							<div class="col-md-5">-->
<#--								<input type="hidden" id="checkedType" value="1"/>-->
<#--								<label class="radio-inline" title="本仓移库">-->
<#--									<input class="input-xsmall" autocomplete="off" name="moveType" value="1" checked="checked" type="radio">本仓移库-->
<#--								</label>-->
<#--								<label class="radio-inline" title="中转仓移库">-->
<#--									<input class="input-xsmall" autocomplete="off" name="moveType" value="3" type="radio">中转仓移库-->
<#--								</label>-->
<#--							</div>-->

<#--						</div>-->
						<div class="scan_container">
							<div class="form-group">
								<label class="control-label col-md-1" ></label>
								<div class="col-md-3"></div>
								<label class="control-label col-md-1" >货号</label>
								<div class="col-md-5">
									<div id="input_sku">
										<input class="form-control"  id="sku" name="sku" type="text" onblur="if(isScan) {return;} $(this).val('');layer.alert('请扫描SKU！', 'error');" onkeypress="if(event.keyCode==13) { getSkuDetail(this); return false;}">
									</div>
								</div>
							</div>
							<div class="form-group">
								<label class="control-label col-md-1" ></label>
								<div class="col-md-3"></div>
								<label class="control-label col-md-1" >名称</label>
								<div class="col-md-5">
									<div><input type="text" id="skuName" name="skuName" class="form-control" value="" readonly /></div>
								</div>
							</div>
							<div class="form-group">
								<label class="control-label col-md-1" ></label>
								<div class="col-md-3"></div>
								<label class="control-label col-md-1" >原库位</label>
								<div class="col-md-5">
									<input class="form-control" id="oldLocation" onchange='changeLocation(this)' name="oldLocation" type="text" value="">
								</div>
							</div>
							<div class="form-group">
								<label class="control-label col-md-1" ></label>
								<div class="col-md-3"></div>
								<label class="control-label col-md-1" >库存</label>
								<div class="col-md-5">
									<div><input type="text" id="skuQuantity" name="skuQuantity" class="form-control" value="" readonly/></div>
								</div>
							</div>
							<div class="form-group">
								<label class="control-label col-md-1" ></label>
								<div class="col-md-3"></div>
								<label class="control-label col-md-1" >新库位</label>
								<div class="col-md-5">
									<input class="form-control" type="text" id="newLocation" name="newLocation" onkeypress="if(event.keyCode==13) { inputnext(this); return false;}">
								</div>
							</div>
						</div>

						<div class="form-group">
							<label class="control-label col-md-1" ></label>
							<div class="col-md-3"></div>
							<label class="control-label col-md-1" ></label>
							<div class="col-md-9" style="float: right">
								<table class="table table-striped table-bordered table-hover table-condensed" id="task-list">
									<thead>
										<tr>
											<th>序号</th>
											<th>SKU</th>
											<th>名称</th>
											<th>原库位</th>
											<th>新库位</th>
											<th>数量</th>
											<th>移除</th>
										</tr>
									</thead>
									<tbody id="tbody">
									</tbody>
								</table>
							</div>
						</div>
					</div>
				</form>
				<div class="col-md-offset-1" style="text-align: center;margin-bottom: 20px;">
					<button class="btn blue" id="confirmMove">
						<i class="icon-search"></i> 确认移库
					</button>
				</div>
			</div>
			<br/>
		</div>
	</div>
	
		<div id="fixed-bottom">
			<div id="pager"></div>
		</div>
	<#include "/common/footer.html">
	</div>
	<script type="text/javascript" src="${CONTEXT_PATH}js/datepicker/WdatePicker.js"></script>
	<script type="text/javascript">
		$('#sku').focus();
		var isScan = false;
		
		function getSkuDetail(obj) {
			var val = $('#sku').val();
			if(!val || val.trim() == ''){
				layer.alert("请输入有效sku!");
				return ;
			}
            if(!(val.indexOf("=") == -1)){
                var realSku = val.split('=')[0];
                $('#sku').val(realSku);
                val = realSku;
            }
            $.ajax({
                url:CONTEXT_PATH+"location/moves/skuDetailMoveSingle",
                type:"POST",
                data:{
                    sku:val.trim(),
					// moveType: $('#checkedType').val()
                },
                success:function(result){
					if (result.status == "200" && result.body) {
						let moveInfo = result.body.moveInfo;
						$('#sku').val(moveInfo.sku);
						$('#skuName').val(moveInfo.name);
						$('#oldLocation').val(moveInfo.oldLocation);
						$("#oldLocation").select2({
							data: moveInfo.locationSelectJson,
							placeholder: "请选择库位",
							multiple: false,
							allowClear: false
						});
						$("#oldLocation").select2("val", moveInfo.locationSelectJson[0].id);
						$("#skuQuantity").val(moveInfo.locationSelectJson[0].id);
						$('#quantity').val(moveInfo.locationSelectJson[0].id);
						isScan = true;

					}else {
						customizeLayer("操作失败：" + result.message, 'error');
						$('#sku').val("");
					}

                }
            });
		}

		// 切换库位更新迁移库存
		function changeLocation(locationTd){
			let quantity = $(locationTd).val();
			$("#skuQuantity").val(quantity);
		}

        function inputnext(obj) {
			var checkedType = 1;
		    var newLocation = $('#newLocation').val();
		    var oldLocation = $("#oldLocation").parent().find(".select2-chosen:eq(0)").text();
		    if(newLocation == oldLocation){
                layer.alert("新库位不能与老库位相同，请重新输入！","error");
                $('#newLocation').val("");
                return ;
			}
            var checkLocation = new RegExp("^[A-Za-z0-9]{3,4}(-[A-Za-z0-9]{2}){3}$");
			if (!checkLocation.test(newLocation)) {
				layer.alert("新库位不符合库位规则！", "error");
                $('#newLocation').val("");
				return false;
			}
			//当选择中转仓移库时，原库位及库存取中转仓数据，新库位必须为中转仓库位
			var flag = true;
			if (checkedType == 3){
            $.ajax({
                url:CONTEXT_PATH+"location/moves/checkNewLocation",
                type:"POST",
                async: false,//使用同步的方式,true为异步方式
                data:{
                    newLocation: newLocation.trim(),
                    moveType: checkedType
                },
                success:function(result){
                    if (result.status != 200) {
                        flag = false;
                        customizeLayer(result.message, "error");
                        $('#newLocation').val("");
                    }
                }
            });
			}

			if (flag){
				var sku_array=new Array();
				$("[id=data-sku]").each(function(){
					sku_array.push($(this).text());
				});
				// 检查是否还有输入框未输入值
				var visible_inputs = $('#input_scan').find("input:visible").length;
				for (i = 0; i < visible_inputs; i++) {
					var e = $('#input_scan').find("input:visible").get(i);
					if ($(e).val() == '') {
						$(e).focus();
						return false;
					}
				}
				var i=sku_array.length;
				if(i > 19){
					layer.alert("最多只能添加20条！","error")
					return false;
				}
				var number=$.inArray($('#sku').val(), sku_array);
				if(number==-1){
					var option="<tr><td id='data-index'>"+parseInt(i+1)+"</td>" +
						"<td id='data-sku'>"+$('#sku').val()+"</td>"+
						"<td id='data-skuName'>"+$('#skuName').val()+"</td>"+
						"<td id='data-oldLocation'>"+oldLocation+"</td>"+
						"<td id='data-newLocation'>"+$('#newLocation').val()+"</td>" +
						"<td id='data-quantity'>"+$('#skuQuantity').val()+"</td>"+
						"<td onclick='onRemove(this)'><i class='icon-remove'></i></td></tr>";
					$("#tbody").append(option);
				}
				$("#oldLocation").select2({data:[]});
				$('.scan_container').find('input').val("");
				$('#sku').focus();
			}
        }

        function onRemove(obj){
            if(confirm("是否删除该条SKU?")){
                $(obj).parent().remove();
                // 重新编排
                $("#tbody").find('tr').each(function (indexValue) {
					indexValue++;
					$(this).find('td[id="data-index"]').text(indexValue);
                });
            }
        }

		function isMinus(obj){
			var r = /^\d+$/;
			if(!r.test($(obj).val())){
				$(obj).val("1");
				layer.alert("请填写正整数！", "error");
			}
			if($(obj).val() == 0){
				$(obj).val("1");
				layer.alert("不能填写0-不需要请删除！", "error");
			}
		}

		$('#confirmMove').on("click",function () {
		    var domain = {};
			var locationMoveInfos = [];
			var skus = '';
            $('#tbody').find('tr').each(function (index) {
                var locationMoveInfo = {};
                var sku = $(this).find('td[id="data-sku"]').text();
                var name = $(this).find('td[id="data-skuName"]').text();
                var oldLocation = $(this).find('td[id="data-oldLocation"]').text();
                var newLocation = $(this).find('td[id="data-newLocation"]').text();
                var quantity = $(this).find('td[id="data-quantity"]').text();
                skus += "," + sku;
                locationMoveInfo.sku = sku;
                locationMoveInfo.name = name;
                locationMoveInfo.oldLocation = oldLocation;
                locationMoveInfo.newLocation = newLocation;
                locationMoveInfo.quantity = quantity;
                locationMoveInfo.operationWay = 1;
                locationMoveInfos.push(locationMoveInfo);
			});
            /* if(locationMoveInfos == ''){
                layer.alert("移库数据为空，请确认后再移库！","error");
                return false;
			} */
            domain.locationMoveInfos = locationMoveInfos;

            var param = {
                locationMoveInfos:JSON.stringify(locationMoveInfos),
                skus:skus
            };

            $.post(CONTEXT_PATH + "location/moves/saveMoveLocation", param, function(data) {
                if (data.status == 200) {
                    if(confirm(data.message)){
                        location.href = CONTEXT_PATH + "location/moves";
					}
                } else {
                    customizeLayer(data.message, "error");
                    // window.location.reload();
                }
            });
        });

		$('input[type=radio][name=moveType]').change(function () {
			var oldValue = $('#checkedType').val();
			var newValue= $('input[type=radio][name=moveType]:checked').val();
			if (newValue != oldValue){
				if(confirm("更换移库类型将会清空列表中的数据，请确认是否更换！")){
					$('#checkedType').val(newValue);
					$("#tbody").find('tr').remove();
					$('.scan_container').find('input').val("");
					$('#sku').focus();
				}
			}
		});
	</script>
</body>
</html>