<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">
<html>
    <head>
        <meta content="text/html; charset=utf-8" http-equiv="Content-type">
        <link href="${CONTEXT_PATH}js/assets/plugins/jquery-file-upload/css/jquery.fileupload-ui.css" rel="stylesheet" type="text/css"/>
        <#include "/common/include.html">
        <title>Print</title>
        <style>
            @media print {
                .printbtn {
                    display: none;
                }
            }
            .print-content-size{
                width: 100mm;
            <#if domain.ceTagDo?? && domain.ceTagDo.whetherToys>
                height: 100mm;
            <#else>
                height: 50mm;
            </#if>
            }
        </style>
    </head>

    <body style="padding: 0px; margin: 20px">
        <#if domain.ceTagDo?? && domain.ceTagDo.qualificationAttr??>
            <div class="printbtn">
                <button onclick="myPreview();">打印预览</button> &nbsp;
                <button onclick="myPrint();">打印</button> &nbsp;
                <button onclick="myPrintDesign();">打印设计</button>
            </div>
            <form id="print_content" class="print-content-size">
                <#if domain.ceTagDo.whetherToys>
                    <!-- 100*18mm -->
                    <div id="print-item-2">
                        <div style="width:99.69mm; height:17.6mm;font-weight: 300; text-align: center; float: left; margin-top: 0mm;">
                            <div style="width: 14.40mm;height: 16mm;margin-top: 1mm; margin-left: 3mm; float: left;">
                                <div style="font-weight: 600;height: 7mm; border: 1pt solid #000000;float: left;width: 14.40mm;line-height: 7mm;">
                                    EC
                                </div>
                                <div style="font-weight: 600;height: 7mm;border: 1pt solid #000000;float: left;width: 14.40mm; border-top: none;line-height: 7mm;">
                                    REP
                                </div>
                            </div>
                            <div style="width: 81.29mm;height: 16mm;margin-top: 1.5mm;float: left;margin-left: 1mm;">
                                <!-- MAC90178-BK 10px会越界-->
                                <div style="font-weight: 500; text-align: left; font-size:11pt;">Apex CE Specialists GmbH Tel : +353212066339</div>
                                <div style="font-weight: 500; text-align: left; font-size:11pt;">Nordrhein-Westfalen Habichtweg 1 Neuss</div>
                                <div style="font-weight: 500; text-align: left; font-size:11pt;">Germany,41468 <EMAIL></div>
                            </div>
                        </div>
                    </div>
                    <br/>
                    <!-- 100*30mm -->
                    <div id="print-item-1">
                        <div style="border: 1pt solid #000000; width:75.69mm; height:29.6mm;margin-left: 3mm; float: left; margin-top: 0mm;">
                            <div style="align-content: center;width:15mm;float: left;">
                                <br/>
                                <strong style="font-size: 15mm;">⚠</strong>
                            </div>
                            <div style="align-content: center;width:59.69mm;float: right;">
                                <br/>
                                <strong style="font-size: 5mm">WARNING:</strong><br/>
                                <span style="font-weight: 600;">CHONING HAZARD</span><br/>
                                Small parts.Not for children under 3 years.
                            </div>
                        </div>
                        <div>
                            <img src="${CONTEXT_PATH }/images/print/ce_picture.jpeg"
                                 style="width:19mm; height:29.6mm; float: right; margin-top: 0mm;"/>
                        </div>
                    </div>
                    <br/>
                    <!-- 100*52mm -->
                    <div id="print-item-0" style="width:99.69mm; height:51.6mm;font-weight: 300; text-align: left; float: left; margin-top: 0mm;">
                        <div id="inner">
                            <ul style="list-style:none;padding-left: 3mm; word-break: break-word;">
                                <li style="max-width: 85mm; font-size: 12px"><strong>Product Name:${domain.ceTagDo.qualificationAttr.productName}</strong></li>
                                <li style="max-width: 85mm;">Type:${domain.ceTagDo.qualificationAttr.type}</li>
                                <li>Manufacturer:${domain.ceTagDo.qualificationAttr.manufacturer}</li>
                                <li>Address:${domain.ceTagDo.qualificationAttr.address}</li>
                                <#assign qualificationAttrBeans = domain.ceTagDo.qualificationAttr.qualificationAttrBeans>
                                <#if qualificationAttrBeans??>
                                    <#list qualificationAttrBeans as bean>
                                        <li>${bean.k}:${bean.v}</li>
                                    </#list>
                                </#if>
                            </ul>
                        </div>
                    </div>
                <#else>
                    <!-- 100*32mm -->
                    <div id="print-item-0" style="width:99.69mm; height: 30.6mm; text-align: left; float: left; margin-top: -1mm;position: relative">
                        <div id="inner">
                            <ul style="list-style:none;padding-left: 3mm; word-break: break-word;">
                                <li style="max-width: 85mm; font-size: 12px"><strong>Product Name:${domain.ceTagDo.qualificationAttr.productName}</strong></li>
                                <li style="max-width: 85mm;">Type:${domain.ceTagDo.qualificationAttr.type}</li>
                                <li>Manufacturer:${domain.ceTagDo.qualificationAttr.manufacturer}</li>
                                <li>Address:${domain.ceTagDo.qualificationAttr.address}</li>
                                <#assign qualificationAttrBeans = domain.ceTagDo.qualificationAttr.qualificationAttrBeans>
                                <#if qualificationAttrBeans??>
                                    <#list qualificationAttrBeans as bean>
                                        <li>${bean.k}:${bean.v}</li>
                                    </#list>
                                </#if>
                            </ul>
                        </div>
                        <div style="position: absolute; right: 0; top: 0; width: 15mm; height: 16.6mm">
                            <img src="${CONTEXT_PATH }/images/print/ce_picture.jpeg" style="width: 15mm; height: 16.6mm" />
                        </div>
                    </div>
                    <br/>
                    <!-- 100*18mm -->
                    <div id="print-item-1">
                        <div style="width:99.69mm; height:16.6mm;font-weight: 300; text-align: center; float: left; margin-top: 0mm;">
                            <div style="width: 14.40mm;height: 16mm;margin-top: 1mm; margin-left: 3mm; float: left;">
                                <div style="font-weight: 600;height: 7mm; border: 1pt solid #000000;float: left;width: 14.40mm;line-height: 7mm;">
                                    EC
                                </div>
                                <div style="font-weight: 600;height: 7mm;border: 1pt solid #000000;float: left;width: 14.40mm; border-top: none;line-height: 7mm;">
                                    REP
                                </div>
                            </div>
                            <div style="width: 81.29mm;height: 16mm;margin-top: 1.5mm;float: left;margin-left: 1mm;">
                                <!-- MAC90178-BK 10px会越界-->
                                <div style="font-weight: 500; text-align: left; font-size:11pt;">Apex CE Specialists GmbH Tel : +353212066339</div>
                                <div style="font-weight: 500; text-align: left; font-size:11pt;">Nordrhein-Westfalen Habichtweg 1 Neuss</div>
                                <div style="font-weight: 500; text-align: left; font-size:11pt;">Germany,41468 <EMAIL></div>
                            </div>
                        </div>
                    </div>
                </#if>
                <!-- 单排 -->
                <p style="page-break-after: always; margin: 0; padding: 0; line-height: 1px; font-size: 1px;clear: both;">
                    &nbsp;
                </p>
            </form>
        <#else>
            <h1 style="color:red;font-size:48px;">${domain.tips!"无可打印内容！"}</h1>
        </#if>
        <object width="0" height="0" classid="clsid:2105C259-1E0C-4534-8141-A753534CB4CA" id="LODOP_OB">
            <embed width="0" height="0" type="application/x-print-lodop" id="LODOP_EM">
        </object>

        <!-- 打印插件 -->
        <script type="text/javascript" src="${CONTEXT_PATH}js/LodopFuncs.js"></script>
        <script src="${CONTEXT_PATH }js/assets/plugins/jquery-1.10.2.min.js" type="text/javascript"></script>
        <script language="javascript">
            initCEZoom();
            //自适应CE标签打印内容
            function initCEZoom(){
                var wrapperSty = $("#print-item-0")[0].getBoundingClientRect();
                var innerSty = $("#inner")[0].getBoundingClientRect();
                const scale = Math.min(wrapperSty.width /innerSty.width, wrapperSty.height /innerSty.height, 1) - 0.1;
                inner.style.zoom = scale;
            }

            var LODOP; //声明为全局变量
            function CheckIsInstall() {
                try {
                    var LODOP = getLodop(document.getElementById('LODOP_OB'),
                        document.getElementById('LODOP_EM'));
                    if ((LODOP != null) && (typeof (LODOP.VERSION) != "undefined"))
                        return LODOP.VERSION;
                } catch (err) {
                    //alert("Error:本机未安装或需要升级!");
                }
                return false;
            }

            function myPrint() {
                CreatePrintPage();
                LODOP.PRINT();
            };

            function myPreview() {
                CreatePrintPage();
                LODOP.PREVIEW();
            };

            function myPrintDesign() {
                CreatePrintPage();
                LODOP.PRINT_DESIGN();
            };

            function CreatePrintPage() {
                LODOP = getLodop(document.getElementById('LODOP_OB'), document
                    .getElementById('LODOP_EM'));
                LODOP.PRINT_INIT("打印");

                try {
                    if (typeof (eval(CreatePrintPageWithImage)) == 'function') {
                        return CreatePrintPageWithImage();
                    }
                } catch (e) {
                }
                var width = "50mm";
                <#if domain.ceTagDo?? && domain.ceTagDo.whetherToys>
                    width = "100mm";
                </#if>
                LODOP.ADD_PRINT_HTM(0, 0, "100mm", width, document.getElementById('print_content').innerHTML);
                LODOP.SET_PRINT_PAGESIZE(0,"100mm", width, 'Note');
            };

        </script>
    </body>
</html>