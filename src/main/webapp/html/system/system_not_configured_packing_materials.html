<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">
<html>
<head>
<title>易世通达仓库管理系统</title>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
	<#include "/common/include.html">
	<style type="text/css">
.modal-body input {
	width: 240px;
}

#item-list tbody {
	display:block;
	height:550px;
	overflow-y:scroll;
	text-align: center;
}

#item-list thead, tbody tr {
	display: table;
	width: 1200px;
	table-layout: fixed;
}

#item-list  td {
	text-align: center;
}


#task-list td {
	vertical-align:middle;
}
</style>
</head>
<body>
<@header method="header" active="14010000"><#include "/ftl/header.ftl"></@header>

	<div id="page" style="background-color: rgb(231, 237, 248)">
		<div class="row">
			<div class="col-md-12" style="padding: 0">
				<ul class="page-breadcrumb breadcrumb" style="background-color: white;">
					<li><a href="#">系统配置</a></li>
					<li class="active">未配置规则包材</li>
				</ul>
			</div>
		</div>
		<!-- 内容 -->
		<div class="container-fluid" style="background-color: white;border: none">
		<!-- END PAGE HEADER-->
		<div class="row" style="padding-top: 40px;height: 760px">
			<div class="col-md-12">
				<form id="submit-form" action="${CONTEXT_PATH}location/rule/not/configured"
					class="form-horizontal form-bordered form-row-stripped"
					method="post" modelAttribute="domain" name="skuVerifyWeightDiffForm">
					<div class="form-body" style="width: 1200px;margin: 0 auto">
						<div style="margin-bottom: 10px">
							<a class="btn btn-default" href='${CONTEXT_PATH}location/rule'>
								<i class="m-icon-swapleft"></i> 规则管理
							</a>
							<a class="btn btn-default" style="opacity: 0.6">
								<i class="m-icon-swapleft"></i>未配置规则包材
							</a>
						</div>
						<div style="margin-bottom: 10px">
							说明：只有类型为包材的耗材才能创建规则
						</div>
						<div>
						<!--<div style="; height:600px; overflow:scroll;">-->
						<table class="table table-striped table-bordered table-hover table-condensed mt10" id="item-list">
							<colgroup>
								<col width="10%" />
								<col width="15%" />
								<col width="20%" />
								<col width="15%" />
								<col width="15%" />
								<col width="15%" />
								<col width="10%" />
							</colgroup>
							<thead>
								<tr>
									<th>编号</th>
									<th>包材型号</th>
									<th>包材名称</th>
									<th>尺寸（mm）</th>
									<th>重量</th>
									<th>使用环节</th>
									<th>状态</th>
								</tr>
							</thead>
							<tbody>
							<#list domain.whPackagingMaterialManagements as managements>
							<tr>
								<td>${managements.id}</td>
								<td>${managements.materialArticleNumber}</td>
								<td>${managements.name}</td>
								<td>${managements.length}*${managements.width}*${managements.height}</td>
								<td>${managements.weight}</td>
								<td>${managements.useLink}</td>
								<td>${util('enumName','com.estone.warehouse.enums.MaterialStatus', managements.status)}</td>
							</tr>
							</#list>
							</tbody>
						</table>
						</div>
					</div>
				</form>
			</div>
			<br/>
		</div>

	</div>
	<#include "/common/footer.html">
	</div>
	<script type="text/javascript" src="${CONTEXT_PATH}js/datepicker/WdatePicker.js"></script>
	<script type="text/javascript" src="${CONTEXT_PATH}js/jquery.region.js"></script>
	<script type="text/javascript">
	
	$(document).ready(function(){
  		var submitForm = $('#submit-form');
  		// 表单校验
  		submitForm.validate({
  			rules : {
  				
  			},
  			messages : {
  				
  			},
  			invalidHandler : function(event, validator) {
  				App.scrollTo(submitForm.find(".has-success:eq(0)"), -50);
  			},
  			submitHandler : function(form) {
  				
  				var length = $("#item-list tbody tr").length;
  				for (var i = 0; i < length; i++) {
  					var percent = $("input[name='skuVerifyWeightDiffs["+i+"].percent").val();
  					var left = $("input[name='skuVerifyWeightDiffs["+i+"].left").val();
  					var right = $("input[name='skuVerifyWeightDiffs["+i+"].right").val();
  					
  					if(percent == ''){
  	  			  		layer.alert("抽检比例不能为空!", 'error');
  	  			  		return false;
  	  			  	}
  					if(left == ''){
  			  			layer.alert("区间上限不能为空!", 'error');
  			  			return false;
  			  		}
  					//if(right == '' && i < (length-1)){
                    if(right == ''){
  			  			layer.alert("区间下限不能为空!", 'error');
  			  			return false;
  			  		}
  					var percent = parseInt(percent);
  					var left = parseInt(left);
  					var right = parseInt(right);
  					if(left >= right && i < (length-1)){
  						layer.alert("区间上限不能大于或等于区间下限!", 'error');
  			  			return false;
  					}
  					if (i>0) {
  						var lastRight = $("input[name='skuVerifyWeightDiffs["+(i-1)+"].right").val();
  						var lastRight = parseInt(lastRight);
  						if(left < lastRight){
  	  						layer.alert("区间上限不能小于上一条的区间下限!", 'error');
  	  			  			return false;
  	  					}else if (left > lastRight) {
  	  						layer.alert("区间未封闭，请补充!", 'error');
	  			  			return false;
						}
					}
				}
  				
  				App.blockUI();
  				form.submit();
  				return false;
  			},
  			/* 重写错误显示消息方法,以layer.alert方式弹出错误消息 */
  			showErrors : function(errorMap, errorList) {
  				var msg = "";  
  				$.each(errorList, function(i, v) {  
  					msg += (v.message + "\r\n");  
  				});  
  				if (msg != "") layer.alert(msg,'error');
  			},
  			/* 失去焦点时不验证 */  
  			onfocusout : false 
  		});
		
	}); // end ready
	
	function removeTr(index){
		// 删除本行
		$("#tr-item-"+index).remove();
			
		// 重新编排
		$("#item-list tbody tr").each(function(i){
			refreshIndex(i, this);
		});
	}
	
	function addTr(index){
		var currentTr = $("#tr-item-"+index);
		var length = $("#item-list tbody tr").length;
		
		var appendTr = '<tr id="tr-item-'+length+'">'
				+'<td></td>'

				+'<td>'
					+'<input class="form-control input-xsmall item-left" onblur="checkNumber(this)" placeholder="≧" name="skuVerifyWeightDiffs['+length+'].left" value=""/></td>'
				+'<td>'
					+'<input type="hidden" name="qcProportions['+length+'].isMin" value="false" />'
					+'<input type="hidden" name="qcProportions['+length+'].isMax" value="false" />'
					+'——'
				+'</td>'
				+'<td>'
					+'<input class="form-control input-xsmall item-right" onblur="checkNumber(this)" placeholder="＜" name="skuVerifyWeightDiffs['+length+'].right" value="" />'
				+'</td>'
				+'<td>'
					+'<input class="form-control input-xsmall item-percent" onblur="checkPercent(this)" placeholder="%" name="skuVerifyWeightDiffs['+length+'].percent" value=""/>'
				+'</td>'

            	+'<td></td>'

				+'<td>'
					+'<buttton type="button" class="btn btn-xs btn-default" onclick="removeTr('+length+')">'
						+'<span class="glyphicon glyphicon-remove"></span>删除'
					+'</buttton>'
					+'<buttton type="button" class="btn btn-xs btn-default" onclick="addTr('+length+')">'
						+'<span class="glyphicon glyphicon-add"></span>添加'
					+'</buttton>'
				+'</td>'
				+'</tr>';
				
		currentTr.after(appendTr);
		
		// 重新编排
		$("#item-list tbody tr").each(function(i){
			refreshIndex(i, this);
		});
	}
	
	function checkPercent(obj){
        var index = obj.parentNode.parentNode.rowIndex;
        index=index-1;
        initWeightDiff(index);

  		var $this = $(obj);
  		if($this.val() == ''){
  			$this.val("");
  			return;
  		}
  		//var reg = /^\+?[1-9][0-9]*$/;
  		var reg = /^(\+)?\d+(\.\d+)?$/;
  		if (!reg.test($this.val())) {
  			//layer.alert("请输入正确的正整数", 'error');
  			layer.alert("只能输入正实数", 'error');
  			$this.val("");
  			return;
  		}
  		if ($this.val() > 100) {
  			layer.alert("百分比不能超过100", 'error');
  			$this.val("");
  			return;
  		}

        buildWeightDiff(index);
	}
	
	function checkNumber(obj){
        var index = obj.parentNode.parentNode.rowIndex;
        index=index-1;
        initWeightDiff(index);

  		var $this = $(obj);
  		if($this.val() == ''){
  			$this.val("");
  			return;
  		}
  		var reg = /^\+?[1-9][0-9]*$/;
  		if (!reg.test($this.val())) {
  			layer.alert("请输入正确的正整数", 'error');
  			$this.val("");
  			return;
  		}

        buildWeightDiff(index);
	}

	function initWeightDiff(index){
		var tds = $("#item-list tbody tr")[index].children;
        $(tds[0]).text("");
        $(tds[5]).text("");
	}

	function buildWeightDiff(index){
        var tds = $("#item-list tbody tr")[index].children;
        var percent = $("input[name='skuVerifyWeightDiffs["+index+"].percent").val();
        var right = $("input[name='skuVerifyWeightDiffs["+index+"].right").val();
        if(percent!='' && right!=''){
			var percent = percent*1;
			var right = parseInt(right);
			var weightDiff = Math.round(percent*right/100);
            $(tds[0]).text("-" + weightDiff);
            $(tds[5]).text(weightDiff);
		}
	}

	</script>
</body>
</html>