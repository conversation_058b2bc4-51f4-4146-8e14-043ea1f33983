<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">
<html>
<head>
<title>易世通达仓库管理系统</title>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
	<#include "/common/include.html">
	<style type="text/css">
.modal-body input {
	width: 240px;
}
.top_bar{
    position:fixed;top:0px;
}
#item-list tbody {
	display:block;
	height:550px;
	overflow-y:scroll;
	text-align: center;
}

#item-list thead, tbody tr {
	display: table;
	width: 800px;
	table-layout: fixed;
}
#task-list td {
	vertical-align:middle;
}
</style>
</head>
<body>
<@header method="header" active="14010000"><#include "/ftl/header.ftl"></@header>

	<div id="page" style="background-color: rgb(231, 237, 248)">
		<div class="row">
			<div class="col-md-12" style="padding: 0">
				<ul class="page-breadcrumb breadcrumb" style="background-color: white;">
					<li><a href="#">系统配置</a></li>
					<li class="active">SKU审核误差配置</li>
					<li class="active">编辑</li>
				</ul>
			</div>
		</div>
		<!-- 内容 -->
		<div class="container-fluid" style="background-color: white;border: none">
		<!-- END PAGE HEADER-->
		<div class="row" style="padding-top: 80px;height: 760px">
			<div class="col-md-12">
				<form id="submit-form" action="${CONTEXT_PATH}system/skuVerifyWeightDiff/auditUpdate"
					class="form-horizontal form-bordered form-row-stripped"
					method="post" modelAttribute="domain" name="skuVerifyWeightDiffForm">
					<div class="form-body" style="width: 800px;margin: 0 auto">
						<div style="margin-bottom: 10px">
							<button type="submit" class="btn blue">
								<i class="icon-save"></i> 保存
							</button>
							<a class="btn btn-default" href='${CONTEXT_PATH}system/skuVerifyWeightDiff'>
								<i class="m-icon-swapleft"></i> 返回
							</a>
						</div>
						<div>
						<!--<div style="; height:600px; overflow:scroll;">-->
						<table class="table table-striped table-bordered table-hover table-condensed mt10" id="item-list">
							<colgroup>
								<col width="20%" />
								<col width="5%" />
								<col width="20%" />
								<col width="15%" />
								<col width="15%" />
							</colgroup>
							<thead>
								<tr>
									<th>SKU净重下限g(≧)</th>
									<th></th>
									<th>SKU净重上限g(＜)</th>
									<th>误差倍数</th>
									<th>操作</th>
								</tr>
							</thead>
							<tbody>
								<#list domain.skuAuditWeightDiffs as item>
									<tr id="tr-item-${item_index}">
										<td>
											<#if (item.isMin)?? && item.isMin == true>
												<input class="form-control input-xsmall item-left" readonly="true" placeholder="≧" name="skuAuditWeightDiffs[${item_index }].left" value="${item.left }"/>
											<#else >
												<input class="form-control input-xsmall item-left" onblur="checkNumber(this)" placeholder="≧" name="skuAuditWeightDiffs[${item_index }].left" value="${item.left }"/>
											</#if>
										</td>
										<td>
											<input type="hidden" name="skuAuditWeightDiffs[${item_index }].isMin" value="${item.isMin }" />
											<input type="hidden" name="skuAuditWeightDiffs[${item_index }].isMax" value="${item.isMax }" />
											——
										</td>
										<td>
											<#if (item.isMax)?? && item.isMax == true>
												<input class="form-control input-xsmall item-right" readonly="true" placeholder="＜" name="skuAuditWeightDiffs[${item_index }].right" value="${item.right }" />
											<#else >
												<input class="form-control input-xsmall item-right" onblur="checkNumber(this)" placeholder="＜" name="skuAuditWeightDiffs[${item_index }].right" value="${item.right }" />
											</#if>
										</td>
										<td>
											<input class="form-control input-xsmall item-percent" onblur="checkNumber(this)" placeholder="%" name="skuAuditWeightDiffs[${item_index }].diffMulti" value="${item.diffMulti}"/>
										</td>
										<td>
											<#if (!(item.isMin)?? || item.isMin == false) && (!(item.isMax)?? || item.isMax == false)>
												<buttton type="button" class="btn btn-xs btn-default" onclick="removeTr(${item_index})">
												<span class="glyphicon glyphicon-remove"></span>  删除
												</buttton>
											</#if>
											<#if !(item.isMax)?? || item.isMax == false>
												<buttton type="button" class="btn btn-xs btn-default" onclick="addTr(${item_index})">
												<span class="glyphicon glyphicon-add"></span>  添加
												</buttton>
											</#if>
										</td>
									</tr>
								</#list>
							</tbody>
						</table>
						</div>
					</div>
				</form>
			</div>
			<br/>
		</div>

	</div>
	<#include "/common/footer.html">
	</div>
	<script type="text/javascript" src="${CONTEXT_PATH}js/datepicker/WdatePicker.js"></script>
	<script type="text/javascript" src="${CONTEXT_PATH}js/jquery.region.js"></script>
	<script type="text/javascript">

	$('#submit-form').submit(function(form){
		var length = $("#item-list tbody tr").length;
		for (var i = 0; i < length; i++) {
			var diffMulti = $("input[name='skuAuditWeightDiffs["+i+"].diffMulti").val();
			var left = $("input[name='skuAuditWeightDiffs["+i+"].left").val();
			var right = $("input[name='skuAuditWeightDiffs["+i+"].right").val();

			if(diffMulti == ''){
				layer.alert("误差倍数不能为空!", 'error');
				return false;
			}
			if(left == ''){
				layer.alert("区间上限不能为空!", 'error');
				return false;
			}
			if(right == ''){
				layer.alert("区间下限不能为空!", 'error');
				return false;
			}
			var diffMulti = parseInt(diffMulti);
			var left = parseInt(left);
			var right = parseInt(right);
			if(left >= right && i <= (length-1)){
				layer.alert("区间上限不能小于区间下限!", 'error');
				return false;
			}
			if (i>0) {
				var lastRight = $("input[name='skuAuditWeightDiffs["+(i-1)+"].right").val();
				var lastRight = parseInt(lastRight);
				if(left < lastRight){
					layer.alert("区间上限不能小于上一条的区间下限!", 'error');
					return false;
				}else if (left > lastRight) {
					layer.alert("区间未封闭，请补充!", 'error');
					return false;
				}
			}
		}
		App.blockUI();
		form.submit();
		return true;
	});

	function removeTr(index){
		// 删除本行
		$("#tr-item-"+index).remove();
			
		// 重新编排
		$("#item-list tbody tr").each(function(i){
			refreshIndex(i, this);
		});
	}
	
	function addTr(index){
		var currentTr = $("#tr-item-"+index);
		var length = $("#item-list tbody tr").length;
		
		var appendTr = '<tr id="tr-item-'+length+'">'
				+'<td>'
					+'<input class="form-control input-xsmall item-left" onblur="checkNumber(this)" placeholder="≧" name="skuAuditWeightDiffs['+length+'].left" value=""/></td>'
				+'<td>'
					+'<input type="hidden" name="qcProportions['+length+'].isMin" value="false" />'
					+'<input type="hidden" name="qcProportions['+length+'].isMax" value="false" />'
					+'——'
				+'</td>'
				+'<td>'
					+'<input class="form-control input-xsmall item-right" onblur="checkNumber(this)" placeholder="＜" name="skuAuditWeightDiffs['+length+'].right" value="" />'
				+'</td>'
				+'<td>'
					+'<input class="form-control input-xsmall item-percent" onblur="checkPercent(this)" placeholder="%" name="skuAuditWeightDiffs['+length+'].diffMulti" value=""/>'
				+'</td>'

				+'<td>'
					+'<buttton type="button" class="btn btn-xs btn-default" onclick="removeTr('+length+')">'
						+'<span class="glyphicon glyphicon-remove"></span>删除'
					+'</buttton>'
					+'<buttton type="button" class="btn btn-xs btn-default" onclick="addTr('+length+')">'
						+'<span class="glyphicon glyphicon-add"></span>添加'
					+'</buttton>'
				+'</td>'
				+'</tr>';
				
		currentTr.after(appendTr);
		
		// 重新编排
		$("#item-list tbody tr").each(function(i){
			refreshIndex(i, this);
		});
	}
	
	function checkPercent(obj){
        var index = obj.parentNode.parentNode.rowIndex;
        index=index-1;

  		var $this = $(obj);
  		if($this.val() == ''){
  			$this.val("");
  			return;
  		}
  		//var reg = /^\+?[1-9][0-9]*$/;
  		var reg = /^(\+)?\d+(\.\d+)?$/;
  		if (!reg.test($this.val())) {
  			//layer.alert("请输入正确的正整数", 'error');
  			layer.alert("只能输入正实数", 'error');
  			$this.val("");
  			return;
  		}
  		if ($this.val() > 100) {
  			layer.alert("百分比不能超过100", 'error');
  			$this.val("");
  			return;
  		}

	}
	
	function checkNumber(obj){
        var index = obj.parentNode.parentNode.rowIndex;
        index=index-1;

  		var $this = $(obj);
  		if($this.val() == ''){
  			$this.val("");
  			return;
  		}
  		var reg = /^\+?[1-9][0-9]*$/;
  		if (!reg.test($this.val())) {
  			layer.alert("请输入正确的正整数", 'error');
  			$this.val("");
  			return;
  		}

	}

	</script>
</body>
</html>