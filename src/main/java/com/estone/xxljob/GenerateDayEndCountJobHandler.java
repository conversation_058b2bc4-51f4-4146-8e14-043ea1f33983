package com.estone.xxljob;


import javax.annotation.Resource;

import org.springframework.stereotype.Component;

import com.estone.warehouse.service.DayEndCountHandlerService;
import com.estone.xxljob.config.AbstractJobHandler;
import com.xxl.job.core.biz.model.ReturnT;

import lombok.extern.slf4j.Slf4j;

/**
 * @Description: 每日库存汇总定时任务
 * @Author: Yimeil
 * @Date: 2021/10/13 17:13
 * @Version: 1.0.0
 */
@Slf4j
@Component
public class GenerateDayEndCountJobHandler extends AbstractJobHandler {

    GenerateDayEndCountJobHandler(){
        super("GenerateDayEndCountJobHandler");
    }
    
    @Resource
    private DayEndCountHandlerService dayEndCountHandlerService;

    @Override
   public ReturnT<String> run(String param) throws Exception {
       try {
           log.info("---invoke GenerateDayEndCountJob start---");
           long startTime = System.currentTimeMillis();
           dayEndCountHandlerService.handleDayEndCount(param);
           log.info("---invoke GenerateDayEndCountJob end[" + (System.currentTimeMillis() - startTime) + "]---");
       }
       catch (Exception e) {
           log.error(e.getMessage(), e);
           return ReturnT.FAIL;
       }
       return ReturnT.SUCCESS;
   }
}
