package com.estone.xxljob;

import com.estone.statistics.service.ScrapRecommendMonthService;
import com.estone.xxljob.config.AbstractJobHandler;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * @description 报废建议
 * @create 2024/7/12 10:17
 */
@Slf4j
@Component
public class ScrapRecommendMonthJobHandler extends AbstractJobHandler {

    ScrapRecommendMonthJobHandler() {
        super("ScrapRecommendMonthJobHandler");
    }

    @Resource
    private ScrapRecommendMonthService scrapRecommendMonthService;

    @Override
    public ReturnT<String> run(String param) throws Exception {
        try {
            long startTime = System.currentTimeMillis();
            XxlJobLogger.log("---invoke scrapRecommendMonthJobHandler start---");
            scrapRecommendMonthService.syncScrapRecommendMonth();
            XxlJobLogger.log("---invoke scrapRecommendMonthJobHandler end[" + (System.currentTimeMillis() - startTime) + "]---");
        }
        catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        return ReturnT.SUCCESS;
    }
}
