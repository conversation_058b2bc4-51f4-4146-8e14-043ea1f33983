package com.estone.xxljob;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.estone.common.executors.ExecutorUtils;
import com.estone.common.util.*;
import com.estone.common.util.model.ApiResult;
import com.estone.pac.configer.PacCommonConfiger;
import com.estone.pac.service.*;
import com.estone.system.log.bean.WhSystemLog;
import com.estone.system.log.service.WhSystemLogService;
import com.estone.system.rabbitmq.bean.AmqMessage;
import com.estone.system.rabbitmq.bean.AmqMessageQueryCondition;
import com.estone.xxljob.config.AbstractJobHandler;
import com.taobao.pac.sdk.cp.dataobject.request.WMS_CONSIGN_ORDER_NOTIFY.WmsConsignOrderItem;
import com.taobao.pac.sdk.cp.dataobject.request.WMS_CONSIGN_ORDER_NOTIFY.WmsConsignOrderNotifyRequest;
import com.xxl.job.core.biz.model.ReturnT;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Semaphore;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Slf4j
@Component
public class GenPacConsignOrderJobHandler extends AbstractJobHandler {

    GenPacConsignOrderJobHandler(){
        super("GenPacConsignOrderJobHandler");
    }


    @Resource
    private PacConsignOrderService pacConsignOrderService;

    @Resource
    private PacStockOutOrderService pacStockOutOrderService;

    @Resource
    private PacStockInOrderService pacStockInOrderService;

    @Resource
    private WhSystemLogService whSystemLogService;

    @Resource
    private PacSubscriptionService pacSubscriptionService;

    private static Semaphore semaphore = new Semaphore(1);

    private final static ExecutorService executors = ExecutorUtils.newFixedThreadPool(10);

    private static final String REDISSON_LOCK_KEY = "REDISSONLOCK:GENPACCONSIGNORDERJOB";

    @Override
    //@XxlJob("GenPacConsignOrderJobHandler")
    public ReturnT<String> run(String param) throws Exception {
        logger.warn("---invoke GenPacConsignOrderJob start---");

        if (!semaphore.tryAcquire()) {
            logger.warn("GenPacConsignOrderJob 等待前一次定时任务执行完......");
            return ReturnT.FAIL;
        }

        try {
            if (RedissonLockUtil.tryLock(REDISSON_LOCK_KEY, 10, 300)) {
//                AmqMessageQueryCondition query = new AmqMessageQueryCondition();
//                query.setSendStatus(false);
//                query.setModuleName(PacCommonConfiger.WMS_CONSIGN_ORDER_NOTIFY);
//                Pager pager = new Pager();
//                pager.setPageSize(2000);
//                List<AmqMessage> messageList = amqMessageService.queryAmqMessages(query, pager);

                Date startDate = new Date();

                Calendar calendar = Calendar.getInstance();

                calendar.setTime(startDate);

                calendar.add(Calendar.HOUR,-1);

                //结束时间
                String startTime = DateUtils.dateToString(calendar.getTime(), "yyyy-MM-dd HH:mm:ss");
                //开始时间
                String endTime = DateUtils.dateToString(startDate, "yyyy-MM-dd HH:mm:ss");

//              String url = "http://120.76.97.34:8088/wms/foreign/caiNiaoPartner/wmsAmqMessageReturn";

                String url;
                try {
                    url = CacheUtils.SystemParamGet("ECS_PARAM.WMS_AMQ_MESSAGE_RETURN_URL").getParamValue();
                } catch (Exception e) {
                    throw new RuntimeException("请求阿里云参数名：WMS_AMQ_MESSAGE_RETURN_URL 未定义!");
                }
                if (StringUtils.isEmpty(url)) throw new RuntimeException("请求阿里云参数名：WMS_AMQ_MESSAGE_RETURN_URL 参数值url未定义!");

                logger.info(url);

                logger.warn("开始执行优选仓库存操作：" + url);

                AmqMessageQueryCondition amqMessageQueryCondition=new AmqMessageQueryCondition();

                amqMessageQueryCondition.setStartTime(startTime);

                amqMessageQueryCondition.setEndTime(endTime);

                amqMessageQueryCondition.setFilterModuleName(PacCommonConfiger.WMS_SKU_INFO_NOTIFY);

                ApiResult apiResult = HttpExtendUtils.post(url,"",amqMessageQueryCondition,  ApiResult.class, 300000,300000);


                if (!apiResult.isSuccess() || apiResult.getResult()==null) {
                    return ReturnT.FAIL;
                }

                List<AmqMessage> messageList= JSONObject.parseObject(JSONObject.toJSONString(apiResult.getResult()), new TypeReference<List<AmqMessage>>() {
                });
                // 获取需要拒单单号
                List<String> countList = getWmsRejectOrderNo(messageList);

                if (CollectionUtils.isNotEmpty(messageList)) {
                    CountDownLatch countDownLatch = new CountDownLatch(messageList.size());

                    for (AmqMessage amqMessage : messageList) {
                        String moduleName = amqMessage.getModuleName();
                        try {
                            if (PacCommonConfiger.WMS_STOCK_IN_ORDER_NOTIFY.equals(moduleName)){
                                pacStockInOrderService.doPacStockInOrder(amqMessage);
                            } // 优选仓 退仓
                            else if (PacCommonConfiger.WMS_STOCK_OUT_ORDER_NOTIFY.equals(moduleName)) {
                                pacStockOutOrderService.doPacStockOutOrder(amqMessage);
                            }else {
                                ExecutorUtils.execute(executors, () -> {

                                    // 优选仓 出库
                                    if (PacCommonConfiger.WMS_CONSIGN_ORDER_NOTIFY.equals(moduleName)) {
                                        pacConsignOrderService.pacConsignOrder(amqMessage,countList);
                                    }
                                    // 优选仓 采购入库
                                   /* else if (PacCommonConfiger.WMS_STOCK_IN_ORDER_NOTIFY.equals(moduleName)){
                                        pacStockInOrderService.doPacStockInOrder(amqMessage);
                                    }*/
                                    // 优选仓 出库 取消
                                    else if (PacCommonConfiger.WMS_ORDER_CANCEL_NOTIFY.equals(moduleName)) {
                                        pacConsignOrderService.pacOrderCancel(amqMessage,false);
                                    }
//                                    //菜鸟商品推送
//                                    else if (PacCommonConfiger.WMS_SKU_INFO_NOTIFY.equals(moduleName)) {
//                                        pacSkuInfoService.doPacSkuInfo(amqMessage);
//                                    }
                                    //老版划仓通知
                                    else if(PacCommonConfiger.WMS_SELLER_ENTER_MESSAGE.equals(moduleName)){
                                        pacSubscriptionService.doPacSellerEnter(amqMessage);
                                    }
                                    //划仓通知
                                    else if(PacCommonConfiger.WMS_SUBSCRIPTION_NOTIFY.equals(moduleName)){
                                        pacSubscriptionService.doPacSubscriptionNotify(amqMessage);
                                    }
                                }, "PAC-CONSIGN_ORDER");
                            }
                        }catch (Exception e) {
                            logger.error("定时执行MQ消息数据执行失败！messageId：" + amqMessage.getMessageId(), e);
                        } finally {
                            countDownLatch.countDown();
                        }
                    }
                    // 等待执行完返回
                    try {
                        countDownLatch.await();
                    }
                    catch (Exception e) {
                        log.error(e.getMessage(), e);
                    }
                }
            }
            else {
                logger.warn("---GenPacConsignOrderJob 已锁定，等待前一次定时任务执行完---");
            }
        }
        catch (Exception e) {
            logger.error("---catch GenPacConsignOrderJob exception---", e);
        }
        finally {
            semaphore.release();
            RedissonLockUtil.unlock(REDISSON_LOCK_KEY);
            logger.warn("---invoke GenPacConsignOrderJob finally semaphore---");
        }

        logger.warn("---invoke GenPacConsignOrderJob end---");
        return ReturnT.SUCCESS;
    }

    // 获取拒单单号
    private List<String> getWmsRejectOrderNo(List<AmqMessage> messageList){
        // 获取本次同步拆分订单
        List<AmqMessage> orderList = messageList.stream().
                filter(m -> PacCommonConfiger.WMS_CONSIGN_ORDER_NOTIFY.equals(m.getModuleName())).collect(Collectors.toList());
        List<String> countList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(orderList)) {
            // 获取拦截配置
            Map<String, Integer> skuInterceptConfig = getSkuInterceptConfig();
            Map<String,String> orderSourceCodeMap = new HashMap<>();
            for(AmqMessage amqMessage:orderList){
                WmsConsignOrderNotifyRequest request =
                        JSONObject.parseObject(amqMessage.getMessageBody(), WmsConsignOrderNotifyRequest.class);
                if (request == null) continue;
                List<WmsConsignOrderItem> orderItemList = request.getOrderItemList();
                if (CollectionUtils.isEmpty(orderItemList)) continue;

                // 拆单拦截
                String orderSourceCode = orderItemList.get(0).getOrderSourceCode();
                if (StringUtils.isBlank(orderSourceCode)) continue;
                String existOrderCode = orderSourceCodeMap.get(orderSourceCode);
                if (StringUtils.isNotBlank(existOrderCode)){
                    countList.add(request.getOrderCode());
                    if (!countList.contains(existOrderCode))
                        countList.add(existOrderCode);
                    continue;
                }else {
                    orderSourceCodeMap.put(orderSourceCode, request.getOrderCode());
                }

                // OMS数量配置拦截
                if (skuInterceptConfig != null && skuInterceptConfig.size()>0){
                    boolean bool = orderItemList.stream().anyMatch(i -> {
                        return skuInterceptConfig.containsKey(i.getItemCode());
                      /*   Integer val = skuInterceptConfig.get(i.getItemCode());
                       if (val != null && val < i.getItemQuantity().intValue()) {
                            return true;
                        }
                        return false;*/
                    });
                    if (bool)
                        countList.add(request.getOrderCode());
                }
            }
        }

        if (CollectionUtils.isNotEmpty(countList))
            log.info("WMS_REJECT:"+countList);
        return  countList;
    }

    // 此处单线程调用
    private Map<String,Integer> getSkuInterceptConfig() {
        Map<String,Integer> existConfig = StringRedisUtils.hGetAll(RedisConstant.SKU_INTERCEPT_CONFIG);
        if (existConfig != null && existConfig.size()>0)
            return existConfig;

        String skuInterceptConfigUrl = CacheUtils.SystemParamGet("OMS_PARAM.GET_SKU_INTERCEPT_CONFIG").getParamValue();
        if (org.apache.commons.lang.StringUtils.isBlank(skuInterceptConfigUrl)) {
           return null;
        }

        Map<String, String> body = new HashMap<>();
        String value = JSON.toJSONString(new HashMap<String,String>(){{put("saleChannel","SMT");}});
        body.put("args", value);
        body.put("method", "findSkuInterceptConfig");
        ApiResult apiResult;
        logger.info("请求OMS获取sku拦截配置: url:" + skuInterceptConfigUrl + "==>requestBody: " + body);
        try {
            apiResult = HttpExtendUtils.post(skuInterceptConfigUrl , HttpUtils.ACCESS_TOKEN, body,ApiResult.class,60000,60000);
            logger.info("请求OMS获取sku拦截配置：url：" + skuInterceptConfigUrl + "===>参数：" + value + "===>result：" + JSON.toJSONString(apiResult));
            WhSystemLog whSystemLog = new WhSystemLog();
            whSystemLog.setContent(JSON.toJSONString(apiResult));
            whSystemLog.setModule(RedisConstant.SKU_INTERCEPT_CONFIG);
            whSystemLog.setRelevanceId(0);
            whSystemLogService.createWhSystemLog(whSystemLog);
        } catch (Exception e) {
            logger.error("请求OMS获取sku拦截配置", e);
            throw new RuntimeException(e.getMessage());
        }
        if (!apiResult.isSuccess()) {
            String errorMessage = apiResult.getErrorMsg();
            logger.error("errorMessage:" + errorMessage);
            throw new RuntimeException(errorMessage);
        }
        if (apiResult.getResult() == null) return null;
        List<SkuInterceptConfig> skuInterceptConfigs = JSONObject.parseObject(JSONObject.toJSONString(apiResult.getResult()), new TypeReference<List<SkuInterceptConfig>>() {});
        if (CollectionUtils.isEmpty(skuInterceptConfigs)) return null;
        existConfig = skuInterceptConfigs.stream().collect(Collectors.toMap(SkuInterceptConfig::getSku, SkuInterceptConfig::getOrderType));
        StringRedisUtils.hmset(RedisConstant.SKU_INTERCEPT_CONFIG,existConfig);
        StringRedisUtils.setExpire(RedisConstant.SKU_INTERCEPT_CONFIG,1, TimeUnit.HOURS);
        return  existConfig;
    }
    // 必须静态，采用反射创建对象，缓存6小时
   static class SkuInterceptConfig{
        private String sku;
        private Integer skuNo;
        private Integer orderType;
        public String getSku() {
            return sku;
        }

        public void setSku(String sku) {
            this.sku = sku;
        }

        public Integer getSkuNo() {
            return skuNo;
        }

        public void setSkuNo(Integer skuNo) {
            this.skuNo = skuNo;
        }

        public Integer getOrderType() {
            return orderType;
        }

        public void setOrderType(Integer orderType) {
            this.orderType = orderType;
        }
    }
}
