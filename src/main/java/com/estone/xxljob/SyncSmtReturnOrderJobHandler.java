package com.estone.xxljob;


import com.estone.checkout.service.SmtReturnOrderService;
import com.estone.common.util.DateUtils;
import com.estone.xxljob.config.AbstractJobHandler;
import com.xxl.job.core.biz.model.ReturnT;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;

/**
 * <AUTHOR>
 * @version 1.0
 * @Description 同步es托管退仓单
 * @date 2020/6/16 11:10
 */
@Slf4j
@Component
public class SyncSmtReturnOrderJobHandler extends AbstractJobHandler {

    SyncSmtReturnOrderJobHandler(){
        super("SyncSmtReturnOrderJobHandler");
    }

    @Resource
    private SmtReturnOrderService smtReturnOrderService;

    public static void main(String[] args) {
        String startTime = null, endTime = null;
        DateTimeFormatter format = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        LocalDate now = LocalDate.now();
        LocalDate yesterday = now.minusDays(1);
        startTime = yesterday.format(format) + " 00:00:00";
        endTime = yesterday.format(format) + " 23:59:59";
        System.out.println(startTime);
        System.out.println(endTime);
    }

    @Override
    public ReturnT<String> run(String param) throws Exception {
        log.warn("---invoke SyncSmtReturnOrderJobHandler start---");
        try {
            String startTime = null, endTime = null;
            if (StringUtils.isNotBlank(param)) {
                String[] split = param.split(",");
                DateUtils.stringToDate(split[0], DateUtils.STANDARD_DATE_PATTERN);
                DateUtils.stringToDate(split[1], DateUtils.STANDARD_DATE_PATTERN);
                startTime = split[0];
                endTime = split[1];
            } else {
                DateTimeFormatter format = DateTimeFormatter.ofPattern("yyyy-MM-dd");
                LocalDate now = LocalDate.now();
                LocalDate lastWeek = now.minusDays(2);
                startTime = lastWeek.format(format) + " 00:00:00";
                endTime = now.minusDays(1).format(format) + " 23:59:59";
            }
            smtReturnOrderService.syncData(startTime, endTime);
        }catch (Exception e){
            log.error("invoke SyncSmtReturnOrderJobHandler error:" + e.getMessage(), e);
        }
        log.warn("---invoke SyncSmtReturnOrderJobHandler end---");
        return ReturnT.SUCCESS;
    }
}
