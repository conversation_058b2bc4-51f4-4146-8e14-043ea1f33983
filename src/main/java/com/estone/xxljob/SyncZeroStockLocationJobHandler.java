package com.estone.xxljob;

import java.util.ArrayList;
import java.util.List;

import javax.annotation.Resource;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import com.estone.common.util.CommonUtils;
import com.estone.warehouse.service.WhStockMoveService;
import com.estone.xxljob.config.AbstractJobHandler;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.log.XxlJobLogger;

import lombok.extern.slf4j.Slf4j;

/**
 * @Description: 库位调整
 * @Author: Yimeil
 * @Date: 2023/3/29 14:56
 * @Version: 1.0.0
 */
@Slf4j
@Component
public class SyncZeroStockLocationJobHandler extends AbstractJobHandler {

    SyncZeroStockLocationJobHandler() {
        super("SyncZeroStockLocationJobHandler");
    }

    @Resource
    private WhStockMoveService whStockMoveService;

    @Override
    public ReturnT<String> run(String param) throws Exception {
        XxlJobLogger.log("----------- SyncZeroStockLocationJobHandler start -------------------");
        List<String> skuList = new ArrayList<>();
        try {
            if (StringUtils.isNotEmpty(param)) {
                if (StringUtils.contains(param, ",")) {
                    skuList = CommonUtils.splitList(param, ",");
                }
            }
            whStockMoveService.syncZeroStockLocation(skuList);
        }
        catch (Exception e) {
            log.error(e.getMessage(), e);
            XxlJobLogger.log("----------- SyncZeroStockLocationJobHandler exception -------------------");
            XxlJobLogger.log(e.getMessage());
        }
        XxlJobLogger.log("----------- SyncZeroStockLocationJobHandler end -------------------");

        return ReturnT.SUCCESS;
    }
}
