package com.estone.xxljob;

import com.estone.apv.bean.WhApvQueryCondition;
import com.estone.apv.common.ApvOrderType;
import com.estone.apv.dao.WhApvDao;
import com.estone.common.util.DateUtils;
import com.estone.common.util.DingTalkSendUtils;
import com.estone.xxljob.config.AbstractJobHandler;
import com.xxl.job.core.biz.model.ReturnT;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.Date;

/**
 * <AUTHOR>
 * 钉钉消息通知OMS推送订单情况
 */
@Slf4j
@Component
public class DingTalkApvOrderCountJobHandler extends AbstractJobHandler {


    DingTalkApvOrderCountJobHandler() {
        super("DingTalkApvOrderCountJobHandler");
    }

    @Autowired
    private WhApvDao whApvDao;

    private static DateTimeFormatter FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    @Override
    public ReturnT<String> run(String param) throws Exception {
        try {
            log.info("---invoke DingTalkApvOrderCountJobHandler start---"+param);
            LocalDateTime startTime = LocalDate.now().minusDays(1).atTime(16, 0, 0);
            LocalDateTime endTime = LocalDate.now().atTime(7, 10, 0);
            WhApvQueryCondition condition = new WhApvQueryCondition();
            condition.setFromCreateDate(FORMATTER.format(startTime));
            condition.setToCreateDate(FORMATTER.format(endTime));
            int countAll = whApvDao.queryWhApvAndItemListCount(condition);
            condition.setShipStatusList(Arrays.asList(ApvOrderType.OPTIMAL.intCode(), ApvOrderType.OPTIMAL_JB.intCode()));
            int countOptimal = whApvDao.queryWhApvAndItemListCount(condition);
            int countNotOptimal = countAll - countOptimal;
            StringBuffer subStr = new StringBuffer();
            subStr.append("时间：").append(FORMATTER.format(startTime)).append("至").append(FORMATTER.format(endTime)).append(",\n")
                    .append("推送本地仓单量：").append(countNotOptimal).append(",\n")
                    .append("推送优选仓单量：").append(countOptimal);
            DingTalkSendUtils.sendOmsPushApvCountMsg(subStr.toString());
        } catch (Exception e) {
            log.error("DingTalkApvOrderCountJobHandler===>>>error: ",e);
        }
        return ReturnT.SUCCESS;
    }

}
