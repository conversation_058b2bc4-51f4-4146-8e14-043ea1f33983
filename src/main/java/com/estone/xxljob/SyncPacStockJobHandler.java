package com.estone.xxljob;


import com.estone.foreign.service.OrderRequestService;
import com.estone.pac.bean.PacNewStockDTO;
import com.estone.pac.dao.PacNewStockDao;
import com.estone.pac.service.PacNewStockService;
import com.estone.xxljob.config.AbstractJobHandler;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @Date 2022/3/12 14:38
 **/
@Slf4j
@Component
public class SyncPacStockJob<PERSON>andler extends AbstractJobHandler {

    SyncPacStockJobHandler(){
        super("SyncPacStockJobHandler");
    }

    @Resource
    private PacNewStockService pacNewStockService;

    @Resource
    private OrderRequestService orderRequestService;

    @Resource
    private PacNewStockDao pacNewStockDao;

    @Override
   // @XxlJob("SyncPacStockJobHandler")
    public ReturnT<String> run(String param) throws Exception {
        try {
            log.info("---invoke SyncPacStockJob start---");
            PacNewStockDTO pacNewStockDTO = new PacNewStockDTO();
            List<String> changeSku = orderRequestService.getChangeSku();
            List<String> skuList = pacNewStockDao.querySynchronizationFailSku();

            if (CollectionUtils.isEmpty(changeSku) && CollectionUtils.isEmpty(skuList)) {
                log.info("没有库存变动的sku:  ---invoke SyncPacStockJob end---");
                return ReturnT.FAIL;
            }
            List<String> collect = Stream.of(Optional.ofNullable(changeSku).orElse(new ArrayList<>()), Optional.ofNullable(skuList)
                    .orElse(new ArrayList<>())).flatMap(Collection::stream).distinct().collect(Collectors.toList());
            pacNewStockDTO.setSkuList(collect);
            this.pacNewStockService.synchronizationSkuStock(pacNewStockDTO);
            log.info("---invoke SyncPacStockJob end---");
        }catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        return ReturnT.SUCCESS;
    }
}
