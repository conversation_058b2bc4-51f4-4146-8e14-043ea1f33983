package com.estone.xxljob;


import com.estone.warehouse.service.WhOverseasReturnReportService;
import com.estone.xxljob.config.AbstractJobHandler;
import com.xxl.job.core.biz.model.ReturnT;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;

/**
 * <AUTHOR>
 **/
@Slf4j
@Component
public class SyncOverseasReturnJobHandler extends AbstractJobHandler {

    @Resource
    private WhOverseasReturnReportService whOverseasReturnReportService;

    SyncOverseasReturnJobHandler(){
        super("SyncOverseasReturnJobHandler");
    }

    @Override
    public ReturnT<String> run(String param) throws Exception {
        try {
            log.info("---invoke SyncOverseasReturnJobHandler start---");
            LocalDate today = LocalDate.now();
            // 获取当天的起始时间
            LocalDateTime startOfDay = LocalDateTime.of(today, LocalTime.MIN);
            LocalDateTime endOfDay = LocalDateTime.of(today, LocalTime.MAX);
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            whOverseasReturnReportService.statisticsOverseasReturn(startOfDay.format(formatter),endOfDay.format(formatter));
            log.info("---invoke SyncOverseasReturnJobHandler end---");
        }catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        return ReturnT.SUCCESS;
    }
}
