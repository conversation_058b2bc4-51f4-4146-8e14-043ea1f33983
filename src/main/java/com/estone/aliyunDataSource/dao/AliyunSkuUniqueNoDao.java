package com.estone.aliyunDataSource.dao;

import com.estone.sku.bean.WhUniqueSku;
import com.estone.system.uniquenosegment.bean.WhSkuUniqueNoSegment;

import java.util.List;

public interface AliyunSkuUniqueNoDao {

    /**
     *  保存sku号段配置至外网
     */
    void saveUniqueNoSegment(List<WhSkuUniqueNoSegment> segmentList);

    void updateUniqueNoSegment(WhSkuUniqueNoSegment segment);

    int deleteBySku(String sku);

    int deleteBatch(List<String> skus);

    List<WhSkuUniqueNoSegment> querySegmentBySkus(List<String> skus);

    List<WhUniqueSku> queryWhUniqueSkuList(String dateBegin, String dateEnd);

}
