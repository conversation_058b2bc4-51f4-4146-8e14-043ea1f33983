package com.estone.elasticsearch.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import org.springframework.data.annotation.Id;

/**
 * 新版SKU销量统计数据模型
 * <AUTHOR>
 * @date 2021-12-08 18:01
 */
@Data
//@Document(indexName = "ads_sku_order_num_purchase_stats_period_b_sum",type = "_doc")
public class EsAdsSkuOrderNumPurchaseStatsPeriodBSum {

    @Id
    private Long id;

    /**
     * 日期
     */
    private String dt;

    /**
     * 货号
     */
    @JsonProperty("article_number")
    private String articleNumber;

    /**
     * 每天销量
     */
    @JsonProperty("sum_quantity_day")
    private Integer quantityDayTotal;


    /**
     * 3天销量
     */
    @JsonProperty("sum_quantity_3d")
    private Integer quantity3dTotal;

    /**
     * 5天销量
     */
    @JsonProperty("sum_quantity_5d")
    private Integer quantity5dTotal;
    /**
     * 7天销量
     */
    @JsonProperty("sum_quantity_7d")
    private Integer quantity7dTotal;
    /**
     * 14天销量
     */
    @JsonProperty("sum_quantity_14d")
    private Integer quantity14dTotal;

    /**
     * 30天销量
     */
    @JsonProperty("sum_quantity_30d")
    private Integer quantity30dTotal;

    /**
     * 90天销量
     */
    @JsonProperty("sum_quantity_90d")
    private Integer quantity90dTotal;
}
