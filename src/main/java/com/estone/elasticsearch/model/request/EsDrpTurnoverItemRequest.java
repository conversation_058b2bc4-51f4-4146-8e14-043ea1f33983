package com.estone.elasticsearch.model.request;

import lombok.Data;

import java.util.List;

@Data
public class EsDrpTurnoverItemRequest {
    /**
     * 单据生成时间
     */
    private String startCreateTime;

    /**
     * 单据生成时间
     */
    private String endCreateTime;

    /**
     * 批次号
     */
    private String batNoStr;

    /**
     * 关联批次号
     */
    private String relationBatNoStr;

    /**
     * SKU
     */
    private String skuStr;

    /**
     * 相关单号
     */
    private String orderNoStr;

    /**
     * 首仓批次
     */
    private String firstBatNoStr;

    /**
     * 海外首仓批次
     */
    private String abroadFirstBatNoStr;

    /**
     * 平台首仓批次
     */
    private String platFirstBatNoStr;

    /**
     * 首仓入库时间
     */
    private String startFirstCreateTime;

    /**
     * 首仓入库时间
     */
    private String endFirstCreateTime;

    /**
     * 海外首仓入库时间
     */
    private String startAbroadFirstCreateTime;

    /**
     * 海外首仓入库时间
     */
    private String endAbroadFirstCreateTime;

    /**
     * 平台/合约首仓入库时间
     */
    private String startPlatFirstCreateTime;

    /**
     * 平台/合约首仓入库时间
     */
    private String endPlatFirstCreateTime;

    private List<Long> ids;

    /**
     * 单据类型
     */
    private Integer orderType;

    /**
     * 入库类型
     */
    private Integer quantityType;

    /**
     * 店铺
     */
    private List<String> storeList;

    private String storeCode;

    /**
     * 批次号LIST
     */
    private List<String> batNoList;
}
