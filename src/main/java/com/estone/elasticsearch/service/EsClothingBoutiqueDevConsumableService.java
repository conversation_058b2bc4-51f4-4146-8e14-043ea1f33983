package com.estone.elasticsearch.service;

import com.estone.elasticsearch.model.ClothingBoutiqueDevConsumableEs;
import com.estone.exquisite.bean.ClothingBoutiqueDevConsumableQueryCondition;
import com.whq.tool.component.Pager;

import java.util.List;

public interface EsClothingBoutiqueDevConsumableService {

     List<ClothingBoutiqueDevConsumableEs> page(ClothingBoutiqueDevConsumableQueryCondition queryCondition, Pager pager);
}
