package com.estone.elasticsearch.service;

import java.util.List;

import com.estone.statistics.bean.WhDrpTurnoverItme;
import com.estone.statistics.bean.WhDrpTurnoverItmeQueryCondition;
import com.whq.tool.component.Pager;

public interface EsDrpTurnoverItemService {
    void doSyncItem(List<WhDrpTurnoverItme> itemList);

    List<WhDrpTurnoverItme> page(WhDrpTurnoverItmeQueryCondition queryCondition, Pager pager);

    void updateItem(WhDrpTurnoverItme entity);
}
