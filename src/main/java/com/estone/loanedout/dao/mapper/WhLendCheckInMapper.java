package com.estone.loanedout.dao.mapper;

import com.estone.loanedout.bean.WhLendCheckIn;
import java.sql.ResultSet;
import java.sql.SQLException;
import org.springframework.jdbc.core.RowMapper;

public class WhLendCheckInMapper implements Row<PERSON>apper<WhLendCheckIn> {

    public WhLendCheckIn mapRow(ResultSet rs, int rowNum) throws SQLException {
        WhLendCheckIn entity = new WhLendCheckIn();
        entity.setId(rs.getObject(WhLendCheckInDBField.ID) == null ? null : rs.getInt(WhLendCheckInDBField.ID));
        entity.setLendCheckOutCode(rs.getString(WhLendCheckInDBField.LEND_CHECK_OUT_CODE));
        entity.setSku(rs.getString(WhLendCheckInDBField.SKU));
        entity.setReturnQuantity(rs.getObject(WhLendCheckInDBField.RETURN_QUANTITY) == null ? null : rs.getInt(WhLendCheckInDBField.RETURN_QUANTITY));
        entity.setAcceptBy(rs.getString(WhLendCheckInDBField.ACCEPT_BY));
        entity.setAcceptByName(rs.getString(WhLendCheckInDBField.ACCEPT_BY_NAME));
        entity.setPlanReturnTime(rs.getTimestamp(WhLendCheckInDBField.PLAN_RETURN_TIME));
        entity.setCreatedBy(rs.getObject(WhLendCheckInDBField.CREATED_BY) == null ? null : rs.getInt(WhLendCheckInDBField.CREATED_BY));
        entity.setCreationDate(rs.getTimestamp(WhLendCheckInDBField.CREATION_DATE));
        entity.setName(rs.getString(WhLendCheckInDBField.NAME));
        entity.setStockId(rs.getObject(WhLendCheckInDBField.STOCK_ID) == null ? null : rs.getInt(WhLendCheckInDBField.STOCK_ID));
        return entity;
    }
}