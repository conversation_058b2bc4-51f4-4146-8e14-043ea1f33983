package com.estone.loanedout.dao.mapper;

import com.estone.loanedout.bean.WhLendUnique;
import java.sql.ResultSet;
import java.sql.SQLException;
import org.springframework.jdbc.core.RowMapper;

public class WhLendUniqueMapper implements RowMapper<WhLendUnique> {

    public WhLendUnique mapRow(ResultSet rs, int rowNum) throws SQLException {
        WhLendUnique entity = new WhLendUnique();
        entity.setId(rs.getObject(WhLendUniqueDBField.ID) == null ? null : rs.getInt(WhLendUniqueDBField.ID));
        entity.setLendNo(rs.getString(WhLendUniqueDBField.LEND_NO));
        entity.setSku(rs.getString(WhLendUniqueDBField.SKU));
        entity.setUniqueId(rs.getObject(WhLendUniqueDBField.UNIQUE_ID) == null ? null : rs.getInt(WhLendUniqueDBField.UNIQUE_ID));
        entity.setUniqueSku(rs.getString(WhLendUniqueDBField.UNIQUE_SKU));
        entity.setCreationDate(rs.getTimestamp(WhLendUniqueDBField.CREATION_DATE));
        entity.setLastUpdateDate(rs.getTimestamp(WhLendUniqueDBField.LAST_UPDATE_DATE));
        return entity;
    }
}