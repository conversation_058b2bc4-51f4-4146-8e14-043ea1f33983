package com.estone.loanedout.dao.mapper;

public interface WhLendDBField {
    String ID = "id";

    String LEND_CHECK_OUT_CODE = "lend_check_out_code";

    String SKU_TOTAL = "sku_total";

    String SKU_SPECIES = "sku_species";

    String SKU_TOTAL_AMOUNT = "sku_total_amount";

    String IS_NEED_RETURN = "is_need_return";

    String PLAN_RETURN_TIME = "plan_return_time";

    String ACCEPT_BY = "accept_by";

    String ACCEPT_BY_NAME = "accept_by_name";

    String STATUS = "status";

    String CREATED_BY = "created_by";

    String CREATION_DATE = "creation_date";

    String VERIFY_BY = "verify_by";

    String VERIFY_DATE = "verify_date";

    String LEND_REASON = "lend_reason";

    String VERIFY_REMARK = "verify_remark";

    String VERIFY_WMS_BY="verify_wms_by";

    String DELIVERY_TIME="delivery_time";

    String DELIVERY_BY="delivery_by";

    String SOURCE = "source";
}