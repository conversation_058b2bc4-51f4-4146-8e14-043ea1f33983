package com.estone.loanedout.dao.mapper;

public interface WhBadProductDBField {
    String ID = "id";

    String BAD_PRODUCT_CODE = "bad_product_code";

    String SKU_TOTAL = "sku_total";

    String SKU_SPECIES = "sku_species";

    String SKU_TOTAL_AMOUNT = "sku_total_amount";

    String STATUS = "status";

    String CREATED_BY = "created_by";

    String CREATION_DATE = "creation_date";

    String VERIFY_BY = "verify_by";

    String VERIFY_DATE = "verify_date";

    String REMARK = "remark";
}