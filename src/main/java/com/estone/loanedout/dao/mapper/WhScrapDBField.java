package com.estone.loanedout.dao.mapper;

public interface WhScrapDBField {
    String ID = "id";

    String SCRAP_NUMBER = "scrap_number";

    String SKU_NUMBER = "sku_number";

    String NUMBER = "number";

    String SCRAP_AMOUNT = "scrap_amount";

    String RECOVERY_AMOUNT = "recovery_amount";

    String STATUS = "status";

    String CREATION_USER = "creation_user";

    String CREATION_DATE = "creation_date";

    String REVIEW_USER = "review_user";

    String REVIEW_NAME = "review_name";

    String REVIEW_DATE = "review_date";

    String REMARK = "remark";
}