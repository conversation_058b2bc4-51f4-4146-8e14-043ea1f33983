package com.estone.loanedout.dao.impl;

import com.estone.loanedout.bean.WhLendItem;
import com.estone.loanedout.bean.WhLendItemQueryCondition;
import com.estone.loanedout.dao.WhLendItemDao;
import com.estone.loanedout.dao.mapper.WhLendItemDBField;
import com.estone.loanedout.dao.mapper.WhLendItemMapper;
import com.estone.loanedout.dao.mapper.WhScrapDetailDBField;
import com.whq.tool.component.Pager;
import com.whq.tool.context.DataContextHolder;
import com.whq.tool.sqler.SqlerRequest;
import com.whq.tool.sqler.analyzer.DataType;
import com.estone.common.util.SqlerTemplate;
import java.sql.Timestamp;
import java.util.List;
import org.springframework.stereotype.Repository;
import org.springframework.util.Assert;

@Repository("whLendItemDao")
public class WhLendItemDaoImpl implements WhLendItemDao {

    private void setQueryCondition(SqlerRequest request, WhLendItemQueryCondition query) {
        if (query == null) {
            return;
        }
        // 添加查询条件
        // 搜索条件不允许出现空字符
        request.setAllowBlank(false);
        
        request.addDataParam(WhLendItemDBField.ID, DataType.INT, query.getId());
        request.addDataParam(WhLendItemDBField.STOCK_ID, DataType.INT, query.getStockId());
        request.addDataParam(WhLendItemDBField.WH_LEND_ID, DataType.INT, query.getWhLendId());

        request.addDataParam("WhLendIdList",DataType.INT,query.getWhLendIdList());
    }

    @Override
    public int queryWhLendItemCount(WhLendItemQueryCondition query) {
        SqlerRequest request = new SqlerRequest("queryWhLendItemCount");
        setQueryCondition(request, query);
        return SqlerTemplate.queryForInt(request);
    }

    @Override
    public List<WhLendItem> queryWhLendItemList() {
        SqlerRequest request = new SqlerRequest("queryWhLendItemList");
        return SqlerTemplate.query(request, new WhLendItemMapper());
    }

    @Override
    public List<WhLendItem> queryWhLendItemList(WhLendItemQueryCondition query, Pager pager) {
        SqlerRequest request = new SqlerRequest("queryWhLendItemList");
        setQueryCondition(request, query);
        if(pager != null) {
            request.addFetch(pager.getPageNo(), pager.getPageSize());
        }
        return SqlerTemplate.query(request, new WhLendItemMapper());
    }

    @Override
    public WhLendItem queryWhLendItem(Integer primaryKey) {
        Assert.notNull(primaryKey, "primaryKey is null!");
        SqlerRequest request = new SqlerRequest("queryWhLendItemByPrimaryKey");
        request.addDataParam(WhLendItemDBField.ID, DataType.INT, primaryKey);
        return SqlerTemplate.queryForObject(request, new WhLendItemMapper());
    }

    @Override
    public WhLendItem queryWhLendItem(WhLendItemQueryCondition query) {
        SqlerRequest request = new SqlerRequest("queryWhLendItem");
        setQueryCondition(request, query);
        return SqlerTemplate.queryForObject(request, new WhLendItemMapper());
    }

    @Override
    public void createWhLendItem(WhLendItem entity) {
        SqlerRequest request = new SqlerRequest("createWhLendItem");
        request.addDataParam(WhLendItemDBField.WH_LEND_ID, DataType.INT, entity.getWhLendId());
        request.addDataParam(WhLendItemDBField.SKU, DataType.STRING, entity.getSku());
        request.addDataParam(WhLendItemDBField.QUANTITY, DataType.INT, entity.getQuantity());
        request.addDataParam(WhLendItemDBField.PRICE, DataType.DOUBLE, entity.getPrice());
        request.addDataParam(WhLendItemDBField.AMOUNT, DataType.DOUBLE, entity.getAmount());
        request.addDataParam(WhLendItemDBField.NAME, DataType.STRING, entity.getName());
        request.addDataParam(WhLendItemDBField.BATCH_NO_JSON, DataType.STRING, entity.getBatchNoJson());
        request.addDataParam(WhLendItemDBField.IS_EXP_SKU, DataType.INT, entity.getIsExpSku());
        request.addDataParam(WhLendItemDBField.STOCK_ID, DataType.INT, entity.getStockId());
        Integer autoIncrementId = SqlerTemplate.executeAndReturn(request);
        entity.setId(autoIncrementId);
    }

    @Override
    public void updateWhLendItem(WhLendItem entity) {
        SqlerRequest request = new SqlerRequest("updateWhLendItemByPrimaryKey");
        request.addDataParam(WhLendItemDBField.ID, DataType.INT, entity.getId());
        
        request.addDataParam(WhLendItemDBField.WH_LEND_ID, DataType.INT, entity.getWhLendId());
        request.addDataParam(WhLendItemDBField.SKU, DataType.STRING, entity.getSku());
        request.addDataParam(WhLendItemDBField.QUANTITY, DataType.INT, entity.getQuantity());
        request.addDataParam(WhLendItemDBField.PICK_QUANTITY, DataType.INT, entity.getPickQuantity());
        request.addDataParam(WhLendItemDBField.PRICE, DataType.DOUBLE, entity.getPrice());
        request.addDataParam(WhLendItemDBField.AMOUNT, DataType.DOUBLE, entity.getAmount());
        request.addDataParam(WhLendItemDBField.NAME, DataType.STRING, entity.getName());
        request.addBatchDataParam(WhLendItemDBField.STATUS, DataType.INT, entity.getStatus());
        request.addBatchDataParam(WhLendItemDBField.STOCK_ID, DataType.INT, entity.getStockId());
        SqlerTemplate.execute(request);
    }

    @Override
    public void batchCreateWhLendItem(List<WhLendItem> entityList) {
        if (entityList != null && !entityList.isEmpty()) {
            SqlerRequest request = new SqlerRequest("createWhLendItem");
            for (WhLendItem entity : entityList) {
                request.addBatchDataParam(WhLendItemDBField.WH_LEND_ID, DataType.INT, entity.getWhLendId());
                request.addBatchDataParam(WhLendItemDBField.SKU, DataType.STRING, entity.getSku());
                request.addBatchDataParam(WhLendItemDBField.QUANTITY, DataType.INT, entity.getQuantity());
                request.addBatchDataParam(WhLendItemDBField.PRICE, DataType.DOUBLE, entity.getPrice());
                request.addBatchDataParam(WhLendItemDBField.AMOUNT, DataType.DOUBLE, entity.getAmount());
                request.addBatchDataParam(WhLendItemDBField.NAME, DataType.STRING, entity.getName());
                request.addBatchDataParam(WhLendItemDBField.BATCH_NO_JSON, DataType.STRING, entity.getBatchNoJson());
                request.addBatchDataParam(WhLendItemDBField.IS_EXP_SKU, DataType.INT, entity.getIsExpSku());
                request.addBatchDataParam(WhLendItemDBField.STOCK_ID, DataType.INT, entity.getStockId());
                request.addBatch();
            }
            SqlerTemplate.batchUpdate(request);
        }
    }

    @Override
    public void batchUpdateWhLendItem(List<WhLendItem> entityList) {
        if (entityList != null && !entityList.isEmpty()) {
            SqlerRequest request = new SqlerRequest("updateWhLendItemByPrimaryKey");
            for (WhLendItem entity : entityList) {
                request.addBatchDataParam(WhLendItemDBField.ID, DataType.INT, entity.getId());
                
                request.addBatchDataParam(WhLendItemDBField.WH_LEND_ID, DataType.INT, entity.getWhLendId());
                request.addBatchDataParam(WhLendItemDBField.SKU, DataType.STRING, entity.getSku());
                request.addBatchDataParam(WhLendItemDBField.QUANTITY, DataType.INT, entity.getQuantity());
                request.addBatchDataParam(WhLendItemDBField.PICK_QUANTITY, DataType.INT, entity.getPickQuantity());
                request.addBatchDataParam(WhLendItemDBField.PRICE, DataType.DOUBLE, entity.getPrice());
                request.addBatchDataParam(WhLendItemDBField.AMOUNT, DataType.DOUBLE, entity.getAmount());
                request.addBatchDataParam(WhLendItemDBField.NAME, DataType.STRING, entity.getName());
                request.addBatchDataParam(WhLendItemDBField.STATUS, DataType.INT, entity.getStatus());
                request.addBatchDataParam(WhLendItemDBField.BATCH_NO_JSON, DataType.STRING, entity.getBatchNoJson());
                request.addBatchDataParam(WhLendItemDBField.STOCK_ID, DataType.INT, entity.getStockId());
                request.addBatch();
            }
            SqlerTemplate.batchUpdate(request);
        }
    }

    @Override
    public void deleteWhLendItem(Integer primaryKey) {
        SqlerRequest request = new SqlerRequest("deleteWhLendItemByPrimaryKey");
        request.addDataParam(WhLendItemDBField.ID, DataType.INT, primaryKey);
        SqlerTemplate.execute(request);
    }
}