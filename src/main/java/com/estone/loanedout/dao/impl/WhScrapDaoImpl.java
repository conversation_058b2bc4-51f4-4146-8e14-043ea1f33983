package com.estone.loanedout.dao.impl;

import com.estone.common.util.CommonUtils;
import com.estone.loanedout.bean.WhScrap;
import com.estone.loanedout.bean.WhScrapQueryCondition;
import com.estone.loanedout.dao.WhScrapDao;
import com.estone.loanedout.dao.mapper.*;
import com.whq.tool.component.Pager;
import com.whq.tool.context.DataContextHolder;
import com.whq.tool.sqler.SqlerRequest;
import com.whq.tool.sqler.analyzer.DataType;
import com.whq.tool.sqler.dialect.DialectFactory;
import com.whq.tool.sqler.dialect.SQLDialect;
import com.estone.common.util.SqlerTemplate;
import java.sql.Timestamp;
import java.util.List;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Repository;
import org.springframework.util.Assert;

@Repository("whScrapDao")
public class WhScrapDaoImpl implements WhScrapDao {

    private void setQueryCondition(SqlerRequest request, WhScrapQueryCondition query) {
        if (query == null) {
            return;
        }
        // 添加查询条件
        // 搜索条件不允许出现空字符
        request.setAllowBlank(false);
        
        request.addDataParam(WhScrapDBField.ID, DataType.INT, query.getId());

        if (CollectionUtils.isNotEmpty(query.getIds())){
            request.addDataParam("ids", DataType.STRING,query.getIds());
        }
        // SKU
        if (StringUtils.isNotBlank(query.getSku())) {
            if (query.getSku().contains(",")) {
                request.addDataParam("skuList", DataType.STRING, CommonUtils.splitList(query.getSku().trim(), ","));
            } else {
                request.addDataParam("sku", DataType.STRING, query.getSku().trim());
            }
        }
        if (query.getIsExpSku() != null){
            request.addDataParam("is_exp_sku", DataType.INT, query.getIsExpSku());
        }

        if (CollectionUtils.isNotEmpty(query.getStockIdList()))
            request.addDataParam("stockIdList", DataType.INT, query.getStockIdList());

        if (query.getStockId() != null)
            request.addDataParam(WhScrapDetailDBField.STOCK_ID, DataType.INT, query.getStockId());

        if (StringUtils.isNotBlank(query.getStockIdStr())) {
            if (query.getStockIdStr().contains(",")) {
                request.addDataParam("stockIdList", DataType.INT,
                        CommonUtils.splitList(query.getStockIdStr().trim(), ","));
            }
            else {
                request.addDataParam(WhScrapDetailDBField.STOCK_ID, DataType.INT, query.getStockIdStr().trim());
            }
        }

        // 状态
        if (query.getStatus() != null) {
            request.addDataParam(WhLendDBField.STATUS, DataType.INT, query.getStatus());
        }

        // 报废单号
        if (StringUtils.isNotBlank(query.getScrapNumber())) {
            if (query.getScrapNumber().contains(",")) {
                request.addDataParam("scrapNumberList", DataType.STRING, CommonUtils.splitList(query.getScrapNumber().trim(), ","));
            } else {
                request.addDataParam(WhScrapDBField.SCRAP_NUMBER, DataType.STRING, query.getScrapNumber().trim());
            }
        }

        request.addDataParam("from_creation_date", DataType.STRING, query.getFromCreationDate());
        request.addDataParam("to_creation_date", DataType.STRING, query.getToCreationDate());

        request.addDataParam("from_verify_time", DataType.STRING, query.getFromVerifyTime());
        request.addDataParam("to_verify_time", DataType.STRING, query.getToVerifyTime());
    }

    @Override
    public int queryWhScrapCount(WhScrapQueryCondition query) {
        SqlerRequest request = new SqlerRequest("queryWhScrapCount");
        setQueryCondition(request, query);
        return SqlerTemplate.queryForInt(request);
    }

    @Override
    public List<WhScrap> queryWhScrapList() {
        SqlerRequest request = new SqlerRequest("queryWhScrapList");
        return SqlerTemplate.query(request, new WhScrapMapper());
    }

    @Override
    public List<WhScrap> queryWhScrapList(WhScrapQueryCondition query, Pager pager) {
        SqlerRequest request = new SqlerRequest("queryWhScrapList");
        setQueryCondition(request, query);
        if(pager != null) {
            request.addFetch(pager.getPageNo(), pager.getPageSize());
        }
        return SqlerTemplate.query(request, new WhScrapMapper());
    }

    @Override
    public WhScrap queryWhScrap(Integer primaryKey) {
        Assert.notNull(primaryKey, "primaryKey is null!");
        SqlerRequest request = new SqlerRequest("queryWhScrapByPrimaryKey");
        request.addDataParam(WhScrapDBField.ID, DataType.INT, primaryKey);
        return SqlerTemplate.queryForObject(request, new WhScrapMapper());
    }

    @Override
    public WhScrap queryWhScrap(WhScrapQueryCondition query) {
        SqlerRequest request = new SqlerRequest("queryWhScrap");
        setQueryCondition(request, query);
        return SqlerTemplate.queryForObject(request, new WhScrapMapper());
    }

    @Override
    public void createWhScrap(WhScrap entity) {
        Timestamp timestamp = new Timestamp(System.currentTimeMillis());
        SqlerRequest request = new SqlerRequest("createWhScrap");
        request.addDataParam(WhScrapDBField.SCRAP_NUMBER, DataType.STRING, entity.getScrapNumber());
        request.addDataParam(WhScrapDBField.SKU_NUMBER, DataType.INT, entity.getSkuNumber());
        request.addDataParam(WhScrapDBField.NUMBER, DataType.INT, entity.getNumber());
        request.addDataParam(WhScrapDBField.SCRAP_AMOUNT, DataType.DOUBLE, entity.getScrapAmount());
        request.addDataParam(WhScrapDBField.RECOVERY_AMOUNT, DataType.DOUBLE, entity.getRecoveryAmount());
        request.addDataParam(WhScrapDBField.STATUS, DataType.INT, entity.getStatus());
        request.addDataParam(WhScrapDBField.CREATION_USER, DataType.INT, entity.getCreationUser() == null ? DataContextHolder.getUserId() : entity.getCreationUser());
        request.addDataParam(WhScrapDBField.CREATION_DATE, DataType.TIMESTAMP, timestamp);
        request.addDataParam(WhScrapDBField.REVIEW_USER, DataType.INT, entity.getReviewUser());
        request.addDataParam(WhScrapDBField.REVIEW_NAME, DataType.STRING, entity.getReviewName());
        request.addDataParam(WhScrapDBField.REVIEW_DATE, DataType.TIMESTAMP, entity.getReviewDate());
        request.addDataParam(WhScrapDBField.REMARK, DataType.STRING, entity.getRemark());
        Integer autoIncrementId = SqlerTemplate.executeAndReturn(request);
        entity.setId(autoIncrementId);
        entity.setCreationDate(timestamp);
    }

    @Override
    public void updateWhScrap(WhScrap entity) {
        SqlerRequest request = new SqlerRequest("updateWhScrapByPrimaryKey");
        request.addDataParam(WhScrapDBField.ID, DataType.INT, entity.getId());
        
        request.addDataParam(WhScrapDBField.SCRAP_NUMBER, DataType.STRING, entity.getScrapNumber());
        request.addDataParam(WhScrapDBField.SKU_NUMBER, DataType.INT, entity.getSkuNumber());
        request.addDataParam(WhScrapDBField.NUMBER, DataType.INT, entity.getNumber());
        request.addDataParam(WhScrapDBField.SCRAP_AMOUNT, DataType.DOUBLE, entity.getScrapAmount());
        request.addDataParam(WhScrapDBField.RECOVERY_AMOUNT, DataType.DOUBLE, entity.getRecoveryAmount());
        request.addDataParam(WhScrapDBField.STATUS, DataType.INT, entity.getStatus());
        request.addDataParam(WhScrapDBField.CREATION_USER, DataType.INT, entity.getCreationUser());
        
        request.addDataParam(WhScrapDBField.REVIEW_USER, DataType.INT, entity.getReviewUser());
        request.addDataParam(WhScrapDBField.REVIEW_NAME, DataType.STRING, entity.getReviewName());
        request.addDataParam(WhScrapDBField.REVIEW_DATE, DataType.TIMESTAMP, entity.getReviewDate());
        request.addDataParam(WhScrapDBField.REMARK, DataType.STRING, entity.getRemark());
        SqlerTemplate.execute(request);
    }

    @Override
    public int queryWhAsnsAndItemCount(WhScrapQueryCondition query) {
        SqlerRequest request = new SqlerRequest("queryWhScrapAndItemCount");
        setQueryCondition(request, query);
        return SqlerTemplate.queryForInt(request);
    }

    @Override
    public List<WhScrap> queryWhScrapAndItems(WhScrapQueryCondition query, Pager pager) {
        SqlerRequest request = new SqlerRequest("queryWhScrapAndItems");
        setQueryCondition(request, query);
        if(pager != null) {
            //request.addFetch(pager.getPageNo(), pager.getPageSize());
            SQLDialect dial = DialectFactory.createDialect(null);

            long start = (long) (pager.getPageNo() - 1) * pager.getPageSize();

            long end = start + pager.getPageSize() - 1L;

            request.addSqlDataParam("LIMIT", dial.queryFirst2Last("", (int) start, (int) end));
        }
        return SqlerTemplate.query(request, new WhScrapMapper(true));
    }

    @Override
    public void batchCreateWhScrap(List<WhScrap> entityList) {
        if (entityList != null && !entityList.isEmpty()) {
            SqlerRequest request = new SqlerRequest("createWhScrap");
            for (WhScrap entity : entityList) {
                request.addBatchDataParam(WhScrapDBField.SCRAP_NUMBER, DataType.STRING, entity.getScrapNumber());
                request.addBatchDataParam(WhScrapDBField.SKU_NUMBER, DataType.INT, entity.getSkuNumber());
                request.addBatchDataParam(WhScrapDBField.NUMBER, DataType.INT, entity.getNumber());
                request.addBatchDataParam(WhScrapDBField.SCRAP_AMOUNT, DataType.DOUBLE, entity.getScrapAmount());
                request.addBatchDataParam(WhScrapDBField.RECOVERY_AMOUNT, DataType.DOUBLE, entity.getRecoveryAmount());
                request.addBatchDataParam(WhScrapDBField.STATUS, DataType.INT, entity.getStatus());
                request.addBatchDataParam(WhScrapDBField.CREATION_USER, DataType.INT, entity.getCreationUser() == null ? DataContextHolder.getUserId() : entity.getCreationUser());
                request.addBatchDataParam(WhScrapDBField.CREATION_DATE, DataType.TIMESTAMP, new Timestamp(System.currentTimeMillis()));
                request.addBatchDataParam(WhScrapDBField.REVIEW_USER, DataType.INT, entity.getReviewUser());
                request.addBatchDataParam(WhScrapDBField.REVIEW_NAME, DataType.STRING, entity.getReviewName());
                request.addBatchDataParam(WhScrapDBField.REVIEW_DATE, DataType.TIMESTAMP, entity.getReviewDate());
                request.addBatchDataParam(WhScrapDBField.REMARK, DataType.STRING, entity.getRemark());
                request.addBatch();
            }
            SqlerTemplate.batchUpdate(request);
        }
    }

    @Override
    public void batchUpdateWhScrap(List<WhScrap> entityList) {
        if (entityList != null && !entityList.isEmpty()) {
            SqlerRequest request = new SqlerRequest("updateWhScrapByPrimaryKey");
            for (WhScrap entity : entityList) {
                request.addBatchDataParam(WhScrapDBField.ID, DataType.INT, entity.getId());
                
                request.addBatchDataParam(WhScrapDBField.SCRAP_NUMBER, DataType.STRING, entity.getScrapNumber());
                request.addBatchDataParam(WhScrapDBField.SKU_NUMBER, DataType.INT, entity.getSkuNumber());
                request.addBatchDataParam(WhScrapDBField.NUMBER, DataType.INT, entity.getNumber());
                request.addBatchDataParam(WhScrapDBField.SCRAP_AMOUNT, DataType.DOUBLE, entity.getScrapAmount());
                request.addBatchDataParam(WhScrapDBField.RECOVERY_AMOUNT, DataType.DOUBLE, entity.getRecoveryAmount());
                request.addBatchDataParam(WhScrapDBField.STATUS, DataType.INT, entity.getStatus());
                request.addBatchDataParam(WhScrapDBField.CREATION_USER, DataType.INT, entity.getCreationUser());
                
                request.addBatchDataParam(WhScrapDBField.REVIEW_USER, DataType.INT, entity.getReviewUser());
                request.addBatchDataParam(WhScrapDBField.REVIEW_NAME, DataType.STRING, entity.getReviewName());
                request.addBatchDataParam(WhScrapDBField.REVIEW_DATE, DataType.TIMESTAMP, entity.getReviewDate());
                request.addBatchDataParam(WhScrapDBField.REMARK, DataType.STRING, entity.getRemark());
                request.addBatch();
            }
            SqlerTemplate.batchUpdate(request);
        }
    }

    @Override
    public void deleteWhScrap(Integer primaryKey) {
        SqlerRequest request = new SqlerRequest("deleteWhScrapByPrimaryKey");
        request.addDataParam(WhScrapDBField.ID, DataType.INT, primaryKey);
        SqlerTemplate.execute(request);
    }
}