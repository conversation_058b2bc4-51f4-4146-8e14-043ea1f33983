package com.estone.loanedout.action;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.estone.common.SelectJson;
import com.estone.common.enums.ExportType;
import com.estone.common.util.CompatibleSkuUtils;
import com.estone.common.util.DateUtils;
import com.estone.common.util.POIUtils;
import com.estone.common.util.RedissonLockUtil;
import com.estone.loanedout.bean.*;
import com.estone.loanedout.domain.WhLendReturnDo;
import com.estone.loanedout.enums.LendReturnStatus;
import com.estone.loanedout.enums.LendStatus;
import com.estone.loanedout.service.WhLendCheckInService;
import com.estone.loanedout.service.WhLendDestroyingService;
import com.estone.loanedout.service.WhLendReturnService;
import com.estone.loanedout.service.WhLendService;
import com.estone.sku.bean.ExpManageItem;
import com.estone.sku.service.LocationMoveInfoService;
import com.whq.tool.action.BaseController;
import com.whq.tool.component.Pager;
import com.whq.tool.component.StatusCode;
import com.whq.tool.json.ResponseJson;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.OutputStream;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Controller
@RequestMapping(value = "whLendReturn")
@Slf4j
public class WhLendReturnController extends BaseController {
    @Resource
    private WhLendReturnService whLendReturnService;

    @Resource
    private WhLendService whLendService;

    @Resource
    private WhLendCheckInService whLendCheckInService;

    @Resource
    private WhLendDestroyingService whLendDestroyingService;

    @Resource
    private LocationMoveInfoService locationMoveInfoService;

    @RequestMapping(method = {RequestMethod.GET})
    public String init(@ModelAttribute("domain") WhLendReturnDo domain) {
        initFormData(domain);
//        queryWhLendReturns(domain);
        return "loanedout/whLendReturnList";
    }

    private void initFormData(@ModelAttribute("domain") WhLendReturnDo domain) {
        domain.setStatusJson(SelectJson.getList(LendReturnStatus.values()));
    }

    private void queryWhLendReturns(@ModelAttribute("domain") WhLendReturnDo domain) {
        WhLendReturnQueryCondition query = domain.getQuery();
        Pager page = domain.getPage();
        if (query == null)
        {
            query = new WhLendReturnQueryCondition();
            domain.setQuery(query);
        }
        List<WhLendReturn> whLendReturns = whLendReturnService.queryWhLendReturnList(query, null);

        if (query.getReturnStatus()!=null && CollectionUtils.isNotEmpty(whLendReturns)) {
            List<String> collect = whLendReturns.stream().map(a -> a.getLendCheckOutCode()).collect(Collectors.toList());
            String join = String.join(",", collect);
            query=new WhLendReturnQueryCondition();
            query.setLendCheckOutCode(join);
            whLendReturns = whLendReturnService.queryWhLendReturnList(query, null);
        }

        //组装数据多件包装异常查询
        List<WhLendReturn> WhLendReturnList = whLendReturnService.assembleData(whLendReturns);

        int pageNo = page.getPageNo();
        int pageSize = page.getPageSize();
        List<WhLendReturn> subList = WhLendReturnList.stream().skip((pageNo-1)*pageSize).limit(pageSize).
                collect(Collectors.toList());
        page.setTotalCount(WhLendReturnList.size());
        domain.setWhLendReturns(subList);
    }

    @RequestMapping(value="search", method = {RequestMethod.POST})
    public String search(@ModelAttribute("domain") WhLendReturnDo domain) {
        initFormData(domain);
        queryWhLendReturns(domain);
        return "loanedout/whLendReturnList";
    }

    @RequestMapping(value = "getWhLendReturnList", method = { RequestMethod.GET })
    public String getWhLendReturnList(@ModelAttribute("domain") WhLendReturnDo domain) {
        WhLendReturnQueryCondition query = domain.getQuery();
        if (query == null) {
            query = new WhLendReturnQueryCondition();
        }
        if (StringUtils.isBlank( query.getLendCheckOutCode())) {
            return "loanedout/whLendReturnAndItemList";
        }
        WhLendReturnQueryCondition whLendReturnQueryCondition=new WhLendReturnQueryCondition();
        whLendReturnQueryCondition.setLendCheckOutCode(query.getLendCheckOutCode());
        List<WhLendReturn> whLendReturns = whLendReturnService.queryWhLendReturnList(query, null);
        domain.setWhLendReturns(whLendReturns);
        return "loanedout/whLendReturnAndItemList";
    }


    @RequestMapping(value="create", method = {RequestMethod.GET})
    public String toCreateWhLendReturn(@ModelAttribute("domain") WhLendReturnDo domain) {
        initFormData(domain);
        queryWhLendReturns(domain);
        return "loanedout/whLendReturnList";
    }

    @RequestMapping(value="create", method = {RequestMethod.POST})
    public String createWhLendReturn(@ModelAttribute("domain") WhLendReturnDo domain) {
        WhLendReturn whLendReturn = domain.getWhLendReturn();
        whLendReturnService.createWhLendReturn(whLendReturn);
        return "redirect:/{查询列表}";
    }

    @RequestMapping(value="update", method = {RequestMethod.GET})
    public String toUpdateWhLendReturn(@ModelAttribute("domain") WhLendReturnDo domain, @RequestParam("whLendReturnId") Integer whLendReturnId) {
        WhLendReturn whLendReturn = whLendReturnService.getWhLendReturn(whLendReturnId);
        domain.setWhLendReturn(whLendReturn);
        return "{模块}/{页面}";
    }

    @RequestMapping(value="update", method = {RequestMethod.POST})
    public String updateWhLendReturn(@ModelAttribute("domain") WhLendReturnDo domain) {
        WhLendReturn whLendReturn = domain.getWhLendReturn();
        whLendReturnService.updateWhLendReturn(whLendReturn);
        return "redirect:/{查询列表}";
    }

    @RequestMapping(value="delete", method = {RequestMethod.GET})
    @ResponseBody
    public ResponseJson deleteWhLendReturn(@ModelAttribute("domain") WhLendReturnDo domain, @RequestParam("whLendReturnId") Integer whLendReturnId) {
        ResponseJson response = new ResponseJson();
        whLendReturnService.deleteWhLendReturn(whLendReturnId);
        return response;
    }

    @RequestMapping(value="returnCheckIn", method = {RequestMethod.POST})
    @ResponseBody
    public ResponseJson returnCheckIn(@RequestParam("whLendReturnItems") String whLendReturnItems) {
        ResponseJson response = new ResponseJson(StatusCode.FAIL);

        response.setStatus(StatusCode.FAIL);
        if (StringUtils.isBlank(whLendReturnItems) ) {
            response.setMessage("参数为空！");
            return response;
        }
        List<WhLendReturn> whLendReturnList = JSONArray.parseArray(whLendReturnItems).toJavaList(WhLendReturn.class);
        if (CollectionUtils.isEmpty(whLendReturnList) || StringUtils.isBlank(whLendReturnList.get(0).getLendCheckOutCode())){
            response.setMessage("参数为空！");
            return response;
        }
        String lendCheckOutCode = whLendReturnList.get(0).getLendCheckOutCode();
        Map<Integer, Integer> map = whLendReturnList.stream().collect(Collectors.toMap(WhLendReturn::getId, WhLendReturn::getReturnQuantity));


        // 校验归还时间是否超过保质期
        WhLendQueryCondition query = new WhLendQueryCondition();
        query.setLendCheckOutCode(lendCheckOutCode);
        List<WhLend> whLends = whLendService.queryWhLendAndItems(query, null);
        if (CollectionUtils.isEmpty(whLends)){
            response.setMessage("外借单【"+lendCheckOutCode+"】不存在！");
            return response;
        }
        if (!LendStatus.ACKNOWLEDGED.equals(whLends.get(0).getStatus())){
            response.setMessage("外借单【"+lendCheckOutCode+"】未确认出库！");
            return response;
        }

        Map<String,String> batchNoMap=new HashMap<>();
        List<WhLendItem> items = whLends.get(0).getItems();
        Date today = new Date();
        for(WhLendItem item:items){
            if(StringUtils.isBlank(item.getBatchNoJson())) continue;
            List<ExpManageItem> expManageItems = JSONObject.parseArray(item.getBatchNoJson(), ExpManageItem.class);
            for(ExpManageItem e:expManageItems){
                if(org.apache.commons.lang3.StringUtils.equalsIgnoreCase("notBatch",e.getBatchNo())) continue;
                String expDateStr = e.getExpDateStr();
                if(StringUtils.isBlank(expDateStr)) continue;
                Integer daysBetween = DateUtils.getHoursBetween(today, DateUtils.stringToDate(expDateStr, DateUtils.DEFAULT_FORMAT));
                if(daysBetween<=0){
                    batchNoMap.put(item.getSku(),e.getBatchNo());
                }
            }
        }
        WhLendReturnQueryCondition queryCondition = new WhLendReturnQueryCondition();
        queryCondition.setLendCheckOutCode(lendCheckOutCode);
        List<WhLendReturn> whLendReturns = whLendReturnService.queryWhLendReturnList(queryCondition, null);
        if (CollectionUtils.isEmpty(whLendReturns)){
            response.setMessage("对应外借单归还管理数据不存在！");
            return response;
        }

        WhLendDestroyingQueryCondition destroyingQuery = new WhLendDestroyingQueryCondition();
        destroyingQuery.setLendCheckOutCode(lendCheckOutCode);
        List<WhLendDestroying> lendDestroyingList = whLendDestroyingService.queryWhLendDestroyings(destroyingQuery, null);
        Map<String, WhLendDestroying> destroyingMap = Optional.ofNullable(lendDestroyingList).orElse(new ArrayList<>())
                .stream().collect(Collectors.toMap(d -> d.getLendCheckOutCode() + d.getSku(), d -> d));

        List<String> failedList = new ArrayList<>();
        int minId =whLendReturns.stream().filter(a -> a.getId() != null).mapToInt(WhLendReturn::getId).min().getAsInt();
        //统计归还数量为零的sku
        int zero=0;
        // 获取虚拟库位
        List<String> virtualLocationList = locationMoveInfoService.getVirtualLocationList();
        Map<Integer, List<WhLendCheckIn>> whLendCheckInMap = new HashMap<>();
        for (WhLendReturn whLendReturn : whLendReturns) {
            if (batchNoMap.containsKey(whLendReturn.getSku())) {
                failedList.add(String.format("sku["+ whLendReturn.getSku()+"]对应批次["+batchNoMap.get(whLendReturn.getSku())+"]已过期，请核销处理！"));
                continue;
            }
            try {
                Integer returnQuantity = Optional.ofNullable(map.get(whLendReturn.getId())).orElse(0);
                if (returnQuantity == 0) {
                    zero++;
                    continue;
                }
                WhLend whLend = whLends.get(0);
                List<WhLendItem> whLendItems =whLend.getItems().stream().filter(whLendItem -> whLendReturn.getSku().equals(whLendItem.getSku())).collect(Collectors.toList());
                whLend.setItems(whLendItems);
                WhLendCheckIn whLendCheckIn = whLendReturnService.doReturnCheckIn(Arrays.asList(whLendReturn.getSku()), whLendReturn, returnQuantity,
                        whLend, minId, destroyingMap,virtualLocationList);
                if (whLendCheckIn != null && Objects.equals("PMS",whLendReturn.getSource().toUpperCase())){
                    List<WhLendCheckIn> whLendCheckIns = whLendCheckInMap.computeIfAbsent(minId, v->new ArrayList<>());
                    whLendCheckIns.add(whLendCheckIn);
                }
            }
            catch (Exception e) {
                failedList.add(String.format("%s%s", whLendReturn.getId(), e.getMessage()));
                log.error(e.getMessage(), e);
            }
        }
        whLendReturnService.updateReturnState(lendCheckOutCode);
        whLendCheckInService.pushPmsWhLendCheckIns(whLendCheckInMap);
        response.setMessage(String.format("成功[%s]条，失败[%s]条，失败详情【%s】", whLendReturns.size() - failedList.size()-zero, failedList.size(), StringUtils.join(failedList, ";")));
        response.setStatus(StatusCode.SUCCESS);
        return response;
    }
    // 核销
    @RequestMapping(value = "updateDestroying", method = {RequestMethod.POST})
    @ResponseBody
    public ResponseJson updateDestroying(@RequestParam("whLendReturnItemsStr") String whLendReturnItemsStr) {
        ResponseJson response = new ResponseJson();
        response.setStatus(StatusCode.FAIL);
        if (StringUtils.isEmpty(whLendReturnItemsStr) ) {
            response.setMessage("参数为空！");
            return response;
        }
        List<WhLendReturn> whLendReturnList = JSONArray.parseArray(whLendReturnItemsStr).toJavaList(WhLendReturn.class);
        if (CollectionUtils.isEmpty(whLendReturnList) || StringUtils.isBlank(whLendReturnList.get(0).getLendCheckOutCode())){
            response.setMessage("参数为空！");
            return response;
        }
        String key = "CREATE_DESTROYING:" + whLendReturnList.get(0).getLendCheckOutCode();
        try {
            if(RedissonLockUtil.tryLock(key, TimeUnit.SECONDS, 5, 300)){
                Map<Integer, Integer> map = whLendReturnList.stream().collect(Collectors.toMap(WhLendReturn::getId, WhLendReturn::getDestroyQuantity));
                List<String> failedList = new ArrayList<>();
                WhLendReturnQueryCondition query = new WhLendReturnQueryCondition();
                query.setLendCheckOutCode(whLendReturnList.get(0).getLendCheckOutCode());
                List<WhLendReturn> whLendReturns = whLendReturnService.queryWhLendReturnList(query, null);
                int minId =whLendReturns.stream().filter(a -> a.getId() != null).mapToInt(WhLendReturn::getId).min().getAsInt();
                //统计归还数量为零的sku
                int zero=0;
                for (WhLendReturn whLendReturn : whLendReturns) {
                    if (whLendReturn == null || map==null) {
                        failedList.add(String.format("[%s]不存在", whLendReturn.getId()));
                        continue;
                    }
                    try {
                        Integer destroyQuantity = Optional.ofNullable(map.get(whLendReturn.getId())).orElse(0);
                        if (destroyQuantity==0){
                            zero++;
                            continue;
                        }
                        whLendReturnService.doDestroying(whLendReturn,minId,destroyQuantity,null);
                    } catch (Exception e) {
                        failedList.add(String.format("%s%s", whLendReturn.getId(), e.getMessage()));
                        log.error(e.getMessage(), e);
                    }
                }
                whLendReturnService.updateReturnState(whLendReturnList.get(0).getLendCheckOutCode());
                // 存在失败的项
                response.setMessage(String.format("成功[%s]条，失败[%s]条，失败详情【%s】", whLendReturns.size() - failedList.size()-zero, failedList.size(), StringUtils.join(failedList, ";")));
            }else{
                response.setMessage("获取创建核销单锁失败");
                return response;
            }
        }finally {
            RedissonLockUtil.unlock(key);
        }
        response.setStatus(StatusCode.SUCCESS);
        return response;
    }



    @RequestMapping(value = "queryDestroying", method = {RequestMethod.GET})
    @ResponseBody
    public ResponseJson queryDestroying(@RequestParam("ids") List<Integer> ids) {
        ResponseJson response = new ResponseJson(StatusCode.FAIL);
        if (CollectionUtils.isEmpty(ids) ) {
            response.setMessage("参数为空！");
            return response;
        }
        WhLendReturnQueryCondition queryCondition=new WhLendReturnQueryCondition();
        queryCondition.setId(ids.get(0));
        WhLendReturn whLendReturn = whLendReturnService.queryWhLendReturn(queryCondition);
        if (whLendReturn==null || StringUtils.isBlank( whLendReturn.getLendCheckOutCode())){
            response.setMessage("无数据！");
            return response;
        }
        if (Objects.equals("PMS", whLendReturn.getSource())){
            response.setMessage("来源为PMS，无法进行转核销操作！");
            return response;
        }
        queryCondition.setId(null);
        queryCondition.setLendCheckOutCode(whLendReturn.getLendCheckOutCode());
        List<WhLendReturn> whLendReturns = whLendReturnService.queryWhLendReturnList(queryCondition, null);
        //根据待归还数量进行从多到少进行排序
        whLendReturns = Optional.ofNullable(whLendReturns)
                .orElse(new ArrayList<>())
                .stream()
                .sorted(Comparator.comparing(WhLendReturn::getNoReturnQuantity).reversed())
                .collect(Collectors.toList());
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("whLendReturns", whLendReturns);
        response.setBody(map);
        response.setStatus(StatusCode.SUCCESS);
        return response;
    }

    @RequestMapping(value = "queryWhLendReturn", method = {RequestMethod.GET})
    @ResponseBody
    public ResponseJson queryWhLendReturn(@RequestParam("orderNo") String orderNo) {
        ResponseJson response = new ResponseJson(StatusCode.FAIL);
        if (StringUtils.isBlank(orderNo) ) {
            response.setMessage("参数为空！");
            return response;
        }
        WhLendReturnQueryCondition queryCondition=new WhLendReturnQueryCondition();
        queryCondition.setLendCheckOutCode(orderNo);
        List<WhLendReturn> whLendReturns = whLendReturnService.queryWhLendReturnList(queryCondition, null);
        //根据待归还数量进行从多到少进行排序
        whLendReturns = Optional.ofNullable(whLendReturns)
                .orElse(new ArrayList<>())
                .stream()
                .sorted(Comparator.comparing(WhLendReturn::getNoReturnQuantity).reversed())
                .collect(Collectors.toList());
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("whLendReturns", whLendReturns);
        response.setBody(map);
        response.setStatus(StatusCode.SUCCESS);
        return response;
    }

    @RequestMapping(value = "updateReturnState", method = {RequestMethod.POST})
    @ResponseBody
    public ResponseJson updateReturnState(@RequestBody List<String> lendCheckOutCodeList) {
        ResponseJson response = new ResponseJson(StatusCode.FAIL);
        for (String lendCheckOutCode : lendCheckOutCodeList) {
            whLendReturnService.updateReturnState(lendCheckOutCode);
        }
        response.setStatus(StatusCode.SUCCESS);
        return response;
    }


    private static String[] HEADERS = { "关联出库单号", "sku数量",  "件数", "外借数量", "拣货数量", "已归还数量", "核销数量","未归还数量", "状态", "接收人", "计划归还时间"};

    /**
     * 导出
     *
     * @param domain
     * @param exportType
     * @param ids
     * @param response
     */
    @SuppressWarnings("incomplete-switch")
    @RequestMapping(value = "download", method = { RequestMethod.POST })
    @ResponseBody
    public void download(@ModelAttribute("domain") WhLendReturnDo domain,
                         @RequestParam("exportType") String exportType,
                         @RequestParam(value = "ids", required = false) List<Integer> ids, HttpServletResponse response) {

        WhLendReturnQueryCondition query = domain.getQuery();

        List<WhLendReturn> whLendReturnList = null;

        ExportType exportTypeEnum = ExportType.build(exportType);

        if (query == null) {
            query = new WhLendReturnQueryCondition();
        }
        // 兼容含有“=”的sku
        query.setSku(CompatibleSkuUtils.getSku(query.getSku()));

        // 查询导出的数据
        switch (exportTypeEnum) {
            case ALL: {
                query.setIds(null);
                List<WhLendReturn> whLendReturns = whLendReturnService.queryWhLendReturnList(query, null);
                //组装数据
                whLendReturnList = whLendReturnService.assembleData(whLendReturns);
            }
            break;
            case PAGE: {
                Pager page = domain.getPage();
                query.setIds(null);
                List<WhLendReturn> whLendReturns = whLendReturnService.queryWhLendReturnList(query, null);
                //组装数据
                whLendReturnList = whLendReturnService.assembleData(whLendReturns);
                int pageNo = page.getPageNo();
                int pageSize = page.getPageSize();
                whLendReturnList= whLendReturnList.stream().skip((pageNo-1)*pageSize).limit(pageSize).
                        collect(Collectors.toList());

            }
            break;
            case CHECKED: {
                if (CollectionUtils.isNotEmpty(ids)) {
                    query = new WhLendReturnQueryCondition();
                    query.setIdList(ids);
                    List<WhLendReturn> whLendReturns = whLendReturnService.queryWhLendReturnList(query, null);
                    if (CollectionUtils.isEmpty(whLendReturns))return;
                    List<String> collect = whLendReturns.stream().map(a -> a.getLendCheckOutCode()).collect(Collectors.toList());
                    String join = String.join(",", collect);
                    query = new WhLendReturnQueryCondition();
                    query.setLendCheckOutCode(join);
                    whLendReturns = whLendReturnService.queryWhLendReturnList(query, null);
                    whLendReturnList = whLendReturnService.assembleData(whLendReturns);
                }
            }
            break;
        }

        OutputStream os = null;
        try {
            String fileName = "外借单归还单" + System.currentTimeMillis() + ".xls";
            response.setContentType("application/ms-excel");
            response.setHeader("Content-Disposition",
                    "attachment; filename=" + java.net.URLEncoder.encode(fileName, "UTF-8"));
            os = response.getOutputStream();
            final List<List<String>> apvData = new ArrayList<List<String>>();
            POIUtils.createExcel(HEADERS, whLendReturnList, whLendReturn -> {
                apvData.clear();
                List<String> apvlist = new ArrayList<String>(HEADERS.length);
                apvlist.add(POIUtils.transferObj2Str(whLendReturn.getLendCheckOutCode()));
                apvlist.add(POIUtils.transferObj2Str(whLendReturn.getSkuQuantity()));
                apvlist.add(POIUtils.transferObj2Str(whLendReturn.getSkuTotal()));
                apvlist.add(POIUtils.transferObj2Str(whLendReturn.getQuantity()));
                apvlist.add(POIUtils.transferObj2Str(whLendReturn.getPickQuantity()));
                apvlist.add(POIUtils.transferObj2Str(whLendReturn.getReturnQuantity()));
                apvlist.add(POIUtils.transferObj2Str(whLendReturn.getDestroyQuantity()));
                apvlist.add(POIUtils.transferObj2Str(whLendReturn.getNoReturnQuantity()));
                apvlist.add(POIUtils.transferObj2Str(LendReturnStatus.getNameByCode(whLendReturn.getStatus()+"") ));
                apvlist.add(POIUtils.transferObj2Str(whLendReturn.getAcceptByName()) );
                apvlist.add(POIUtils.transferObj2Str(whLendReturn.getPlanReturnTime()));
                apvData.add(apvlist);
                return apvData;
            }, true, os);

        }
        catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        finally {
            IOUtils.closeQuietly(os);
        }
    }


    private static String[] HEADERS_ITEM = { "关联出库单号", "sku", "库存ID", "名称", "报废批次/到期时间", "外借数量", "拣货数量", "归还数量","核销数量", "未归还数量"};

    /**
     * 导出
     *
     * @param domain
     * @param exportType
     * @param ids
     * @param response
     */
    @SuppressWarnings("incomplete-switch")
    @RequestMapping(value = "downloadLendReturnList", method = { RequestMethod.POST })
    @ResponseBody
    public void downloadLendReturnList(@ModelAttribute("domain") WhLendReturnDo domain,
                         @RequestParam("exportType") String exportType,
                         @RequestParam(value = "ids", required = false) List<Integer> ids, HttpServletResponse response) {

        WhLendReturnQueryCondition query = domain.getQuery();

        List<WhLendReturn> whLendReturns = null;

        ExportType exportTypeEnum = ExportType.build(exportType);

        if (query == null) {
            query = new WhLendReturnQueryCondition();
        }
        // 兼容含有“=”的sku
        query.setSku(CompatibleSkuUtils.getSku(query.getSku()));

        // 查询导出的数据
        switch (exportTypeEnum) {
            case ALL: {
                query.setIds(null);
                whLendReturns = whLendReturnService.queryWhLendReturnList(query, null);
            }
            break;
            case PAGE: {
                Pager page = domain.getPage();
                query.setIds(null);
                whLendReturns = whLendReturnService.queryWhLendReturnList(query, null);
                //组装数据
                whLendReturns = whLendReturnService.assembleData(whLendReturns);
                int pageNo = page.getPageNo();
                int pageSize = page.getPageSize();
                whLendReturns= whLendReturns.stream().skip((pageNo-1)*pageSize).limit(pageSize).
                        collect(Collectors.toList());
                if (CollectionUtils.isEmpty(whLendReturns))return;
                List<String> collect = whLendReturns.stream().map(a -> a.getLendCheckOutCode()).collect(Collectors.toList());
                String join = String.join(",", collect);
                query = new WhLendReturnQueryCondition();
                query.setLendCheckOutCode(join);
                whLendReturns = whLendReturnService.queryWhLendReturnList(query, null);
            }
            break;
            case CHECKED: {
                if (CollectionUtils.isNotEmpty(ids)) {
                    query = new WhLendReturnQueryCondition();
                    query.setIdList(ids);
                    List<WhLendReturn> whLendReturnList = whLendReturnService.queryWhLendReturnList(query, null);
                    if (CollectionUtils.isEmpty(whLendReturnList))return;
                    List<String> collect = whLendReturnList.stream().map(a -> a.getLendCheckOutCode()).collect(Collectors.toList());
                    String join = String.join(",", collect);
                    query = new WhLendReturnQueryCondition();
                    query.setLendCheckOutCode(join);
                    whLendReturns = whLendReturnService.queryWhLendReturnList(query, null);
                }
            }
            break;
        }

        OutputStream os = null;
        try {
            String fileName = "外借单归还单明细" + System.currentTimeMillis() + ".xls";
            response.setContentType("application/ms-excel");
            response.setHeader("Content-Disposition",
                    "attachment; filename=" + java.net.URLEncoder.encode(fileName, "UTF-8"));
            os = response.getOutputStream();
            final List<List<String>> apvData = new ArrayList<List<String>>();
            POIUtils.createExcel(HEADERS_ITEM, whLendReturns, whLendReturn -> {
                apvData.clear();
                List<String> apvlist = new ArrayList<String>(HEADERS.length);
                apvlist.add(POIUtils.transferObj2Str(whLendReturn.getLendCheckOutCode()));
                apvlist.add(POIUtils.transferObj2Str(whLendReturn.getSku()));
                apvlist.add(POIUtils.transferObj2Str(whLendReturn.getStockId()));
                apvlist.add(POIUtils.transferObj2Str(whLendReturn.getName()));
                apvlist.add(POIUtils.transferObj2Str(whLendReturn.getBatchNoInfo()));
                apvlist.add(POIUtils.transferObj2Str(whLendReturn.getQuantity()));
                apvlist.add(POIUtils.transferObj2Str(whLendReturn.getPickQuantity()));
                apvlist.add(POIUtils.transferObj2Str(whLendReturn.getReturnQuantity()));
                apvlist.add(POIUtils.transferObj2Str(whLendReturn.getDestroyQuantity()));
                apvlist.add(POIUtils.transferObj2Str(whLendReturn.getNoReturnQuantity()));
                apvData.add(apvlist);
                return apvData;
            }, true, os);

        }
        catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        finally {
            IOUtils.closeQuietly(os);
        }
    }


}