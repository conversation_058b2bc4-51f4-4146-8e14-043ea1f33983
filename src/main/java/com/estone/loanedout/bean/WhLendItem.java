package com.estone.loanedout.bean;

import java.io.Serializable;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import com.alibaba.fastjson.JSONObject;
import com.estone.checkout.bean.ReturnFormOrderItem;
import com.estone.sku.bean.ExpManageItem;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.ArrayUtils;
import org.apache.commons.lang.StringUtils;

@Data
public class WhLendItem implements Serializable, Comparable<WhLendItem>{
    private static final long serialVersionUID = 1L;

    /**
     * 主键id database column wh_lend_item.id
     */
    private Integer id;

    /**
     * wh_asn id database column wh_lend_item.wh_lend_id
     */
    private Integer whLendId;

    /**
     * SKU database column wh_lend_item.sku
     */
    private String sku;

    /**
     * 数量 database column wh_lend_item.quantity
     */
    private Integer quantity;

    /**
     * 单价 database column wh_lend_item.price
     */
    private Double price;

    /**
     * 金额 database column wh_lend_item.amount
     */
    private Double amount;

    /**
     * 产品名称 database column wh_lend_item.name
     */
    private String name;

    // 是否保质期sku
    private Integer isExpSku;

    // 保质期sku过期JSON
    private String batchNoJson;

    private String expDate;

    private Integer taskId;

    //外借拣货号
    private String taskNo;

    //已拣数量
    private Integer pickQuantity;

    //库位
    private String location;

    //sku名称
    private String imageUrl;

    //拣货状态
    private Integer status;

    private Integer stockId;

    //拣货状态
    public Integer getStatus(){
        return Optional.ofNullable(status).orElse(0);
    }

    //需拣数量
    public Integer getAllotQuantity(){
       return Optional.ofNullable(quantity).orElse(0);
    }



    public String getBatchNoInfo(){
        List<ExpManageItem> expManageItems = JSONObject.parseArray(batchNoJson, ExpManageItem.class);
        if (CollectionUtils.isEmpty(expManageItems)){
            return null;
        }
        List<String> notBatch = expManageItems.stream().filter(e -> !StringUtils.equalsIgnoreCase("notBatch", e.getBatchNo())).map(s -> {
            return s.getBatchNo() + "/" + s.getExpDateStr();
        }).collect(Collectors.toList());
        return StringUtils.join(notBatch,"<br/>");

    }
    // changed by liuguolin at 2017-12-26 新增新仓库对比规则。2G>2A>2B>2C>2D>2F>2E
    final static String[] newAreaSeqArr = new String[] { "2K", "2A", "2B", "2C", "2D", "2F", "2E" };
    final static String[] newAreaReplaceArr = new String[] { "2A", "2B", "2C", "2D", "2E", "2F", "2G" };
    // 旧仓库规则
    // 2021-06-04 最新对比规则 Y-D-E-F-G-H-J-K-L-M
    final static String[] areaSeqArr = new String[] { "Y", "D", "E", "F", "G", "H", "J", "K", "L", "M" };
    final static String[] areaReplaceArr = new String[] { "A", "B", "C", "D", "E", "F", "G", "H", "I", "J" };

    @Override
    public int compareTo(WhLendItem o) {
        // 旧版本
        String StockLocation1 = transformStockLocation(location);
        String StockLocation2 = transformStockLocation(o.getLocation());

        String[] str1s = StockLocation1.split("-");
        if (str1s.length == 4) {
            if (str1s[0].length() == 2) {
                str1s[0] = str1s[0].substring(0, 1) + "0" + str1s[0].substring(1, 2);
            }
            if (str1s[1].length() == 1) {
                str1s[1] = "0" + str1s[1];
            }
            if (str1s[2].length() == 1) {
                str1s[2] = "0" + str1s[2];
            }
            if (str1s[3].length() == 1) {
                str1s[3] = "0" + str1s[3];
            }
            StockLocation1 = str1s[0] + "-" + str1s[1] + "-" + str1s[2] + "-" + str1s[3];
        }

        String[] str2s = StockLocation2.split("-");
        if (str2s.length == 4) {
            if (str2s[0].length() == 2) {
                str2s[0] = str2s[0].substring(0, 1) + "0" + str2s[0].substring(1, 2);
            }
            if (str2s[1].length() == 1) {
                str2s[1] = "0" + str2s[1];
            }
            if (str2s[2].length() == 1) {
                str2s[2] = "0" + str2s[2];
            }
            if (str2s[3].length() == 1) {
                str2s[3] = "0" + str2s[3];
            }
            StockLocation2 = str2s[0] + "-" + str2s[1] + "-" + str2s[2] + "-" + str2s[3];
        }

        int result = StockLocation1.compareTo(StockLocation2);

        if (result == 0) {
            return sku.compareTo(o.getSku());
        }
        return result;
    }

    private String transformStockLocation(String stockLocation) {
        String transformStockLocation = null;
        if (StringUtils.isNotEmpty(stockLocation)) {
            stockLocation = stockLocation.toUpperCase();
            String areaCode = stockLocation.substring(0, 1);
            if (ArrayUtils.contains(areaSeqArr, areaCode)) {
                int areaCodeIndex = ArrayUtils.indexOf(areaSeqArr, areaCode);
                String areaReplaceCode = areaReplaceArr[areaCodeIndex];
                stockLocation = stockLocation.replaceFirst(areaCode, areaReplaceCode);
                transformStockLocation = stockLocation;
            }
            else if ("2".equals(areaCode)) {
                String newAreaCode = stockLocation.substring(0, 2);
                if (ArrayUtils.contains(newAreaSeqArr, newAreaCode)) {
                    int newAreaCodeIndex = ArrayUtils.indexOf(newAreaSeqArr, newAreaCode);
                    String newAreaReplaceCode = newAreaReplaceArr[newAreaCodeIndex];
                    stockLocation = stockLocation.replaceFirst(newAreaCode, newAreaReplaceCode);
                    return stockLocation;
                }
            }
        }
        transformStockLocation = stockLocation;
        if (transformStockLocation == null) {
            transformStockLocation = "Z";
        }
        return transformStockLocation;
    }


    /**
     * 用于传递状态变更消息给pms的时候带过去的字段值
     * @return
     */
    public Integer getActualQuantity(){
        return this.getPickQuantity();
    }
}