package com.estone.loanedout.bean;

import java.io.Serializable;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import lombok.Data;
import org.apache.commons.collections.CollectionUtils;

@Data
public class WhLend implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键id database column wh_lend.id
     */
    private Integer id;

    /**
     * 出库单号 database column wh_lend.lend_check_out_code
     */
    private String lendCheckOutCode;

    /**
     * 产品总数 database column wh_lend.sku_total
     */
    private Integer skuTotal;

    /**
     * SKU种类 database column wh_lend.sku_species
     */
    private Integer skuSpecies;

    /**
     * 总价 database column wh_lend.sku_total_amount
     */
    private Double skuTotalAmount;

    /**
     * 是否需要归还，0不需要，1需要 database column wh_lend.is_need_return
     */
    private Integer isNeedReturn;

    /**
     * 计划归还日期 database column wh_lend.plan_return_time
     */
    private Timestamp planReturnTime;

    private String planReturnDate;

    /**
     * 接收工号 database column wh_lend.accept_by
     */
    private String acceptBy;

    /**
     * 接收姓名 database column wh_lend.accept_by_name
     */
    private String acceptByName;

    /**
     * 外借状态 database column wh_lend.status
     */
    private Integer status;

    /**
     * 创建人 database column wh_lend.created_by
     */
    private Integer createdBy;

    /**
     * 入库时间 database column wh_lend.creation_date
     */
    private Timestamp creationDate;

    private String createDate;

    /**
     * 审核人 database column wh_lend.verify_by
     */
    private Integer verifyBy;

    /**
     * 审核时间 database column wh_lend.verify_date
     */
    private Timestamp verifyDate;

    /**
     * 外借原因 database column wh_lend.lend_reason
     */
    private String lendReason;

    /**
     * 审核人database column wh_lend.verify_remark
     */
    private String verifyRemark;

    private List<WhLendItem> items = new ArrayList<>();

    private String wareHouse;

    private String creationName;

    private Integer isExpSku; //是否保质期sku

    /**
     * 仓库审核人 database column wh_lend.verify_wms_by
     */
    private Integer verifyWmsBy;

    /**
     *仓库审核人姓名
     */
    private String verifyWmsByName;

    /**
     * 确定出库时间
     */
    private Timestamp deliveryTime;

    /**
     * 确定出库人
     */
    private Integer deliveryBy;

    private boolean clearPlanReturnTime = false;

    private String source;

    // 唯一码集合
    private List<String> uniqueSkuList = new ArrayList<>();

    public Integer getSkuPickingTotal(){
        if (CollectionUtils.isEmpty(items)) {
            return null;
        }
       return items.stream().filter(a->a.getPickQuantity()!=null).mapToInt(WhLendItem::getPickQuantity).sum();
    }

    /**
     * 用于传递状态变更消息给pms的时候带过去的字段值
     * @return
     */
    public String getWarehouseProcessor(){
        return Optional.ofNullable(this.getVerifyWmsByName()).orElse(this.getVerifyRemark());
    }
    /**
     * 用于传递状态变更消息给pms的时候带过去的字段值
     * @return
     */
    public String getWarehouseNotes(){
        return this.getVerifyRemark();
    }
}