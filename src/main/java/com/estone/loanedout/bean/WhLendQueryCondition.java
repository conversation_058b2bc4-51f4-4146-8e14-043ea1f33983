package com.estone.loanedout.bean;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class WhLendQueryCondition extends WhLend {
    private static final long serialVersionUID = 1L;

    private List<Integer> ids = new ArrayList<>();

    private String sku;

    private String fromCreationDate;// 创建起始时间

    private String toCreationDate;// 创建终止时间

    private String fromPlanReturnTime;// 归还起始时间

    private String toPlanReturnTime;// 归还终止时间

    private Integer isOverTime; //是否超时

    private List<Integer> statusList;

    private Integer stockId;

    private List<Integer> stockIdList;

    private String stockIdStr;


}