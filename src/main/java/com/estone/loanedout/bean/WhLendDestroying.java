package com.estone.loanedout.bean;

import java.io.Serializable;
import java.sql.Timestamp;
import lombok.Data;

@Data
public class WhLendDestroying implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键id database column wh_lend_destroying.id
     */
    private Integer id;

    /**
     * 出库单号 database column wh_lend_destroying.lend_check_out_code
     */
    private String lendCheckOutCode;

    /**
     * SKU database column wh_lend_destroying.sku
     */
    private String sku;

    /**
     * 核销数量 database column wh_lend_destroying.destroy_quantity
     */
    private Integer destroyQuantity;

    /**
     * 单价 database column wh_lend_destroying.price
     */
    private Double price;

    /**
     * 金额 database column wh_lend_destroying.amount
     */
    private Double amount;

    /**
     * 状态 database column wh_lend_destroying.status
     */
    private Integer status;

    /**
     * 接收工号 database column wh_lend_destroying.accept_by
     */
    private String acceptBy;

    /**
     * 接收姓名 database column wh_lend_destroying.accept_by_name
     */
    private String acceptByName;

    /**
     * 计划归还日期 database column wh_lend_destroying.plan_return_time
     */
    private Timestamp planReturnTime;

    /**
     * 创建人 database column wh_lend_destroying.created_by
     */
    private Integer createdBy;

    /**
     * 入库时间 database column wh_lend_destroying.creation_date
     */
    private Timestamp creationDate;

    // 核销完成时间
    private Timestamp completeDate;

    /**
     * 产品名称 database column wh_lend_destroying.name
     */
    private String name;

    private Integer stockId;

    /**
     * 收款账号
     */
    private String receiveAccount;

    /**
     * 金额
     */
    private Double money;

    /**
     * 打款时间
     */
    private Timestamp payDate;

    /**
     * 备注
     */
    private String remark;

    /**
     * 付款截图url
     */
    private String paySnapShotUrl;

    private String source;

    /**
     * 来源为PMS的时候进行前端展示使用
     */
    private String createdNoAndName;
}