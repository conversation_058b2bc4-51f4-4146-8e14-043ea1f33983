package com.estone.loanedout.bean;


import lombok.Data;
import java.io.Serializable;
import java.sql.Timestamp;
import java.util.Date;

/**
 * <p>
 * 核销记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-03-30
 */
@Data
public class WhVerification implements Serializable {

    private static final long serialVersionUID = 1L;

    private Integer id;

    private String verificationOrderId;

    private String lendCheckOutCode;

    private String sku;

    private Integer number;

    private Double price;

    private Double totalAmount;

    private String warehouseName;

    private Double amountPaid;

    private String payAccount;

    private String payOrderId;

    private Date planReturnTime;

    private String acceptBy;

    private String acceptByName;

    private Integer status;

    private Integer createdBy;

    private Date creationDate;

    private String creationName;

    private String verifyBy;

    private Date verifyDate;

    private Integer isPay;

    private String arriveAccount;

    private String payOrderIdAck;

    private Double amountPaidAck;

    private String wmsCreationDate;

    private String wmsPlanReturnTime;

    private Integer wmsId;


    /**
     * 收款账号
     */
    private String receiveAccount;

    /**
     * 金额
     */
    private Double money;

    /**
     * 打款时间
     */
    private Timestamp payDate;

    /**
     * 备注
     */
    private String remark;

    /**
     * 付款截图url
     */
    private String paySnapShotUrl;
}
