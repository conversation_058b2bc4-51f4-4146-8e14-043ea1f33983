package com.estone.apv.domain;

import com.estone.apv.bean.TransportExceptionRecords;
import com.estone.apv.bean.TransportExceptionRecordsQueryCondition;
import com.whq.tool.component.Pager;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;
@Data
public class TransportExceptionRecordsDo {
    private TransportExceptionRecords transportExceptionRecords;

    private TransportExceptionRecordsQueryCondition query;

    private List<TransportExceptionRecords> transportExceptionRecordss = new ArrayList<TransportExceptionRecords>();

    private Pager page = new Pager();

    private String typeSelectJson;
}