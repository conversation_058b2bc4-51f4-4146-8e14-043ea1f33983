package com.estone.apv.domain;

import com.estone.apv.bean.SmPackingException;
import com.estone.apv.bean.SmPackingPickingTaskItem;
import com.estone.apv.bean.SmPackingPickingTaskItemQueryCondition;
import com.whq.tool.component.Pager;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;
@Data
public class SmPackingPickingTaskItemDo {
    private SmPackingPickingTaskItem smPackingPickingTaskItem;

    private SmPackingPickingTaskItemQueryCondition query;

    private List<SmPackingPickingTaskItem> smPackingPickingTaskItems = new ArrayList<SmPackingPickingTaskItem>();

    private Pager page = new Pager();

    private List<SmPackingException> smPackingExceptionList=new ArrayList<>();

    private List<SmPackingException> assembleSmPackingExceptionList=new ArrayList<>();
}