package com.estone.apv.dao;

import com.estone.apv.bean.SmPackingPickingTask;
import com.estone.apv.bean.SmPackingPickingTaskQueryCondition;
import com.estone.picking.bean.WhPickingTaskSku;
import com.estone.picking.bean.WhPickingTaskSkuLocation;
import com.whq.tool.component.Pager;
import java.util.List;

public interface SmPackingPickingTaskDao {
    int querySmPackingPickingTaskCount(SmPackingPickingTaskQueryCondition query);

    List<SmPackingPickingTask> querySmPackingPickingTaskList();

    List<SmPackingPickingTask> querySmPackingPickingTaskList(SmPackingPickingTaskQueryCondition query, Pager pager);

    SmPackingPickingTask querySmPackingPickingTask(Integer primaryKey);

    SmPackingPickingTask querySmPackingPickingTask(SmPackingPickingTaskQueryCondition query);

    void createSmPackingPickingTask(SmPackingPickingTask entity);

    void batchCreateSmPackingPickingTask(List<SmPackingPickingTask> entityList);

    void batchUpdateSmPackingPickingTask(List<SmPackingPickingTask> entityList);

    void deleteSmPackingPickingTask(Integer primaryKey);

    void updateSmPackingPickingTask(SmPackingPickingTask entity);

    List<WhPickingTaskSkuLocation> querySmPackingItemSkuList(List<Integer> idList);
}