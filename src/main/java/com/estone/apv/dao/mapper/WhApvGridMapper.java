package com.estone.apv.dao.mapper;

import com.estone.apv.bean.WhApvGrid;
import java.sql.ResultSet;
import java.sql.SQLException;
import org.springframework.jdbc.core.RowMapper;

public class WhApvGridMapper implements RowMapper<WhApvGrid> {

    /**
     * This method corresponds to the database table wh_apv_grid
     *
     * @mbggenerated Wed Aug 15 11:11:43 CST 2018
     */
    public WhApvGrid mapRow(ResultSet rs, int rowNum) throws SQLException {
        WhApvGrid entity = new WhApvGrid();
        entity.setId(rs.getObject(WhApvGridDBField.ID) == null ? null : rs.getInt(WhApvGridDBField.ID));
        entity.setLocation(rs.getObject(WhApvGridDBField.LOCATION) == null ? null : rs.getInt(WhApvGridDBField.LOCATION));
        entity.setSerialNumber(rs.getString(WhApvGridDBField.SERIAL_NUMBER));
        entity.setNumber(rs.getObject(WhApvGridDBField.NUMBER) == null ? null : rs.getInt(WhApvGridDBField.NUMBER));
        entity.setApvId(rs.getObject(WhApvGridDBField.APV_ID) == null ? null : rs.getInt(WhApvGridDBField.APV_ID));
        entity.setStatus(rs.getObject(WhApvGridDBField.STATUS) == null ? null : rs.getInt(WhApvGridDBField.STATUS));
        entity.setCreatedBy(rs.getObject(WhApvGridDBField.CREATED_BY) == null ? null : rs.getInt(WhApvGridDBField.CREATED_BY));
        entity.setCreationDate(rs.getTimestamp(WhApvGridDBField.CREATION_DATE));
        return entity;
    }
}