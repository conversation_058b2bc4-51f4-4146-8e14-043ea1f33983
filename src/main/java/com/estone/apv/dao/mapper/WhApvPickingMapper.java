package com.estone.apv.dao.mapper;

import java.sql.ResultSet;
import java.sql.SQLException;

import org.springframework.jdbc.core.RowMapper;

import com.estone.apv.bean.WhApvPicking;

public class WhApvPickingMapper implements RowMapper<WhApvPicking> {

    @Override
    public WhApvPicking mapRow(ResultSet rs, int rowNum) throws SQLException {
        WhApvPicking whApvPicking = new WhApvPicking();

        whApvPicking.setWarehouseId(rs.getInt("sku.warehouse_id"));
        whApvPicking.setSku(rs.getString("apv_item.sku"));
        whApvPicking.setProductName(rs.getString("sku.name"));
        whApvPicking.setLocationNumber(rs.getString("sku.location_number"));
        whApvPicking.setPickingQuantity(rs.getInt("picking_quantity"));

        return whApvPicking;
    }

}
