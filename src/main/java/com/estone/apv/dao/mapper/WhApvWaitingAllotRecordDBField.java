package com.estone.apv.dao.mapper;

/**
 * <AUTHOR>
 * @date 2022-11-30 15:12
 */
public interface WhApvWaitingAllotRecordDBField {
    String ID = "id";

    String APV_ID = "apv_id";

    String TASK_ID = "task_id";

    String APV_HISTORICAL_STATUS = "apv_historical_status";

    String REMARK = "remark";

    String OPERATOR = "operator";

    String CREATE_TIME = "create_time";

    String INTERCEPT_TYPE = "intercept_type";
}
