package com.estone.apv.dao.mapper;

public interface WhApvOutStockChainDBField {
    String ID = "id";

    String SKU = "sku";

    String RELEVANT_NO = "relevant_no";

    String STATUS = "status";

    String STOCK_ID = "stock_id";

    String QUANTITY = "quantity";

    String PICK_QUANTITY = "pick_quantity";

    String GIRD_QUANTITY = "gird_quantity";

    String CREATION_DATE = "creation_date";

    String LAST_UPDATE_DATE = "last_update_date";
    String PICK_STOCK_ID = "pick_stock_id";
}