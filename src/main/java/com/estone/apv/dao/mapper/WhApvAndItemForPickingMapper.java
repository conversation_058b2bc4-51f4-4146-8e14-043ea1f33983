package com.estone.apv.dao.mapper;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.HashMap;
import java.util.Map;

import org.springframework.jdbc.core.RowMapper;

import com.estone.apv.bean.WhApv;
import com.estone.apv.bean.WhApvItem;

/**
 * 
 * @ClassName: WhApvAndItemForPickingMapper
 * @Description: 拣货查询
 * <AUTHOR>
 * @date 2019年5月10日
 * @version 0.0.2
 *
 */
public class WhApvAndItemForPickingMapper implements RowMapper<WhApv> {

    private Map<Integer, WhApv> exist = new HashMap<Integer, WhApv>();

    @Override
    public WhApv mapRow(ResultSet rs, int rowNum) throws SQLException {
        // 订单id
        Integer apvId = rs.getInt("apv.id");

        WhApv whApv = exist.get(apvId);

        boolean isExist = false;

        if (whApv == null) {
            // 订单信息
            whApv = new WhApv();
            whApv.setId(apvId);
            whApv.setApvNo(rs.getString("apv.apv_no"));
            whApv.setPlatform(rs.getInt("apv.platform"));
            whApv.setStatus(rs.getInt("apv.status"));
            whApv.setSignDistributionGoods(rs.getBoolean("apv.sign_distribution_goods"));
            exist.put(apvId, whApv);
        }
        else {
            isExist = true;
        }

        WhApvItem apvItem = new WhApvItem();
        apvItem.setId(rs.getInt("apv_item.id"));
        apvItem.setApvId(rs.getInt("apv_item.apv_id"));
        apvItem.setApvLineItemId(rs.getString("apv_item.apv_line_item_id"));
        apvItem.setSku(rs.getString("apv_item.sku"));
        apvItem.setSaleQuantity(rs.getInt("apv_item.sale_quantity"));
        apvItem.setPickQuantity(rs.getInt("apv_item.pick_quantity"));

        whApv.addApvItem(apvItem);

        return isExist ? null : whApv;
    }

}
