package com.estone.apv.dao.mapper;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.HashMap;
import java.util.Map;

import org.springframework.jdbc.core.RowMapper;

import com.estone.apv.bean.ApvTrack;
import com.estone.apv.bean.WhApv;
import com.estone.apv.bean.WhApvItem;
import com.estone.sku.bean.WhSku;

/**
 * 
 * @ClassName: WhApvDeliverOrderStandardWeightMapper
 * @Description: TODO
 * <AUTHOR>
 * @date 2019年4月8日
 * @version 0.0.2
 *
 */
public class WhApvDeliverOrderStandardWeightMapper implements RowMapper<WhApv> {

    private Map<Integer, WhApv> exist = new HashMap<Integer, WhApv>();

    @Override
    public WhApv mapRow(ResultSet rs, int rowNum) throws SQLException {
        // 订单id
        Integer apvId = rs.getInt("apv.id");

        WhApv whApv = exist.get(apvId);

        boolean isExist = false;

        if (whApv == null) {
            // 订单信息
            whApv = new WhApv();
            whApv.setId(apvId);
            whApv.setApvNo(rs.getString("apv.apv_no"));
            whApv.setPlatform(rs.getInt("apv.platform"));
            whApv.setStatus(rs.getInt("apv.status"));
            whApv.setSalesRecordNumber(rs.getString("apv.sales_record_number"));
            whApv.setShipService(rs.getString("apv.ship_service"));
            whApv.setTrackingNumber(rs.getString("apv.tracking_number"));
            whApv.setServiceProviderNo(rs.getString("apv.service_provider_no"));
            whApv.setActualWeight(rs.getObject("apv.actual_weight") == null ? null : rs.getDouble("apv.actual_weight"));
            whApv.setLogisticsCompany(rs.getString("apv.logistics_company"));
            whApv.setShipFreight(
                    rs.getObject("apv.ship_freight") == null ? null : rs.getDouble("apv.ship_freight"));
            exist.put(apvId, whApv);
        }
        else {
            isExist = true;
        }

        WhApvItem apvItem = new WhApvItem();
        apvItem.setId(rs.getInt("apv_item.id"));
        apvItem.setApvId(rs.getInt("apv_item.apv_id"));
        apvItem.setSku(rs.getString("apv_item.sku"));
        apvItem.setSaleQuantity(rs.getInt("apv_item.sale_quantity"));

        // 关联的产品
        WhSku sku = new WhSku();
        sku.setWarehouseId(rs.getInt("sku.warehouse_id"));
        sku.setWeight(rs.getObject("sku.weight") == null ? null : rs.getDouble("sku.weight"));
        sku.setNetWeight(rs.getObject("sku.net_weight") == null ? null : rs.getDouble("sku.net_weight"));
        apvItem.setWhSku(sku);

        ApvTrack apvTrack = new ApvTrack();
        apvTrack.setDeliverTime(rs.getTimestamp("track.deliver_time"));
        apvTrack.setIsOverStandardWeightDiff(rs.getObject("track.is_over_standard_weight_diff") == null ? null
                : rs.getBoolean("track.is_over_standard_weight_diff"));
        apvTrack.setStandardWeightCount(rs.getObject("track.standard_weight_count") == null ? null
                : rs.getDouble("track.standard_weight_count"));
        apvTrack.setStandardWeightDiff(
                rs.getObject("track.standard_weight_diff") == null ? null : rs.getDouble("track.standard_weight_diff"));
        apvTrack.setCalculatedStandardWeight(rs.getObject("track.calculated_standard_weight") == null ? null
                : rs.getBoolean("track.calculated_standard_weight"));
        whApv.setApvTrack(apvTrack);
        apvTrack.setGoodsWeightCount(
                rs.getObject("track.goods_weight_count") == null ? null : rs.getDouble("track.goods_weight_count"));
        apvTrack.setGoodsWeightDiff(
                rs.getObject("track.goods_weight_diff") == null ? null : rs.getDouble("track.goods_weight_diff"));

        apvTrack.setEstimatedWeightCount(
                rs.getObject("track.estimated_weight_count") == null ? null : rs.getDouble("track.estimated_weight_count"));
        apvTrack.setBillWeightCount(
                rs.getObject("track.bill_weight_count") == null ? null : rs.getDouble("track.bill_weight_count"));
        apvTrack.setBillWeightDiff(
                rs.getObject("track.bill_weight_diff") == null ? null : rs.getDouble("track.bill_weight_diff"));

        whApv.addApvItem(apvItem);

        return isExist ? null : whApv;
    }

}
