package com.estone.apv.action;

import com.estone.apv.bean.PackExceptionUuidItem;
import com.estone.apv.bean.PackExceptionUuidItemQueryCondition;
import com.estone.apv.domain.PackExceptionUuidItemDo;
import com.estone.apv.enums.PackExceptionTypeEnum;
import com.estone.apv.service.PackExceptionUuidItemService;
import com.estone.common.SelectJson;
import com.estone.common.util.DateUtils;
import com.estone.common.util.POIUtils;
import com.estone.common.util.TaglibUtils;
import com.estone.sku.bean.WhUniqueSkuLog;
import com.whq.tool.action.BaseController;
import com.whq.tool.component.Pager;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 唯一码包装异常明细
 */
@Slf4j
@Controller
@RequestMapping(value = "apv/packExceptionUuidItem")
public class PackExceptionUuidItemController extends BaseController {

    @Resource
    private PackExceptionUuidItemService packExceptionUuidItemService;

    private static String[] headers = {"唯一码","异常类型","扫描人","扫描界面","拣货任务","扫描时间","库内返架单操作时间","已绑定的发货单"};

    @RequestMapping(method = {RequestMethod.GET})
    public String init(@ModelAttribute("domain") PackExceptionUuidItemDo domain) {
        initFormData(domain);
        queryPackExceptionUuidItems(domain);
        return "pack/packExceptionForUuidList";
    }

    private void initFormData(@ModelAttribute("domain") PackExceptionUuidItemDo domain) {
        domain.setTypeSelectJson(SelectJson.getList(PackExceptionTypeEnum.values()));
    }

    private void queryPackExceptionUuidItems(@ModelAttribute("domain") PackExceptionUuidItemDo domain) {
        PackExceptionUuidItemQueryCondition query = domain.getQuery();
        Pager page = domain.getPage();
        if (query == null){
            query = new PackExceptionUuidItemQueryCondition();
            domain.setQuery(query);
        }
        // 默认读从库
        List<PackExceptionUuidItem> packExceptionUuidItems = packExceptionUuidItemService.queryPackExceptionUuidItems(query, page);
        domain.setPackExceptionUuidItems(packExceptionUuidItems);
    }

    @RequestMapping(value="search", method = {RequestMethod.POST})
    public String search(@ModelAttribute("domain") PackExceptionUuidItemDo domain) {
        initFormData(domain);
        queryPackExceptionUuidItems(domain);
        return "pack/packExceptionForUuidList";
    }

    /**
     * @Description 导出明细 POST
     * <AUTHOR>
     * @date 2020/7/20 15:53
     * @version 1.0
     */
    @RequestMapping(value = "download", method = {RequestMethod.POST})
    @ResponseBody
    public void downloadByPost(@RequestParam(value = "ids") String idStr,
                               HttpServletResponse response) {
        List<Integer> ids = new ArrayList<>();
        CollectionUtils.addAll(ids, StringUtils.split(idStr, ","));
        download(new PackExceptionUuidItemDo(), ids, response);
    }

    /**
     * @Description 导出明细 GET
     * <AUTHOR>
     * @date 2020/7/20 15:53
     * @version 1.0
     */
    @RequestMapping(value = "download", method = {RequestMethod.GET})
    @ResponseBody
    public void download(@ModelAttribute("domain") PackExceptionUuidItemDo domain,
                         @RequestParam(value = "ids", required = false) List<Integer> ids, HttpServletResponse response) {
        PackExceptionUuidItemQueryCondition query = domain.getQuery();
        if (query == null) {
            query = new PackExceptionUuidItemQueryCondition();
            domain.setQuery(query);
        }
        if (CollectionUtils.isNotEmpty(ids)) {
            query.setIds(ids);
        }
        List<PackExceptionUuidItem> items = packExceptionUuidItemService.queryPackExceptionUuidItems(query, null);
        // 以防万一在controller层也限制导出10万条数据
        if (CollectionUtils.isEmpty(items) || items.size() > 100000) {
            return;
        }

        try {
            String fileName = "唯一码包装异常明细" + System.currentTimeMillis() + ".xlsx";
            response.setContentType("application/ms-excel");
            response.setHeader("Content-Disposition",
                    "attachment; filename=" + java.net.URLEncoder.encode(fileName, "UTF-8"));
            log.warn("download file name: " + fileName);
            final List<List<String>> data = new ArrayList<List<String>>();
            POIUtils.createExcel(headers, items, item -> {
                data.clear();
                List<String> rowList = new ArrayList<>();
                rowList.add(POIUtils.transferObj2Str(item.getUuid()));
                rowList.add(POIUtils.transferObj2Str(PackExceptionTypeEnum.getNameByCode(item.getExceptionType())));
                rowList.add(POIUtils.transferObj2Str(TaglibUtils.getEmployeeNameByUserId(item.getScanUser())));
                rowList.add(POIUtils.transferObj2Str(item.getScanPage()));
                rowList.add(POIUtils.transferObj2Str(item.getPickingTackNo()));
                rowList.add(POIUtils.transferObj2Str(DateUtils.formatDate(item.getScanTime(), "yyyy-MM-dd HH:mm:ss")));
                Timestamp returnTime = null;
                WhUniqueSkuLog returnLog = item.getReturnLog();
                if (Objects.nonNull(returnLog)){
                    returnTime = returnLog.getCreationDate();
                }
                rowList.add(POIUtils.transferObj2Str(returnTime));
                rowList.add(POIUtils.transferObj2Str(item.getBindingApvNo()));
                data.add(rowList);
                return data;
            }, true, response.getOutputStream());
            log.warn("---task execute end---");
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }

}