package com.estone.apv.action;

import com.alibaba.fastjson.JSON;
import com.estone.apv.bean.*;
import com.estone.apv.common.ApvOrderType;
import com.estone.apv.common.ApvStatus;
import com.estone.apv.domain.WhApvDo;
import com.estone.apv.enums.ApvOversizeStatus;
import com.estone.apv.enums.ApvTaxTypeEnum;
import com.estone.apv.service.ApvOversizeService;
import com.estone.apv.service.WhApvService;
import com.estone.apv.util.QueryNumberUtils;
import com.estone.common.SelectJson;
import com.estone.common.util.DateUtils;
import com.estone.common.util.POIUtils;
import com.estone.common.util.SystemLogUtils;
import com.estone.common.util.model.ResultModel;
import com.estone.system.downloadcenter.enums.WhDownloadContentEnum;
import com.estone.system.downloadcenter.service.WhDownloadCenterService;
import com.whq.tool.action.BaseController;
import com.whq.tool.component.Pager;
import com.whq.tool.component.StatusCode;
import com.whq.tool.json.ResponseJson;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @Description 超体积发货管理
 * @date 2020/7/17 17:28
 */
@Slf4j
@Controller
@RequestMapping(value = "apvOversize")
public class ApvOversizeController extends BaseController {

    String[] headers = {"发货单号", "DD号", "体积", "称重重量(g)", "是否可发货", "销售确认时间", "推单时间", "付款时间", "发货类型", "物流方式", "标签", "取消时间", "扫描拦截时间", "平台", "备注"};

    @Resource
    private ApvOversizeService apvOversizeService;

    @Resource
    private WhApvService whApvService;

    @Resource
    private WhDownloadCenterService whDownloadCenterService;
    @RequestMapping(method = {RequestMethod.GET})
    public String init(@ModelAttribute("domain") WhApvDo domain) {
        initFormData(domain);
//        queryApvOversize(domain);
        return "apv/apvOversizeList";
    }

    private void initFormData(@ModelAttribute("domain") WhApvDo domain) {
        domain.setOversizeStatusJson(SelectJson.getList(ApvOversizeStatus.values()));
        domain.setStatusSelectJson(SelectJson.getList(new ApvStatus[]{ApvStatus.CANCEL, ApvStatus.WAITING_DELIVER, ApvStatus.DELIVER, ApvStatus.LOADED}));
    }

    private void queryApvOversize(@ModelAttribute("domain") WhApvDo domain) {
        WhApvQueryCondition query = domain.getQuery();
        Pager page = domain.getPage();
        if (query == null) {
            query = new WhApvQueryCondition();
            domain.setQuery(query);
        }
        if(StringUtils.isNotBlank(query.getFromDateValue()) || StringUtils.isNotBlank(query.getToDateValue())){
            switch (query.getDateType()){
                case 1:
                    query.setFromCreateDate(query.getFromDateValue());
                    query.setToCreateDate(query.getToDateValue());
                    break;
                case 2:
                    query.setFromAllotDate(query.getFromDateValue());
                    query.setToAllotDate(query.getToDateValue());
                    break;
                case 3:
                    query.setFromTaskDate(query.getFromDateValue());
                    query.setToTaskDate(query.getToDateValue());
                    break;
                case 4:
                    query.setFromPackDate(query.getFromDateValue());
                    query.setToPackDate(query.getToDateValue());
                    break;
                case 5:
                    query.setFromConfirmDate(query.getFromDateValue());
                    query.setToConfirmDate(query.getToDateValue());
                    break;
                case 6:
                    query.setFromInterceptDate(query.getFromDateValue());
                    query.setToInterceptDate(query.getToDateValue());
                    break;
            }
        }
        query.setOversize(true);
        query.setStatusList(Arrays.asList(ApvStatus.CANCEL.intCode(), ApvStatus.WAITING_DELIVER.intCode(), ApvStatus.DELIVER.intCode(), ApvStatus.LOADED.intCode()));
        List<WhApv> whApvs = whApvService.queryWhApvAndItemList(query, page);
        domain.setWhApvs(whApvs);
    }

    @RequestMapping(value = "search", method = {RequestMethod.POST})
    public String search(@ModelAttribute("domain") WhApvDo domain) {
        initFormData(domain);
        queryApvOversize(domain);
        return "apv/apvOversizeList";
    }

    /**
     * @Description 编辑尺寸、重量
     */
    @RequestMapping(value = "updateVolumeAndWeight", method = {RequestMethod.POST})
    @ResponseBody
    public ResponseJson updateVolumeAndWeight(@RequestBody ApvOversize apvOversize) {
        return apvOversizeService.updateVolumeAndWeight(apvOversize,false);
    }

    @ResponseBody
    @RequestMapping(value = "importVolumeAndWeight", method = {RequestMethod.POST})
    public ResponseJson importVolumeAndWeight(MultipartFile file) throws IOException {
        ResponseJson response = new ResponseJson(StatusCode.FAIL);
        ResultModel<Map<String, String>> impModel = POIUtils.readExcel(new String[]{"YST", "长(CM)", "宽(CM)", "高(CM)", "称重重量(g)", "备注"}, file, row -> {
            Map<String, String> map = new HashMap<>(8);
            String apvNo = POIUtils.cellValue2Str(row.getCell(0));
            String length = POIUtils.cellValue2Str(row.getCell(1));
            String width = POIUtils.cellValue2Str(row.getCell(2));
            String hight = POIUtils.cellValue2Str(row.getCell(3));
            String weight = POIUtils.cellValue2Str(row.getCell(4));
            String remark = POIUtils.cellValue2Str(row.getCell(5));
            map.put("apvNo", apvNo);
            map.put("length", length);
            map.put("width", width);
            map.put("hight", hight);
            map.put("weight", weight);
            map.put("remark", remark);
            return map;
        }, false);
        List<Map<String, String>> impList = impModel.getList();
        if(CollectionUtils.isEmpty(impList)){
            response.setMessage("导入数据为空");
            return response;
        }
        StringBuffer successSubf = new StringBuffer();
        StringBuffer failedSubf = new StringBuffer();
        ApvOversize oversize = null;
        response.setStatus(StatusCode.SUCCESS);
        for (Map<String, String> map : impList) {
            try {
                oversize = new ApvOversize();
                oversize.setApvNo(map.get("apvNo"));
                oversize.setLength(map.get("length")==null?null:Double.valueOf(map.get("length")));
                oversize.setWidth(map.get("width")==null?null:Double.valueOf(map.get("width")));
                oversize.setHight(map.get("hight")==null?null:Double.valueOf(map.get("hight")));
                oversize.setWeight(map.get("weight")==null?null:Double.valueOf(map.get("weight")));
                oversize.setRemark(map.get("remark"));
                ResponseJson responseJson = apvOversizeService.updateVolumeAndWeight(oversize,false);
                if(responseJson.isSuccess()){
                    successSubf.append(oversize.getApvNo()).append(",");
                }else{
                    failedSubf.append(oversize.getApvNo()).append("：").append(responseJson.getMessage()).append("<br/>");
                    response.setStatus(StatusCode.FAIL);
                }
            }catch (Exception e){
                failedSubf.append(oversize.getApvNo()).append("：").append("导入尺寸重量异常<br/>");
                response.setStatus(StatusCode.FAIL);
            }
        }
        response.setMessage(String.format("【导入成功单号】：<br/>%s<br/> 【导入失败单号】：<br/>%s", successSubf.toString(), failedSubf.toString()));
        return response;
    }

    /**
     * 批量推送快递尺寸信息
     */
    @ResponseBody
    @PostMapping("batchPush")
    public ResponseJson batchPush(@RequestParam List<Integer> ids){
        ResponseJson responseJson = new ResponseJson(StatusCode.FAIL);
        if (CollectionUtils.isEmpty(ids)) {
            responseJson.setMessage("请选择需要推送的单据");
            return responseJson;
        }
        WhApvQueryCondition query = new WhApvQueryCondition();
        query.setApvIds(ids);
        query.setOversize(true);
        List<WhApv> apvList = whApvService.queryWhApvAndItemList(query, null);
        if (CollectionUtils.isEmpty(apvList)) {
            responseJson.setMessage("查询到的单据为空！");
            return responseJson;
        }

        int failed = 0;
        StringBuffer sbuf = new StringBuffer("失败信息：<br/>");
        for (WhApv whApv:apvList){
            try {
                if (!ApvStatus.WAITING_DELIVER.equals(whApv.getStatus())) {
                    sbuf.append("[" + whApv.getApvNo() + "]").append("发货单不是等待发货状态！");
                    failed++;
                    continue;
                }
                ApvOversize apvOversize = whApv.getApvOversize();
                if(apvOversize == null || !apvOversize.canPush()){
                    sbuf.append("[" + whApv.getApvNo() + "]").append("体积或重量不能为空");
                    failed++;
                    continue;
                }
                if(ApvOversizeStatus.UN_CONFIRM.intCode().equals(apvOversize.getStatus())
                        || ApvOversizeStatus.CONFIRM.intCode().equals(apvOversize.getStatus())){
                    sbuf.append("[" + whApv.getApvNo() + "]").append("超体积发货单已推送或已确认发货！");
                    failed++;
                    continue;
                }
                apvOversize.setNewApvNo(whApv.getPaymentStatus());
                // 推送体积重量信息至OMS
                apvOversize.setApvId(whApv.getId());
                if(whApv.getShipStatus() == ApvOrderType.PACKING.intCode()){
                    apvOversize.setType(1);
                }
                if(StringUtils.isNotBlank(whApv.getBuyerCheckout())){
                    if (ApvTaxTypeEnum.OVERSIZE.getCode().equals(whApv.getBuyerCheckout())){
                        apvOversize.setType(2);
                    }else if(ApvTaxTypeEnum.OVERWEIGHT.getCode().equals(whApv.getBuyerCheckout())){
                        apvOversize.setType(3);
                    }
                }
                ResponseJson push = apvOversizeService.pushToOms(apvOversize);
                if (!push.isSuccess()){
                    sbuf.append("[" + whApv.getApvNo() + "]").append(push.getMessage()).append("<br/>");
                    failed++;
                }
            } catch (Exception e) {
                log.error("推送超体积单尺寸异常：" + e.getMessage(), e);
                sbuf.append("[" + whApv.getApvNo() + "]").append(e.getMessage()).append("<br/>");
                failed++;
            }
        }
        if (failed > 0){
            responseJson.setMessage(sbuf.toString());
            return responseJson;
        }
        responseJson.setStatus(StatusCode.SUCCESS);
        return responseJson;
    }

    /**
     * @Description 导出明细 GET
     * <AUTHOR>
     * @date 2020/7/20 15:53
     * @version 1.0
     */
    @RequestMapping(value = "download", method = {RequestMethod.POST})
    @ResponseBody
    public ResponseJson download(@ModelAttribute("domain") WhApvDo domain,
                         @RequestParam(value = "ids", required = false) List<Integer> ids) {

        ResponseJson response = new ResponseJson();
        WhApvQueryCondition query = domain.getQuery();
        if (query == null) {
            query = new WhApvQueryCondition();
            domain.setQuery(query);
        }else {
            QueryNumberUtils.queryApvInstall(query);
        }
        if (CollectionUtils.isNotEmpty(ids)) {
            query.setApvIds(ids);
        }
        domain.getPage().setPageNo(-1);
        boolean isAll = whDownloadCenterService.checkFieldAllNull(query);

        if(StringUtils.isNotBlank(query.getFromDateValue()) || StringUtils.isNotBlank(query.getToDateValue())){
            switch (query.getDateType()){
                case 1:
                    query.setFromCreateDate(query.getFromDateValue());
                    query.setToCreateDate(query.getToDateValue());
                    break;
                case 2:
                    query.setFromAllotDate(query.getFromDateValue());
                    query.setToAllotDate(query.getToDateValue());
                    break;
                case 3:
                    query.setFromTaskDate(query.getFromDateValue());
                    query.setToTaskDate(query.getToDateValue());
                    break;
                case 4:
                    query.setFromPackDate(query.getFromDateValue());
                    query.setToPackDate(query.getToDateValue());
                    break;
                case 5:
                    query.setFromConfirmDate(query.getFromDateValue());
                    query.setToConfirmDate(query.getToDateValue());
                    break;
                case 6:
                    query.setFromInterceptDate(query.getFromDateValue());
                    query.setToInterceptDate(query.getToDateValue());
                    break;
            }
        }
        query.setOversize(true);
        query.setDownload(true);
        query.setStatusList(Arrays.asList(ApvStatus.CANCEL.intCode(), ApvStatus.WAITING_DELIVER.intCode(), ApvStatus.DELIVER.intCode(), ApvStatus.LOADED.intCode()));

        String fileName = "超体积发货列表" + System.currentTimeMillis() + ".xlsx";
        WhApvQueryCondition finalQuery = query;
        whDownloadCenterService.downloading(fileName,headers, WhDownloadContentEnum.APV_OVERSIZE,isAll,domain.getPage(),(page) -> {
            List<WhApv> whApvs = whApvService.queryWhApvAndItemList(finalQuery, page);
            return getExportList(whApvs);
        });
        response.setStatus(StatusCode.SUCCESS);
        response.setMessage("导出任务已经创建，请到下载中心查看结果");
        return response;
    }

    private List<List<String>> getExportList(List<WhApv> whApvs) {
        List<List<String>> dataList = new ArrayList<>();
        List<SaleChannel> saleChannels = whApvService.getSaleChannels();
        Map<Integer, String> saleMap = saleChannels.stream().collect(Collectors.toMap(SaleChannel::getId, SaleChannel::getName, (k1, k2) -> k2));

        for (WhApv whApv : whApvs) {
            outputItems(dataList,whApv,saleMap);
        }
        return dataList;
    }

    private void outputItems(List<List<String>> data, WhApv apv,Map<Integer, String> saleMap ) {
        if (Objects.isNull(apv)) {
            return;
        }
        List<String> rowList = new ArrayList<>();
        rowList.add(POIUtils.transferObj2Str(apv.getApvNo()));
        rowList.add(POIUtils.transferObj2Str(apv.getSalesRecordNumber()));
        ApvOversize apvOversize = apv.getApvOversize();
        if(apvOversize != null){
            if(apvOversize.getLength() != null) {
                rowList.add(POIUtils.transferObj2Str(apvOversize.getLength() +" * "+ apvOversize.getWidth() +" * "+ apvOversize.getHight()));
            }else{
                rowList.add(POIUtils.transferObj2Str(""));
            }
            rowList.add(POIUtils.transferObj2Str(apvOversize.getWeight()));
            rowList.add(POIUtils.transferObj2Str(apvOversize.getStatusName()));
            rowList.add(POIUtils.transferObj2Str(apvOversize.getConfirmDate()));
        }else{
            rowList.add(POIUtils.transferObj2Str(""));
            rowList.add(POIUtils.transferObj2Str(""));
            rowList.add(POIUtils.transferObj2Str(""));
            rowList.add(POIUtils.transferObj2Str(""));
        }
        if (apv.getCreationDate() != null) {
            rowList.add(POIUtils.transferObj2Str(DateUtils.formatDate(apv.getCreationDate(), "yyyy-MM-dd HH:mm:ss")));
        } else {
            rowList.add(POIUtils.transferObj2Str(""));
        }
        if (apv.getPaidDate() != null) {
            rowList.add(POIUtils.transferObj2Str(DateUtils.formatDate(apv.getPaidDate(), "yyyy-MM-dd HH:mm:ss")));
        } else {
            rowList.add(POIUtils.transferObj2Str(""));
        }
        rowList.add(POIUtils.transferObj2Str(ApvOrderType.getNameByCode(String.valueOf(apv.getShipStatus()))));
        rowList.add(POIUtils.transferObj2Str(apv.getLogisticsCompany()));
        rowList.add(POIUtils.transferObj2Str(StringUtils.isBlank(apv.getBuyerCheckout())?"": ApvTaxTypeEnum.getNameByCode(apv.getBuyerCheckout())));
        rowList.add(POIUtils.transferObj2Str(apv.getLastModifiedTime()));
        rowList.add(apvOversize == null?null:POIUtils.transferObj2Str(apvOversize.getInterceptDate()));
        // 获取pms平台名称
        String plantFormName = "";
        if (apv.getPlatform() != null && saleMap != null) {
            plantFormName = saleMap.get(apv.getPlatform());
        }
        rowList.add(POIUtils.transferObj2Str(plantFormName));
        rowList.add(apvOversize == null?null:POIUtils.transferObj2Str(apvOversize.getRemark()));
        data.add(rowList);
    }

}