package com.estone.apv.bean;

import com.estone.sku.bean.WhUniqueSkuLog;
import lombok.Data;
import org.apache.commons.lang.StringUtils;

import java.io.Serializable;
import java.sql.Timestamp;

/**
 * 唯一码包装异常明细
 */
@Data
public class PackExceptionUuidItem implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     *  database column pack_exception_uuid_item.id
     */
    private Integer id;

    /**
     * SKU database column pack_exception_uuid_item.sku
     */
    private String sku;

    /**
     * 唯一码 database column pack_exception_uuid_item.uuid
     */
    private String uuid;

    /**
     * 异常类型 database column pack_exception_uuid_item.exception_type
     * {@link com.estone.apv.enums.PackExceptionTypeEnum }
     */
    private Integer exceptionType;

    /**
     * 扫描人 database column pack_exception_uuid_item.scan_user
     */
    private Integer scanUser;

    /**
     * 扫描界面 database column pack_exception_uuid_item.scan_page
     */
    private String scanPage;

    /**
     * 扫描时间 database column pack_exception_uuid_item.scan_time
     */
    private Timestamp scanTime;

    /**
     * 拣货任务号 database column pack_exception_uuid_item.picking_tack_no
     */
    private String pickingTackNo;

    /**
     * 已绑定的发货单 database column pack_exception_uuid_item.binding_apv_no
     */
    private String bindingApvNo;

    /**
     * 唯一码关联的WhUniqueSkuLog对象。此处是库内返架单操作日志
     */
    private WhUniqueSkuLog returnLog;


    /**
     * 用于校验UUid是否符合语法规则(即是否至少包含一个 "=" 且前后有内容),如果是的话返回true，否则返回false
     * @return
     */
    public boolean isUuidSyntax(){
        String uuId = this.getUuid();
        if (StringUtils.isNotBlank(uuId)){
            return  uuId.contains("=") && uuId.split("=").length >= 2;
        }
        return true;
    }

}