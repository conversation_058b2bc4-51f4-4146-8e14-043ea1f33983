package com.estone.apv.bean;

import lombok.Data;

import java.io.Serializable;
@Data
public class WhApvGridItem implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 
     * This field corresponds to the database column wh_apv_grid_item.id
     *
     * @mbggenerated Mon Aug 20 20:02:41 CST 2018
     */
    private Integer id;

    /**
     * 
     * This field corresponds to the database column wh_apv_grid_item.item_sku
     *
     * @mbggenerated Mon Aug 20 20:02:41 CST 2018
     */
    private String itemSku;

    /**
     * 要收集的数量
     * This field corresponds to the database column wh_apv_grid_item.item_quantity
     *
     * @mbggenerated Mon Aug 20 20:02:41 CST 2018
     */
    private Integer itemQuantity;

    /**
     * 当前格子数量
     * This field corresponds to the database column wh_apv_grid_item.grid_quantity
     *
     * @mbggenerated Mon Aug 20 20:02:41 CST 2018
     */
    private Integer gridQuantity;

    /**
     * 
     * This field corresponds to the database column wh_apv_grid_item.grid_id
     *
     * @mbggenerated Mon Aug 20 20:02:41 CST 2018
     */
    private Integer gridId;
    
    private Integer pickQuantity;// 拣货库存

    private String skuName;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getItemSku() {
        return itemSku;
    }

    public void setItemSku(String itemSku) {
        this.itemSku = itemSku;
    }

    public Integer getItemQuantity() {
        return itemQuantity;
    }

    public void setItemQuantity(Integer itemQuantity) {
        this.itemQuantity = itemQuantity;
    }

    public Integer getGridQuantity() {
        return gridQuantity;
    }

    public void setGridQuantity(Integer gridQuantity) {
        this.gridQuantity = gridQuantity;
    }

    public Integer getPickQuantity() {
        return pickQuantity;
    }

    public void setPickQuantity(Integer pickQuantity) {
        this.pickQuantity = pickQuantity;
    }

    public Integer getGridId() {
        return gridId;
    }

    public void setGridId(Integer gridId) {
        this.gridId = gridId;
    }
}