package com.estone.apv.bean;

import java.util.ArrayList;
import java.util.List;

public class WhApvBatchQueryCondition extends WhApvBatch {
    
    private static final long serialVersionUID = 1L;
    
    private Integer itemId;
    
    private Integer warehouseId;
    
    private String sku;
    
    
    /**
     * 多个sku 同时满足
     */
    private String skusAnd;
    
    /**
     * 未使用过的订单
     */
    private List<Integer> notInWhApvIdList = new ArrayList<Integer>();

    private List<Integer> apvIdList = new ArrayList<>();
    
    /**
     * 批次状态
     */
    private List<Integer> batchStatusList = new ArrayList<Integer>();
    
    /**
     * 订单
     */
    private Integer apvId;
    
    /**
     * 发货单状态
     */
    private Integer status;
    
    /**
     * 仓库列表
     */
    private List<Integer> warehouseIdList = new ArrayList<Integer>();
    
    /**
     * 创建时间
     */
    private String fromCreationDate;
    
    private String toCreationDate;
    
    /**
     * 批次列表
     */
    private List<Integer> batchIdList = new ArrayList<Integer>();
    
    

    public String getSkusAnd() {
        return skusAnd;
    }

    public void setSkusAnd(String skusAnd) {
        this.skusAnd = skusAnd;
    }

    public Integer getItemId() {
        return itemId;
    }

    public void setItemId(Integer itemId) {
        this.itemId = itemId;
    }

    public Integer getWarehouseId() {
        return warehouseId;
    }

    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    public String getSku() {
        return sku;
    }

    public void setSku(String sku) {
        this.sku = sku;
    }

    public List<Integer> getNotInWhApvIdList() {
        return notInWhApvIdList;
    }

    public void setNotInWhApvIdList(List<Integer> notInWhApvIdList) {
        this.notInWhApvIdList = notInWhApvIdList;
    }

    public List<Integer> getApvIdList() {
        return apvIdList;
    }

    public void setApvIdList(List<Integer> apvIdList) {
        this.apvIdList = apvIdList;
    }

    public List<Integer> getBatchStatusList() {
        return batchStatusList;
    }

    public void setBatchStatusList(List<Integer> batchStatusList) {
        this.batchStatusList = batchStatusList;
    }

    public Integer getApvId() {
        return apvId;
    }

    public void setApvId(Integer apvId) {
        this.apvId = apvId;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public List<Integer> getWarehouseIdList() {
        return warehouseIdList;
    }

    public void setWarehouseIdList(List<Integer> warehouseIdList) {
        this.warehouseIdList = warehouseIdList;
    }

    public String getFromCreationDate() {
        return fromCreationDate;
    }

    public void setFromCreationDate(String fromCreationDate) {
        this.fromCreationDate = fromCreationDate;
    }

    public String getToCreationDate() {
        return toCreationDate;
    }

    public void setToCreationDate(String toCreationDate) {
        this.toCreationDate = toCreationDate;
    }

    public List<Integer> getBatchIdList() {
        return batchIdList;
    }

    public void setBatchIdList(List<Integer> batchIdList) {
        this.batchIdList = batchIdList;
    }
    
    
}