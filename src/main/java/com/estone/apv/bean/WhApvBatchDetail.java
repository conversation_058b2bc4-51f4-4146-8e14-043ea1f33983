package com.estone.apv.bean;

import java.io.Serializable;

public class WhApvBatchDetail implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 
     * This field corresponds to the database column wh_apv_batch_detail.id
     *
     * @mbggenerated Wed Aug 15 11:11:08 CST 2018
     */
    private Integer id;

    /**
     * 
     * This field corresponds to the database column wh_apv_batch_detail.batch_id
     *
     * @mbggenerated Wed Aug 15 11:11:08 CST 2018
     */
    private Integer batchId;

    /**
     * 
     * This field corresponds to the database column wh_apv_batch_detail.apv_id
     *
     * @mbggenerated Wed Aug 15 11:11:08 CST 2018
     */
    private Integer apvId;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getBatchId() {
        return batchId;
    }

    public void setBatchId(Integer batchId) {
        this.batchId = batchId;
    }

    public Integer getApvId() {
        return apvId;
    }

    public void setApvId(Integer apvId) {
        this.apvId = apvId;
    }
}