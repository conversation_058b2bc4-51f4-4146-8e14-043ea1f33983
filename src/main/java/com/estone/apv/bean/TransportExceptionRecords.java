package com.estone.apv.bean;

import java.io.Serializable;
import java.sql.Timestamp;

import com.whq.tool.context.DataContextHolder;
import lombok.Data;

@Data
public class TransportExceptionRecords implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键 database column transport_exception_records.id
     */
    private Integer id;

    /**
     * 扫描单号 database column transport_exception_records.scan_order_number
     */
    private String scanOrderNumber;

    /**
     * 发货单号 database column transport_exception_records.shipping_order_number
     */
    private String shippingOrderNumber;

    /**
     * 出库类型    (本地仓,JIT, 海外仓, FBA, 中转仓, 拼多多) database column transport_exception_records.warehouse_type
     */
    private Integer warehouseType;

    /**
     * 交运工具 database column transport_exception_records.transport_tool
     */
    private String transportTool;

    /**
     * 扫描人 database column transport_exception_records.scan_user
     */
    private Integer scanUser;

    /**
     * 扫描时间 database column transport_exception_records.scan_time
     */
    private Timestamp scanTime;

    /**
     * 异常明细 database column transport_exception_records.error_message
     */
    private String errorMessage;

    public TransportExceptionRecords(){}

    public TransportExceptionRecords(String scanOrderNumber, String shippingOrderNumber, Integer warehouseType, String transportTool,String errorMessage){
        this.scanOrderNumber=scanOrderNumber;
        this.shippingOrderNumber=shippingOrderNumber;
        this.warehouseType=warehouseType;
        this.transportTool=transportTool;
        this.errorMessage=errorMessage;
        if (scanUser==null){
            this.scanUser=DataContextHolder.getUserId();
        }
        if (scanTime==null){
            this.scanTime=new Timestamp(System.currentTimeMillis());
        }
    }
}