package com.estone.apv.bean;

import java.io.Serializable;

public class WhApvBatchItem implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 
     * This field corresponds to the database column wh_apv_batch_item.id
     *
     * @mbggenerated Wed Aug 15 11:11:26 CST 2018
     */
    private Integer id;

    /**
     * 
     * This field corresponds to the database column wh_apv_batch_item.batch_id
     *
     * @mbggenerated Wed Aug 15 11:11:26 CST 2018
     */
    private Integer batchId;

    /**
     * 仓库
     * This field corresponds to the database column wh_apv_batch_item.warehouse_id
     *
     * @mbggenerated Wed Aug 15 11:11:26 CST 2018
     */
    private Integer warehouseId;

    /**
     * 
     * This field corresponds to the database column wh_apv_batch_item.item_status
     *
     * @mbggenerated Wed Aug 15 11:11:26 CST 2018
     */
    private Integer itemStatus;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getBatchId() {
        return batchId;
    }

    public void setBatchId(Integer batchId) {
        this.batchId = batchId;
    }

    public Integer getWarehouseId() {
        return warehouseId;
    }

    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    public Integer getItemStatus() {
        return itemStatus;
    }

    public void setItemStatus(Integer itemStatus) {
        this.itemStatus = itemStatus;
    }
}