package com.estone.apv.common;

/**
 * 
 * @ClassName: ApvLockType
 * @Description: apv类型
 * <AUTHOR>
 * @date 2018年8月15日
 * @version 0.0.1
 *
 */
public enum ApvLockType {

    WHLOCK("1", "有锁"),

    WHUNLOCK("2", "没锁"),

    ;

    private String code;

    private String name;

    private ApvLockType(String code, String name) {
        this.name = name;
        this.code = code;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public int intCode() {
        return Integer.valueOf(this.code).intValue();
    }
}
