package com.estone.apv.common;

import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * 
 * @Description:
 * 
 * @ClassName: ApvType
 * @Author: qinyangkai
 * @Date: 2018/11/29
 * @Version: 0.0.1
 */
public enum ApvOrderType {
    BASICS("0", "普通", 99),

    SECURITY_CHECK_REFUND("1", "安检退件", 0),

    EXPRESS_BUSINESS("2", "快递业务", 4),

    FBA("3", "FBA", 99),

    VIRTUAL_OVERSEA("4", "虚拟海外仓", 8),

    REISSUE_PARTS("5", "补发配件", 9),

    NOT_STOCKED("6", "不备货", 99),

    /* SPECIAL_PURCHASE("7", "特采"), */

    CLIPPING("8", "剪标", 99),

    FBA_NOT_STOCKED("9", "FBA不备货", 99),

    /* FBA_SPECIAL_PURCHASE("10", "FBA特采"), */

    FBA_CLIPPING("11", "FBA剪标", 99),

    EXPRESS_NOT_STOCKED("12", "快递不备货", 2),

    /* EXPRESS_SPECIAL_PURCHASE("13", "快递特采"), */

    EXPRESS_CLIPPING("14", "快递剪标", 3),

    VALUABLES("15", "批发订单", 6),

    OPTIMAL("16","优选仓", 99),

    OPTIMAL_JB("17","优选仓集包", 99),

    OVERSIZE("18", "超体积", 5),

    PACKING("19", "装箱订单", 7),

    BALE("20", "大包发货", 1),

    LACK_MERCHANDISE("21","缺货订单", 0),
    JIT_FULL_MANAGEMENT("22","JIT全托管", 0),
    JIT_HALF_MANAGEMENT("23","JIT半托管", 0),
    ;

    private String code;

    private String name;

    private Integer priority;

    private ApvOrderType(String code, String name, Integer priority) {
        this.name = name;
        this.code = code;
        this.priority = priority;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public int intCode() {
        return Integer.valueOf(this.code).intValue();
    }

    public Integer getPriority() {
        return priority;
    }

    public void setPriority(Integer priority) {
        this.priority = priority;
    }

    public static String getNameByCode(String code) {

        if (StringUtils.isEmpty(code)) {
            return BASICS.getName();
        }

        ApvOrderType[] values = values();
        for (ApvOrderType type : values) {
            if (type.code.equals(code)) {
                return type.getName();
            }
        }
        return null;
    }

    /**
     * 获取特殊发货类型
     * 
     * @return
     */
    public static List<Integer> getSpecialApvDeliveryTypes() {
        List<Integer> apvDeliveryTypes = new ArrayList<Integer>();

        ApvOrderType[] values = ApvOrderType.values();
        for (ApvOrderType apvDeliveryType : values) {

            // 排除基础类型
            if (ApvOrderType.BASICS.intCode() != apvDeliveryType.intCode()
                    && ApvOrderType.CLIPPING.intCode() != apvDeliveryType.intCode()
                    && ApvOrderType.NOT_STOCKED.intCode() != apvDeliveryType.intCode()) {
                apvDeliveryTypes.add(apvDeliveryType.intCode());
            }
        }

        return apvDeliveryTypes;
    }

    public static ApvOrderType build(String code) {
        ApvOrderType[] values = values();

        for (ApvOrderType type : values) {
            if (type.code.equals(code)) {
                return type;
            }
        }

        return null;
    }

    public static List<Integer> getBasicsIntCode() {
        List<Integer> apvDeliveryTypes = new ArrayList<Integer>();

        apvDeliveryTypes.add(ApvOrderType.NOT_STOCKED.intCode());
        apvDeliveryTypes.add(ApvOrderType.CLIPPING.intCode());
        apvDeliveryTypes.add(ApvOrderType.BASICS.intCode());
        apvDeliveryTypes.add(ApvOrderType.OPTIMAL.intCode());
        apvDeliveryTypes.add(ApvOrderType.OPTIMAL_JB.intCode());
        apvDeliveryTypes.add(ApvOrderType.VIRTUAL_OVERSEA.intCode());
        apvDeliveryTypes.add(ApvOrderType.VALUABLES.intCode());
        apvDeliveryTypes.add(ApvOrderType.OVERSIZE.intCode());
        apvDeliveryTypes.add(ApvOrderType.PACKING.intCode());
        apvDeliveryTypes.add(ApvOrderType.BALE.intCode());
        apvDeliveryTypes.add(ApvOrderType.LACK_MERCHANDISE.intCode());
        apvDeliveryTypes.add(ApvOrderType.JIT_FULL_MANAGEMENT.intCode());
        apvDeliveryTypes.add(ApvOrderType.JIT_HALF_MANAGEMENT.intCode());
        return apvDeliveryTypes;
    }

    public static List<Integer> getFbaIntCode() {
        List<Integer> apvDeliveryTypes = new ArrayList<Integer>();
        apvDeliveryTypes.add(ApvOrderType.FBA.intCode());
        apvDeliveryTypes.add(ApvOrderType.FBA_CLIPPING.intCode());
        apvDeliveryTypes.add(ApvOrderType.FBA_NOT_STOCKED.intCode());
        return apvDeliveryTypes;
    }

    public static List<Integer> getExpressIntCode() {
        List<Integer> apvDeliveryTypes = new ArrayList<Integer>();
        apvDeliveryTypes.add(ApvOrderType.EXPRESS_BUSINESS.intCode());
        apvDeliveryTypes.add(ApvOrderType.EXPRESS_CLIPPING.intCode());
        apvDeliveryTypes.add(ApvOrderType.EXPRESS_NOT_STOCKED.intCode());
        apvDeliveryTypes.add(ApvOrderType.BALE.intCode());
        return apvDeliveryTypes;
    }

    public static List<Integer> getOptimalIntCode() {
        List<Integer> apvDeliveryTypes = new ArrayList<Integer>();
        apvDeliveryTypes.add(ApvOrderType.OPTIMAL.intCode());
        apvDeliveryTypes.add(ApvOrderType.OPTIMAL_JB.intCode());
        return apvDeliveryTypes;
    }

    public static List<Integer> getJitIntCode() {
        List<Integer> apvDeliveryTypes = new ArrayList<Integer>();
        apvDeliveryTypes.add(ApvOrderType.JIT_FULL_MANAGEMENT.intCode());
        apvDeliveryTypes.add(ApvOrderType.JIT_HALF_MANAGEMENT.intCode());
        return apvDeliveryTypes;
    }
}
