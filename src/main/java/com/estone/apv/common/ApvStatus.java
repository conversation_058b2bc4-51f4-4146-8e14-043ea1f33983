package com.estone.apv.common;

/**
 * 
 * @ClassName: ApvStatus
 * @Description: apv类型
 * <AUTHOR>
 * @date 2018年8月15日
 * @version 0.0.1
 *
 */
public enum ApvStatus {

    WAITING_ALLOT("1", "待分配"),

    CANCEL("2", "取消发货"),

    STOCKOUT_NOT("4", "缺货"),

    ALLOT("6", "已分配"),

    PICKING_STOCKOUT_NOT("8", "拣货缺货"),

    SINGLETON_TOUCHING("10", "单件合单"),

    EXCESSIVE_PARTS_TOUCHING("12", "多件合单"),

    /**
     * 多品合单
     */
    MULTI_TOUCHING("14", "多品合单"),

    CHECK_PRINT("15", "待包装"),

    WAITING_DELIVER("16", "等待发货"),

    /**
     * 已发货
     */
    DELIVER("17", "已交运"),

    LOADED("18", "已装车");

    private String code;

    private String name;

    private ApvStatus(String code, String name) {
        this.name = name;
        this.code = code;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public int intCode() {
        return Integer.valueOf(this.code).intValue();
    }

    public boolean equals(Integer status) {
        return Integer.valueOf(this.code).equals(status);
    }

    public static ApvStatus build(String code) {
        ApvStatus[] values = values();

        for (ApvStatus type : values) {
            if (type.code.equals(code)) {
                return type;
            }
        }

        return null;
    }

    public static String getNameByCode(String code) {
        ApvStatus[] values = values();
        for (ApvStatus type : values) {
            if (type.code.equals(code)) {
                return type.getName();
            }
        }
        return null;
    }

    /**
     * 不操作库存的发货单状态
     * @return
     */
    public static int[] notStock() {
        int[] not = { WAITING_ALLOT.intCode(), CANCEL.intCode(), STOCKOUT_NOT.intCode(), PICKING_STOCKOUT_NOT.intCode() };
        return not;
    }

    public static Integer[] getStockStatus() {
        Integer[] stockStatus = { ALLOT.intCode(), PICKING_STOCKOUT_NOT.intCode(), SINGLETON_TOUCHING.intCode(),
                EXCESSIVE_PARTS_TOUCHING.intCode(), MULTI_TOUCHING.intCode(), CHECK_PRINT.intCode(),
                WAITING_DELIVER.intCode() };
        return stockStatus;
    }

    public static Integer[] getTouchingStatus() {
        Integer[] touchingStatus = { SINGLETON_TOUCHING.intCode(), EXCESSIVE_PARTS_TOUCHING.intCode(),
                MULTI_TOUCHING.intCode() };
        return touchingStatus;
    }
}
