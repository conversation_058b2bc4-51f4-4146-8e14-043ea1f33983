
package com.estone.apv.common;

public enum ApvBatchItemStatus {
    PENDING("1", "待处理"),

    COMPLETED("2", "已完成"),

    /**
     * 强制完成
     */
    FORCE_COMPLETED("3", "强制完成"),;

    private String code;

    private String display;

    private ApvBatchItemStatus(String code, String display) {
        this.code = code;
        this.display = display;
    }

    /**
     * 根据code构造枚举
     * 
     * <p>
     * TODO 方法功能描述
     * 
     * @param code
     * @return
     * @return EbayCategories
     */
    public static ApvBatchItemStatus build(String code) {
        ApvBatchItemStatus[] values = values();

        for (ApvBatchItemStatus type : values) {
            if (type.code.equals(code)) {
                return type;
            }
        }

        return null;
    }

    public int intCode() {
        return Integer.valueOf(this.code).intValue();
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getDisplay() {
        return display;
    }

    public void setDisplay(String display) {
        this.display = display;
    }

}