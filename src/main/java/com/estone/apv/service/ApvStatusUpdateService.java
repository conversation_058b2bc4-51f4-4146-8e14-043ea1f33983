package com.estone.apv.service;

import com.estone.apv.bean.WhApv;
import com.estone.transfer.bean.WhFbaAllocation;
import com.whq.tool.json.ResponseJson;

import java.util.Date;
import java.util.List;

/**
 * @Description 发货单改状态服务接口
 * <AUTHOR>
 * @Date 2019/12/17 14:30
 **/
public interface ApvStatusUpdateService {

    /**
     * @Description 取消发货
     * @param whApv
     * <AUTHOR>
     * @Date 2019/12/17 14:32
     **/
    boolean cancel(WhApv whApv);

    /**
     * @Description 改已分配
     * @param whApvList
     * <AUTHOR>
     * @Date 2019/12/17 14:32
     **/
    void allot(List<WhApv> whApvList);
    void allotOutStockApv(List<WhApv> whApvList);

    /**
     * @Description 改待分配
     * @param whApvList
     * <AUTHOR>
     * @Date 2019/12/17 14:32
     **/
    void waitingAllot(List<WhApv> whApvList);

    /**
     * 改为待分配
     * @param whApvList 待分配订单列表
     * @param remark 改为待分配的原因
     */
    void waitingAllot(List<WhApv> whApvList, String remark);

    /**
     * @Description 改交运 JMS
     * @param whApv
     * <AUTHOR>
     * @Date 2019/12/17 14:34
     **/
    void deliverByJms(WhApv whApv);

    /**
     * @Description 改交运 手动按钮
     * @param whApv
     * <AUTHOR>
     * @Date 2019/12/17 14:34
     **/
    void deliverByBtn(WhApv whApv);

    boolean deliverWhApvStatus(Integer id, int sum,Double  actualWeight);

    void rollbackWhApvStatus(Integer apvId,int count,Integer status);

    void retryDeliver(WhApv apv,  Date date);

    /**
     * jit 发货单分配
     * @param whFbaAllocation
     */
    ResponseJson createAndAllotJitApv(List<String> skuList, WhFbaAllocation whFbaAllocation);

    //拆分单修改状态
    boolean splitApvStatus(WhApv whApv, boolean isCancel);
}
