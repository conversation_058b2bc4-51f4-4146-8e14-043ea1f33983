package com.estone.apv.service.impl;

import com.alibaba.fastjson.JSON;
import com.estone.apv.bean.*;
import com.estone.apv.common.ApvOrderType;
import com.estone.apv.common.ApvStatus;
import com.estone.apv.dao.ApvOversizeDao;
import com.estone.apv.enums.ApvOversizeStatus;
import com.estone.apv.enums.ApvTaxTypeEnum;
import com.estone.apv.service.ApvOversizeService;
import com.estone.apv.service.WhApvService;
import com.estone.common.enums.LogModule;
import com.estone.common.executors.ExecutorUtils;
import com.estone.common.util.CacheUtils;
import com.estone.common.util.HttpUtils;
import com.estone.common.util.SystemLogUtils;
import com.estone.common.util.model.ApiResult;
import com.estone.scan.deliver.bean.DeliverDTO;
import com.estone.system.param.bean.SystemParam;
import com.whq.tool.component.Pager;
import com.whq.tool.component.StatusCode;
import com.whq.tool.exception.BusinessException;
import com.whq.tool.exception.DBErrorCode;
import com.whq.tool.json.ResponseJson;
import com.whq.tool.sqler.SqlerException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.ExecutorService;

@Service("apvOversizeService")
@Slf4j
public class ApvOversizeServiceImpl implements ApvOversizeService {

    final static SystemLogUtils WH_APV_LOG = SystemLogUtils.create(LogModule.WHAPV.getCode());

    final static SystemLogUtils SCANSHIPMENTLOG = SystemLogUtils.create(LogModule.SCANSHIPMENT.getCode());

    @Resource
    private WhApvService whApvService;

    @Resource
    private ApvOversizeDao apvOversizeDao;

    private final static ExecutorService executors = ExecutorUtils.newFixedThreadPool(5);

    @Override
    public ApvOversize getApvOversize(Integer id) {
        ApvOversize ApvOversize = apvOversizeDao.queryApvOversize(id);
        return ApvOversize;
    }

    @Override
    public ApvOversize getApvOversizeDetail(Integer id) {
        ApvOversize ApvOversize = apvOversizeDao.queryApvOversize(id);
        // 关联查询
        return ApvOversize;
    }

    @Override
    public ApvOversize queryApvOversize(ApvOversizeQueryCondition query) {
        Assert.notNull(query, "query is null!");
        ApvOversize ApvOversize = apvOversizeDao.queryApvOversize(query);
        return ApvOversize;
    }

    @Override
    public List<ApvOversize> queryAllApvOversizes() {
        return apvOversizeDao.queryApvOversizeList();
    }

    @Override
    public List<ApvOversize> queryApvOversizes(ApvOversizeQueryCondition query, Pager pager) {
        Assert.notNull(query, "query is null!");
        if (pager != null && pager.isQueryCount()) {
            int count = apvOversizeDao.queryApvOversizeCount(query);
            pager.setTotalCount(count);
            if (count == 0) {
                return new ArrayList<ApvOversize>();
            }
        }
        List<ApvOversize> ApvOversizes = apvOversizeDao.queryApvOversizeList(query, pager);
        return ApvOversizes;
    }

    @Override
    public void createApvOversize(ApvOversize apvOversize) {
        try {
            apvOversizeDao.createApvOversize(apvOversize);
        } catch (SqlerException e) {
            log.error(e.getMessage(), e);
            throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
        }
    }

    @Override
    public void batchCreateApvOversize(List<ApvOversize> entityList) {
        if (CollectionUtils.isNotEmpty(entityList)) {
            try {
                apvOversizeDao.batchCreateApvOversize(entityList);
            } catch (SqlerException e) {
                log.error(e.getMessage(), e);
                throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
            }
        }
    }

    @Override
    public void deleteApvOversize(Integer id) {
        try {
            apvOversizeDao.deleteApvOversize(id);
        } catch (SqlerException e) {
            log.error(e.getMessage(), e);
            throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
        }
    }

    @Override
    public void updateApvOversize(ApvOversize apvOversize) {
        try {
            apvOversizeDao.updateApvOversize(apvOversize);
        } catch (SqlerException e) {
            log.error(e.getMessage(), e);
            throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
        }
    }

    @Override
    public void batchUpdateApvOversize(List<ApvOversize> entityList) {
        if (CollectionUtils.isNotEmpty(entityList)) {
            try {
                apvOversizeDao.batchUpdateApvOversize(entityList);
            } catch (SqlerException e) {
                log.error(e.getMessage(), e);
                throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
            }
        }
    }

    @Override
    public int updateApvOversizeByApvNo(ApvOversize entity) {
        try {
            return apvOversizeDao.updateApvOversizeByApvNo(entity);
        } catch (SqlerException e) {
            log.error(e.getMessage(), e);
            throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
        }
    }

    @Override
    public int resetApvOversize(ApvOversize entity) {
        try {
            return apvOversizeDao.resetApvOversize(entity);
        } catch (SqlerException e) {
            log.error(e.getMessage(), e);
            throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
        }
    }

    @Override
    public ResponseJson updateVolumeAndWeight(ApvOversize apvOversize,boolean limited) {
        ResponseJson response = new ResponseJson(StatusCode.FAIL);
        if(StringUtils.isBlank(apvOversize.getApvNo())){
            response.setMessage("发货单号不能为空");
            return response;
        }
        WhApvQueryCondition query = new WhApvQueryCondition();
        query.setApvNo(apvOversize.getApvNo());
//        query.setOversize(true);
        List<WhApv> apvList = whApvService.queryWhApvAndItemList(query, null);
        if(CollectionUtils.isEmpty(apvList)){
            response.setMessage("查无此发货单：" + apvOversize.getApvNo());
        }
        WhApv whApv = apvList.get(0);
        if (!ApvStatus.WAITING_DELIVER.equals(whApv.getStatus())) {
            response.setMessage("发货单不是等待发货状态！");
            return response;
        }
        ApvOversizeQueryCondition condition = new ApvOversizeQueryCondition();
        condition.setApvNo(whApv.getApvNo());
        ApvOversize dbOversize = this.queryApvOversize(condition);
        if(dbOversize == null || dbOversize.getId() == null){
            dbOversize = new ApvOversize();
            dbOversize.setApvNo(apvOversize.getApvNo());
            dbOversize.setStatus(ApvOversizeStatus.UN_PUSH.intCode());
        }else if(limited){
            if(ApvOversizeStatus.UN_CONFIRM.intCode().equals(dbOversize.getStatus())
                    || ApvOversizeStatus.CONFIRM.intCode().equals(dbOversize.getStatus())){
                response.setMessage("超体积发货单已推送或已确认发货！");
                return response;
            }
        }
        dbOversize.setWeight(apvOversize.getWeight());
        dbOversize.setLength(apvOversize.getLength());
        dbOversize.setWidth(apvOversize.getWidth());
        dbOversize.setHight(apvOversize.getHight());
        dbOversize.setRemark(apvOversize.getRemark());
        if(dbOversize.getId() == null){
            apvOversizeDao.createApvOversize(dbOversize);
        }else{
            dbOversize.setStatus(ApvOversizeStatus.UN_PUSH.intCode());
            apvOversizeDao.updateApvOversize(dbOversize);
        }
        dbOversize.setNewApvNo(whApv.getPaymentStatus());
        SystemLogUtils.APVLOG.log(whApv.getId(), "更新超体积发货单尺寸重量备注-" + JSON.toJSONString(dbOversize));
        // 推送体积重量信息至OMS
        dbOversize.setApvId(whApv.getId());
        if(whApv.getShipStatus() == ApvOrderType.PACKING.intCode()){
            dbOversize.setType(1);
        }
        if(StringUtils.isNotBlank(whApv.getBuyerCheckout())){
            if (ApvTaxTypeEnum.OVERSIZE.getCode().equals(whApv.getBuyerCheckout())){
                dbOversize.setType(2);
            }else if(ApvTaxTypeEnum.OVERWEIGHT.getCode().equals(whApv.getBuyerCheckout())){
                dbOversize.setType(3);
            }
        }
        if(apvOversize == null || !apvOversize.canPush()){
            response.setStatus(StatusCode.SUCCESS);
            response.setMessage("保存成功, 发货单体积重量信息为空不推送订单系统");
            return response;
        }
        return this.pushToOms(dbOversize);
    }

    /**
     * 交运超重超体积拦截保存、推送OMS
     */
    @Override
    public void saveAndPush(DeliverDTO deliverDTO, String apvTaxType) {
        executors.submit(() -> {
            try {
                WhApv whApv = deliverDTO.getWhApv();
                if (StringUtils.isBlank(whApv.getApvNo())) {
                    log.error("交运超重超体积拦截出库单号缺失");
                    return;
                }
                WhApvQueryCondition apvCondition = new WhApvQueryCondition();
                apvCondition.setApvNo(whApv.getApvNo());
                WhApv dbApv = whApvService.queryWhApv(apvCondition);
                if(dbApv == null){
                    log.error("交运超重超体积拦截未找到出库单");
                    return;
                }
                ApvOversizeQueryCondition condition = new ApvOversizeQueryCondition();
                condition.setApvNo(whApv.getApvNo());
                ApvOversize oversize = this.queryApvOversize(condition);
                if (oversize == null || oversize.getId() == null) {
                    oversize = new ApvOversize();
                    oversize.setApvNo(whApv.getApvNo());
                }
                oversize.setWeight(deliverDTO.getActualWeight());
                if(deliverDTO.getLength() != null || deliverDTO.getWidth() != null || deliverDTO.getHeight() != null) {
                    oversize.setLength(deliverDTO.getLength());
                    oversize.setWidth(deliverDTO.getWidth());
                    oversize.setHight(deliverDTO.getHeight());
                }
                oversize.setInterceptDate(new Timestamp(System.currentTimeMillis()));
                oversize.setStatus(ApvOversizeStatus.UN_PUSH.intCode());
                oversize.setNewApvNo(dbApv.getPaymentStatus());
                if (ApvTaxTypeEnum.OVERSIZE.getCode().equals(apvTaxType)){
                    oversize.setType(2);
                }else if(ApvTaxTypeEnum.OVERWEIGHT.getCode().equals(apvTaxType)){
                    oversize.setType(3);
                }
                if (oversize.getId() == null) {
                    apvOversizeDao.createApvOversize(oversize);
                } else {
                    apvOversizeDao.updateApvOversize(oversize);
                }
                oversize.setApvId(dbApv.getId());
                // apv更新拦截标签
                ApvTaxTypeEnum addBuyerCheckout = ApvTaxTypeEnum.build(apvTaxType);
                WhApv updateApv = new WhApv();
                updateApv.setId(dbApv.getId());
                updateApv.setBuyerCheckout(dbApv.getBuyerCheckout());
                if(updateApv.addBuyerCheckout(addBuyerCheckout)) {
                    whApvService.updateWhApv(updateApv);
                }
                // 推送
                this.pushToOms(oversize);
            }catch (Exception e){
                log.error(e.getMessage(), e);
            }
        });
    }

    /**
     *  推送体积重量信息至OMS
     * @param apvOversize
     * @return
     */
    @Override
    public ResponseJson pushToOms(ApvOversize apvOversize) {
        ResponseJson response = new ResponseJson(StatusCode.FAIL);
        try {
            SystemParam param = CacheUtils.SystemParamGet("OMS_PARAM.PUSH_VOLUME_WEIGHT_OVERSIZE");
            if (param == null || StringUtils.isBlank(param.getParamValue())) {
                response.setMessage("未配置推送超体积发货单尺寸和重量请求地址!");
                return response;
            }
            ApvExpressBoxInfo boxInfo = new ApvExpressBoxInfo();
            BeanUtils.copyProperties(apvOversize, boxInfo);
            ApvExpress express = new ApvExpress();
            express.setApvNo(apvOversize.getApvNo());
            if (StringUtils.isNotBlank(apvOversize.getNewApvNo())) {
                express.setApvNo(apvOversize.getNewApvNo());
            }
            express.setWeight(apvOversize.getWeight());
            express.setType(apvOversize.getType());
            express.setBoxInfoJson(JSON.toJSONString(Collections.singletonList(boxInfo)));
            log.info("push apvOversize to OMS request param:{}", JSON.toJSONString(express));
            ApiResult result = HttpUtils.post(param.getParamValue(), HttpUtils.ACCESS_TOKEN, express, ApiResult.class);
            log.info("push apvOversize to OMS result:{}", JSON.toJSONString(result));
            if(result.isSuccess()){
                ApvOversize updateEntity = new ApvOversize();
                updateEntity.setApvNo(apvOversize.getApvNo());
                updateEntity.setStatus(ApvOversizeStatus.UN_CONFIRM.intCode());
                if(apvOversizeDao.updateApvOversizeByApvNo(updateEntity) > 0){
                    SystemLogUtils.APVLOG.log(apvOversize.getApvId(), "推送超体积发货单尺寸重量到OMS");
                    response.setStatus(StatusCode.SUCCESS);
                }else{
                    log.error("数据库更新失败: apvNo[{}]", apvOversize.getApvNo());
                    response.setMessage("数据库更新失败!");
                }
            }else{
                log.error(result.getErrorMsg());
                response.setMessage(result.getErrorMsg());
            }
        }catch (Exception e){
            log.error("push apvOversize to OMS error:", e);
        }
        return response;
    }

    /**
     * 保存并推送
     * @param apvOversize
     * @param deliverDTO
     */
    @Override
    public void saveAndPush(ApvOversize apvOversize,DeliverDTO deliverDTO){
        executors.submit(() -> {
            WhApvQueryCondition apvCondition = new WhApvQueryCondition();
            apvCondition.setApvNo(apvOversize.getApvNo());
            WhApv dbApv = whApvService.queryWhApv(apvCondition);
            if(dbApv == null){
                log.error("交运超重超体积拦截未找到出库单");
                return;
            }
            ApvOversize oversize = new ApvOversize();
            oversize.setApvNo(apvOversize.getApvNo());
            oversize.setWeight(deliverDTO.getActualWeight());
            if(deliverDTO.getLength() != null || deliverDTO.getWidth() != null || deliverDTO.getHeight() != null) {
                oversize.setLength(deliverDTO.getLength());
                oversize.setWidth(deliverDTO.getWidth());
                oversize.setHight(deliverDTO.getHeight());
            }
            oversize.setStatus(ApvOversizeStatus.CONFIRM.intCode());
            if(apvOversizeDao.updateApvOversizeByApvNo(oversize) > 0){
                // apv更新拦截标签
               /* WhApv updateApv = new WhApv();
                updateApv.setId(dbApv.getId());
                dbApv.remBuyerCheckout(ApvTaxTypeEnum.OVERSIZE);
                dbApv.remBuyerCheckout(ApvTaxTypeEnum.OVERWEIGHT);
                updateApv.setBuyerCheckout(dbApv.getBuyerCheckout());
                whApvService.updateWhApv(updateApv);*/
                SystemLogUtils.APVLOG.log(dbApv.getId(), "更新超体积发货单尺寸重量信息");
                pushConfirmToOms(oversize);
            }else{
                log.error("数据库更新失败: apvNo[{}]", apvOversize.getApvNo());

            }
        });
    }
    // 推送待销售确认给OMS
    @Override
    public void pushConfirmToOms(ApvOversize apvOversize) {
            try {
                SystemParam param = CacheUtils.SystemParamGet("OMS_PARAM.PUSH_VOLUME_WEIGHT_OVERSIZE");
                if (param == null || StringUtils.isBlank(param.getParamValue())) {
                    log.warn("未配置推送超体积发货单尺寸和重量请求地址!");
                }
                ApvExpress express = new ApvExpress();
                express.setApvNo(apvOversize.getApvNo());
                if (StringUtils.isNotBlank(apvOversize.getNewApvNo())) {
                    express.setApvNo(apvOversize.getNewApvNo());
                }
                express.setWeight(apvOversize.getWeight());
                express.setStatus(ApvOversizeStatus.CONFIRM.intCode());
                log.info("pushConfirmToOms to OMS request param:{}", JSON.toJSONString(express));
                ApiResult result = HttpUtils.post(param.getParamValue(), HttpUtils.ACCESS_TOKEN, express, ApiResult.class);
                log.info("pushConfirmToOms to OMS result:{}", JSON.toJSONString(result));
            } catch (Exception e) {
                log.error("pushConfirmToOms to OMS error:", e);
            }
    }

}