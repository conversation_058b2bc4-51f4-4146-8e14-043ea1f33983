package com.estone.apv.service.impl;

import java.util.ArrayList;
import java.util.List;

import javax.annotation.Resource;

import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import com.estone.apv.bean.WhApvBatchItem;
import com.estone.apv.bean.WhApvBatchItemQueryCondition;
import com.estone.apv.dao.WhApvBatchItemDao;
import com.estone.apv.service.WhApvBatchItemService;
import com.whq.tool.component.Pager;
import com.whq.tool.exception.BusinessException;
import com.whq.tool.exception.DBErrorCode;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import com.whq.tool.sqler.SqlerException;

@Service("whApvBatchItemService")
public class WhApvBatchItemServiceImpl implements WhApvBatchItemService {
    private static final Logger logger = LoggerFactory.getLogger(WhApvBatchItemServiceImpl.class);

    @Resource
    private WhApvBatchItemDao whApvBatchItemDao;

    /**
     * This method corresponds to the database table wh_apv_batch_item
     *
     * @mbggenerated Wed Aug 15 11:11:26 CST 2018
     */
    public WhApvBatchItem getWhApvBatchItem(Integer id) {
        WhApvBatchItem whApvBatchItem = whApvBatchItemDao.queryWhApvBatchItem(id);
        return whApvBatchItem;
    }

    /**
     * This method corresponds to the database table wh_apv_batch_item
     *
     * @mbggenerated Wed Aug 15 11:11:26 CST 2018
     */
    public WhApvBatchItem getWhApvBatchItemDetail(Integer id) {
        WhApvBatchItem whApvBatchItem = whApvBatchItemDao.queryWhApvBatchItem(id);
        // 关联查询
        return whApvBatchItem;
    }

    /**
     * This method corresponds to the database table wh_apv_batch_item
     *
     * @mbggenerated Wed Aug 15 11:11:26 CST 2018
     */
    public WhApvBatchItem queryWhApvBatchItem(WhApvBatchItemQueryCondition query) {
        Assert.notNull(query);
        WhApvBatchItem whApvBatchItem = whApvBatchItemDao.queryWhApvBatchItem(query);
        return whApvBatchItem;
    }

    /**
     * This method corresponds to the database table wh_apv_batch_item
     *
     * @mbggenerated Wed Aug 15 11:11:26 CST 2018
     */
    public List<WhApvBatchItem> queryAllWhApvBatchItems() {
        return whApvBatchItemDao.queryWhApvBatchItemList();
    }

    /**
     * This method corresponds to the database table wh_apv_batch_item
     *
     * @mbggenerated Wed Aug 15 11:11:26 CST 2018
     */
    public List<WhApvBatchItem> queryWhApvBatchItems(WhApvBatchItemQueryCondition query, Pager pager) {
        Assert.notNull(query);
        if (pager != null && pager.isQueryCount()) {
            int count = whApvBatchItemDao.queryWhApvBatchItemCount(query);
            pager.setTotalCount(count);
            if (count == 0) {
                return new ArrayList<WhApvBatchItem>();
            }
        }
        List<WhApvBatchItem> whApvBatchItems = whApvBatchItemDao.queryWhApvBatchItemList(query, pager);
        return whApvBatchItems;
    }

    /**
     * This method corresponds to the database table wh_apv_batch_item
     *
     * @mbggenerated Wed Aug 15 11:11:26 CST 2018
     */
    public void createWhApvBatchItem(WhApvBatchItem whApvBatchItem) {
        try {
            whApvBatchItemDao.createWhApvBatchItem(whApvBatchItem);
        }
        catch (SqlerException e) {
            logger.error(e.getMessage(), e);
            throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
        }
    }

    /**
     * This method corresponds to the database table wh_apv_batch_item
     *
     * @mbggenerated Wed Aug 15 11:11:26 CST 2018
     */
    public void batchCreateWhApvBatchItem(List<WhApvBatchItem> entityList) {
        if (CollectionUtils.isNotEmpty(entityList)) {
            try {
                whApvBatchItemDao.batchCreateWhApvBatchItem(entityList);
            }
            catch (SqlerException e) {
                logger.error(e.getMessage(), e);
                throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
            }
        }
    }

    /**
     * This method corresponds to the database table wh_apv_batch_item
     *
     * @mbggenerated Wed Aug 15 11:11:26 CST 2018
     */
    public void deleteWhApvBatchItem(Integer id) {
        try {
            whApvBatchItemDao.deleteWhApvBatchItem(id);
        }
        catch (SqlerException e) {
            logger.error(e.getMessage(), e);
            throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
        }
    }

    /**
     * This method corresponds to the database table wh_apv_batch_item
     *
     * @mbggenerated Wed Aug 15 11:11:26 CST 2018
     */
    public void updateWhApvBatchItem(WhApvBatchItem whApvBatchItem) {
        try {
            whApvBatchItemDao.updateWhApvBatchItem(whApvBatchItem);
        }
        catch (SqlerException e) {
            logger.error(e.getMessage(), e);
            throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
        }
    }

    /**
     * This method corresponds to the database table wh_apv_batch_item
     *
     * @mbggenerated Wed Aug 15 11:11:26 CST 2018
     */
    public void batchUpdateWhApvBatchItem(List<WhApvBatchItem> entityList) {
        if (CollectionUtils.isNotEmpty(entityList)) {
            try {
                whApvBatchItemDao.batchUpdateWhApvBatchItem(entityList);
            }
            catch (SqlerException e) {
                logger.error(e.getMessage(), e);
                throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
            }
        }
    }
}