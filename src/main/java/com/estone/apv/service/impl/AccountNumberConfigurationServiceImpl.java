package com.estone.apv.service.impl;

import com.estone.apv.bean.AccountNumberConfiguration;
import com.estone.apv.bean.AccountNumberConfigurationQueryCondition;
import com.estone.apv.dao.AccountNumberConfigurationDao;
import com.estone.apv.service.AccountNumberConfigurationService;
import com.estone.picking.bean.WhMergeApvQueryCondition;
import com.whq.tool.component.Pager;
import com.whq.tool.exception.BusinessException;
import com.whq.tool.exception.DBErrorCode;
import com.whq.tool.sqler.SqlerException;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

@Service("accountNumberConfigurationService")
@Slf4j
public class AccountNumberConfigurationServiceImpl implements AccountNumberConfigurationService {
    @Resource
    private AccountNumberConfigurationDao accountNumberConfigurationDao;

    @Override
    public AccountNumberConfiguration getAccountNumberConfiguration(Integer id) {
        AccountNumberConfiguration accountNumberConfiguration = accountNumberConfigurationDao.queryAccountNumberConfiguration(id);
        return accountNumberConfiguration;
    }

    @Override
    public AccountNumberConfiguration getAccountNumberConfigurationDetail(Integer id) {
        AccountNumberConfiguration accountNumberConfiguration = accountNumberConfigurationDao.queryAccountNumberConfiguration(id);
        // 关联查询
        return accountNumberConfiguration;
    }

    @Override
    public AccountNumberConfiguration queryAccountNumberConfiguration(AccountNumberConfigurationQueryCondition query) {
        Assert.notNull(query, "query is null!");
        AccountNumberConfiguration accountNumberConfiguration = accountNumberConfigurationDao.queryAccountNumberConfiguration(query);
        return accountNumberConfiguration;
    }

    @Override
    public List<AccountNumberConfiguration> queryAllAccountNumberConfigurations() {
        return accountNumberConfigurationDao.queryAccountNumberConfigurationList();
    }

    @Override
    public List<AccountNumberConfiguration> queryAccountNumberConfigurations(AccountNumberConfigurationQueryCondition query, Pager pager) {
        Assert.notNull(query, "query is null!");
        if (pager != null && pager.isQueryCount()) {
            int count = accountNumberConfigurationDao.queryAccountNumberConfigurationCount(query);
            pager.setTotalCount(count);
            if ( count == 0) {
                return new ArrayList<AccountNumberConfiguration>();
            }
        }
        List<AccountNumberConfiguration> accountNumberConfigurations = accountNumberConfigurationDao.queryAccountNumberConfigurationList(query, pager);
        return accountNumberConfigurations;
    }

    @Override
    public void createAccountNumberConfiguration(AccountNumberConfiguration accountNumberConfiguration) {
        try {
            accountNumberConfigurationDao.createAccountNumberConfiguration(accountNumberConfiguration);
        }
        catch (SqlerException e) {
            log.error(e.getMessage(), e);
            throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
        }
    }

    @Override
    public void batchCreateAccountNumberConfiguration(List<AccountNumberConfiguration> entityList) {
        if (CollectionUtils.isNotEmpty(entityList)) {
            try {
                accountNumberConfigurationDao.batchCreateAccountNumberConfiguration(entityList);
            }
            catch (SqlerException e) {
                log.error(e.getMessage(), e);
                throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
            }
        }
    }

    @Override
    public void deleteAccountNumberConfiguration(Integer id) {
        try {
            accountNumberConfigurationDao.deleteAccountNumberConfiguration(id);
        }
        catch (SqlerException e) {
            log.error(e.getMessage(), e);
            throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
        }
    }

    @Override
    public void updateAccountNumberConfiguration(AccountNumberConfiguration accountNumberConfiguration) {
        try {
            accountNumberConfigurationDao.updateAccountNumberConfiguration(accountNumberConfiguration);
        }
        catch (SqlerException e) {
            log.error(e.getMessage(), e);
            throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
        }
    }

    @Override
    public void batchUpdateAccountNumberConfiguration(List<AccountNumberConfiguration> entityList) {
        if (CollectionUtils.isNotEmpty(entityList)) {
            try {
                accountNumberConfigurationDao.batchUpdateAccountNumberConfiguration(entityList);
            }
            catch (SqlerException e) {
                log.error(e.getMessage(), e);
                throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
            }
        }
    }

    @Override
    public void getAccountNumberConfigurationList(WhMergeApvQueryCondition whMergeApvQueryCondition) {
        if (whMergeApvQueryCondition==null) {
            return;
        }
        List<AccountNumberConfiguration> accountNumberConfigurations = accountNumberConfigurationDao.queryAccountNumberConfigurationList();
        List<String> accountNumberList = Optional.ofNullable(accountNumberConfigurations).orElse(new ArrayList<>()).stream().map(AccountNumberConfiguration::getAccountNumber).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(accountNumberList)) {
            return;
        }
        if (whMergeApvQueryCondition.getIsNanjingShop()!=null && whMergeApvQueryCondition.getIsNanjingShop()) {
            whMergeApvQueryCondition.setShopList(accountNumberList);
            whMergeApvQueryCondition.setSaleChannel("15");
        }
        else{
            whMergeApvQueryCondition.setExcludeShopList(accountNumberList);
        }

    }
}