package com.estone.apv.service;

import java.util.Date;
import java.util.List;
import java.util.Map;

import com.estone.system.rabbitmq.bean.AmqMessage;
import org.springframework.web.multipart.MultipartFile;

import com.estone.apv.bean.SaleChannel;
import com.estone.apv.bean.WhApv;
import com.estone.apv.bean.WhApvQueryCondition;
import com.estone.foreign.bean.WhApvOrderInfoDTO;
import com.estone.warehouse.bean.WhRecord;
import com.whq.tool.component.Pager;
import com.whq.tool.json.ResponseJson;

public interface WhApvService {
    /**
     * This method corresponds to the database table wh_apv
     *
     * @mbggenerated Wed Aug 15 17:39:06 CST 2018
     */
    List<WhApv> queryAllWhApvs();

    /**
     * This method corresponds to the database table wh_apv
     *
     * @mbggenerated Wed Aug 15 17:39:06 CST 2018
     */
    List<WhApv> queryWhApvs(WhApvQueryCondition query, Pager pager);

    /**
     * This method corresponds to the database table wh_apv
     *
     * @mbggenerated Wed Aug 15 17:39:06 CST 2018
     */
    WhApv getWhApv(Integer id);

    /**
     * This method corresponds to the database table wh_apv
     *
     * @mbggenerated Wed Aug 15 17:39:06 CST 2018
     */
    WhApv getWhApvDetail(Integer id);

    /**
     * This method corresponds to the database table wh_apv
     *
     * @mbggenerated Wed Aug 15 17:39:06 CST 2018
     */
    WhApv queryWhApv(WhApvQueryCondition query);

    /**
     * 查询apv列表
     *
     * @mbggenerated Wed Aug 15 17:39:06 CST 2018
     */
    List<WhApv> queryWhApvListByPickingTask(WhApvQueryCondition query);

    /**
     * This method corresponds to the database table wh_apv
     *
     * @mbggenerated Wed Aug 15 17:39:06 CST 2018
     */
    void createWhApv(WhApv whApv);

    /**
     * This method corresponds to the database table wh_apv
     *
     * @mbggenerated Wed Aug 15 17:39:06 CST 2018
     */
    void batchCreateWhApv(List<WhApv> entityList);

    /**
     * This method corresponds to the database table wh_apv
     *
     * @mbggenerated Wed Aug 15 17:39:06 CST 2018
     */
    void deleteWhApv(Integer id);

    /**
     * This method corresponds to the database table wh_apv
     *
     * @mbggenerated Wed Aug 15 17:39:06 CST 2018
     */
    boolean updateWhApv(WhApv whApv);

    /**
     * 重复修改至交运
     *
     * @param whApv
     * @return
     * @Description:
     * @Author: wuhuiqiang
     * @Date: 2018/12/18
     * @Version: 0.0.1
     */
    boolean deliverWhApv(Integer id, Double actualWeight);

    /**
     * 用于添加超体积拦截标签
     * @param ids 要进行添加超体积拦截标签的订单id
     * @return 成功添加超体积拦截标签的数量
     */
    Integer addOverSizeLabel(List<Integer> ids);

    /**
     * This method corresponds to the database table wh_apv
     *
     * @mbggenerated Wed Aug 15 17:39:06 CST 2018
     */
    void batchUpdateWhApv(List<WhApv> entityList);

    /**
     * 批量修改状态
     *
     * @param entityList
     * @Description: 根据ID批量修改状态 所有APV修改状态只能在这个方法里修改
     * @Author: wuhuiqiang
     * @Date: 2018/08/22
     * @Version: 0.0.1
     */
    String batchUpdateWhApvStatus(WhApvQueryCondition query, Pager page, Integer status);

    /**
     * 批量匹配库存
     *
     * @param query
     * @param page
     * @return 处理结果
     */
    String batchAllotWhApv(WhApvQueryCondition query, Pager page);

    /**
     * 批量移动到待分配
     *
     * @param query
     * @param page
     * @param remark 移动到待分配的原因
     * @return 处理结果
     */
    String batchWaitingAllotWhApv(WhApvQueryCondition query, Pager page, String remark);

    /**
     * @param ids
     * @param status
     * @return
     * @Description: 批量修改APV状态为拣货缺货状态和未生成任务 (迫于压力只能冒生命危险来写APV的修改状态方法)
     * @return: String
     * @Author: qinyangkai
     * @Date: 2018/10/25
     * @Version: 0.0.1
     */
    int updateWhApvListStatusAndSignPaymentByIds(List<Integer> ids, int status, boolean signPayment,
                                                 Integer beforeStatus);

    /**
     * 按框扫描通过
     *
     * @param apvId
     * @param type  0--单品包装 1--多件包装
     * @return
     */
    public boolean passBasketScan(Integer apvId, Integer type, String boxNo);

    /**
     * 多品扫描通过
     *
     * @param apvId
     * @return
     */
    public boolean passMoreProductScan(Integer apvId);

    /**
     * 查询总数量
     *
     * @param queryCondition
     * @return
     * @Description: 根据条件查询总数量
     * @Author: wuhuiqiang
     * @Date: 2018/09/28
     * @Version: 0.0.1
     */
    int queryWhApvAndItemListCount(WhApvQueryCondition queryCondition);

    /**
     * @param queryCondition
     * @param pager
     * @return
     * @Description: 查询apv和apvitem集合列表
     * @return: List<WhApv>
     * @Author: qinyangkai
     * @Date: 2018/08/23
     * @Version: 0.0.1
     */
    List<WhApv> queryWhApvAndItemList(WhApvQueryCondition queryCondition, Pager pager);

    /**
     * @param queryCondition
     * @return
     */
    List<Integer> queryWhApvAndItemListApvIds(WhApvQueryCondition queryCondition);

    List<WhApv> queryWhApvDeliverOrderStandardWeightList(WhApvQueryCondition queryCondition, Pager pager);

    void calculateApvStandardWeightByApvNo(String apvNo);

    /**
     * @param apv
     * @return
     * @Title: calculateApvStandardWeight
     * @Description: 计算APV标准重量
     */
    int calculateApvStandardWeight(WhApv apv);

    /**
     * 扫描配货专用，其他用 统一修改状态方法
     *
     * @param entity
     * @param status
     * @return
     */
    int updateWhApvStatusByStatus(WhApv entity, Integer status);

    /**
     * 推送包货完成数据
     *
     * @param whApv
     */
    ResponseJson pushPackingFinish(WhApv whApv);

    List<SaleChannel> getSaleChannels();

    /**
     * @param warehouse
     * @return
     * @Description: 根据apv状态获取apv数量
     * @Author: wanglin
     * @Date: 2018年10月12日
     * @Version: 0.0.1
     */
    Map<String, Object> getApvQuantityByStatus(String warehouse);

    /**
     * @param beginDate
     * @param endDate
     * @param warehouse
     * @return
     * @Description: 根据时间获取Apv 的交运量和推送量
     * @Author: wanglin
     * @Date: 2018年10月12日
     * @Version: 0.0.1
     */
    Map<String, Object> getApvQuantityByDate(String beginDate, String endDate, String warehouse);

    /**
     * 首页获取wms推送量和交运量数据
     *
     * @param beginDate
     * @param endDate
     * @param warehouse
     * @return
     */
    Map<String, Object> getWmsPushQuantityAndDeliverQuantityByCondition(String beginDate, String endDate,
                                                                        String warehouse);

    /**
     * 定时迁移订单数据到历史表 start
     */
    void insertThreeMonthsAgoDataFromApvToHistory();

    void deleteThreeMonthsAgoDataFromApvAndApvItem();
    // 定时迁移订单数据到历史表 end

    /**
     * @param list
     * @return
     * @Description: 根据sku查询 待QC、待上架、上架中 库存
     * @Author: Administrator
     * @Date: 2018/12/04
     * @Version: 0.0.1
     */
    List<WhRecord> countApvInventory(List<WhRecord> list);

    /**
     * @param query
     * @return
     * @Description: 查询批量匹配库存后的apv
     * @Author: Administrator
     * @Date: 2018/12/06
     * @Version: 0.0.1
     */
    List<WhApv> queryAllotStockApv(WhApvQueryCondition query, Pager pager);

    /**
     * @param whApvs
     * @return
     * @Description:
     * @Author: Administrator
     * @Date: 2018/12/07
     * @Version: 0.0.1
     */
    int[] updateApvStatusByPrimaryKey(List<WhApv> whApvs);

    /**
     * @param query
     * @return
     * @Description: 推单相关查询
     * @Author: Administrator
     * @Date: 2019/02/26
     * @Version: 0.0.1
     */
    List<WhApv> queryPushOrderConditionList(WhApvQueryCondition query);

    //拣货查询
    List<WhApv> queryWhApvAndItemListForPickingByTaskId(Integer taskId);

    WhApv querySecurityApvByOrderNo(String orderNo);

    /**
     * 根据apvId去TMS获取追踪号
     *
     * @param apvId
     * @return
     */
    Boolean updateWhApvTrackingNumberById(Integer apvId);

    /**
     * 根据ID初始化APV拣货变量
     *
     * @param idList
     * @return
     * @Description: item拣货数量和apv是否拣货标识
     * @Author: wuhuiqiang
     * @Date: 2019/04/23
     * @Version: 0.0.1
     */
    int updateWhApvPickQuantity(List<Integer> idList);

    /**
     * 匹配待返架库存失败并取消
     *
     * @param apvIds
     * @return
     */
    String allotAndCancelByApvIds(List<Integer> apvIds);

    /**
     * 查询当前拣货任务中拣货正常状态的的发货单ID
     *
     * @param taskNo
     * @return
     */
    List<Integer> queryCurrentTaskNormal(String taskNo);

    /**
     * 查询当前热销拣货任务中拣货正常状态的的发货单ID
     *
     * @param taskNo
     * @return
     */
    List<Integer> queryCurrentReXiaoTaskNormal(String taskNo);

    List<WhApv> queryWhApvAndItemList2019(WhApvQueryCondition query, Pager page);

    /**
     * 根据交运时间查询本地仓sku与交运数量
     *
     * @param startTime
     * @param endTime
     * @return java.util.List<java.util.Map < java.lang.String, java.lang.Object>>
     * <AUTHOR>
     * @date 2022/1/5 16:27
     */
    List<Map<String, Object>> getPcsByTime(Date startTime, Date endTime);

    /**
     * 优选仓分配库存
     */
    void pacStockAllotWhApv();

    WhApv matchPacOrderInfoList(WhApvOrderInfoDTO whApvOrderDTO);

    void batchInterceptorWhApv(WhApv whApv, Boolean isInterceptor, String interceptorType );


    /**
     * 根据条件查询出符合分布条件的数据及其汇总
     *
     * @param whApvQueryCondition 查询条件
     * @return 符合分布条件的数据及其汇总
     */
    List<Map<String, Object>> queryStandardWeightDistribution(WhApvQueryCondition whApvQueryCondition);

    /**
     * 根据条件下载符合分布条件的数据excel表格
     *
     * @param whApvQueryCondition 查询条件
     */
    void downloadDistribution(WhApvQueryCondition whApvQueryCondition);

    ResponseJson doImportTrackingNo(String[] titles, List<Integer> expressIntCode, MultipartFile multiPartFile);

    String allotWhApv(WhApvQueryCondition query, Pager page);

    public boolean passZfProductScan(Integer apvId);

    void allotUpNotAllotApv();

    ResponseJson doCancelJitApv(String apvNo);

    //JIT订单取消推送TMS
    void cancelJitApvTms(String apvNo, Integer status);

    //JIT订单取消推送oms
    void cancelJitApvOms(String apvNo,String trackingNumber, Integer status);

    ResponseJson doDeliverOrderOnAmq(AmqMessage amqMessage);
}