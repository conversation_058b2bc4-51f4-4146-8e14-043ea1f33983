package com.estone.apv.service;

import com.estone.apv.bean.SmPackingException;
import com.estone.apv.bean.SmPackingPickingTaskItem;
import com.estone.apv.bean.SmPackingPickingTaskItemQueryCondition;
import com.whq.tool.component.Pager;
import java.util.List;

public interface SmPackingPickingTaskItemService {
    List<SmPackingPickingTaskItem> queryAllSmPackingPickingTaskItems();

    List<SmPackingPickingTaskItem> querySmPackingPickingTaskItems(SmPackingPickingTaskItemQueryCondition query, Pager pager);

    SmPackingPickingTaskItem getSmPackingPickingTaskItem(Integer id);

    SmPackingPickingTaskItem getSmPackingPickingTaskItemDetail(Integer id);

    SmPackingPickingTaskItem querySmPackingPickingTaskItem(SmPackingPickingTaskItemQueryCondition query);

    void createSmPackingPickingTaskItem(SmPackingPickingTaskItem smPackingPickingTaskItem);

    void batchCreateSmPackingPickingTaskItem(List<SmPackingPickingTaskItem> entityList);

    void deleteSmPackingPickingTaskItem(Integer id);

    void updateSmPackingPickingTaskItem(SmPackingPickingTaskItem smPackingPickingTaskItem);

    void batchUpdateSmPackingPickingTaskItem(List<SmPackingPickingTaskItem> entityList);

    List<SmPackingException> querySmPackingExceptionListItemList(SmPackingPickingTaskItemQueryCondition query);
}