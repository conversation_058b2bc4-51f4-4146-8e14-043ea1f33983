package com.estone.shop.action;

import com.estone.shop.bean.ShopOrderItem;
import com.estone.shop.bean.ShopOrderItemQueryCondition;
import com.estone.shop.domain.ShopOrderItemDo;
import com.estone.shop.service.ShopOrderItemService;
import com.whq.tool.action.BaseController;
import com.whq.tool.component.Pager;
import com.whq.tool.json.ResponseJson;
import java.util.List;
import javax.annotation.Resource;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping(value = "shopOrderItem")
public class ShopOrderItemController extends BaseController {
    @Resource
    private ShopOrderItemService shopOrderItemService;

    @RequestMapping(method = {RequestMethod.GET})
    public String init(@ModelAttribute("domain") ShopOrderItemDo domain) {
        return "{模块}/{页面}";
    }

    private void initFormData(@ModelAttribute("domain") ShopOrderItemDo domain) {
         
    }

    private void queryShopOrderItems(@ModelAttribute("domain") ShopOrderItemDo domain) {
        ShopOrderItemQueryCondition query = domain.getQuery();
        Pager page = domain.getPage();
        if (query == null)
        {
            query = new ShopOrderItemQueryCondition();
            domain.setQuery(query);
        }
        // 默认读从库
        query.setReadOnly(true);
        List<ShopOrderItem> shopOrderItems = shopOrderItemService.queryShopOrderItems(query, page);
        domain.setShopOrderItems(shopOrderItems);
    }

    @RequestMapping(value="search", method = {RequestMethod.POST})
    public String search(@ModelAttribute("domain") ShopOrderItemDo domain) {
        initFormData(domain);
        queryShopOrderItems(domain);
        return "{模块}/{页面}";
    }

    @RequestMapping(value="create", method = {RequestMethod.GET})
    public String toCreateShopOrderItem(@ModelAttribute("domain") ShopOrderItemDo domain) {
        return "{模块}/{页面}";
    }

    @RequestMapping(value="create", method = {RequestMethod.POST})
    public String createShopOrderItem(@ModelAttribute("domain") ShopOrderItemDo domain) {
        ShopOrderItem shopOrderItem = domain.getShopOrderItem();
        shopOrderItemService.createShopOrderItem(shopOrderItem);
        return "redirect:/{查询列表}";
    }

    @RequestMapping(value="update", method = {RequestMethod.GET})
    public String toUpdateShopOrderItem(@ModelAttribute("domain") ShopOrderItemDo domain, @RequestParam("shopOrderItemId") Integer shopOrderItemId) {
        ShopOrderItem shopOrderItem = shopOrderItemService.getShopOrderItem(shopOrderItemId);
        domain.setShopOrderItem(shopOrderItem);
        return "{模块}/{页面}";
    }

    @RequestMapping(value="update", method = {RequestMethod.POST})
    public String updateShopOrderItem(@ModelAttribute("domain") ShopOrderItemDo domain) {
        ShopOrderItem shopOrderItem = domain.getShopOrderItem();
        shopOrderItemService.updateShopOrderItem(shopOrderItem);
        return "redirect:/{查询列表}";
    }

    @RequestMapping(value="delete", method = {RequestMethod.GET})
    @ResponseBody
    public ResponseJson deleteShopOrderItem(@ModelAttribute("domain") ShopOrderItemDo domain, @RequestParam("shopOrderItemId") Integer shopOrderItemId) {
        ResponseJson response = new ResponseJson();
        shopOrderItemService.deleteShopOrderItem(shopOrderItemId);
        return response;
    }
}