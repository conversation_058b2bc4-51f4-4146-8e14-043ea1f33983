package com.estone.shop.service;

import com.estone.android.domain.AndroidProductDo;
import com.estone.checkout.bean.ReturnFormOrderItem;
import com.estone.checkout.bean.RfoPickingTask;
import com.estone.shop.bean.ShopOrderItem;
import com.estone.shop.bean.ShopOrderPickingTask;
import com.estone.shop.bean.ShopOrderPickingTaskQueryCondition;
import com.whq.tool.component.Pager;
import com.whq.tool.json.ResponseJson;

import java.util.List;

public interface ShopOrderPickingTaskService {
    List<ShopOrderPickingTask> queryAllShopOrderPickingTasks();

    List<ShopOrderPickingTask> queryShopOrderPickingTasks(ShopOrderPickingTaskQueryCondition query, Pager pager);

    ShopOrderPickingTask getShopOrderPickingTask(Integer id);

    ShopOrderPickingTask getShopOrderPickingTaskDetail(Integer id);

    ShopOrderPickingTask queryShopOrderPickingTask(ShopOrderPickingTaskQueryCondition query);

    void createShopOrderPickingTask(ShopOrderPickingTask shopOrderPickingTask);

    void batchCreateShopOrderPickingTask(List<ShopOrderPickingTask> entityList);

    void deleteShopOrderPickingTask(Integer id);

    void updateShopOrderPickingTask(ShopOrderPickingTask shopOrderPickingTask);

    void batchUpdateShopOrderPickingTask(List<ShopOrderPickingTask> entityList);

    List<ShopOrderItem> queryPickingTaskDetail(Integer id);

    ResponseJson doReceiveTask(AndroidProductDo domain);

    ResponseJson scanningPicking(AndroidProductDo domain);

    ResponseJson getTaskAndSkus(ShopOrderPickingTask task);

    ResponseJson doDiscardTask(ShopOrderPickingTask task);


    ResponseJson checkSubTaskNo(String subTaskNo);
}