package com.estone.shop.service;

import com.estone.checkout.bean.ReturnFormOrderItem;
import com.estone.shop.bean.ShopOrderItem;
import com.estone.shop.bean.ShopOrderItemQueryCondition;
import com.whq.tool.component.Pager;
import java.util.List;

public interface ShopOrderItemService {
    List<ShopOrderItem> queryAllShopOrderItems();

    List<ShopOrderItem> queryShopOrderItems(ShopOrderItemQueryCondition query, Pager pager);

    ShopOrderItem getShopOrderItem(Integer id);

    ShopOrderItem getShopOrderItemDetail(Integer id);

    ShopOrderItem queryShopOrderItem(ShopOrderItemQueryCondition query);

    void createShopOrderItem(ShopOrderItem shopOrderItem);

    void batchCreateShopOrderItem(List<ShopOrderItem> entityList);

    void deleteShopOrderItem(Integer id);

    void updateShopOrderItem(ShopOrderItem shopOrderItem);

    void batchUpdateShopOrderItem(List<ShopOrderItem> entityList);

    void updateItemBySpoIdAndSku(ShopOrderItem item);
}