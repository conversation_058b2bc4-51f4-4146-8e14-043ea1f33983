package com.estone.shop.service;

import com.estone.shop.bean.ShopOrder;
import com.estone.shop.bean.ShopOrderGridDto;
import com.estone.shop.bean.ShopOrderQueryCondition;
import com.whq.tool.component.Pager;
import java.util.List;
import java.util.Map;

public interface ShopOrderService {
    List<ShopOrder> queryAllShopOrders();

    List<ShopOrder> queryShopOrders(ShopOrderQueryCondition query, Pager pager);

    ShopOrder getShopOrder(Integer id);

    ShopOrder getShopOrderDetail(Integer id);

    ShopOrder queryShopOrder(ShopOrderQueryCondition query);

    void createShopOrder(ShopOrder shopOrder);

    void batchCreateShopOrder(List<ShopOrder> entityList);

    void deleteShopOrder(Integer id);

    void updateShopOrder(ShopOrder shopOrder);

    void batchUpdateShopOrder(List<ShopOrder> entityList);

    List<Map<String,Object>> queryShopUserList();

    List<ShopOrderGridDto> getPrintGridData(String taskNo);
}