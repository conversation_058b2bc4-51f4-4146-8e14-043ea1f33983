package com.estone.shop.bean;

import lombok.Data;

import java.util.List;

@Data
public class ShopOrderPickingTaskQueryCondition extends ShopOrderPickingTask {
    private static final long serialVersionUID = 1L;

    private Boolean readOnly = false;

    private List<Integer> ids;

    private List<Integer> statusList;

    private String sku;

    private String spoNo;

    private String fromCreatedDate;

    private String toCreatedDate;

    private String fromPickingEndDate;

    private String toPickingEndDate;
}