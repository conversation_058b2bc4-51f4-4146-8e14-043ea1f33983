package com.estone.shop.dao;

import com.estone.shop.bean.ShopOrderItem;
import com.estone.shop.bean.ShopOrderItemQueryCondition;
import com.whq.tool.component.Pager;
import java.util.List;

public interface ShopOrderItemDao {
    int queryShopOrderItemCount(ShopOrderItemQueryCondition query);

    List<ShopOrderItem> queryShopOrderItemList();

    List<ShopOrderItem> queryShopOrderItemList(ShopOrderItemQueryCondition query, Pager pager);

    ShopOrderItem queryShopOrderItem(Integer primaryKey);

    ShopOrderItem queryShopOrderItem(ShopOrderItemQueryCondition query);

    void createShopOrderItem(ShopOrderItem entity);

    void batchCreateShopOrderItem(List<ShopOrderItem> entityList);

    void batchUpdateShopOrderItem(List<ShopOrderItem> entityList);

    void deleteShopOrderItem(Integer primaryKey);

    void updateShopOrderItem(ShopOrderItem entity);

    void updateItemBySpoIdAndSku(ShopOrderItem item);
}