package com.estone.shop.dao.impl;

import java.util.List;

import org.springframework.stereotype.Repository;
import org.springframework.util.Assert;

import com.estone.shop.bean.ShopOrderItem;
import com.estone.shop.bean.ShopOrderItemQueryCondition;
import com.estone.shop.dao.ShopOrderItemDao;
import com.estone.shop.dao.mapper.ShopOrderItemDBField;
import com.estone.shop.dao.mapper.ShopOrderItemMapper;
import com.whq.tool.component.Pager;
import com.whq.tool.sqler.SqlerRequest;
import com.whq.tool.sqler.analyzer.DataType;
import com.estone.common.util.SqlerTemplate;

@Repository("shopOrderItemDao")
public class ShopOrderItemDaoImpl implements ShopOrderItemDao {

    private void setQueryCondition(SqlerRequest request, ShopOrderItemQueryCondition query) {
        if (query == null) {
            return;
        }
        // 添加只读权限
        if (query.getReadOnly() != null && query.getReadOnly())
        {
            request.setReadOnly(true);
        }
        // 添加查询条件
        // 搜索条件不允许出现空字符
        request.setAllowBlank(false);
        
        request.addDataParam(ShopOrderItemDBField.ID, DataType.INT, query.getId());
    }

    @Override
    public int queryShopOrderItemCount(ShopOrderItemQueryCondition query) {
        SqlerRequest request = new SqlerRequest("queryShopOrderItemCount");
        setQueryCondition(request, query);
        return SqlerTemplate.queryForInt(request);
    }

    @Override
    public List<ShopOrderItem> queryShopOrderItemList() {
        SqlerRequest request = new SqlerRequest("queryShopOrderItemList");
        return SqlerTemplate.query(request, new ShopOrderItemMapper());
    }

    @Override
    public List<ShopOrderItem> queryShopOrderItemList(ShopOrderItemQueryCondition query, Pager pager) {
        SqlerRequest request = new SqlerRequest("queryShopOrderItemList");
        setQueryCondition(request, query);
        if(pager != null) {
            request.addFetch(pager.getPageNo(), pager.getPageSize());
        }
        return SqlerTemplate.query(request, new ShopOrderItemMapper());
    }

    @Override
    public ShopOrderItem queryShopOrderItem(Integer primaryKey) {
        Assert.notNull(primaryKey, "primaryKey is null!");
        SqlerRequest request = new SqlerRequest("queryShopOrderItemByPrimaryKey");
        request.addDataParam(ShopOrderItemDBField.ID, DataType.INT, primaryKey);
        return SqlerTemplate.queryForObject(request, new ShopOrderItemMapper());
    }

    @Override
    public ShopOrderItem queryShopOrderItem(ShopOrderItemQueryCondition query) {
        SqlerRequest request = new SqlerRequest("queryShopOrderItem");
        setQueryCondition(request, query);
        return SqlerTemplate.queryForObject(request, new ShopOrderItemMapper());
    }

    @Override
    public void createShopOrderItem(ShopOrderItem entity) {
        SqlerRequest request = new SqlerRequest("createShopOrderItem");
        request.addDataParam(ShopOrderItemDBField.ORDER_ID, DataType.INT, entity.getOrderId());
        request.addDataParam(ShopOrderItemDBField.SKU, DataType.STRING, entity.getSku());
        request.addDataParam(ShopOrderItemDBField.IMAGE_URL, DataType.STRING, entity.getImageUrl());
        request.addDataParam(ShopOrderItemDBField.NAME, DataType.STRING, entity.getName());
        request.addDataParam(ShopOrderItemDBField.DISCOUNTED_PRICE, DataType.BIGDECIMAL, entity.getDiscountedPrice());
        request.addDataParam(ShopOrderItemDBField.COST_PRICE, DataType.BIGDECIMAL, entity.getCostPrice());
        request.addDataParam(ShopOrderItemDBField.QUANTITY, DataType.INT, entity.getQuantity());
        request.addDataParam(ShopOrderItemDBField.ALLOT_QTY, DataType.INT, entity.getAllotQty());
        request.addDataParam(ShopOrderItemDBField.PICK_QTY, DataType.INT, entity.getPickQty());
        request.addDataParam(ShopOrderItemDBField.GRID_QTY, DataType.INT, entity.getGridQty());
        request.addDataParam(ShopOrderItemDBField.LOCATION, DataType.STRING, entity.getLocation());
        Integer autoIncrementId = SqlerTemplate.executeAndReturn(request);
        entity.setId(autoIncrementId);
    }

    @Override
    public void updateShopOrderItem(ShopOrderItem entity) {
        SqlerRequest request = new SqlerRequest("updateShopOrderItemByPrimaryKey");
        request.addDataParam(ShopOrderItemDBField.ID, DataType.INT, entity.getId());
        
        request.addDataParam(ShopOrderItemDBField.ORDER_ID, DataType.INT, entity.getOrderId());
        request.addDataParam(ShopOrderItemDBField.SKU, DataType.STRING, entity.getSku());
        request.addDataParam(ShopOrderItemDBField.IMAGE_URL, DataType.STRING, entity.getImageUrl());
        request.addDataParam(ShopOrderItemDBField.NAME, DataType.STRING, entity.getName());
        request.addDataParam(ShopOrderItemDBField.DISCOUNTED_PRICE, DataType.BIGDECIMAL, entity.getDiscountedPrice());
        request.addDataParam(ShopOrderItemDBField.COST_PRICE, DataType.BIGDECIMAL, entity.getCostPrice());
        request.addDataParam(ShopOrderItemDBField.QUANTITY, DataType.INT, entity.getQuantity());
        request.addDataParam(ShopOrderItemDBField.ALLOT_QTY, DataType.INT, entity.getAllotQty());
        request.addDataParam(ShopOrderItemDBField.PICK_QTY, DataType.INT, entity.getPickQty());
        request.addDataParam(ShopOrderItemDBField.GRID_QTY, DataType.INT, entity.getGridQty());
        request.addDataParam(ShopOrderItemDBField.LOCATION, DataType.STRING, entity.getLocation());
        request.addDataParam(ShopOrderItemDBField.REMARK, DataType.STRING, entity.getRemark());
        SqlerTemplate.execute(request);
    }

    @Override
    public void batchCreateShopOrderItem(List<ShopOrderItem> entityList) {
        if (entityList != null && !entityList.isEmpty()) {
            SqlerRequest request = new SqlerRequest("createShopOrderItem");
            for (ShopOrderItem entity : entityList) {
                request.addBatchDataParam(ShopOrderItemDBField.ORDER_ID, DataType.INT, entity.getOrderId());
                request.addBatchDataParam(ShopOrderItemDBField.SKU, DataType.STRING, entity.getSku());
                request.addBatchDataParam(ShopOrderItemDBField.IMAGE_URL, DataType.STRING, entity.getImageUrl());
                request.addBatchDataParam(ShopOrderItemDBField.NAME, DataType.STRING, entity.getName());
                request.addBatchDataParam(ShopOrderItemDBField.DISCOUNTED_PRICE, DataType.BIGDECIMAL, entity.getDiscountedPrice());
                request.addBatchDataParam(ShopOrderItemDBField.COST_PRICE, DataType.BIGDECIMAL, entity.getCostPrice());
                request.addBatchDataParam(ShopOrderItemDBField.QUANTITY, DataType.INT, entity.getQuantity());
                request.addBatchDataParam(ShopOrderItemDBField.ALLOT_QTY, DataType.INT, entity.getAllotQty());
                request.addBatchDataParam(ShopOrderItemDBField.PICK_QTY, DataType.INT, entity.getPickQty());
                request.addBatchDataParam(ShopOrderItemDBField.GRID_QTY, DataType.INT, entity.getGridQty());
                request.addBatchDataParam(ShopOrderItemDBField.LOCATION, DataType.STRING, entity.getLocation());
                request.addBatch();
            }
            SqlerTemplate.batchUpdate(request);
        }
    }

    @Override
    public void batchUpdateShopOrderItem(List<ShopOrderItem> entityList) {
        if (entityList != null && !entityList.isEmpty()) {
            SqlerRequest request = new SqlerRequest("updateShopOrderItemByPrimaryKey");
            for (ShopOrderItem entity : entityList) {
                request.addBatchDataParam(ShopOrderItemDBField.ID, DataType.INT, entity.getId());
                
                request.addBatchDataParam(ShopOrderItemDBField.ORDER_ID, DataType.INT, entity.getOrderId());
                request.addBatchDataParam(ShopOrderItemDBField.SKU, DataType.STRING, entity.getSku());
                request.addBatchDataParam(ShopOrderItemDBField.IMAGE_URL, DataType.STRING, entity.getImageUrl());
                request.addBatchDataParam(ShopOrderItemDBField.NAME, DataType.STRING, entity.getName());
                request.addBatchDataParam(ShopOrderItemDBField.DISCOUNTED_PRICE, DataType.BIGDECIMAL, entity.getDiscountedPrice());
                request.addBatchDataParam(ShopOrderItemDBField.COST_PRICE, DataType.BIGDECIMAL, entity.getCostPrice());
                request.addBatchDataParam(ShopOrderItemDBField.QUANTITY, DataType.INT, entity.getQuantity());
                request.addBatchDataParam(ShopOrderItemDBField.ALLOT_QTY, DataType.INT, entity.getAllotQty());
                request.addBatchDataParam(ShopOrderItemDBField.PICK_QTY, DataType.INT, entity.getPickQty());
                request.addBatchDataParam(ShopOrderItemDBField.GRID_QTY, DataType.INT, entity.getGridQty());
                request.addBatchDataParam(ShopOrderItemDBField.LOCATION, DataType.STRING, entity.getLocation());
                request.addBatchDataParam(ShopOrderItemDBField.REMARK, DataType.STRING, entity.getRemark());
                request.addBatch();
            }
            SqlerTemplate.batchUpdate(request);
        }
    }

    @Override
    public void deleteShopOrderItem(Integer primaryKey) {
        SqlerRequest request = new SqlerRequest("deleteShopOrderItemByPrimaryKey");
        request.addDataParam(ShopOrderItemDBField.ID, DataType.INT, primaryKey);
        SqlerTemplate.execute(request);
    }

    @Override
    public void updateItemBySpoIdAndSku(ShopOrderItem item) {
        SqlerRequest request = new SqlerRequest("updateShopOrderItemByOrderIdAndSku");
        request.addDataParam(ShopOrderItemDBField.ORDER_ID, DataType.INT, item.getOrderId());
        request.addDataParam(ShopOrderItemDBField.SKU, DataType.STRING, item.getSku());
        request.addDataParam(ShopOrderItemDBField.QUANTITY, DataType.INT, item.getQuantity());
        request.addDataParam(ShopOrderItemDBField.ALLOT_QTY, DataType.INT, item.getAllotQty());
        request.addDataParam(ShopOrderItemDBField.PICK_QTY, DataType.INT, item.getPickQty());
        request.addDataParam(ShopOrderItemDBField.GRID_QTY, DataType.INT, item.getGridQty());
        request.addDataParam(ShopOrderItemDBField.LOCATION, DataType.STRING, item.getLocation());
        SqlerTemplate.execute(request);
    }
}