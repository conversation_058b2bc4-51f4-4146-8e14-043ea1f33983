package com.estone.shop.domain;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.estone.shop.bean.ShopOrderItem;
import com.estone.shop.bean.ShopOrderPickingTask;
import com.estone.shop.bean.ShopOrderPickingTaskQueryCondition;
import com.whq.tool.component.Pager;

import lombok.Data;

@Data
public class ShopOrderPickingTaskDo {
    private ShopOrderPickingTask shopOrderPickingTask;

    private ShopOrderPickingTaskQueryCondition query = new ShopOrderPickingTaskQueryCondition();

    private List<ShopOrderPickingTask> shopOrderPickingTasks = new ArrayList<ShopOrderPickingTask>();

    private Pager page = new Pager();
    private String isPrintings;

    private String taskStatusJson;// 状态JSON

    private String gridStatusJson;// 状态JSON

    private String taskTypeJson;// 任务类型

    private Map<String, List<ShopOrderItem>> orderMap = new HashMap<>();

    private List<ShopOrderItem> skuItemList = new ArrayList<>();

    private String title;

}