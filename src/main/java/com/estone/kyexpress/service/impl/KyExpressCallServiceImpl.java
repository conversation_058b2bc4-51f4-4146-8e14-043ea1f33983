package com.estone.kyexpress.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.estone.common.config.KyExpressConfig;
import com.estone.kyexpress.bean.request.KyeOrderInfo;
import com.estone.kyexpress.bean.request.KyePrintInfo;
import com.estone.kyexpress.bean.request.OrderInfoItem;
import com.estone.kyexpress.bean.response.KyeOrderResult;
import com.estone.kyexpress.bean.response.KyePrintResult;
import com.estone.kyexpress.bean.response.KyeResult;
import com.estone.kyexpress.enums.KyExpressApi;
import com.estone.kyexpress.service.KyExpressCallService;
import com.estone.kyexpress.service.KyeBaseService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/7/11
 */
@Slf4j
@Service
public class KyExpressCallServiceImpl implements KyExpressCallService {

    @Resource
    private KyeBaseService kyeBaseService;

    @Resource
    private KyExpressConfig kyExpressConfig;

    /**
     * 下单
     */
    @Override
    public KyeResult<KyeOrderResult> createOrderBatch(List<OrderInfoItem> itemList) {
        if (CollectionUtils.isEmpty(itemList)) {
            return null;
        }
        KyeOrderInfo kyeInfo = new KyeOrderInfo();
        kyeInfo.setCustomerCode(kyExpressConfig.getCustomerCode());
        kyeInfo.setPlatformFlag(kyExpressConfig.getPlatformFlag());
        for (OrderInfoItem item : itemList) {
            item.setPaymentCustomer(kyeInfo.getCustomerCode());
        }
        kyeInfo.setOrderInfos(itemList);
        String response = kyeBaseService.execute(JSON.toJSONString(kyeInfo), KyExpressApi.ADD_ORDER);
        if (StringUtils.isBlank(response)) {
            return null;
        }
        return JSON.parseObject(response, new TypeReference<KyeResult<KyeOrderResult>>() {
        });
    }

    /**
     * 生成打印文件（会同步返回pdf 及回调返回）
     * @param printType 10：取货标签 20：寄件存根 30：子母单 40：普通面单 默认10
     * @param generateFileType 生成文件类型 10：一个运单生成多个文件 20：一个运单生成一个文件 默认20
     */
    @Override
    public KyeResult<KyePrintResult> generatePrintPdf(List<String> waybillNos, Integer printType, Integer generateFileType, Boolean async){
        if (CollectionUtils.isEmpty(waybillNos)) {
            return null;
        }
        KyePrintInfo kyeInfo = KyePrintInfo.buildPrintInfo(waybillNos, printType);
        kyeInfo.setCustomerCode(kyExpressConfig.getCustomerCode());
        kyeInfo.setPlatformFlag(kyExpressConfig.getPlatformFlag());
        if (Boolean.TRUE.equals(async)) {
            kyeInfo.setCallbackUrl(kyExpressConfig.getCallbackUrl());
        }
        kyeInfo.setGenerateFileType(generateFileType);
        String response = kyeBaseService.execute(JSON.toJSONString(kyeInfo), KyExpressApi.PRINT_ORDER);
        if (StringUtils.isBlank(response)) {
            return null;
        }
        return JSON.parseObject(response, new TypeReference<KyeResult<KyePrintResult>>(){});
    }

    /**
     * 下载打印文件
     * @param taskId 任务ID 获取后有效期为10分钟，逾期使用无效
     */
    @Override
    public KyeResult<KyePrintResult> getPdfByTaskId(String taskId){
        if (StringUtils.isBlank(taskId)) {
            return null;
        }
        Map<String, String> paramMap = new HashMap();
        paramMap.put("platformFlag", kyExpressConfig.getPlatformFlag());
        paramMap.put("taskId", taskId);
        String response = kyeBaseService.execute(JSON.toJSONString(paramMap), KyExpressApi.GET_ORDER_PDF);
        if (StringUtils.isBlank(response)) {
            return null;
        }
        return JSON.parseObject(response, new TypeReference<KyeResult<KyePrintResult>>(){});
    }

    @Override
    public KyeResult<?> cancelOrder(String waybillNo) {
        Map<String, String> paramMap = new HashMap();
        paramMap.put("platformFlag", kyExpressConfig.getPlatformFlag());
        paramMap.put("customerCode", kyExpressConfig.getCustomerCode());
        paramMap.put("waybillNumber", waybillNo);
        String response = kyeBaseService.execute(JSON.toJSONString(paramMap), KyExpressApi.CANCEL_ORDER);
        if (StringUtils.isBlank(response)) {
            return null;
        }
        return JSON.parseObject(response, new TypeReference<KyeResult<Object>>(){});
    }
}
