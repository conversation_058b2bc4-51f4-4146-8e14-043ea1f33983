package com.estone.kyexpress.bean.response;

import lombok.Data;

@Data
public class BillPrinting {
    //代理平台名称
    private String agentName;
    //运输模式
    private String transportType;
    //产品类型
    private String productType;
    //运单条形码
    private String deliveryBarCode;
    //打印运单号
    private String waybillNumber;
    //始发分拣
    private String originNodeName;
    //发滑道号 - 始发笼车号
    private String originCode;
    //目的滑道号 - 目的笼车号
    private String destinationCode;
    //特快标识，产品类型 为 特快，则打印 T
    private String expressSign;
    //集包地
    private String collePackage;
    //目的站点
    private String destinationSiteName;
    // 路区
    private String destinationRoad;
    // 目的分拣水印代码
    private String destinationNodeWaterCode;
    //增值服务
    private String addServices;
    //拓展1
    private String expand1;
    //拓展2
    private String expand2;
    //拓展3
    private String expand3;
    //目的分拣
    private String targetSortcenterName;
    //打印运单号
    private String printNumber;
}
