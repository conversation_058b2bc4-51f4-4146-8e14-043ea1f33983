package com.estone.core.interceptor;

import com.alibaba.fastjson.JSON;
import com.estone.android.PdaExceptionCode;
import com.estone.common.util.*;
import com.estone.system.permission.util.UserRolePermissionUtils;
import com.estone.system.user.bean.SaleUser;
import com.whq.tool.component.StatusCode;
import com.whq.tool.context.DataContextHolder;
import com.whq.tool.json.ResponseJson;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.handler.HandlerInterceptorAdapter;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.PrintWriter;
import java.util.concurrent.TimeUnit;

public class AuthAdapter extends HandlerInterceptorAdapter {
    private Logger logger = LoggerFactory.getLogger(AuthAdapter.class);

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler)
            throws Exception {
        if (StringUtils.contains(request.getRequestURI(), "/android/")) {
            if (!StringUtils.contains(request.getRequestURI(), "/users/login")) {
                CookieUtils.syncCookie(request);
                Object currentUser = request.getSession().getAttribute("currentUser");
                if (currentUser == null && StringUtils.contains(request.getRequestURI(), "/android/checkins")) {
                    ResponseJson responseJson = new ResponseJson(StatusCode.FAIL);
                    responseJson.setExceptionCode(PdaExceptionCode.LOGOUT);
                    responseJson.setMessage("您已被挤下线,请退出后重新登录!");
                    response.setContentType("application/json; charset=utf-8");
                    try (PrintWriter write = response.getWriter()) {
                        write.write(JSON.toJSONString(responseJson));
                    }
                    request.getSession().setAttribute("currentUser", null);
                    return false;
                }
            }
            DataContextHolder.setOperationId(request.getHeader("userId"));
            DataContextHolder.setContext(Constant.SALE_USER_ID, request.getHeader("userId"));
            DataContextHolder.setContext(Constant.SALE_USER_NAME, request.getHeader("name"));
            DataContextHolder.setContext(Constant.TOKEN, request.getHeader("Authorization"));
            DataContextHolder.setContext(Constant.VERSION, request.getHeader("version"));
            if (request.getSession() != null && request.getSession().getAttribute("currentUser") != null) {
                SaleUser user = (SaleUser) request.getSession().getAttribute("currentUser");
                logger.warn(user.getUserId() + "[" + user.getUsername() + "]--IP:[" + IpUtils.getIpAddress() + "]--" + request.getRequestURI());
            }
            return true;
        }

        if (StringUtils.contains(request.getRequestURI(), "/foreign")) {
            DataContextHolder.setOperationId("2");
            DataContextHolder.setContext(Constant.SALE_USER_ID, 2);
            return true;
        }
        if (StringUtils.contains(request.getRequestURI(), "/syncCookie")
                || StringUtils.contains(request.getRequestURI(), "/getUserInfo")) {
            return true;
        }
        if (StringUtils.contains(request.getRequestURI(), "/scan/deliverOrder")) {
            DataContextHolder.setOperationId(null);
            DataContextHolder.setContext(Constant.SALE_USER_ID, null);
            return true;
        }
        if (StringUtils.contains(request.getRequestURI(), "/scan/deliverExpress")) {
            return true;
        }
        if(StringUtils.contains(request.getRequestURI(), "/checkin/scans/client")){
            return true;
        }
        if(StringUtils.isNotBlank( request.getHeader("Authorization"))
                && StringUtils.equalsIgnoreCase( request.getHeader("Authorization"),HttpUtils.ACCESS_TOKEN)) {
            return true;
        }

        CookieUtils.syncCookie(request);
        if (StringUtils.contains(request.getRequestURI(), "/updateProcessingBagging")
                && request.getSession().getAttribute("currentUser") == null) {
            DataContextHolder.setOperationId("2");
            DataContextHolder.setContext(Constant.SALE_USER_ID, 2);
            return true;
        }
        // 校验单个IP并发量
        /*if ( !StringUtils.contains(request.getRequestURI(), "/login") && !addIpRequestCount()){
            ResponseJson responseJson = new ResponseJson();
            responseJson.setStatus(StatusCode.FAIL);
            responseJson.setMessage("**********IP: " + IpUtils.getIpAddress() + " REQUEST FULL!");
            response.getWriter().write(JSON.toJSONString(responseJson));
            return false;
        }*/
        if (request.getSession().getAttribute("currentUser") != null) {
            SaleUser user = (SaleUser) request.getSession().getAttribute("currentUser");
            AuthAdapter.initDataContextHolder(user);
            logger.warn(user.getUserId() + "[" + user.getUsername() + "]--IP:[" + IpUtils.getIpAddress() + "]--" + request.getRequestURI());
            String method = request.getMethod();
            //  权限校验
            if (!AuthorityUtils.isAdministrator()){
                boolean success = UserRolePermissionUtils.checkUserPermission(user.getUserId(), request.getRequestURI(), method);
                if (!success && !request.getRequestURI().endsWith("error/forbidden")){
                    logger.warn("用户：" + user.getUserId() + "[" + user.getUsername() + "]--IP:[" + IpUtils.getIpAddress() + "]--" + request.getRequestURI() + "--无此权限");
                    response.sendRedirect("/wms/error/forbidden");
                    return true;
                }
            }

            return true;
        }
        else {
            //reduceIpRequestCount();
            response.sendRedirect(request.getContextPath() + "/login");
            request.getSession().setAttribute("currentUser", null);
            return false;
        }
    }

    @Override
    public void postHandle(HttpServletRequest request, HttpServletResponse response, Object handler,
            ModelAndView modelAndView) throws Exception {

    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex)
            throws Exception {
        /*String URI = request.getRequestURI();
        if (!StringUtils.contains(URI, "/android/") && !StringUtils.contains(URI, "/foreigns")
                && !StringUtils.contains(URI, "/syncCookie") && !StringUtils.contains(URI, "/scan/deliverOrder")
                && !StringUtils.contains(URI, "/updateProcessingBagging") && !StringUtils.contains(URI, "/login")){
            reduceIpRequestCount();
        }*/
    }

    public boolean addIpRequestCount(){
        String ip = IpUtils.getIpAddress();
        if (StringUtils.isNotBlank(ip)){
            String lockKey = IP_REQUEST_LOCK_PRIFIX + ip;
            try {
                if (RedissonLockUtil.tryLock(RedisKeys.getUpSkuToLocationKey(lockKey), TimeUnit.MILLISECONDS, 1000, 5 * 1000)) {
                    String key = IP_REQUEST_PRIFIX + ip;
                    Long count = StringRedisUtils.incr(key,  5 * 60);
                    // logger.warn("IP: " + ip + " REQUEST COUNT: " + (count+1));
                    if (count >= MAX_REQUEST){
                        logger.warn("IP: " + ip + " REQUEST COUNT FULL: " + count);
                        // 里面是先GET再加1，所以超过10时要释放
                        reduceIpRequestCount();
                        return false;
                    }else {
                        return true;
                    }
                } else {
                    return false;
                }
            }catch (Exception e) {
                logger.error(e.getMessage(), e);
                return false;
            }
            finally {
                RedissonLockUtil.unlock(RedisKeys.getUpSkuToLocationKey(lockKey));
            }
        }
        return true;
    }

    public void reduceIpRequestCount(){
        String ip = IpUtils.getIpAddress();
        if (StringUtils.isNotBlank(ip)){
            String key = IP_REQUEST_PRIFIX + ip;
            StringRedisUtils.decrementAndGet(key,  5 * 60);
        }
    }

    private static final String IP_REQUEST_PRIFIX = "IP_REQUEST_PRIFIX:";
    private static final String IP_REQUEST_LOCK_PRIFIX = "IP_REQUEST_LOCK_PRIFIX:";
    private static final int MAX_REQUEST = 10;

    public static void initDataContextHolder(SaleUser user) {
        if (user != null) { // 登录成功存储方便调用
            DataContextHolder.setOperationId(String.valueOf(user.getUserId()));
            DataContextHolder.setUsername(user.getUsername());
            DataContextHolder.setContext(Constant.SALE_USER_ID, user.getUserId());
            DataContextHolder.setContext(Constant.SALE_USER_NAME, user.getName());
        }
    }
}
