package com.estone.core.controller;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import com.estone.amazon.model.AmazonAccount;
import com.estone.amazon.service.AmazonAccountService;
import com.estone.common.util.CacheUtils;
import com.estone.shop.service.ShopOrderService;
import com.estone.warehouse.service.WhLocationService;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.estone.checkin.bean.PmsPurchaseUsers;
import com.estone.checkin.utils.GetUserNameOrEmployeeNameUtil;
import com.estone.common.SelectJson;
import com.estone.common.util.CommonUtils;
import com.estone.common.util.model.ApiResult;
import com.estone.system.log.bean.WhSystemLog;
import com.estone.system.log.bean.WhSystemLogQueryCondition;
import com.estone.system.log.service.WhSystemLogService;
import com.estone.system.user.bean.SaleUser;
import com.estone.system.user.service.SaleUserService;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@RestController
@RequestMapping(value = "commonWeb")
public class CommonWebController {
    @Resource
    private WhSystemLogService whSystemLogService;
    @Resource
    private SaleUserService saleUserService;

    @Resource
    private WhLocationService whLocationService;

    @Resource
    private AmazonAccountService amazonAccountService;

    @Resource
    private ShopOrderService shopOrderService;

    @GetMapping(value = "sysLog/{module}/{relevanceId}")
    public ApiResult<?> viewPurchaseLog(@PathVariable("module") String module,
                                     @PathVariable("relevanceId") Integer relevanceId) {
        // 查询日志
        WhSystemLogQueryCondition query = new WhSystemLogQueryCondition();
        query.setModule(module);
        query.setRelevanceId(relevanceId);
        if (relevanceId != null) {
            query.setTableIndex(CommonUtils.tableIndex(relevanceId));
            List<WhSystemLog> systemLogs = whSystemLogService.queryWhSystemLogs(query, null);
            return ApiResult.newSuccess(systemLogs);
        }
        return ApiResult.newSuccess();
    }

    @GetMapping(value = "getUserSelectJson/{type}")
    public ApiResult<?> getUserSelectJson(@PathVariable String type) {
        try {
            switch (type) {
                case "PURCHASE_USER":
                    List<PmsPurchaseUsers> purchaseUser = GetUserNameOrEmployeeNameUtil.getPurchaseUser();
                    return ApiResult.newSuccess(purchaseUser);
                case "AMAZON_ACCOUNT":
                    List<AmazonAccount> amazonAccounts = amazonAccountService.queryAmazonAccountByAccountNumberList(null);
                    if (CollectionUtils.isEmpty(amazonAccounts)) {
                        return ApiResult.newSuccess();
                    }
                    List<SelectJson> amazonAccountJsons = amazonAccounts.stream().map(user -> {
                        return new SelectJson(user.getAccountNumber(), user.getAccountNumber());
                    }).collect(Collectors.toList());
                    return ApiResult.newSuccess(amazonAccountJsons);
                case "SHOP_USER":
                    List<Map<String,Object>> shopUser = shopOrderService.queryShopUserList();
                    return ApiResult.newSuccess(shopUser);
                default:
                    List<SaleUser> users = saleUserService.queryAllSaleUsers();
                    if (CollectionUtils.isEmpty(users)) {
                        return ApiResult.newSuccess();
                    }
                    List<SelectJson> selectJsons = users.stream().map(user -> {
                        return new SelectJson(user.getUserId().toString(), user.getUsername() + "-" + user.getName());
                    }).collect(Collectors.toList());
                    return ApiResult.newSuccess(selectJsons);
            }
        }
        catch (Exception e) {
            log.error(e.getMessage(), e);
            return ApiResult.newError(e.getMessage());
        }
    }

    // 获取区域下拉
    @GetMapping(value = "getLocationRegionDrop")
    public ApiResult<?> getLocationRegionDrop() {
        try {
            return ApiResult.newSuccess(whLocationService.selectRegion(null));
        } catch (Exception e) {
            log.error(e.getMessage(),e);
            return ApiResult.newError(e.getMessage());
        }
    }

    // 获取通道下拉
    @GetMapping(value = "getLocationAisleDrop")
    public ApiResult<?> getLocationAisleDrop() {
        try {
            String newPickingAisle = CacheUtils.SystemParamGet("apv_params.newPickingAisle").getParamValue();
            String olcPickingAisle = CacheUtils.SystemParamGet("apv_params.oldPickingAisle").getParamValue();
            String pickingAisle = newPickingAisle + "," + olcPickingAisle;
            // 把引号去掉
            pickingAisle = pickingAisle.replaceAll("\"", "");
            // 把空格去掉
            pickingAisle = pickingAisle.replaceAll(" ", "");
            // 用逗号将字符串分开，得到字符串数组
            String[] strs = pickingAisle.split(",");
            return ApiResult.newSuccess(Arrays.asList(strs));
        } catch (Exception e) {
            log.error(e.getMessage(),e);
            return ApiResult.newError(e.getMessage());
        }
    }
}
