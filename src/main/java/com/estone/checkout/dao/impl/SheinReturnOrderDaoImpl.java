package com.estone.checkout.dao.impl;

import com.estone.checkout.bean.SheinReturnOrder;
import com.estone.checkout.bean.SheinReturnOrderQueryCondition;
import com.estone.checkout.dao.SheinReturnOrderDao;
import com.estone.checkout.dao.mapper.SheinReturnOrderDBField;
import com.estone.checkout.dao.mapper.SheinReturnOrderMapper;
import com.estone.common.util.CommonUtils;
import com.estone.common.util.SqlerTemplate;
import com.whq.tool.component.Pager;
import com.whq.tool.sqler.SqlerRequest;
import com.whq.tool.sqler.analyzer.DataType;
import com.whq.tool.sqler.dialect.DialectFactory;
import com.whq.tool.sqler.dialect.SQLDialect;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Repository;
import org.springframework.util.Assert;

import java.util.List;

@Repository("sheinReturnOrderDao")
public class SheinReturnOrderDaoImpl implements SheinReturnOrderDao {

    private void setQueryCondition(SqlerRequest request, SheinReturnOrderQueryCondition query) {
        if (query == null) {
            return;
        }
        // 添加只读权限
        if (query.getReadOnly() != null && query.getReadOnly())
        {
            request.setReadOnly(true);
        }
        // 添加查询条件
        // 搜索条件不允许出现空字符
        request.setAllowBlank(false);

        request.addDataParam(SheinReturnOrderDBField.ID, DataType.INT, query.getId());
        request.addDataParam(SheinReturnOrderDBField.HANDLE_TYPE, DataType.STRING, query.getHandleType());
        request.addDataParam(SheinReturnOrderDBField.RETURN_METHOD, DataType.STRING, query.getReturnMethod());
        request.addDataParam(SheinReturnOrderDBField.PLATFORM_STATUS, DataType.STRING, query.getPlatformStatus());
        request.addDataParam(SheinReturnOrderDBField.STATUS, DataType.INT, query.getStatus());

        // 创建时间
        request.addDataParam("createDateStart", DataType.STRING, query.getCreateDateStart());
        request.addDataParam("createDateEnd", DataType.STRING, query.getCreateDateEnd());
        // 签收时间
        request.addDataParam("receiptDateStart", DataType.STRING, query.getReceiptDateStart());
        request.addDataParam("receiptDateEnd", DataType.STRING, query.getReceiptDateEnd());
        // 处理时间
        request.addDataParam("handleDateStart", DataType.STRING, query.getHandleDateStart());
        request.addDataParam("handleDateEnd", DataType.STRING, query.getHandleDateEnd());

        if (CollectionUtils.isNotEmpty(query.getIds())) {
            request.addDataParam("ids", DataType.INT, query.getIds());
        }

        // 店铺
        if (StringUtils.isNotBlank(query.getAccountNumber())){
            if (query.getAccountNumber().contains(",")) {
                request.addDataParam("accountNumberList", DataType.STRING, CommonUtils.splitList(query.getAccountNumber(), ","));
            } else {
                request.addDataParam(SheinReturnOrderDBField.ACCOUNT_NUMBER, DataType.STRING, query.getAccountNumber().trim());
            }
        }
        // 退货单号
        if (StringUtils.isNotBlank(query.getReturnNo())){
            if (query.getReturnNo().contains(",")) {
                request.addDataParam("returnNoList", DataType.STRING, CommonUtils.splitList(query.getReturnNo(), ","));
            } else {
                request.addDataParam(SheinReturnOrderDBField.RETURN_NO, DataType.STRING, query.getReturnNo().trim());
            }
        }
        // 退货计划单号
        if (StringUtils.isNotBlank(query.getReturnPlanNo())){
            if (query.getReturnPlanNo().contains(",")) {
                request.addDataParam("returnPlanNoList", DataType.STRING, CommonUtils.splitList(query.getReturnPlanNo(), ","));
            } else {
                request.addDataParam(SheinReturnOrderDBField.RETURN_PLAN_NO, DataType.STRING, query.getReturnPlanNo().trim());
            }
        }
        // SKU
        if (StringUtils.isNotBlank(query.getSku())) {
            request.addDataParam("skuList", DataType.STRING, CommonUtils.splitList(query.getSku(), ","));
        }
        // 发货单号
        if (StringUtils.isNotBlank(query.getDeliverOrderNo())) {
            request.addDataParam("deliverOrderNoList", DataType.STRING, CommonUtils.splitList(query.getDeliverOrderNo(), ","));
        }
        // 快递单号
        if (StringUtils.isNotBlank(query.getExpressNo())){
            if (query.getExpressNo().contains(",")) {
                List<String> nos = CommonUtils.splitList(query.getExpressNo(), ",");
                request.addSqlDataParam("EXPRESS_NO_FILTER", " AND express_no regexp '" + StringUtils.join(nos, "|") + "' ");
            } else {
                request.addDataParam("expressNoLike", DataType.STRING, "%" + query.getExpressNo().trim() + "%");
            }
        }
        if (CollectionUtils.isNotEmpty(query.getReturnNoList())) {
            request.addDataParam("returnNoList", DataType.STRING, query.getReturnNoList());
        }
    }

    @Override
    public int querySheinReturnOrderCount(SheinReturnOrderQueryCondition query) {
        SqlerRequest request = new SqlerRequest("querySheinReturnOrderCount");
        setQueryCondition(request, query);
        return SqlerTemplate.queryForInt(request);
    }

    @Override
    public List<SheinReturnOrder> querySheinReturnOrderAndItems(SheinReturnOrderQueryCondition query, Pager pager) {
        SqlerRequest request = new SqlerRequest("querySheinReturnOrderAndItems");
        setQueryCondition(request, query);
        if(pager != null) {
            SQLDialect dial = DialectFactory.createDialect(null);
            long start = (long) (pager.getPageNo() - 1) * pager.getPageSize();
            long end = start + pager.getPageSize() - 1L;
            request.addSqlDataParam("LIMIT", dial.queryFirst2Last("", (int) start, (int) end));
        }
        return SqlerTemplate.query(request, new SheinReturnOrderMapper(true));
    }

    @Override
    public List<SheinReturnOrder> querySheinReturnOrderList(SheinReturnOrderQueryCondition query, Pager pager) {
        SqlerRequest request = new SqlerRequest("querySheinReturnOrderList");
        setQueryCondition(request, query);
        if(pager != null) {
            request.addFetch(pager.getPageNo(), pager.getPageSize());
        }
        return SqlerTemplate.query(request, new SheinReturnOrderMapper());
    }

    @Override
    public SheinReturnOrder querySheinReturnOrder(Integer primaryKey) {
        Assert.notNull(primaryKey, "primaryKey is null!");
        SqlerRequest request = new SqlerRequest("querySheinReturnOrderByPrimaryKey");
        request.addDataParam(SheinReturnOrderDBField.ID, DataType.INT, primaryKey);
        return SqlerTemplate.queryForObject(request, new SheinReturnOrderMapper());
    }

    @Override
    public SheinReturnOrder querySheinReturnOrder(SheinReturnOrderQueryCondition query) {
        SqlerRequest request = new SqlerRequest("querySheinReturnOrder");
        setQueryCondition(request, query);
        return SqlerTemplate.queryForObject(request, new SheinReturnOrderMapper());
    }

    @Override
    public void createSheinReturnOrder(SheinReturnOrder entity) {
        SqlerRequest request = new SqlerRequest("createSheinReturnOrder");
        request.addDataParam(SheinReturnOrderDBField.SHOP_ID, DataType.STRING, entity.getShopId());
        request.addDataParam(SheinReturnOrderDBField.ACCOUNT_NUMBER, DataType.STRING, entity.getAccountNumber());
        request.addDataParam(SheinReturnOrderDBField.RETURN_NO, DataType.STRING, entity.getReturnNo());
        request.addDataParam(SheinReturnOrderDBField.RETURN_PLAN_NO, DataType.STRING, entity.getReturnPlanNo());
        request.addDataParam(SheinReturnOrderDBField.HANDLE_TYPE, DataType.STRING, entity.getHandleType());
        request.addDataParam(SheinReturnOrderDBField.RETURN_METHOD, DataType.STRING, entity.getReturnMethod());
        request.addDataParam(SheinReturnOrderDBField.PLATFORM_STATUS, DataType.STRING, entity.getPlatformStatus());
        request.addDataParam(SheinReturnOrderDBField.STATUS, DataType.INT, entity.getStatus());
        request.addDataParam(SheinReturnOrderDBField.REASON, DataType.STRING, entity.getReason());
        request.addDataParam(SheinReturnOrderDBField.REMARK, DataType.STRING, entity.getRemark());
        request.addDataParam(SheinReturnOrderDBField.RETURN_WAREHOUSE, DataType.STRING, entity.getReturnWarehouse());
        request.addDataParam(SheinReturnOrderDBField.EXPRESS_NO, DataType.STRING, entity.getExpressNo());
        request.addDataParam(SheinReturnOrderDBField.SKU_COUNT, DataType.INT, entity.getSkuCount());
        request.addDataParam(SheinReturnOrderDBField.RETURN_QUANTITY_TOTAL, DataType.INT, entity.getReturnQuantityTotal());
        request.addDataParam(SheinReturnOrderDBField.CREATE_DATE, DataType.TIMESTAMP, entity.getCreateDate());
        request.addDataParam(SheinReturnOrderDBField.RECEIPT_DATE, DataType.TIMESTAMP, entity.getReceiptDate());
        request.addDataParam(SheinReturnOrderDBField.HANDLE_DATE, DataType.TIMESTAMP, entity.getHandleDate());
        request.addDataParam(SheinReturnOrderDBField.UPDATE_TIME, DataType.TIMESTAMP, entity.getUpdateTime());
        Integer autoIncrementId = SqlerTemplate.executeAndReturn(request);
        entity.setId(autoIncrementId);
    }

    @Override
    public void updateSheinReturnOrder(SheinReturnOrder entity) {
        SqlerRequest request = new SqlerRequest("updateSheinReturnOrderByPrimaryKey");
        request.addDataParam(SheinReturnOrderDBField.ID, DataType.INT, entity.getId());
        
        request.addDataParam(SheinReturnOrderDBField.SHOP_ID, DataType.STRING, entity.getShopId());
        request.addDataParam(SheinReturnOrderDBField.ACCOUNT_NUMBER, DataType.STRING, entity.getAccountNumber());
        request.addDataParam(SheinReturnOrderDBField.RETURN_NO, DataType.STRING, entity.getReturnNo());
        request.addDataParam(SheinReturnOrderDBField.RETURN_PLAN_NO, DataType.STRING, entity.getReturnPlanNo());
        request.addDataParam(SheinReturnOrderDBField.HANDLE_TYPE, DataType.STRING, entity.getHandleType());
        request.addDataParam(SheinReturnOrderDBField.RETURN_METHOD, DataType.STRING, entity.getReturnMethod());
        request.addDataParam(SheinReturnOrderDBField.PLATFORM_STATUS, DataType.STRING, entity.getPlatformStatus());
        request.addDataParam(SheinReturnOrderDBField.STATUS, DataType.INT, entity.getStatus());
        request.addDataParam(SheinReturnOrderDBField.REASON, DataType.STRING, entity.getReason());
        request.addDataParam(SheinReturnOrderDBField.REMARK, DataType.STRING, entity.getRemark());
        request.addDataParam(SheinReturnOrderDBField.RETURN_WAREHOUSE, DataType.STRING, entity.getReturnWarehouse());
        request.addDataParam(SheinReturnOrderDBField.EXPRESS_NO, DataType.STRING, entity.getExpressNo());
        request.addDataParam(SheinReturnOrderDBField.SKU_COUNT, DataType.INT, entity.getSkuCount());
        request.addDataParam(SheinReturnOrderDBField.RETURN_QUANTITY_TOTAL, DataType.INT, entity.getReturnQuantityTotal());
        request.addDataParam(SheinReturnOrderDBField.CREATE_DATE, DataType.TIMESTAMP, entity.getCreateDate());
        request.addDataParam(SheinReturnOrderDBField.RECEIPT_DATE, DataType.TIMESTAMP, entity.getReceiptDate());
        request.addDataParam(SheinReturnOrderDBField.HANDLE_DATE, DataType.TIMESTAMP, entity.getHandleDate());
        request.addDataParam(SheinReturnOrderDBField.UPDATE_TIME, DataType.TIMESTAMP, entity.getUpdateTime());
        SqlerTemplate.execute(request);
    }

    @Override
    public void batchCreateSheinReturnOrder(List<SheinReturnOrder> entityList) {
        if (entityList != null && !entityList.isEmpty()) {
            SqlerRequest request = new SqlerRequest("createSheinReturnOrder");
            for (SheinReturnOrder entity : entityList) {
                request.addBatchDataParam(SheinReturnOrderDBField.SHOP_ID, DataType.STRING, entity.getShopId());
                request.addBatchDataParam(SheinReturnOrderDBField.ACCOUNT_NUMBER, DataType.STRING, entity.getAccountNumber());
                request.addBatchDataParam(SheinReturnOrderDBField.RETURN_NO, DataType.STRING, entity.getReturnNo());
                request.addBatchDataParam(SheinReturnOrderDBField.RETURN_PLAN_NO, DataType.STRING, entity.getReturnPlanNo());
                request.addBatchDataParam(SheinReturnOrderDBField.HANDLE_TYPE, DataType.STRING, entity.getHandleType());
                request.addBatchDataParam(SheinReturnOrderDBField.RETURN_METHOD, DataType.STRING, entity.getReturnMethod());
                request.addBatchDataParam(SheinReturnOrderDBField.PLATFORM_STATUS, DataType.STRING, entity.getPlatformStatus());
                request.addBatchDataParam(SheinReturnOrderDBField.STATUS, DataType.INT, entity.getStatus());
                request.addBatchDataParam(SheinReturnOrderDBField.REASON, DataType.STRING, entity.getReason());
                request.addBatchDataParam(SheinReturnOrderDBField.REMARK, DataType.STRING, entity.getRemark());
                request.addBatchDataParam(SheinReturnOrderDBField.RETURN_WAREHOUSE, DataType.STRING, entity.getReturnWarehouse());
                request.addBatchDataParam(SheinReturnOrderDBField.EXPRESS_NO, DataType.STRING, entity.getExpressNo());
                request.addBatchDataParam(SheinReturnOrderDBField.SKU_COUNT, DataType.INT, entity.getSkuCount());
                request.addBatchDataParam(SheinReturnOrderDBField.RETURN_QUANTITY_TOTAL, DataType.INT, entity.getReturnQuantityTotal());
                request.addBatchDataParam(SheinReturnOrderDBField.CREATE_DATE, DataType.TIMESTAMP, entity.getCreateDate());
                request.addBatchDataParam(SheinReturnOrderDBField.RECEIPT_DATE, DataType.TIMESTAMP, entity.getReceiptDate());
                request.addBatchDataParam(SheinReturnOrderDBField.HANDLE_DATE, DataType.TIMESTAMP, entity.getHandleDate());
                request.addBatchDataParam(SheinReturnOrderDBField.UPDATE_TIME, DataType.TIMESTAMP, entity.getUpdateTime());
                request.addBatch();
            }
            SqlerTemplate.batchUpdate(request);
        }
    }

    @Override
    public void batchUpdateSheinReturnOrder(List<SheinReturnOrder> entityList) {
        if (entityList != null && !entityList.isEmpty()) {
            SqlerRequest request = new SqlerRequest("updateSheinReturnOrderByPrimaryKey");
            for (SheinReturnOrder entity : entityList) {
                request.addBatchDataParam(SheinReturnOrderDBField.ID, DataType.INT, entity.getId());
                
                request.addBatchDataParam(SheinReturnOrderDBField.SHOP_ID, DataType.STRING, entity.getShopId());
                request.addBatchDataParam(SheinReturnOrderDBField.ACCOUNT_NUMBER, DataType.STRING, entity.getAccountNumber());
                request.addBatchDataParam(SheinReturnOrderDBField.RETURN_NO, DataType.STRING, entity.getReturnNo());
                request.addBatchDataParam(SheinReturnOrderDBField.RETURN_PLAN_NO, DataType.STRING, entity.getReturnPlanNo());
                request.addBatchDataParam(SheinReturnOrderDBField.HANDLE_TYPE, DataType.STRING, entity.getHandleType());
                request.addBatchDataParam(SheinReturnOrderDBField.RETURN_METHOD, DataType.STRING, entity.getReturnMethod());
                request.addBatchDataParam(SheinReturnOrderDBField.PLATFORM_STATUS, DataType.STRING, entity.getPlatformStatus());
                request.addBatchDataParam(SheinReturnOrderDBField.STATUS, DataType.INT, entity.getStatus());
                request.addBatchDataParam(SheinReturnOrderDBField.REASON, DataType.STRING, entity.getReason());
                request.addBatchDataParam(SheinReturnOrderDBField.REMARK, DataType.STRING, entity.getRemark());
                request.addBatchDataParam(SheinReturnOrderDBField.RETURN_WAREHOUSE, DataType.STRING, entity.getReturnWarehouse());
                request.addBatchDataParam(SheinReturnOrderDBField.EXPRESS_NO, DataType.STRING, entity.getExpressNo());
                request.addBatchDataParam(SheinReturnOrderDBField.SKU_COUNT, DataType.INT, entity.getSkuCount());
                request.addBatchDataParam(SheinReturnOrderDBField.RETURN_QUANTITY_TOTAL, DataType.INT, entity.getReturnQuantityTotal());
                request.addBatchDataParam(SheinReturnOrderDBField.CREATE_DATE, DataType.TIMESTAMP, entity.getCreateDate());
                request.addBatchDataParam(SheinReturnOrderDBField.RECEIPT_DATE, DataType.TIMESTAMP, entity.getReceiptDate());
                request.addBatchDataParam(SheinReturnOrderDBField.HANDLE_DATE, DataType.TIMESTAMP, entity.getHandleDate());
                request.addBatchDataParam(SheinReturnOrderDBField.UPDATE_TIME, DataType.TIMESTAMP, entity.getUpdateTime());
                request.addBatch();
            }
            SqlerTemplate.batchUpdate(request);
        }
    }

    @Override
    public void deleteSheinReturnOrder(Integer primaryKey) {
        SqlerRequest request = new SqlerRequest("deleteSheinReturnOrderByPrimaryKey");
        request.addDataParam(SheinReturnOrderDBField.ID, DataType.INT, primaryKey);
        SqlerTemplate.execute(request);
    }
}