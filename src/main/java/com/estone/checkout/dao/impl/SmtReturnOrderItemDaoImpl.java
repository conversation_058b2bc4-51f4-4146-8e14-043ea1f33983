package com.estone.checkout.dao.impl;

import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;
import org.springframework.util.Assert;

import com.estone.checkout.bean.SmtReturnOrderItem;
import com.estone.checkout.bean.SmtReturnOrderItemQueryCondition;
import com.estone.checkout.dao.SmtReturnOrderItemDao;
import com.estone.checkout.dao.mapper.SmtReturnOrderItemDBField;
import com.estone.checkout.dao.mapper.SmtReturnOrderItemMapper;
import com.estone.common.util.SqlerTemplate;
import com.whq.tool.component.Pager;
import com.whq.tool.sqler.SqlerRequest;
import com.whq.tool.sqler.analyzer.DataType;

@Repository("smtReturnOrderItemDao")
public class SmtReturnOrderItemDaoImpl implements SmtReturnOrderItemDao {

    private void setQueryCondition(SqlerRequest request, SmtReturnOrderItemQueryCondition query) {
        if (query == null) {
            return;
        }
        // 添加只读权限
        if (query.getReadOnly() != null && query.getReadOnly())
        {
            request.setReadOnly(true);
        }
        // 添加查询条件
        // 搜索条件不允许出现空字符
        request.setAllowBlank(false);
        
        request.addDataParam(SmtReturnOrderItemDBField.ID, DataType.INT, query.getId());
        request.addDataParam(SmtReturnOrderItemDBField.RETURN_ID, DataType.INT, query.getReturnId());
        request.addDataParam(SmtReturnOrderItemDBField.BARCODE, DataType.STRING, query.getBarcode());

        String sql = " AND return_id in (select id from smt_return_order where 1=1 ";

        if (StringUtils.isNotBlank(query.getFromUpdateTime()) || StringUtils.isNotBlank(query.getToUpdateTime())
                || StringUtils.isNotBlank(query.getBizType()) || StringUtils.isNotBlank(query.getType())) {
            if (StringUtils.isNotBlank(query.getFromUpdateTime())) {
                sql += " AND checkout_time >= '" + query.getFromUpdateTime() + "' ";
            }
            if (StringUtils.isNotBlank(query.getToUpdateTime())) {
                sql += " AND checkout_time <= '" + query.getToUpdateTime() + "' ";
            }
            if (StringUtils.isNotBlank(query.getBizType())) {
                sql += " AND biz_type = '" + query.getBizType() + "' ";
            }
            if (StringUtils.isNotBlank(query.getType())) {
                sql += " AND type = '" + query.getType() + "' ";
            }
            sql += ") ";
            request.addSqlDataParam("ORDER_FILTER", sql);
        }

        request.addDataParam("returnIdList", DataType.INT, query.getReturnIds());

        request.addDataParam("orderStatus", DataType.INT, query.getOrderStatus());
    }

    @Override
    public int querySmtReturnOrderItemCount(SmtReturnOrderItemQueryCondition query) {
        SqlerRequest request = new SqlerRequest("querySmtReturnOrderItemCount");
        setQueryCondition(request, query);
        return SqlerTemplate.queryForInt(request);
    }

    @Override
    public List<SmtReturnOrderItem> querySmtReturnOrderItemList() {
        SqlerRequest request = new SqlerRequest("querySmtReturnOrderItemList");
        return SqlerTemplate.query(request, new SmtReturnOrderItemMapper());
    }

    @Override
    public List<SmtReturnOrderItem> querySmtReturnOrderItemList(SmtReturnOrderItemQueryCondition query, Pager pager) {
        SqlerRequest request = new SqlerRequest("querySmtReturnOrderItemList");
        setQueryCondition(request, query);
        if(pager != null) {
            request.addFetch(pager.getPageNo(), pager.getPageSize());
        }
        return SqlerTemplate.query(request, new SmtReturnOrderItemMapper());
    }

    @Override
    public SmtReturnOrderItem querySmtReturnOrderItem(Integer primaryKey) {
        Assert.notNull(primaryKey, "primaryKey is null!");
        SqlerRequest request = new SqlerRequest("querySmtReturnOrderItemByPrimaryKey");
        request.addDataParam(SmtReturnOrderItemDBField.ID, DataType.INT, primaryKey);
        return SqlerTemplate.queryForObject(request, new SmtReturnOrderItemMapper());
    }

    @Override
    public SmtReturnOrderItem querySmtReturnOrderItem(SmtReturnOrderItemQueryCondition query) {
        SqlerRequest request = new SqlerRequest("querySmtReturnOrderItem");
        setQueryCondition(request, query);
        return SqlerTemplate.queryForObject(request, new SmtReturnOrderItemMapper());
    }

    @Override
    public void createSmtReturnOrderItem(SmtReturnOrderItem entity) {
        SqlerRequest request = new SqlerRequest("createSmtReturnOrderItem");
        request.addDataParam(SmtReturnOrderItemDBField.RETURN_ID, DataType.INT, entity.getReturnId());
        request.addDataParam(SmtReturnOrderItemDBField.ORIGIN_ORDER_NO, DataType.STRING, entity.getOriginOrderNo());
        request.addDataParam(SmtReturnOrderItemDBField.ORIGIN_OUTBOUND_NO, DataType.STRING, entity.getOriginOutboundNo());
        request.addDataParam(SmtReturnOrderItemDBField.INVENTORY_TYPE, DataType.STRING, entity.getInventoryType());
        request.addDataParam(SmtReturnOrderItemDBField.RETURN_REASON, DataType.STRING, entity.getReturnReason());
        request.addDataParam(SmtReturnOrderItemDBField.OUT_BOUND_STATUS, DataType.STRING, entity.getOutBoundStatus());
        request.addDataParam(SmtReturnOrderItemDBField.SIGN_STATUS, DataType.STRING, entity.getSignStatus());
        request.addDataParam(SmtReturnOrderItemDBField.TITLE, DataType.STRING, entity.getTitle());
        request.addDataParam(SmtReturnOrderItemDBField.SC_ITEM_ID, DataType.STRING, entity.getScItemId());
        request.addDataParam(SmtReturnOrderItemDBField.BARCODE, DataType.STRING, entity.getBarcode());
        request.addDataParam(SmtReturnOrderItemDBField.CATEGORY, DataType.STRING, entity.getCategory());
        request.addDataParam(SmtReturnOrderItemDBField.QUANTITY, DataType.INT, entity.getQuantity());
        request.addDataParam(SmtReturnOrderItemDBField.RETURN_QUANTITY, DataType.INT, entity.getReturnQuantity());
        request.addDataParam(SmtReturnOrderItemDBField.GOOD_QUANTITY, DataType.INT, entity.getGoodQuantity());
        request.addDataParam(SmtReturnOrderItemDBField.BAD_QUANTITY, DataType.INT, entity.getBadQuantity());
        Integer autoIncrementId = SqlerTemplate.executeAndReturn(request);
        entity.setId(autoIncrementId);
    }

    @Override
    public void updateSmtReturnOrderItem(SmtReturnOrderItem entity) {
        SqlerRequest request = new SqlerRequest("updateSmtReturnOrderItemByPrimaryKey");
        request.addDataParam(SmtReturnOrderItemDBField.ID, DataType.INT, entity.getId());
        
        request.addDataParam(SmtReturnOrderItemDBField.RETURN_ID, DataType.INT, entity.getReturnId());
        request.addDataParam(SmtReturnOrderItemDBField.ORIGIN_ORDER_NO, DataType.STRING, entity.getOriginOrderNo());
        request.addDataParam(SmtReturnOrderItemDBField.ORIGIN_OUTBOUND_NO, DataType.STRING, entity.getOriginOutboundNo());
        request.addDataParam(SmtReturnOrderItemDBField.INVENTORY_TYPE, DataType.STRING, entity.getInventoryType());
        request.addDataParam(SmtReturnOrderItemDBField.RETURN_REASON, DataType.STRING, entity.getReturnReason());
        request.addDataParam(SmtReturnOrderItemDBField.OUT_BOUND_STATUS, DataType.STRING, entity.getOutBoundStatus());
        request.addDataParam(SmtReturnOrderItemDBField.SIGN_STATUS, DataType.STRING, entity.getSignStatus());
        request.addDataParam(SmtReturnOrderItemDBField.TITLE, DataType.STRING, entity.getTitle());
        request.addDataParam(SmtReturnOrderItemDBField.SC_ITEM_ID, DataType.STRING, entity.getScItemId());
        request.addDataParam(SmtReturnOrderItemDBField.BARCODE, DataType.STRING, entity.getBarcode());
        request.addDataParam(SmtReturnOrderItemDBField.CATEGORY, DataType.STRING, entity.getCategory());
        request.addDataParam(SmtReturnOrderItemDBField.QUANTITY, DataType.INT, entity.getQuantity());
        request.addDataParam(SmtReturnOrderItemDBField.RETURN_QUANTITY, DataType.INT, entity.getReturnQuantity());
        request.addDataParam(SmtReturnOrderItemDBField.GOOD_QUANTITY, DataType.INT, entity.getGoodQuantity());
        request.addDataParam(SmtReturnOrderItemDBField.BAD_QUANTITY, DataType.INT, entity.getBadQuantity());
        SqlerTemplate.execute(request);
    }

    @Override
    public void batchCreateSmtReturnOrderItem(List<SmtReturnOrderItem> entityList) {
        if (entityList != null && !entityList.isEmpty()) {
            SqlerRequest request = new SqlerRequest("createSmtReturnOrderItem");
            for (SmtReturnOrderItem entity : entityList) {
                request.addBatchDataParam(SmtReturnOrderItemDBField.RETURN_ID, DataType.INT, entity.getReturnId());
                request.addBatchDataParam(SmtReturnOrderItemDBField.ORIGIN_ORDER_NO, DataType.STRING, entity.getOriginOrderNo());
                request.addBatchDataParam(SmtReturnOrderItemDBField.ORIGIN_OUTBOUND_NO, DataType.STRING, entity.getOriginOutboundNo());
                request.addBatchDataParam(SmtReturnOrderItemDBField.INVENTORY_TYPE, DataType.STRING, entity.getInventoryType());
                request.addBatchDataParam(SmtReturnOrderItemDBField.RETURN_REASON, DataType.STRING, entity.getReturnReason());
                request.addBatchDataParam(SmtReturnOrderItemDBField.OUT_BOUND_STATUS, DataType.STRING, entity.getOutBoundStatus());
                request.addBatchDataParam(SmtReturnOrderItemDBField.SIGN_STATUS, DataType.STRING, entity.getSignStatus());
                request.addBatchDataParam(SmtReturnOrderItemDBField.TITLE, DataType.STRING, entity.getTitle());
                request.addBatchDataParam(SmtReturnOrderItemDBField.SC_ITEM_ID, DataType.STRING, entity.getScItemId());
                request.addBatchDataParam(SmtReturnOrderItemDBField.BARCODE, DataType.STRING, entity.getBarcode());
                request.addBatchDataParam(SmtReturnOrderItemDBField.CATEGORY, DataType.STRING, entity.getCategory());
                request.addBatchDataParam(SmtReturnOrderItemDBField.QUANTITY, DataType.INT, entity.getQuantity());
                request.addBatchDataParam(SmtReturnOrderItemDBField.RETURN_QUANTITY, DataType.INT, entity.getReturnQuantity());
                request.addBatchDataParam(SmtReturnOrderItemDBField.GOOD_QUANTITY, DataType.INT, entity.getGoodQuantity());
                request.addBatchDataParam(SmtReturnOrderItemDBField.BAD_QUANTITY, DataType.INT, entity.getBadQuantity());
                request.addBatch();
            }
            SqlerTemplate.batchUpdate(request);
        }
    }

    @Override
    public void batchUpdateSmtReturnOrderItem(List<SmtReturnOrderItem> entityList) {
        if (entityList != null && !entityList.isEmpty()) {
            SqlerRequest request = new SqlerRequest("updateSmtReturnOrderItemByPrimaryKey");
            for (SmtReturnOrderItem entity : entityList) {
                request.addBatchDataParam(SmtReturnOrderItemDBField.ID, DataType.INT, entity.getId());
                
                request.addBatchDataParam(SmtReturnOrderItemDBField.RETURN_ID, DataType.INT, entity.getReturnId());
                request.addBatchDataParam(SmtReturnOrderItemDBField.ORIGIN_ORDER_NO, DataType.STRING, entity.getOriginOrderNo());
                request.addBatchDataParam(SmtReturnOrderItemDBField.ORIGIN_OUTBOUND_NO, DataType.STRING, entity.getOriginOutboundNo());
                request.addBatchDataParam(SmtReturnOrderItemDBField.INVENTORY_TYPE, DataType.STRING, entity.getInventoryType());
                request.addBatchDataParam(SmtReturnOrderItemDBField.RETURN_REASON, DataType.STRING, entity.getReturnReason());
                request.addBatchDataParam(SmtReturnOrderItemDBField.OUT_BOUND_STATUS, DataType.STRING, entity.getOutBoundStatus());
                request.addBatchDataParam(SmtReturnOrderItemDBField.SIGN_STATUS, DataType.STRING, entity.getSignStatus());
                request.addBatchDataParam(SmtReturnOrderItemDBField.TITLE, DataType.STRING, entity.getTitle());
                request.addBatchDataParam(SmtReturnOrderItemDBField.SC_ITEM_ID, DataType.STRING, entity.getScItemId());
                request.addBatchDataParam(SmtReturnOrderItemDBField.BARCODE, DataType.STRING, entity.getBarcode());
                request.addBatchDataParam(SmtReturnOrderItemDBField.CATEGORY, DataType.STRING, entity.getCategory());
                request.addBatchDataParam(SmtReturnOrderItemDBField.QUANTITY, DataType.INT, entity.getQuantity());
                request.addBatchDataParam(SmtReturnOrderItemDBField.RETURN_QUANTITY, DataType.INT, entity.getReturnQuantity());
                request.addBatchDataParam(SmtReturnOrderItemDBField.GOOD_QUANTITY, DataType.INT, entity.getGoodQuantity());
                request.addBatchDataParam(SmtReturnOrderItemDBField.BAD_QUANTITY, DataType.INT, entity.getBadQuantity());
                request.addBatch();
            }
            SqlerTemplate.batchUpdate(request);
        }
    }

    @Override
    public void deleteSmtReturnOrderItem(Integer primaryKey) {
        SqlerRequest request = new SqlerRequest("deleteSmtReturnOrderItemByPrimaryKey");
        request.addDataParam(SmtReturnOrderItemDBField.ID, DataType.INT, primaryKey);
        SqlerTemplate.execute(request);
    }
}