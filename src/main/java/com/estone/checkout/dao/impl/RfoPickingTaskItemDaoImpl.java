package com.estone.checkout.dao.impl;

import com.estone.checkout.bean.RfoPickingTaskItem;
import com.estone.checkout.bean.RfoPickingTaskItemQueryCondition;
import com.estone.checkout.dao.RfoPickingTaskItemDao;
import com.estone.checkout.dao.mapper.RfoPickingTaskItemDBField;
import com.estone.checkout.dao.mapper.RfoPickingTaskItemMapper;
import com.whq.tool.component.Pager;
import com.whq.tool.context.DataContextHolder;
import com.whq.tool.sqler.SqlerRequest;
import com.whq.tool.sqler.analyzer.DataType;
import com.estone.common.util.SqlerTemplate;
import java.sql.Timestamp;
import java.util.List;
import org.springframework.stereotype.Repository;
import org.springframework.util.Assert;

@Repository("rfoPickingTaskItemDao")
public class RfoPickingTaskItemDaoImpl implements RfoPickingTaskItemDao {

    private void setQueryCondition(SqlerRequest request, RfoPickingTaskItemQueryCondition query) {
        if (query == null) {
            return;
        }
        // 添加查询条件
        // 搜索条件不允许出现空字符
        request.setAllowBlank(false);
        
        request.addDataParam(RfoPickingTaskItemDBField.ID, DataType.INT, query.getId());
        request.addDataParam(RfoPickingTaskItemDBField.RFO_ID, DataType.INT, query.getRfoId());
        request.addDataParam(RfoPickingTaskItemDBField.TASK_ID, DataType.INT, query.getTaskId());
    }

    @Override
    public int queryRfoPickingTaskItemCount(RfoPickingTaskItemQueryCondition query) {
        SqlerRequest request = new SqlerRequest("queryRfoPickingTaskItemCount");
        setQueryCondition(request, query);
        return SqlerTemplate.queryForInt(request);
    }

    @Override
    public List<RfoPickingTaskItem> queryRfoPickingTaskItemList() {
        SqlerRequest request = new SqlerRequest("queryRfoPickingTaskItemList");
        return SqlerTemplate.query(request, new RfoPickingTaskItemMapper());
    }

    @Override
    public List<RfoPickingTaskItem> queryRfoPickingTaskItemList(RfoPickingTaskItemQueryCondition query, Pager pager) {
        SqlerRequest request = new SqlerRequest("queryRfoPickingTaskItemList");
        setQueryCondition(request, query);
        if(pager != null) {
            request.addFetch(pager.getPageNo(), pager.getPageSize());
        }
        return SqlerTemplate.query(request, new RfoPickingTaskItemMapper());
    }

    @Override
    public RfoPickingTaskItem queryRfoPickingTaskItem(Integer primaryKey) {
        Assert.notNull(primaryKey, "primaryKey is null!");
        SqlerRequest request = new SqlerRequest("queryRfoPickingTaskItemByPrimaryKey");
        request.addDataParam(RfoPickingTaskItemDBField.ID, DataType.INT, primaryKey);
        return SqlerTemplate.queryForObject(request, new RfoPickingTaskItemMapper());
    }

    @Override
    public RfoPickingTaskItem queryRfoPickingTaskItem(RfoPickingTaskItemQueryCondition query) {
        SqlerRequest request = new SqlerRequest("queryRfoPickingTaskItem");
        setQueryCondition(request, query);
        return SqlerTemplate.queryForObject(request, new RfoPickingTaskItemMapper());
    }

    @Override
    public void createRfoPickingTaskItem(RfoPickingTaskItem entity) {
        SqlerRequest request = new SqlerRequest("createRfoPickingTaskItem");
        request.addDataParam(RfoPickingTaskItemDBField.TASK_ID, DataType.INT, entity.getTaskId());
        request.addDataParam(RfoPickingTaskItemDBField.RFO_ID, DataType.INT, entity.getRfoId());
        request.addDataParam(RfoPickingTaskItemDBField.CREATED_DATE, DataType.TIMESTAMP, entity.getCreatedDate());
        request.addDataParam(RfoPickingTaskItemDBField.CREATE_BY, DataType.INT, entity.getCreateBy());
        request.addDataParam(RfoPickingTaskItemDBField.STATUS, DataType.INT, entity.getStatus());
        Integer autoIncrementId = SqlerTemplate.executeAndReturn(request);
        entity.setId(autoIncrementId);
    }

    @Override
    public void updateRfoPickingTaskItem(RfoPickingTaskItem entity) {
        SqlerRequest request = new SqlerRequest("updateRfoPickingTaskItemByPrimaryKey");
        request.addDataParam(RfoPickingTaskItemDBField.ID, DataType.INT, entity.getId());
        
        request.addDataParam(RfoPickingTaskItemDBField.TASK_ID, DataType.INT, entity.getTaskId());
        request.addDataParam(RfoPickingTaskItemDBField.RFO_ID, DataType.INT, entity.getRfoId());
        request.addDataParam(RfoPickingTaskItemDBField.CREATED_DATE, DataType.TIMESTAMP, entity.getCreatedDate());
        request.addDataParam(RfoPickingTaskItemDBField.CREATE_BY, DataType.INT, entity.getCreateBy());
        request.addDataParam(RfoPickingTaskItemDBField.STATUS, DataType.INT, entity.getStatus());
        SqlerTemplate.execute(request);
    }

    @Override
    public void batchCreateRfoPickingTaskItem(List<RfoPickingTaskItem> entityList) {
        if (entityList != null && !entityList.isEmpty()) {
            SqlerRequest request = new SqlerRequest("createRfoPickingTaskItem");
            for (RfoPickingTaskItem entity : entityList) {
                request.addBatchDataParam(RfoPickingTaskItemDBField.TASK_ID, DataType.INT, entity.getTaskId());
                request.addBatchDataParam(RfoPickingTaskItemDBField.RFO_ID, DataType.INT, entity.getRfoId());
                request.addBatchDataParam(RfoPickingTaskItemDBField.CREATED_DATE, DataType.TIMESTAMP, entity.getCreatedDate());
                request.addBatchDataParam(RfoPickingTaskItemDBField.CREATE_BY, DataType.INT, entity.getCreateBy());
                request.addBatchDataParam(RfoPickingTaskItemDBField.STATUS, DataType.INT, entity.getStatus());
                request.addBatch();
            }
            SqlerTemplate.batchUpdate(request);
        }
    }

    @Override
    public void batchUpdateRfoPickingTaskItem(List<RfoPickingTaskItem> entityList) {
        if (entityList != null && !entityList.isEmpty()) {
            SqlerRequest request = new SqlerRequest("updateRfoPickingTaskItemByPrimaryKey");
            for (RfoPickingTaskItem entity : entityList) {
                request.addBatchDataParam(RfoPickingTaskItemDBField.ID, DataType.INT, entity.getId());
                
                request.addBatchDataParam(RfoPickingTaskItemDBField.TASK_ID, DataType.INT, entity.getTaskId());
                request.addBatchDataParam(RfoPickingTaskItemDBField.RFO_ID, DataType.INT, entity.getRfoId());
                request.addBatchDataParam(RfoPickingTaskItemDBField.CREATED_DATE, DataType.TIMESTAMP, entity.getCreatedDate());
                request.addBatchDataParam(RfoPickingTaskItemDBField.CREATE_BY, DataType.INT, entity.getCreateBy());
                request.addBatchDataParam(RfoPickingTaskItemDBField.STATUS, DataType.INT, entity.getStatus());
                request.addBatch();
            }
            SqlerTemplate.batchUpdate(request);
        }
    }

    @Override
    public void deleteRfoPickingTaskItem(Integer primaryKey) {
        SqlerRequest request = new SqlerRequest("deleteRfoPickingTaskItemByPrimaryKey");
        request.addDataParam(RfoPickingTaskItemDBField.ID, DataType.INT, primaryKey);
        SqlerTemplate.execute(request);
    }
}