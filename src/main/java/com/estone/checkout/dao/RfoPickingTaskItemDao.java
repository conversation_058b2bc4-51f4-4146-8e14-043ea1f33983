package com.estone.checkout.dao;

import com.estone.checkout.bean.RfoPickingTaskItem;
import com.estone.checkout.bean.RfoPickingTaskItemQueryCondition;
import com.whq.tool.component.Pager;
import java.util.List;

public interface RfoPickingTaskItemDao {
    int queryRfoPickingTaskItemCount(RfoPickingTaskItemQueryCondition query);

    List<RfoPickingTaskItem> queryRfoPickingTaskItemList();

    List<RfoPickingTaskItem> queryRfoPickingTaskItemList(RfoPickingTaskItemQueryCondition query, Pager pager);

    RfoPickingTaskItem queryRfoPickingTaskItem(Integer primaryKey);

    RfoPickingTaskItem queryRfoPickingTaskItem(RfoPickingTaskItemQueryCondition query);

    void createRfoPickingTaskItem(RfoPickingTaskItem entity);

    void batchCreateRfoPickingTaskItem(List<RfoPickingTaskItem> entityList);

    void batchUpdateRfoPickingTaskItem(List<RfoPickingTaskItem> entityList);

    void deleteRfoPickingTaskItem(Integer primaryKey);

    void updateRfoPickingTaskItem(RfoPickingTaskItem entity);
}