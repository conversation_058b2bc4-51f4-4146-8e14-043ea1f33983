package com.estone.checkout.dao.mapper;

public interface WhCheckOutDBField {
    String ID = "id";

    String TYPE = "type";

    String REMARK = "remark";

    String CREATION_DATE = "creation_date";

    String CREATED_BY = "created_by";

    String LAST_UPDATE_DATE = "last_update_date";

    String LAST_UPDATE_BY = "last_update_by";

    String WAREHOUSE_ID = "warehouse_id";

    String STATUS = "status";
}