package com.estone.checkout.bean;

import lombok.Data;

import java.util.List;

@Data
public class RfoPickingTaskQueryCondition extends RfoPickingTask {
    private static final long serialVersionUID = 1L;

    private List<Integer> ids;

    private List<Integer> statusList;

    private String sku;

    private String rfoNo;

    private String fromCreatedDate;

    private String toCreatedDate;

    private String fromPickingEndDate;

    private String toPickingEndDate;
}