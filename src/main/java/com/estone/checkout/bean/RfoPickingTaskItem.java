package com.estone.checkout.bean;

import java.io.Serializable;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import com.estone.apv.common.ApvGridStatus;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;

@Data
public class RfoPickingTaskItem implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     *  database column wh_rfo_picking_task_item.id
     */
    private Integer id;

    /**
     *  database column wh_rfo_picking_task_item.task_id
     */
    private Integer taskId;

    /**
     * rfo_id database column wh_rfo_picking_task_item.rfo_id
     */
    private Integer rfoId;

    /**
     * 创建时间 database column wh_rfo_picking_task_item.created_date
     */
    private Timestamp createdDate;

    /**
     * 创建人 database column wh_rfo_picking_task_item.create_by
     */
    private Integer createBy;

    /**
     * 状态 database column wh_rfo_picking_task_item.status
     */
    private Integer status;

    private String rfoNo;
    
    private Integer gridStatus = ApvGridStatus.PENDING.intCode();

    private List<ReturnFormOrderItem> rfoItemList = new ArrayList<>();
    /**
     * SKU 分组合并
     */
    private List<ReturnFormOrderItem> groupItems;

    public Integer getSkuPcs() {
        Map<String, Integer> skuQuantityMap = rfoItemList.stream().collect(Collectors.toMap(ReturnFormOrderItem::getSku,
                o -> o.getAllotQuantity() == null ? 0 : o.getAllotQuantity(), (o1, o2) -> o2));
        return skuQuantityMap.values().stream().mapToInt(q -> q).sum();
    }

    public Integer getPickNum() {
        Map<String, Integer> skuPickQuantityMap = rfoItemList.stream()
                .collect(Collectors.toMap(ReturnFormOrderItem::getSku,
                        o -> o.getPickQuantity() == null ? 0 : o.getPickQuantity(), (o1, o2) -> o2));
        return skuPickQuantityMap.values().stream().mapToInt(q -> q).sum();
    }

    public Integer getGridNum() {
        Map<String, Integer> skuGridQuantityMap = rfoItemList.stream()
                .collect(Collectors.toMap(ReturnFormOrderItem::getSku,
                        o -> o.getGridQuantity() == null ? 0 : o.getGridQuantity(), (o1, o2) -> o2));
        return skuGridQuantityMap.values().stream().mapToInt(q -> q).sum();
    }
    public Integer getSowNum() {
        Map<String, Integer> skuSowQuantityMap = rfoItemList.stream()
                .collect(Collectors.toMap(ReturnFormOrderItem::getSku,
                        o -> o.getSowQuantity() == null ? 0 : o.getSowQuantity(), (o1, o2) -> o2));
        return skuSowQuantityMap.values().stream().mapToInt(q -> q).sum();
    }

    public List<ReturnFormOrderItem> buildGroupItems() {
        if (CollectionUtils.isNotEmpty(rfoItemList)) {
            Map<String, ReturnFormOrderItem> map = new HashMap<>();
            for (ReturnFormOrderItem orderItem : rfoItemList) {
                ReturnFormOrderItem exist = map.get(orderItem.getSku());
                if (exist == null) {
                    exist = new ReturnFormOrderItem();
                    exist.setSku(orderItem.getSku());
                    exist.setLocation(orderItem.getLocation());
                }
                // 装箱数量
                Integer loadingQuantityCount = exist.getLoadingQuantity() == null ? 0 : exist.getLoadingQuantity();

                Integer quantity = orderItem.getQuantity() == null ? 0 : orderItem.getQuantity();
                Integer allotQuantity = orderItem.getAllotQuantity() == null ? 0 : orderItem.getAllotQuantity();
                Integer gridQuantity = orderItem.getGridQuantity() == null ? 0 : orderItem.getGridQuantity();
                Integer pickQuantity = orderItem.getPickQuantity() == null ? 0 : orderItem.getPickQuantity();
                Integer loadingQuantity = orderItem.getLoadingQuantity() == null ? 0 : orderItem.getLoadingQuantity();
                Integer sowQuantity = orderItem.getSowQuantity() == null ? 0 : orderItem.getSowQuantity();

                exist.setQuantity(quantity);
                exist.setAllotQuantity(allotQuantity);
                exist.setPickQuantity(pickQuantity);
                exist.setGridQuantity(gridQuantity);
                exist.setSowQuantity(sowQuantity);
                exist.setLoadingQuantity(loadingQuantityCount + loadingQuantity);
                map.put(orderItem.getSku(), exist);
            }
            groupItems = new ArrayList<>(map.values());
        }
        return groupItems;
    }

    public Integer getGridStatus() {
        if (getGridNum() > 0 && getGridNum().equals(getPickNum()))
            return ApvGridStatus.COMPLETED.intCode();
        return gridStatus;
    }
}