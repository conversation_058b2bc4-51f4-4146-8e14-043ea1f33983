package com.estone.checkout.bean;

import java.io.Serializable;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import lombok.Data;

@Data
public class RfoPickingTask implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID database column wh_rfo_picking_task.id
     */
    private Integer id;

    /**
     * 任务号 database column wh_rfo_picking_task.task_no
     */
    private String taskNo;

    /**
     * 任务状态  0-未领取 1-已领取   database column wh_rfo_picking_task.task_status
     */
    private Integer taskStatus;

    /**
     * 创建人 database column wh_rfo_picking_task.created_by
     */
    private Integer createdBy;

    /**
     * 创建时间 database column wh_rfo_picking_task.created_date
     */
    private Timestamp createdDate;

    /**
     * 领取人 database column wh_rfo_picking_task.receive_person
     */
    private Integer receivePerson;

    /**
     * 领取时间 database column wh_rfo_picking_task.receive_date
     */
    private Timestamp receiveDate;

    /**
     * 最后修改时间 database column wh_rfo_picking_task.last_update_date
     */
    private Timestamp lastUpdateDate;

    /**
     * 最后更新人 database column wh_rfo_picking_task.last_update_by
     */
    private Integer lastUpdateBy;

    /**
     * 拣货完成时间 database column wh_rfo_picking_task.picking_end_date
     */
    private Timestamp pickingEndDate;

    /**
     * 是否已打印 database column wh_rfo_picking_task.is_printing
     */
    private Integer isPrinting;

    /**
     * 打印人 database column wh_rfo_picking_task.print_user
     */
    private Integer printUser;

    /**
     * 打印时间 database column wh_rfo_picking_task.print_date
     */
    private Timestamp printDate;

    private Integer gridStatus;

    private Integer gridBy;

    private Timestamp gridTime;

    private Integer gridDiff;

    /**
     * 任务类型 0：普通， 1：播种差异
     */
    private Integer taskType;

    private String boxNo;//播种差异周转筐

    private List<RfoPickingTaskItem> itemList = new ArrayList<>();

    public Integer getOrderNum() {
        return itemList.stream().map(RfoPickingTaskItem::getRfoId).distinct().collect(Collectors.toList()).size();
    }

    public Integer getSkuNum() {
        List<ReturnFormOrderItem> rfoItemList = new ArrayList<>();
        itemList.stream().forEach(i -> rfoItemList.addAll(i.getRfoItemList()));
        return rfoItemList.stream().map(ReturnFormOrderItem::getSku).distinct().collect(Collectors.toList()).size();
    }

    public Integer getSkuPcs() {
        return itemList.stream().mapToInt(RfoPickingTaskItem::getSkuPcs).sum();
    }

    public Integer getPickNum() {
        return itemList.stream().mapToInt(RfoPickingTaskItem::getPickNum).sum();
    }

    public Integer getGridNum() {
        return itemList.stream().mapToInt(RfoPickingTaskItem::getGridNum).sum();
    }

    public Integer getSowNum() {
        return itemList.stream().mapToInt(RfoPickingTaskItem::getSowNum).sum();
    }

    public Integer getPickDiff() {
        return getSkuPcs() - getPickNum();
    }
}