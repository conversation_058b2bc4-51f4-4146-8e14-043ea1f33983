package com.estone.checkout.bean;

import lombok.Data;

import java.util.List;

@Data
public class SmtReturnOrderQueryCondition extends SmtReturnOrder {

    private Boolean readOnly = false;

    private Boolean pageQuery = false;

    // 是否查询入仓明细
    private Boolean checkinDetail = false;

    // 出库时间
    private String checkoutTimeStart;
    private String checkoutTimeEnd;
    // 拆包时间
    private String splitTimeStart;
    private String splitTimeEnd;
    // 上架完成时间
    private String upTimeStart;
    private String upTimeEnd;
    // 播种时间
    private String gridTimeStart;
    private String gridTimeEnd;

    // 退货原因
    private String returnReason;

    private String sku;

    // 原入库PONX
    private String originOrderNo;
    // 原物流单号
    private String originOutboundNo;

    private List<String> returnOrderNos;

}