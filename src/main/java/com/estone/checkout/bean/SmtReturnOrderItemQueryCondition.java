package com.estone.checkout.bean;

import lombok.Data;

import java.util.List;

@Data
public class SmtReturnOrderItemQueryCondition extends SmtReturnOrderItem {
    private static final long serialVersionUID = 1L;

    private Boolean readOnly = false;

    private String fromUpdateTime;
    private String ToUpdateTime;

    private String bizType;

    private String type;

    private List<Integer> returnIds;

    private Integer orderStatus;

}