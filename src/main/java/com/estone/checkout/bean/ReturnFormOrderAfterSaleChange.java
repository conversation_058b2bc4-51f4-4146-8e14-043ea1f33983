package com.estone.checkout.bean;

import lombok.Data;

import java.util.List;

@Data
public class ReturnFormOrderAfterSaleChange {

    /**
     * 退货单号
     */
    private String returnOrderNo;

    /**
     * 供应商sku信息
     */
    private List<ReturnFormOrderAfterSaleSkuInfo> skuInfoList;

    @Data
    public static class ReturnFormOrderAfterSaleSkuInfo {

        private String sku;

        /**
         * 数量
         */
        private Integer quantity;

        /**
         * 供应商code
         */
        private String vendorCode;

        /**
         * 供应商名称
         */
        private String vendorName;
    }
}
