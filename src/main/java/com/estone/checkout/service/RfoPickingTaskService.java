package com.estone.checkout.service;

import com.estone.android.domain.AndroidProductDo;
import com.estone.checkout.bean.ReturnFormOrderItem;
import com.estone.checkout.bean.RfoPickingTask;
import com.estone.checkout.bean.RfoPickingTaskItem;
import com.estone.checkout.bean.RfoPickingTaskQueryCondition;
import com.whq.tool.component.Pager;
import com.whq.tool.json.ResponseJson;

import java.util.List;

public interface RfoPickingTaskService {
    List<RfoPickingTask> queryAllRfoPickingTasks();

    List<RfoPickingTask> queryRfoPickingTasks(RfoPickingTaskQueryCondition query, Pager pager);

    RfoPickingTask getRfoPickingTask(Integer id);

    RfoPickingTask getRfoPickingTaskDetail(Integer id);

    RfoPickingTask queryRfoPickingTask(RfoPickingTaskQueryCondition query);

    void createRfoPickingTask(RfoPickingTask rfoPickingTask);

    void batchCreateRfoPickingTask(List<RfoPickingTask> entityList);

    void deleteRfoPickingTask(Integer id);

    void updateRfoPickingTask(RfoPickingTask rfoPickingTask);

    void batchUpdateRfoPickingTask(List<RfoPickingTask> entityList);

    List<ReturnFormOrderItem> queryRfoPickingTaskDetail(Integer id);

    ResponseJson doReceiveTask(AndroidProductDo domain);

    ResponseJson scanningPicking(AndroidProductDo domain);

    ResponseJson getWhTaskAndSkus(RfoPickingTask task);

    ResponseJson doDiscardedRfoTask(RfoPickingTask task);
}