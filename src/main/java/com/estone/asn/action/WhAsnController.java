package com.estone.asn.action;

import com.estone.allocation.util.WarehouseProperties;
import com.estone.apv.common.ApvTaskRedisLock;
import com.estone.apv.enums.ApvTaxTypeEnum;
import com.estone.apv.enums.ApvTypeEnum;
import com.estone.apv.util.ApvPackUtils;
import com.estone.asn.bean.WhAsn;
import com.estone.asn.bean.WhAsnExtra;
import com.estone.asn.bean.WhAsnItem;
import com.estone.asn.bean.WhAsnQueryCondition;
import com.estone.asn.domain.WhAsnDo;
import com.estone.asn.enums.*;
import com.estone.asn.service.WhAsnCheckOutService;
import com.estone.asn.service.WhAsnService;
import com.estone.common.SaleChannel;
import com.estone.common.SelectJson;
import com.estone.common.enums.ExportType;
import com.estone.common.util.*;
import com.estone.common.util.model.ResultModel;
import com.estone.picking.bean.WhPickingTask;
import com.estone.picking.domain.WhMergeApvDo;
import com.estone.picking.enums.MergeApvType;
import com.estone.picking.enums.PickingTaskType;
import com.estone.sku.bean.*;
import com.estone.sku.service.WhSkuService;
import com.estone.sku.service.WhUniqueSkuService;
import com.estone.system.param.bean.SystemParam;
import com.estone.temu.bean.TemuPackageInfo;
import com.estone.temu.bean.TemuPrepareOrderQueryCondition;
import com.estone.temu.enums.TemuPackageStatus;
import com.estone.temu.service.TemuPrepareOrderService;
import com.estone.transfer.bean.WhFbaAllocation;
import com.estone.transfer.bean.WhFbaAllocationItem;
import com.estone.transfer.bean.WhFbaAllocationQueryCondition;
import com.estone.transfer.enums.AsnPrepareStatus;
import com.estone.transfer.enums.JITBoxOrderType;
import com.estone.transfer.service.JitPickBoxScanService;
import com.estone.transfer.service.WhFbaAllocationHandleService;
import com.estone.transfer.service.WhFbaAllocationService;
import com.whq.tool.action.BaseController;
import com.whq.tool.component.Pager;
import com.whq.tool.component.StatusCode;
import com.whq.tool.json.ResponseJson;
import jodd.util.ArraysUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.DataInputStream;
import java.io.File;
import java.io.IOException;
import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Controller
@RequestMapping(value = "whAsn")
public class WhAsnController extends BaseController {
    private static Logger logger = LoggerFactory.getLogger(WhAsnController.class);
    @Resource
    private WhAsnService whAsnService;
    @Resource
    private WhAsnCheckOutService whAsnCheckOutService;
    @Resource
    private WhSkuService whSkuService;

    @Resource
    private WhFbaAllocationService whFbaAllocationService;

    @Resource
    private WhUniqueSkuService whUniqueSkuService;

    @Resource
    private WhFbaAllocationHandleService whFbaAllocationHandleService;
    
    @Resource
    private JitPickBoxScanService jitPickBoxScanService;

    @Resource
    private TemuPrepareOrderService temuPrepareOrderService;

    @RequestMapping(method = {RequestMethod.GET})
    public String init(@ModelAttribute("domain") WhAsnDo domain) {
        initFormData(domain);
        queryWhAsns(domain);
        return "asn/whAsnList";
    }

    private void initFormData(@ModelAttribute("domain") WhAsnDo domain) {
        domain.setStatusJson(SelectJson.getList(AsnStatus.values()));
        domain.setAsnWhJson(SelectJson.getList(AsnWarehouseStatus.values()));
        SystemParam param = CacheUtils.SystemParamGet("ALLOCATION.ASN_SHIPPING_METHOD");
        if (param != null && StringUtils.isNotBlank(param.getParamValue())) {
            // "韵达,中通,圆通,申通,百世易通, 顺丰,德邦"
            domain.setShippingMethodList(Arrays.asList(StringUtils.split(param.getParamValue(), ",")));
        } else {
            logger.warn("请配置快海外仓出单交运物流公司选项后重试:ALLOCATION.ASN_SHIPPING_METHOD");
        }
    }

    private void queryWhAsns(@ModelAttribute("domain") WhAsnDo domain) {
        WhAsnQueryCondition query = domain.getQuery();
        Pager page = domain.getPage();
        if (query == null) {
            query = new WhAsnQueryCondition();
            domain.setQuery(query);
        }
        List<WhAsn> whAsns = whAsnService.queryWhAsnsAndItems(query, page);
        domain.setWhAsns(whAsns);
    }

    @RequestMapping(value = "search", method = {RequestMethod.POST})
    public String search(@ModelAttribute("domain") WhAsnDo domain) {
        initFormData(domain);
        queryWhAsns(domain);
        return "asn/whAsnList";
    }

    @RequestMapping(value = "create", method = {RequestMethod.GET})
    public String toCreateWhAsn(@ModelAttribute("domain") WhAsnDo domain) {
        return "{模块}/{页面}";
    }

    @RequestMapping(value = "create", method = {RequestMethod.POST})
    public String createWhAsn(@ModelAttribute("domain") WhAsnDo domain) {
        WhAsn whAsn = domain.getWhAsn();
        whAsnService.createWhAsn(whAsn);
        return "redirect:/{查询列表}";
    }

    @RequestMapping(value = "update", method = {RequestMethod.GET})
    public String toUpdateWhAsn(@ModelAttribute("domain") WhAsnDo domain, @RequestParam("whAsnId") Integer whAsnId) {
        WhAsn whAsn = whAsnService.getWhAsn(whAsnId);
        domain.setWhAsn(whAsn);
        return "{模块}/{页面}";
    }

    @RequestMapping(value = "update", method = {RequestMethod.POST})
    public String updateWhAsn(@ModelAttribute("domain") WhAsnDo domain) {
        WhAsn whAsn = domain.getWhAsn();
        whAsnService.updateWhAsn(whAsn);
        return "redirect:/{查询列表}";
    }

    @RequestMapping(value = "delete", method = {RequestMethod.GET})
    @ResponseBody
    public ResponseJson deleteWhAsn(@ModelAttribute("domain") WhAsnDo domain, @RequestParam("whAsnId") Integer whAsnId) {
        ResponseJson response = new ResponseJson();
        whAsnService.deleteWhAsn(whAsnId);
        return response;
    }

    // 同步
    @RequestMapping(value = "sync", method = {RequestMethod.POST})
    @ResponseBody
    public ResponseJson syncWhAsn(@RequestParam("fromCreationDate") String fromCreationDate,
                                  @RequestParam("toCreationDate") String toCreationDate,
                                  @RequestParam("receivingCode") String receivingCode) {
        ResponseJson response = new ResponseJson();
        response.setStatus(StatusCode.FAIL);
        List<String> failedList = new ArrayList<>();
        try {
            WhAsnQueryCondition queryCondition = new WhAsnQueryCondition();
            if (StringUtils.isNotBlank(fromCreationDate)) queryCondition.setFromCreationDate(fromCreationDate);
            if (StringUtils.isNotBlank(toCreationDate)) queryCondition.setToCreationDate(toCreationDate);
            if (StringUtils.isNotBlank(receivingCode)) queryCondition.setReceivingCode(receivingCode);
            for(AsnWarehouseStatus asnWarehouseStatus: AsnWarehouseStatus.values()){
                queryCondition.setWarehouseCode(asnWarehouseStatus.getCode());
                try {
                    whAsnCheckOutService.syncAsn(queryCondition);
                } catch (Exception e) {
                    failedList.add(asnWarehouseStatus.getName()+"失败"+e.getMessage());
                    log.error(e.getMessage(), e);
                }
            };
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            response.setMessage(e.getMessage());
            return response;
        }
        if (CollectionUtils.isNotEmpty(failedList)) {
            response.setMessage(StringUtils.join(failedList, ";"));
            return response;
        }
        response.setStatus(StatusCode.SUCCESS);
        return response;
    }

    // 分配
    @RequestMapping(value = "allot", method = {RequestMethod.GET})
    @ResponseBody
    public ResponseJson allot(@RequestParam("ids") List<Integer> ids) {
        ResponseJson response = new ResponseJson();
        response.setStatus(StatusCode.FAIL);

        // 获取仓库属性
        Integer warehouseId = WarehouseProperties.getWarehouseProperties().getLocalWarehouseId();

        List<String> failedList = new ArrayList<>();
        for (Integer id : ids) {
            WhAsnQueryCondition query = new WhAsnQueryCondition();
            query.setId(id);
            List<WhAsn> whAsnList = whAsnService.queryWhAsnsAndItems(query, null);
            if (CollectionUtils.isEmpty(whAsnList)) {
                failedList.add(String.format("[%s]不存在", id));
                continue;
            }
            try {
                whAsnCheckOutService.exeAllot(whAsnList.get(0), warehouseId);
            } catch (Exception e) {
                failedList.add(String.format("%s%s", id, e.getMessage()));
                log.error(e.getMessage(), e);
            }
        }
        // 存在失败的项
        response.setMessage(String.format("成功[%s]条，失败[%s]条，失败详情【%s】", ids.size() - failedList.size(), failedList.size(), StringUtils.join(failedList, ";")));
        response.setStatus(StatusCode.SUCCESS);
        return response;
    }

    // 生成
    @RequestMapping(value = "genPackingTask", method = {RequestMethod.GET})
    @ResponseBody
    public ResponseJson genPackingTask(@RequestParam("ids") List<Integer> ids) {
        ResponseJson response = new ResponseJson();
        response.setStatus(StatusCode.FAIL);

        // 获取仓库属性
        Integer warehouseId = WarehouseProperties.getWarehouseProperties().getLocalWarehouseId();

        List<String> failedList = new ArrayList<>();
        for (Integer id : ids) {
            WhAsnQueryCondition query = new WhAsnQueryCondition();
            query.setId(id);
            List<WhAsn> whAsnList = whAsnService.queryWhAsnsAndItems(query, null);
            if (CollectionUtils.isEmpty(whAsnList)) {
                failedList.add(String.format("[%s]不存在", id));
                continue;
            }
            try {
                whAsnCheckOutService.doGenPackingTask(whAsnList.get(0), warehouseId);
            } catch (Exception e) {
                failedList.add(String.format("%s%s", id, e.getMessage()));
                log.error(e.getMessage(), e);
            }
        }
        // 存在失败的项
        response.setMessage(String.format("成功[%s]条，失败[%s]条，失败详情【%s】", ids.size() - failedList.size(), failedList.size(), StringUtils.join(failedList, ";")));
        response.setStatus(StatusCode.SUCCESS);
        return response;
    }

    // 取消
    @RequestMapping(value = "cancelAsn", method = {RequestMethod.GET})
    @ResponseBody
    public ResponseJson cancel(@RequestParam("ids") List<Integer> ids) {
        ResponseJson response = new ResponseJson();
        response.setStatus(StatusCode.FAIL);

        // 获取仓库属性
        Integer warehouseId = WarehouseProperties.getWarehouseProperties().getLocalWarehouseId();

        List<String> failedList = new ArrayList<>();
        for (Integer id : ids) {
            WhAsnQueryCondition query = new WhAsnQueryCondition();
            query.setId(id);
            List<WhAsn> whAsnList = whAsnService.queryWhAsnsAndItems(query, null);
            if (CollectionUtils.isEmpty(whAsnList)) {
                failedList.add(String.format("[%s]不存在", id));
                continue;
            }
            try {
                whAsnCheckOutService.doCancelAsn(whAsnList.get(0), warehouseId);
            } catch (Exception e) {
                failedList.add(String.format("%s%s", id, e.getMessage()));
                log.error(e.getMessage(), e);
            }
        }
        // 存在失败的项
        response.setMessage(String.format("成功[%s]条，失败[%s]条，失败详情【%s】", ids.size() - failedList.size(), failedList.size(), StringUtils.join(failedList, ";")));
        response.setStatus(StatusCode.SUCCESS);
        return response;
    }

    @RequestMapping(value = "editAndDeliver", method = {RequestMethod.POST})
    @ResponseBody
    public ResponseJson editAndDeliver(@RequestParam("receivingCode") String receivingCode, @RequestParam("type") Integer type
            , @RequestParam("trackingNumber") String trackingNumber, @RequestParam("shippingMethod") String shippingMethod) {
        ResponseJson response = new ResponseJson();
        response.setStatus(StatusCode.FAIL);
        if (StringUtils.isBlank(receivingCode)) {
            response.setMessage("出库单号不能为空！");
            return response;
        }
        if (StringUtils.isBlank(trackingNumber)) {
            response.setMessage("追踪号不能为空！");
            return response;
        }
        if (StringUtils.isBlank(shippingMethod)) {
            response.setMessage("物流公司不能为空！");
            return response;
        }
        WhAsnQueryCondition query = new WhAsnQueryCondition();
        query.setReceivingCode(receivingCode);
        List<WhAsn> whAsns = whAsnService.queryWhAsnsAndItems(query, null);
        if (CollectionUtils.isEmpty(whAsns)) {
            response.setMessage("单号有误，查无此出库单！");
            return response;
        }
        WhAsn whAsn = whAsns.get(0);
        if (type == 1) {
            if (!AsnStatus.WAITING_DELIVER.intCode().equals(whAsn.getStatus())) {
                response.setMessage("不是待发货，不允许交运！");
                return response;
            }
            query.setTrackingNumber(trackingNumber);
            query.setReceivingCode(null);
            List<WhAsn> exist = whAsnService.queryWhAsnsAndItems(query, null);
            if (CollectionUtils.isNotEmpty(exist)) {
                response.setMessage("追踪号已使用！");
                return response;
            }
            try {
                whAsn.setTrackingNumber(trackingNumber);
                whAsn.setShippingMethod(shippingMethod);
                return whAsnService.doDeliver(whAsn);
            } catch (Exception e) {
                logger.warn(e.getMessage(), e);
                response.setMessage("交运失败: " + e.getMessage());
            }
        } else {
            // 编辑
            if (!AsnStatus.DELIVER.intCode().equals(whAsn.getStatus()) && !AsnStatus.LOADED.intCode().equals(whAsn.getStatus())) {
                response.setMessage("不是已交运或已装车，不允许编辑！");
                return response;
            }
            query.setTrackingNumber(trackingNumber);
            query.setReceivingCode(null);
            List<WhAsn> exist = whAsnService.queryWhAsnsAndItems(query, null);
            if (CollectionUtils.isNotEmpty(exist) && !exist.get(0).getId().equals(whAsn.getId())) {
                response.setMessage("追踪号已使用！");
                return response;
            }
            try {
                whAsn.setTrackingNumber(trackingNumber);
                whAsn.setShippingMethod(shippingMethod);
                whAsnService.updateWhAsn(whAsn);
                SystemLogUtils.WHASNLOG.log(whAsn.getId(), "编辑海外仓出库单",
                        new String[][]{{"追踪号", whAsn.getTrackingNumber()}, {"物流公司", whAsn.getShippingMethod()}});
                response.setStatus(StatusCode.SUCCESS);
                whAsnService.sendMsg(whAsn);
            } catch (Exception e) {
                logger.warn(e.getMessage(), e);
                response.setMessage("编辑失败: " + e.getMessage());
            }
        }
        return response;
    }

    /**
     * 打印SKU标签
     *
     * @param domain
     * @param id
     * @return
     */
    @RequestMapping(value = "toPrintSKU")
    public String toPrintSKU(@ModelAttribute("domain") WhAsnDo domain, @RequestParam("id") Integer id) {
        if (id == null) {
            return "asn/printSku";
        }
        WhAsnQueryCondition query = new WhAsnQueryCondition();
        query.setId(id);
        List<WhAsn> whAsns = whAsnService.queryWhAsnsAndItems(query, null);
        if (CollectionUtils.isNotEmpty(whAsns)) {
            WhAsn whAsn = whAsns.get(0);
            domain.setWhAsn(whAsn);
            domain.getWhAsn().buildGroupItems();
        }
        return "asn/toPrintSku";
    }

    /**
     * 打印SKU标签
     *
     * @param domain
     * @param id
     * @return
     */
    @RequestMapping(value = "printSKU")
    public String printSKU(@ModelAttribute("domain") WhAsnDo domain, @RequestParam("id") Integer id) {
        if (id == null) {
            return "asn/printSku";
        }
        WhAsnQueryCondition query = new WhAsnQueryCondition();
        query.setId(id);
         List<WhAsn> whAsns = whAsnService.queryWhAsnsAndItems(query, null);
        if (CollectionUtils.isNotEmpty(whAsns)) {
            List<WhAsnItem> items = domain.getWhAsn().getItems();
            List<String> skus = items.stream().map(item -> item.getProductSku()).collect(Collectors.toList());
            WhSkuQueryCondition skuQuery = new WhSkuQueryCondition();
            skuQuery.setSkus(skus);
            List<WhSku> whSkus = whSkuService.queryWhSkus(skuQuery, null);
            Map<String, String> map = whSkus.stream().collect(Collectors.toMap(WhSku::getSku, item -> item.getName()));
            List<WhAsnItem> printList = new ArrayList<>();
            for (WhAsnItem item : items) {
                for (int i = 0; i < item.getQuantity(); i++) {
                    WhAsnItem print = new WhAsnItem();
                    print.setProductSku(item.getProductSku());
                    print.setProductBarcode(item.getProductBarcode());
                    print.setSellerSku(item.getSellerSku());
                    print.setSellerSkuName(item.getSellerSkuName());
                    print.setSkuName(map.get(item.getProductSku()));
                    printList.add(print);
                }
            }
            WhAsn exist = whAsns.get(0);
            exist.setItems(printList);
            domain.setWhAsn(exist);
            SystemLogUtils.WHASNLOG.log(exist.getId(), "海外仓出库单打印SKU");
        }
        return "asn/printSku";
    }
    private static String[] HEADERS_IMPORT = { "SKU", "备货数量" };
    @RequestMapping(value = "upload", method = { RequestMethod.POST })
    @ResponseBody
    public ModelAndView upload(@ModelAttribute("domain") WhAsnDo domain,HttpServletRequest request) {
        ModelAndView modelAndView = new ModelAndView("asn/printSku");
        ResultModel<WhAsnItem> whAbroadReturnItems = null;
        MultipartHttpServletRequest multiRequest = (MultipartHttpServletRequest) request;
        Map<String, MultipartFile> fileMap = multiRequest.getFileMap();
        for (MultipartFile multiPartFile : fileMap.values()) {
            try {
                whAbroadReturnItems = POIUtils.readExcel(HEADERS_IMPORT, multiPartFile, row -> {
                    int cellnum = 0;
                    WhAsnItem whAsnItem = new WhAsnItem();
                    whAsnItem.setProductSku(POIUtils.cellValue2Str(row.getCell(cellnum++)));
                    whAsnItem.setQuantity(POIUtils.cellValue2Int(row.getCell(cellnum++)));
                    return whAsnItem;
                }, true);

                if (whAbroadReturnItems.isSuccess()) {
                    List<WhAsnItem> items = whAbroadReturnItems.getList();
                    List<String> skus = items.stream().map(item -> item.getProductSku()).collect(Collectors.toList());
                    WhSkuQueryCondition skuQuery = new WhSkuQueryCondition();
                    skuQuery.setSkus(skus);
                    List<WhSku> whSkus = whSkuService.queryWhSkus(skuQuery, null);
                    Map<String, String> map = whSkus.stream().collect(Collectors.toMap(WhSku::getSku, item -> item.getName()));
                    List<WhAsnItem> printList = new ArrayList<>();
                    for (WhAsnItem item : items) {
                        for (int i = 0; i < item.getQuantity(); i++) {
                            WhAsnItem print = new WhAsnItem();
                            print.setProductSku(item.getProductSku());
                            print.setProductBarcode(item.getProductBarcode());
                            print.setSellerSku(item.getSellerSku());
                            print.setSellerSkuName(item.getSellerSkuName());
                            print.setSkuName(map.get(item.getProductSku()));
                            printList.add(print);
                        }
                    }
                    WhAsn exist = new WhAsn();
                    exist.setItems(printList);
                    exist.setType("TEST");
                    domain.setWhAsn(exist);
                    modelAndView.addObject(domain);
                    return modelAndView;
                }
                else {
                    domain.setErrorMsg(whAbroadReturnItems.getMsg());
                }

            }
            catch (IOException e) {
                domain.setErrorMsg(e.getMessage());
                modelAndView.addObject(domain);
                return modelAndView;
            }
        }
        modelAndView.addObject(domain);
        return modelAndView;
    }

    /**
     * 打印唛头
     *
     * @param domain
     * @param id
     * @return
     */
    @RequestMapping(value = "printMaitou")
    public String printMaitou(@ModelAttribute("domain") WhAsnDo domain, @RequestParam("id") Integer id) {
        if (id == null) {
            return "asn/printMaitou";
        }
        WhAsnQueryCondition query = new WhAsnQueryCondition();
        query.setId(id);
        List<WhAsn> whAsns = whAsnService.queryWhAsnsAndItems(query, null);
        if (CollectionUtils.isNotEmpty(whAsns)) {
            WhAsn exist = whAsns.get(0);
            domain.setWhAsn(exist);
            SystemLogUtils.WHASNLOG.log(exist.getId(), "海外仓出库单打印唛头");
        }
        return "asn/printMaitou";
    }

    /**
     * 装箱
     *
     * @param domain
     * @param id
     * @return
     */
    @RequestMapping(value = "toBox")
    public String toBox(@ModelAttribute("domain") WhAsnDo domain, @RequestParam("id") Integer id) {
        if (id == null) {
            return "asn/toBox";
        }
        WhAsnQueryCondition query = new WhAsnQueryCondition();
        query.setId(id);
        List<WhAsn> whAsns = whAsnService.queryWhAsnsAndItems(query, null);
        if (CollectionUtils.isNotEmpty(whAsns)) {
            domain.setWhAsn(whAsns.get(0));
            domain.getWhAsn().buildGroupItems();
            if (AsnType.FBA.getName().equalsIgnoreCase(whAsns.get(0).getType())){
                return "asn/toBoxFBA";
            }
        }
        return "asn/toBox";
    }

    /**
     * 装箱
     *
     * @param domain
     * @param id
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "box")
    public ResponseJson box(@ModelAttribute("domain") WhAsnDo domain, @RequestParam("id") Integer id, @RequestParam("type") Integer type) {
        ResponseJson response = new ResponseJson();
        response.setStatus(StatusCode.FAIL);
        if (id == null) {
            response.setMessage("出库单ID不能为空！");
            return response;
        }
        if (type == null) {
            response.setMessage("装箱参数不能为空！");
            return response;
        }
        WhAsnQueryCondition query = new WhAsnQueryCondition();
        query.setId(id);
        List<WhAsn> whAsns = whAsnService.queryWhAsnsAndItems(query, null);
        if (CollectionUtils.isEmpty(whAsns)) {
            response.setMessage("单号有误，查无此出库单！");
            return response;
        }
        WhAsn whAsn = whAsns.get(0);
        if (!AsnStatus.WAITING_BOX.intCode().equals(whAsn.getStatus()) && !AsnStatus.WAITING_CHECK.intCode().equals(whAsn.getStatus())) {
            response.setMessage("状态不是待装箱或待审核！");
            return response;
        }
        if (domain.getWhAsn() == null || CollectionUtils.isEmpty(domain.getWhAsn().getItems())) {
            response.setMessage("装箱SKU数量不能为空！");
            return response;
        }
        try {
            domain.setEditBoxType(type);
            Integer warehouseId = CacheUtils.getLocalWarehouseId();
            List<String> skus = whAsn.getItems().stream().filter(item -> warehouseId.equals(item.getWarehouseId())).map(item -> item.getProductSku()).collect(Collectors.toList());
            if (AsnType.FBA.getName().equalsIgnoreCase(whAsn.getType())) {
                return whAsnService.doBoxFBA(skus, domain, whAsn);
            }else{
                return whAsnService.doBox(skus, domain, whAsn);
            }
        } catch (Exception e) {
            logger.warn(e.getMessage(), e);
            response.setMessage("装箱失败: " + e.getMessage());
        }
        return response;
    }

    /**
     * 装箱审核
     *
     * @param domain
     * @param id
     * @return
     */
    @RequestMapping(value = "toBoxCheck")
    public String toBoxCheck(@ModelAttribute("domain") WhAsnDo domain, @RequestParam("id") Integer id) {
        if (id == null) {
            return "asn/toBoxCheck";
        }
        WhAsnQueryCondition query = new WhAsnQueryCondition();
        query.setId(id);
        List<WhAsn> whAsns = whAsnService.queryWhAsnsAndItems(query, null);
        if (CollectionUtils.isNotEmpty(whAsns)) {
            domain.setWhAsn(whAsns.get(0));
        }
        return "asn/toBoxCheck";
    }

    /**
     * 打印箱号
     *
     * @param domain
     * @param boxNo
     * @return
     */
    @RequestMapping(value = "toPrintBox")
    public String toPrintBox(@ModelAttribute("domain") WhAsnDo domain,
        @RequestParam("receivingCode") String receivingCode, @RequestParam("boxNo") Integer boxNo) {
        domain.setReceivingCode(receivingCode);
        domain.setBoxNo(boxNo);
        return "asn/printBox";
    }

    /**
     * 装箱审核
     *
     * @param domain
     * @param id
     * @return
     */
    @RequestMapping(value = "boxCheck", method = {RequestMethod.GET})
    @ResponseBody
    public ResponseJson boxCheck(@ModelAttribute("domain") WhAsnDo domain, @RequestParam("id") Integer id) {
        ResponseJson response = new ResponseJson();
        response.setStatus(StatusCode.FAIL);
        if (id == null) {
            response.setMessage("参数错误!");
            return response;
        }
        WhAsnQueryCondition query = new WhAsnQueryCondition();
        query.setId(id);
        List<WhAsn> whAsns = whAsnService.queryWhAsnsAndItems(query, null);
        try {
            if (CollectionUtils.isNotEmpty(whAsns)) {
                WhAsn exist = whAsns.get(0);
                whAsnCheckOutService.doBoxCheck(exist);
            }
        } catch (Exception e) {
            response.setStatus(e.getMessage());
            return response;
        }
        response.setStatus(StatusCode.SUCCESS);
        return response;
    }

    /**
     * 打印箱唛
     *
     * @param domain
     * @param id
     * @return
     */
    @RequestMapping(value = "printXiangmai", method = {RequestMethod.GET})
    public String printXiangmai(@ModelAttribute("domain") WhAsnDo domain, @RequestParam("id") Integer id) {
        if (id == null) {
            return "asn/printXiangmai";
        }
        WhAsnQueryCondition query = new WhAsnQueryCondition();
        query.setId(id);
        List<WhAsn> whAsns = whAsnService.queryWhAsnsAndItems(query, null);
        if (CollectionUtils.isNotEmpty(whAsns)) {
            domain.setWhAsn(whAsns.get(0));
            domain.getWhAsn().splitReceivingCode();
            SystemLogUtils.WHASNLOG.log(whAsns.get(0).getId(), "海外仓出库单打印箱唛");
            try {
                if (AsnType.FBA.getName().equalsIgnoreCase(whAsns.get(0).getType())){
                    String url = whAsnCheckOutService.printXiangmaiFBA(whAsns.get(0));
                    domain.setPrintUrl(url);
                    return "asn/printCheckOut";
                }
            } catch (Exception e) {
                domain.setErrorMsg(e.getMessage());
                return "asn/printCheckOut";
            }
        }
        return "asn/printXiangmai";
    }

    /**
     * 打印交运条码
     *
     * @param domain
     * @param id
     * @return
     */
    @RequestMapping(value = "printDeliverNo")
    public String printDeliverNo(@ModelAttribute("domain") WhAsnDo domain, @RequestParam("id") Integer id) {
        if (id == null) {
            return "asn/printDeliverNo";
        }
        WhAsnQueryCondition query = new WhAsnQueryCondition();
        query.setId(id);
        List<WhAsn> whAsns = whAsnService.queryWhAsnsAndItems(query, null);
        if (CollectionUtils.isNotEmpty(whAsns)) {
            WhAsn exist = whAsns.get(0);
            domain.setWhAsn(exist);
            SystemLogUtils.WHASNLOG.log(exist.getId(), "海外仓出库单打印交运条码");
        }
        return "asn/printDeliverNo";
    }


    /**
     * 打印出库单
     *
     * @param domain
     * @param id
     * @return
     */
    @RequestMapping(value = "printCheckOut", method = {RequestMethod.GET})
    public String printCheckOut(@ModelAttribute("domain") WhAsnDo domain, @RequestParam("id") Integer id,
                                @RequestParam("type") String type) {

        if (id == null) {
            domain.setErrorMsg("参数错误!");
            return "asn/printCheckOut";
        }
        WhAsnQueryCondition query = new WhAsnQueryCondition();
        query.setId(id);
        List<WhAsn> whAsns = whAsnService.queryWhAsnsAndItems(query, null);
        try {
            if (CollectionUtils.isNotEmpty(whAsns)) {
                WhAsn exist = whAsns.get(0);
                String url = whAsnCheckOutService.printCheckOut(exist, type);
                domain.setPrintUrl(url);
            }
        } catch (Exception e) {
            domain.setErrorMsg(e.getMessage());
        }
        return "asn/printCheckOut";
    }

    /**
     * 导出
     *
     * @param domain
     * @param ids
     * @param response
     */
    @RequestMapping(value = "download", method = {RequestMethod.GET})
    @ResponseBody
    public void download(@ModelAttribute("domain") WhAsnDo domain, @RequestParam(value = "ids", required = false) List<Integer> ids,
                         HttpServletResponse response) {

        String[] HEADERS = {"编号", "出库单号", "目的仓", "箱号", "sku", "件数", "已分配", "已拣", "装箱数量", "海外仓上架数量", "尺寸(CM)", "重量(G)", "状态", "追踪号", "第三方状态", "创建/更新时间", "交运时间"};

        WhAsnQueryCondition query = domain.getQuery();
        if (query == null || CollectionUtils.isNotEmpty(ids)) {
            query = new WhAsnQueryCondition();
            query.setIds(ids);
        }
        List<WhAsn> whAsns = whAsnService.queryWhAsnsAndItems(query, null);

        // 大于100W不能导出
        if (CollectionUtils.isEmpty(whAsns) || whAsns.size() > 1000000) {
            return;
        }
        OutputStream os = null;
        try {
            String fileName = "海外仓出库单" + System.currentTimeMillis() + ".xls";
            response.setContentType("application/ms-excel");
            response.setHeader("Content-Disposition",
                    "attachment; filename=" + java.net.URLEncoder.encode(fileName, "UTF-8"));
            os = response.getOutputStream();
            log.warn("download file name: " + fileName);
            final List<List<String>> batchReturnData = new ArrayList<>();
            POIUtils.createExcel(HEADERS, whAsns, whAsn -> {

                batchReturnData.clear();

                /**
                 * "编号", "出库单号", "目的仓", "箱号", "sku", "件数", "已分配",
                 * "已拣", "装箱数量", "海外仓上架数量", "尺寸(CM)", "重量(G)",
                 * "状态", "追踪号","第三方状态","创建/更新时间/交运时间"
                 */
                for (WhAsnItem item : whAsn.getItems()) {
                    List<String> batchReturnList = new ArrayList<String>(HEADERS.length);
                    batchReturnList.add(POIUtils.transferObj2Str(whAsn.getId()));
                    batchReturnList.add(POIUtils.transferObj2Str(whAsn.getReceivingCode()));
                    batchReturnList.add(POIUtils.transferObj2Str(AsnWarehouseStatus.getNameByCode(whAsn.getWarehouseCode())));
                    batchReturnList.add(POIUtils.transferObj2Str(item.getBoxNo()));
                    batchReturnList.add(POIUtils.transferObj2Str(item.getProductSku()));
                    batchReturnList.add(POIUtils.transferObj2Str(item.getQuantity()));
                    batchReturnList.add(POIUtils.transferObj2Str(item.getAllotQuantity()));
                    batchReturnList.add(POIUtils.transferObj2Str(item.getPickQuantity()));
                    batchReturnList.add(POIUtils.transferObj2Str(item.getLoadingQuantity()));
                    batchReturnList.add(POIUtils.transferObj2Str(item.getPutawayQuantity()));
                    String size = null;
                    if (item.getProductLength() != null || item.getProductWidth() != null || item.getProductHeight() != null) {
                        size = item.getProductLength() + "*" + item.getProductWidth() + "*" + item.getProductHeight();
                    }
                    batchReturnList.add(POIUtils.transferObj2Str(size));
                    batchReturnList.add(POIUtils.transferObj2Str(item.getProductWeight()));
                    batchReturnList.add(POIUtils.transferObj2Str(AsnStatus.getNameByCode(whAsn.getStatus() + "")));
                    String shippingMethod = null;
                    if (StringUtils.isNotBlank(whAsn.getShippingMethod()) || StringUtils.isNotBlank(whAsn.getTrackingNumber())) {
                        shippingMethod = whAsn.getShippingMethod() + "*" + whAsn.getTrackingNumber();
                    }
                    batchReturnList.add(POIUtils.transferObj2Str(shippingMethod));
                    batchReturnList.add(POIUtils.transferObj2Str(AsnPlatformStatus.getNameByCode(whAsn.getReceivingStatus())));
                    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                    batchReturnList.add(POIUtils.transferObj2Str(sdf.format(whAsn.getReceivingAddTime()) + "/" + sdf.format(whAsn.getReceivingModifyTime())));
                    batchReturnList.add(POIUtils.transferObj2Str(whAsn.getDeliverTime()));
                    batchReturnData.add(batchReturnList);
                }
                return batchReturnData;

            }, true, os);
            log.warn("---task execute end---");
        } catch (Exception e) {
            log.warn(e.getMessage(), e);
        } finally {
            IOUtils.closeQuietly(os);
        }
    }

    /**
     *
     * @Description:打开合成拣货任务页面
     *
     * @param whApvDo
     * @return
     * @return: String
     * @Author: qinyangkai
     * @Date: 2018/12/27
     * @Version: 0.0.1
     */
    @RequestMapping(value = "pickingTask", method = { RequestMethod.GET })
    public String pickingTask(@ModelAttribute("domain") WhMergeApvDo whApvDo,
                              @RequestParam("mergeApvType") Integer mergeApvType) {
        if (MergeApvType.MULTIPLEMULTIPLE.intCode().equals(mergeApvType)) {
//            whApvDo.setLocationCount(20); // 多品默认20
        } else {
            whApvDo.setLocationCount(50); // 单品默认50
        }
        return "transfer/create_transfer_picking_task";
    }

    /**
     * 见单出单(唯一码)
     */
    @RequestMapping(value = "toPrintAsnUuid", method = {RequestMethod.GET})
    public String toPrintAsnUuid(@ModelAttribute("domain") WhAsnDo domain) {
        return "asn/asn_print_uuid";
    }

    @ResponseBody
    @RequestMapping(value = "printAsnUuid", method = {RequestMethod.GET})
    public ResponseJson printAsnUuid(@RequestParam String input){
        ResponseJson rsp = new ResponseJson();
        rsp.setStatus(StatusCode.FAIL);

        if (StringUtils.isBlank(input) || input.indexOf(",") > -1) {
            rsp.setMessage("数据为空！");
            return rsp;
        }

        WhFbaAllocation whFba = null;
        WhFbaAllocationQueryCondition query = new WhFbaAllocationQueryCondition();
        query.setQueryWhAsnExtra(true);
        if(input.contains("=")) {
            // 唯一码匹配订单打印面单、需要校验订单状态为等待发货
            WhUniqueSku unique = whUniqueSkuService.getWhUniqueSkuByUuidAndSku(input);
            if (unique == null) {
                rsp.setMessage("唯一码不存在！");
                return rsp;
            }
            if (StringUtils.isBlank(unique.getApvNo())) {
                rsp.setMessage("唯一码未绑定中转仓发货单！");
                return rsp;
            }
            rsp.getBody().put("SSUniqueSkuId", unique.getId());

            query.setFbaNo(unique.getApvNo());
            List<WhFbaAllocation> fbaAllocationList = whFbaAllocationService.queryWhFbaAllocationAndItems(query, null);
            if (CollectionUtils.isEmpty(fbaAllocationList)) {
                whFba = getTemuPrintInfo(query);
                if (whFba == null) {
                    rsp.setMessage("唯一码绑定中转仓发货单不存在！");
                    return rsp;
                }
            } else {
                whFba = fbaAllocationList.get(0);
            }
        }else{
            query.setFbaNo(input);
            List<WhFbaAllocation> fbaAllocationList = whFbaAllocationService.queryWhFbaAllocationAndItems(query, null);
            if (CollectionUtils.isEmpty(fbaAllocationList)) {
                query.setFbaNo(null);
                query.setTrackingNumber(input);
                fbaAllocationList = whFbaAllocationService.queryWhFbaAllocationAndItems(query, null);
            }
            if (CollectionUtils.isEmpty(fbaAllocationList)) {
                whFba = getTemuPrintInfo(query);
                if (whFba == null) {
                    query.setTrackingNumber(null);
                    query.setFbaNo(input);
                    whFba = getTemuPrintInfo(query);
                    if (whFba == null) {
                        rsp.setMessage("未找到中转仓发货单信息：" + input);
                        return rsp;
                    }
                }
            } else {
                whFba = fbaAllocationList.get(0);
            }
        }

        if (!AsnPrepareStatus.WAITING_DELIVER.equals(whFba.getStatus())) {
            rsp.setMessage("唯一码绑定中转仓发货单不是等待发货状态！");
            return rsp;
        }

        // 如果是已经取消的则查询最新的订单
        if (AsnPrepareStatus.CANCEL.equals(whFba.getStatus())) {
            rsp.setMessage("该订单已取消发货！");
            return rsp;
        }

        /*if (ApvTypeEnum.MM.getCode().equals(whFba.getApvType())) {
            if (whFba.getTaskId() != null) {
                WhPickingTaskQueryCondition taskQuery = new WhPickingTaskQueryCondition();
                taskQuery.setId(whApvList.get(0).getTaskId());
                taskQuery.setGridStatus(PickTaskGridStatus.COMPLETED.intCode());
                List<WhPickingTask> whPickingTasks = pickingTaskService.queryWhPickingTasks(taskQuery, null);
                if (CollectionUtils.isEmpty(whPickingTasks)) {
                    rsp.setMessage("多品多件拣货任务ID：" + whApvList.get(0).getTaskId() + " 没有播种完成，不能见单出单！");
                    return rsp;
                }
            }
        }*/

        rsp.setLocation("OMS");
        rsp.setMessage(whFba.getFbaNo());

        WhAsnExtra whAsnExtra = whFba.getWhAsnExtra();
        if (whAsnExtra != null) {
            rsp.getBody().put("printSkuTagUrl", whAsnExtra.getPrintSkuTagUrl() == null?"":whAsnExtra.getPrintSkuTagUrl());
            rsp.getBody().put("printEnvProtectionTagUrl", whAsnExtra.getPrintEnvironmentalProtectionTagUrl() == null?"":whAsnExtra.getPrintEnvironmentalProtectionTagUrl());
            rsp.getBody().put("printTextileTagUrl", whAsnExtra.getPrintTextileTagUrl()==null?"":whAsnExtra.getPrintTextileTagUrl());
        }
        rsp.getBody().put("platform", whFba.getPurposeHouse());
        rsp.getBody().put("apvNo", whFba.getFbaNo());
        rsp.getBody().put("fbaId", whFba.getId());
        if (Objects.equals(SaleChannel.CHANNEL_SHEIN, whFba.getPurposeHouse())){
            rsp.getBody().put("gpsr", ApvTaxTypeEnum.GPSR.getName());
        } else if (Objects.equals(SaleChannel.CHANNEL_TEMU, whFba.getPurposeHouse())) {
            WhFbaAllocationItem item = whFba.getItems().get(0);
            rsp.getBody().put("sku", item.getProductSku());
            rsp.getBody().put("quantity", item.getPickQuantity());
        }

        String billPdfUrl = null;
        try {
            //JIT备货单打印箱唛
            Integer[] packageMethods = new Integer[]{AsnPackageMethodEnum.JIT.getCode(), AsnPackageMethodEnum.JIT_HALF.getCode(), AsnPackageMethodEnum.SMT_PRIORITY_WAREHOUSE.getCode()};
            if (!Objects.equals(SaleChannel.CHANNEL_SHEIN, whFba.getPurposeHouse()) && whAsnExtra != null
                    && ArraysUtil.contains(packageMethods, whAsnExtra.getPackageMethod())) {
                Integer orderType = null;
                Integer boxQuantity = null;
                if (AsnPackageMethodEnum.SMT_PRIORITY_WAREHOUSE.getCode().equals(whAsnExtra.getPackageMethod())) {
                    orderType = 2;
                    boxQuantity = 1;
                }
//                else {
//                    try {
//                        Integer boxNumber = jitPickBoxScanService.packGetBoxNumber(whFba);
//                        if (boxNumber != null){
//                            String boxStr = String.valueOf(boxNumber);
//                            if(Objects.equals(AsnPackageMethodEnum.JIT_HALF.getCode(),whAsnExtra.getPackageMethod())){
//                                boxStr = JITBoxOrderType.HALF_JIT_BOX_ORDER_TYPE.getCode() + boxStr;
//                            }
//                            rsp.getBody().put("boxNumber", boxStr);
//                        }
//                    }
//                    catch (Exception e) {
//                        log.error("获取分拣框失败：" + e.getMessage(), e);
//                    }
//                }
                billPdfUrl = whFbaAllocationHandleService.getJitPdfUrl(whFba.getFbaNo(), 2, orderType, boxQuantity);
            }else if (!SaleChannel.CHANNEL_TEMU.equals(whFba.getPurposeHouse())) {
                billPdfUrl = whFbaAllocationHandleService.printXiangmaiFBA(whFba);
            }
            rsp.getBody().put("billPdfUrl", billPdfUrl);
        } catch (Exception e) {
            log.error("获取中转仓面单异常：" + e.getMessage(), e);
            rsp.setMessage("获取中转仓面单异常");
            return rsp;
        }
        if (StringUtils.isNotBlank(billPdfUrl)) {
            String localPdfUrl = this.downloadPdf(whFba.getFbaNo(), billPdfUrl);
            if (StringUtils.isNotBlank(localPdfUrl)) {
                rsp.getBody().put("localPdfUrl", localPdfUrl);
            }
        }

        if (input.contains("=")) {
            List<String> skus = new ArrayList<>();
            if (ApvTypeEnum.MM.getCode().equals(whFba.getApvType()) || ApvTypeEnum.SM.getCode().equals(whFba.getApvType())) {
                WhUniqueSkuQueryCondition uniqueCondition = new WhUniqueSkuQueryCondition();
                uniqueCondition.setCurrentApvNo(whFba.getFbaNo());
                List<WhUniqueSku> uniqueSkus = whUniqueSkuService.queryWhUniqueSkus(uniqueCondition, null);
                if (CollectionUtils.isNotEmpty(uniqueSkus)) {
                    Map<String, List<WhUniqueSku>> uniqueSkuMap = uniqueSkus.stream().collect(Collectors.groupingBy(WhUniqueSku::getSku));
                    rsp.getBody().put("uniqueSkuMap", uniqueSkuMap);
                    // 查询sku信息
                    WhSkuQueryCondition skuCondition = new WhSkuQueryCondition();
                    skuCondition.setSkus(new ArrayList<>(uniqueSkuMap.keySet()));
                    skus.addAll(skuCondition.getSkus());
                    List<WhSku> whSkus = whSkuService.queryWhSkus(skuCondition, null);
                    Map<String, WhSku> skuMap = new HashMap<>();
                    if (CollectionUtils.isNotEmpty(whSkus)) {
                        skuMap = whSkus.stream().collect(Collectors.toMap(WhSku::getSku, c -> c));
                    }
                    for (String sku : skus) {
                        if (!skuMap.containsKey(sku)) {
                            // 若未查到sku信息则
                            skuMap.put(sku, new WhSku());
                        }
                    }
                    rsp.getBody().put("skuMap", skuMap);
                }
            } else if (ApvTypeEnum.SS.getCode().equals(whFba.getApvType())) {
                WhSkuQueryCondition skuCondition = new WhSkuQueryCondition();
                skuCondition.setSku(input.split("=")[0]);
                skus.add(skuCondition.getSku());
                rsp.getBody().put("skuInfo", Optional.ofNullable(whSkuService.queryWhSku(skuCondition)).orElse(new WhSku()));
            }
            if (CollectionUtils.isNotEmpty(skus)) {
                Map<String, WhSkuWithPmsInfo> packMap = new HashMap<>();
                List<WhSkuWithPmsInfo> pmsSkuInfoList = ApvPackUtils.getPmsSkuInfoList(skus);
                if (CollectionUtils.isNotEmpty(pmsSkuInfoList)) {
                    packMap = pmsSkuInfoList.stream().collect(Collectors.toMap(WhSkuWithPmsInfo::getSku, c -> c));
                }
                for (String sku : skus) {
                    if (!packMap.containsKey(sku)) {
                        packMap.put(sku, new WhSkuWithPmsInfo());
                    }
                }
                rsp.getBody().put("packMap", packMap);
            }
        }
        SystemLogUtils.WHASNLOG.log(whFba.getId(), "中转仓见单出单(唯一码)打印面单!");
        rsp.setStatus(StatusCode.SUCCESS);
        return rsp;
    }

    public WhFbaAllocation getTemuPrintInfo(WhFbaAllocationQueryCondition fbaQuery){
        TemuPrepareOrderQueryCondition temuQuery = new TemuPrepareOrderQueryCondition();
        temuQuery.setPackageSn(fbaQuery.getFbaNo());
        temuQuery.setDeliverOrderNo(fbaQuery.getTrackingNumber());
        List<TemuPackageInfo> packageInfoList = temuPrepareOrderService.queryTemuPackageAndItems(temuQuery, null);
        if (CollectionUtils.isEmpty(packageInfoList)) {
            return null;
        }
        TemuPackageInfo temuPackage = packageInfoList.get(0);
        WhFbaAllocation whFbaAllocation = new WhFbaAllocation();
        whFbaAllocation.setId(temuPackage.getId());
        whFbaAllocation.setFbaNo(temuPackage.getPackageSn());
        whFbaAllocation.setPurposeHouse(SaleChannel.CHANNEL_TEMU);
        whFbaAllocation.setIsAsn(false);
        whFbaAllocation.setStatus(temuPackage.getPackageStatus());
        WhFbaAllocationItem item = new WhFbaAllocationItem();
        item.setProductSku(temuPackage.getSku());
        item.setQuantity(temuPackage.getRealQuantity());
        item.setPickQuantity(temuPackage.getPickQuantity());
        item.setGridQuantity(Optional.ofNullable(temuPackage.getGridQuantity()).orElse(0));
        whFbaAllocation.setApvType(item.getQuantity() > 1?"SM":"SS");
        whFbaAllocation.setItems(List.of(item));
        return whFbaAllocation;
    }

    private String downloadPdf(String fbaNo, String printUrl){
        if (StringUtils.isBlank(fbaNo)) {
            return null;
        }
        try {
            String path = "/usr/local/erp/static/file/pdf/transfer/" + fbaNo + ".pdf";
            File file = new File(path);
            String localip = "http://************";
            Environment env = SpringUtils.getBean(Environment.class);
            if(!StringUtils.contains(env.getProperty("spring.profiles.active"),"prod")){
                localip = "http://************";
//                    localip = "http://localhost:8181";
            }
            if(file.exists()){
                path = localip + "/wms/file/pdf/transfer/" + fbaNo + ".pdf";
                return path;
            }
            if (StringUtils.isBlank(printUrl)) {
                return null;
            }
            URL pdfUrl = new URL(printUrl);
            HttpURLConnection connection = (HttpURLConnection) pdfUrl.openConnection();
            DataInputStream in = new DataInputStream(connection.getInputStream());
            try {
                FileUtils.writeByteArrayToFile(file, in.readAllBytes());
            }
            catch (IOException e) {
                logger.warn(e.getMessage(), e);
            }
            in.close();
            path = localip + "/wms/file/pdf/transfer/" + fbaNo + ".pdf";
            return path;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        return null;
    }

}