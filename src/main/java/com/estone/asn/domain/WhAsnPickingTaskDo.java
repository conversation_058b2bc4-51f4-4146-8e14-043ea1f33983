package com.estone.asn.domain;

import com.estone.asn.bean.WhAsnPickingTask;
import com.estone.asn.bean.WhAsnPickingTaskQueryCondition;
import com.whq.tool.component.Pager;
import java.util.ArrayList;
import java.util.List;

public class WhAsnPickingTaskDo {
    private WhAsnPickingTask whAsnPickingTask;

    private WhAsnPickingTaskQueryCondition query;

    private String asnWhJson;

    private String asnPickTaskJson;

    private String whJson;

    private String isPrintings;

    private List<WhAsnPickingTask> whAsnPickingTasks = new ArrayList<WhAsnPickingTask>();

    private Pager page = new Pager();

    public WhAsnPickingTask getWhAsnPickingTask() {
        return whAsnPickingTask;
    }

    public void setWhAsnPickingTask(WhAsnPickingTask whAsnPickingTask) {
        this.whAsnPickingTask = whAsnPickingTask;
    }

    public WhAsnPickingTaskQueryCondition getQuery() {
        return query;
    }

    public void setQuery(WhAsnPickingTaskQueryCondition query) {
        this.query = query;
    }

    public List<WhAsnPickingTask> getWhAsnPickingTasks() {
        return whAsnPickingTasks;
    }

    public void setWhAsnPickingTasks(List<WhAsnPickingTask> whAsnPickingTasks) {
        this.whAsnPickingTasks = whAsnPickingTasks;
    }

    public Pager getPage() {
        return page;
    }

    public void setPage(Pager page) {
        this.page = page;
    }

    public String getAsnWhJson() {
        return asnWhJson;
    }

    public void setAsnWhJson(String asnWhJson) {
        this.asnWhJson = asnWhJson;
    }

    public String getAsnPickTaskJson() {
        return asnPickTaskJson;
    }

    public void setAsnPickTaskJson(String asnPickTaskJson) {
        this.asnPickTaskJson = asnPickTaskJson;
    }

    public String getIsPrintings() {
        return isPrintings;
    }

    public void setIsPrintings(String isPrintings) {
        this.isPrintings = isPrintings;
    }

    public String getWhJson() {
        return whJson;
    }

    public void setWhJson(String whJson) {
        this.whJson = whJson;
    }
}