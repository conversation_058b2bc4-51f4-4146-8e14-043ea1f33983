package com.estone.asn.dao;

import com.estone.asn.bean.WhAsnPickingTaskItem;
import com.estone.asn.bean.WhAsnPickingTaskItemQueryCondition;
import com.whq.tool.component.Pager;
import java.util.List;

public interface WhAsnPickingTaskItemDao {
    int queryWhAsnPickingTaskItemCount(WhAsnPickingTaskItemQueryCondition query);

    List<WhAsnPickingTaskItem> queryWhAsnPickingTaskItemList();

    List<WhAsnPickingTaskItem> queryWhAsnPickingTaskItemList(WhAsnPickingTaskItemQueryCondition query, Pager pager);

    WhAsnPickingTaskItem queryWhAsnPickingTaskItem(Integer primaryKey);

    WhAsnPickingTaskItem queryWhAsnPickingTaskItem(WhAsnPickingTaskItemQueryCondition query);

    void createWhAsnPickingTaskItem(WhAsnPickingTaskItem entity);

    void batchCreateWhAsnPickingTaskItem(List<WhAsnPickingTaskItem> entityList);

    void batchUpdateWhAsnPickingTaskItem(List<WhAsnPickingTaskItem> entityList);

    void deleteWhAsnPickingTaskItem(Integer primaryKey);

    void updateWhAsnPickingTaskItem(WhAsnPickingTaskItem entity);
}