package com.estone.asn.dao.impl;

import com.estone.asn.bean.WhAsnPickingTask;
import com.estone.asn.bean.WhAsnPickingTaskQueryCondition;
import com.estone.asn.dao.WhAsnPickingTaskDao;
import com.estone.asn.dao.mapper.WhAsnDBField;
import com.estone.asn.dao.mapper.WhAsnMapper;
import com.estone.asn.dao.mapper.WhAsnPickingTaskDBField;
import com.estone.asn.dao.mapper.WhAsnPickingTaskMapper;
import com.estone.common.util.CommonUtils;
import com.whq.tool.component.Pager;
import com.whq.tool.context.DataContextHolder;
import com.whq.tool.sqler.SqlerRequest;
import com.whq.tool.sqler.analyzer.DataType;
import com.whq.tool.sqler.dialect.DialectFactory;
import com.whq.tool.sqler.dialect.SQLDialect;
import com.estone.common.util.SqlerTemplate;
import java.sql.Timestamp;
import java.util.List;

import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Repository;
import org.springframework.util.Assert;

@Repository("whAsnPickingTaskDao")
public class WhAsnPickingTaskDaoImpl implements WhAsnPickingTaskDao {

    private void setQueryCondition(SqlerRequest request, WhAsnPickingTaskQueryCondition query) {
        if (query == null) {
            return;
        }
        // 添加查询条件
        // 搜索条件不允许出现空字符
        request.setAllowBlank(false);
        request.addDataParam(WhAsnPickingTaskDBField.ID, DataType.INT, query.getId());
        request.addDataParam("ids", DataType.STRING, query.getTaskIds());
        request.addDataParam("from_creation_date", DataType.STRING, query.getFromCreationDate());
        request.addDataParam("to_creation_date", DataType.STRING, query.getToCreationDate());
        if (StringUtils.isNotBlank(query.getReceivingCode())) {
            if (query.getReceivingCode().contains(",")) {
                request.addDataParam("receivingCodeList", DataType.STRING, CommonUtils.splitList(query.getReceivingCode().trim(), ","));
            } else {
                request.addDataParam(WhAsnDBField.RECEIVING_CODE, DataType.STRING, query.getReceivingCode().trim());
            }
        }
        if (StringUtils.isNotBlank(query.getTaskNo())) {
            if (query.getTaskNo().contains(",")) {
                request.addDataParam("taskNoList", DataType.STRING, CommonUtils.splitList(query.getTaskNo().trim(), ","));
            } else {
                request.addDataParam(WhAsnPickingTaskDBField.TASK_NO, DataType.STRING, query.getTaskNo().trim());
            }
        }
        request.addDataParam(WhAsnPickingTaskDBField.WAREHOUSE_CODE, DataType.STRING, query.getWarehouseCode());
        request.addDataParam(WhAsnPickingTaskDBField.TASK_STATUS, DataType.INT, query.getTaskStatus());
        request.addDataParam(WhAsnPickingTaskDBField.IS_PRINTING, DataType.INT, query.getIsPrinting());
        request.addDataParam(WhAsnPickingTaskDBField.RECEIVE_PERSON, DataType.INT, query.getReceivePerson());
        request.addDataParam("fromPickingEndDate", DataType.STRING, query.getFromPickingEndDate());
        request.addDataParam("toPickingEndDate", DataType.STRING, query.getToPickingEndDate());
        // SKU
        if (StringUtils.isNotBlank(query.getSku())) {
            if (query.getSku().contains(",")) {
                request.addDataParam("skuList", DataType.STRING, CommonUtils.splitList(query.getSku().trim(), ","));
            } else {
                request.addDataParam("sku", DataType.STRING, query.getSku().trim());
            }
        }
    }

    @Override
    public int queryWhAsnPickingTaskCount(WhAsnPickingTaskQueryCondition query) {
        SqlerRequest request = new SqlerRequest("queryWhAsnPickingTaskCount");
        setQueryCondition(request, query);
        return SqlerTemplate.queryForInt(request);
    }

    @Override
    public List<WhAsnPickingTask> queryWhAsnPickingTaskList() {
        SqlerRequest request = new SqlerRequest("queryWhAsnPickingTaskList");
        return SqlerTemplate.query(request, new WhAsnPickingTaskMapper());
    }

    @Override
    public List<WhAsnPickingTask> queryWhAsnPickingTaskList(WhAsnPickingTaskQueryCondition query, Pager pager) {
        SqlerRequest request = new SqlerRequest("queryWhAsnPickingTaskList");
        setQueryCondition(request, query);
        if(pager != null) {
            request.addFetch(pager.getPageNo(), pager.getPageSize());
        }
        return SqlerTemplate.query(request, new WhAsnPickingTaskMapper());
    }

    @Override
    public WhAsnPickingTask queryWhAsnPickingTask(Integer primaryKey) {
        Assert.notNull(primaryKey, "primaryKey is null!");
        SqlerRequest request = new SqlerRequest("queryWhAsnPickingTaskByPrimaryKey");
        request.addDataParam(WhAsnPickingTaskDBField.ID, DataType.INT, primaryKey);
        return SqlerTemplate.queryForObject(request, new WhAsnPickingTaskMapper());
    }

    @Override
    public WhAsnPickingTask queryWhAsnPickingTask(WhAsnPickingTaskQueryCondition query) {
        SqlerRequest request = new SqlerRequest("queryWhAsnPickingTask");
        setQueryCondition(request, query);
        return SqlerTemplate.queryForObject(request, new WhAsnPickingTaskMapper());
    }

    @Override
    public void createWhAsnPickingTask(WhAsnPickingTask entity) {
        SqlerRequest request = new SqlerRequest("createWhAsnPickingTask");
        request.addDataParam(WhAsnPickingTaskDBField.RECEIVING_CODE, DataType.STRING, entity.getReceivingCode());
        request.addDataParam(WhAsnPickingTaskDBField.TASK_NO, DataType.STRING, entity.getTaskNo());
        request.addDataParam(WhAsnPickingTaskDBField.WAREHOUSE_CODE_DELIVER, DataType.STRING, entity.getWarehouseCodeDeliver());
        request.addDataParam(WhAsnPickingTaskDBField.WAREHOUSE_CODE, DataType.STRING, entity.getWarehouseCode());
        request.addDataParam(WhAsnPickingTaskDBField.SKU_TOTAL, DataType.INT, entity.getSkuTotal());
        request.addDataParam(WhAsnPickingTaskDBField.SKU_SPECIES, DataType.INT, entity.getSkuSpecies());
        request.addDataParam(WhAsnPickingTaskDBField.TASK_STATUS, DataType.INT, entity.getTaskStatus());
        request.addDataParam(WhAsnPickingTaskDBField.RECEIVE_PERSON, DataType.INT, entity.getReceivePerson());
        request.addDataParam(WhAsnPickingTaskDBField.CREATED_BY, DataType.INT, entity.getCreatedBy() == null ? DataContextHolder.getUserId() : entity.getCreatedBy());
        request.addDataParam(WhAsnPickingTaskDBField.CREATED_DATE, DataType.TIMESTAMP,  new Timestamp(System.currentTimeMillis()));
        request.addDataParam(WhAsnPickingTaskDBField.RECEIVE_DATE, DataType.TIMESTAMP, entity.getReceiveDate());
        request.addDataParam(WhAsnPickingTaskDBField.LAST_UPDATE_DATE, DataType.TIMESTAMP, new Timestamp(System.currentTimeMillis()));
        request.addDataParam(WhAsnPickingTaskDBField.LAST_UPDATE_BY, DataType.INT, entity.getLastUpdateBy());
        request.addDataParam(WhAsnPickingTaskDBField.PICKING_END_DATE, DataType.TIMESTAMP, entity.getPickingEndDate());
        request.addDataParam(WhAsnPickingTaskDBField.IS_PRINTING, DataType.INT, entity.getIsPrinting());
        request.addDataParam(WhAsnPickingTaskDBField.PRINT_USER, DataType.INT, entity.getPrintUser());
        request.addDataParam(WhAsnPickingTaskDBField.PRINT_DATE, DataType.TIMESTAMP, entity.getPrintDate());
        request.addDataParam(WhAsnPickingTaskDBField.IS_SPAN, DataType.INT, entity.getIsSpan());
        Integer autoIncrementId = SqlerTemplate.executeAndReturn(request);
        entity.setId(autoIncrementId);
    }

    @Override
    public Integer updateWhAsnPickingTask(WhAsnPickingTask entity) {
        SqlerRequest request = new SqlerRequest("updateWhAsnPickingTaskByPrimaryKey");
        request.addDataParam(WhAsnPickingTaskDBField.ID, DataType.INT, entity.getId());
        
        request.addDataParam(WhAsnPickingTaskDBField.RECEIVING_CODE, DataType.STRING, entity.getReceivingCode());
        request.addDataParam(WhAsnPickingTaskDBField.TASK_NO, DataType.STRING, entity.getTaskNo());
        request.addDataParam(WhAsnPickingTaskDBField.WAREHOUSE_CODE_DELIVER, DataType.STRING, entity.getWarehouseCodeDeliver());
        request.addDataParam(WhAsnPickingTaskDBField.WAREHOUSE_CODE, DataType.STRING, entity.getWarehouseCode());
        request.addDataParam(WhAsnPickingTaskDBField.SKU_TOTAL, DataType.INT, entity.getSkuTotal());
        request.addDataParam(WhAsnPickingTaskDBField.SKU_SPECIES, DataType.INT, entity.getSkuSpecies());
        request.addDataParam(WhAsnPickingTaskDBField.TASK_STATUS, DataType.INT, entity.getTaskStatus());
        request.addDataParam(WhAsnPickingTaskDBField.RECEIVE_PERSON, DataType.INT, entity.getReceivePerson());
        
        request.addDataParam(WhAsnPickingTaskDBField.CREATED_DATE, DataType.TIMESTAMP, entity.getCreatedDate());
        request.addDataParam(WhAsnPickingTaskDBField.RECEIVE_DATE, DataType.TIMESTAMP, entity.getReceiveDate());
        request.addDataParam(WhAsnPickingTaskDBField.LAST_UPDATE_DATE, DataType.TIMESTAMP, new Timestamp(System.currentTimeMillis()));
        request.addDataParam(WhAsnPickingTaskDBField.LAST_UPDATE_BY, DataType.INT, entity.getLastUpdateBy());
        request.addDataParam(WhAsnPickingTaskDBField.PICKING_END_DATE, DataType.TIMESTAMP, entity.getPickingEndDate());
        request.addDataParam(WhAsnPickingTaskDBField.IS_PRINTING, DataType.INT, entity.getIsPrinting());
        request.addDataParam(WhAsnPickingTaskDBField.PRINT_USER, DataType.INT, entity.getPrintUser());
        request.addDataParam(WhAsnPickingTaskDBField.PRINT_DATE, DataType.TIMESTAMP, entity.getPrintDate());
        request.addDataParam(WhAsnPickingTaskDBField.IS_SPAN, DataType.INT, entity.getIsSpan());
        return SqlerTemplate.execute(request);
    }

    @Override
    public int queryWhAsnPickingTasksAndItemCount(WhAsnPickingTaskQueryCondition query) {
        SqlerRequest request = new SqlerRequest("queryWhAsnPickingTasksAndItemCount");
        setQueryCondition(request, query);
        return SqlerTemplate.queryForInt(request);
    }

    @Override
    public List<WhAsnPickingTask> queryWhAsnPickingTasksAndItem(WhAsnPickingTaskQueryCondition query, Pager pager) {
        SqlerRequest request = new SqlerRequest("queryWhAsnPickingTasksAndItem");
        setQueryCondition(request, query);
        if (pager != null) {
            SQLDialect dial = DialectFactory.createDialect(null);

            long start = (long) (pager.getPageNo() - 1) * pager.getPageSize();

            long end = start + pager.getPageSize() - 1L;

            request.addSqlDataParam("LIMIT", dial.queryFirst2Last("", (int) start, (int) end));
        }
        return SqlerTemplate.query(request, new WhAsnPickingTaskMapper(true));
    }

    @Override
    public void batchCreateWhAsnPickingTask(List<WhAsnPickingTask> entityList) {
        if (entityList != null && !entityList.isEmpty()) {
            SqlerRequest request = new SqlerRequest("createWhAsnPickingTask");
            for (WhAsnPickingTask entity : entityList) {
                request.addBatchDataParam(WhAsnPickingTaskDBField.RECEIVING_CODE, DataType.STRING, entity.getReceivingCode());
                request.addBatchDataParam(WhAsnPickingTaskDBField.TASK_NO, DataType.STRING, entity.getTaskNo());
                request.addBatchDataParam(WhAsnPickingTaskDBField.WAREHOUSE_CODE_DELIVER, DataType.STRING, entity.getWarehouseCodeDeliver());
                request.addBatchDataParam(WhAsnPickingTaskDBField.WAREHOUSE_CODE, DataType.STRING, entity.getWarehouseCode());
                request.addBatchDataParam(WhAsnPickingTaskDBField.SKU_TOTAL, DataType.INT, entity.getSkuTotal());
                request.addBatchDataParam(WhAsnPickingTaskDBField.SKU_SPECIES, DataType.INT, entity.getSkuSpecies());
                request.addBatchDataParam(WhAsnPickingTaskDBField.TASK_STATUS, DataType.INT, entity.getTaskStatus());
                request.addBatchDataParam(WhAsnPickingTaskDBField.RECEIVE_PERSON, DataType.INT, entity.getReceivePerson());
                request.addBatchDataParam(WhAsnPickingTaskDBField.CREATED_BY, DataType.INT, entity.getCreatedBy() == null ? DataContextHolder.getUserId() : entity.getCreatedBy());
                request.addBatchDataParam(WhAsnPickingTaskDBField.CREATED_DATE, DataType.TIMESTAMP, entity.getCreatedDate());
                request.addBatchDataParam(WhAsnPickingTaskDBField.RECEIVE_DATE, DataType.TIMESTAMP, entity.getReceiveDate());
                request.addBatchDataParam(WhAsnPickingTaskDBField.LAST_UPDATE_DATE, DataType.TIMESTAMP, new Timestamp(System.currentTimeMillis()));
                request.addBatchDataParam(WhAsnPickingTaskDBField.LAST_UPDATE_BY, DataType.INT, entity.getLastUpdateBy());
                request.addBatchDataParam(WhAsnPickingTaskDBField.PICKING_END_DATE, DataType.TIMESTAMP, entity.getPickingEndDate());
                request.addBatchDataParam(WhAsnPickingTaskDBField.IS_PRINTING, DataType.INT, entity.getIsPrinting());
                request.addBatchDataParam(WhAsnPickingTaskDBField.PRINT_USER, DataType.INT, entity.getPrintUser());
                request.addBatchDataParam(WhAsnPickingTaskDBField.PRINT_DATE, DataType.TIMESTAMP, entity.getPrintDate());
                request.addBatchDataParam(WhAsnPickingTaskDBField.IS_SPAN, DataType.INT, entity.getIsSpan());
                request.addBatch();
            }
            SqlerTemplate.batchUpdate(request);
        }
    }

    @Override
    public void batchUpdateWhAsnPickingTask(List<WhAsnPickingTask> entityList) {
        if (entityList != null && !entityList.isEmpty()) {
            SqlerRequest request = new SqlerRequest("updateWhAsnPickingTaskByPrimaryKey");
            for (WhAsnPickingTask entity : entityList) {
                request.addBatchDataParam(WhAsnPickingTaskDBField.ID, DataType.INT, entity.getId());
                
                request.addBatchDataParam(WhAsnPickingTaskDBField.RECEIVING_CODE, DataType.STRING, entity.getReceivingCode());
                request.addBatchDataParam(WhAsnPickingTaskDBField.TASK_NO, DataType.STRING, entity.getTaskNo());
                request.addBatchDataParam(WhAsnPickingTaskDBField.WAREHOUSE_CODE_DELIVER, DataType.STRING, entity.getWarehouseCodeDeliver());
                request.addBatchDataParam(WhAsnPickingTaskDBField.WAREHOUSE_CODE, DataType.STRING, entity.getWarehouseCode());
                request.addBatchDataParam(WhAsnPickingTaskDBField.SKU_TOTAL, DataType.INT, entity.getSkuTotal());
                request.addBatchDataParam(WhAsnPickingTaskDBField.SKU_SPECIES, DataType.INT, entity.getSkuSpecies());
                request.addBatchDataParam(WhAsnPickingTaskDBField.TASK_STATUS, DataType.INT, entity.getTaskStatus());
                request.addBatchDataParam(WhAsnPickingTaskDBField.RECEIVE_PERSON, DataType.INT, entity.getReceivePerson());
                
                request.addBatchDataParam(WhAsnPickingTaskDBField.CREATED_DATE, DataType.TIMESTAMP, entity.getCreatedDate());
                request.addBatchDataParam(WhAsnPickingTaskDBField.RECEIVE_DATE, DataType.TIMESTAMP, entity.getReceiveDate());
                request.addBatchDataParam(WhAsnPickingTaskDBField.LAST_UPDATE_DATE, DataType.TIMESTAMP, new Timestamp(System.currentTimeMillis()));
                request.addBatchDataParam(WhAsnPickingTaskDBField.LAST_UPDATE_BY, DataType.INT, entity.getLastUpdateBy());
                request.addBatchDataParam(WhAsnPickingTaskDBField.PICKING_END_DATE, DataType.TIMESTAMP, entity.getPickingEndDate());
                request.addBatchDataParam(WhAsnPickingTaskDBField.IS_PRINTING, DataType.INT, entity.getIsPrinting());
                request.addBatchDataParam(WhAsnPickingTaskDBField.PRINT_USER, DataType.INT, entity.getPrintUser());
                request.addBatchDataParam(WhAsnPickingTaskDBField.PRINT_DATE, DataType.TIMESTAMP, entity.getPrintDate());
                request.addBatchDataParam(WhAsnPickingTaskDBField.IS_SPAN, DataType.INT, entity.getIsSpan());
                request.addBatch();
            }
            SqlerTemplate.batchUpdate(request);
        }
    }

    @Override
    public void deleteWhAsnPickingTask(Integer primaryKey) {
        SqlerRequest request = new SqlerRequest("deleteWhAsnPickingTaskByPrimaryKey");
        request.addDataParam(WhAsnPickingTaskDBField.ID, DataType.INT, primaryKey);
        SqlerTemplate.execute(request);
    }
}