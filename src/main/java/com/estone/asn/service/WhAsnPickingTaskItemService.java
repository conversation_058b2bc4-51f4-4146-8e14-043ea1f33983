package com.estone.asn.service;

import com.estone.asn.bean.WhAsnPickingTaskItem;
import com.estone.asn.bean.WhAsnPickingTaskItemQueryCondition;
import com.whq.tool.component.Pager;
import java.util.List;

public interface WhAsnPickingTaskItemService {
    List<WhAsnPickingTaskItem> queryAllWhAsnPickingTaskItems();

    List<WhAsnPickingTaskItem> queryWhAsnPickingTaskItems(WhAsnPickingTaskItemQueryCondition query, Pager pager);

    WhAsnPickingTaskItem getWhAsnPickingTaskItem(Integer id);

    WhAsnPickingTaskItem getWhAsnPickingTaskItemDetail(Integer id);

    WhAsnPickingTaskItem queryWhAsnPickingTaskItem(WhAsnPickingTaskItemQueryCondition query);

    void createWhAsnPickingTaskItem(WhAsnPickingTaskItem whAsnPickingTaskItem);

    void batchCreateWhAsnPickingTaskItem(List<WhAsnPickingTaskItem> entityList);

    void deleteWhAsnPickingTaskItem(Integer id);

    void updateWhAsnPickingTaskItem(WhAsnPickingTaskItem whAsnPickingTaskItem);

    void batchUpdateWhAsnPickingTaskItem(List<WhAsnPickingTaskItem> entityList);
}