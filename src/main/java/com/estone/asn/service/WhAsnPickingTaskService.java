package com.estone.asn.service;

import com.estone.asn.bean.WhAsn;
import com.estone.asn.bean.WhAsnPickingTask;
import com.estone.asn.bean.WhAsnPickingTaskQueryCondition;
import com.whq.tool.component.Pager;
import java.util.List;

public interface WhAsnPickingTaskService {
    List<WhAsnPickingTask> queryAllWhAsnPickingTasks();

    List<WhAsnPickingTask> queryWhAsnPickingTasks(WhAsnPickingTaskQueryCondition query, Pager pager);

    WhAsnPickingTask getWhAsnPickingTask(Integer id);

    WhAsnPickingTask getWhAsnPickingTaskDetail(Integer id);

    WhAsnPickingTask queryWhAsnPickingTask(WhAsnPickingTaskQueryCondition query);

    void createWhAsnPickingTask(WhAsnPickingTask whAsnPickingTask);

    void batchCreateWhAsnPickingTask(List<WhAsnPickingTask> entityList);

    void deleteWhAsnPickingTask(Integer id);

    Integer updateWhAsnPickingTask(WhAsnPickingTask whAsnPickingTask);

    void batchUpdateWhAsnPickingTask(List<WhAsnPickingTask> entityList);

    void createWhAsnPickTaskItem(WhAsnPickingTask task, String content) throws Exception;

    List<WhAsnPickingTask> queryWhAsnPickingTasksAndItem(WhAsnPickingTaskQueryCondition query, Pager page);

    void pushAsnAllocation(WhAsn whAsn, WhAsnPickingTask task);
}