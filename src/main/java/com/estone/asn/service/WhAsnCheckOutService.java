package com.estone.asn.service;

import com.estone.asn.bean.WhAsn;
import com.estone.asn.bean.WhAsnQueryCondition;

public interface WhAsnCheckOutService {

    void syncAsn(WhAsnQueryCondition query) throws Exception;

    void doAllot(WhAsn whAsn,WhAsn currentWhAsn) throws Exception;

    void exeAllot(WhAsn whAsn, Integer warehouseId) throws Exception ;

    void doGenPackingTask(WhAsn whAsn, Integer warehouseId) throws Exception;


    void doCancelAsn(WhAsn whAsn, Integer warehouseId) throws Exception;

    void doBoxCheck(WhAsn whAsn) throws Exception;

    String printCheckOut(WhAsn exist, String type) throws Exception;

    void generateWhAsn(WhAsn whAsn) throws Exception;

    String printXiangmaiFBA(WhAsn whAsn) throws Exception;

    void uploadBoxInfoComplete(WhAsn whAsn);

    void batchExeAllot(boolean isSpan);
}
