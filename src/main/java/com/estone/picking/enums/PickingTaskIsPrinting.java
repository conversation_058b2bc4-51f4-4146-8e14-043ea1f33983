package com.estone.picking.enums;

/**
 * 
 * @Description:
 * 
 * @ClassName: PickingTaskIsPrinting
 * @Author: qinyangkai
 * @Date: 2018/09/04
 * @Version: 0.0.1
 */
public enum PickingTaskIsPrinting {
    UNPRINTING("未打印", "0"), PRINTING("已打印", "1"), PAPER_PRINTING("前置打印", "2");

    private String code;

    private String name;

    private PickingTaskIsPrinting(String name, String code) {
        this.name = name;
        this.code = code;
    }

    public String getCode() {
        return code;
    }

    public Integer intCode() {
        return Integer.valueOf(code);
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public static String getNameByCode(String code) {
        PickingTaskIsPrinting[] values = values();
        for (PickingTaskIsPrinting type : values) {
            if (type.code.equals(code)) {
                return type.getName();
            }
        }
        return null;
    }
}
