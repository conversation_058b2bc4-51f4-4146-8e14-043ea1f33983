package com.estone.picking.enums;

/**
 * PDA拣货类型
 */
public enum PdaPickingType {
    ORDINARYMULTISPECIES("普通多品", "1"),
    ORDINARYNOTMULTISPECIES("普通非多品", "3"),
    EXPRESSAGE("快递业务", "4"),
    FBATASK("FBA", "6"),
    PACMULTISPECIES("优选仓多品", "11"),
    PACNOTMULTISPECIES("优选仓非多品", "12"),
    PAC_RU_MULTISPECIES("集包多品", "13"),
    PAC_RU_NOT_MULTISPECIES("集包非多品", "14"),
    ASN_FBA_TASK("海外仓FBA", "9"),
    ASN_FIRST_TASK("海外仓头程单", "15"),
    OVERSIZE_MULTISPECIES("超体积多品","17"),
    OVERSIZE_NOT_MULTISPECIES("超体积非多品","19"),
    TRANSFER_SINGLEMULTIPLE("中转仓非多品", "52"),
    TRANSFER_MULTIPLEMULTIPLE("中转仓多品", "53"),
    TEMU_MULTIPLEMULTIPLE("拼多多备货", "56"),

    JIT_NOTMULTIPLEMULTIPLE("JIT非多品", "60"),
    JIT_MULTIPLEMULTIPLE("JIT多品", "61"),
    JIT_ASN_NOTMULTIPLEMULTIPLE("仓发单品", "64"),
    JIT_ASN_MULTIPLEMULTIPLE("仓发多品", "65"),

    ;

    private String code;

    private String name;

    private PdaPickingType(String name, String code) {
        this.name = name;
        this.code = code;
    }

    public String getCode() {
        return code;
    }

    public Integer intCode() {
        return Integer.valueOf(code);
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public static String getNameByCode(String code) {
        PdaPickingType[] values = values();
        for (PdaPickingType type : values) {
            if (type.code.equals(code)) {
                return type.getName();
            }
        }
        return null;
    }
    public static PdaPickingType build(String code) {
        PdaPickingType[] values = values();

        for (PdaPickingType type : values) {
            if (type.code.equals(code)) {
                return type;
            }
        }

        return null;
    }
}
