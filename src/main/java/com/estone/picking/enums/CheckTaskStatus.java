package com.estone.picking.enums;

/**
 *核对任务状态
 * <AUTHOR>
 */
public enum CheckTaskStatus {

    UNRECEIVED("待领取", "0"),
    PICKING("核对中", "1"),
    COMPLETED("已完成", "2"),
    DISCARDED("已废除", "5"),
    ;

    private String code;

    private String name;

    private CheckTaskStatus(String name, String code) {
        this.name = name;
        this.code = code;
    }

    public String getCode() {
        return code;
    }

    public Integer intCode() {
        return Integer.valueOf(code);
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public static String getNameByCode(String code) {
        CheckTaskStatus[] values = values();
        for (CheckTaskStatus type : values) {
            if (type.code.equals(code)) {
                return type.getName();
            }
        }
        return null;
    }
}
