package com.estone.picking.utils;

import org.apache.commons.lang.StringUtils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.estone.common.util.CacheUtils;
import com.estone.common.util.HttpUtils;
import com.estone.common.util.model.ApiResult;
import com.whq.tool.exception.BusinessException;

import lombok.extern.slf4j.Slf4j;

/**
 * @Description: 爬虫翻译
 * @Author: Yimeil
 * @Date: 2024/7/22 17:40
 * @Version: 1.0.0
 */
@Slf4j
public class PyTransUtils {

    /**
     *
     * @param text string 或 list 类型，待翻译文本信息,单个文本的最大字符限制为5000个
     * @param srcLang 类型，待翻译语种
     * @param destLang 类型，目标语种
     * @return
     */
    public static String googleTrans(String text, String srcLang, String destLang) {
        String result = "";
        try {
            String url = CacheUtils.SystemParamGet("product_system.climb_google_trans").getParamValue();
            if (StringUtils.isBlank(url)) {
                throw new Exception("google翻译配置url为空");
            }

            JSONObject jsonObject = new JSONObject();
            jsonObject.put("text", text);
            jsonObject.put("srcLang", srcLang);
            jsonObject.put("destLang", destLang);
            ApiResult<String> apiResult = HttpUtils.post(url, HttpUtils.ACCESS_TOKEN, jsonObject, ApiResult.class);
            log.info(JSON.toJSONString(apiResult));
            if (apiResult == null || !apiResult.isSuccess() || apiResult.getResult() == null) {
                throw new BusinessException(
                        "调用爬虫翻译失败：" + (apiResult != null && StringUtils.isNotBlank(apiResult.getErrorMsg())
                                ? apiResult.getErrorMsg()
                                : ""));
            }
            return apiResult.getResult();

        }
        catch (Exception e) {
            log.error("调用爬虫翻译失败：" + e.getMessage(), e);
        }
        return result;
    }
}
