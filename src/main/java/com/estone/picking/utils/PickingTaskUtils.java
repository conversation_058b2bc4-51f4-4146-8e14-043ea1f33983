package com.estone.picking.utils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import com.estone.common.util.CacheUtils;
import com.estone.common.util.SpringUtils;
import com.estone.picking.bean.WhPickingTaskItem;
import com.estone.picking.bean.WhPickingTaskItemQueryCondition;
import com.estone.picking.dao.WhPickingTaskItemDao;

/**
 * @Description: 拣货任务工具类
 * @Author: Yimeil
 * @Date: 2021/8/25 10:44
 * @Version: 1.0.0
 */
public class PickingTaskUtils {

    private static WhPickingTaskItemDao whPickingTaskItemDao = SpringUtils.getBean(WhPickingTaskItemDao.class);

    /**
     * 检验是否跨楼层
     * 
     * @param taskId
     * @return
     */
    public static boolean checkCrossFloor(Integer taskId,Integer taskType) {

        if (taskId == null)
            return false;
        WhPickingTaskItemQueryCondition tempQueryObject = new WhPickingTaskItemQueryCondition();
        tempQueryObject.setTaskId(taskId);
        tempQueryObject.setTaskType(taskType);
        List<WhPickingTaskItem> taskItems = whPickingTaskItemDao.queryPickingTaskItemAndSkuList(tempQueryObject, null);
        if (CollectionUtils.isEmpty(taskItems))
            return false;

        List<String> locationList = new ArrayList<>();
        taskItems.forEach(item -> {
            if (CollectionUtils.isNotEmpty(item.getWhApvItems())) {
                item.getWhApvItems().forEach(apvItem -> {
                    if (apvItem.getWhSku() != null && StringUtils.isNotEmpty(apvItem.getWhSku().getLocationNumber())) {
                        locationList.add(apvItem.getWhSku().getLocationNumber());
                    }
                });
            }
        });

        if (CollectionUtils.isEmpty(locationList))
            return false;

        String secondFloorLocationRegion = CacheUtils.SystemParamGet("apv_params.secondFloorLocationRegion")
                .getParamValue();
        if (StringUtils.isEmpty(secondFloorLocationRegion))
            return false;
        // 把引号去掉
        secondFloorLocationRegion = secondFloorLocationRegion.replaceAll("\"", "");
        // 把空格去掉
        secondFloorLocationRegion = secondFloorLocationRegion.replaceAll(" ", "");
        // 用逗号将字符串分开，得到字符串数组
        String[] strs = secondFloorLocationRegion.split(",");
        // 将字符串数组转换成集合list
        List<String> regionList = Arrays.asList(strs);

        // 全部是2楼库区
        boolean bool2 = locationList.stream().allMatch(i -> regionList.contains(StringUtils.substring(i, 0, 1)));
        if (bool2)
            return false;

        // 全部是3楼库区
        boolean bool3 = locationList.stream().allMatch(i -> !regionList.contains(StringUtils.substring(i, 0, 1)));

        if (bool3)
            return false;

        return true;
    }
}
