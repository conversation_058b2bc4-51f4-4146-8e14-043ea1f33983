package com.estone.picking.bean;

import lombok.Data;

import java.util.List;

@Data
public class WhPickingTaskItemQueryCondition extends WhPickingTaskItem {
    private static final long serialVersionUID = 1L;

    // sku
    private String sku;

    // 任务类型，用于区分二次配货
    private Integer taskType;

    private Integer isAsn;

    private List<Integer> taskIds;

    // 是否查从库
    private Boolean readonly;
}