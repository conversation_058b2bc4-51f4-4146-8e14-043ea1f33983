package com.estone.picking.bean;

import java.io.Serializable;
import java.util.Optional;

import lombok.Data;

@Data
public class ExpManageCheckTaskItem implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     *  database column exp_manage_check_task_item.id
     */
    private Integer id;

    /**
     * 关联单据ID database column exp_manage_check_task_item.relation_id
     */
    private Integer relationId;

    /**
     * 批次号 database column exp_manage_check_task_item.batch_no
     */
    private String batchNo;

    /**
     * 批次在库数量 database column exp_manage_check_task_item.quantity
     */
    private Integer quantity;

    /**
     * 核对数量 database column exp_manage_check_task_item.check_quantity
     */
    private Integer checkQuantity;

    //差异数量
    public Integer getDiffQuantity(){
        return  Optional.ofNullable(checkQuantity).orElse(0)-Optional.ofNullable(quantity).orElse(0) ;
    }
    
}