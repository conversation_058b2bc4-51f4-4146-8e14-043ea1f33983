package com.estone.picking.bean;

import java.util.List;

public class WhAllocationPickTaskQueryCondition extends WhAllocationPickTask {
    private static final long serialVersionUID = 1L;
    private String fromCreatedDate;// 创建时间
    private String toCreatedDate;// 创建时间

    private String fromReceiveDate;// 领取时间
    private String toReceiveDate;// 领取时间

    private List<Integer> taskIds;
    
    private Integer allocationType;// 调拨类型==2:订单调拨;1:库存调拨;3:海外仓调拨

    /**
     * 是否是导出查询
     */
    private boolean download = false;

    private Integer beforStatus;

    private Integer afterStatus;

    public String getFromCreatedDate() {
        return fromCreatedDate;
    }

    public void setFromCreatedDate(String fromCreatedDate) {
        this.fromCreatedDate = fromCreatedDate;
    }

    public String getToCreatedDate() {
        return toCreatedDate;
    }

    public void setToCreatedDate(String toCreatedDate) {
        this.toCreatedDate = toCreatedDate;
    }

    public String getFromReceiveDate() {
        return fromReceiveDate;
    }

    public void setFromReceiveDate(String fromReceiveDate) {
        this.fromReceiveDate = fromReceiveDate;
    }

    public String getToReceiveDate() {
        return toReceiveDate;
    }

    public void setToReceiveDate(String toReceiveDate) {
        this.toReceiveDate = toReceiveDate;
    }

    public List<Integer> getTaskIds() {
        return taskIds;
    }

    public void setTaskIds(List<Integer> taskIds) {
        this.taskIds = taskIds;
    }

    public Integer getAllocationType() {
        return allocationType;
    }

    public void setAllocationType(Integer allocationType) {
        this.allocationType = allocationType;
    }

    public boolean isDownload() {
        return download;
    }

    public void setDownload(boolean download) {
        this.download = download;
    }

    public Integer getBeforStatus() {
        return beforStatus;
    }

    public void setBeforStatus(Integer beforStatus) {
        this.beforStatus = beforStatus;
    }

    public Integer getAfterStatus() {
        return afterStatus;
    }

    public void setAfterStatus(Integer afterStatus) {
        this.afterStatus = afterStatus;
    }

}