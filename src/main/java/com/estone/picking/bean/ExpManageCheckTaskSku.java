package com.estone.picking.bean;

import java.io.Serializable;
import lombok.Data;

@Data
public class ExpManageCheckTaskSku implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     *  database column exp_manage_check_task_sku.id
     */
    private Integer id;

    /**
     * 关联ID database column exp_manage_check_task_sku.relation_id
     */
    private Integer relationId;

    /**
     * 批次号 database column exp_manage_check_task_sku.batch_no
     */
    private String batchNo;

    /**
     * 唯一SKU码 database column exp_manage_check_task_sku.uuid
     */
    private String uuid;

    /**
     * sku database column exp_manage_check_task_sku.sku
     */
    private String sku;

    /**
     * 核对状态 database column exp_manage_check_task_sku.status
     */
    private Integer status;
}