package com.estone.picking.bean;

import lombok.Data;

import java.util.List;

@Data
public class LocationCheckTaskQueryCondition extends LocationCheckTask {
    private static final long serialVersionUID = 1L;

    private Boolean readOnly = false;

    private List<Integer> taskIdList;

    private String locationNumber;

    private String sku;

    private String aisle;

    private String region;

    private String fromCreatedDate;
    private String toCreatedDate;

    private String fromReceiveDate;

    private String toReceiveDate;

    private String fromFinishDate;

    private String toFinishDate;

    // 领取人是否为null
    private Boolean receiverIsNull;
}