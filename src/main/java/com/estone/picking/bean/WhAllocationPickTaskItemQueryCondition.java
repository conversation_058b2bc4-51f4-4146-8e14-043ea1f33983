package com.estone.picking.bean;

import java.util.ArrayList;
import java.util.List;

public class WhAllocationPickTaskItemQueryCondition extends WhAllocationPickTaskItem {
    private static final long serialVersionUID = 1L;

    private String taskNo;

    private List<String> skuList = new ArrayList<String>();

    private Integer allocationId;

    public Integer getAllocationId() {
        return allocationId;
    }

    public void setAllocationId(Integer allocationId) {
        this.allocationId = allocationId;
    }

    public List<String> getSkuList() {
        return skuList;
    }

    public void setSkuList(List<String> skuList) {
        this.skuList = skuList;
    }

    public String getTaskNo() {
        return taskNo;
    }

    public void setTaskNo(String taskNo) {
        this.taskNo = taskNo;
    }

}