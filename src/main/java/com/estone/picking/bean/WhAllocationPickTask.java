package com.estone.picking.bean;

import java.io.Serializable;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;

import com.estone.allocation.bean.WhApvAllocation;
import com.estone.sku.bean.WhSku;

public class WhAllocationPickTask implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键 This field corresponds to the database column
     * wh_allocation_pick_task.task_id
     *
     * @mbggenerated Thu Mar 14 14:55:31 CST 2019
     */
    private Integer taskId;

    /**
     * 拣货任务号 This field corresponds to the database column
     * wh_allocation_pick_task.task_no
     *
     * @mbggenerated Thu Mar 14 14:55:31 CST 2019
     */
    private String taskNo;

    /**
     * 调拨单id This field corresponds to the database column
     * wh_allocation_pick_task.allocation_id
     *
     * @mbggenerated Thu Mar 14 14:55:31 CST 2019
     */
    private Integer allocationId;

    /**
     * 调拨单号 This field corresponds to the database column
     * wh_allocation_pick_task.allocation_no
     *
     * @mbggenerated Thu Mar 14 14:55:31 CST 2019
     */
    private String allocationNo;

    /**
     * 任务状态 1=待拣货 2=拣货中 3=拣货完成 This field corresponds to the database column
     * wh_allocation_pick_task.task_status
     *
     * @mbggenerated Thu Mar 14 14:55:31 CST 2019
     */
    private Integer taskStatus;

    /**
     * 打印状态 0=未打印 1=已打印 This field corresponds to the database column
     * wh_allocation_pick_task.print_status
     *
     * @mbggenerated Thu Mar 14 14:55:31 CST 2019
     */
    private Integer printStatus;

    /**
     * 创建人 This field corresponds to the database column
     * wh_allocation_pick_task.create_by
     *
     * @mbggenerated Thu Mar 14 14:55:31 CST 2019
     */
    private Integer createBy;

    /**
     * 创建时间 This field corresponds to the database column
     * wh_allocation_pick_task.create_time
     *
     * @mbggenerated Thu Mar 14 14:55:31 CST 2019
     */
    private Timestamp createTime;

    /**
     * 领取人 This field corresponds to the database column
     * wh_allocation_pick_task.receive_by
     *
     * @mbggenerated Thu Mar 14 14:55:31 CST 2019
     */
    private Integer receiveBy;

    /**
     * 领取时间 This field corresponds to the database column
     * wh_allocation_pick_task.receive_time
     *
     * @mbggenerated Thu Mar 14 14:55:31 CST 2019
     */
    private Timestamp receiveTime;

    /**
     * 修改人 This field corresponds to the database column
     * wh_allocation_pick_task.update_by
     *
     * @mbggenerated Thu Mar 14 14:55:31 CST 2019
     */
    private Integer updateBy;

    /**
     * 修改时间 This field corresponds to the database column
     * wh_allocation_pick_task.update_time
     *
     * @mbggenerated Thu Mar 14 14:55:31 CST 2019
     */
    private Timestamp updateTime;

    // 任务类型  1库存调拨,2订单调拨,3海外仓调拨
    private Integer allocationType;

    private List<WhAllocationPickTaskItem> whAllocationPickTaskItems = new ArrayList<WhAllocationPickTaskItem>();

    private WhSku whSku;

    // sku的种类
    private Integer skuType;

    // 商品的数量 sku的总数量
    private Integer skuQuantity;

    private WhApvAllocation whApvAllocation;

    public Integer getTaskId() {
        return taskId;
    }

    public void setTaskId(Integer taskId) {
        this.taskId = taskId;
    }

    public String getTaskNo() {
        return taskNo;
    }

    public void setTaskNo(String taskNo) {
        this.taskNo = taskNo;
    }

    public Integer getAllocationId() {
        return allocationId;
    }

    public void setAllocationId(Integer allocationId) {
        this.allocationId = allocationId;
    }

    public String getAllocationNo() {
        return allocationNo;
    }

    public void setAllocationNo(String allocationNo) {
        this.allocationNo = allocationNo;
    }

    public Integer getTaskStatus() {
        return taskStatus;
    }

    public void setTaskStatus(Integer taskStatus) {
        this.taskStatus = taskStatus;
    }

    public Integer getPrintStatus() {
        return printStatus;
    }

    public void setPrintStatus(Integer printStatus) {
        this.printStatus = printStatus;
    }

    public Integer getCreateBy() {
        return createBy;
    }

    public void setCreateBy(Integer createBy) {
        this.createBy = createBy;
    }

    public Timestamp getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Timestamp createTime) {
        this.createTime = createTime;
    }

    public Integer getReceiveBy() {
        return receiveBy;
    }

    public void setReceiveBy(Integer receiveBy) {
        this.receiveBy = receiveBy;
    }

    public Timestamp getReceiveTime() {
        return receiveTime;
    }

    public void setReceiveTime(Timestamp receiveTime) {
        this.receiveTime = receiveTime;
    }

    public Integer getUpdateBy() {
        return updateBy;
    }

    public void setUpdateBy(Integer updateBy) {
        this.updateBy = updateBy;
    }

    public Timestamp getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Timestamp updateTime) {
        this.updateTime = updateTime;
    }

    public List<WhAllocationPickTaskItem> getWhAllocationPickTaskItems() {
        return whAllocationPickTaskItems;
    }

    public void setWhAllocationPickTaskItems(List<WhAllocationPickTaskItem> whAllocationPickTaskItems) {
        this.whAllocationPickTaskItems = whAllocationPickTaskItems;
    }

    public WhSku getWhSku() {
        return whSku;
    }

    public void setWhSku(WhSku whSku) {
        this.whSku = whSku;
    }

    public Integer getSkuType() {
        return skuType;
    }

    public void setSkuType(Integer skuType) {
        this.skuType = skuType;
    }

    public Integer getSkuQuantity() {
        return skuQuantity;
    }

    public void setSkuQuantity(Integer skuQuantity) {
        this.skuQuantity = skuQuantity;
    }

    public WhApvAllocation getWhApvAllocation() {
        return whApvAllocation;
    }

    public void setWhApvAllocation(WhApvAllocation whApvAllocation) {
        this.whApvAllocation = whApvAllocation;
    }

    public Integer getAllocationType() {
        return allocationType;
    }

    public void setAllocationType(Integer allocationType) {
        this.allocationType = allocationType;
    }
}