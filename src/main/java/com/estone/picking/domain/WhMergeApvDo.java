package com.estone.picking.domain;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.estone.apv.bean.WhApv;
import com.estone.picking.bean.MeagerApvLogisticsConfig;
import com.estone.picking.bean.WhMergeApvQueryCondition;
import com.estone.picking.enums.MergeApvType;
import com.estone.picking.enums.PickingTaskWarehouseType;
import com.estone.picking.enums.PickingTaskWaybillType;
import com.whq.tool.component.Pager;
import lombok.Data;

@Data
public class WhMergeApvDo {

    /**
     * 发货单列表
     */
    private List<WhApv> whApvs = new ArrayList<WhApv>();

    /**
     * 分页
     */
    private Pager page = new Pager();

    /**
     * 查询实体
     */
    private WhMergeApvQueryCondition whMergeApvQueryCondition;

    /**
     * 合单库位上限数
     */
    private Integer locationCount;

    /**
     * 合单单量上限数
     */
    private Integer apvSkuCount;

    /**
     * 生成爆款拣货任务 最小销售数量
     */
    private Integer startSalesQuatity;

    /**
     * 发货单合单类型
     */
    private MergeApvType[] mergeApvTypeList = new MergeApvType[] {};

    /**
     * 面单类型
     */
    private PickingTaskWaybillType[] waybillTypeList = new PickingTaskWaybillType[] {};

    /**
     * 仓库类型
     */
    private PickingTaskWarehouseType[] warehouseTypeList = new PickingTaskWarehouseType[] {};

    private String mergeApvTypes;// 发货单合单类型JSON
    private String waybillTypes;// 面单类型JSON
    private String warehouseTypes;// 仓库类型JSON
    private String apvOrderTypes;// 发货类型

    /**
     * 销售平台
     */
    private Map<String, String> saleChannelMap = new HashMap<>();

    /**
     * 区域
     */
    private String locationRegionList;

    /**
     * 通道
     */
    private String locationAisleList;

    /**
     * 本仓所有通道
     */
    private String locationAllAisleList;

    /**
     * 合单加急渠道
     */
    private List<MeagerApvLogisticsConfig> meagerApvLogisticsConfigList;
}