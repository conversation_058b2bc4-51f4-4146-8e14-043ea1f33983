package com.estone.picking.domain;

import java.util.ArrayList;
import java.util.List;

import com.estone.picking.bean.WhPickingTask;
import com.estone.picking.bean.WhPickingTaskQueryCondition;
import com.estone.picking.enums.*;
import com.whq.tool.component.Pager;
import lombok.Data;

@Data
public class WhPickingTaskDo {
    private WhPickingTask whPickingTask;

    private WhPickingTaskQueryCondition query = new WhPickingTaskQueryCondition();

    private List<WhPickingTask> whPickingTasks = new ArrayList<WhPickingTask>();

    private Pager page = new Pager();

    private PickingTaskType[] typeList = new PickingTaskType[] {};// 类型

    private PickingTaskStatus[] statusList = new PickingTaskStatus[] {};// 状态

    private PickingTaskIsPrinting[] isPrintingList = new PickingTaskIsPrinting[] {};// 打印状态

    private PickingTaskWarehouseType[] warehouseList = new PickingTaskWarehouseType[] {};// 仓库类型

    private PickingTaskWaybillType[] waybillTypeList = new PickingTaskWaybillType[] {}; // 面单类型

    private String gridStatus; // 播种状态

    private String taskTypes;// 类型JSON

    private String flagTypes;// 标签类型JSON

    private String taskStatuss;// 状态JSON

    private String isPrintings;// 打印状态JSON

    private String warehouses;// 仓库类型JSON

    private String waybillTypes; // 面单类型JSON

    private List<String> skuList = new ArrayList<String>();// 商品编码集合

    private String title;
}