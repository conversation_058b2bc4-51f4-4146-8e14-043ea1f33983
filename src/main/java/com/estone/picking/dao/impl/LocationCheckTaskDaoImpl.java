package com.estone.picking.dao.impl;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.stereotype.Repository;
import org.springframework.util.Assert;

import com.estone.common.util.CommonUtils;
import com.estone.common.util.SqlerTemplate;
import com.estone.picking.bean.LocationCheckTask;
import com.estone.picking.bean.LocationCheckTaskQueryCondition;
import com.estone.picking.dao.LocationCheckTaskDao;
import com.estone.picking.dao.mapper.LocationCheckTaskDBField;
import com.estone.picking.dao.mapper.LocationCheckTaskItemDBField;
import com.estone.picking.dao.mapper.LocationCheckTaskMapper;
import com.estone.warehouse.bean.WhLocation;
import com.estone.warehouse.bean.WhStock;
import com.estone.warehouse.dao.mapper.WhLocationDBField;
import com.estone.warehouse.dao.mapper.WhStockDBField;
import com.whq.tool.component.Pager;
import com.whq.tool.context.DataContextHolder;
import com.whq.tool.sqler.SqlerRequest;
import com.whq.tool.sqler.analyzer.DataType;
import com.whq.tool.sqler.dialect.DialectFactory;
import com.whq.tool.sqler.dialect.SQLDialect;

@Repository("locationCheckTaskDao")
public class LocationCheckTaskDaoImpl implements LocationCheckTaskDao {

    private void setQueryCondition(SqlerRequest request, LocationCheckTaskQueryCondition query) {
        if (query == null) {
            return;
        }
        // 添加只读权限
        if (query.getReadOnly() != null && query.getReadOnly()) {
            request.setReadOnly(true);
        }
        // 添加查询条件
        // 搜索条件不允许出现空字符
        request.setAllowBlank(false);

        request.addDataParam(LocationCheckTaskDBField.ID, DataType.INT, query.getId());
        request.addDataParam(LocationCheckTaskDBField.TASK_STATUS, DataType.INT, query.getTaskStatus());
        request.addDataParam(LocationCheckTaskDBField.TASK_STATUS, DataType.INT, query.getTaskStatus());
        request.addDataParam(LocationCheckTaskDBField.RECEIVE_BY, DataType.INT, query.getReceiveBy());
        request.addDataParam(LocationCheckTaskDBField.CREATED_BY, DataType.INT, query.getCreatedBy());
        request.addDataParam(LocationCheckTaskDBField.FINISH_BY, DataType.INT, query.getFinishBy());
        if (StringUtils.isNotBlank(query.getTaskNo())) {
            if (StringUtils.contains(query.getTaskNo(), ",")) {
                List<String> list = CommonUtils.splitList(query.getTaskNo(), ",");
                request.addDataParam("task_no_list", DataType.STRING, list);
            }
            else {
                request.addDataParam(LocationCheckTaskDBField.TASK_NO, DataType.STRING, query.getTaskNo());
            }
        }
        if (CollectionUtils.isNotEmpty(query.getTaskIdList())) {
            request.addDataParam("id_list", DataType.INT, query.getTaskIdList());
        }
        if (StringUtils.isNotBlank(query.getLocationNumber())) {
            if (StringUtils.contains(query.getLocationNumber(), ",")) {
                List<String> list = CommonUtils.splitList(query.getLocationNumber(), ",");
                request.addDataParam("location_no_list", DataType.STRING, list);
            }
            else {
                request.addDataParam(LocationCheckTaskItemDBField.LOCATION_NUMBER, DataType.STRING,
                        query.getLocationNumber());
            }
        }
        if (StringUtils.isNotBlank(query.getSku())) {
            if (StringUtils.contains(query.getSku(), ",")) {
                List<String> list = CommonUtils.splitList(query.getSku(), ",");
                request.addDataParam("sku_list", DataType.STRING, list);
            }
            else {
                request.addDataParam(LocationCheckTaskItemDBField.SKU, DataType.STRING, query.getSku());
            }
        }
        // 处理区域通道请求
        if (StringUtils.isNotBlank(query.getAisle()) && query.getAisle().indexOf(",") > -1) {
            request.addDataParam("aisle_list", DataType.STRING, Arrays.asList(query.getAisle().split(",")));
        }
        else {
            request.addDataParam(WhLocationDBField.LOCATION_AISLE, DataType.STRING, query.getAisle());
        }
        request.addDataParam(WhLocationDBField.LOCATION_REGION, DataType.STRING, query.getRegion());

        if (StringUtils.isNotBlank(query.getFromCreatedDate())) {
            request.addDataParam("from_created_date", DataType.STRING, query.getFromCreatedDate());
        }
        if (StringUtils.isNotBlank(query.getToCreatedDate())) {
            request.addDataParam("to_created_date", DataType.STRING, query.getToCreatedDate());
        }

        if (StringUtils.isNotBlank(query.getFromReceiveDate())) {
            request.addDataParam("from_receive_date", DataType.STRING, query.getFromReceiveDate());
        }
        if (StringUtils.isNotBlank(query.getToReceiveDate())) {
            request.addDataParam("to_receive_date", DataType.STRING, query.getToReceiveDate());
        }

        if (StringUtils.isNotBlank(query.getFromFinishDate())) {
            request.addDataParam("from_finish_date", DataType.STRING, query.getFromFinishDate());
        }
        if (StringUtils.isNotBlank(query.getToFinishDate())) {
            request.addDataParam("to_finish_date", DataType.STRING, query.getToFinishDate());
        }

        if (Objects.nonNull(query.getReceiverIsNull()) && query.getReceiverIsNull()){
            request.addSqlDataParam("RECEIVER_IS_NULL"," AND receive_by IS NULL ");
        }
    }

    @Override
    public int queryLocationCheckTaskCount(LocationCheckTaskQueryCondition query) {
        SqlerRequest request = new SqlerRequest("queryLocationCheckTaskCount");
        setQueryCondition(request, query);
        return SqlerTemplate.queryForInt(request);
    }

    @Override
    public List<LocationCheckTask> queryLocationCheckTaskList(LocationCheckTaskQueryCondition query, Pager pager) {
        SqlerRequest request = new SqlerRequest("queryLocationCheckTaskList");
        setQueryCondition(request, query);
        if (pager != null) {
            SQLDialect dial = DialectFactory.createDialect(null);

            long start = (long) (pager.getPageNo() - 1) * pager.getPageSize();

            long end = start + pager.getPageSize() - 1L;

            request.addSqlDataParam("LIMIT", dial.queryFirst2Last("", (int) start, (int) end));
        }
        return SqlerTemplate.query(request, new LocationCheckTaskMapper(true));
    }

    @Override
    public LocationCheckTask queryLocationCheckTask(Integer primaryKey) {
        Assert.notNull(primaryKey, "primaryKey is null!");
        SqlerRequest request = new SqlerRequest("queryLocationCheckTaskByPrimaryKey");
        request.addDataParam(LocationCheckTaskDBField.ID, DataType.INT, primaryKey);
        return SqlerTemplate.queryForObject(request, new LocationCheckTaskMapper());
    }

    @Override
    public LocationCheckTask queryLocationCheckTask(LocationCheckTaskQueryCondition query) {
        SqlerRequest request = new SqlerRequest("queryLocationCheckTask");
        setQueryCondition(request, query);
        return SqlerTemplate.queryForObject(request, new LocationCheckTaskMapper());
    }

    @Override
    public void createLocationCheckTask(LocationCheckTask entity) {
        SqlerRequest request = new SqlerRequest("createLocationCheckTask");
        request.addDataParam(LocationCheckTaskDBField.TASK_NO, DataType.STRING, entity.getTaskNo());
        request.addDataParam(LocationCheckTaskDBField.TASK_STATUS, DataType.INT, entity.getTaskStatus());
        request.addDataParam(LocationCheckTaskDBField.CREATED_BY, DataType.INT,
                entity.getCreatedBy() == null ? DataContextHolder.getUserId() : entity.getCreatedBy());
        request.addDataParam(LocationCheckTaskDBField.CREATED_DATE, DataType.TIMESTAMP,
                entity.getCreatedDate() == null ? new Timestamp(System.currentTimeMillis()) : entity.getCreatedDate());
        request.addDataParam(LocationCheckTaskDBField.RECEIVE_BY, DataType.INT, entity.getReceiveBy());
        request.addDataParam(LocationCheckTaskDBField.RECEIVE_DATE, DataType.TIMESTAMP, entity.getReceiveDate());
        request.addDataParam(LocationCheckTaskDBField.FINISH_BY, DataType.INT, entity.getFinishBy());
        request.addDataParam(LocationCheckTaskDBField.FINISH_DATE, DataType.TIMESTAMP, entity.getFinishDate());
        request.addDataParam(LocationCheckTaskDBField.IS_PRINTING, DataType.INT, entity.getIsPrinting());
        request.addDataParam(LocationCheckTaskDBField.PRINT_USER, DataType.INT, entity.getPrintUser());
        request.addDataParam(LocationCheckTaskDBField.PRINT_DATE, DataType.TIMESTAMP, entity.getPrintDate());
        Integer autoIncrementId = SqlerTemplate.executeAndReturn(request);
        entity.setId(autoIncrementId);
    }

    @Override
    public void updateLocationCheckTask(LocationCheckTask entity) {
        SqlerRequest request = new SqlerRequest("updateLocationCheckTaskByPrimaryKey");
        request.addDataParam(LocationCheckTaskDBField.ID, DataType.INT, entity.getId());

        request.addDataParam(LocationCheckTaskDBField.TASK_NO, DataType.STRING, entity.getTaskNo());
        request.addDataParam(LocationCheckTaskDBField.TASK_STATUS, DataType.INT, entity.getTaskStatus());

        request.addDataParam(LocationCheckTaskDBField.CREATED_DATE, DataType.TIMESTAMP, entity.getCreatedDate());
        request.addDataParam(LocationCheckTaskDBField.RECEIVE_BY, DataType.INT, entity.getReceiveBy());
        request.addDataParam(LocationCheckTaskDBField.RECEIVE_DATE, DataType.TIMESTAMP, entity.getReceiveDate());
        request.addDataParam(LocationCheckTaskDBField.FINISH_BY, DataType.INT, entity.getFinishBy());
        request.addDataParam(LocationCheckTaskDBField.FINISH_DATE, DataType.TIMESTAMP, entity.getFinishDate());
        request.addDataParam(LocationCheckTaskDBField.IS_PRINTING, DataType.INT, entity.getIsPrinting());
        request.addDataParam(LocationCheckTaskDBField.PRINT_USER, DataType.INT, entity.getPrintUser());
        request.addDataParam(LocationCheckTaskDBField.PRINT_DATE, DataType.TIMESTAMP, entity.getPrintDate());
        SqlerTemplate.execute(request);
    }

    @Override
    public void batchCreateLocationCheckTask(List<LocationCheckTask> entityList) {
        if (entityList != null && !entityList.isEmpty()) {
            SqlerRequest request = new SqlerRequest("createLocationCheckTask");
            for (LocationCheckTask entity : entityList) {
                request.addBatchDataParam(LocationCheckTaskDBField.TASK_NO, DataType.STRING, entity.getTaskNo());
                request.addBatchDataParam(LocationCheckTaskDBField.TASK_STATUS, DataType.INT, entity.getTaskStatus());
                request.addBatchDataParam(LocationCheckTaskDBField.CREATED_BY, DataType.INT,
                        entity.getCreatedBy() == null ? DataContextHolder.getUserId() : entity.getCreatedBy());
                request.addBatchDataParam(LocationCheckTaskDBField.CREATED_DATE, DataType.TIMESTAMP,
                        entity.getCreatedDate() == null ? new Timestamp(System.currentTimeMillis())
                                : entity.getCreatedDate());
                request.addBatchDataParam(LocationCheckTaskDBField.RECEIVE_BY, DataType.INT, entity.getReceiveBy());
                request.addBatchDataParam(LocationCheckTaskDBField.RECEIVE_DATE, DataType.TIMESTAMP,
                        entity.getReceiveDate());
                request.addBatchDataParam(LocationCheckTaskDBField.FINISH_BY, DataType.INT, entity.getFinishBy());
                request.addBatchDataParam(LocationCheckTaskDBField.FINISH_DATE, DataType.TIMESTAMP,
                        entity.getFinishDate());
                request.addBatchDataParam(LocationCheckTaskDBField.IS_PRINTING, DataType.INT, entity.getIsPrinting());
                request.addBatchDataParam(LocationCheckTaskDBField.PRINT_USER, DataType.INT, entity.getPrintUser());
                request.addBatchDataParam(LocationCheckTaskDBField.PRINT_DATE, DataType.TIMESTAMP,
                        entity.getPrintDate());
                request.addBatch();
            }
            SqlerTemplate.batchUpdate(request);
        }
    }

    @Override
    public void batchUpdateLocationCheckTask(List<LocationCheckTask> entityList) {
        if (entityList != null && !entityList.isEmpty()) {
            SqlerRequest request = new SqlerRequest("updateLocationCheckTaskByPrimaryKey");
            for (LocationCheckTask entity : entityList) {
                request.addBatchDataParam(LocationCheckTaskDBField.ID, DataType.INT, entity.getId());

                request.addBatchDataParam(LocationCheckTaskDBField.TASK_NO, DataType.STRING, entity.getTaskNo());
                request.addBatchDataParam(LocationCheckTaskDBField.TASK_STATUS, DataType.INT, entity.getTaskStatus());

                request.addBatchDataParam(LocationCheckTaskDBField.CREATED_DATE, DataType.TIMESTAMP,
                        entity.getCreatedDate());
                request.addBatchDataParam(LocationCheckTaskDBField.RECEIVE_BY, DataType.INT, entity.getReceiveBy());
                request.addBatchDataParam(LocationCheckTaskDBField.RECEIVE_DATE, DataType.TIMESTAMP,
                        entity.getReceiveDate());
                request.addBatchDataParam(LocationCheckTaskDBField.FINISH_BY, DataType.INT, entity.getFinishBy());
                request.addBatchDataParam(LocationCheckTaskDBField.FINISH_DATE, DataType.TIMESTAMP,
                        entity.getFinishDate());
                request.addBatchDataParam(LocationCheckTaskDBField.IS_PRINTING, DataType.INT, entity.getIsPrinting());
                request.addBatchDataParam(LocationCheckTaskDBField.PRINT_USER, DataType.INT, entity.getPrintUser());
                request.addBatchDataParam(LocationCheckTaskDBField.PRINT_DATE, DataType.TIMESTAMP,
                        entity.getPrintDate());
                request.addBatch();
            }
            SqlerTemplate.batchUpdate(request);
        }
    }

    @Override
    public void deleteLocationCheckTask(Integer primaryKey) {
        SqlerRequest request = new SqlerRequest("deleteLocationCheckTaskByPrimaryKey");
        request.addDataParam(LocationCheckTaskDBField.ID, DataType.INT, primaryKey);
        SqlerTemplate.execute(request);
    }

    @Override
    public List<WhStock> queryWhStockListByLocationOrRegions(List<String> locationNos, List<WhLocation> locations) {
        if (CollectionUtils.isEmpty(locationNos) && CollectionUtils.isEmpty(locations)) {
            return null;
        }
        SqlerRequest request = new SqlerRequest("queryWhStockListByLocationOrRegions");
        if (CollectionUtils.isNotEmpty(locationNos)) {
            request.addDataParam("locationNos", DataType.STRING, locationNos);
        }
        String sqlStr = " AND";
        if (CollectionUtils.isNotEmpty(locations)) {
            for (WhLocation whLocation : locations) {
                if (StringUtils.isEmpty(whLocation.getLocationRegion())
                        || StringUtils.isEmpty(whLocation.getLocationAisle())) {
                    continue;
                }
                String aisleSplit = "'" + whLocation.getLocationAisle().replaceAll(" ", "").replaceAll(",", "','")
                        + "'";
                sqlStr += " (location_region = '" + whLocation.getLocationRegion() + "' AND location_aisle IN ("
                        + aisleSplit + ")) OR";
            }
            sqlStr = StringUtils.substringBeforeLast(sqlStr, "OR");
            request.addSqlDataParam("LOCATIONNOSQL",
                    "AND location_number IN (SELECT location FROM wh_location WHERE 1=1" + sqlStr + ")");
        }
        return SqlerTemplate.query(request, new RowMapper<WhStock>() {
            @Override
            public WhStock mapRow(ResultSet rs, int i) throws SQLException {
                WhStock entity = new WhStock();
                entity.setSku(rs.getString(WhStockDBField.SKU));
                entity.setLocationNumber(rs.getString(WhStockDBField.LOCATION_NUMBER));
                return entity;
            }
        });
    }
}