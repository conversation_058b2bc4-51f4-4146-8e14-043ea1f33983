package com.estone.picking.dao.impl;

import java.sql.Timestamp;
import java.util.List;

import org.springframework.stereotype.Repository;
import org.springframework.util.Assert;

import com.estone.common.util.SqlerTemplate;
import com.estone.picking.bean.LocationCheckTaskItem;
import com.estone.picking.bean.LocationCheckTaskItemQueryCondition;
import com.estone.picking.dao.LocationCheckTaskItemDao;
import com.estone.picking.dao.mapper.LocationCheckTaskItemDBField;
import com.estone.picking.dao.mapper.LocationCheckTaskItemMapper;
import com.whq.tool.component.Pager;
import com.whq.tool.context.DataContextHolder;
import com.whq.tool.sqler.SqlerRequest;
import com.whq.tool.sqler.analyzer.DataType;

@Repository("locationCheckTaskItemDao")
public class LocationCheckTaskItemDaoImpl implements LocationCheckTaskItemDao {

    private void setQueryCondition(SqlerRequest request, LocationCheckTaskItemQueryCondition query) {
        if (query == null) {
            return;
        }
        // 添加只读权限
        if (query.getReadOnly() != null && query.getReadOnly()) {
            request.setReadOnly(true);
        }
        // 添加查询条件
        // 搜索条件不允许出现空字符
        request.setAllowBlank(false);

        request.addDataParam(LocationCheckTaskItemDBField.ID, DataType.INT, query.getId());
        request.addDataParam(LocationCheckTaskItemDBField.TASK_ID, DataType.INT, query.getTaskId());
        request.addDataParam(LocationCheckTaskItemDBField.STATUS, DataType.INT, query.getStatus());
        request.addDataParam(LocationCheckTaskItemDBField.SKU, DataType.STRING, query.getSku());
        request.addDataParam(LocationCheckTaskItemDBField.LOCATION_NUMBER, DataType.STRING, query.getLocationNumber());
    }

    @Override
    public int queryLocationCheckTaskItemCount(LocationCheckTaskItemQueryCondition query) {
        SqlerRequest request = new SqlerRequest("queryLocationCheckTaskItemCount");
        setQueryCondition(request, query);
        return SqlerTemplate.queryForInt(request);
    }

    @Override
    public List<LocationCheckTaskItem> queryLocationCheckTaskItemList() {
        SqlerRequest request = new SqlerRequest("queryLocationCheckTaskItemList");
        return SqlerTemplate.query(request, new LocationCheckTaskItemMapper());
    }

    @Override
    public List<LocationCheckTaskItem> queryLocationCheckTaskItemList(LocationCheckTaskItemQueryCondition query,
            Pager pager) {
        SqlerRequest request = new SqlerRequest("queryLocationCheckTaskItemList");
        setQueryCondition(request, query);
        if (pager != null) {
            request.addFetch(pager.getPageNo(), pager.getPageSize());
        }
        return SqlerTemplate.query(request, new LocationCheckTaskItemMapper());
    }

    @Override
    public LocationCheckTaskItem queryLocationCheckTaskItem(Integer primaryKey) {
        Assert.notNull(primaryKey, "primaryKey is null!");
        SqlerRequest request = new SqlerRequest("queryLocationCheckTaskItemByPrimaryKey");
        request.addDataParam(LocationCheckTaskItemDBField.ID, DataType.INT, primaryKey);
        return SqlerTemplate.queryForObject(request, new LocationCheckTaskItemMapper());
    }

    @Override
    public LocationCheckTaskItem queryLocationCheckTaskItem(LocationCheckTaskItemQueryCondition query) {
        SqlerRequest request = new SqlerRequest("queryLocationCheckTaskItem");
        setQueryCondition(request, query);
        return SqlerTemplate.queryForObject(request, new LocationCheckTaskItemMapper());
    }

    @Override
    public void createLocationCheckTaskItem(LocationCheckTaskItem entity) {
        SqlerRequest request = new SqlerRequest("createLocationCheckTaskItem");
        request.addDataParam(LocationCheckTaskItemDBField.TASK_ID, DataType.INT, entity.getTaskId());
        request.addDataParam(LocationCheckTaskItemDBField.LOCATION_NUMBER, DataType.STRING, entity.getLocationNumber());
        request.addDataParam(LocationCheckTaskItemDBField.SKU, DataType.STRING, entity.getSku());
        request.addDataParam(LocationCheckTaskItemDBField.QUANTITY, DataType.INT, entity.getQuantity());
        request.addDataParam(LocationCheckTaskItemDBField.CREATED_DATE, DataType.TIMESTAMP,
                entity.getCreatedDate() == null ? new Timestamp(System.currentTimeMillis()) : entity.getCreatedDate());
        request.addDataParam(LocationCheckTaskItemDBField.CREATE_BY, DataType.INT,
                entity.getCreateBy() == null ? DataContextHolder.getUserId() : entity.getCreateBy());
        request.addDataParam(LocationCheckTaskItemDBField.STATUS, DataType.INT, entity.getStatus());
        request.addDataParam(LocationCheckTaskItemDBField.CHECK_BY, DataType.INT, entity.getCheckBy());
        request.addDataParam(LocationCheckTaskItemDBField.CHECK_DATE, DataType.TIMESTAMP, entity.getCheckDate());
        Integer autoIncrementId = SqlerTemplate.executeAndReturn(request);
        entity.setId(autoIncrementId);
    }

    @Override
    public void updateLocationCheckTaskItem(LocationCheckTaskItem entity) {
        SqlerRequest request = new SqlerRequest("updateLocationCheckTaskItemByPrimaryKey");
        request.addDataParam(LocationCheckTaskItemDBField.ID, DataType.INT, entity.getId());

        request.addDataParam(LocationCheckTaskItemDBField.TASK_ID, DataType.INT, entity.getTaskId());
        request.addDataParam(LocationCheckTaskItemDBField.LOCATION_NUMBER, DataType.STRING, entity.getLocationNumber());
        request.addDataParam(LocationCheckTaskItemDBField.SKU, DataType.STRING, entity.getSku());
        request.addDataParam(LocationCheckTaskItemDBField.QUANTITY, DataType.INT, entity.getQuantity());
        request.addDataParam(LocationCheckTaskItemDBField.CREATED_DATE, DataType.TIMESTAMP, entity.getCreatedDate());
        request.addDataParam(LocationCheckTaskItemDBField.CREATE_BY, DataType.INT, entity.getCreateBy());
        request.addDataParam(LocationCheckTaskItemDBField.STATUS, DataType.INT, entity.getStatus());
        request.addDataParam(LocationCheckTaskItemDBField.CHECK_BY, DataType.INT, entity.getCheckBy());
        request.addDataParam(LocationCheckTaskItemDBField.CHECK_DATE, DataType.TIMESTAMP, entity.getCheckDate());
        SqlerTemplate.execute(request);
    }

    @Override
    public void batchCreateLocationCheckTaskItem(List<LocationCheckTaskItem> entityList) {
        if (entityList != null && !entityList.isEmpty()) {
            SqlerRequest request = new SqlerRequest("createLocationCheckTaskItem");
            for (LocationCheckTaskItem entity : entityList) {
                request.addBatchDataParam(LocationCheckTaskItemDBField.TASK_ID, DataType.INT, entity.getTaskId());
                request.addBatchDataParam(LocationCheckTaskItemDBField.LOCATION_NUMBER, DataType.STRING,
                        entity.getLocationNumber());
                request.addBatchDataParam(LocationCheckTaskItemDBField.SKU, DataType.STRING, entity.getSku());
                request.addBatchDataParam(LocationCheckTaskItemDBField.QUANTITY, DataType.INT, entity.getQuantity());
                request.addBatchDataParam(LocationCheckTaskItemDBField.CREATED_DATE, DataType.TIMESTAMP,
                        entity.getCreatedDate() == null ? new Timestamp(System.currentTimeMillis())
                                : entity.getCreatedDate());
                request.addBatchDataParam(LocationCheckTaskItemDBField.CREATE_BY, DataType.INT,
                        entity.getCreateBy() == null ? DataContextHolder.getUserId() : entity.getCreateBy());
                request.addBatchDataParam(LocationCheckTaskItemDBField.STATUS, DataType.INT, entity.getStatus());
                request.addBatchDataParam(LocationCheckTaskItemDBField.CHECK_BY, DataType.INT, entity.getCheckBy());
                request.addBatchDataParam(LocationCheckTaskItemDBField.CHECK_DATE, DataType.TIMESTAMP,
                        entity.getCheckDate());
                request.addBatch();
            }
            SqlerTemplate.batchUpdate(request);
        }
    }

    @Override
    public void batchUpdateLocationCheckTaskItem(List<LocationCheckTaskItem> entityList) {
        if (entityList != null && !entityList.isEmpty()) {
            SqlerRequest request = new SqlerRequest("updateLocationCheckTaskItemByPrimaryKey");
            for (LocationCheckTaskItem entity : entityList) {
                request.addBatchDataParam(LocationCheckTaskItemDBField.ID, DataType.INT, entity.getId());

                request.addBatchDataParam(LocationCheckTaskItemDBField.TASK_ID, DataType.INT, entity.getTaskId());
                request.addBatchDataParam(LocationCheckTaskItemDBField.LOCATION_NUMBER, DataType.STRING,
                        entity.getLocationNumber());
                request.addBatchDataParam(LocationCheckTaskItemDBField.SKU, DataType.STRING, entity.getSku());
                request.addBatchDataParam(LocationCheckTaskItemDBField.QUANTITY, DataType.INT, entity.getQuantity());
                request.addBatchDataParam(LocationCheckTaskItemDBField.CREATED_DATE, DataType.TIMESTAMP,
                        entity.getCreatedDate());
                request.addBatchDataParam(LocationCheckTaskItemDBField.CREATE_BY, DataType.INT, entity.getCreateBy());
                request.addBatchDataParam(LocationCheckTaskItemDBField.STATUS, DataType.INT, entity.getStatus());
                request.addBatchDataParam(LocationCheckTaskItemDBField.CHECK_BY, DataType.INT, entity.getCheckBy());
                request.addBatchDataParam(LocationCheckTaskItemDBField.CHECK_DATE, DataType.TIMESTAMP,
                        entity.getCheckDate());
                request.addBatch();
            }
            SqlerTemplate.batchUpdate(request);
        }
    }

    @Override
    public void deleteLocationCheckTaskItem(Integer primaryKey) {
        SqlerRequest request = new SqlerRequest("deleteLocationCheckTaskItemByPrimaryKey");
        request.addDataParam(LocationCheckTaskItemDBField.ID, DataType.INT, primaryKey);
        SqlerTemplate.execute(request);
    }
}