package com.estone.picking.dao.impl;

import java.sql.Timestamp;
import java.util.List;

import org.springframework.stereotype.Repository;
import org.springframework.util.Assert;

import com.estone.common.util.SqlerTemplate;
import com.estone.picking.bean.CePhotoTaskItem;
import com.estone.picking.bean.CePhotoTaskItemQueryCondition;
import com.estone.picking.dao.CePhotoTaskItemDao;
import com.estone.picking.dao.mapper.CePhotoTaskItemDBField;
import com.estone.picking.dao.mapper.CePhotoTaskItemMapper;
import com.whq.tool.component.Pager;
import com.whq.tool.sqler.SqlerRequest;
import com.whq.tool.sqler.analyzer.DataType;

@Repository("cePhotoTaskItemDao")
public class CePhotoTaskItemDaoImpl implements CePhotoTaskItemDao {

    private void setQueryCondition(SqlerRequest request, CePhotoTaskItemQueryCondition query) {
        if (query == null) {
            return;
        }
        // 添加只读权限
        if (query.getReadOnly() != null && query.getReadOnly())
        {
            request.setReadOnly(true);
        }
        // 添加查询条件
        // 搜索条件不允许出现空字符
        request.setAllowBlank(false);
        
        request.addDataParam(CePhotoTaskItemDBField.ID, DataType.INT, query.getId());
        request.addDataParam(CePhotoTaskItemDBField.TASK_ID, DataType.INT, query.getTaskId());
        request.addDataParam(CePhotoTaskItemDBField.SKU, DataType.STRING, query.getSku());
        request.addDataParam(CePhotoTaskItemDBField.STATUS, DataType.INT, query.getStatus());
    }

    @Override
    public int queryCePhotoTaskItemCount(CePhotoTaskItemQueryCondition query) {
        SqlerRequest request = new SqlerRequest("queryCePhotoTaskItemCount");
        setQueryCondition(request, query);
        return SqlerTemplate.queryForInt(request);
    }

    @Override
    public List<CePhotoTaskItem> queryCePhotoTaskItemList() {
        SqlerRequest request = new SqlerRequest("queryCePhotoTaskItemList");
        return SqlerTemplate.query(request, new CePhotoTaskItemMapper());
    }

    @Override
    public List<CePhotoTaskItem> queryCePhotoTaskItemList(CePhotoTaskItemQueryCondition query, Pager pager) {
        SqlerRequest request = new SqlerRequest("queryCePhotoTaskItemList");
        setQueryCondition(request, query);
        if(pager != null) {
            request.addFetch(pager.getPageNo(), pager.getPageSize());
        }
        return SqlerTemplate.query(request, new CePhotoTaskItemMapper());
    }

    @Override
    public CePhotoTaskItem queryCePhotoTaskItem(Integer primaryKey) {
        Assert.notNull(primaryKey, "primaryKey is null!");
        SqlerRequest request = new SqlerRequest("queryCePhotoTaskItemByPrimaryKey");
        request.addDataParam(CePhotoTaskItemDBField.ID, DataType.INT, primaryKey);
        return SqlerTemplate.queryForObject(request, new CePhotoTaskItemMapper());
    }

    @Override
    public CePhotoTaskItem queryCePhotoTaskItem(CePhotoTaskItemQueryCondition query) {
        SqlerRequest request = new SqlerRequest("queryCePhotoTaskItem");
        setQueryCondition(request, query);
        return SqlerTemplate.queryForObject(request, new CePhotoTaskItemMapper());
    }

    @Override
    public void createCePhotoTaskItem(CePhotoTaskItem entity) {
        SqlerRequest request = new SqlerRequest("createCePhotoTaskItem");
        request.addDataParam(CePhotoTaskItemDBField.TASK_ID, DataType.INT, entity.getTaskId());
        request.addDataParam(CePhotoTaskItemDBField.SKU, DataType.STRING, entity.getSku());
        request.addDataParam(CePhotoTaskItemDBField.NAME, DataType.STRING, entity.getName());
        request.addDataParam(CePhotoTaskItemDBField.LOCATION_NUMBER, DataType.STRING, entity.getLocationNumber());
        request.addDataParam(CePhotoTaskItemDBField.OUTSOURCING_IMAGE, DataType.STRING, entity.getOutsourcingImage());
        request.addDataParam(CePhotoTaskItemDBField.WAREHOUSE_IMAGE, DataType.STRING, entity.getWarehouseImage());
        request.addDataParam(CePhotoTaskItemDBField.LAST_UPDATE_BY, DataType.INT, entity.getLastUpdateBy());
        request.addDataParam(CePhotoTaskItemDBField.LAST_UPDATE_DATE, DataType.TIMESTAMP, new Timestamp(System.currentTimeMillis()));
        request.addDataParam(CePhotoTaskItemDBField.STATUS, DataType.INT, entity.getStatus());
        request.addDataParam(CePhotoTaskItemDBField.CE_ID, DataType.INT, entity.getCeId());
        Integer autoIncrementId = SqlerTemplate.executeAndReturn(request);
        entity.setId(autoIncrementId);
    }

    @Override
    public void updateCePhotoTaskItem(CePhotoTaskItem entity) {
        SqlerRequest request = new SqlerRequest("updateCePhotoTaskItemByPrimaryKey");
        request.addDataParam(CePhotoTaskItemDBField.ID, DataType.INT, entity.getId());
        
        request.addDataParam(CePhotoTaskItemDBField.TASK_ID, DataType.INT, entity.getTaskId());
        request.addDataParam(CePhotoTaskItemDBField.SKU, DataType.STRING, entity.getSku());
        request.addDataParam(CePhotoTaskItemDBField.NAME, DataType.STRING, entity.getName());
        request.addDataParam(CePhotoTaskItemDBField.LOCATION_NUMBER, DataType.STRING, entity.getLocationNumber());
        request.addDataParam(CePhotoTaskItemDBField.OUTSOURCING_IMAGE, DataType.STRING, entity.getOutsourcingImage());
        request.addDataParam(CePhotoTaskItemDBField.WAREHOUSE_IMAGE, DataType.STRING, entity.getWarehouseImage());
        request.addDataParam(CePhotoTaskItemDBField.LAST_UPDATE_BY, DataType.INT, entity.getLastUpdateBy());
        request.addDataParam(CePhotoTaskItemDBField.LAST_UPDATE_DATE, DataType.TIMESTAMP, new Timestamp(System.currentTimeMillis()));
        request.addDataParam(CePhotoTaskItemDBField.STATUS, DataType.INT, entity.getStatus());
        SqlerTemplate.execute(request);
    }

    @Override
    public void batchCreateCePhotoTaskItem(List<CePhotoTaskItem> entityList) {
        if (entityList != null && !entityList.isEmpty()) {
            SqlerRequest request = new SqlerRequest("createCePhotoTaskItem");
            for (CePhotoTaskItem entity : entityList) {
                request.addBatchDataParam(CePhotoTaskItemDBField.TASK_ID, DataType.INT, entity.getTaskId());
                request.addBatchDataParam(CePhotoTaskItemDBField.SKU, DataType.STRING, entity.getSku());
                request.addBatchDataParam(CePhotoTaskItemDBField.NAME, DataType.STRING, entity.getName());
                request.addBatchDataParam(CePhotoTaskItemDBField.LOCATION_NUMBER, DataType.STRING, entity.getLocationNumber());
                request.addBatchDataParam(CePhotoTaskItemDBField.OUTSOURCING_IMAGE, DataType.STRING, entity.getOutsourcingImage());
                request.addBatchDataParam(CePhotoTaskItemDBField.WAREHOUSE_IMAGE, DataType.STRING, entity.getWarehouseImage());
                request.addBatchDataParam(CePhotoTaskItemDBField.LAST_UPDATE_BY, DataType.INT, entity.getLastUpdateBy());
                request.addBatchDataParam(CePhotoTaskItemDBField.LAST_UPDATE_DATE, DataType.TIMESTAMP, new Timestamp(System.currentTimeMillis()));
                request.addBatchDataParam(CePhotoTaskItemDBField.STATUS, DataType.INT, entity.getStatus());
                request.addBatchDataParam(CePhotoTaskItemDBField.CE_ID, DataType.INT, entity.getCeId());
                request.addBatch();
            }
            SqlerTemplate.batchUpdate(request);
        }
    }

    @Override
    public void batchUpdateCePhotoTaskItem(List<CePhotoTaskItem> entityList) {
        if (entityList != null && !entityList.isEmpty()) {
            SqlerRequest request = new SqlerRequest("updateCePhotoTaskItemByPrimaryKey");
            for (CePhotoTaskItem entity : entityList) {
                request.addBatchDataParam(CePhotoTaskItemDBField.ID, DataType.INT, entity.getId());
                
                request.addBatchDataParam(CePhotoTaskItemDBField.TASK_ID, DataType.INT, entity.getTaskId());
                request.addBatchDataParam(CePhotoTaskItemDBField.SKU, DataType.STRING, entity.getSku());
                request.addBatchDataParam(CePhotoTaskItemDBField.NAME, DataType.STRING, entity.getName());
                request.addBatchDataParam(CePhotoTaskItemDBField.LOCATION_NUMBER, DataType.STRING, entity.getLocationNumber());
                request.addBatchDataParam(CePhotoTaskItemDBField.OUTSOURCING_IMAGE, DataType.STRING, entity.getOutsourcingImage());
                request.addBatchDataParam(CePhotoTaskItemDBField.WAREHOUSE_IMAGE, DataType.STRING, entity.getWarehouseImage());
                request.addBatchDataParam(CePhotoTaskItemDBField.LAST_UPDATE_BY, DataType.INT, entity.getLastUpdateBy());
                request.addBatchDataParam(CePhotoTaskItemDBField.LAST_UPDATE_DATE, DataType.TIMESTAMP, new Timestamp(System.currentTimeMillis()));
                request.addBatchDataParam(CePhotoTaskItemDBField.STATUS, DataType.INT, entity.getStatus());
                request.addBatch();
            }
            SqlerTemplate.batchUpdate(request);
        }
    }

    @Override
    public void deleteCePhotoTaskItem(Integer primaryKey) {
        SqlerRequest request = new SqlerRequest("deleteCePhotoTaskItemByPrimaryKey");
        request.addDataParam(CePhotoTaskItemDBField.ID, DataType.INT, primaryKey);
        SqlerTemplate.execute(request);
    }
}