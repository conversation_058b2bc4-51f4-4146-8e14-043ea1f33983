package com.estone.picking.dao.impl;

import com.estone.picking.bean.WhReplenishmentPickingItem;
import com.estone.picking.bean.WhReplenishmentPickingItemQueryCondition;
import com.estone.picking.dao.WhReplenishmentPickingItemDao;
import com.estone.picking.dao.mapper.WhReplenishmentPickingItemAndSkuMapper;
import com.estone.picking.dao.mapper.WhReplenishmentPickingItemDBField;
import com.estone.picking.dao.mapper.WhReplenishmentPickingItemMapper;
import com.estone.picking.enums.PickingTaskType;
import com.whq.tool.component.Pager;
import com.whq.tool.context.DataContextHolder;
import com.whq.tool.sqler.SqlerRequest;
import com.whq.tool.sqler.analyzer.DataType;
import com.estone.common.util.SqlerTemplate;
import java.sql.Timestamp;
import java.util.List;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Repository;
import org.springframework.util.Assert;

@Repository("whReplenishmentPickingItemDao")
public class WhReplenishmentPickingItemDaoImpl implements WhReplenishmentPickingItemDao {

    private void setQueryCondition(SqlerRequest request, WhReplenishmentPickingItemQueryCondition query) {
        if (query == null) {
            return;
        }
        // 添加只读权限
        if (query.getReadOnly() != null && query.getReadOnly())
        {
            request.setReadOnly(true);
        }
        // 添加查询条件
        // 搜索条件不允许出现空字符
        request.setAllowBlank(false);
        
        request.addDataParam(WhReplenishmentPickingItemDBField.ID, DataType.INT, query.getId());
        request.addDataParam(WhReplenishmentPickingItemDBField.TASK_ID, DataType.INT, query.getTaskId());
        request.addDataParam(WhReplenishmentPickingItemDBField.APV_ID, DataType.INT, query.getApvId());
        request.addDataParam(WhReplenishmentPickingItemDBField.APV_NO, DataType.STRING, query.getApvNo());
        request.addDataParam(WhReplenishmentPickingItemDBField.CREATE_BY, DataType.INT, query.getCreateBy());
        if(CollectionUtils.isNotEmpty(query.getApvIds())){
            request.addDataParam("apv_id_list", DataType.INT, query.getApvIds());
        }
        if (CollectionUtils.isNotEmpty(query.getTaskStatusList())) {
            request.addDataParam("task_status_list", DataType.INT, query.getTaskStatusList());
        }
    }

    @Override
    public int queryWhReplenishmentPickingItemCount(WhReplenishmentPickingItemQueryCondition query) {
        SqlerRequest request = new SqlerRequest("queryWhReplenishmentPickingItemCount");
        setQueryCondition(request, query);
        return SqlerTemplate.queryForInt(request);
    }

    @Override
    public List<WhReplenishmentPickingItem> queryWhReplenishmentPickingItemsAndSkus(WhReplenishmentPickingItemQueryCondition query, Pager pager) {
        SqlerRequest request = new SqlerRequest("queryReplenishmentTaskItemAndSkus");
        if ((query.getTaskType() != null && PickingTaskType.getTransferIntCode().contains(query.getTaskType()))
                || (query.getIsAsn() != null && PickingTaskType.getTransferIntCode().contains(query.getIsAsn()))) {
            request = new SqlerRequest("queryReplenishmentTaskItemAndSkusFBA");
        }
        setQueryCondition(request, query);
        request.addDataParam("sku", DataType.STRING, query.getSku());
        if (pager != null) {
            request.addFetch(pager.getPageNo(), pager.getPageSize());
        }
        return SqlerTemplate.query(request, new WhReplenishmentPickingItemAndSkuMapper(query));
    }

    @Override
    public List<WhReplenishmentPickingItem> queryWhReplenishmentPickingItemList(WhReplenishmentPickingItemQueryCondition query, Pager pager) {
        SqlerRequest request = new SqlerRequest("queryWhReplenishmentPickingItemList");
        setQueryCondition(request, query);
        if(pager != null) {
            request.addFetch(pager.getPageNo(), pager.getPageSize());
        }
        return SqlerTemplate.query(request, new WhReplenishmentPickingItemMapper());
    }

    @Override
    public WhReplenishmentPickingItem queryWhReplenishmentPickingItem(Integer primaryKey) {
        Assert.notNull(primaryKey, "primaryKey is null!");
        SqlerRequest request = new SqlerRequest("queryWhReplenishmentPickingItemByPrimaryKey");
        request.addDataParam(WhReplenishmentPickingItemDBField.ID, DataType.INT, primaryKey);
        return SqlerTemplate.queryForObject(request, new WhReplenishmentPickingItemMapper());
    }

    @Override
    public WhReplenishmentPickingItem queryWhReplenishmentPickingItem(WhReplenishmentPickingItemQueryCondition query) {
        SqlerRequest request = new SqlerRequest("queryWhReplenishmentPickingItem");
        setQueryCondition(request, query);
        return SqlerTemplate.queryForObject(request, new WhReplenishmentPickingItemMapper());
    }

    @Override
    public void createWhReplenishmentPickingItem(WhReplenishmentPickingItem entity) {
        SqlerRequest request = new SqlerRequest("createWhReplenishmentPickingItem");
        request.addDataParam(WhReplenishmentPickingItemDBField.TASK_ID, DataType.INT, entity.getTaskId());
        request.addDataParam(WhReplenishmentPickingItemDBField.APV_ID, DataType.INT, entity.getApvId());
        request.addDataParam(WhReplenishmentPickingItemDBField.APV_NO, DataType.STRING, entity.getApvNo());
        request.addDataParam(WhReplenishmentPickingItemDBField.CREATED_DATE, DataType.TIMESTAMP, entity.getCreatedDate());
        request.addDataParam(WhReplenishmentPickingItemDBField.CREATE_BY, DataType.INT, entity.getCreateBy());
        request.addDataParam(WhReplenishmentPickingItemDBField.STATUS, DataType.INT, entity.getStatus());
        Integer autoIncrementId = SqlerTemplate.executeAndReturn(request);
        entity.setId(autoIncrementId);
    }

    @Override
    public void updateWhReplenishmentPickingItem(WhReplenishmentPickingItem entity) {
        SqlerRequest request = new SqlerRequest("updateWhReplenishmentPickingItemByPrimaryKey");
        request.addDataParam(WhReplenishmentPickingItemDBField.ID, DataType.INT, entity.getId());
        
        request.addDataParam(WhReplenishmentPickingItemDBField.TASK_ID, DataType.INT, entity.getTaskId());
        request.addDataParam(WhReplenishmentPickingItemDBField.APV_ID, DataType.INT, entity.getApvId());
        request.addDataParam(WhReplenishmentPickingItemDBField.APV_NO, DataType.STRING, entity.getApvNo());
        request.addDataParam(WhReplenishmentPickingItemDBField.CREATED_DATE, DataType.TIMESTAMP, entity.getCreatedDate());
        request.addDataParam(WhReplenishmentPickingItemDBField.CREATE_BY, DataType.INT, entity.getCreateBy());
        request.addDataParam(WhReplenishmentPickingItemDBField.STATUS, DataType.INT, entity.getStatus());
        SqlerTemplate.execute(request);
    }

    @Override
    public void batchCreateWhReplenishmentPickingItem(List<WhReplenishmentPickingItem> entityList) {
        if (entityList != null && !entityList.isEmpty()) {
            SqlerRequest request = new SqlerRequest("createWhReplenishmentPickingItem");
            for (WhReplenishmentPickingItem entity : entityList) {
                request.addBatchDataParam(WhReplenishmentPickingItemDBField.TASK_ID, DataType.INT, entity.getTaskId());
                request.addBatchDataParam(WhReplenishmentPickingItemDBField.APV_ID, DataType.INT, entity.getApvId());
                request.addBatchDataParam(WhReplenishmentPickingItemDBField.APV_NO, DataType.STRING, entity.getApvNo());
                request.addBatchDataParam(WhReplenishmentPickingItemDBField.CREATED_DATE, DataType.TIMESTAMP, entity.getCreatedDate());
                request.addBatchDataParam(WhReplenishmentPickingItemDBField.CREATE_BY, DataType.INT, entity.getCreateBy());
                request.addBatchDataParam(WhReplenishmentPickingItemDBField.STATUS, DataType.INT, entity.getStatus());
                request.addBatch();
            }
            SqlerTemplate.batchUpdate(request);
        }
    }

    @Override
    public void batchUpdateWhReplenishmentPickingItem(List<WhReplenishmentPickingItem> entityList) {
        if (entityList != null && !entityList.isEmpty()) {
            SqlerRequest request = new SqlerRequest("updateWhReplenishmentPickingItemByPrimaryKey");
            for (WhReplenishmentPickingItem entity : entityList) {
                request.addBatchDataParam(WhReplenishmentPickingItemDBField.ID, DataType.INT, entity.getId());
                
                request.addBatchDataParam(WhReplenishmentPickingItemDBField.TASK_ID, DataType.INT, entity.getTaskId());
                request.addBatchDataParam(WhReplenishmentPickingItemDBField.APV_ID, DataType.INT, entity.getApvId());
                request.addBatchDataParam(WhReplenishmentPickingItemDBField.APV_NO, DataType.STRING, entity.getApvNo());
                request.addBatchDataParam(WhReplenishmentPickingItemDBField.CREATED_DATE, DataType.TIMESTAMP, entity.getCreatedDate());
                request.addBatchDataParam(WhReplenishmentPickingItemDBField.CREATE_BY, DataType.INT, entity.getCreateBy());
                request.addBatchDataParam(WhReplenishmentPickingItemDBField.STATUS, DataType.INT, entity.getStatus());
                request.addBatch();
            }
            SqlerTemplate.batchUpdate(request);
        }
    }

    @Override
    public void deleteWhReplenishmentPickingItem(Integer primaryKey) {
        SqlerRequest request = new SqlerRequest("deleteWhReplenishmentPickingItemByPrimaryKey");
        request.addDataParam(WhReplenishmentPickingItemDBField.ID, DataType.INT, primaryKey);
        SqlerTemplate.execute(request);
    }
}