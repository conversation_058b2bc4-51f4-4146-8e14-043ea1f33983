package com.estone.picking.dao;

import com.estone.picking.bean.MeagerApvLogisticsConfig;
import com.estone.picking.bean.MeagerApvLogisticsConfigQueryCondition;
import com.whq.tool.component.Pager;
import java.util.List;

public interface MeagerApvLogisticsConfigDao {
    int queryMeagerApvLogisticsConfigCount(MeagerApvLogisticsConfigQueryCondition query);

    List<MeagerApvLogisticsConfig> queryMeagerApvLogisticsConfigList();

    List<MeagerApvLogisticsConfig> queryMeagerApvLogisticsConfigList(MeagerApvLogisticsConfigQueryCondition query, Pager pager);

    MeagerApvLogisticsConfig queryMeagerApvLogisticsConfig(Integer primaryKey);

    MeagerApvLogisticsConfig queryMeagerApvLogisticsConfig(MeagerApvLogisticsConfigQueryCondition query);

    void createMeagerApvLogisticsConfig(MeagerApvLogisticsConfig entity);

    void batchCreateMeagerApvLogisticsConfig(List<MeagerApvLogisticsConfig> entityList);

    void batchUpdateMeagerApvLogisticsConfig(List<MeagerApvLogisticsConfig> entityList);

    void deleteMeagerApvLogisticsConfig(Integer primaryKey);

    void updateMeagerApvLogisticsConfig(MeagerApvLogisticsConfig entity);
}