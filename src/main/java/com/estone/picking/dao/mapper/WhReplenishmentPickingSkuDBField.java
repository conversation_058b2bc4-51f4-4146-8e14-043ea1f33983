package com.estone.picking.dao.mapper;

public interface WhReplenishmentPickingSkuDBField {
    String ID = "id";

    String TASK_ID = "task_id";

    String SKU = "sku";

    String SKU_NAME = "sku_name";

    String LOCATION = "location";

    String QUANTITY = "quantity";

    String PICK_QUANTITY = "pick_quantity";

    String STATUS = "status";

    String CREATION_DATE = "creation_date";

    String LAST_UPDATE_DATE = "last_update_date";

    String REMARK = "remark";

    String STOCK_ID = "stock_id";
}