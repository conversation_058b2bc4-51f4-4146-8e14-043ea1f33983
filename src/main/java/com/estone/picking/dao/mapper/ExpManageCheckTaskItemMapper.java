package com.estone.picking.dao.mapper;

import com.estone.picking.bean.ExpManageCheckTaskItem;
import java.sql.ResultSet;
import java.sql.SQLException;
import org.springframework.jdbc.core.RowMapper;

public class ExpManageCheckTaskItemMapper implements <PERSON><PERSON>apper<ExpManageCheckTaskItem> {

    public ExpManageCheckTaskItem mapRow(ResultSet rs, int rowNum) throws SQLException {
        ExpManageCheckTaskItem entity = new ExpManageCheckTaskItem();
        entity.setId(rs.getObject(ExpManageCheckTaskItemDBField.ID) == null ? null : rs.getInt(ExpManageCheckTaskItemDBField.ID));
        entity.setRelationId(rs.getObject(ExpManageCheckTaskItemDBField.RELATION_ID) == null ? null : rs.getInt(ExpManageCheckTaskItemDBField.RELATION_ID));
        entity.setBatchNo(rs.getString(ExpManageCheckTaskItemDBField.BATCH_NO));
        entity.setQuantity(rs.getObject(ExpManageCheckTaskItemDBField.QUANTITY) == null ? null : rs.getInt(ExpManageCheckTaskItemDBField.QUANTITY));
        entity.setCheckQuantity(rs.getObject(ExpManageCheckTaskItemDBField.CHECK_QUANTITY) == null ? null : rs.getInt(ExpManageCheckTaskItemDBField.CHECK_QUANTITY));
        return entity;
    }
}