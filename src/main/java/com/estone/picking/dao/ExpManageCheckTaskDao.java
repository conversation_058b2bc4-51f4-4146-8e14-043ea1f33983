package com.estone.picking.dao;

import com.estone.picking.bean.ExpManageCheckTask;
import com.estone.picking.bean.ExpManageCheckTaskQueryCondition;
import com.whq.tool.component.Pager;
import java.util.List;

public interface ExpManageCheckTaskDao {
    int queryExpManageCheckTaskCount(ExpManageCheckTaskQueryCondition query);

    List<ExpManageCheckTask> queryExpManageCheckTaskList();

    List<ExpManageCheckTask> queryExpManageCheckTaskList(ExpManageCheckTaskQueryCondition query, Pager pager);

    ExpManageCheckTask queryExpManageCheckTask(Integer primaryKey);

    ExpManageCheckTask queryExpManageCheckTask(ExpManageCheckTaskQueryCondition query);

    void createExpManageCheckTask(ExpManageCheckTask entity);

    void batchCreateExpManageCheckTask(List<ExpManageCheckTask> entityList);

    void batchUpdateExpManageCheckTask(List<ExpManageCheckTask> entityList);

    void deleteExpManageCheckTask(Integer primaryKey);

    void updateExpManageCheckTask(ExpManageCheckTask entity);
}