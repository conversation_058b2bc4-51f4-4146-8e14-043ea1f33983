package com.estone.picking.service;

import java.util.List;

import com.estone.allocation.bean.WhApvAllocationPickTask;
import com.estone.picking.bean.WhAllocationPickTaskItem;
import com.estone.picking.bean.WhAllocationPickTaskItemQueryCondition;
import com.whq.tool.component.Pager;
import com.whq.tool.json.ResponseJson;

public interface WhAllocationPickTaskItemService {
    /**
     * This method corresponds to the database table
     * wh_allocation_pick_task_item
     *
     * @mbggenerated Thu Mar 14 14:57:56 CST 2019
     */
    List<WhAllocationPickTaskItem> queryAllWhAllocationPickTaskItems();

    /**
     * This method corresponds to the database table
     * wh_allocation_pick_task_item
     *
     * @mbggenerated Thu Mar 14 14:57:56 CST 2019
     */
    List<WhAllocationPickTaskItem> queryWhAllocationPickTaskItems(WhAllocationPickTaskItemQueryCondition query,
            Pager pager);

    /**
     * This method corresponds to the database table
     * wh_allocation_pick_task_item
     *
     * @mbggenerated Thu Mar 14 14:57:56 CST 2019
     */
    WhAllocationPickTaskItem getWhAllocationPickTaskItem(Integer id);

    /**
     * This method corresponds to the database table
     * wh_allocation_pick_task_item
     *
     * @mbggenerated Thu Mar 14 14:57:56 CST 2019
     */
    WhAllocationPickTaskItem getWhAllocationPickTaskItemDetail(Integer id);

    /**
     * This method corresponds to the database table
     * wh_allocation_pick_task_item
     *
     * @mbggenerated Thu Mar 14 14:57:56 CST 2019
     */
    WhAllocationPickTaskItem queryWhAllocationPickTaskItem(WhAllocationPickTaskItemQueryCondition query);

    /**
     * This method corresponds to the database table
     * wh_allocation_pick_task_item
     *
     * @mbggenerated Thu Mar 14 14:57:56 CST 2019
     */
    void createWhAllocationPickTaskItem(WhAllocationPickTaskItem whAllocationPickTaskItem);

    /**
     * This method corresponds to the database table
     * wh_allocation_pick_task_item
     *
     * @mbggenerated Thu Mar 14 14:57:56 CST 2019
     */
    void batchCreateWhAllocationPickTaskItem(List<WhAllocationPickTaskItem> entityList);

    /**
     * This method corresponds to the database table
     * wh_allocation_pick_task_item
     *
     * @mbggenerated Thu Mar 14 14:57:56 CST 2019
     */
    void deleteWhAllocationPickTaskItem(Integer id);

    /**
     * This method corresponds to the database table
     * wh_allocation_pick_task_item
     *
     * @mbggenerated Thu Mar 14 14:57:56 CST 2019
     */
    void updateWhAllocationPickTaskItem(WhAllocationPickTaskItem whAllocationPickTaskItem);

    /**
     * This method corresponds to the database table
     * wh_allocation_pick_task_item
     *
     * @mbggenerated Thu Mar 14 14:57:56 CST 2019
     */
    void batchUpdateWhAllocationPickTaskItem(List<WhAllocationPickTaskItem> entityList);

    /**
     * 
     * @Description: 调拨拣货详情页面和对应的调拨拣货主表和调拨单表数据
     *
     * @param query
     * @param pager
     * @return
     * @return: List<WhAllocationPickTaskItem>
     * @Author: qinyangkai
     * @Date: 2019/03/26
     * @Version: 0.0.1
     */
    List<WhAllocationPickTaskItem> queryWhAllocationPickTaskItemAndTasks(WhAllocationPickTaskItemQueryCondition query,
            Pager pager);

    // 调拨拣货下一步接口，修改详情拣货状态为拣货完成和已拣数量
    ResponseJson updatePickTaskItemStatusAndPickQuantity(WhApvAllocationPickTask whApvAllocationPickTask);
}