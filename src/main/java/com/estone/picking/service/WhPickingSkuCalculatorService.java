package com.estone.picking.service;

import com.estone.picking.bean.WhPickingSkuCalculator;
import com.estone.picking.bean.WhPickingSkuCalculatorQueryCondition;
import com.whq.tool.component.Pager;
import java.util.List;

public interface WhPickingSkuCalculatorService {
    /**
     * This method corresponds to the database table wh_picking_sku_calculator
     *
     * @mbggenerated Thu May 30 17:58:39 CST 2019
     */
    List<WhPickingSkuCalculator> queryAllWhPickingSkuCalculators();

    /**
     * This method corresponds to the database table wh_picking_sku_calculator
     *
     * @mbggenerated Thu May 30 17:58:39 CST 2019
     */
    List<WhPickingSkuCalculator> queryWhPickingSkuCalculators(WhPickingSkuCalculatorQueryCondition query, Pager pager);

    /**
     * This method corresponds to the database table wh_picking_sku_calculator
     *
     * @mbggenerated Thu May 30 17:58:39 CST 2019
     */
    WhPickingSkuCalculator getWhPickingSkuCalculator(Integer id);

    /**
     * This method corresponds to the database table wh_picking_sku_calculator
     *
     * @mbggenerated Thu May 30 17:58:39 CST 2019
     */
    WhPickingSkuCalculator getWhPickingSkuCalculatorDetail(Integer id);

    /**
     * This method corresponds to the database table wh_picking_sku_calculator
     *
     * @mbggenerated Thu May 30 17:58:39 CST 2019
     */
    WhPickingSkuCalculator queryWhPickingSkuCalculator(WhPickingSkuCalculatorQueryCondition query);

    /**
     * This method corresponds to the database table wh_picking_sku_calculator
     *
     * @mbggenerated Thu May 30 17:58:39 CST 2019
     */
    void createWhPickingSkuCalculator(WhPickingSkuCalculator whPickingSkuCalculator);

    /**
     * This method corresponds to the database table wh_picking_sku_calculator
     *
     * @mbggenerated Thu May 30 17:58:39 CST 2019
     */
    void batchCreateWhPickingSkuCalculator(List<WhPickingSkuCalculator> entityList);

    /**
     * This method corresponds to the database table wh_picking_sku_calculator
     *
     * @mbggenerated Thu May 30 17:58:39 CST 2019
     */
    void deleteWhPickingSkuCalculator(Integer id);

    /**
     * This method corresponds to the database table wh_picking_sku_calculator
     *
     * @mbggenerated Thu May 30 17:58:39 CST 2019
     */
    void updateWhPickingSkuCalculator(WhPickingSkuCalculator whPickingSkuCalculator);

    /**
     * This method corresponds to the database table wh_picking_sku_calculator
     *
     * @mbggenerated Thu May 30 17:58:39 CST 2019
     */
    void batchUpdateWhPickingSkuCalculator(List<WhPickingSkuCalculator> entityList);
    
    void batchUpdateCalculator(List<WhPickingSkuCalculator> entityList);
}