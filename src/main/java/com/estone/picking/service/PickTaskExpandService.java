package com.estone.picking.service;

import com.estone.picking.bean.PickTaskExpand;
import com.estone.picking.bean.PickTaskExpandQueryCondition;
import com.estone.picking.bean.WhPickingTask;
import com.whq.tool.component.Pager;
import java.util.List;

public interface PickTaskExpandService {
    List<PickTaskExpand> queryAllPickTaskExpands();

    List<PickTaskExpand> queryPickTaskExpands(PickTaskExpandQueryCondition query, Pager pager);

    PickTaskExpand getPickTaskExpand(Integer id);

    PickTaskExpand getPickTaskExpandDetail(Integer id);

    PickTaskExpand queryPickTaskExpand(PickTaskExpandQueryCondition query);

    void createPickTaskExpand(PickTaskExpand pickTaskExpand);

    void batchCreatePickTaskExpand(List<PickTaskExpand> entityList);

    void deletePickTaskExpand(Integer id);

    void updatePickTaskExpand(PickTaskExpand pickTaskExpand);

    void batchUpdatePickTaskExpand(List<PickTaskExpand> entityList);

    /**
     * 更新任务播种状态
     * @param taskNo
     * @param status
     * @return
     */
    int updatePickTaskExpandStatusByTaskNo(String taskNo, Integer status);

    /**
     * 创建拣货任务播种记录
     * @param pickingTask
     */
    void createPickTaskExpandByTask(WhPickingTask pickingTask);


    void updatePickTaskExpandBoxUnBind(String taskNo);
}