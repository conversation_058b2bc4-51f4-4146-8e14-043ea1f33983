package com.estone.picking.service;

import com.estone.picking.bean.ExpManageCheckTaskSku;
import com.estone.picking.bean.ExpManageCheckTaskSkuQueryCondition;
import com.whq.tool.component.Pager;
import java.util.List;

public interface ExpManageCheckTaskSkuService {
    List<ExpManageCheckTaskSku> queryAllExpManageCheckTaskSkus();

    List<ExpManageCheckTaskSku> queryExpManageCheckTaskSkus(ExpManageCheckTaskSkuQueryCondition query, Pager pager);

    ExpManageCheckTaskSku getExpManageCheckTaskSku(Integer id);

    ExpManageCheckTaskSku getExpManageCheckTaskSkuDetail(Integer id);

    ExpManageCheckTaskSku queryExpManageCheckTaskSku(ExpManageCheckTaskSkuQueryCondition query);

    void createExpManageCheckTaskSku(ExpManageCheckTaskSku expManageCheckTaskSku);

    void batchCreateExpManageCheckTaskSku(List<ExpManageCheckTaskSku> entityList);

    void deleteExpManageCheckTaskSku(Integer id);

    void updateExpManageCheckTaskSku(ExpManageCheckTaskSku expManageCheckTaskSku);

    void batchUpdateExpManageCheckTaskSku(List<ExpManageCheckTaskSku> entityList);
}