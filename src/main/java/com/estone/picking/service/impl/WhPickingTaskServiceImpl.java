package com.estone.picking.service.impl;

import java.sql.Timestamp;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import com.estone.apv.util.ApvPackUtils;
import com.estone.common.util.model.ApiResult;
import com.estone.sowstockout.bean.GridException;
import com.estone.sowstockout.bean.GridExceptionQueryCondition;
import com.estone.sowstockout.enums.GridExceptionStatus;
import com.estone.sowstockout.enums.SowStockoutStatus;
import com.estone.system.param.bean.SystemParam;
import com.estone.sowstockout.service.GridExceptionService;
import com.estone.warehouse.bean.WhBox;
import com.estone.warehouse.bean.WhBoxQueryCondition;
import com.estone.warehouse.enums.BoxStatus;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.estone.android.domain.AndroidProductDo;
import com.estone.apv.bean.*;
import com.estone.apv.common.*;
import com.estone.apv.dao.WhApvBatchDao;
import com.estone.apv.dao.WhApvBatchDetailDao;
import com.estone.apv.dao.WhApvBatchItemDao;
import com.estone.apv.enums.WhApvOutStockChainStatusEnum;
import com.estone.apv.service.*;
import com.estone.apv.util.ApvGridUtils;
import com.estone.common.CacheName;
import com.estone.common.enums.LogModule;
import com.estone.common.executors.ExecutorUtils;
import com.estone.common.util.*;
import com.estone.picking.bean.*;
import com.estone.picking.bo.CheckPickingData;
import com.estone.picking.dao.WhPickingTaskDao;
import com.estone.picking.dao.WhPickingTaskItemDao;
import com.estone.picking.dao.WhPickingTaskSkuDao;
import com.estone.picking.enums.*;
import com.estone.picking.service.*;
import com.estone.picking.utils.PickingTaskSukQuantityCalculator;
import com.estone.picking.utils.PickingTaskUtils;
import com.estone.sku.enums.UniqueSkuStep;
import com.estone.sku.service.WhUniqueSkuService;
import com.estone.sowstockout.bean.SowStockout;
import com.estone.sowstockout.bean.SowStockoutQueryCondition;
import com.estone.sowstockout.service.SowStockoutService;
import com.estone.system.rabbitmq.producer.RabbitmqProducerService;
import com.estone.system.user.bean.SaleUser;
import com.estone.temu.bean.TemuPrepareOrder;
import com.estone.temu.bean.TemuPrepareOrderItem;
import com.estone.temu.enums.TemuPackageStatus;
import com.estone.temu.service.TemuPrepareOrderService;
import com.estone.temu.service.TemuTaskService;
import com.estone.transfer.bean.WhFbaAllocation;
import com.estone.transfer.bean.WhFbaAllocationQueryCondition;
import com.estone.transfer.enums.AsnPrepareStatus;
import com.estone.transfer.service.AsnPrepareGridService;
import com.estone.transfer.service.TransferUpdateStockService;
import com.estone.transfer.service.WhFbaAllocationService;
import com.estone.warehouse.aspect.StockServicelock;
import com.estone.warehouse.bean.InventoryDemandParam;
import com.estone.warehouse.bean.WhSaleStockLog;
import com.estone.warehouse.enums.BoxType;
import com.estone.warehouse.enums.LocationWarehouseType;
import com.estone.warehouse.enums.SaleStockLogType;
import com.estone.warehouse.service.*;
import com.whq.tool.component.Pager;
import com.whq.tool.component.StatusCode;
import com.whq.tool.context.DataContextHolder;
import com.whq.tool.exception.BusinessException;
import com.whq.tool.exception.DBErrorCode;
import com.whq.tool.json.ResponseJson;
import com.whq.tool.sqler.SqlerException;

@Service("whPickingTaskService")
public class WhPickingTaskServiceImpl implements WhPickingTaskService {

    private Logger logger = LoggerFactory.getLogger(WhPickingTaskServiceImpl.class);

    private final static SystemLogUtils PICKINGLOG = SystemLogUtils.create(LogModule.WHPICKING.getCode());

    private final static SystemLogUtils WHAPV = SystemLogUtils.create(LogModule.WHAPV.getCode());

    private final static ExecutorService executors = ExecutorUtils.newFixedThreadPool(5);

    @Resource
    private WhPickingTaskDao whPickingTaskDao;

    @Resource
    private WhPickingTaskItemDao whPickingTaskItemDao;

    @Resource
    private WhPickingTaskSkuDao whPickingTaskSkuDao;

    @Resource
    private WhApvService whApvService;

    @Resource
    private WhApvItemService whApvItemService;

    @Resource
    private WhApvLockService whApvLockService;

    @Resource
    private WhBoxService whBoxService;

    @Resource
    private ApvTrackService apvTrackService;

    @Resource
    private WhPickingSkuCalculatorService whPickingSkuCalculatorService;

    @Resource
    private WhPickInventoryDemandService whPickInventoryDemandService;

    @Resource
    private WhPickingTaskItemService whPickingTaskItemService;

    @Resource
    private WhPickingTaskSkuService whPickingTaskSkuService;

    @Resource
    private ApvPickUpdateStockService apvPickUpdateStockService;

    @Resource
    private WhSaleStockLogService whSaleStockLogService;
    
    @Resource
    private PickTaskExpandService pickTaskExpandService;
    
    @Resource
    private WhApvBatchDao whApvBatchDao;
    
    @Resource
    private WhApvBatchItemDao whApvBatchItemDao;
    
    @Resource
    private WhApvBatchDetailDao whApvBatchDetailDao;
    
    @Resource
    private SowStockoutService sowStockoutService;
    
    @Resource
    private WhFbaAllocationService whFbaAllocationService;

    @Resource
    private WhUniqueSkuService whUniqueSkuService;

    @Resource
    private WhApvGridService apvGridService;

    @Resource
    private AsnPrepareGridService asnPrepareGridService;

    @Resource
    private WhApvOutStockChainService whApvOutStockChainService;

    @Resource
    private TemuPrepareOrderService temuPrepareOrderService;

    @Resource
    private TemuTaskService temuTaskService;

    @Resource
    private GridExceptionService gridExceptionService;

    @Resource
    private WhOrderCancelItemService whOrderCancelItemService;

    @Resource
    private WhApvOutStockChainCancelService whApvOutStockChainCancelService;

    @Resource
    private WhApvOutStockChainService whApvOutStockChinaService;
    @Resource
    private TransferUpdateStockService transferUpdateStockService;


    /**
     * This method corresponds to the database table wh_picking_task
     *
     * @mbggenerated Sat Aug 18 12:37:24 CST 2018
     */
    public WhPickingTask getWhPickingTask(Integer id) {
        WhPickingTask whPickingTask = whPickingTaskDao.queryWhPickingTask(id);
        return whPickingTask;
    }

    /**
     * This method corresponds to the database table wh_picking_task
     *
     * @mbggenerated Sat Aug 18 12:37:24 CST 2018
     */
    public WhPickingTask getWhPickingTaskDetail(Integer id) {
        WhPickingTask whPickingTask = whPickingTaskDao.queryWhPickingTask(id);
        // 关联查询
        return whPickingTask;
    }

    /**
     * This method corresponds to the database table wh_picking_task
     *
     * @mbggenerated Sat Aug 18 12:37:24 CST 2018
     */
    public WhPickingTask queryWhPickingTask(WhPickingTaskQueryCondition query) {
        Assert.notNull(query);
        WhPickingTask whPickingTask = whPickingTaskDao.queryWhPickingTask(query);
        return whPickingTask;
    }

    /**
     * This method corresponds to the database table wh_picking_task
     *
     * @mbggenerated Sat Aug 18 12:37:24 CST 2018
     */
    public List<WhPickingTask> queryAllWhPickingTasks() {
        return whPickingTaskDao.queryWhPickingTaskList();
    }

    /**
     * This method corresponds to the database table wh_picking_task
     *
     * @mbggenerated Sat Aug 18 12:37:24 CST 2018
     */
    public List<WhPickingTask> queryWhPickingTasks(WhPickingTaskQueryCondition query, Pager pager) {
        Assert.notNull(query);
        if (pager != null && pager.isQueryCount()) {
            int count = whPickingTaskDao.queryWhPickingTaskCount(query);
            pager.setTotalCount(count);
            if (count == 0) {
                return new ArrayList<WhPickingTask>();
            }
        }
        // 导出限制
        if (query != null && query.isDownload()) {
            int count = whPickingTaskDao.queryWhPickingTaskCount(query);
            if (count >= 500000) {
                return null;
            }
        }
        List<WhPickingTask> whPickingTasks = whPickingTaskDao.queryWhPickingTaskList(query, pager);
        return whPickingTasks;
    }

    /**
     * This method corresponds to the database table wh_picking_task
     *
     * @mbggenerated Sat Aug 18 12:37:24 CST 2018
     */
    public void createWhPickingTask(WhPickingTask whPickingTask) {
        try {
            WhPickingTaskQueryCondition whPickingTaskQueryCondition = new WhPickingTaskQueryCondition();
            whPickingTaskQueryCondition.setTaskNo(whPickingTask.getTaskNo());
            List<WhPickingTask> whPickingTasks = whPickingTaskDao.queryWhPickingTaskList(whPickingTaskQueryCondition,
                    null);
            if (CollectionUtils.isNotEmpty(whPickingTasks)) {
                whPickingTask.setTaskNo(CreateTaskNoUtils.createTaskNo());
            }

            whPickingTaskDao.createWhPickingTask(whPickingTask);
        } catch (SqlerException e) {
            logger.error(e.getMessage(), e);
            throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
        }
    }

    /**
     * This method corresponds to the database table wh_picking_task
     *
     * @mbggenerated Sat Aug 18 12:37:24 CST 2018
     */
    public void batchCreateWhPickingTask(List<WhPickingTask> entityList) {
        if (CollectionUtils.isNotEmpty(entityList)) {
            try {
                whPickingTaskDao.batchCreateWhPickingTask(entityList);
            } catch (SqlerException e) {
                logger.error(e.getMessage(), e);
                throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
            }
        }
    }

    /**
     * This method corresponds to the database table wh_picking_task
     *
     * @mbggenerated Sat Aug 18 12:37:24 CST 2018
     */
    public void deleteWhPickingTask(Integer id) {
        try {
            whPickingTaskDao.deleteWhPickingTask(id);
        } catch (SqlerException e) {
            logger.error(e.getMessage(), e);
            throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
        }
    }

    /**
     * This method corresponds to the database table wh_picking_task
     *
     * @mbggenerated Sat Aug 18 12:37:24 CST 2018
     */
    public void updateWhPickingTask(WhPickingTask whPickingTask) {
        try {
            whPickingTaskDao.updateWhPickingTask(whPickingTask);
        } catch (SqlerException e) {
            logger.error(e.getMessage(), e);
            throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
        }
    }

    /**
     * This method corresponds to the database table wh_picking_task
     *
     * @mbggenerated Sat Aug 18 12:37:24 CST 2018
     */
    public void batchUpdateWhPickingTask(List<WhPickingTask> entityList) {
        if (CollectionUtils.isNotEmpty(entityList)) {
            try {
                whPickingTaskDao.batchUpdateWhPickingTask(entityList);
            } catch (SqlerException e) {
                logger.error(e.getMessage(), e);
                throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
            }
        }
    }

    /**
     * 领取拣货任务
     */
    public int receivePickingTask(WhPickingTask whPickingTask) {
        int execute = whPickingTaskDao.receivePickingTask(whPickingTask);
        // temu更新单据状态 拣货中
        if (execute > 0 || whPickingTask.getTaskType().equals(PickingTaskType.TEMU_MULTIPLEMULTIPLE.intCode())) {
            List<TemuPrepareOrder> temuPrepareOrders = temuPrepareOrderService
                    .queryOrderByPickingTaskId(whPickingTask.getId());
            if (CollectionUtils.isEmpty(temuPrepareOrders))
                return execute;
            List<TemuPrepareOrderItem> orderItems = temuPrepareOrders.stream().flatMap(t -> t.getItemList().stream())
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(orderItems))
                return execute;
            List<Integer> ids = orderItems.stream()
                    .filter(t -> TemuPackageStatus.MERGED.intCode().equals(t.getPackageStatus()))
                    .map(TemuPrepareOrderItem::getId).collect(Collectors.toList());
            temuTaskService.updatePackageStatus(ids, TemuPackageStatus.PICKING.intCode(),
                    (id) -> SystemLogUtils.TEMU_PACKAGE.log(id, "领取拣货任务",
                            new String[][] { { "详情", "任务ID (" + whPickingTask.getId() + ") " },
                                    { "状态", TemuPackageStatus.PICKING.getName() } }));
        }
        return execute;
    }

    /**
     * 领取并打印拣货任务
     */
    public int receiveAndPrintingPickingTask(WhPickingTask whPickingTask) {
        return whPickingTaskDao.receiveAndPrintingPickingTask(whPickingTask);
    }

    /**
     * @return
     * @Description: 查询任务等级最大值
     * @return: int
     * @Author: qinyangkai
     * @Date: 2018/09/18
     * @Version: 0.0.1
     */
    public int queryWhPickingTaskMax() {
        return whPickingTaskDao.queryWhPickingTaskMax();
    }

    /**
     * 批量置顶（增加任务等级）
     */
    public int setTopList(List<Integer> taskIds, Integer taskLevel) {
        return whPickingTaskDao.setTopList(taskIds, taskLevel);
    }

    public List<WhPickingTaskSku> queryPrintingWhPickingTask(Integer taskId) {
        return whPickingTaskDao.queryPrintingWhPickingTask(taskId);
    }

    public WhPickingTask queryPickingQuantity(String taskNo, Integer taskType) {
        return whPickingTaskDao.queryPickingQuantity(taskNo, taskType);
    }

    public void updateWhApvPickQuantity(List<Integer> apvIds) {
        if (CollectionUtils.isEmpty(apvIds)) {
            return;
        }
        WhApvQueryCondition apvQuery = new WhApvQueryCondition();
        apvQuery.setApvIds(apvIds);
        List<WhApv> apvList = whApvService.queryWhApvAndItemList(apvQuery, null);
        if (CollectionUtils.isNotEmpty(apvList)) {
            List<WhApv> updateApvs = new ArrayList<>();
            List<WhApvItem> updateApvItems = new ArrayList<>();
            for (int i = 0; i < apvList.size(); i++) {
                WhApv apv = apvList.get(i);
                WhApv updateApv = new WhApv();
                updateApv.setId(apv.getId());
                updateApv.setSignDistributionGoods(true);
                updateApvs.add(updateApv);
                List<WhApvItem> apvItems = apv.getWhApvItems();
                for (int j = 0; j < apvItems.size(); j++) {
                    WhApvItem apvItem = apvItems.get(j);
                    WhApvItem updateApvitem = new WhApvItem();
                    updateApvitem.setId(apvItem.getId());
                    updateApvitem.setPickQuantity(apvItem.getSaleQuantity());
                    updateApvItems.add(updateApvitem);
                }
            }
            whApvService.batchUpdateWhApv(updateApvs);
            whApvItemService.batchUpdateWhApvItem(updateApvItems);
        }
    }

    @Override
    public int addApvTrackPickInfo(Integer taskId, String taskNo) {
        WhPickingTaskItemQueryCondition tempQueryObject = new WhPickingTaskItemQueryCondition();
        tempQueryObject.setTaskId(taskId);
        List<WhPickingTaskItem> whPickingTaskItems = whPickingTaskItemDao
                .queryPickingTaskItemAndSkuList(tempQueryObject, null);

        List<ApvTrack> apvTracks = new ArrayList<ApvTrack>();
        for (WhPickingTaskItem item : whPickingTaskItems) {
            ApvTrack apvTrack = new ApvTrack();
            apvTrack.setApvNo(item.getApvNo());
            apvTrack.setTaskNo(taskNo);
            apvTrack.setPickUser(DataContextHolder.getUserId());
            apvTrack.setPickFinishTime(new Timestamp(System.currentTimeMillis()));
            apvTracks.add(apvTrack);
        }
        // 记录拣货完成
        apvTrackService.batchCreateApvTrack(apvTracks);
        return 1;
    }

    /**
     * 废除任务
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRED)
    public int discardedPickingTask(List<Integer> taskIds) {

        WhPickingTaskQueryCondition whPickingTaskQueryCondition = new WhPickingTaskQueryCondition();
        whPickingTaskQueryCondition.setTaskIds(taskIds);
        List<WhPickingTask> whPickingTasks = whPickingTaskDao.queryWhPickingTaskList(whPickingTaskQueryCondition, null);

        List<Integer> ids = new ArrayList<Integer>();
        List<String> discardedOrderNos = new ArrayList<>();
        List<String> relevantNoList = new ArrayList<>();
        WhPickingTask tempWhPickingTasks = new WhPickingTask();
        Integer returnInteger = 0;
        for (WhPickingTask whPickingTask : whPickingTasks) {
            if (PickingTaskType.TRANSFER_BZYC.intCode().equals(whPickingTask.getTaskType())
                    || PickingTaskType.BZYC.intCode().equals(whPickingTask.getTaskType())
                    || PickingTaskType.BZCY.intCode().equals(whPickingTask.getTaskType())
                    || PickingTaskType.TEMU_BZYC.intCode().equals(whPickingTask.getTaskType())) {
                throw new RuntimeException(String.format("不支持废除该类型的拣货任务[%s]", whPickingTask.getTaskNo()));
            }

            if (!whPickingTask.getTaskStatus().equals(PickingTaskStatus.UNRECEIVED.intCode())) {
                throw new RuntimeException(String.format("不支持废除不是待领取的任务[%s]，请筛选后再废弃！", whPickingTask.getTaskNo()));
            }
            // 1. 把任务状态修改状态为已废除
            List<Integer> beforeTaskStatuss = new ArrayList<Integer>();
            beforeTaskStatuss.add(PickingTaskStatus.UNRECEIVED.intCode());

            tempWhPickingTasks.setId(whPickingTask.getId());
            tempWhPickingTasks.setTaskStatus(PickingTaskStatus.DISCARDED.intCode());
            int tempInteger = whPickingTaskDao.updatePickingTaskStatus(tempWhPickingTasks, null, beforeTaskStatuss);

            if (tempInteger > 0) {
                returnInteger++;

                PICKINGLOG.log(whPickingTask.getId(), "废除任务-修改状态为已废除", new String[][]{
                        {"原状态", PickingTaskStatus.getNameByCode(whPickingTask.getTaskStatus().toString())}});

                // 1.1 查询出任务对应的APV集合
                WhPickingTaskItemQueryCondition whPickingTaskItemQueryCondition = new WhPickingTaskItemQueryCondition();
                whPickingTaskItemQueryCondition.setTaskId(whPickingTask.getId());
                whPickingTaskItemQueryCondition.setTaskType(whPickingTask.getTaskType());
                List<WhPickingTaskItem> whPickingTaskItems = whPickingTaskItemDao
                        .queryWhPickingTaskItemList(whPickingTaskItemQueryCondition, null);

                // 1.2 批量修改APV状态为已分配状态和未生成任务
                ids.clear();
                for (WhPickingTaskItem whPickingTaskItem : whPickingTaskItems) {
                    ids.add(whPickingTaskItem.getApvId());
                    discardedOrderNos.add(whPickingTaskItem.getApvNo());
                    if (PickingTaskType.TEMU_MULTIPLEMULTIPLE.intCode().equals(whPickingTask.getTaskType())) {
                        relevantNoList.add(whPickingTaskItem.getPrepareOrderNo());
                    }
                    else {
                        relevantNoList.add(whPickingTaskItem.getApvNo());
                    }
                }
                // 中转仓废弃
                if (whPickingTask.getIsAsn() != null
                        && PickingTaskType.getTransferIntCode().contains(whPickingTask.getIsAsn())){
                    // 根据单号过滤出pdd与中转仓
                    List<Integer> temuIdList = whPickingTaskItems.stream().filter(w -> StringUtils.startsWith(w.getApvNo(), "PC"))
                            .map(w -> w.getApvId()).collect(Collectors.toList());
                    List<Integer> idList = whPickingTaskItems.stream().filter(w -> !StringUtils.startsWith(w.getApvNo(), "PC"))
                            .map(w -> w.getApvId()).collect(Collectors.toList());
                    // 中转仓废弃
                    if (CollectionUtils.isNotEmpty(idList)) {
                        List<WhFbaAllocation> whFbaAllocationList = whFbaAllocationService.updateWhFbaAllocationStatus(idList, AsnPrepareStatus.WAITING_GEN.intCode(), "废弃拣货任务");
                        if (CollectionUtils.isNotEmpty(whFbaAllocationList)) {
                            for (WhFbaAllocation whFbaAllocation : whFbaAllocationList) {
                                // 推送消息
                                if (whFbaAllocation.isFba()) {
                                    whFbaAllocation.setStatus(AsnPrepareStatus.WAITING_GEN.intCode());
                                    whFbaAllocationService.sendMsg(whFbaAllocation);
                                }
                            }
                        }
                    }
                    // pdd废弃
                    if (CollectionUtils.isNotEmpty(temuIdList))
                        temuTaskService.updatePackageStatus(temuIdList
                                , TemuPackageStatus.WAIT_MERGED.intCode(),(id) -> SystemLogUtils.TEMU_PACKAGE.log(id, "废弃拣货任务", new String[][] {
                                        { "详情", "任务号 (" + whPickingTask.getTaskNo() + ") " },
                                        { "状态", TemuPackageStatus.WAIT_MERGED.getName()} }));
                }
                // 拼多多
                else if (PickingTaskType.TEMU_MULTIPLEMULTIPLE.intCode().equals(whPickingTask.getTaskType())){
                    // 更新状态
                    temuTaskService.updatePackageStatus(ids
                            , TemuPackageStatus.WAIT_MERGED.intCode(),(id) -> SystemLogUtils.TEMU_PACKAGE.log(id, "废弃拣货任务", new String[][] {
                                    { "详情", "任务号 (" + whPickingTask.getTaskNo() + ") " },
                                    { "状态", TemuPackageStatus.WAIT_MERGED.getName()} }));
                }
                else {
                    whApvService.updateWhApvListStatusAndSignPaymentByIds(ids, ApvStatus.ALLOT.intCode(), false, null);
                }
                    // 1.3 批量对APV解锁 解除apv的锁--》》解除
                whApvLockService.unlockAccordingCargoPrintWhApvs(ids);
                // 废除任务，更新whApvOutStockChain状态
                whApvOutStockChainService.updateWhApvOutStockChainStatus(relevantNoList, WhApvOutStockChainStatusEnum.TOUCHING,
                        WhApvOutStockChainStatusEnum.ALLOT);
            }
        }
        return returnInteger;
    }

    /**
     * 解绑通道任务
     */
    public int untiedPickingTask(List<Integer> taskIds) {

        WhPickingTaskQueryCondition whPickingTaskQueryCondition = new WhPickingTaskQueryCondition();
        whPickingTaskQueryCondition.setTaskIds(taskIds);
        List<WhPickingTask> whPickingTasks = whPickingTaskDao.queryWhPickingTaskList(whPickingTaskQueryCondition, null);

        WhPickingTask tempWhPickingTasks = new WhPickingTask();
        Integer returnInteger = 0;
        for (WhPickingTask whPickingTask : whPickingTasks) {
            if (whPickingTask.getTaskStatus().equals(PickingTaskStatus.UNRECEIVED.intCode())
                    && (whPickingTask.getTaskType() == PickingTaskType.AISLESINGLESINGLE.intCode()
                    || whPickingTask.getTaskType() == PickingTaskType.AISLESINGLEMULTIPLE.intCode())) {

                // 1. 把通道任务类型改为对应的普通任务类型
                // 2. 把领取人改为空
                if (whPickingTask.getTaskType() == PickingTaskType.AISLESINGLESINGLE.intCode()) {
                    tempWhPickingTasks.setTaskType(PickingTaskType.SINGLESINGLE.intCode());
                } else if (whPickingTask.getTaskType() == PickingTaskType.AISLESINGLEMULTIPLE.intCode()) {
                    tempWhPickingTasks.setTaskType(PickingTaskType.SINGLEMULTIPLE.intCode());
                }
                tempWhPickingTasks.setId(whPickingTask.getId());
                tempWhPickingTasks.setReceivePerson(0);
                int tempInteger = whPickingTaskDao.untiedPickingTask(tempWhPickingTasks);
                if (tempInteger > 0) {
                    returnInteger++;
                    PICKINGLOG.log(whPickingTask.getId(), "解绑拣货区任务-修改拣货区任务类型为普通任务类型", new String[][]{
                            {"原任务类型", PickingTaskType.getNameByCode(whPickingTask.getTaskType().toString())}});
                }
            }
        }
        return returnInteger;
    }

    /**
     * 完成拣货任务
     */
    @Override
    public int completePickingTask(WhPickingTask whPickingTask, Integer beforeTaskStatus) {
        return whPickingTaskDao.updatePickingTaskStatus(whPickingTask, beforeTaskStatus, null);
    }

    /**
     * 部分完成拣货
     */
    @Override
    public int partCompletePickingTask(WhPickingTask whPickingTask, Integer beforeTaskStatus) {
        return whPickingTaskDao.updatePickingTaskStatus(whPickingTask, beforeTaskStatus, null);
    }

    /**
     * 查询拣货任务里面的拣货总数
     */
    @Override
    public int queryPickQuantity(WhPickingTask whPickingTask) {
        return whPickingTaskDao.queryPickQuantity(whPickingTask);
    }

    /**
     * PDA专用方法：修改状态，从部分拣货完成状态改为已领取
     */
    @Override
    public int updatePickingTaskForPartToReceived(WhPickingTask whPickingTask) {
        return whPickingTaskDao.updatePickingTaskForPartToReceived(whPickingTask);
    }

    /**
     * 只有需要计算拣货任务下所有的订单apvIds才允许传入null
     */
    @Override
    public void calculationPickingSkuQuantityByTaskIdAndApvIds(Integer taskId, List<Integer> apvIds) {
        logger.warn("================start CalculationSkuQuantity ===============");
        executors.execute(new Runnable() {
            @SuppressWarnings("static-access")
            @Override
            public void run() {
                WhPickingTask whPickingTask = whPickingTaskDao.queryWhPickingTask(taskId);
                if (!(whPickingTask != null && whPickingTask.getTaskType() != null
                        && !PickingTaskStatus.COMPLETED.intCode().equals(whPickingTask.getTaskStatus()))) {
                    return;
                }
                Integer taskType = whPickingTask.getTaskType();
                if (taskType.equals(PickingTaskType.MULTIPLEMULTIPLE.intCode())
                        || taskType.equals(PickingTaskType.OPTIMAL_RU_MM.intCode())
                        || taskType.equals(PickingTaskType.EXPRESSMULTIPLEMULTIPLE.intCode())
                        || taskType.equals(PickingTaskType.FBAMULTIPLEMULTIPLE.intCode())
                        || taskType.equals(PickingTaskType.OVERSIZE_MULTIPLE_PRODUCT.intCode())
                        || taskType.equals(PickingTaskType.JIT_MULTIPLEMULTIPLE.intCode())) {


                    WhPickingTaskSkuQueryCondition query = new WhPickingTaskSkuQueryCondition();
                    query.setTaskId(taskId);
                    List<WhPickingTaskSku> taskSkus = whPickingTaskSkuDao.queryWhPickingTaskSkuList(query, null);
                    if (CollectionUtils.isNotEmpty(taskSkus)) {
                        WhApvQueryCondition apvQuery = new WhApvQueryCondition();
                        apvQuery.setTaskId(taskId);
                        //List<WhApv> apvs = whApvService.queryWhApvAndItemList(apvQuery, null);
                        List<WhApv> apvs =  whApvService.queryWhApvAndItemListForPickingByTaskId(taskId);
                        List<Integer> filterApvIds = new ArrayList<>();
                        if (CollectionUtils.isEmpty(apvIds)) {
                            for (WhApv apv : apvs) {
                                filterApvIds.add(apv.getId());
                            }
                        } else {
                            filterApvIds.addAll(apvIds);
                        }
                        List<WhPickingSkuCalculator> calculators = PickingTaskSukQuantityCalculator.CalculationSkuQuantity(taskSkus,
                                apvs, filterApvIds);
                        if (CollectionUtils.isNotEmpty(calculators)) {
                            try {
                                whPickingSkuCalculatorService.batchUpdateCalculator(calculators);
                            } catch (Exception e) {
                                logger.error(e.getMessage());
                            }
                        }
                    }
                }
            }
        });
    }

    /**
     * 从新计算APV中SKU的拣货算法规则
     */
    @Override
    public void calculationPickingSkuQuantityByApvId(Integer apvId) {
        executors.execute(new Runnable() {

            @Override
            public void run() {
                if (apvId == null) {
                    return;
                }
                WhApvQueryCondition query = new WhApvQueryCondition();
                query.setId(apvId);
                List<WhApv> apvs = whApvService.queryWhApvAndItemList(query, null);
                if (CollectionUtils.isNotEmpty(apvs)) {
                    WhApv apv = apvs.get(0);
                    if (apv != null && apv.getTaskId() != null) {
                        List<Integer> filterApvIds = new ArrayList<>();
                        filterApvIds.add(apvId);
                        calculationPickingSkuQuantityByTaskIdAndApvIds(apv.getTaskId(), filterApvIds);
                    }
                }
            }
        });
    }

    /**
     * 播种差异
     *
     * @param createSowStockouts
     * @param whPickingTask
     */
    @Override
    public void updatePickingTaskDiffer(List<SowStockout> createSowStockouts, WhPickingTask whPickingTask) {
        if (whPickingTask != null) {
            WhPickingTask entity = new WhPickingTask();
            entity.setId(whPickingTask.getId());
            entity.setSowDifferQuantity(whPickingTask.getSowDifferQuantity());
            whPickingTaskDao.updateWhPickingTask(entity);

            List<WhPickingTaskSku> whPickingTaskSkuList = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(createSowStockouts)) {
                Map<String, Integer> map = new HashMap<String, Integer>();
                for (SowStockout sowStockout : createSowStockouts) {
                    Integer quantity = map.get(sowStockout.getSku());
                    int num = sowStockout.getNeedQuantity() - sowStockout.getScanQuantity();
                    if (quantity == null) {
                        map.put(sowStockout.getSku(), num);
                    } else {
                        map.put(sowStockout.getSku(), quantity + num);
                    }
                }

                for (String sku : map.keySet()) {
                    WhPickingTaskSku whPickingTaskSku = new WhPickingTaskSku();
                    whPickingTaskSku.setSku(sku);
                    whPickingTaskSku.setSowDifferQuantity(map.get(sku));
                    whPickingTaskSku.setTaskId(whPickingTask.getId());
                    whPickingTaskSkuList.add(whPickingTaskSku);
                }
            }
            whPickingTaskSkuDao.batchUpdateDiffer(whPickingTaskSkuList, entity);
        }
    }

    @Override
    public void batchUpdatePickingTaskSkuDiffer(WhPickingTask whPickingTask) {
        whPickingTaskSkuDao.batchUpdateSkuDiffer(whPickingTask);
    }

    /**
     * 单品拣货下一步
     *
     * @param skus
     * @param domain
     * @param whPickingTask
     * @param retutnQuantity
     * @return
     */
    @Override
    @StockServicelock
    public ResponseJson updateSinglePicking(List<String> skus, AndroidProductDo domain, WhPickingTask whPickingTask,
                                            Integer retutnQuantity, List<Integer> cancleApvIds, List<WhApvOutStockChain> whApvOutStockChainAllList) {
        logger.warn("updateSinglePicking: taskNo=" + whPickingTask.getTaskNo() + ";sku=" + domain.getSku() +  ";location=" + domain.getLocation() + ";pickQuantity=" + domain.getPickQuantity());
        ResponseJson response = new ResponseJson();
        response.setStatus(StatusCode.FAIL);
        // 获取拣货数量为null的库位
        long count = whApvOutStockChainAllList.stream().filter(w -> Integer.valueOf(WhApvOutStockChainStatusEnum.TOUCHING.intCode()).equals(w.getStatus()))
                .map(w -> w.getStockId()).distinct().count();
        // 获取sku总的已捡数量
        int sumPickQty = whApvOutStockChainAllList.stream().mapToInt(w -> w.getPickQuantity() == null ? 0 : w.getPickQuantity()).sum();
        // 根据发货单号分组
        Map<String, List<WhApvOutStockChain>> map = whApvOutStockChainAllList.stream().collect(Collectors.groupingBy(w -> w.getRelevantNo() + w.getStockId()));
        // 获取拣货缺货的项
        List<String> lackPositionApvNoList = whApvOutStockChainAllList.stream().filter(w ->
                WhApvOutStockChainStatusEnum.PICKED.intCode() == w.getStatus() && w.getPickQuantity() < w.getQuantity())
                .map(w -> w.getRelevantNo()).collect(Collectors.toList());
        // 收集拣货完成的apvNo 集合
        Set<String> pickApvNoList = new HashSet<String>();
        Integer returnPickOutQuantity = 0;
        // 拣货数量待扣除参数
        Integer tempQuantity = domain.getPickQuantity();
        WhPickingTaskItemQueryCondition whPickingTaskItemQueryCondition = new WhPickingTaskItemQueryCondition();
        whPickingTaskItemQueryCondition.setTaskId(domain.getTaskId());
        whPickingTaskItemQueryCondition.setSku(domain.getSku());
        if (retutnQuantity < 0)
            whPickingTaskItemQueryCondition
                    .setOrderBy(OrderByType.SPECIFIED_LOGISTICS_AND_PLATFORM_PRIORITY_AND_QUANTITY_DESC.intCode());
        List<WhPickingTaskItem> whPickingTaskItems = whPickingTaskItemService
                .queryPickingTaskItemAndSkus(whPickingTaskItemQueryCondition, null);
        List<Integer> lackPositionIdList = new ArrayList<Integer>();
        List<WhApvOutStockChain> updateWhApvOutStockChains= new ArrayList<>();
        Map<Integer,Integer> pickQtyMap = new HashMap<>();
        for (WhPickingTaskItem whPickingTaskItem : whPickingTaskItems) {
            // 已取消订单不用处理
            if (CollectionUtils.isNotEmpty(cancleApvIds) && cancleApvIds.contains(whPickingTaskItem.getApvId())) {
                continue;
            }
            // 如果拣货数量不等-->>拣货异常-缺货情况
            List<WhApvOutStockChain> whApvOutStockChainList = map.get(whPickingTaskItem.getApvNo() + domain.getStockIds().get(0));
            if (CollectionUtils.isEmpty(whApvOutStockChainList)) continue;
            WhApvOutStockChain whApvOutStockChain = whApvOutStockChainList.get(0);
            int singlePickingQuantity =  whApvOutStockChain.getQuantity()== null?0:whApvOutStockChain.getQuantity();
            int pickQuantity =  whApvOutStockChain.getPickQuantity()== null?0:whApvOutStockChain.getPickQuantity();
            if (lackPositionApvNoList.contains(whPickingTaskItem.getApvNo())) {
                lackPositionIdList.add(whPickingTaskItem.getApvId());
                returnPickOutQuantity += singlePickingQuantity;
                continue;
            }
            if (!(pickQuantity<singlePickingQuantity)) continue;
            if (retutnQuantity < 0) {
                // 处理单品的任务类型（单品单件和单品多件）
                if (singlePickingQuantity > tempQuantity || tempQuantity == 0) {
                    // 批量改为拣货缺货的list
                    lackPositionIdList.add(whPickingTaskItem.getApvId());
                    lackPositionApvNoList.add(whPickingTaskItem.getApvNo());
                    returnPickOutQuantity += singlePickingQuantity;
                    updateWhApvOutStockChains.add(WhApvOutStockChain.builder().id(whApvOutStockChain.getId())
                            .pickQuantity(0).status(WhApvOutStockChainStatusEnum.PICKED.intCode()).build());
                } else {
                    updateWhApvOutStockChains.add(WhApvOutStockChain.builder().id(whApvOutStockChain.getId())
                            .pickQuantity(singlePickingQuantity).status(WhApvOutStockChainStatusEnum.PICKED.intCode()).build());
                    sumPickQty += singlePickingQuantity;
                    pickApvNoList.add(whPickingTaskItem.getApvNo());
                    logger.warn("扣除已匹配数量 " + tempQuantity + " - " + singlePickingQuantity);
                    tempQuantity -= singlePickingQuantity;
                    pickQtyMap.put(whPickingTaskItem.getApvId(),singlePickingQuantity);
                }
            }else {
                updateWhApvOutStockChains.add(WhApvOutStockChain.builder().id(whApvOutStockChain.getId())
                        .pickQuantity(singlePickingQuantity).status(WhApvOutStockChainStatusEnum.PICKED.intCode()).build());
                sumPickQty += singlePickingQuantity;
                pickApvNoList.add(whPickingTaskItem.getApvNo());
                pickQtyMap.put(whPickingTaskItem.getApvId(),singlePickingQuantity);
            }
        }
        // 返回给PDA一个是否需要退SKU的信息
        if (tempQuantity > 0 && retutnQuantity < 0) {
            response.setMessage("请把该SKU退" + tempQuantity + "个回到原货位！");
            return response;
        }

        List<ApvTrack> apvTracks = new ArrayList<ApvTrack>();
        for (String apvNo : pickApvNoList) {
            ApvTrack apvTrack = new ApvTrack();
            apvTrack.setApvNo(apvNo);
            apvTrack.setTaskNo(whPickingTask.getTaskNo());
            apvTrack.setPickUser(whPickingTask.getReceivePerson());
            apvTrack.setPickFinishTime(new Timestamp(System.currentTimeMillis()));
            apvTracks.add(apvTrack);
        }
        // 记录拣货完成
        apvTrackService.batchCreateApvTrack(apvTracks);

        // TODO 单品单件或单品多件记录拣货数量
        if (CollectionUtils.isNotEmpty(pickApvNoList)) {
            WhApvQueryCondition apvQuery = new WhApvQueryCondition();
            apvQuery.setApvNo(StringUtils.join(pickApvNoList, ","));
            List<WhApvItem> apvItems = null;
            List<WhApv> whapvs = whApvService.queryWhApvAndItemList(apvQuery, null);
            if (CollectionUtils.isNotEmpty(whapvs)) {
                apvItems = new ArrayList<>();
                List<WhApv> updateWhApvs = new ArrayList<>();
                for (WhApv whApv : whapvs) {
                    if (whApv.getStatus().equals(ApvStatus.CANCEL.intCode()) || whApv.getStatus().equals(ApvStatus.WAITING_ALLOT.intCode())
                            || whApv.getStatus().equals(ApvStatus.DELIVER.intCode())) {
                        continue;
                    }
                    WhApv updateWhApv = new WhApv();
                    updateWhApv.setSignDistributionGoods(true);// 已拣货
                    updateWhApv.setId(whApv.getId());
                    updateWhApvs.add(updateWhApv);
                    apvItems.addAll(whApv.getWhApvItems());
                }
                whApvService.batchUpdateWhApv(updateWhApvs);
            }
            if (CollectionUtils.isNotEmpty(apvItems)) {
                for (WhApvItem whApvItem : apvItems) {
                    // 设置已拣数量等于订单数量
                    Integer curQty = pickQtyMap.get(whApvItem.getApvId());
                    whApvItem.setPickQuantity(whApvItem.getPickQuantity()+(curQty==null?0:curQty));
                }
                whApvItemService.batchUpdateWhApvItem(apvItems);
            }
        }

        whApvOutStockChainService.batchUpdateWhApvOutStockChain(updateWhApvOutStockChains);
        // TODO 改库存
        if (domain.getPickQuantity() > 0 || returnPickOutQuantity > 0) {
            boolean updateLine = apvPickUpdateStockService.singlePick(domain, returnPickOutQuantity, whPickingTask.getTaskNo());
            logger.warn("拣货改库存: taskNo=" + whPickingTask.getTaskNo() + ";sku=" + domain.getSku() + ";location=" + domain.getLocation() + ";pickQuantity=" + domain.getPickQuantity() + ";updateLine=" +updateLine);
            if (!updateLine) {
                throw new RuntimeException("拣货下一步改库存失败!");
            }
        }
        // 捡最后一个库位时，计算拣货缺货
        if (count == 1){
            // 更新拣货数量
            updateWhPickingTaskSku(PickingTaskSkuStatus.COMPLETED.intCode(), sumPickQty,
                    domain.getTaskSkuId());
            if ( CollectionUtils.isNotEmpty(lackPositionIdList)) {
                // 解除apv的锁--》》解除
                whApvLockService.unlockAccordingCargoPrintWhApvs(lackPositionIdList);
                // 修改apv的状态--》》【拣货缺货】
                /**
                 * 单品单件没有周转框 会出现抢单情况 所以当任务类型是单品单件时 需要把订单状态为等待发货不进行修改为拣货缺货
                 */

                WhApvQueryCondition query = new WhApvQueryCondition();
                query.setApvIds(lackPositionIdList);
                List<WhApv> whapvList = whApvService.queryWhApvs(query, null);
                if (domain.getTaskType().equals(PickingTaskType.SINGLESINGLE.intCode())
                        || domain.getTaskType().equals(PickingTaskType.OPTIMAL_RU_SS.intCode())
                        || domain.getTaskType().equals(PickingTaskType.JIT_SINGLESINGLE.intCode())) {
                    List<WhApv> whapvs = whapvList.stream().filter(whApv -> whApv.getStatus().equals(ApvStatus.WAITING_DELIVER.intCode())).collect(Collectors.toList());
                    // 查询id列表中为等待发货的APV单
                    if (CollectionUtils.isNotEmpty(whapvs)) {
                        // 把等待发货的订单删除掉
                        List<Integer> lackLists = new ArrayList<Integer>();
                        for (WhApv whApv : whapvs) {
                            lackPositionIdList.remove(whApv.getId());
                            lackLists.add(whApv.getId());
                        }
                        // 重新锁定订单
                        whApvLockService.lockAccordingCargoPrintWhApvs(lackLists);
                    }
                }
                // 批量修改APV状态为拣货缺货状态和未生成任务
                whApvService.updateWhApvListStatusAndSignPaymentByIds(lackPositionIdList,
                        ApvStatus.PICKING_STOCKOUT_NOT.intCode(), false, null);
                // TODO 生成拣货缺货盘点需求
                List<WhApvOutStockChain> inventoryDemandStockChain = whApvOutStockChainAllList.stream().filter(c ->lackPositionApvNoList.contains(c.getRelevantNo())).collect(Collectors.toList());

                InventoryDemandParam param = new InventoryDemandParam(domain);
                param.setFailedApvIds(lackPositionIdList);
                param.setStockId(domain.getStockIds().get(0));
                whPickInventoryDemandService.generatePickInventoryDemand(param, DataContextHolder.getUserId(),inventoryDemandStockChain);
                // 退已捡库存
                List<WhApvOutStockChain> returnWhApvOutStockChain = whApvOutStockChainAllList.stream().filter(w -> WhApvOutStockChainStatusEnum.PICKED.intCode() == w.getStatus()
                        && w.getQuantity().equals( w.getPickQuantity()) && lackPositionApvNoList.contains(w.getRelevantNo())).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(returnWhApvOutStockChain)) {
                    Map<Integer,Integer> returnMap = new HashMap<>();
                    for (WhApvOutStockChain whApvOutStockChain:returnWhApvOutStockChain){
                        Integer pickQty = returnMap.get(whApvOutStockChain.getStockId());
                        if (pickQty == null) pickQty = 0;
                        pickQty+=whApvOutStockChain.getPickQuantity();
                        returnMap.put(whApvOutStockChain.getStockId(),pickQty);
                    }
                    apvPickUpdateStockService.singlePickReturn(returnMap,domain.getSku(),whPickingTask.getTaskNo());
                }
                cancelJITOrder(Optional.ofNullable(whapvList).orElse(new ArrayList<>()).stream().filter(whApv -> !whApv.getStatus().equals(ApvStatus.WAITING_DELIVER.intCode())).collect(Collectors.toList()),whApvOutStockChainAllList);
            }

        }

        if (CollectionUtils.isNotEmpty(domain.getUuidList())) {
            for (String uuid : domain.getUuidList()) {
                whUniqueSkuService.pickingUniqueSKuLog(uuid, UniqueSkuStep.PICKING.intCode(),whPickingTask.getTaskNo());
            }
        }
        response.setStatus(StatusCode.SUCCESS);
        return response;
    }

    public void cancelJITOrder(List<WhApv> apvList,List<WhApvOutStockChain> whApvOutStockChainAllList) {
        List<Integer> shipStatuList = Arrays.asList(ApvOrderType.JIT_FULL_MANAGEMENT.intCode(), ApvOrderType.JIT_HALF_MANAGEMENT.intCode());
        List<WhApv> collectApvList = Optional.ofNullable(apvList).orElse(new ArrayList<>())
                .stream()
                .filter(whApv -> shipStatuList.contains(whApv.getShipStatus())
                        && whApv.getOriginalOrderId() != null
                        && whApv.getOriginalOrderId() >= 2)
                .collect(Collectors.toList());

        Map<String, List<WhApvOutStockChain>> map = whApvOutStockChainAllList.stream().collect(Collectors.groupingBy(WhApvOutStockChain::getRelevantNo));
        List<WhApvOutStockChain> whApvOutStockChainList=new ArrayList<>();
        for (WhApv whApv : collectApvList) {
            WhApv apv = whApv.cancelHandle();
            apv.setStatus(ApvStatus.CANCEL.intCode());
            apv.setLastModifiedTime(new Timestamp(System.currentTimeMillis()));
            whApvService.updateWhApv(apv);
            SystemLogUtils.APVLOG.log(apv.getId(), "JIT订单二次合单拣货缺货取消", new String[][]{{"历史状态", whApv.getStatusCn()},
                    {"更改状态", ApvStatus.CANCEL.getName()}, {"DD号更改", whApv.getSalesRecordNumber()}});

            //取消订单通知oms
            whApvService.cancelJitApvOms(whApv.getApvNo(), whApv.getTrackingNumber(),AsnPrepareStatus.CANCEL.intCode());
            //取消订单通知tms
            whApvService.cancelJitApvTms(whApv.getApvNo(),AsnPrepareStatus.CANCEL.intCode());

            whApv.setCancelReason("JIT二次合单拣货缺货取消订单");
            //取消订单，创建取消报表数据
            whOrderCancelItemService.createWhOrderCancelItem(whApv);

            if (MapUtils.isNotEmpty(map) && CollectionUtils.isNotEmpty(map.get(whApv.getApvNo()))) {
                whApvOutStockChainList.addAll(map.get(whApv.getApvNo()));
            }
        }
        if (CollectionUtils.isNotEmpty(whApvOutStockChainList)) {
            // 清除出库库存分配关联表
            whApvOutStockChainService.deleteWhApvOutStockChainByIds(whApvOutStockChainList.stream().map(WhApvOutStockChain::getId).collect(Collectors.toList()));
            // 迁移到分配取消表，用于拣货计算
            whApvOutStockChainCancelService.save(whApvOutStockChainList, WhApvOutStockChainStatusEnum.CANCEL);
        }

    }


    @Override
    @StockServicelock
    public ResponseJson updateMultiplePicking(List<String> skus, AndroidProductDo domain, WhPickingTask whPickingTask, List<WhApv> updateApvs,
                                              List<WhApvItem> updateApvItems, List<WhApvOutStockChain> updateWhApvOutStockChainList, long count, Integer sumPickQty) {
        logger.warn("updateMultiplePicking: taskNo=" + whPickingTask.getTaskNo() + ";sku=" + domain.getSku() +  ";location=" + domain.getLocation() + ";pickQuantity=" + domain.getPickQuantity());
        ResponseJson response = new ResponseJson();
        response.setStatus(StatusCode.FAIL);
        // 扣减库存
        if (domain.getPickQuantity() > 0) {
            boolean updateLine = apvPickUpdateStockService.multiplePick(domain, whPickingTask.getTaskNo());
            logger.warn("拣货移库存: taskNo=" + whPickingTask.getTaskNo() + ";sku=" + domain.getSku() +  ";location=" + domain.getLocation() +
                    ";pickQuantity=" + domain.getPickQuantity() + ";updateLine=" +updateLine);
            if (!updateLine) {
                throw new RuntimeException("拣货下一步改库存失败!");
            }
        }

        if (count == 1) {
            //记录是否跨楼层拣货
            WhPickingTask updateTask = new WhPickingTask();
            boolean crossFloor = PickingTaskUtils.checkCrossFloor(whPickingTask.getId(), whPickingTask.getTaskType());
            updateTask.setCrossFloor(crossFloor);
            updateTask.setId(whPickingTask.getId());
            whPickingTaskDao.updateWhPickingTask(updateTask);

            // 修改拣货任务Sku的状态-->>已完成
            updateWhPickingTaskSku(PickingTaskSkuStatus.COMPLETED.intCode(), sumPickQty,
                    domain.getTaskSkuId());
        }

        if (CollectionUtils.isNotEmpty(domain.getUuidList())) {
            for (String uuid : domain.getUuidList()) {
                whUniqueSkuService.pickingUniqueSKuLog(uuid,UniqueSkuStep.PICKING.intCode(),whPickingTask.getTaskNo());
            }
        }
        whApvService.batchUpdateWhApv(updateApvs);
        whApvItemService.batchUpdateWhApvItem(updateApvItems);
        whApvOutStockChainService.batchUpdateWhApvOutStockChain(updateWhApvOutStockChainList);
        response.setStatus(StatusCode.SUCCESS);
        return response;
    }

    /**
     * 修改taskSku方法
     *
     * @param status
     * @param pickQuantity
     * @param id
     */
    private void updateWhPickingTaskSku(Integer status, Integer pickQuantity, Integer id) {
        WhPickingTaskSku whPickingTaskSku = new WhPickingTaskSku();
        // 完成拣货
        whPickingTaskSku.setStatus(status);
        whPickingTaskSku.setId(id);
        // （实际拣货数量 ）=（客户输入的数量-实际APV用到的数量）
        // 因为在缺货的情况下需要客户自动把拣多的数量放回到原库位中
        whPickingTaskSku.setPickQuantity(pickQuantity);
        whPickingTaskSku.setLastUpdateDate(new Timestamp(System.currentTimeMillis()));
        whPickingTaskSkuService.updateWhPickingTaskSku(whPickingTaskSku);
    }

    private String checkReXiaoTask(WhPickingTask whPickingTask, String sku, Integer checkQuantity){
        if (whPickingTask == null) {
            return "输入任务号有误！";
        }
        if (!whPickingTask.getTaskStatus().equals(PickingTaskStatus.UNRECEIVED.intCode())
                || !whPickingTask.getIsPrinting().equals(PickingTaskIsPrinting.PAPER_PRINTING.intCode())) {
            return "拣货任务必须为未领取和纸质已打印！";
        }
        if (whPickingTask.getTaskType().equals(PickingTaskType.RXSINGLEMULTIPLE.intCode())
                || whPickingTask.getTaskType().equals(PickingTaskType.RXSINGLESINGLE.intCode())) {
            // 热销单品单件 热销单品两件
            if (whPickingTask.getQuantity() < checkQuantity) {
                return "输入数量有误！";
            }

            if (whPickingTask.getCheckQuantity() >= 0) {
                return "已经核对过了，不可再次核对！";
            }

            if (whPickingTask.getTaskType().equals(PickingTaskType.RXSINGLEMULTIPLE.intCode())) {
                // 必须为偶数
                if (!(checkQuantity % 2 == 0 && whPickingTask.getQuantity() % 2 == 0)) {
                    return "热销单品两件，拣货数量和复核数量都必须为偶数！";
                }
            }

            WhPickingTaskSkuQueryCondition whPickingTaskSkuQueryCondition = new WhPickingTaskSkuQueryCondition();
            whPickingTaskSkuQueryCondition.setTaskId(whPickingTask.getId());
            WhPickingTaskSku whPickingTaskSku = whPickingTaskSkuDao
                    .queryWhPickingTaskSku(whPickingTaskSkuQueryCondition);
            if (!whPickingTaskSku.getSku().equals(sku)) {
                return "sku核对不正确！";
            }
            whPickingTask.setWhPickingTaskSkus(Arrays.asList(whPickingTaskSku));
        }else {
            return "任务类型错误！";
        }
        return null;
    }

    public CheckPickingData buildCheckPickingData(WhPickingTask task){
        CheckPickingData checkPickingData = new CheckPickingData();
        // 所有发货单
        List<Integer> apvIds = new ArrayList<>();
        // 当前任务绑定发货单
        List<Integer> currentApvIds = whApvService.queryCurrentReXiaoTaskNormal(task.getTaskNo());
        // 前置打印后取消的发货单
        List<Integer> cancelApvIds = new ArrayList<>();
        // 前置打印后待分配、已分配、合单的发货单
        List<Integer> waitAllotApvIds = new ArrayList<>();
        // 与本批次解绑的单
        List<Integer> ycIds = new ArrayList<>();

        // 前置打印后解绑的单
        List<Integer> aferPaperPrintMoveIds = new ArrayList<>();

        // 前置打印前取消的发货单
        List<Integer> beforeCancelApvIds = new ArrayList<>();
        // 前置打印前待分配、已分配、合单的发货单
        List<Integer> beforeWaitAllotApvIds = new ArrayList<>();

        // 查询所有的订单信息 按付款时间倒序排序
        WhPickingTaskItemQueryCondition tempQueryObject = new WhPickingTaskItemQueryCondition();
        tempQueryObject.setTaskId(task.getId());
        tempQueryObject.setOrderBy(OrderByType.PAID_DATA_DESC.intCode());
        List<WhPickingTaskItem> whPickingTaskItems = whPickingTaskItemDao
                .queryPickingTaskItemAndSkuList(tempQueryObject, null);
        for (WhPickingTaskItem item: whPickingTaskItems){
            apvIds.add(item.getApvId());
            if (CollectionUtils.isNotEmpty(currentApvIds) && !currentApvIds.contains(item.getApvId())){
                ycIds.add(item.getApvId());
                if (Integer.valueOf(1).equals(item.getMoveAsPrint())){
                    aferPaperPrintMoveIds.add(item.getApvId());
                }
            }
        }
        if (CollectionUtils.isEmpty(currentApvIds)){
            ycIds.addAll(apvIds);
        }
        if (CollectionUtils.isNotEmpty(ycIds)){
            WhApvQueryCondition apvQuery = new WhApvQueryCondition();
            apvQuery.setApvIds(ycIds);
            List<WhApv> ycApvs = whApvService.queryWhApvs(apvQuery, null);
            if (CollectionUtils.isNotEmpty(ycApvs)){
                for (WhApv apv: ycApvs){
                    if (apv.getStatus().equals(ApvStatus.DELIVER.intCode()) && task.getTaskNo().equals(apv.getCompleteStatus())){
                        continue;
                    }
                    if (apv.getStatus().equals(ApvStatus.CANCEL.intCode())){
                        if (aferPaperPrintMoveIds.contains(apv.getId())){
                            cancelApvIds.add(apv.getId());
                        }else {
                            beforeCancelApvIds.add(apv.getId());
                        }

                    }else {
                        if (aferPaperPrintMoveIds.contains(apv.getId())){
                            waitAllotApvIds.add(apv.getId());
                        }else {
                            beforeWaitAllotApvIds.add(apv.getId());
                        }
                    }
                }
            }
        }
        checkPickingData.setPickingTask(task);
        checkPickingData.setTaskNo(task.getTaskNo());
        checkPickingData.setApvIds(apvIds);
        checkPickingData.setCurrentApvIds(currentApvIds);
        checkPickingData.setCancelApvIds(cancelApvIds);
        checkPickingData.setWaitAllotApvIds(waitAllotApvIds);
        checkPickingData.setBeforeCancelApvIds(beforeCancelApvIds);
        checkPickingData.setBeforeWaitAllotApvIds(beforeWaitAllotApvIds);
        return checkPickingData;
    }

    @Override
    @StockServicelock
    public ResponseJson updateCheckPickingQuantity(List<String> skus, String taskNo, Integer checkQuantity){
        ResponseJson response = new ResponseJson();
        response.setStatus(StatusCode.FAIL);
        String sku  = skus.get(0);
        WhPickingTask whPickingTask = whPickingTaskDao.queryPickingQuantity(taskNo, null);
        String errMes = checkReXiaoTask(whPickingTask, sku, checkQuantity);
        if (StringUtils.isNotBlank(errMes)){
            response.setMessage(errMes);
            return response;
        }

        CheckPickingData data = buildCheckPickingData(whPickingTask);

        // 当前任务绑定发货单
        List<Integer> currentApvIds = data.getCurrentApvIds();
        // 取消的发货单
        List<Integer> cancelApvIds = data.getCancelApvIds();
        // 待分配、已分配、合单的发货单
        List<Integer> waitAllotApvIds = data.getWaitAllotApvIds();

        // 拣货完成的发货单
        List<Integer> completeApvIds = new ArrayList<>();
        // 拣货缺货的发货单
        List<Integer> pickOutApvIds = new ArrayList<>();

        Integer pickForApvCount = 0;
        int times = 1;
        if (whPickingTask.getTaskType().equals(PickingTaskType.RXSINGLEMULTIPLE.intCode())) {
            if (checkQuantity % 2 == 0 && whPickingTask.getQuantity() % 2 == 0) {
                pickForApvCount = checkQuantity / 2;
                times = 2;
            }
        } else {
            pickForApvCount = checkQuantity;
        }

        if (CollectionUtils.isNotEmpty(currentApvIds)){
            for (Integer apvid: currentApvIds){
                if (pickForApvCount > 0){
                    pickForApvCount--;
                    completeApvIds.add(apvid);
                }else {
                    pickOutApvIds.add(apvid);
                }
            }
        }

        // 拣到货的发货单--锁定退已拣返架
        List<Integer> completeWaitAllotApvIds = new ArrayList<>();
        // 未拣到货的发货单--锁定退可用
        List<Integer> pickOutWaitAllotApvIds = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(waitAllotApvIds)){
            for (Integer apvid: waitAllotApvIds){
                if (pickForApvCount > 0){
                    pickForApvCount--;
                    completeWaitAllotApvIds.add(apvid);
                }else {
                    pickOutWaitAllotApvIds.add(apvid);
                }
            }
        }

        // 拣到货的发货单--锁定退取消
        List<Integer> completeCancelApvIds = new ArrayList<>();
        // 未拣到货的发货单--锁定退可用
        List<Integer> pickOutCancelApvIds = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(cancelApvIds)){
            for (Integer apvid: cancelApvIds){
                if (pickForApvCount > 0){
                    pickForApvCount--;
                    completeCancelApvIds.add(apvid);
                }else {
                    pickOutCancelApvIds.add(apvid);
                }
            }
        }

        Integer completeBeforeWaitAllotQuantity = 0;
        Integer completeBeforeCancelQuantity = 0;
        // 打印前的不处理，即使检到货了也不处理，后面直接返架
        /*if (CollectionUtils.isNotEmpty(data.getBeforeWaitAllotApvIds())){
            for (Integer apvid: data.getBeforeWaitAllotApvIds()){
                if (pickForApvCount > 0){
                    pickForApvCount--;
                    completeBeforeWaitAllotQuantity += 1 * times;
                }
            }
        }
        if (CollectionUtils.isNotEmpty(data.getBeforeCancelApvIds())){
            for (Integer apvid: data.getBeforeCancelApvIds()){
                if (pickForApvCount > 0){
                    pickForApvCount--;
                    completeBeforeCancelQuantity += 1 * times;
                }
            }
        }*/

        data.setSku(sku);
        data.setCheckQuantity(checkQuantity);
        data.setCompleteQuantity(completeApvIds.size() * times);
        data.setPickOutQuantity(pickOutApvIds.size() * times);
        data.setCompleteWaitAllotQuantity(completeWaitAllotApvIds.size() * times);
        data.setPickOutWaitAllotQuantity(pickOutWaitAllotApvIds.size() * times);
        data.setCompleteCancelQuantity(completeCancelApvIds.size() * times);
        data.setPickOutCancelQuantity(pickOutCancelApvIds.size() * times);
        data.setCompleteBeforeWaitAllotQuantity(completeBeforeWaitAllotQuantity);
        data.setCompleteBeforeCancelQuantity(completeBeforeCancelQuantity);

        // TODO 库存处理
        boolean updateLine = apvPickUpdateStockService.checkReXiaoTask(data);
        logger.warn("热销核对拣货数量移库存: taskNo=" + whPickingTask.getTaskNo() + ";sku=" + sku + ";checkQuantity=" + checkQuantity + ";updateLine=" +updateLine);
        if (!updateLine) {
            throw new RuntimeException("热销核对拣货数量改库存失败!");
        }

        if (CollectionUtils.isNotEmpty(pickOutApvIds)) {
            // 批量修改APV状态为拣货缺货状态和未生成任务
            whApvService.updateWhApvListStatusAndSignPaymentByIds(pickOutApvIds,
                    ApvStatus.PICKING_STOCKOUT_NOT.intCode(), false, null);
            // 解除apv的锁--》》解除
            whApvLockService.unlockAccordingCargoPrintWhApvs(pickOutApvIds);
            batchCreatPickOutWhSaleStockLog(pickOutApvIds);
        }

        // checkReXiaoTask中塞入的
        WhPickingTaskSku whPickingTaskSku = whPickingTask.getWhPickingTaskSkus().get(0);
        if (checkQuantity > 0) {
            // 修改拣货任务item表SKU已拣数量数据
            whPickingTaskSku.setPickQuantity(checkQuantity);
            whPickingTaskSkuDao.updateWhPickingTaskSku(whPickingTaskSku);
        }

        // 修改拣货任务主表表 状态为已完成
        WhPickingTask updateTask = new WhPickingTask();
        updateTask.setId(whPickingTask.getId());

        updateTask.setTaskStatus(PickingTaskStatus.COMPLETED.intCode());
        updateTask.setPickingEndDate(new Timestamp(System.currentTimeMillis()));
        updateTask.setCheckQuantity(checkQuantity);
        updateTask.setReceivePerson(DataContextHolder.getUserId());
        updateTask.setReceiveDate(new Timestamp(System.currentTimeMillis()));
        int returnInt = whPickingTaskDao.updatePickingTaskStatus(updateTask, PickingTaskStatus.UNRECEIVED.intCode(), null);
        if (returnInt <= 0){
            throw new RuntimeException("修改失败, 更新拣货任务状态失败");
        }
        PICKINGLOG.log(whPickingTask.getId(), "拣货数量核对-修改状态为已完成",
                new String[][]{{"原状态", PickingTaskStatus.UNRECEIVED.getName().toString()}});

        // 修改拣货任务SKU表 状态为已完成
        whPickingTaskSkuDao.updateWhPickingTaskSkuStatusByTaskId(whPickingTask.getId(),
                PickingTaskSkuStatus.COMPLETED.intCode());

        addApvTrackPickInfo(whPickingTask.getId(), whPickingTask.getTaskNo());

        if (CollectionUtils.isNotEmpty(completeApvIds)) {
            updateWhApvPickQuantity(completeApvIds);
        }
        // 记录apv拣货完成
        if (CollectionUtils.isNotEmpty(completeApvIds)) {
            for (Integer apvId : completeApvIds) {
                WHAPV.log(apvId, "完成拣货", new String[][]{{"详情", "任务号 (" + taskNo + ") 拣货完成"}});
            }
        }
        response.setMessage("修改成功！");
        response.setStatus(StatusCode.SUCCESS);
        return response;
    }

    @Override
    @StockServicelock
    public ResponseJson updateReceiveReXiaoTask(List<String> skus, WhPickingTask task){
        ResponseJson response = new ResponseJson();
        response.setStatus(StatusCode.FAIL);
        String sku  = skus.get(0);
        CheckPickingData data = buildCheckPickingData(task);
        // 取消的发货单
        List<Integer> cancelApvIds = data.getCancelApvIds();
        // 待分配、已分配、合单的发货单
        List<Integer> waitAllotApvIds = data.getWaitAllotApvIds();

        int times = 1;
        if (task.getTaskType().equals(PickingTaskType.RXSINGLEMULTIPLE.intCode())) {
            times = 2;
        }
        // 需要退冻结到可用的库存
        Integer quantity = 0;
        if (CollectionUtils.isNotEmpty(waitAllotApvIds)){
            quantity += waitAllotApvIds.size();
        }
        if (CollectionUtils.isNotEmpty(cancelApvIds)){
            quantity += cancelApvIds.size();
        }
        quantity = quantity * times;

        // TODO 库存处理
        if (quantity > 0){
            boolean updateLine = apvPickUpdateStockService.updateReceiveReXiaoTask(sku, task.getTaskNo(), quantity);
            logger.warn("热销拣货领取任务移库存: taskNo=" + task.getTaskNo() + ";sku=" + sku + ";quantity=" + quantity + ";updateLine=" +updateLine);
            if (!updateLine) {
                throw new RuntimeException("热销拣货领取任务改库存失败!");
            }
        }

        WhPickingTask whPickingTask = new WhPickingTask();
        whPickingTask.setId(task.getId());
        whPickingTask.setReceivePerson(task.getReceivePerson());
        int returnInt = receivePickingTask(whPickingTask);
        if (returnInt > 0) {
            PICKINGLOG.log(task.getId(), "领取拣货任务-修改状态为已领取",
                    new String[][] { { "原状态", PickingTaskStatus.UNRECEIVED.getName().toString() } });
        }
        else {
            throw new RuntimeException("领取任务失败，请重新领取！");
        }
        response.setMessage("修改成功！");
        response.setStatus(StatusCode.SUCCESS);
        return response;
    }

    @Override
    public  boolean checkWhApvIsReXiao(WhApv whApv){
        boolean isReXiao = false;
        if (whApv.getWhApvItems().size() == 1 && whApv.getWhApvItems().get(0).getSaleQuantity() <= 2
                && (whApv.getStatus().equals(ApvStatus.SINGLETON_TOUCHING.intCode()) || whApv.getStatus().equals(ApvStatus.EXCESSIVE_PARTS_TOUCHING.intCode()))
                && StringUtils.isNotBlank(whApv.getCompleteStatus())){
            String taskNo = whApv.getCompleteStatus();
            WhPickingTaskQueryCondition query = new WhPickingTaskQueryCondition();
            query.setTaskNo(taskNo);
            WhPickingTask task = queryWhPickingTask(query);
            if (task != null && task.getTaskStatus().equals(PickingTaskStatus.UNRECEIVED.intCode())
                    && (task.getTaskType().equals(PickingTaskType.RXSINGLESINGLE.intCode()) || task.getTaskType().equals(PickingTaskType.RXSINGLEMULTIPLE.intCode()))
                    && task.getIsPrinting().equals(PickingTaskIsPrinting.PAPER_PRINTING.intCode())){
                whApv.setTaskId(task.getId());
                isReXiao = true;
            }
        }
        return isReXiao;
    }

    @Override
    public int updateWhPickingTaskItemMoveAsPrintByApvIdAndTaskId(Integer taskId, Integer apvId) {
        if (taskId == null || apvId == null) {
            throw new RuntimeException("参数缺失!");
        }
        return whPickingTaskItemDao.updateWhPickingTaskItemMoveAsPrintByApvIdAndTaskId(taskId, apvId);
    }

    /**
     * 拣货取货添加销售库存变动日志
     * @param apvIds
     */
    @Override
    public void batchCreatPickOutWhSaleStockLog(List<Integer> apvIds){
        if (CollectionUtils.isNotEmpty(apvIds)){
            try {
                WhApvQueryCondition query = new WhApvQueryCondition();
                query.setApvIds(apvIds);
                List<WhApv> apvs = whApvService.queryWhApvAndItemList(query, null);
                if (CollectionUtils.isNotEmpty(apvs)){
                    // 平台占用库存日志
                    List<WhSaleStockLog> whSaleStockLogs = new ArrayList<>();
                    for (WhApv whApv: apvs){
                        for (WhApvItem item: whApv.getWhApvItems()){
                            if (item.getWhSku().getWarehouseId() == CacheUtils.getLocalWarehouseId()) {
                                whSaleStockLogs.add(new WhSaleStockLog(item.getSku(), item.getSaleQuantity(), whApv.getId(), SaleStockLogType.APV));
                            }
                        }
                    }
                    // 添加平台库存日志
                    whSaleStockLogService.batchCreateWhSaleStockLog(whSaleStockLogs);
                }
            } catch (Exception e) {
                logger.error(e.getMessage(), e);
            }
        }
    }

    @Override
    public int queryWhPickingTasksCount(WhPickingTaskQueryCondition query) {
        return whPickingTaskDao.queryWhPickingTaskCount(query);
    }

    @Override
    public void doSowStockOut(WhPickingTask whPickingTask, List<SowStockout> sowStockouts) {
        // 生成拣货任务主表
        if (whPickingTask == null) {
            whPickingTask = new WhPickingTask();
            whPickingTask.setTaskNo(CreateTaskNoUtils.createTaskNo());
            if (PickingTaskType.TRANSFER_MULTIPLEMULTIPLE.intCode().equals(sowStockouts.get(0).getOldTaskType())
                    || PickingTaskType.TRANSFER_BZCY.intCode().equals(sowStockouts.get(0).getOldTaskType())) {
                whPickingTask.setTaskType(PickingTaskType.TRANSFER_BZYC.intCode());// 播种异常
            }
            else if (PickingTaskType.TEMU_MULTIPLEMULTIPLE.intCode().equals(sowStockouts.get(0).getOldTaskType())
                    || PickingTaskType.TEMU_BZCY.intCode().equals(sowStockouts.get(0).getOldTaskType())) {
                whPickingTask.setTaskType(PickingTaskType.TEMU_BZYC.intCode());// 播种异常
                whPickingTask.setIsAsn(sowStockouts.get(0).getOldTaskType());
            }
            else if (PickingTaskType.JIT_MULTIPLEMULTIPLE.intCode().equals(sowStockouts.get(0).getOldTaskType())
                    || PickingTaskType.JIT_BZCY.intCode().equals(sowStockouts.get(0).getOldTaskType())) {
                whPickingTask.setTaskType(PickingTaskType.JIT_BZYC.intCode());// 播种异常
                whPickingTask.setIsAsn(sowStockouts.get(0).getOldTaskType());
            }
            else if (PickingTaskType.JIT_ASN_MULTIPLE.intCode().equals(sowStockouts.get(0).getOldTaskType())
                    || PickingTaskType.JIT_ASN_BZCY.intCode().equals(sowStockouts.get(0).getOldTaskType())) {
                whPickingTask.setTaskType(PickingTaskType.JIT_ASN_BZYC.intCode());// 播种异常
                whPickingTask.setIsAsn(sowStockouts.get(0).getOldTaskType());
            } else if (PickingTaskType.TRANSFER_SINGLESINGLE.intCode().equals(sowStockouts.get(0).getOldTaskType())
                    || PickingTaskType.TRANSFER_SINGLEMULTIPLE.intCode().equals(sowStockouts.get(0).getOldTaskType())) {
                whPickingTask.setTaskType(PickingTaskType.TRANSFER_BZYC.intCode());// 播种异常
                whPickingTask.setIsAsn(sowStockouts.get(0).getOldTaskType());
            }
            else {
                whPickingTask.setTaskType(PickingTaskType.BZYC.intCode());// 播种异常
            }
            whPickingTask.setTaskStatus(PickingTaskStatus.UNRECEIVED.intCode());
            whPickingTask.setCreatedBy(DataContextHolder.getUserId());
            whPickingTask.setCreatedDate(new Timestamp(System.currentTimeMillis()));
            whPickingTask.setWarehouseType(-1);// 默认-1
            whPickingTask.setWaybillType(-1);// 默认-1
            whPickingTask.setTaskLevel(0);
            // 海外仓二次配货
            if (sowStockouts.get(0).getOldTaskType() != null
                    && PickingTaskType.getTransferIntCode().contains(sowStockouts.get(0).getOldTaskType())) {
                whPickingTask.setIsAsn(sowStockouts.get(0).getOldTaskType());
            }
            // 优选仓二次配货
            if (sowStockouts.get(0).getOldTaskType() != null
                    && sowStockouts.get(0).getOldTaskType().equals(PickingTaskType.OPTIMAL.intCode())) {
                whPickingTask.setIsAsn(PickingTaskType.OPTIMAL.intCode());
            }
            this.createWhPickingTask(whPickingTask);
            logger.warn("创建拣货任务 -------》ID=" + whPickingTask.getId());
            // 创建多品多件拣货任务播种记录
            whPickingTask.setYcBoxNo(sowStockouts.get(0).getStockoutBox());
            pickTaskExpandService.createPickTaskExpandByTask(whPickingTask);
        }

        WhPickingTaskSkuQueryCondition skuQuery = new WhPickingTaskSkuQueryCondition();
        skuQuery.setTaskId(whPickingTask.getId());
        List<WhPickingTaskSku> whPickingTaskSkus = whPickingTaskSkuService.queryWhPickingTaskAndSkus(skuQuery, null);
        Map<String, WhPickingTaskSku> taskSkuMap = Optional.ofNullable(whPickingTaskSkus).orElse(whPickingTaskSkus)
                .stream().collect(Collectors.toMap(WhPickingTaskSku::getSku, s -> s, (s1, s2) -> s1));
        // 生成拣货任务SKU表
        Map<Integer, String> whPickingTaskItemMap = new HashMap<Integer, String>();

        Map<String, Integer> whPickingTaskSkuMap = new HashMap<String, Integer>();
        
        for (SowStockout sowStockout : sowStockouts) {
            sowStockout.setScanQuantity(Optional.ofNullable(sowStockout.getScanQuantity()).orElse(0));
            if (sowStockout.getNeedQuantity() - sowStockout.getScanQuantity() <= 0) {
                continue;
            }

            whPickingTaskItemMap.put(sowStockout.getApvId(), sowStockout.getApvNo());

            String key = sowStockout.getSku();
            Integer integer = whPickingTaskSkuMap.get(key);

            if (integer == null) {
                integer = 0;
            }

            whPickingTaskSkuMap.put(key, integer + (sowStockout.getNeedQuantity() - sowStockout.getScanQuantity()));
            sowStockout.setStatus(SowStockoutStatus.GENERATE_TASK.intCode());


        }

        List<WhPickingTaskSku> skus = new ArrayList<>();
        List<WhPickingTaskSku> updateSkuList = new ArrayList<>();
        for (String key : whPickingTaskSkuMap.keySet()) {
            if (taskSkuMap.get(key) != null) {
                WhPickingTaskSku existSku = taskSkuMap.get(key);
                WhPickingTaskSku updateSku = new WhPickingTaskSku();
                updateSku.setId(existSku.getId());
                updateSku.setQuantity(
                        whPickingTaskSkuMap.get(key) + Optional.ofNullable(existSku.getQuantity()).orElse(0));
                updateSkuList.add(updateSku);
                continue;
            }

            WhPickingTaskSku whPickingTaskSku = new WhPickingTaskSku();
            whPickingTaskSku.setTaskId(whPickingTask.getId());
            whPickingTaskSku.setSku(key);
            whPickingTaskSku.setQuantity(whPickingTaskSkuMap.get(key));// 需求数量-已扫描数量=就是需要拣货的数量
            whPickingTaskSku.setPickQuantity(0);
            whPickingTaskSku.setCreationDate(new Timestamp(System.currentTimeMillis()));
            whPickingTaskSku.setLastUpdateDate(new Timestamp(System.currentTimeMillis()));
            whPickingTaskSku.setStatus(PickingTaskSkuStatus.UNCOMPLETED.intCode());

            skus.add(whPickingTaskSku);
        }

        // 批量添加任务sku (不用排序 因为PDA拣货时 SKU会重新排序再传出去)
        whPickingTaskSkuService.batchCreateWhPickingTaskSku(skus);
        if (CollectionUtils.isNotEmpty(updateSkuList))
            whPickingTaskSkuService.batchUpdateWhPickingTaskSku(updateSkuList);
        logger.warn("创建拣货任务SKU列表------|");

        // 算出仓库数
        List<Integer> whApvIds = new ArrayList<Integer>();
        // 原始拣货任务类型是不是海外仓备货
        boolean asnPrepare = ApvGridUtils.checkAsnPrepare(sowStockouts.get(0));

        // 生成拣货任务详情
        List<WhApv> updateWhApvs = new ArrayList<WhApv>();
        List<WhFbaAllocation> updateAllocations = new ArrayList<WhFbaAllocation>();
        List<WhPickingTaskItem> items = new ArrayList<>();
        for (Integer key : whPickingTaskItemMap.keySet()) {

            WhApv updatewhApv = new WhApv();
            updatewhApv.setId(key);
            updatewhApv.setCompleteStatus(whPickingTask.getTaskNo());// 拣货任务号
            updateWhApvs.add(updatewhApv);
            //海外仓备货类型的拣货任务二次配货记录拣货任务号
            if (asnPrepare){
                WhFbaAllocation updateAllocation = new WhFbaAllocation();
                updateAllocation.setId(key);
                updateAllocation.setTaskNo(whPickingTask.getTaskNo());
                updateAllocations.add(updateAllocation);
            }

            WhPickingTaskItem whPickingTaskItem = new WhPickingTaskItem();
            whPickingTaskItem.setTaskId(whPickingTask.getId());
            whPickingTaskItem.setApvId(key);
            whPickingTaskItem.setApvNo(whPickingTaskItemMap.get(key));
            whPickingTaskItem.setCreateBy(DataContextHolder.getUserId());
            whPickingTaskItem.setCreatedDate(new Timestamp(System.currentTimeMillis()));
            items.add(whPickingTaskItem);

            whApvIds.add(key);
        }
        // 批量添加任务item详情
        whPickingTaskItemService.batchCreateWhPickingTaskItem(items);
        logger.warn("创建拣货任务item详情列表------|");

        // 修改为已分配任务方法
        if (asnPrepare) {
            //海外仓订单
            whFbaAllocationService.batchUpdateWhFbaAllocation(updateAllocations);
            for (WhFbaAllocation whApv : updateAllocations) {
                SystemLogUtils.FBAALLOCATIONLOG.log(whApv.getId(), "订单生成播种拣货任务",
                        new String[][] { { "详情", "添加任务号(" + whApv.getTaskNo() + ")记录" } });
            }
        }
        else if (!PickingTaskType.TEMU_MULTIPLEMULTIPLE.intCode().equals(sowStockouts.get(0).getOldTaskType())) {

            whApvService.batchUpdateWhApv(updateWhApvs);
            for (WhApv whApv : updateWhApvs) {
                WHAPV.log(whApv.getId(), "订单生成播种拣货任务",
                        new String[][] { { "详情", "添加任务号(" + whApv.getCompleteStatus() + ")到APV记录" } });
            }
        }

        //更新fba播种格子的任务号
        if (PickingTaskType.ASN_PREPARE.intCode().equals(sowStockouts.get(0).getOldTaskType())
            || PickingTaskType.TRANSFER_MULTIPLEMULTIPLE.intCode().equals(sowStockouts.get(0).getOldTaskType())) {
            // 查询格子
            List<WhApvGrid> whApvGrids = asnPrepareGridService.queryApvGrid(sowStockouts.get(0).getStockoutBox(),
                    LocationWarehouseType.OLD.intCode(), sowStockouts.get(0).getOldTaskType());
            if (CollectionUtils.isNotEmpty(whApvGrids)){
                WhPickingTask finalWhPickingTask = whPickingTask;
                whApvGrids.forEach(g->g.setSerialNumber(finalWhPickingTask.getTaskNo()));
                apvGridService.batchUpdateWhApvGrid(whApvGrids);
            }

        }


        WhApvBatchQueryCondition batchQuery = new WhApvBatchQueryCondition();
        batchQuery.setSerialNumber(whPickingTask.getTaskNo());
        List<WhApvBatch> existBatchList = whApvBatchDao.queryWhApvBatchList(batchQuery,null);
        Integer batchId = null;
        if (CollectionUtils.isEmpty(existBatchList)) {
            // 创建批次
            WhApvBatch whApvBatch = new WhApvBatch();
            whApvBatch.setBatchType(ApvBatchType.SINGLE_WAREHOUSE.intCode());

            // 单仓库默认完成
            whApvBatch.setBatchStatus(ApvBatchStatus.COMPLETED.intCode());

            whApvBatch.setSerialNumber(whPickingTask.getTaskNo());
            whApvBatchDao.createWhApvBatch(whApvBatch);
            logger.warn("创建批次createWhApvBatch------|");
            batchId = whApvBatch.getId();
        }
        else {
            batchId = existBatchList.get(0).getId();
        }

        // 批次内的仓库
        List<WhApvBatchItem> whApvBatchItems = new ArrayList<WhApvBatchItem>();

        WhApvBatchItem whApvBatchItem = new WhApvBatchItem();
        whApvBatchItem.setBatchId(batchId);
        whApvBatchItem.setWarehouseId(-1);

        // 单仓库默认完成
        whApvBatchItem.setItemStatus(ApvBatchItemStatus.COMPLETED.intCode());

        whApvBatchItems.add(whApvBatchItem);

        whApvBatchItemDao.batchCreateWhApvBatchItem(whApvBatchItems);
        logger.warn("创建批次batchCreateWhApvBatchItem------|");

        // 批次内的订单
        List<WhApvBatchDetail> whApvBatchDetails = new ArrayList<WhApvBatchDetail>();
        for (Integer apvId : whApvIds) {
            WhApvBatchDetail whApvBatchDetail = new WhApvBatchDetail();
            whApvBatchDetail.setBatchId(batchId);
            whApvBatchDetail.setApvId(apvId);

            whApvBatchDetails.add(whApvBatchDetail);
        }

        whApvBatchDetailDao.batchCreateWhApvBatchDetail(whApvBatchDetails);
        logger.warn("创建批次batchCreateWhApvBatchDetail------|");

        sowStockoutService.batchCreateSowStockout(sowStockouts);
    }

    @Override
    public boolean allocationPicking(Integer id, Integer pickingUser) {
        boolean result = false;
        WhPickingTaskQueryCondition query = new WhPickingTaskQueryCondition();
        query.setId(id);
        query.setTaskStatus(PickingTaskStatus.UNRECEIVED.intCode());
        List<WhPickingTask> tasks = queryWhPickingTasks(query, new Pager(0, 1));
        // 当前任务非 未领取状态
        if (CollectionUtils.isEmpty(tasks)){
            return result;
        }
        // 当前任务 认领人=当前分配人员
        WhPickingTask pick = tasks.get(0);
        if (pickingUser.equals(pick.getReceivePerson())){
            return result;
        }
        this.updateToAllocationInventoryPicking(pick,pickingUser);
        return true;
    }
    /**
     * 分配盘拣货任务-->更新状态,拣货人
     */
    public void updateToAllocationInventoryPicking(WhPickingTask task, Integer pickingUser) {

        WhPickingTask updateTask = new WhPickingTask();
        updateTask.setId(task.getId());
        updateTask.setTaskStatus(task.getTaskStatus());
        updateTask.setReceivePerson(pickingUser);
        int returnInt = whPickingTaskDao.updatePickingTaskUser(updateTask);
        Assert.state(returnInt == 1, "分配失败，该拣货任务状态改变");
        SaleUser originalBuyer = new SaleUser();
        SaleUser newBuyer = null;
        if (task.getReceivePerson() != null && task.getReceivePerson()!=0) {
            originalBuyer = CacheUtils.get(CacheName.USER_CACHE, String.valueOf(task.getReceivePerson()), SaleUser.class);
        }
        if (pickingUser != null) {
            newBuyer = CacheUtils.get(CacheName.USER_CACHE, String.valueOf(pickingUser), SaleUser.class);
        }
        PICKINGLOG.log(task.getId(), "分配拣货任务", new String[][]{{"原领取人", originalBuyer.getUsername()+"-"+originalBuyer.getName()},
                {"新领取人", newBuyer.getUsername()+"-"+newBuyer.getName()}});
    }

    /**
     * 查看用户是否存在已分配的任务·
     */
    @Override
    public WhPickingTask existsAssigningTask(List<WhPickingTask> whPickingTasks,Integer pickingTaskType){
        if (pickingTaskType==null||CollectionUtils.isEmpty(whPickingTasks)) {
            return null;
        }
        //已领取的直接返回
        List<WhPickingTask> whPickingTasksList = whPickingTasks.stream().filter(s -> PickingTaskStatus.RECEIVED.intCode().equals(s.getTaskStatus())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(whPickingTasksList)) {
            return whPickingTasksList.get(0);
        }
        //过滤是拣货区不去没有打印的任务
        List<WhPickingTask> pickingTasks = whPickingTasks.stream().filter(t -> (PickingTaskType.AISLESINGLESINGLE.intCode().equals(t.getTaskType()) ||
                (PickingTaskType.AISLESINGLEMULTIPLE.intCode().equals(t.getTaskType())))).collect(Collectors.toList());

        whPickingTasks.removeAll(pickingTasks);

        if (CollectionUtils.isEmpty(whPickingTasks)) {
            return null;
        }
        //过滤带GPSR标签的任务
        whPickingTasks = whPickingTasks.stream().filter(t -> !PickingTaskTag.GPSR.intCode().equals(t.getIsAsn())).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(whPickingTasks)) {
            return null;
        }
        PdaPickingType pickingType = PdaPickingType.build(pickingTaskType.toString());
        
        List<Integer> codes=new ArrayList<>();
        switch(pickingType){
            case PACMULTISPECIES:
                codes.add(PickingTaskType.MULTIPLEMULTIPLE.intCode());
                break;
            case PACNOTMULTISPECIES:
                codes.addAll(Arrays.asList(PickingTaskType.SINGLESINGLE.intCode(),PickingTaskType.SINGLEMULTIPLE.intCode(),PickingTaskType.SINGLEVERIETY.intCode()));
                break; 
            case PAC_RU_NOT_MULTISPECIES:
                codes.addAll(Arrays.asList(PickingTaskType.OPTIMAL_RU_SS.intCode(),PickingTaskType.OPTIMAL_RU_SM.intCode(),PickingTaskType.OPTIMAL_RU_SV.intCode()));
                break;
            case PAC_RU_MULTISPECIES:
                codes.addAll(Arrays.asList(PickingTaskType.OPTIMAL_RU_MM.intCode()));
                break;
            case EXPRESSAGE:
                codes.addAll(Arrays.asList(PickingTaskType.EXPRESSSINGLESINGLE.intCode(),PickingTaskType.EXPRESSSINGLEMULTIPLE.intCode(),PickingTaskType.EXPRESSMULTIPLEMULTIPLE.intCode()));
                break;
            case FBATASK:
                codes.addAll(Arrays.asList(PickingTaskType.FBASINGLESINGLE.intCode(),PickingTaskType.FBASINGLEMULTIPLE.intCode(),PickingTaskType.FBAMULTIPLEMULTIPLE.intCode()));
                break;
            case  ASN_FBA_TASK:
                codes.add(PickingTaskType.ASN_PREPARE.intCode());
            case  ASN_FIRST_TASK:
                codes.add(PickingTaskType.ASN_FIRST.intCode());
            case  OVERSIZE_MULTISPECIES:
                codes.add(PickingTaskType.OVERSIZE_MULTIPLE_PRODUCT.intCode());
            case OVERSIZE_NOT_MULTISPECIES:
                codes.addAll(Arrays.asList(PickingTaskType.OVERSIZE_SINGLE.intCode(),PickingTaskType.OVERSIZE_MULTIPLE.intCode(),PickingTaskType.OVERSIZE_SINGLE_PRODUCT.intCode()));
                break;
        }

       List<WhPickingTask> whPickingTaskList = whPickingTasks.stream().filter(k -> {
            if(pickingTaskType == 11 || pickingTaskType == 12){
                return codes.contains(k.getTaskType());
            }
           if (pickingTaskType == 13 || pickingTaskType == 14)
               return codes.contains(k.getTaskType());
           if (k.getIsAsn() != null && k.getIsAsn() != PickingTaskTag.OPTIMAL.intCode()
                   && k.getIsAsn() != PickingTaskTag.OPTIMAL_JB.intCode()) {
               return codes.contains(k.getTaskType());
           }
            return false;
        }).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(whPickingTaskList)) {
            return whPickingTaskList.get(0);
        }
        return null;
    }

    @Override
    public List<WhPickingTaskSku> querySecondaryDistributionSku(String taskNo,List<Integer> taskType) {
        return whPickingTaskDao.querySecondaryDistributionSku(taskNo,taskType);
    }

    @Override
    public ResponseJson verifyBoxNo(AndroidProductDo domain) {
        ResponseJson responseJson=new ResponseJson();
        responseJson.setStatus(StatusCode.FAIL);
        try {
            if (StringUtils.isNotBlank(domain.getRelationNo()) && StringUtils.isNotBlank(domain.getBoxNo()) && domain.getBoxNo().contains("A")) {
                WhPickingTaskQueryCondition whPickingTaskQueryCondition = new WhPickingTaskQueryCondition();
                whPickingTaskQueryCondition.setId(Integer.parseInt(domain.getRelationNo()));
                WhPickingTask whPickingTask = whPickingTaskDao.queryWhPickingTask(whPickingTaskQueryCondition);
                if (whPickingTask == null) {
                    responseJson.setStatus(StatusCode.SUCCESS);
                    return responseJson;
                }
                domain.setTaskType(whPickingTask.getTaskType());
                domain.setIsAsn(whPickingTask.getIsAsn());
                PickingTaskType apvOrderType = PickingTaskType.build(whPickingTask.getTaskType() + "");
                String boxNo = "";
                // 避免调整pda，特殊处理
                if (StringUtils.startsWith(domain.getBoxNo(),BoxType.ASN_FIRST.getShortCode()+"A")){
                    boxNo = BoxType.ASN_FIRST.getShortCode();
                }else{
                     boxNo = domain.getBoxNo().substring(0, domain.getBoxNo().indexOf("A"));
                }
                switch (apvOrderType) {
                    case SINGLEMULTIPLE:
                    case EXPRESSSINGLEMULTIPLE:
                    case  JIT_SINGLEMULTIPLE:
                        if (!BoxType.SINGLE_PRODUCT.getShortCode().equals(boxNo)) {
                            responseJson.setMessage("请绑定普通多件周转框[JHDJ]！");
                            return responseJson;
                        }
                        break;
                    case MULTIPLEMULTIPLE:
                    case EXPRESSMULTIPLEMULTIPLE:
                    case JIT_MULTIPLEMULTIPLE:
                        if (!BoxType.MULTI_PRODUCT.getShortCode().equals(boxNo)) {
                            responseJson.setMessage("请绑定普通多品周转框[JHDP]！");
                            return responseJson;
                        }
                        break;
                    case ASN_PREPARE:{
                        if (!BoxType.FBA_FIRST.getShortCode().equals(boxNo)) {
                            responseJson.setMessage("请绑定FBA头程拣货周转筐[JHDPF]！");
                            return responseJson;
                        }
                        break;
                    }
                    case ASN_FIRST:
                        if (!BoxType.ASN_FIRST.getShortCode().equals(boxNo)) {
                            responseJson.setMessage("请绑定海外仓头程拣货周转筐[JHDPAA]！");
                            return responseJson;
                        }
                        break;
                    case TRANSFER_SINGLEMULTIPLE:
                        if (!BoxType.SINGLE_TRANSFER_WAREHOUSE.getShortCode().equals(boxNo)) {
                            responseJson.setMessage("请绑定中转仓多件周转筐[JHDJZ]！");
                            return responseJson;
                        }
                        break;
                    case TRANSFER_MULTIPLEMULTIPLE:{
                        if (!BoxType.TRANSFER_WAREHOUSE.getShortCode().equals(boxNo)) {
                            responseJson.setMessage("请绑定中转仓拣货周转筐[JHDPZ]！");
                            return responseJson;
                        }
                        break;
                    }
                    case TEMU_MULTIPLEMULTIPLE:{
                        if (!BoxType.TEMU_FIRST.getShortCode().equals(boxNo)) {
                            responseJson.setMessage("请绑定拼多多备货周转筐[JHDPT]！");
                            return responseJson;
                        }
                        break;
                    }
                    case OPTIMAL_RU_MM: {
                        if (!BoxType.MULTI_PRODUCT_JB.getShortCode().equals(boxNo)) {
                            responseJson.setMessage("请绑定集包多品周转筐[JHDPJB]！");
                            return responseJson;
                        }
                        break;
                    }
                    case OPTIMAL_RU_SM: {
                        if (!BoxType.SINGLE_PRODUCT_JB.getShortCode().equals(boxNo)) {
                            responseJson.setMessage("请绑定集包多件周转筐[JHDJJB]！");
                            return responseJson;
                        }
                        break;
                    }
                    case JIT_ASN_MULTIPLE: {
                        if (!BoxType.JIT_ASN_MM.getShortCode().equals(boxNo)) {
                            responseJson.setMessage("请绑定仓发多品周转筐[JHDPCF]！");
                            return responseJson;
                        }
                        break;
                    }
                    case JIT_ASN_SINGLESINGLE: {
                        if (!BoxType.JIT_ASN_SM.getShortCode().equals(boxNo)) {
                            responseJson.setMessage("请绑定仓发单品周转筐[JHDPCF]！");
                            return responseJson;
                        }
                        break;
                    }
                    default:{
                        if (BoxType.getShortCodeList().contains(boxNo)) {
                            responseJson.setMessage(String.format("%s类型请绑定对应的周转筐！",PickingTaskType.getNameByCode(whPickingTask.getTaskType().toString())));
                            return responseJson;
                        }
                    }
                }
            }
        }catch (Exception e) {
           responseJson.setMessage(e.getMessage());
           logger.error("校验拣货周转筐异常！"+e);
           return responseJson;
        }
        responseJson.setStatus(StatusCode.SUCCESS);
        return responseJson;
    }

    // 完成拣货带事务
    @Override
    public void doCompletePickingTask(AndroidProductDo domain,List<WhFbaAllocation> whFbaAllocationList,List<Integer> avpIds) {
        // 修改拣货状态为已完成
        WhPickingTask whPickingTask = new WhPickingTask();
        whPickingTask.setId(domain.getTaskId());
        whPickingTask.setTaskStatus(PickingTaskStatus.COMPLETED.intCode());
        whPickingTask.setPickingEndDate(new Timestamp(System.currentTimeMillis()));
        int returnInt = completePickingTask(whPickingTask, domain.getPickStatus());
        if (!(returnInt > 0)) {
            throw new RuntimeException("修改拣货状态为已完成-失败！");
        }
        WhPickingTaskQueryCondition whPickingTaskQueryCondition = new WhPickingTaskQueryCondition();
        whPickingTaskQueryCondition.setId(domain.getTaskId());
        WhPickingTask tempWhPickingTask = queryWhPickingTask(whPickingTaskQueryCondition);

        logger.info("周转筐：" + domain.getBzycBoxNo() + " 类型：" + tempWhPickingTask.getTaskType() + "--"
                + PickingTaskType.getNameByCode(tempWhPickingTask.getTaskType() + ""));
        // 删除数据 sow_stockout
        if (PickingTaskType.BZYC.intCode() == tempWhPickingTask.getTaskType()
                || PickingTaskType.TRANSFER_BZYC.intCode().equals(tempWhPickingTask.getTaskType())
                || PickingTaskType.JIT_ASN_BZYC.intCode().equals(tempWhPickingTask.getTaskType())
                || PickingTaskType.TEMU_BZYC.intCode().equals(tempWhPickingTask.getTaskType())
                || PickingTaskType.JIT_BZYC.intCode().equals(tempWhPickingTask.getTaskType())
        ) {
            if (StringUtils.isBlank(domain.getBzycBoxNo())) {
                throw new RuntimeException("参数bzycBoxNo为空！");
            }
            // 删除数据 sow_stockout
            sowStockoutService.deleteSowStockoutByStockoutBox(domain.getBzycBoxNo());
        }
        // 多品多件 或者播种异常 需要在这里记录拣货完成 ApvTrack
        //TODO 海外仓备货是否需要记录拣货完成
        if (PickingTaskType.MULTIPLEMULTIPLE.intCode().equals(tempWhPickingTask.getTaskType())
                || PickingTaskType.OPTIMAL_RU_MM.intCode().equals(tempWhPickingTask.getTaskType())
                || PickingTaskType.EXPRESSMULTIPLEMULTIPLE.intCode().equals(tempWhPickingTask.getTaskType())
                || PickingTaskType.FBAMULTIPLEMULTIPLE.intCode().equals(tempWhPickingTask.getTaskType())
                || PickingTaskType.BZYC.intCode().equals(tempWhPickingTask.getTaskType())
                || PickingTaskType.getTransferIntCode().contains(tempWhPickingTask.getTaskType())
                || PickingTaskType.getJITIntCode().contains(tempWhPickingTask.getTaskType())
                || PickingTaskType.getJitAsnIntCode().contains(tempWhPickingTask.getTaskType())
                || PickingTaskType.JIT_MULTIPLEMULTIPLE.intCode().equals(tempWhPickingTask.getTaskType())
                || PickingTaskType.JIT_BZYC.intCode().equals(tempWhPickingTask.getTaskType())
        ) {

            addApvTrackPickInfo(domain.getTaskId(), tempWhPickingTask.getTaskNo());
        }
        // 记录apv拣货完成
        WhPickingTaskItemQueryCondition whPickingTaskItemQueryCondition = new WhPickingTaskItemQueryCondition();
        whPickingTaskItemQueryCondition.setTaskId(whPickingTask.getId());
        List<WhPickingTaskItem> whPickingTaskItems = whPickingTaskItemService
                .queryWhPickingTaskItems(whPickingTaskItemQueryCondition, null);
        if (CollectionUtils.isEmpty(whPickingTaskItems))
            return;
        List<Integer> orderIds = whPickingTaskItems.stream().map(WhPickingTaskItem::getApvId)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(orderIds))
            return;
        // 拼多多
        if (PickingTaskType.getPddIntCode().contains(tempWhPickingTask.getTaskType())) {
            temuTaskService.doCompleteTemuTask(tempWhPickingTask,orderIds);
        }// 中转仓
        else if (PickingTaskType.getTransferIntCode().contains(tempWhPickingTask.getTaskType())
                || PickingTaskType.getJITIntCode().contains(tempWhPickingTask.getTaskType())
                || PickingTaskType.getJitAsnIntCode().contains(tempWhPickingTask.getTaskType())) {

            Map<Boolean, List<Integer>> partitionedIds = whPickingTaskItems.stream()
                    .filter(item -> StringUtils.isNotBlank(item.getApvNo()))
                    .collect(Collectors.partitioningBy(
                            item -> item.getApvNo().startsWith("YSTN"),
                            Collectors.mapping(WhPickingTaskItem::getApvId, Collectors.toList())
                    ));

            List<Integer> idList= partitionedIds.getOrDefault(true, Collections.emptyList());
            List<Integer> pddIdList = partitionedIds.getOrDefault(false, Collections.emptyList());

            if (CollectionUtils.isNotEmpty(pddIdList)) {
                temuTaskService.doCompleteTemuTask(tempWhPickingTask, pddIdList);
            }

            if (CollectionUtils.isEmpty(idList)) {
                return;
            }

            WhFbaAllocationQueryCondition queryCondition = new WhFbaAllocationQueryCondition();
            queryCondition.setIds(idList);
            List<WhFbaAllocation> whFbaAllocations = whFbaAllocationService.queryWhFbaAllocationAndItems(queryCondition, null);
            if (CollectionUtils.isEmpty(whFbaAllocations))
                return;
            whFbaAllocations.removeIf(f -> f.getStatus().equals(AsnPrepareStatus.CANCEL.intCode()));
            if (CollectionUtils.isEmpty(whFbaAllocations))
                return;
            if (PickingTaskType.ASN_PREPARE.intCode().equals(tempWhPickingTask.getIsAsn())) {
                whFbaAllocationList.addAll(
                        updateWhFbaAllocationStatus(whFbaAllocations, AsnPrepareStatus.WAITING_GRID_CONFIRM.intCode(), null));
                // fba的单通知给OMS
                whFbaAllocationService.pushFbaWaitGridStatusToOMS(whFbaAllocations);
            }
            else if (PickingTaskType.TRANSFER_SINGLESINGLE.intCode().equals(tempWhPickingTask.getTaskType())
                    || PickingTaskType.TRANSFER_SINGLEMULTIPLE.intCode().equals(tempWhPickingTask.getTaskType())
                    || PickingTaskType.JIT_ASN_SINGLESINGLE.intCode().equals(tempWhPickingTask.getTaskType())) {
                // 更新状态
                whFbaAllocationList.addAll(updateWhFbaAllocationStatus(whFbaAllocations,
                        AsnPrepareStatus.CHECK_PRINT.intCode(), tempWhPickingTask));
            }
            else if (PickingTaskType.TRANSFER_MULTIPLEMULTIPLE.intCode().equals(tempWhPickingTask.getTaskType())
                    || PickingTaskType.JIT_ASN_MULTIPLE.intCode().equals(tempWhPickingTask.getTaskType())
                    || PickingTaskType.JIT_ASN_BZYC.intCode().equals(tempWhPickingTask.getTaskType())) {
                // 更新状态
                whFbaAllocationList.addAll(updateWhFbaAllocationStatus(whFbaAllocations,
                        AsnPrepareStatus.WAITING_GRID.intCode(), tempWhPickingTask));
            }
            addLog(whFbaAllocationList.stream().map(WhFbaAllocation::getId).collect(Collectors.toList()),
                    tempWhPickingTask.getTaskNo(), SystemLogUtils.FBAALLOCATIONLOG);
        }
        else {
            // 本地仓
            avpIds.addAll(whPickingTaskItems.stream().map(w -> w.getApvId()).collect(Collectors.toList()));
            addLog(avpIds, tempWhPickingTask.getTaskNo(), SystemLogUtils.APVLOG);
        }
    }

    // 更新中转仓状态
    private List<WhFbaAllocation> updateWhFbaAllocationStatus(List<WhFbaAllocation> whFbaAllocations, Integer status,
            WhPickingTask pickingTask) {
        List<WhFbaAllocation> updateWhFbaAllocations = new ArrayList<>();
        List<String> deleteRelevantNos = new ArrayList<String>();
        
        boolean jitSingle = pickingTask != null
                && PickingTaskType.JIT_ASN_SINGLESINGLE.intCode().equals(pickingTask.getTaskType());
        boolean jitSingleOrYc = jitSingle
                || pickingTask != null && PickingTaskType.JIT_ASN_BZYC.intCode().equals(pickingTask.getTaskType());
        Map<String, Integer> allotMap = new HashMap<>();
        for (WhFbaAllocation allocation : whFbaAllocations) {
            WhFbaAllocation whFbaAllocation = new WhFbaAllocation();
            whFbaAllocation.setId(allocation.getId());
            whFbaAllocation.setStatus(status);
            // 拣货成功推送拣货完成时间到OMS
            whFbaAllocation.setFbaNo(allocation.getFbaNo());
            if (jitSingleOrYc) {
                boolean pickStockOut = Optional.ofNullable(allocation.getItems()).orElse(new ArrayList<>()).stream()
                        .anyMatch(i -> i.getAllotQuantity() != null && i.getAllotQuantity() != 0
                                && !ObjectUtils.equals(i.getAllotQuantity(), i.getPickQuantity()));
                boolean pickZero = Optional.ofNullable(allocation.getItems()).orElse(new ArrayList<>()).stream()
                        .allMatch(i -> i.getPickQuantity() == null || i.getPickQuantity() == 0);
                if (allocation.getIsAsn() != null && allocation.getIsAsn()
                        && CollectionUtils.isNotEmpty(allocation.getItems()) && pickStockOut) {
                    whFbaAllocation.setStatus(AsnPrepareStatus.PICK_STOCK_OUT.intCode());
                    if (pickZero) {
                        whFbaAllocation.setStatus(AsnPrepareStatus.CANCEL.intCode());
                        whFbaAllocationService.sendMsg(allocation);
                        deleteRelevantNos.add(allocation.getFbaNo());
                    }
                    allocation.buildGroupItems().forEach(item -> {
                        Integer pickedQuantity = item.getPickQuantity() == null ? 0 : item.getPickQuantity();
                        Integer saleQuantity = item.getSkuQuantity() == null ? 0 : item.getSkuQuantity();
                        if (pickedQuantity < saleQuantity) {
                            Integer allotQuantity = allotMap.get(item.getProductSku()) == null ? 0
                                    : allotMap.get(item.getProductSku());
                            allotMap.put(item.getProductSku(), allotQuantity + (saleQuantity - pickedQuantity));
                        }
                    });

                }
            }
            updateWhFbaAllocations.add(whFbaAllocation);
        }

        // 仓发单品拣货缺货退已分配到可用
        if (jitSingle && MapUtils.isNotEmpty(allotMap)
                && !transferUpdateStockService.transferPickReturn(new ArrayList<>(allotMap.keySet()), null, allotMap,
                        pickingTask.getTaskNo(), whFbaAllocations)) {
            throw new RuntimeException("单品拣货缺货操作库存失败!");
        }
        
        whFbaAllocationService.batchUpdateWhFbaAllocation(updateWhFbaAllocations);
        updateWhFbaAllocations.forEach(f -> SystemLogUtils.FBAALLOCATIONLOG.log(f.getId(), "完成拣货",
                new String[][] { { "状态变更", AsnPrepareStatus.getNameByCode(f.getStatus() + "") } }));
        // 取消的发货单清除出库库存分配关联表
        whApvOutStockChinaService.deleteAndCreateCancelByRelevantNos(deleteRelevantNos);
        return updateWhFbaAllocations;
    }
    // 记录日志
    private void addLog(List<Integer> apvIds, String taskNo, SystemLogUtils systemLogUtils ) {
        for(Integer id:apvIds) {
            systemLogUtils.log(id, "完成拣货", new String[][]{
                    {"详情", "任务号 (" + taskNo + ") 拣货完成"}});
        }
    }

    /**
     * 二次配货
     * @param domain
     * @return
     */
    @Override
    public ResponseJson createOrReceiveBZYCTask(AndroidProductDo domain) {
        ResponseJson responseJson = new ResponseJson();
        responseJson.setStatus(StatusCode.FAIL);

        if (StringUtils.isBlank(domain.getBzycBoxNo())) {
            responseJson.setMessage("参数bzycBoxNo为空！");
            return responseJson;
        }

        WhBoxQueryCondition boxCondition = new WhBoxQueryCondition();
        boxCondition.setBoxNo(domain.getBzycBoxNo());
        WhBox whBox = whBoxService.queryWhBox(boxCondition);

        if (whBox == null){
            responseJson.setMessage("周转筐不存在！");
            return responseJson;
        }

        if (domain.getReceivePerson() == null || domain.getReceivePerson() == 0) {
            responseJson.setMessage("参数receivePerson为空或者为空！");
            return responseJson;
        }

        // 根据占用周转筐查询
        WhPickingTaskQueryCondition taskQuery = new WhPickingTaskQueryCondition();
        taskQuery.setTaskTypeList(Arrays.asList(PickingTaskType.BZYC.intCode(), PickingTaskType.TRANSFER_BZYC.intCode(),
                PickingTaskType.TEMU_BZYC.intCode(), PickingTaskType.JIT_BZYC.intCode(),
                PickingTaskType.JIT_ASN_BZYC.intCode()));
        taskQuery.setYcBoxNo(domain.getBzycBoxNo());
        List<WhPickingTask> whPickingTasks = queryWhPickingTasks(taskQuery, null);

        // 根据绑定周转筐查询
        if (CollectionUtils.isEmpty(whPickingTasks)) {
            taskQuery.setYcBoxNo(null);
            taskQuery.setBoxNo(domain.getBzycBoxNo());
            whPickingTasks = queryWhPickingTasks(taskQuery, null);
        }
        
        if (CollectionUtils.isEmpty(whPickingTasks)) {
            SowStockoutQueryCondition query = new SowStockoutQueryCondition();
            query.setStockoutBox(domain.getBzycBoxNo());
            List<SowStockout> sowStockouts = sowStockoutService.querySowStockouts(query, null);
            if (sowStockouts.size() <= 0) {
                responseJson.setMessage("请检查周转框是否已被使用！");
                return responseJson;
            }
            doSowStockOut(null, sowStockouts);
            whPickingTasks = queryWhPickingTasks(taskQuery, null);
        }

        if (CollectionUtils.isEmpty(whPickingTasks) || whPickingTasks.get(0) == null) {
            responseJson.setMessage("请检查周转框是否已被使用！");
            return responseJson;
        }

        WhPickingTask whPickingTask = whPickingTasks.get(0);

        if (whPickingTask.getReceivePerson() != null
                && !domain.getReceivePerson().equals(whPickingTask.getReceivePerson())) {
            responseJson.setMessage("任务已被领取！");
            return responseJson;
        }

        if (PickingTaskType.TRANSFER_BZYC.intCode().equals(whPickingTask.getTaskType())
                && !PickingTaskType.TRANSFER_BZYC.intCode().equals(domain.getTaskType())) {
            responseJson.setMessage("请从中转仓二次配货菜单进去！");
            return responseJson;
        }
        if (PickingTaskType.TEMU_BZYC.intCode().equals(whPickingTask.getTaskType())
                && !PickingTaskType.TEMU_BZYC.intCode().equals(domain.getTaskType())) {
            responseJson.setMessage("请从拼多多二次配货菜单进去！");
            return responseJson;
        }
        if (PickingTaskType.BZYC.intCode().equals(whPickingTask.getTaskType())
                && !PickingTaskType.BZYC.intCode().equals(domain.getTaskType())) {
            responseJson.setMessage("请从普通二次配货菜单进去！");
            return responseJson;
        }
        if (PickingTaskType.JIT_BZYC.intCode().equals(whPickingTask.getTaskType())
                && !PickingTaskType.BZYC.intCode().equals(domain.getTaskType())) {
            responseJson.setMessage("JIT二次配货请从普通二次配货菜单进去！");
            return responseJson;
        }
        if (PickingTaskType.JIT_ASN_BZYC.intCode().equals(whPickingTask.getTaskType())
                && !PickingTaskType.JIT_ASN_BZYC.intCode().equals(domain.getTaskType())) {
            responseJson.setMessage("请从仓发二次配货菜单进去！");
            return responseJson;
        }

        // 先修改拣货任务已领取并绑定异常周转筐
        if (whPickingTask.getTaskStatus().equals(PickingTaskStatus.UNRECEIVED.intCode())) {
            WhPickingTask updateTask = new WhPickingTask();
            updateTask.setId(whPickingTask.getId());
            updateTask.setTaskStatus(PickingTaskStatus.RECEIVED.intCode());
            updateTask.setReceivePerson(domain.getReceivePerson());
            updateTask.setReceiveDate(new Timestamp(System.currentTimeMillis()));
            this.updateWhPickingTask(updateTask);
            PICKINGLOG.log(whPickingTask.getId(), "领取拣货任务-修改状态为已领取",
                    new String[][] { { "原状态", PickingTaskStatus.UNRECEIVED.getName() } });

            //查询播种异常管理，写入处理人
            this.updateGridException(whPickingTask);

            if (StringUtils.isBlank(whBox.getRelationNo())
                    || Objects.equals(whBox.getStatus(), BoxStatus.ALREADY_USED.intCode())) {
                String isAsnStr = whPickingTask.getIsAsn() == null ? "" : whPickingTask.getIsAsn().toString();
                String content = "<a target='_blank' href='" + IpUtils.getLocalIpUrl()
                        + "/picking/taskItems/getWhPickingTasks?query.taskId=" + whPickingTask.getId()
                        + "&query.taskType=" + whPickingTask.getTaskType() + "&query.isAsn=" + isAsnStr + "'>"
                        + whPickingTask.getId() + "</a>";
                // 绑定异常播种周转框
                whBoxService.updateWhBoxOfBinding(domain.getBzycBoxNo(), String.valueOf(whPickingTask.getId()),
                        new String[][] { { "绑定拣货任务号", content } });
            }

        }

        List<WhPickingTaskSkuLocation> tempList;
        String o = StringRedisUtils.get(RedisConstant.BZYC_KEY + whPickingTask.getTaskNo());
        if (StringUtils.isNotBlank(o) && StringUtils.contains(o,"boxNo")) {
            tempList = JSONObject.parseArray(o, WhPickingTaskSkuLocation.class);
            responseJson.setMessage(JSON.toJSONString(tempList));
            responseJson.setStatus(StatusCode.SUCCESS);
            return responseJson;
        }
        WhPickingTaskSkuQueryCondition tempObject = new WhPickingTaskSkuQueryCondition();
        tempObject.setTaskId(whPickingTask.getId());
        tempObject.setIsAsn(whPickingTask.getIsAsn());
        if (PickingTaskType.ASN_PREPARE.intCode().equals(whPickingTask.getIsAsn())
                || PickingTaskType.TEMU_BZCY.intCode().equals(whPickingTask.getIsAsn())
                || PickingTaskType.JIT_ASN_BZYC.intCode().equals(whPickingTask.getTaskType())
                || PickingTaskType.TRANSFER_BZYC.intCode().equals(whPickingTask.getTaskType())) {
            tempList = whPickingTaskSkuService.queryBZYCSkuLocations(tempObject, null);
        }
        else {
            tempList = whPickingTaskSkuService.queryWhPickingTaskAndSkuLocations(tempObject, null);
        }

        if (CollectionUtils.isEmpty(tempList)) {
            responseJson.setMessage("任务详情为空，请联系管理员处理！");
            return responseJson;
        }
        // 二配只展示未捡够的sku
        tempList = tempList.stream().filter(t -> t.getPickQuantity() < t.getQuantity()).map(t -> {
            t.setQuantity(t.getQuantity() - t.getPickQuantity());
            t.setPickQuantity(0);
            t.setStatus(PickingTaskSkuStatus.UNCOMPLETED.intCode());
            return t;
        }).collect(Collectors.toList());
        // 海外仓二次配货，返回pda任务类型 海外仓备货，便于拣货走海外仓多品拣货
        if (whPickingTask.getIsAsn() != null
                && PickingTaskType.getTransferIntCode().contains(whPickingTask.getIsAsn())) {

            tempList.forEach(t -> t.setTaskType(whPickingTask.getIsAsn()));
        }
        StringRedisUtils.set(RedisConstant.BZYC_KEY + whPickingTask.getTaskNo(),
                JSONObject.toJSONString(tempList), 3 * 24 * 3600L);
        responseJson.setMessage(JSON.toJSONString(tempList));
        responseJson.setStatus(StatusCode.SUCCESS);

        return responseJson;
    }

    private void updateGridException(WhPickingTask whPickingTask) {
        if (whPickingTask == null || StringUtils.isBlank(whPickingTask.getTaskNo()))
            return;
        GridExceptionQueryCondition query = new GridExceptionQueryCondition();
        query.setNewTaskNo(whPickingTask.getTaskNo());
        List<GridException> exceptionList = gridExceptionService.queryGridExceptions(query, null);
        if (CollectionUtils.isEmpty(exceptionList))
            return;
        List<GridException> updatedExceptionList = new ArrayList<>();
        exceptionList.forEach(e -> {
            GridException updateException = new GridException();
            updateException.setId(e.getId());
            updateException.setHandleBy(DataContextHolder.getUserId());
            updateException.setHandleDate(new Timestamp(System.currentTimeMillis()));
            updateException.setStatus(GridExceptionStatus.COMPLETE.intCode());
            updatedExceptionList.add(updateException);
        });
        gridExceptionService.batchUpdateGridException(updatedExceptionList);
    }
}