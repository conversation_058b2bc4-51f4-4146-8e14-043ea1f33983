package com.estone.picking.service.impl;

import com.estone.common.CacheName;
import com.estone.common.util.CacheUtils;
import com.estone.common.util.ComparatorUtils;
import com.estone.common.util.SystemLogUtils;
import com.estone.picking.bean.LocationCheckTask;
import com.estone.picking.bean.LocationCheckTaskItem;
import com.estone.picking.bean.LocationCheckTaskQueryCondition;
import com.estone.picking.dao.LocationCheckTaskDao;
import com.estone.picking.dao.LocationCheckTaskItemDao;
import com.estone.picking.domain.LocationCheckTaskDo;
import com.estone.picking.enums.LocationCheckItemStatus;
import com.estone.picking.enums.LocationCheckStatus;
import com.estone.picking.service.LocationCheckTaskService;
import com.estone.system.user.bean.SaleUser;
import com.estone.warehouse.bean.WhStock;
import com.whq.tool.component.Pager;
import com.whq.tool.component.StatusCode;
import com.whq.tool.context.DataContextHolder;
import com.whq.tool.exception.BusinessException;
import com.whq.tool.exception.DBErrorCode;
import com.whq.tool.json.ResponseJson;
import com.whq.tool.sqler.SqlerException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Service("locationCheckTaskService")
@Slf4j
public class LocationCheckTaskServiceImpl implements LocationCheckTaskService {
    @Resource
    private LocationCheckTaskDao locationCheckTaskDao;
    @Resource
    private LocationCheckTaskItemDao locationCheckTaskItemDao;

    @Override
    public LocationCheckTask getLocationCheckTask(Integer id) {
        LocationCheckTask locationCheckTask = locationCheckTaskDao.queryLocationCheckTask(id);
        return locationCheckTask;
    }

    @Override
    public LocationCheckTask getLocationCheckTaskDetail(Integer id) {
        LocationCheckTask locationCheckTask = locationCheckTaskDao.queryLocationCheckTask(id);
        // 关联查询
        return locationCheckTask;
    }

    @Override
    public LocationCheckTask queryLocationCheckTask(LocationCheckTaskQueryCondition query) {
        Assert.notNull(query, "query is null!");
        LocationCheckTask locationCheckTask = locationCheckTaskDao.queryLocationCheckTask(query);
        return locationCheckTask;
    }

    @Override
    public List<LocationCheckTask> queryLocationCheckTasks(LocationCheckTaskQueryCondition query, Pager pager) {
        Assert.notNull(query, "query is null!");
        if (pager != null && pager.isQueryCount()) {
            int count = locationCheckTaskDao.queryLocationCheckTaskCount(query);
            pager.setTotalCount(count);
            if (count == 0) {
                return new ArrayList<LocationCheckTask>();
            }
        }
        List<LocationCheckTask> locationCheckTasks = locationCheckTaskDao.queryLocationCheckTaskList(query, pager);
        return locationCheckTasks;
    }

    @Override
    public void createLocationCheckTask(LocationCheckTask locationCheckTask) {
        try {
            locationCheckTaskDao.createLocationCheckTask(locationCheckTask);
        } catch (SqlerException e) {
            log.error(e.getMessage(), e);
            throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
        }
    }

    @Override
    public void batchCreateLocationCheckTask(List<LocationCheckTask> entityList) {
        if (CollectionUtils.isNotEmpty(entityList)) {
            try {
                locationCheckTaskDao.batchCreateLocationCheckTask(entityList);
            } catch (SqlerException e) {
                log.error(e.getMessage(), e);
                throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
            }
        }
    }

    @Override
    public void deleteLocationCheckTask(Integer id) {
        try {
            locationCheckTaskDao.deleteLocationCheckTask(id);
        } catch (SqlerException e) {
            log.error(e.getMessage(), e);
            throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
        }
    }

    @Override
    public void updateLocationCheckTask(LocationCheckTask locationCheckTask) {
        try {
            locationCheckTaskDao.updateLocationCheckTask(locationCheckTask);
        } catch (SqlerException e) {
            log.error(e.getMessage(), e);
            throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
        }
    }

    @Override
    public void batchUpdateLocationCheckTask(List<LocationCheckTask> entityList) {
        if (CollectionUtils.isNotEmpty(entityList)) {
            try {
                locationCheckTaskDao.batchUpdateLocationCheckTask(entityList);
            } catch (SqlerException e) {
                log.error(e.getMessage(), e);
                throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
            }
        }
    }

    @Override
    public ResponseJson discardTask(List<Integer> taskIds) {
        ResponseJson responseJson = new ResponseJson(StatusCode.FAIL);
        // 废弃只能废弃待领取状态的任务
        LocationCheckTaskQueryCondition taskQuery = new LocationCheckTaskQueryCondition();
        taskQuery.setTaskIdList(taskIds);
        taskQuery.setTaskStatus(LocationCheckStatus.UNRECEIVED.intCode());
        List<LocationCheckTask> taskList = locationCheckTaskDao.queryLocationCheckTaskList(taskQuery, null);
        if (CollectionUtils.isEmpty(taskList)) {
            responseJson.setMessage("只能废弃待领取状态的任务");
            return responseJson;
        }
        for (LocationCheckTask task : taskList) {
            LocationCheckTask updateTask = new LocationCheckTask();
            updateTask.setId(task.getId());
            updateTask.setTaskStatus(LocationCheckStatus.DISCARDED.intCode());
            locationCheckTaskDao.updateLocationCheckTask(updateTask);
            SystemLogUtils.LOCATION_CHECK_TASK.log(task.getId(), "废除任务-修改状态为已废除",
                    new String[][]{{"原状态", LocationCheckStatus.getNameByCode(task.getTaskStatus().toString())}});
        }
        responseJson.setStatus(StatusCode.SUCCESS);
        return responseJson;
    }

    @Override
    public ResponseJson createTask(LocationCheckTaskDo domain) {
        ResponseJson response = new ResponseJson(StatusCode.FAIL);
        if (domain == null || StringUtils.isEmpty(domain.getTaskNo()) || StringUtils.isEmpty(domain.getCheckType())) {
            response.setMessage("参数为空");
            return response;
        }
        if (StringUtils.equals(domain.getCheckType(), "REGIONS") && CollectionUtils.isEmpty(domain.getLocationList())
                || StringUtils.equals(domain.getCheckType(), "LOCATION")
                && CollectionUtils.isEmpty(domain.getLocationNoList())) {
            response.setMessage("参数错误！");
            return response;
        }

        List<WhStock> stockList = locationCheckTaskDao.queryWhStockListByLocationOrRegions(domain.getLocationNoList(),
                domain.getLocationList());
        if (CollectionUtils.isEmpty(stockList)) {
            response.setMessage("库位不存在SKU记录");
            return response;
        }

        LocationCheckTask locationCheckTask = new LocationCheckTask();
        locationCheckTask.setTaskNo(domain.getTaskNo());
        locationCheckTask.setTaskStatus(LocationCheckStatus.UNRECEIVED.intCode());
        locationCheckTask.setIsPrinting(0);
        locationCheckTaskDao.createLocationCheckTask(locationCheckTask);
        List<LocationCheckTaskItem> taskItemList = locationCheckTask.getItemList();
        for (WhStock stock : stockList) {
            LocationCheckTaskItem taskItem = new LocationCheckTaskItem();
            taskItem.setTaskId(locationCheckTask.getId());
            taskItem.setLocationNumber(stock.getLocationNumber());
            taskItem.setSku(stock.getSku());
            taskItem.setStatus(LocationCheckItemStatus.UNCHECK.intCode());
            taskItemList.add(taskItem);
        }
        locationCheckTaskItemDao.batchCreateLocationCheckTaskItem(taskItemList);
        SystemLogUtils.LOCATION_CHECK_TASK.log(locationCheckTask.getId(), "创建任务", new String[][]{
                {"库位数", locationCheckTask.getLocationQty() + ""}, {"SKU数", locationCheckTask.getSkuQty() + ""}});
        response.setStatus(StatusCode.SUCCESS);
        return response;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean allocation(Integer id, Integer allocatedUser) {
        if (Objects.isNull(id) || Objects.isNull(allocatedUser)) {
            log.error("分配库位校验任务失败，id为null或allocatedUser为空！");
            return false;
        }
        LocationCheckTask task = this.getLocationCheckTask(id);
        if (Objects.isNull(task)) {
            log.error("分配库位校验任务失败，根据id查询不到对应库位校验任务！");
            return false;
        }
        if (!Objects.equals(LocationCheckStatus.UNRECEIVED.intCode(), task.getTaskStatus())) {
            log.error("分配库位校验任务失败，库位校验任务状态不为待领取，不可进行分配！");
            return false;
        }
        if (Objects.equals(task.getReceiveBy(), allocatedUser)) {
            log.error("当前领取人即为被分配人！");
            return false;
        }
        LocationCheckTask updateTask = new LocationCheckTask();
        updateTask.setId(id);
        updateTask.setReceiveBy(allocatedUser);
        updateTask.setReceiveDate(new Timestamp(System.currentTimeMillis()));
        this.updateLocationCheckTask(updateTask);

        SaleUser originalUser = new SaleUser();
        SaleUser newUser = new SaleUser();
        SaleUser allocateUser = new SaleUser();
        if (!Objects.isNull(task.getReceiveBy())) {
            originalUser = CacheUtils.get(CacheName.USER_CACHE, String.valueOf(task.getReceiveBy()), SaleUser.class);
        }
        if (!Objects.isNull(allocatedUser)) {
            newUser = CacheUtils.get(CacheName.USER_CACHE, String.valueOf(allocatedUser), SaleUser.class);
        }
        if (!Objects.isNull(DataContextHolder.getUserId())){
            allocateUser = CacheUtils.get(CacheName.USER_CACHE, String.valueOf(DataContextHolder.getUserId()), SaleUser.class);
        }
        SystemLogUtils.LOCATION_CHECK_TASK.log(task.getId(), allocateUser.getUsername() + "-" + allocateUser.getName()+"分配库位校验任务", new String[][]{{"原领取人", originalUser.getUsername() + "-" + originalUser.getName()},
                {"新领取人", newUser.getUsername() + "-" + newUser.getName()}});
        return true;
    }

    @Override
    public void sortByLocation(LocationCheckTask checkTask)  {
        if (Objects.isNull(checkTask)){
            return;
        }
        List<LocationCheckTaskItem> itemList = checkTask.getItemList();
        if (CollectionUtils.isEmpty(itemList)){
            return;
        }
        ComparatorUtils.sortByLocation(itemList);
        checkTask.setItemList(itemList);
    }
}