package com.estone.picking.service.impl;

import java.util.ArrayList;
import java.util.List;

import javax.annotation.Resource;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import com.estone.picking.bean.PickTaskExpand;
import com.estone.picking.bean.PickTaskExpandQueryCondition;
import com.estone.picking.bean.WhPickingTask;
import com.estone.picking.dao.PickTaskExpandDao;
import com.estone.picking.enums.PickTaskGridStatus;
import com.estone.picking.service.PickTaskExpandService;
import com.whq.tool.component.Pager;
import com.whq.tool.exception.BusinessException;
import com.whq.tool.exception.DBErrorCode;
import com.whq.tool.sqler.SqlerException;

import lombok.extern.slf4j.Slf4j;

@Service("pickTaskExpandService")
@Slf4j
public class PickTaskExpandServiceImpl implements PickTaskExpandService {
    @Resource
    private PickTaskExpandDao pickTaskExpandDao;

    @Override
    public PickTaskExpand getPickTaskExpand(Integer id) {
        PickTaskExpand pickTaskExpand = pickTaskExpandDao.queryPickTaskExpand(id);
        return pickTaskExpand;
    }

    @Override
    public PickTaskExpand getPickTaskExpandDetail(Integer id) {
        PickTaskExpand pickTaskExpand = pickTaskExpandDao.queryPickTaskExpand(id);
        // 关联查询
        return pickTaskExpand;
    }

    @Override
    public PickTaskExpand queryPickTaskExpand(PickTaskExpandQueryCondition query) {
        Assert.notNull(query, "query is null!");
        PickTaskExpand pickTaskExpand = pickTaskExpandDao.queryPickTaskExpand(query);
        return pickTaskExpand;
    }

    @Override
    public List<PickTaskExpand> queryAllPickTaskExpands() {
        return pickTaskExpandDao.queryPickTaskExpandList();
    }

    @Override
    public List<PickTaskExpand> queryPickTaskExpands(PickTaskExpandQueryCondition query, Pager pager) {
        Assert.notNull(query, "query is null!");
        if (pager != null && pager.isQueryCount()) {
            int count = pickTaskExpandDao.queryPickTaskExpandCount(query);
            pager.setTotalCount(count);
            if ( count == 0) {
                return new ArrayList<PickTaskExpand>();
            }
        }
        List<PickTaskExpand> pickTaskExpands = pickTaskExpandDao.queryPickTaskExpandList(query, pager);
        return pickTaskExpands;
    }

    @Override
    public void createPickTaskExpand(PickTaskExpand pickTaskExpand) {
        try {
            pickTaskExpandDao.createPickTaskExpand(pickTaskExpand);
        }
        catch (SqlerException e) {
            log.error(e.getMessage(), e);
            throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
        }
    }

    @Override
    public void batchCreatePickTaskExpand(List<PickTaskExpand> entityList) {
        if (CollectionUtils.isNotEmpty(entityList)) {
            try {
                pickTaskExpandDao.batchCreatePickTaskExpand(entityList);
            }
            catch (SqlerException e) {
                log.error(e.getMessage(), e);
                throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
            }
        }
    }

    @Override
    public void deletePickTaskExpand(Integer id) {
        try {
            pickTaskExpandDao.deletePickTaskExpand(id);
        }
        catch (SqlerException e) {
            log.error(e.getMessage(), e);
            throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
        }
    }

    @Override
    public void updatePickTaskExpand(PickTaskExpand pickTaskExpand) {
        try {
            pickTaskExpandDao.updatePickTaskExpand(pickTaskExpand);
        }
        catch (SqlerException e) {
            log.error(e.getMessage(), e);
            throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
        }
    }

    @Override
    public void batchUpdatePickTaskExpand(List<PickTaskExpand> entityList) {
        if (CollectionUtils.isNotEmpty(entityList)) {
            try {
                pickTaskExpandDao.batchUpdatePickTaskExpand(entityList);
            }
            catch (SqlerException e) {
                log.error(e.getMessage(), e);
                throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
            }
        }
    }

    @Override
    public int updatePickTaskExpandStatusByTaskNo(String taskNo, Integer status){
        try {

            if (PickTaskGridStatus.COMPLETED.intCode().equals(status)) {
                pickTaskExpandDao.updatePickTaskExpandBoxUnBind(taskNo);
            }

            return pickTaskExpandDao.updatePickTaskExpandStatusByTaskNo(taskNo, status);
        }
        catch (SqlerException e) {
            log.error(e.getMessage(), e);
            throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
        }
    }

    @Override
    public void createPickTaskExpandByTask(WhPickingTask pickingTask){
        if (pickingTask.getId() == null || StringUtils.isBlank(pickingTask.getTaskNo())){
            return;
        }
        PickTaskExpand pickTaskExpand = new PickTaskExpand();
        pickTaskExpand.setTaskNo(pickingTask.getTaskNo());
        pickTaskExpand.setTaskId(pickingTask.getId());
        pickTaskExpand.setGridStatus(PickTaskGridStatus.UN_GRID.intCode());
        pickTaskExpand.setYcBoxNo(pickingTask.getYcBoxNo());
        createPickTaskExpand(pickTaskExpand);
    }

    @Override
    public void updatePickTaskExpandBoxUnBind(String taskNo) {
        pickTaskExpandDao.updatePickTaskExpandBoxUnBind(taskNo);
    }
}