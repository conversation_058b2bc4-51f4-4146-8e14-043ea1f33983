package com.estone.picking.service.impl;

import com.estone.picking.bean.CePhotoTaskItem;
import com.estone.picking.bean.CePhotoTaskItemQueryCondition;
import com.estone.picking.dao.CePhotoTaskItemDao;
import com.estone.picking.service.CePhotoTaskItemService;
import com.whq.tool.component.Pager;
import com.whq.tool.exception.BusinessException;
import com.whq.tool.exception.DBErrorCode;
import com.whq.tool.sqler.SqlerException;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

@Service("cePhotoTaskItemService")
@Slf4j
public class CePhotoTaskItemServiceImpl implements CePhotoTaskItemService {
    @Resource
    private CePhotoTaskItemDao cePhotoTaskItemDao;

    @Override
    public CePhotoTaskItem getCePhotoTaskItem(Integer id) {
        CePhotoTaskItem cePhotoTaskItem = cePhotoTaskItemDao.queryCePhotoTaskItem(id);
        return cePhotoTaskItem;
    }

    @Override
    public CePhotoTaskItem getCePhotoTaskItemDetail(Integer id) {
        CePhotoTaskItem cePhotoTaskItem = cePhotoTaskItemDao.queryCePhotoTaskItem(id);
        // 关联查询
        return cePhotoTaskItem;
    }

    @Override
    public CePhotoTaskItem queryCePhotoTaskItem(CePhotoTaskItemQueryCondition query) {
        Assert.notNull(query, "query is null!");
        CePhotoTaskItem cePhotoTaskItem = cePhotoTaskItemDao.queryCePhotoTaskItem(query);
        return cePhotoTaskItem;
    }

    @Override
    public List<CePhotoTaskItem> queryAllCePhotoTaskItems() {
        return cePhotoTaskItemDao.queryCePhotoTaskItemList();
    }

    @Override
    public List<CePhotoTaskItem> queryCePhotoTaskItems(CePhotoTaskItemQueryCondition query, Pager pager) {
        Assert.notNull(query, "query is null!");
        if (pager != null && pager.isQueryCount()) {
            int count = cePhotoTaskItemDao.queryCePhotoTaskItemCount(query);
            pager.setTotalCount(count);
            if ( count == 0) {
                return new ArrayList<CePhotoTaskItem>();
            }
        }
        List<CePhotoTaskItem> cePhotoTaskItems = cePhotoTaskItemDao.queryCePhotoTaskItemList(query, pager);
        return cePhotoTaskItems;
    }

    @Override
    public void createCePhotoTaskItem(CePhotoTaskItem cePhotoTaskItem) {
        try {
            cePhotoTaskItemDao.createCePhotoTaskItem(cePhotoTaskItem);
        }
        catch (SqlerException e) {
            log.error(e.getMessage(), e);
            throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
        }
    }

    @Override
    public void batchCreateCePhotoTaskItem(List<CePhotoTaskItem> entityList) {
        if (CollectionUtils.isNotEmpty(entityList)) {
            try {
                cePhotoTaskItemDao.batchCreateCePhotoTaskItem(entityList);
            }
            catch (SqlerException e) {
                log.error(e.getMessage(), e);
                throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
            }
        }
    }

    @Override
    public void deleteCePhotoTaskItem(Integer id) {
        try {
            cePhotoTaskItemDao.deleteCePhotoTaskItem(id);
        }
        catch (SqlerException e) {
            log.error(e.getMessage(), e);
            throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
        }
    }

    @Override
    public void updateCePhotoTaskItem(CePhotoTaskItem cePhotoTaskItem) {
        try {
            cePhotoTaskItemDao.updateCePhotoTaskItem(cePhotoTaskItem);
        }
        catch (SqlerException e) {
            log.error(e.getMessage(), e);
            throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
        }
    }

    @Override
    public void batchUpdateCePhotoTaskItem(List<CePhotoTaskItem> entityList) {
        if (CollectionUtils.isNotEmpty(entityList)) {
            try {
                cePhotoTaskItemDao.batchUpdateCePhotoTaskItem(entityList);
            }
            catch (SqlerException e) {
                log.error(e.getMessage(), e);
                throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
            }
        }
    }
}