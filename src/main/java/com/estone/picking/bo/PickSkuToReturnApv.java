/**
 * @Title: PickSkuToReturnApv.java
 * @Package com.estone.picking.bo
 * @Description: TODO
 * <AUTHOR>
 * @date 2019年5月29日
 * @version 0.0.2
 */
package com.estone.picking.bo;

import java.io.Serializable;
import java.util.List;

/**
 * @ClassName: PickSkuToReturnApv
 * @Description: 拣货数量区间需退回Apv和退回数量模型
 * <AUTHOR>
 * @date 2019年5月29日
 * @version 0.0.2
 *
 */
public class PickSkuToReturnApv implements Serializable, Comparable<PickSkuToReturnApv> {

    private String sku;

    // 上区间(>= 闭合)
    private Integer left;

    // 下区间(< 开)
    private Integer right;

    // 匹配成功的订单号
    private List<Integer> apvIds;

    // 拣货需取消退回的订单号
    private List<Integer> failedApvIds;

    public String getSku() {
        return sku;
    }

    public void setSku(String sku) {
        this.sku = sku;
    }

    public Integer getLeft() {
        return left;
    }

    public void setLeft(Integer left) {
        this.left = left;
    }

    public Integer getRight() {
        return right;
    }

    public void setRight(Integer right) {
        this.right = right;
    }

    public List<Integer> getApvIds() {
        return apvIds;
    }

    public void setApvIds(List<Integer> apvIds) {
        this.apvIds = apvIds;
    }

    public List<Integer> getFailedApvIds() {
        return failedApvIds;
    }

    public void setFailedApvIds(List<Integer> failedApvIds) {
        this.failedApvIds = failedApvIds;
    }

    public boolean containQuantity(Integer quantity) {
        if (left <= quantity && quantity < right) {
            return true;
        }
        return false;
    }

    /**
     * @param sku
     * @param left
     * @param right
     * @param apvIds
     * @param failedApvIds
     */
    public PickSkuToReturnApv(String sku, Integer left, Integer right, List<Integer> apvIds,
            List<Integer> failedApvIds) {
        super();
        this.sku = sku;
        this.left = left;
        this.right = right;
        this.apvIds = apvIds;
        this.failedApvIds = failedApvIds;
    }
    
    public PickSkuToReturnApv() {
        super();
    }
    
    @Override
    public int compareTo(PickSkuToReturnApv o) {
        return left.compareTo(o.getLeft());
    }

}
