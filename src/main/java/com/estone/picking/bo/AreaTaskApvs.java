package com.estone.picking.bo;

import java.util.List;

import com.estone.apv.bean.WhApv;
import com.estone.picking.bean.WhPickingTask;

public class AreaTaskApvs {

    private List<WhApv> apvList;

    private WhPickingTask whPickingTask;

    public AreaTaskApvs() {

    }

    public AreaTaskApvs(List<WhApv> apvList, WhPickingTask whPickingTask) {
        super();
        this.apvList = apvList;
        this.whPickingTask = whPickingTask;
    }

    public List<WhApv> getApvList() {
        return apvList;
    }

    public void setApvList(List<WhApv> apvList) {
        this.apvList = apvList;
    }

    public WhPickingTask getWhPickingTask() {
        return whPickingTask;
    }

    public void setWhPickingTask(WhPickingTask whPickingTask) {
        this.whPickingTask = whPickingTask;
    }

}
