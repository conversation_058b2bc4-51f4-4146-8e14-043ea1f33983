package com.estone.common;

import lombok.Data;

import java.sql.Timestamp;
import java.util.Date;

/**
 * @Auther: wxy
 * @Date: 2019/6/24 14:57
 * @Description: 账号和账号业务信息rep
 */
@Data
public class SaleAccountAndBusinessResponse {

    /**
     *   主键
     */
    private Long id;

    /**
     *   账号
     */
    private String accountNumber;

    /** 母账号名称 */
    private String momAccountName;

    /** 子账号名称 */
    private String sonAccountName;

    /**
     *   账号等级
     */
    private String accountLevel;

    /**
     *   公司id
     */
    private Long companyId;

    /**
     *   销售渠道
     */
    private String saleChannel;

    /**
     *   客户端Key
     */
    private String clientId;

    /**
     *   客户端秘钥
     */
    private String clientSecret;

    /**
     *   访问令牌
     */
    private String accessToken;

    /**
     *   刷新令牌
     */
    private String refreshToken;

    /**
     *   token过期时间
     */
    private Timestamp tokenExpireTime;

    /**
     *   账号状态
     */
    private String accountStatus;

    /**
     *   授权状态
     */
    private String authorizationStatus;

    /**
     *  访问WMS的应用名称
     */
    private String appName;

    /**
     * 访问WMS的应用版本
     */
    private String appVersion;

    /**
     * 卖家ID
     */
    private String merchantId;

    /**
     * 商城编号 分站点存在
     */
    private String marketplaceId;

    /**
     * 服务URL
     */
    private String serviceUrl;

    /**
     * 订单服务URL
     */
    private String orderServiceUrl;

    /**
     * 产品服务URL
     */
    private String productServiceUrl;

    /**
     * 订单同步时间
     */
    private Timestamp syncTime;

    /**
     * 账号所在站点
     */
    private String accountSite;

    /**
     * 开发者账号
     */
    private String developerAccount;

    /**
     * 创建时间
     */
    private Date createDate;

    /**
     * 修改时间
     */
    private Date modifiedDate;

    /**
     * 卖家ID
     */
    private String sellerId;

    /**
     * 虾皮店铺id
     */
    private String shopId;

    /**
     * 虾皮签名
     */
    private String apiKey;
    /**
     * 货号前缀
     */
    private String skuPrefix;

    /**
     * 货号后缀
     */
    private String skuSuffix;

    /**
     * ean前缀(amazon)
     */
    private String eanPrefix;

    /**
     * upc前缀(amazon)
     */
    private String upcPrefix;

    /**
     * paypal账号(ebay)
     */
    private String paypalAccountNumber;

    /**
     * 分销商产品
     */
    private Boolean distributor;

    private String salesman;

    private Boolean isSip;

    private String groupCode;


    /** 商家名称 */
    private String merchantName;
}
