package com.estone.common.util.model;

import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;

@Data
public class CQuery<T> implements Serializable {
    private static final long serialVersionUID = 3341855372947133412L;

    /**
     * 查询条件
     */
    private T search;

    /**
     * 是否需要分页，默认分页
     */
    private boolean pageReqired = true;

    /**
     * limit 限定数据量
     */
    private int size = 10;

    /**
     * 页码
     */
    private int page = 1;

    /**
     * 排序字段
     */
    private String sort;

    /**
     * desc, asc
     */
    private String order;

    public CQuery() {
    }

    public CQuery(boolean pageReqired) {
        this.pageReqired = pageReqired;
    }

    public CQuery(T search, boolean pageReqired) {
        this.search = search;
        this.pageReqired = pageReqired;
    }

    public String getOrderByClause() {
        return getOrderByClause(null);
    }

    public String getOrderByClause(String prefix) {
        if (StringUtils.isEmpty(sort) || StringUtils.isEmpty(order)) {
            return null;
        }

        if (StringUtils.isEmpty(prefix)) {
            return sort + " " + order;
        }
        else {
            return prefix + "." + sort + " " + order;
        }
    }

    public int getPage() {
        return page;
    }
}

