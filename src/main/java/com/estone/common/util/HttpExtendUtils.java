package com.estone.common.util;

import com.estone.common.util.model.HttpParams;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.*;
import org.springframework.http.client.SimpleClientHttpRequestFactory;
import org.springframework.web.client.RestClientResponseException;
import org.springframework.web.client.RestTemplate;

import java.net.URI;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;


/**
 * 发送Http请求  自定超时时间
 *
 * <AUTHOR>
 */
@Slf4j
public class HttpExtendUtils {

    private static Map<String, RestTemplate> restMap = new ConcurrentHashMap<>();
    private static String warehouseId;
   static  {
       warehouseId = CacheUtils.getLocalWarehouseIdStr();
    }


    /**
     * 根据请求获取http / https 的RestTemplate
     *
     * @param url
     * @return
     */
    public static RestTemplate getRestTemplate(String url) {
        // 默认超时
        return getRestTemplate(url, 20000, 20000);
    }

    /**
     * 根据请求获取http  的RestTemplate
     *
     * @param url
     * @return
     */
    public static RestTemplate getRestTemplate(String url, Integer connectTimeout, Integer readTimeout) {
        // 同一key，使用同一对象
        String key = connectTimeout + "_" + readTimeout;
        return MapUtils.putIfAbsent(restMap, key, () -> {
            SimpleClientHttpRequestFactory requestFactory = null;
            requestFactory = new SimpleClientHttpRequestFactory();
            requestFactory.setReadTimeout(connectTimeout); // connectTimeout 连接时间（毫秒）
            requestFactory.setConnectTimeout(readTimeout);// readTimeout 读取时间（毫秒）
            return new RestTemplate(requestFactory);
        });
    }

    /**
     * post请求
     *
     * @param url         url
     * @param accessToken 访问token
     * @param body        requestBody
     * @param clazz       response返回值类型R
     * @return R对象
     * @throws RestClientResponseException 非200状态，捕获该异常
     */
    public static <T, R> R post(String url, String accessToken, T body, Class<R> clazz, Integer connectTimeout, Integer readTimeout)
            throws RestClientResponseException {
        RequestEntity<T> entity = new RequestEntity<>(body, getHttpHeadersByAccessToken(accessToken), HttpMethod.POST,
                URI.create(url));
        return getRestTemplate(url, connectTimeout, readTimeout).exchange(entity, clazz).getBody();
    }

    public static <T, R> R post(String url , T body, Class<R> clazz, Integer connectTimeout, Integer readTimeout)
            throws RestClientResponseException {
        HttpHeaders httpHeaders = new HttpHeaders();
       // if (StringUtils.contains(url,"10.100.1"))
            httpHeaders.add("warehouseId",warehouseId);
        RequestEntity<T> entity = new RequestEntity<>(body,httpHeaders, HttpMethod.POST,
                URI.create(url));
        return getRestTemplate(url, connectTimeout, readTimeout).exchange(entity, clazz).getBody();
    }


    /**
     * post请求
     *
     * @param url     url
     * @param headers 请求header对象
     * @param body    requestBody
     * @param clazz   response返回值类型R
     * @return R对象
     * @throws RestClientResponseException 非200状态，捕获该异常
     */
    public static <T, R> R post(String url, Map<String, String> headers, T body, Class<R> clazz, Integer connectTimeout, Integer readTimeout)
            throws RestClientResponseException {
        RequestEntity<T> entity = new RequestEntity<>(body, getHttpHeadersByMap(headers,url), HttpMethod.POST,
                URI.create(url));
        return getRestTemplate(url, connectTimeout, readTimeout).exchange(entity, clazz).getBody();
    }

    /**
     * get请求
     *
     * @param url         url
     * @param accessToken 访问token
     * @param clazz       response返回值模型R
     * @return R对象
     * @throws RestClientResponseException 非200状态，捕获该异常
     */
    public static <R> R get(String url, String accessToken, Class<R> clazz, Integer connectTimeout, Integer readTimeout) throws RestClientResponseException {
        RequestEntity<?> requestEntity = new RequestEntity<>(getHttpHeadersByAccessToken(accessToken), HttpMethod.GET,
                URI.create(url));
        return getRestTemplate(url, connectTimeout, readTimeout).exchange(requestEntity, clazz).getBody();
    }

    /**
     * post请求
     *
     * @param url         url
     * @param accessToken 访问token
     * @param body        requestBody
     * @param clazz       response返回值类型R
     * @return R对象
     * @throws RestClientResponseException 非200状态，捕获该异常
     */
    public static <T, R> R post(String url, String accessToken, T body, Class<R> clazz)
            throws RestClientResponseException {
        RequestEntity<T> entity = new RequestEntity<>(body, getHttpHeadersByAccessToken(accessToken), HttpMethod.POST,
                URI.create(url));
        return getRestTemplate(url).exchange(entity, clazz).getBody();
    }


    /**
     * 设置http header参数token并返回
     *
     * @param accessToken 访问token
     * @return http header
     */
    public static HttpHeaders getHttpHeadersByAccessToken(String accessToken) {
        if (StringUtils.isBlank(accessToken)) {
            return null;
        }

        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.add("Authorization", accessToken);
        httpHeaders.add("warehouseId",warehouseId);
        return httpHeaders;
    }


    public static HttpHeaders getHttpHeadersByMap(Map<String, String> map,String url) {
        if (org.apache.commons.collections.MapUtils.isEmpty(map)) {
            return null;
        }
        HttpHeaders httpHeaders = new HttpHeaders();
        for (Map.Entry<String, String> entry : map.entrySet()) {
            httpHeaders.add(entry.getKey(), entry.getValue());
        }
        //if (StringUtils.contains(url,"10.100.1"))
            httpHeaders.add("warehouseId",warehouseId);
        return httpHeaders;
    }

    /**
     * 发送请求
     *
     * @param <T, R>
     *
     * @param httpParams 请求参数
     * @param resultType 返回值类型
     *
     * @return
     */
    public static <T, R> R exchange(HttpParams<T> httpParams, Class<R> resultType) {
        if (!checkHttpParams(httpParams) || resultType == null) {
            log.warn("http params  can not be satisfy to exchange http.");
            return null;
        }

        HttpEntity<T> httpEntity = new HttpEntity<>(httpParams.getBody(), httpParams.getHttpHeaders());
        // 执行HTTP请求
        ResponseEntity<R> response = HttpRestTemplateFactory.getRestTemplate(httpParams).exchange(httpParams.getUrl(),
                httpParams.getHttpMethod(), httpEntity, resultType);

        return response.getBody();
    }

    public static <T> String exchangeCookie(HttpParams<T> httpParams) {
        if (!checkHttpParams(httpParams) ) {
            log.warn("http params  can not be satisfy to exchange http.");
            return null;
        }
        HttpEntity<T> httpEntity = new HttpEntity<>(httpParams.getBody(), httpParams.getHttpHeaders());
        // 执行HTTP请求
        ResponseEntity<String> response = HttpRestTemplateFactory.getRestTemplate(httpParams).exchange(httpParams.getUrl(),
                httpParams.getHttpMethod(), httpEntity, String.class);
        // 获取响应头中的 Cookies
        HttpHeaders responseHeaders = response.getHeaders();
        List<String> cookies = responseHeaders.get(HttpHeaders.SET_COOKIE);
        return cookies.get(0);
    }
    private static boolean checkHttpParams(HttpParams<?> httpParams) {
        return !(httpParams == null || StringUtils.isEmpty(httpParams.getUrl()));
    }
}
