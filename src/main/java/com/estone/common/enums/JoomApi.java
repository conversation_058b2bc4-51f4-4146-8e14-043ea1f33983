package com.estone.common.enums;

/**
 * @Description: JOOM API
 * @Author: Yimeil
 * @Date: 2024/12/6 17:01
 * @Version: 1.0.0
 */
public enum JoomApi {

    BASE_URL("https://api-merchant.joom.com/api/v3"),

    /**
     * Generate EU labels for Product Variants
     */
    GENERATE_EU_LABELS("/products/generateEuLabels"),

    /**
     * Get Product
     */
    GET_PRODUCT("/products"),

    /**
     * Get Order
     */
    GET_ORDER("/orders"),
    ;
    private String value;

    private JoomApi(String value) {
        this.value = value;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }
}
