package com.estone.common.executors;

import java.util.concurrent.ThreadPoolExecutor;

/**
 * 
 * @Description: wms线程池
 * 
 * @ClassName: WmsExecutors
 * @Author: qinyangkai
 * @Date: 2018/08/28
 * @Version: 0.0.1
 */
public class WmsExecutors {
    /**
     * 最大同步产品的线程数
     */
    public static final int MAX_SYNC_PRODUCT_THREADS = 5;

    public static final ThreadPoolExecutor SYNC_PRODUCT_POOL = ExecutorUtils
            .newFixedThreadPool(MAX_SYNC_PRODUCT_THREADS);

    /**
     * 执行同步产品任务
     * 
     * @param runnable runnable
     * 
     */
    public static void executeSyncProduct(Runnable runnable) {
        ExecutorUtils.execute(SYNC_PRODUCT_POOL, runnable, "wms-pms");
    }
}
