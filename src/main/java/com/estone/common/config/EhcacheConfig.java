package com.estone.common.config;

import com.estone.common.EhCacheKeyGenerator;
import com.estone.common.EhRedisCache;
import org.springframework.cache.Cache;
import org.springframework.cache.annotation.CachingConfigurerSupport;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cache.ehcache.EhCacheFactoryBean;
import org.springframework.cache.ehcache.EhCacheManagerFactoryBean;
import org.springframework.cache.interceptor.KeyGenerator;
import org.springframework.cache.support.SimpleCacheManager;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.ClassPathResource;
import org.springframework.data.redis.core.RedisTemplate;

import java.util.ArrayList;
import java.util.List;

/**
 * ehcach缓存
 * 
 * @Description:
 * 
 * @ClassName: EhcacheConfig
 * @Author: wuhuiqiang
 * @Date: 2019/05/03
 * @Version: 0.0.1
 */
@Configuration
@EnableCaching // 标注启动了缓存
public class EhcacheConfig extends CachingConfigurerSupport {

    @Override
    public KeyGenerator keyGenerator(){
        return new EhCacheKeyGenerator();
    }

    /**
     * ehCache 配置管理器
     *
     * @Description:
     *
     * @return
     * @Author: wuhuiqiang
     * @Date: 2019/05/03
     * @Version: 0.0.1
     */
    @Bean
    public EhCacheManagerFactoryBean ehCacheManagerFactoryBean() {
        EhCacheManagerFactoryBean cacheManagerFactoryBean = new EhCacheManagerFactoryBean();
        cacheManagerFactoryBean.setConfigLocation(new ClassPathResource("ehcache.xml"));
        cacheManagerFactoryBean.setShared(true);
        cacheManagerFactoryBean.setCacheManagerName("cacheManager");
        return cacheManagerFactoryBean;
    }

    /**
     * ehCache 操作对象
     * 
     * @Description:
     *
     * @param bean
     * @return
     * @Author: wuhuiqiang
     * @Date: 2019/05/03
     * @Version: 0.0.1
     */
    @Bean
    public EhCacheFactoryBean ehCacheFactoryBean(EhCacheManagerFactoryBean bean) {
        EhCacheFactoryBean ehCacheFactoryBean = new EhCacheFactoryBean();
        ehCacheFactoryBean.setCacheName("ehCache");
        ehCacheFactoryBean.setCacheManager(bean.getObject());
        return ehCacheFactoryBean;
    }

    @Bean
    public SimpleCacheManager ehRedisCacheManager(RedisTemplate redisTemplate, net.sf.ehcache.Cache ehCache){
        SimpleCacheManager ehRedisCacheManager = new SimpleCacheManager();
        List<Cache> caches = new ArrayList<>();
        caches.add(new EhRedisCache(redisTemplate, ehCache, "USER_CACHE"));
        caches.add(new EhRedisCache(redisTemplate, ehCache, "SYSTEM_PARAM_CACHE"));
        caches.add(new EhRedisCache(redisTemplate, ehCache, "UNIQUE_INCR_CACHE"));
        caches.add(new EhRedisCache(redisTemplate, ehCache, "SCAN_APV_ID"));
        caches.add(new EhRedisCache(redisTemplate, ehCache, "WAREHOUSE_CACHE"));
        caches.add(new EhRedisCache(redisTemplate, ehCache, "STOCK_ZONE_CACHE"));
        caches.add(new EhRedisCache(redisTemplate, ehCache, "COUNTRY_CACHE"));
        caches.add(new EhRedisCache(redisTemplate, ehCache, "LOGISTICS_CACHE"));
        caches.add(new EhRedisCache(redisTemplate, ehCache, "PLATFORM_CACHE"));
        ehRedisCacheManager.setCaches(caches);
        return  ehRedisCacheManager;
    }
}
