package com.estone.common.config;

import java.util.Properties;

import javax.sql.DataSource;

import org.springframework.aop.aspectj.AspectJExpressionPointcut;
import org.springframework.aop.support.DefaultPointcutAdvisor;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.AutoConfigureAfter;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.interceptor.TransactionInterceptor;

@Configuration
@AutoConfigureAfter(DataSourceConfig.class)
public class TxAdviceConfig {
    public static final String transactionExecution = "execution(* com.estone.*.service.*Service.*(..)) || execution(* com.estone.*.*.service.*Service.*(..))";

    // wms创建事务管理器
    @Primary
    @Bean(name = "txManager")
    public PlatformTransactionManager txManager(@Qualifier(DataSources.WMS_DS) DataSource dataSource) {
        return new DataSourceTransactionManager(dataSource);
    }

    @Bean(name = "txAdvice")
    public TransactionInterceptor transactionInterceptor(@Qualifier("txManager") PlatformTransactionManager txManager) {
        Properties attributes = new Properties();
        attributes.setProperty("get*", "PROPAGATION_REQUIRED,readOnly");
        attributes.setProperty("search*", "PROPAGATION_REQUIRED,readOnly");
        //attributes.setProperty("query*", "PROPAGATION_REQUIRED,readOnly");
        attributes.setProperty("add*", "PROPAGATION_REQUIRED,-Exception");
        attributes.setProperty("insert*", "PROPAGATION_REQUIRED,-Exception");
        attributes.setProperty("save*", "PROPAGATION_REQUIRED,-Exception");
        attributes.setProperty("create*", "PROPAGATION_REQUIRED,-Exception");
        attributes.setProperty("batch*", "PROPAGATION_REQUIRED,-Exception");
        attributes.setProperty("del*", "PROPAGATION_REQUIRED,-Exception");
        attributes.setProperty("update*", "PROPAGATION_REQUIRED,-Exception");
        attributes.setProperty("do*", "PROPAGATION_REQUIRED,-Exception");
        return new TransactionInterceptor(txManager, attributes);
    }

    @Bean
    public DefaultPointcutAdvisor defaultPointcutAdvisor(@Qualifier("txAdvice") TransactionInterceptor txAdvice) {
        AspectJExpressionPointcut pointcut = new AspectJExpressionPointcut();
        pointcut.setExpression(transactionExecution);
        DefaultPointcutAdvisor advisor = new DefaultPointcutAdvisor();
        advisor.setPointcut(pointcut);
        advisor.setAdvice(txAdvice);
        return advisor;
    }

    // amq事务管理器
    @Bean(name = "amqTxManager")
    public PlatformTransactionManager amqTxManager(@Qualifier(DataSources.AMQ_DS) DataSource dataSource) {
        return new DataSourceTransactionManager(dataSource);
    }
}
