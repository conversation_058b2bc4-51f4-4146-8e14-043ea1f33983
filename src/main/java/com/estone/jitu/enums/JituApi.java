package com.estone.jitu.enums;

/**
 * @Description:极兔接口枚举
 * @Author: Yimeil
 * @Date: 2024/3/22 17:01
 * @Version: 1.0.0
 */
public enum JituApi {
    /**
     * 创建/修改订单。目前创建订单接口支持修改订单，即如果客户订单号（txlogisticId）已存在，且对应的订单未揽件，则直接修改已有订单。
     * 如果收件地址发生变更就重新获取最新的三段码/大头笔返回（运单号不变）
     */
    /**
     * 创建订单
     */
    ADD_ORDER("/order/addOrder"),
    /**
     * 创建订单（带运单号）
     */
    ADD_ORDER_V2("/order/v2/addOrder"),
    /**
     * 查询订单
     */
    GET_ORDERS("/order/getOrders"),

    /**
     * 取消订单
     */
    CANCEL_ORDER("/order/cancelOrder"),
    /**
     * 面单打印
     */
    PRINT_ORDER("/order/printOrder"),
    /**
     * 获取电子面单
     */
    GET_BATCH_BILL_CODE("/billCode/getBatchBillCode"),

    ;

    private String value;

    private JituApi(String value) {
        this.value = value;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }
}
