package com.estone.system.qcproportion.bean;

import lombok.Data;

/**
 * 入库异常类型配置
 * <AUTHOR>
 * @date 2025/3/11
 */
@Data
public class ExceptionTypeConfigDTO {

    private String exceptionName;

    private String exceptionType;

    private Boolean enable;

    private String createdBy;

    /** add、update、delete */
    private String operation;

    public ExceptionTypeConfigDTO() {
    }

    public ExceptionTypeConfigDTO(String exceptionName, String exceptionType, Boolean enable, String createdBy, String operation) {
        this.exceptionType = exceptionType;
        this.exceptionName = exceptionName;
        this.enable = enable;
        this.createdBy = createdBy;
        this.operation = operation;
    }

}
