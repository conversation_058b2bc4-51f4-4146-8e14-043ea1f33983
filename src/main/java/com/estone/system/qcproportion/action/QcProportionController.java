package com.estone.system.qcproportion.action;

import java.util.ArrayList;
import java.util.List;

import javax.annotation.Resource;
import javax.servlet.http.HttpSession;

import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import com.estone.common.util.CacheUtils;
import com.estone.system.param.bean.SystemParam;
import com.estone.system.param.service.SystemParamService;
import com.estone.system.qcproportion.bean.QcProportion;
import com.estone.system.qcproportion.domain.QcProportionDo;
import com.estone.system.qcproportion.util.QcProportionUtils;
import com.estone.system.qcproportion.util.QcSamplingProportionUtils;
import com.whq.tool.action.BaseController;
import com.whq.tool.component.StatusCode;
import com.whq.tool.json.ResponseJson;

@Controller
@RequestMapping(value = "system/qcProportion")
public class QcProportionController extends BaseController {
    
    @Resource
    private SystemParamService systemParamService;

    @RequestMapping(method = { RequestMethod.GET })
    public String init(@ModelAttribute("domain") QcProportionDo domain) {
        initFormData(domain);
        querySystemParams(domain);
        return "system/system_qcproportion_list";
    }

    private void initFormData(@ModelAttribute("domain") QcProportionDo domain) {

    }

    private void querySystemParams(@ModelAttribute("domain") QcProportionDo domain) {
        List<QcProportion> qcProportions = QcProportionUtils.getQcProportions();
        domain.setQcProportions(qcProportions);
        domain.setQcSamplingProportion(QcSamplingProportionUtils.getQcSamplingProportion());
    }

    @RequestMapping(value = "update", method = { RequestMethod.GET })
    public String toUpdateQcProportion(@ModelAttribute("domain") QcProportionDo domain) {
        List<QcProportion> qcProportions = QcProportionUtils.getQcProportions();
        if (CollectionUtils.isEmpty(qcProportions)) {
            qcProportions = new ArrayList<QcProportion>();
            QcProportion min = new QcProportion();
            min.setIsMin(true);
            min.setLeft(0);
            QcProportion max = new QcProportion();
            max.setIsMax(true);
            max.setLeft(1000);
            qcProportions.add(min);
            qcProportions.add(max);
        }
        domain.setQcProportions(qcProportions);
        return "system/system_qcproportion_update";
    }

    @RequestMapping(value = "update", method = { RequestMethod.POST })
    public String updateQcProportion(@ModelAttribute("domain") QcProportionDo domain, HttpSession session) {
        List<QcProportion> qcProportions = domain.getQcProportions();
        QcProportionUtils.setQcProportions2Redis(qcProportions);
        return "redirect:/system/qcProportion";
    }

    @RequestMapping(value = "delete", method = { RequestMethod.GET })
    public String deleteQcProportion(@ModelAttribute("domain") QcProportionDo domain) {
        QcProportionUtils.deleteQcProportions();
        return "redirect:/system/qcProportion";
    }

    @RequestMapping(value = "updateSampling", method = { RequestMethod.POST })
    @ResponseBody
    public ResponseJson updateQcSamplingProportion(@RequestParam("percentage") Double percentage,
            HttpSession session) {
        ResponseJson response = new ResponseJson();
        if (percentage < 0 || percentage > 100) {
            response.setStatus(StatusCode.FAIL);
            response.setMessage("数值范围必须在0-100！");
            return response;
        }
        SystemParam systemParam = CacheUtils.SystemParamGet("QC_ALLCHECK_CODE.QC_ALLCHECK_KEY");
        if(null != systemParam) {
            systemParam.setParamValue(percentage.toString());
            systemParamService.updateSystemParam(systemParam);
        }else {
            systemParam = new SystemParam();
            systemParam.setParamCode("QC_ALLCHECK_CODE");
            systemParam.setParamKey("QC_ALLCHECK_KEY");
            systemParam.setParamName("QC全检概率");
            systemParam.setParamValue(percentage.toString());
            systemParam.setParamType(3);
            systemParam.setParamDisplay(false);
            systemParam.setParamEnabled(false);
            systemParamService.createSystemParam(systemParam);
        }
        response.setMessage("修改成功！");
        return response;
    }
}