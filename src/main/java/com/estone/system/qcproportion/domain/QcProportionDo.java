package com.estone.system.qcproportion.domain;

import java.util.ArrayList;
import java.util.List;

import com.estone.system.qcproportion.bean.QcProportion;

public class QcProportionDo {
	private QcProportion qcProportion;
	
    private Double qcSamplingProportion;

	private List<QcProportion> qcProportions = new ArrayList<QcProportion>();

	public QcProportion getQcProportion() {
		return qcProportion;
	}

	public void setQcProportion(QcProportion qcProportion) {
		this.qcProportion = qcProportion;
	}

	public List<QcProportion> getQcProportions() {
		return qcProportions;
	}

	public void setQcProportions(List<QcProportion> qcProportions) {
		this.qcProportions = qcProportions;
	}

    public Double getQcSamplingProportion() {
        return qcSamplingProportion;
    }

    public void setQcSamplingProportion(Double qcSamplingProportion) {
        this.qcSamplingProportion = qcSamplingProportion;
    }
}