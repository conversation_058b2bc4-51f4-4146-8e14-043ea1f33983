package com.estone.system.permission.service;

import java.util.List;
import java.util.Map;

import com.estone.system.permission.bean.Permission;
import com.estone.system.permission.bean.PermissionQueryCondition;
import com.estone.system.role.bean.Role;
import com.whq.tool.component.Pager;

public interface PermissionService {
    /**
     * This method corresponds to the database table t_permission
     *
     * @mbggenerated Thu Sep 14 17:50:18 CST 2017
     */
    List<Permission> queryAllPermissions();

    /**
     * This method corresponds to the database table t_permission
     *
     * @mbggenerated Thu Sep 14 17:50:18 CST 2017
     */
    List<Permission> queryPermissions(PermissionQueryCondition query, Pager pager);

    /**
     * This method corresponds to the database table t_permission
     *
     * @mbggenerated Thu Sep 14 17:50:18 CST 2017
     */
    Permission getPermission(Integer id);

    /**
     * This method corresponds to the database table t_permission
     *
     * @mbggenerated Thu Sep 14 17:50:18 CST 2017
     */
    Permission getPermissionDetail(Integer id);

    /**
     * This method corresponds to the database table t_permission
     *
     * @mbggenerated Thu Sep 14 17:50:18 CST 2017
     */
    Permission queryPermission(PermissionQueryCondition query);

    /**
     * This method corresponds to the database table t_permission
     *
     * @mbggenerated Thu Sep 14 17:50:18 CST 2017
     */
    void createPermission(Permission permission);

    /**
     * This method corresponds to the database table t_permission
     *
     * @mbggenerated Thu Sep 14 17:50:18 CST 2017
     */
    void batchCreatePermission(List<Permission> entityList);

    /**
     * This method corresponds to the database table t_permission
     *
     * @mbggenerated Thu Sep 14 17:50:18 CST 2017
     */
    void deletePermission(Integer id);

    /**
     * This method corresponds to the database table t_permission
     *
     * @mbggenerated Thu Sep 14 17:50:18 CST 2017
     */
    void updatePermission(Permission permission);

    void updatePermissionByCode(Permission permission);

    /**
     * This method corresponds to the database table t_permission
     *
     * @mbggenerated Thu Sep 14 17:50:18 CST 2017
     */
    void batchUpdatePermission(List<Permission> entityList);

    /**
     * 根据角色查询角色操作权限
     * 
     * 
     * @param roleId
     * @return
     * @return List<Permission>
     */
    List<Permission> queryRolePermissionListByRoleId(List<Integer> roleIds);

    /**
     * 查询角色操作权限
     * 
     * 
     * @return Map
     */
    Map<Integer, List<Permission>> queryAllRolePermissionMap();

    /**
     * 删除角色权限
     * 
     * 
     * @param roleId
     * @return void
     */
    void deleteRolePermission(Integer roleId);

    /**
     * 创建角色权限
     * 
     * 
     * @param role
     * @param permissionList
     * @return void
     */
    void createRolePermission(Role role, List<Permission> permissionList);

    /**
     * @Title: queryAuthPermission
     * @Description: 获取当前用户按钮权限
     * @Date 2018年11月12日_上午11:29:34
     * @param query
     * @return
     */
    List<Permission> queryAuthPermissions(PermissionQueryCondition query);

    /**
     * @Title: deleteRoleAuthPermission
     * @Description: 删除角色按钮权限
     * @Date 2018年11月13日_下午4:03:39
     * @param roleId
     */
    void deleteRoleAuthPermission(Integer roleId, String parentCode);

    void batchDeletePermissionByCode(List<String> codeList);

    List<Permission> queryAuthPDAPermissions(PermissionQueryCondition query);

    void deletePermissionByCode(String permissionCode);
}