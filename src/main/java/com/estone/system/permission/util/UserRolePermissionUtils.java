package com.estone.system.permission.util;

import com.estone.common.util.CommonUtils;
import com.estone.common.util.SpringUtils;
import com.estone.common.util.StringRedisUtils;
import com.estone.system.menu.bean.Menu;
import com.estone.system.menu.bean.MenuQueryCondition;
import com.estone.system.menu.service.MenuService;
import com.estone.system.role.bean.Role;
import com.estone.system.role.bean.RoleQueryCondition;
import com.estone.system.role.service.RoleService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;

/**
 * <AUTHOR>
 * @version 1.0
 * @Description 用户角色权限工具类
 * @date 2019/10/22 10:05
 */
public class UserRolePermissionUtils {

    private static Logger logger = LoggerFactory.getLogger(UserRolePermissionUtils.class);

    private static final String PERMISSION_KEY = "PERMISSION_PRIFIX:";

    private static final String ROLE_SET_KEY = "ROLE_SET_PRIFIX:";

    private static final String MENU_URL_LIST_KEY = "MENU_URL_LIST_PRIFIX:";

    /**
     * 设置用户对应的角色
     *
     * @param userId
     * @param roles
     * @return
     */
    public static boolean setUserAndRoleDataToRedis(Integer userId, List<Role> roles) {
        if (userId != null && CollectionUtils.isNotEmpty(roles)) {
            Set<String> set = new HashSet<>();
            for (Role role : roles) {
                set.add(role.getRoleId().toString());
            }
            StringRedisUtils.set(PERMISSION_KEY + userId, StringUtils.join(set, ","));
            logger.warn("RoleSet:" + set);
            return true;
        } else if (userId != null) {
            // 当清除了用户的所有角色后要置空
            StringRedisUtils.del(PERMISSION_KEY + userId);
        }
        return false;
    }

    public static List<String> getRoleSetByUserId(Integer userId) {
        String result = StringRedisUtils.get(PERMISSION_KEY + userId);
        if (StringUtils.isNotBlank(result)) {
            return CommonUtils.splitList(result, ",");
        } else {
            return null;
        }
    }

    /**
     * 删除用户——角色数据
     *
     * @param userId
     */
    public static void delUserToRoleSetData(Integer userId) {
        StringRedisUtils.del(PERMISSION_KEY + userId);
    }

    /**
     * 设置角色对应的菜单权限
     *
     * @param roleId
     * @param menus
     * @return
     */
    public static boolean setMenuUrlListToRedis(Integer roleId, List<Menu> menus) {
        if (roleId != null && CollectionUtils.isNotEmpty(menus)) {
            List<String> urlList = handleMenuUrlList(menus);
            if (CollectionUtils.isNotEmpty(urlList)) {
                StringRedisUtils.set(ROLE_SET_KEY + roleId, StringUtils.join(urlList, ","));
                return true;
            }
        } else if (roleId != null) {
            // 当清除了角色的所有菜单后要置空
            StringRedisUtils.del(ROLE_SET_KEY + roleId);
        }
        return false;
    }

    /**
     * 删除角色——菜单URL数据
     *
     * @param roleId
     */
    public static void delRoleToMenuUrlData(Integer roleId) {
        StringRedisUtils.del(ROLE_SET_KEY + roleId);
    }

    /**
     * 根据角色获取菜单权限
     *
     * @param roleId
     * @return
     */
    public static List<String> getMenuUrlListByRoleId(Integer roleId) {
        String result = StringRedisUtils.get(ROLE_SET_KEY + roleId);
        if (StringUtils.isNotBlank(result)) {
            return CommonUtils.splitList(result, ",");
        } else {
            return null;
        }
    }

    /**
     * 查询用户有用的菜单权限
     * 暂时无用
     *
     * @param userId
     * @return
     */
    public static Set<String> getPermissionUrlListByUserId(Integer userId) {
        if (userId == null) {
            return null;
        }
        List<String> set = getRoleSetByUserId(userId);
        if (set != null && set.size() > 0) {
            // logger.warn("RoleSet:" + set);
            List<String> keys = new ArrayList<>();
            for (String key : set) {
                keys.add(ROLE_SET_KEY + key);
            }
            List<Object> values = StringRedisUtils.batchGet(keys);
            if (CollectionUtils.isNotEmpty(values)) {
                Set<String> urlSet = new HashSet<>();
                for (Object obj : values) {
                    if (obj != null) {
                        List<String> urls = CommonUtils.splitList(obj.toString(), ",");
                        if (CollectionUtils.isNotEmpty(urls)) {
                            urlSet.addAll(urls);
                        }
                    }
                }
                return urlSet;
            } else {
                return null;
            }
        } else {
            return null;
        }
    }

    /**
     * 校验用户访问权限
     *
     * @param userId
     * @param requestUrl
     * @return
     */
    public static boolean checkUserPermission(Integer userId, String requestUrl, String method) {
        requestUrl = requestUrl.replaceAll("/wms/", "");
        requestUrl = requestUrl.replaceAll("//", "/");
        requestUrl = requestUrl.replaceAll("//", "/");
        if (requestUrl.startsWith("/")) {
            requestUrl = requestUrl.replaceFirst("/", "");
        }
        if (requestUrl.endsWith("/")) {
            requestUrl = requestUrl.substring(0, requestUrl.length() - 1);
        }

        if (StringUtils.isBlank(requestUrl)) {
            return true;
        }
        if ("GET".equals(method) && requestUrl.endsWith("search")) {
            // 处理特殊的几个配置了search GET请求的URL
            if (checkGETMethodUrl(requestUrl)) {
                requestUrl = requestUrl.replaceAll("/search", "");
            }
        }
        List<String> urlList = getMenuUrlListData();
        if (CollectionUtils.isEmpty(urlList)) {
            buildMenuUrlListData();
            buildRolesToMenuUrlListData(null);
            urlList = getMenuUrlListData();
        }

        if (CollectionUtils.isNotEmpty(urlList) && urlList.contains(requestUrl)) {
            // 判断URL是菜单URL才需校验
            Set<String> permissionUrlList = getPermissionUrlListByUserId(userId);
            if (CollectionUtils.isEmpty(permissionUrlList)) {
                buildUserToRole(userId);
                permissionUrlList = getPermissionUrlListByUserId(userId);
            }
            if (CollectionUtils.isNotEmpty(permissionUrlList) &&
                    permissionUrlList.contains(requestUrl)) {
                return true;
            }
            return false;
        } else {
            return true;
        }

    }

    /**
     * @Description 获取所有菜单URL
     * <AUTHOR>
     * @date 2019/10/23 9:53
     * @version 1.0
     */
    public static List<String> getMenuUrlListData() {
        String result = StringRedisUtils.get(MENU_URL_LIST_KEY);
        if (StringUtils.isNotBlank(result)) {
            return CommonUtils.splitList(result, ",");
        } else {
            return null;
        }
    }

    /**
     * @Description 保存所有菜单URL
     * <AUTHOR>
     * @date 2019/10/23 9:52
     * @version 1.0
     */
    public static void buildMenuUrlListData() {
        MenuService menuService = SpringUtils.getBean("menuService", MenuService.class);
        MenuQueryCondition query = new MenuQueryCondition();
        query.setLevel(3);
        List<Menu> menus = menuService.queryMenus(query, null);
        List<String> urlList = handleMenuUrlList(menus);
        if (CollectionUtils.isNotEmpty(urlList)) {
            StringRedisUtils.set(MENU_URL_LIST_KEY, StringUtils.join(urlList, ","));
        }
    }

    /**
     * @Description 更新角色-URL数据
     * <AUTHOR>
     * @date 2019/10/23 19:16
     * @version 1.0
     */
    public static void buildRolesToMenuUrlListData(MenuQueryCondition query) {
        MenuService menuService = SpringUtils.getBean("menuService", MenuService.class);
        Map<Integer, List<Menu>> map = menuService.queryMenuListMap(query);
        if (map != null) {
            for (Integer roleId : map.keySet()) {
                List<Menu> menus = map.get(roleId);
                if (CollectionUtils.isNotEmpty(menus)) {
                    setMenuUrlListToRedis(roleId, menus);
                }
            }
        }
    }

    /**
     * 查询用户角色并更新缓存
     *
     * @param userId
     */
    public static void buildUserToRole(Integer userId) {
        RoleService roleService = SpringUtils.getBean("roleService", RoleService.class);
        List<Role> roles = roleService.queryRoleByUserId(userId);
        setUserAndRoleDataToRedis(userId, roles);
    }

    /**
     * @Description 从菜单中获取URL
     * <AUTHOR>
     * @date 2019/10/23 19:15
     * @version 1.0
     */
    public static List<String> handleMenuUrlList(List<Menu> menus) {
        List<String> urlList = null;
        if (CollectionUtils.isNotEmpty(menus)) {
            urlList = new ArrayList<>();
            for (Menu menu : menus) {
                String url = handleMenuUrl(menu.getUrl());
                if (StringUtils.isNotBlank(url)) {
                    urlList.add(url);
                }
            }
        }
        return urlList;
    }

    /**
     * 菜单URL特殊字符处理
     *
     * @param menuUrl
     * @return
     */
    public static String handleMenuUrl(String menuUrl) {
        if (StringUtils.isNotBlank(menuUrl)) {
            String url = menuUrl.trim();
            if (url.contains("?")) {
                url = url.split("\\?")[0];
            }
            url = url.replaceAll("//", "/");
            url = url.replaceAll("//", "/");
            if (url.startsWith("/")) {
                url = url.replaceFirst("/", "");
            }
            if (url.endsWith("/")) {
                url = url.substring(0, url.length() - 1);
            }
            return url;
        } else {
            return null;
        }
    }

    /**
     * 查询拥有此菜单的角色
     *
     * @param menuCode
     * @return
     */
    public static List<Integer> getRoleIdListByMenuCode(String menuCode) {
        if (StringUtils.isBlank(menuCode)) {
            return null;
        }
        List<Integer> roleList = null;
        MenuService menuService = SpringUtils.getBean("menuService", MenuService.class);
        MenuQueryCondition query = new MenuQueryCondition();
        query.setMenuCode(menuCode);
        Menu menu = menuService.queryMenu(query);
        if (menu != null && menu.getLevel() != null && menu.getLevel().equals(1)
                || menu.getLevel().equals(2) || menu.getLevel().equals(3)) {
            RoleService roleService = SpringUtils.getBean("roleService", RoleService.class);
            RoleQueryCondition roleQuery = new RoleQueryCondition();
            roleQuery.setMenuCode(menuCode);
            List<Role> roles = roleService.queryRoles(roleQuery, null);
            if (CollectionUtils.isNotEmpty(roles)) {
                roleList = new ArrayList<>();
                for (Role role : roles) {
                    roleList.add(role.getRoleId());
                }
            }
        }
        return roleList;
    }

    /**
     * 更新这些角色的菜单URL数据
     *
     * @param roleList
     */
    public static void updateRoleToMenuUrlListDataByRoleIdList(List<Integer> roleList) {
        if (CollectionUtils.isNotEmpty(roleList)) {
            MenuService menuService = SpringUtils.getBean("menuService", MenuService.class);
            MenuQueryCondition query = new MenuQueryCondition();
            query.setRoleIdList(roleList);
            Map<Integer, List<Menu>> map = menuService.queryMenuListMap(query);
            if (map != null) {
                // 更新角色对应菜单URL数据
                for (Integer roleId : roleList) {
                    List<Menu> menus = map.get(roleId);
                    if (CollectionUtils.isNotEmpty(menus)) {
                        UserRolePermissionUtils.setMenuUrlListToRedis(roleId, menus);
                    } else {
                        UserRolePermissionUtils.delRoleToMenuUrlData(roleId);
                    }
                }
            } else {
                for (Integer roleId : roleList) {
                    UserRolePermissionUtils.delRoleToMenuUrlData(roleId);
                }
            }
        }
    }

    public static boolean checkGETMethodUrl(String url) {
        // value = "search",
        List<String> urls = new ArrayList<>();
        CollectionUtils.addAll(urls, CHECK_URL_LIST);
        if (urls.contains(url)) {
            return true;
        }
        return false;
    }

    private static final String[] CHECK_URL_LIST = new String[]{
            "warehouse/pickInventoryDemands/search",
            "warehouse/inventoryTasks/search",
            "warehouse/inventoryTaskReviews/search",
            "checkins/search",
            "allocationCheckin/search",
            "checkInException/search",
            "uniqueSkus/search",
    };

}
