package com.estone.system.param.listener;

import com.alibaba.fastjson.JSON;
import com.estone.common.executors.ExecutorUtils;
import com.estone.common.util.CommonUtils;
import com.estone.sku.service.WhSkuExtendService;
import com.estone.system.param.bean.SystemParam;
import com.estone.system.param.event.SystemParamDeleteEvent;
import com.estone.system.param.event.SystemParamUpdateEvent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023-02-13 17:40
 */
@Component
@Slf4j
public class SystemParamListener {

    @Resource
    private WhSkuExtendService whSkuExtendService;

    private final static String SKU_TAG_PARAM = "SKU_TAG_PARAM";

    private static ThreadPoolExecutor threadPoolExecutor = ExecutorUtils.newFixedThreadPool(1);


    @EventListener
    @Transactional(rollbackFor = Exception.class)
    public void onApplicationEvent(SystemParamDeleteEvent event) {
        ExecutorUtils.execute(threadPoolExecutor, () -> {
            SystemParam systemParam = event.getObj();
            log.info("执行系统配置删除事件响应，所删除配置为-{}", JSON.toJSONString(systemParam));
            if (!isSkuTagEvent(systemParam)) {
                return;
            }
            List<String> tagList = CommonUtils.splitList(systemParam.getParamValue(), ",");
            whSkuExtendService.deleteSkuTags(tagList);
        }, "系统配置删除");
    }


    @EventListener
    @Transactional(rollbackFor = Exception.class)
    public void onApplicationEvent(SystemParamUpdateEvent event) {
        ExecutorUtils.execute(threadPoolExecutor, () -> {
            SystemParam oldSystemParam = event.getBefore();
            SystemParam systemParam = event.getAfter();
            log.info("执行系统配置更新事件响应，更新前配置为-{},更新后配置为-{}", JSON.toJSONString(oldSystemParam),JSON.toJSONString(systemParam));
            if (!isSkuTagEvent(oldSystemParam) || !isSkuTagEvent(systemParam)) {
                return;
            }
            List<String> oldTagList = CommonUtils.splitList(oldSystemParam.getParamValue(), ",");
            List<String> tagList = CommonUtils.splitList(systemParam.getParamValue(), ",");
            //得到tagList中没有，而oldTagList中有的数据
            List<String> deleteTagList = oldTagList.stream()
                    .filter(tag -> !tagList.contains(tag))
                    .collect(Collectors.toList());
            whSkuExtendService.deleteSkuTags(deleteTagList);
        }, "系统配置更新");
    }

    /**
     * 用于判断是否为标签修改的相关事件
     *
     * @param systemParam 数据对象
     * @return 是返回true, 不是返回false
     */
    private boolean isSkuTagEvent(SystemParam systemParam) {
        return SKU_TAG_PARAM.equals(systemParam.getParamKey());
    }
}
