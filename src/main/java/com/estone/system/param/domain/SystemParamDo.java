package com.estone.system.param.domain;

import com.estone.system.param.bean.SystemParam;
import com.estone.system.param.bean.SystemParamQueryCondition;
import com.whq.tool.component.Pager;
import java.util.HashMap;
import java.util.Map;

public class SystemParamDo {
    private SystemParam systemParam;

    private SystemParamQueryCondition query = new SystemParamQueryCondition();

    private Map<String, Map<String, SystemParam>> systemParams = new HashMap<String, Map<String, SystemParam>>();

    private Pager page = new Pager();

    public SystemParam getSystemParam() {
        return systemParam;
    }

    public void setSystemParam(SystemParam systemParam) {
        this.systemParam = systemParam;
    }

    public SystemParamQueryCondition getQuery() {
        return query;
    }

    public void setQuery(SystemParamQueryCondition query) {
        this.query = query;
    }

    public Map<String, Map<String, SystemParam>> getSystemParams() {
        return systemParams;
    }

    public void setSystemParams(Map<String, Map<String, SystemParam>> systemParams) {
        this.systemParams = systemParams;
    }

    public Pager getPage() {
        return page;
    }

    public void setPage(Pager page) {
        this.page = page;
    }
}