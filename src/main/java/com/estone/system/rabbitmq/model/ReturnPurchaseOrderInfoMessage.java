package com.estone.system.rabbitmq.model;

/**
 * ---------------------------------------------------------------------------
 * Copyright (c) 2018, estone- All Rights Reserved.
 * Project Name:wms
 * Package Name:com.estone.system.rabbitmq.model
 * File Name:ReturnPurchaseOrderInfoMessage.java
 * Description:采购传递的消息体
 * Author:Yimeil
 * Date:2019-07-15 16:11
 * ---------------------------------------------------------------------------
 */

import com.estone.checkin.bean.ReturnPurchaseOrder;
import com.estone.checkin.bean.ReturnPurchaseOrderLog;
import lombok.Data;

import java.sql.Timestamp;
import java.util.List;

@Data
public class ReturnPurchaseOrderInfoMessage {

    /**
     * 退货单信息
     */

    private List<ReturnPurchaseOrder> returnOrders;

    /**
     * 操作日志
     */

    private List<ReturnPurchaseOrderLog> returnCheckRecord;

    /**
     * 是否退回
     */

    private Boolean isBack;

    /**
     * 待仓库操作人
     */
    private String waitWmsHandleUser;

    /**
     * 待仓库操作时间
     */
    private Timestamp waitWmsHandleDate;
}
