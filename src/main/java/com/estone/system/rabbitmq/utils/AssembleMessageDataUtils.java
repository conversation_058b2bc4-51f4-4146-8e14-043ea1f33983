package com.estone.system.rabbitmq.utils;

import com.alibaba.fastjson.JSON;
import com.estone.common.CacheName;
import com.estone.common.util.CacheUtils;
import com.estone.common.util.MapUtils;
import com.estone.core.mq.Queues;
import com.estone.core.mq.RabbitMqExchange;
import com.estone.core.mq.RoutingKey;
import com.estone.scan.deliver.bean.DeliverRecordDTO;
import com.estone.sku.bean.ExpManage;
import com.estone.sku.bean.WhSku;
import com.estone.system.rabbitmq.bean.AmqMessage;
import com.estone.system.rabbitmq.common.AmqMessageModuleName;
import com.estone.system.rabbitmq.model.*;
import com.estone.system.user.bean.SaleUser;
import com.estone.temu.bean.TemuReturnPackage;
import com.whq.tool.context.DataContextHolder;

import java.sql.Timestamp;
import java.util.*;
import java.util.function.Function;

public class AssembleMessageDataUtils {


    /**
     * @param productSkuMessage
     * @return
     * @Description: 组装库存变更消息发送产品系统
     * @Author: fangxin
     * @Date: 2020/01/16
     * @Version: 0.0.1
     */
    public static AmqMessage assembleStockDataDiff(ProductSkuMessage productSkuMessage) {
        AmqMessage amqMessage = new AmqMessage();
        // 模块名称
        amqMessage.setModuleName(AmqMessageModuleName.PUSH_PRODUCT_STOCK_INFO.getCode());
        // 交换机
        amqMessage.setExchange(RabbitMqExchange.PRODUCT_DIRECT_EXCHANGE);
        // 队列
        amqMessage.setQueue(Queues.WMS_PRODUCT_SKU_STOCK_QUEUE);
        // 路由名
        amqMessage.setRoutingKey(RoutingKey.WMS_PRODUCT_SKU_STOCK_KEY);
        // 关联字段
        String relevantParam = productSkuMessage.getSku();
        amqMessage.setRelevantParam(relevantParam);
        // 消息体
        Integer createBy = DataContextHolder.getUserId() == null ? 1 : DataContextHolder.getUserId();
        SaleUser saleUser = CacheUtils.get(CacheName.USER_CACHE, String.valueOf(createBy), SaleUser.class);
        productSkuMessage.setAccountName(getAccountName(saleUser));
        productSkuMessage.setHandleTime(System.currentTimeMillis());
        String messageBody = JSON.toJSONString(productSkuMessage);
        amqMessage.setMessageBody(messageBody);
        // 创建人
        amqMessage.setCreateBy(createBy);
        // 创建时间
        amqMessage.setCreateTime(new Timestamp(System.currentTimeMillis()));
        // 发送状态
        amqMessage.setSendStatus(false);
        // 重试次数
        amqMessage.setRetryLimit(0);

        return amqMessage;
    }

    /**
     * @param assembleTransferStockDataDiff
     * @return
     * @Description: 中转仓库存变更推送redis 总库存，分平台
     * @Author: fangxin
     * @Date: 2024/02/28
     * @Version: 0.0.1
     */
    public static AmqMessage assembleTransferStockDataDiff(String sku,String messageBody) {
        AmqMessage amqMessage = new AmqMessage();
        // 模块名称
        amqMessage.setModuleName(AmqMessageModuleName.PUSH_TRANSFER_STOCK_INFO.getCode());

        amqMessage.setExchange(AmqMessageModuleName.PUSH_TRANSFER_STOCK_INFO.getCode());
        // 交换机
        amqMessage.setExchange(AmqMessageModuleName.PUSH_TRANSFER_STOCK_INFO.getCode());
        // 队列
        amqMessage.setQueue(AmqMessageModuleName.PUSH_TRANSFER_STOCK_INFO.getCode());
        // 路由名
        amqMessage.setRoutingKey(AmqMessageModuleName.PUSH_TRANSFER_STOCK_INFO.getCode());

        amqMessage.setRelevantParam(sku);

        amqMessage.setMessageBody(messageBody);
        // 创建人
        amqMessage.setCreateBy(DataContextHolder.getUserId() == null ? 1 : DataContextHolder.getUserId());
        // 创建时间
        amqMessage.setCreateTime(new Timestamp(System.currentTimeMillis()));
        // 发送状态
        amqMessage.setSendStatus(false);
        // 重试次数
        amqMessage.setRetryLimit(0);

        return amqMessage;
    }

    /**
     * @param assembleTransferStockDataDiff
     * @return
     * @Description: 组合sku库存变更推送redis 总库存
     * @Author: fangxin
     * @Date: 2024/02/28
     * @Version: 0.0.1
     */
    public static AmqMessage assembleCombineStockDataDiff(String sku,String messageBody) {
        AmqMessage amqMessage = new AmqMessage();
        // 模块名称
        amqMessage.setModuleName(AmqMessageModuleName.PUSH_COMBINE_SKU_STOCK_INFO.getCode());

        amqMessage.setExchange(AmqMessageModuleName.PUSH_COMBINE_SKU_STOCK_INFO.getCode());
        // 交换机
        amqMessage.setExchange(AmqMessageModuleName.PUSH_COMBINE_SKU_STOCK_INFO.getCode());
        // 队列
        amqMessage.setQueue(AmqMessageModuleName.PUSH_COMBINE_SKU_STOCK_INFO.getCode());
        // 路由名
        amqMessage.setRoutingKey(AmqMessageModuleName.PUSH_COMBINE_SKU_STOCK_INFO.getCode());

        amqMessage.setRelevantParam(sku);

        amqMessage.setMessageBody(messageBody);
        // 创建人
        amqMessage.setCreateBy(DataContextHolder.getUserId() == null ? 1 : DataContextHolder.getUserId());
        // 创建时间
        amqMessage.setCreateTime(new Timestamp(System.currentTimeMillis()));
        // 发送状态
        amqMessage.setSendStatus(false);
        // 重试次数
        amqMessage.setRetryLimit(0);

        return amqMessage;
    }


    /**
     * @param productSkuMessage
     * @return
     * @Description: 组装库存变更消息发送产品系统
     * @Author: Administrator
     * @Date: 2019/08/29
     * @Version: 0.0.1
     */
    public static AmqMessage assembleStockData(ProductSkuMessage productSkuMessage) {
        AmqMessage amqMessage = new AmqMessage();
        // 模块名称
        if (null != productSkuMessage.getSurplusQuantity()) {
            // 可用库存变更
            amqMessage.setModuleName(AmqMessageModuleName.PUSH_PRODUCT_STOCK_INFO.getCode());
        } else {
            // 调拨数量变更
            amqMessage.setModuleName(AmqMessageModuleName.PUSH_PRODUCT_ALLOCATION_INFO.getCode());
        }
        // 交换机
        amqMessage.setExchange(RabbitMqExchange.PRODUCT_DIRECT_EXCHANGE);
        // 队列
        amqMessage.setQueue(Queues.WMS_PRODUCT_SKU_CHANGE_STOCK_QUEUE);
        // 路由名
        amqMessage.setRoutingKey(RoutingKey.WMS_PRODUCT_SKU_CHANGE_STOCK_KEY);
        // 关联字段
        String relevantParam = productSkuMessage.getSku();
        amqMessage.setRelevantParam(relevantParam);
        // 消息体
        Integer createBy = DataContextHolder.getUserId() == null ? 1 : DataContextHolder.getUserId();
        SaleUser saleUser = CacheUtils.get(CacheName.USER_CACHE, String.valueOf(createBy), SaleUser.class);
        productSkuMessage.setAccountName(getAccountName(saleUser));
        productSkuMessage.setHandleTime(System.currentTimeMillis());
        String messageBody = JSON.toJSONString(productSkuMessage);
        amqMessage.setMessageBody(messageBody);
        // 创建人
        amqMessage.setCreateBy(createBy);
        // 创建时间
        amqMessage.setCreateTime(new Timestamp(System.currentTimeMillis()));
        // 发送状态
        amqMessage.setSendStatus(false);
        // 重试次数
        amqMessage.setRetryLimit(0);

        return amqMessage;
    }

    /**
     * @param productSkuMessage
     * @return
     * @Description: 组装sku信息变更消息发送产品系统
     * @Author: Administrator
     * @Date: 2019/08/29
     * @Version: 0.0.1
     */
    public static AmqMessage assembleSkuData(ProductSkuMessage productSkuMessage) {
        AmqMessage amqMessage = new AmqMessage();
        // 模块名称
        amqMessage.setModuleName(AmqMessageModuleName.PUSH_PRODUCT_SKU_INFO.getCode());
        // 交换机
        amqMessage.setExchange(RabbitMqExchange.PRODUCT_DIRECT_EXCHANGE);
        // 队列
        amqMessage.setQueue(Queues.WMS_PRODUCT_SKU_CHANGE_QUEUE);
        // 路由名
        amqMessage.setRoutingKey(RoutingKey.WMS_PRODUCT_SKU_CHANGE_KEY);
        // 关联字段（sku=uuid）用于消息回执删除消息
        String relevantParam = productSkuMessage.getSku().concat("=").concat(UUID.randomUUID().toString());
        amqMessage.setRelevantParam(relevantParam);
        // 消息体
        String updateField = getUpdateField(productSkuMessage);
        productSkuMessage.setUpdateField(updateField);
        Integer createBy = DataContextHolder.getUserId() == null ? 1 : DataContextHolder.getUserId();
        SaleUser saleUser = CacheUtils.get(CacheName.USER_CACHE, String.valueOf(createBy), SaleUser.class);
        productSkuMessage.setAccountName(getAccountName(saleUser));
        long currentTimeMillis = System.currentTimeMillis();
        productSkuMessage.setHandleTime(currentTimeMillis);
        productSkuMessage.setMessagePrimaryKey(relevantParam);
        productSkuMessage.setSendWarehouseId(CacheUtils.getLocalWarehouseId());
        String messageBody = JSON.toJSONString(productSkuMessage);
        amqMessage.setMessageBody(messageBody);
        // 创建人
        amqMessage.setCreateBy(createBy);
        // 创建时间
        amqMessage.setCreateTime(new Timestamp(currentTimeMillis));
        // 发送状态
        amqMessage.setSendStatus(false);
        // 重试次数
        amqMessage.setRetryLimit(0);

        return amqMessage;
    }


    /**
     * @param sameSPUMap 需要组装信息的sku数据，key为变更了净重的sku信息，value为同SPU的其它sku
     * @Description: 组装具有相同SPU信息的发送到订单系统
     */
    public static AmqMessage assembleSameSPUData(Map<WhSku, List<WhSku>> sameSPUMap) {
        AmqMessage amqMessage = new AmqMessage();
        // 交换机
        amqMessage.setExchange(RabbitMqExchange.PUSH_WMS_DIRECT_EXCHANGE);
        // 队列
        amqMessage.setQueue(Queues.PUSH_WMS_SKU_WEIGHT_CHANGE);

        // 消息体
        List<WhSkuWeightMessage> whSkuWeightMessages = new ArrayList<>();
        Integer createBy = DataContextHolder.getUserId() == null ? 1 : DataContextHolder.getUserId();
        long currentTimeMillis = System.currentTimeMillis();
        for (Map.Entry<WhSku, List<WhSku>> entry : sameSPUMap.entrySet()) {
            WhSku modifyWhSku = entry.getKey();
            for (WhSku whSku : entry.getValue()) {
                WhSkuWeightMessage whSkuWeightMessage = new WhSkuWeightMessage();
                whSkuWeightMessage.setImageUrl(whSku.getImageUrl());
                whSkuWeightMessage.setSku(whSku.getSku());
                whSkuWeightMessage.setOriginalWeight(whSku.getWeight());
                whSkuWeightMessage.setWeight(modifyWhSku.getWeight());
                whSkuWeightMessages.add(whSkuWeightMessage);
            }
        }
        String messageBody = JSON.toJSONString(whSkuWeightMessages);
        amqMessage.setMessageBody(messageBody);
        // 创建人
        amqMessage.setCreateBy(createBy);
        // 创建时间
        amqMessage.setCreateTime(new Timestamp(currentTimeMillis));
        // 发送状态
        amqMessage.setSendStatus(false);
        // 重试次数
        amqMessage.setRetryLimit(0);

        return amqMessage;
    }


    /**
     * 组装入库单变更信息发送至产品系统
     *
     * @param pushCheckinMessage
     * @return
     */
    public static AmqMessage assembleCheckInData(PushCheckinMessage pushCheckinMessage) {
        AmqMessage amqMessage = new AmqMessage();
        // 模块名称
        amqMessage.setModuleName(AmqMessageModuleName.PUSH_CHECK_IN_INFO.getCode());
        // 交换机
        amqMessage.setExchange(RabbitMqExchange.PMS_WMS_DIRECT_EXCHANGE);
        // 队列
        amqMessage.setQueue(Queues.PUSH_CHECKIN_TO_PMS);
        // 路由名
        amqMessage.setRoutingKey(RoutingKey.PUSH_CHECKIN_TO_PMS);
        // 关联字段（expressId=uuid）用于消息回执删除消息
        String relevantParam = pushCheckinMessage.getExpressId().concat("=").concat(UUID.randomUUID().toString());
        amqMessage.setRelevantParam(relevantParam);
        // 消息体
        String messageBody = JSON.toJSONString(pushCheckinMessage);
        amqMessage.setMessageBody(messageBody);
        // 创建人
        amqMessage.setCreateBy(DataContextHolder.getUserId() == null ? 1 : DataContextHolder.getUserId());
        // 创建时间
        amqMessage.setCreateTime(new Timestamp(System.currentTimeMillis()));
        // 发送状态
        amqMessage.setSendStatus(false);
        // 重试次数
        amqMessage.setRetryLimit(0);

        return amqMessage;
    }

    /**
     * @param packingMaterialMessage
     * @return
     */
    public static AmqMessage assemblePackingMaterialData(PackingMaterialMessage packingMaterialMessage) {
        AmqMessage amqMessage = new AmqMessage();
        // 模块名称
        amqMessage.setModuleName(AmqMessageModuleName.PUSH_PACKING_MATERIAL_INFO.getCode());
        // 交换机
        amqMessage.setExchange(RabbitMqExchange.PRODUCT_DIRECT_EXCHANGE);
        // 队列
        amqMessage.setQueue(Queues.WMS_PRODUCT_PACKING_MATERIAL_QUEUE);
        // 路由名
        amqMessage.setRoutingKey(RoutingKey.WMS_PRODUCT_PACKING_MATERIAL_KEY);
        // 关联字段（articleNumber=uuid）用于消息回执删除消息
        String relevantParam = packingMaterialMessage.getArticleNumber().concat("=").concat(UUID.randomUUID().toString());
        amqMessage.setRelevantParam(relevantParam);
        // 消息体
        String messageBody = JSON.toJSONString(packingMaterialMessage);
        amqMessage.setMessageBody(messageBody);
        // 创建人
        amqMessage.setCreateBy(DataContextHolder.getUserId() == null ? 1 : DataContextHolder.getUserId());
        // 创建时间
        amqMessage.setCreateTime(new Timestamp(System.currentTimeMillis()));
        // 发送状态
        amqMessage.setSendStatus(false);
        // 重试次数
        amqMessage.setRetryLimit(0);

        return amqMessage;
    }

    public static AmqMessage assembleDataToFinance(String moduleName, Function<AmqMessage, String> dataFun) {
        AmqMessage amqMessage = new AmqMessage();
        // 模块名称
        amqMessage.setModuleName(moduleName);

        amqMessage.setExchange(moduleName);
        // 交换机
        amqMessage.setExchange(moduleName);
        // 队列
        amqMessage.setQueue(moduleName);
        // 路由名
        amqMessage.setRoutingKey(moduleName);

        String messageBody = dataFun.apply(amqMessage);

        amqMessage.setMessageBody(messageBody);
        // 创建人
        amqMessage.setCreateBy(DataContextHolder.getUserId() == null ? 1 : DataContextHolder.getUserId());
        // 创建时间
        amqMessage.setCreateTime(new Timestamp(System.currentTimeMillis()));
        // 发送状态
        amqMessage.setSendStatus(false);
        // 重试次数
        amqMessage.setRetryLimit(0);

        return amqMessage;
    }

    // 组装仓库同步数据
    public static AmqMessage assembleWmsSyncData(String moduleName,Function<AmqMessage, String> dataFun) {
        return assembleWmsSyncData(moduleName,moduleName,moduleName,moduleName,dataFun);
    }
    public static AmqMessage assembleWmsSyncData(String exchange,String moduleName,String queue,
                                                 String routingKey, Function<AmqMessage, String> dataFun) {
        AmqMessage amqMessage = new AmqMessage();
        // 模块名称
        amqMessage.setModuleName(moduleName);
        // 交换机
        amqMessage.setExchange(exchange);
        // 队列
        amqMessage.setQueue(queue);
        // 路由名
        amqMessage.setRoutingKey(routingKey);

        String messageBody = dataFun.apply(amqMessage);

        amqMessage.setMessageBody(messageBody);
        // 创建人
        amqMessage.setCreateBy(DataContextHolder.getUserId() == null ? 1 : DataContextHolder.getUserId());
        // 创建时间
        amqMessage.setCreateTime(new Timestamp(System.currentTimeMillis()));
        // 发送状态
        amqMessage.setSendStatus(false);
        // 重试次数
        amqMessage.setRetryLimit(0);

        return amqMessage;
    }

    /**
     * @param omsFbaSkuMessage
     * @return
     * @Description: 组装库存变更消息发送OMS系统
     * @Author: Administrator
     * @Date: 2019/08/29
     * @Version: 0.0.1
     */
    public static AmqMessage assembleooFbaStockData(OmsFbaSkuMessage omsFbaSkuMessage) {
        AmqMessage amqMessage = new AmqMessage();

        amqMessage.setModuleName(AmqMessageModuleName.PUSH_OMS_FBA_STOCK_INFO.getCode());

        // 交换机
        amqMessage.setExchange(RabbitMqExchange.PUSH_WMS_DIRECT_EXCHANGE);
        // 队列
        amqMessage.setQueue(Queues.WMS_OMS_FBA_SKU_STOCK_QUEUE);
        // 路由名
        amqMessage.setRoutingKey(Queues.WMS_OMS_FBA_SKU_STOCK_QUEUE);
        // 关联字段
        String relevantParam = omsFbaSkuMessage.getItemLockKeys();
        amqMessage.setRelevantParam(relevantParam);
        // 消息体
        Integer createBy = DataContextHolder.getUserId() == null ? 1 : DataContextHolder.getUserId();
        SaleUser saleUser = CacheUtils.get(CacheName.USER_CACHE, String.valueOf(createBy), SaleUser.class);
        omsFbaSkuMessage.setAccountName(getAccountName(saleUser));
        omsFbaSkuMessage.setHandleTime(System.currentTimeMillis());
        String messageBody = JSON.toJSONString(omsFbaSkuMessage);
        amqMessage.setMessageBody(messageBody);
        // 创建人
        amqMessage.setCreateBy(createBy);
        // 创建时间
        amqMessage.setCreateTime(new Timestamp(System.currentTimeMillis()));
        // 发送状态
        amqMessage.setSendStatus(false);
        // 重试次数
        amqMessage.setRetryLimit(0);

        return amqMessage;
    }

    /**
     * @param productPacSkuMessage
     * @Description: 组装优选仓库存变更消息发送到产品系统
     * @Date: 2021/10/23
     */
    public static AmqMessage assemblePacStockData(ProductPacSkuMessage productPacSkuMessage) {
        AmqMessage amqMessage = new AmqMessage();

        amqMessage.setModuleName(AmqMessageModuleName.PUSH_PRODUCT_PAC_STOCK_INFO.getCode());

        // 交换机
        amqMessage.setExchange(RabbitMqExchange.PRODUCT_DIRECT_EXCHANGE);
        // 队列
        amqMessage.setQueue(Queues.WMS_PRODUCT_PAC_SKU_STOCK_QUEUE);
        // 路由名
        amqMessage.setRoutingKey(Queues.WMS_PRODUCT_PAC_SKU_STOCK_QUEUE);
        // 关联字段
        String relevantParam = productPacSkuMessage.getItemLockKeys();
        amqMessage.setRelevantParam(relevantParam);
        // 消息体
        Integer createBy = DataContextHolder.getUserId() == null ? 1 : DataContextHolder.getUserId();
        SaleUser saleUser = CacheUtils.get(CacheName.USER_CACHE, String.valueOf(createBy), SaleUser.class);
        productPacSkuMessage.setAccountName(getAccountName(saleUser));
        productPacSkuMessage.setHandleTime(System.currentTimeMillis());
        String messageBody = JSON.toJSONString(productPacSkuMessage);
        amqMessage.setMessageBody(messageBody);
        // 创建人
        amqMessage.setCreateBy(createBy);
        // 创建时间
        amqMessage.setCreateTime(new Timestamp(System.currentTimeMillis()));
        // 发送状态
        amqMessage.setSendStatus(false);
        // 重试次数
        amqMessage.setRetryLimit(0);

        return amqMessage;
    }

    /**
     * @param productSkuMessage
     * @return
     */
    public static AmqMessage assembleSkuUpTimeData(ProductSkuUpTimeMessage productSkuMessage) {
        AmqMessage amqMessage = new AmqMessage();
        // 模块名称
        amqMessage.setModuleName(AmqMessageModuleName.PUSH_WMS_SKU_UP_TIME_TO_PRODUCT.getCode());
        // 交换机
        amqMessage.setExchange(RabbitMqExchange.PRODUCT_DIRECT_EXCHANGE);
        // 队列
        amqMessage.setQueue(Queues.WMS_PUSH_SKU_UP_TIME_TO_PRODUCT);
        // 路由名
        amqMessage.setRoutingKey(Queues.WMS_PUSH_SKU_UP_TIME_TO_PRODUCT);
        // 关联字段（sku=uuid）用于消息回执删除消息
        String relevantParam = productSkuMessage.getSonSku();
        amqMessage.setRelevantParam(relevantParam);
        // 消息体
        Integer createBy = DataContextHolder.getUserId() == null ? 1 : DataContextHolder.getUserId();
        String messageBody = JSON.toJSONString(productSkuMessage);
        amqMessage.setMessageBody(messageBody);
        // 创建人
        amqMessage.setCreateBy(createBy);
        // 创建时间
        amqMessage.setCreateTime(new Timestamp(System.currentTimeMillis()));
        // 发送状态
        amqMessage.setSendStatus(false);
        // 重试次数
        amqMessage.setRetryLimit(0);

        return amqMessage;
    }

    /**
     * 仓库系统推送促销信息给产品系统
     *
     * @param pushStr
     * @return
     */
    public static AmqMessage pushPromotionInfo(String pushStr) {
        AmqMessage amqMessage = new AmqMessage();
        // 模块名称
        amqMessage.setModuleName(AmqMessageModuleName.PUSH_WMS_PROMOTION_INFO_STATE.getCode());
        // 交换机
        amqMessage.setExchange(RabbitMqExchange.PRODUCT_DIRECT_EXCHANGE);
        // 队列
        amqMessage.setQueue(Queues.WMS_PUSH_PROMOTION_INFO_QUEUE);
        // 路由名
        amqMessage.setRoutingKey(Queues.WMS_PUSH_PROMOTION_INFO_QUEUE);
        // 消息体
        Integer createBy = DataContextHolder.getUserId() == null ? 1 : DataContextHolder.getUserId();
        amqMessage.setMessageBody(pushStr);
        // 创建人
        amqMessage.setCreateBy(createBy);
        // 创建时间
        amqMessage.setCreateTime(new Timestamp(System.currentTimeMillis()));
        // 发送状态
        amqMessage.setSendStatus(false);
        // 重试次数
        amqMessage.setRetryLimit(0);

        return amqMessage;
    }

    /**
     * @param apvNoList
     * @Description: wms推送已退仓的YST到OMS
     * @Date: 2021/10/23
     */
    public static AmqMessage wmsPushSignOutYstData(List<String> apvNoList) {
        AmqMessage amqMessage = new AmqMessage();

        amqMessage.setModuleName(AmqMessageModuleName.PUSH_OMS_SIGN_OUT_YST_INFO.getCode());

        // 交换机
        amqMessage.setExchange(RabbitMqExchange.PUSH_WMS_DIRECT_EXCHANGE);
        // 队列
        amqMessage.setQueue(Queues.WMS_PUSH_SIGN_OUT_YST_QUEUE);
        // 路由名
        amqMessage.setRoutingKey(Queues.WMS_PUSH_SIGN_OUT_YST_QUEUE);

        // 消息体
        Integer createBy = DataContextHolder.getUserId() == null ? 1 : DataContextHolder.getUserId();

        String messageBody = JSON.toJSONString(apvNoList);

        amqMessage.setMessageBody(messageBody);
        // 创建人
        amqMessage.setCreateBy(createBy);
        // 创建时间
        amqMessage.setCreateTime(new Timestamp(System.currentTimeMillis()));
        // 发送状态
        amqMessage.setSendStatus(true);
        // 重试次数
        amqMessage.setRetryLimit(0);

        return amqMessage;
    }


    /**
     * @param expManageList
     * @description 推送保质期批次数据到刊登系统
     * @date 2022/11/28 16:05
     */
    public static AmqMessage pushExpManageData(List<ExpManage> expManageList) {
        AmqMessage amqMessage = new AmqMessage();

        amqMessage.setModuleName(AmqMessageModuleName.PUSH_EXP_MANAGE_INFO_TO_PUBLISH.getCode());

        // 交换机
        amqMessage.setExchange(RabbitMqExchange.PUBLISH_WMS_DIRECT_EXCHANGE);
        // 队列
        amqMessage.setQueue(Queues.PUSH_EXP_MANAGE_TO_PUBLISH_QUEUE);
        // 路由名
        amqMessage.setRoutingKey(Queues.PUSH_EXP_MANAGE_TO_PUBLISH_QUEUE);

        // 消息体
        Integer createBy = DataContextHolder.getUserId() == null ? 1 : DataContextHolder.getUserId();

        String messageBody = JSON.toJSONString(expManageList);

        amqMessage.setMessageBody(messageBody);
        // 创建人
        amqMessage.setCreateBy(createBy);
        // 创建时间
        amqMessage.setCreateTime(new Timestamp(System.currentTimeMillis()));
        // 发送状态
        amqMessage.setSendStatus(false);
        // 重试次数
        amqMessage.setRetryLimit(0);

        return amqMessage;
    }

    /**
     * @description: 推送包材信息到采购系统
     * <AUTHOR>
     * @date 2022/12/7 11:50
     */
    public static AmqMessage pushPackingMaterialData(PackingMaterialMessage packingMaterialMessage) {
        AmqMessage amqMessage = new AmqMessage();
        // 模块名称
        amqMessage.setModuleName(AmqMessageModuleName.PUSH_PURCHASE_PACKING_MATERIAL_INFO.getCode());
        // 交换机
        amqMessage.setExchange(RabbitMqExchange.PMS_WMS_DIRECT_EXCHANGE);
        // 队列
        amqMessage.setQueue(Queues.PUSH_PURCHASE_PACKING_MATERIAL_QUEUE);
        // 路由名
        amqMessage.setRoutingKey(Queues.PUSH_PURCHASE_PACKING_MATERIAL_QUEUE);
        amqMessage.setRelevantParam(packingMaterialMessage.getArticleNumber());
        // 消息体
        String messageBody = JSON.toJSONString(packingMaterialMessage);
        amqMessage.setMessageBody(messageBody);
        // 创建人
        amqMessage.setCreateBy(DataContextHolder.getUserId() == null ? 1 : DataContextHolder.getUserId());
        // 创建时间
        amqMessage.setCreateTime(new Timestamp(System.currentTimeMillis()));
        // 发送状态
        amqMessage.setSendStatus(false);
        // 重试次数
        amqMessage.setRetryLimit(0);

        return amqMessage;
    }

    /**
     * TEMU退仓单推送OMS
     * @return
     */
    public static AmqMessage pushOmsTemuReturnInfo(TemuReturnPackage temuReturnPackage) {
        AmqMessage amqMessage = new AmqMessage();
        // 模块名称
        amqMessage.setModuleName(AmqMessageModuleName.PUSH_OMS_TEMU_RETURN_INFO.getCode());
        // 交换机
        amqMessage.setExchange(RabbitMqExchange.PUSH_WMS_DIRECT_EXCHANGE);
        // 队列
        amqMessage.setQueue(Queues.WMS_PUSH_TEMU_RETURN_QUEUE);
        // 路由名
        amqMessage.setRoutingKey(Queues.WMS_PUSH_TEMU_RETURN_QUEUE);

        amqMessage.setRelevantParam(temuReturnPackage.getPackageNo());
        // 消息体
        String messageBody = JSON.toJSONString(temuReturnPackage);
        amqMessage.setMessageBody(messageBody);
        // 创建人
        amqMessage.setCreateBy(DataContextHolder.getUserId() == null ? 1 : DataContextHolder.getUserId());
        // 创建时间
        amqMessage.setCreateTime(new Timestamp(System.currentTimeMillis()));
        // 发送状态
        amqMessage.setSendStatus(false);
        // 重试次数
        amqMessage.setRetryLimit(0);

        return amqMessage;
    }

    /**
     * TEMU退仓单推送OMS
     * @return
     */
    public static AmqMessage assembleDeliverScan(DeliverRecordDTO dto) {
        AmqMessage amqMessage = new AmqMessage();
        // 模块名称
        amqMessage.setModuleName(AmqMessageModuleName.WMS_DELIVER_SCAN_PC_INFO.getCode());
        // 交换机
        amqMessage.setExchange(AmqMessageModuleName.WMS_DELIVER_SCAN_PC_INFO.getCode());
        // 队列
        amqMessage.setQueue(AmqMessageModuleName.WMS_DELIVER_SCAN_PC_INFO.getCode());
        // 路由名
        amqMessage.setRoutingKey(AmqMessageModuleName.WMS_DELIVER_SCAN_PC_INFO.getCode());

        amqMessage.setRelevantParam(dto.getApvNo());
        // 消息体
        String messageBody = JSON.toJSONString(dto);
        amqMessage.setMessageBody(messageBody);
        // 创建人
        amqMessage.setCreateBy(DataContextHolder.getUserId() == null ? 1 : DataContextHolder.getUserId());
        // 创建时间
        amqMessage.setCreateTime(new Timestamp(System.currentTimeMillis()));
        // 发送状态
        amqMessage.setSendStatus(false);
        // 重试次数
        amqMessage.setRetryLimit(0);
        return amqMessage;
    }


    // 获取Sku修改的字段
    private static String getUpdateField(ProductSkuMessage productSkuMessage) {
        String updateField = "";
        Map<String, Object> map = MapUtils.transBean2Map(productSkuMessage, true);
        Set<String> keySet = map.keySet();
        keySet.remove("sku");
        keySet.remove("messagePrimaryKey");
        keySet.remove("handleTime");
        keySet.remove("surplusQuantity");
        keySet.remove("updateField");
        int length = keySet.size();
        if (0 != length) {
            for (String key : keySet) {
                length--;
                if (length > 0) {
                    updateField += key.concat("=");
                } else {
                    updateField += key;
                }
            }
        }
        return updateField;
    }

    /**
     * 功能描述: 获取当前账号-姓名
     *
     * @param
     * @return
     * @Author: zhoucx
     * @Date: 2019/12/10
     * @Version: 0.0.1
     */
    public static String getAccountName(SaleUser saleUser) {
        String accountName = "SYSTEM(系统)";
        if (null == saleUser) {
            return accountName;
        }

        accountName = saleUser.getUsername().concat("-").concat(saleUser.getName());

        return accountName;
    }
}
