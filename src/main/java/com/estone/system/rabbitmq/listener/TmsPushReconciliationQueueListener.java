package com.estone.system.rabbitmq.listener;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.estone.apv.bean.ApvTrack;
import com.estone.apv.bean.ApvTrackQueryCondition;
import com.estone.apv.common.ApvPlatform;
import com.estone.apv.dao.ApvTrackDao;
import com.estone.system.rabbitmq.common.RabbitmqListenerSqlInit;
import com.estone.system.rabbitmq.handle.ServiceMessageHandle;
import com.estone.system.rabbitmq.model.ArcelReconciliationDetailsResponse;
import com.rabbitmq.client.Channel;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.core.ChannelAwareMessageListener;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2022/6/29 14:39
 **/
public class TmsPushReconciliationQueueListener  implements ChannelAwareMessageListener {
    private Logger logger = LoggerFactory.getLogger(WmsPacStockDelayListener.class);

    @Resource
    private ServiceMessageHandle serviceMessageHandle;

    @Resource
    private ApvTrackDao apvTrackDao;


    private boolean isInit = false;
    @Override
    public void onMessage(Message message, Channel channel) throws Exception {
        // 校验容器加载时所有bean是否均已实例化
        if (!isInit) {
            RabbitmqListenerSqlInit.validateSqlerInit(this.getClass());
            this.isInit = true;
        }
        // 获取消息体
        String body = new String(message.getBody(), "UTF-8");
        logger.warn("TmsPushReconciliationQueueListener message body -> " + body);
        if (doService(body)) {
            // 消费成功，移除队列
            serviceMessageHandle.basicACK(message, channel);
            logger.info("TmsPushReconciliationQueueListener 消费成功移除队列 message");
        } else {
            // 消费失败，重回队列
           // serviceMessageHandle.basicNACK(message, channel);
            logger.info("TmsPushReconciliationQueueListener 消费失败重回队列 message:");
        }
    }
    private boolean doService(String body) {
        try {
            JSONObject obj = JSON.parseObject(body);
            if (null == obj) {
                return true;
            }
            String msgBody = obj.getString("body");

            if (StringUtils.isEmpty(msgBody))return true;
            List<ArcelReconciliationDetailsResponse>  detailsResponses= JSONObject.parseArray( msgBody, ArcelReconciliationDetailsResponse.class);
            if (CollectionUtils.isEmpty(detailsResponses)) {
                return true;
            }
            List<String> trackingNumberList = detailsResponses.stream().map(ArcelReconciliationDetailsResponse::getTrackingOrderNo).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(trackingNumberList)) {
                logger.info(body+"跟踪单号为空！");
                return true;
            }
            ApvTrackQueryCondition apvTrackQueryCondition=new ApvTrackQueryCondition();
            apvTrackQueryCondition.setTrackingNumberList(trackingNumberList);
            apvTrackQueryCondition.setSaleChannel(ApvPlatform.SMT.intCode());
            List<ApvTrack> apvTracks = apvTrackDao.queryApvTrackByTrackingNumberList(apvTrackQueryCondition);
            if (CollectionUtils.isEmpty(apvTracks)) {
                logger.info(body+"数据不存在！");
                return true;
            }

            Map<String, ApvTrack> trackMap = apvTracks.stream().collect(Collectors.toMap(ApvTrack::getTrackingNumberBill, a -> a));

            List<ApvTrack> updateApvTrackList=new ArrayList<>();
            for (ArcelReconciliationDetailsResponse detailsResponse : detailsResponses) {

                ApvTrack apvTrack = trackMap.get(detailsResponse.getTrackingOrderNo());

                if (apvTrack==null){
                    logger.info("TmsPushReconciliationQueueListener-跟踪单号："+detailsResponse.getTrackingOrderNo()+" 数据不存在或改平台为速卖通平台单号！");
                    continue;
                }
                ApvTrack updateTrack=new ApvTrack();

                Double actualWeigh = Optional.ofNullable(apvTrack.getActualWeightBill()).orElse(0.0);
                Double billWeight=Optional.ofNullable(detailsResponse.getLogisticsPrice()).orElse(0.0);
                //账单重量差等于  标准重量-账单重量
                updateTrack.setBillWeightDiff(actualWeigh-billWeight);
                //账单重量
                updateTrack.setBillWeightCount(billWeight);
                updateTrack.setId(apvTrack.getId());

                updateApvTrackList.add(updateTrack);
            }
            if (CollectionUtils.isNotEmpty(updateApvTrackList)) {
                apvTrackDao.batchUpdateApvTrack(updateApvTrackList);
            }
            return true;
        } catch (Exception e) {
            logger.error("消息队列TmsPushReconciliationQueueListener消费失败,解析失败！", e);
        }
        return true;
    }
}
