package com.estone.system.rabbitmq.listener;

import com.alibaba.fastjson.JSON;
import com.estone.loanedout.bean.WhLendDestroying;
import com.estone.loanedout.service.WhLendDestroyingService;
import com.estone.system.rabbitmq.common.RabbitmqListenerSqlInit;
import com.estone.system.rabbitmq.handle.ServiceMessageHandle;
import com.rabbitmq.client.Channel;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.core.ChannelAwareMessageListener;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024-06-01 17:49
 * 接收产品系统创建的核销单
 */
public class ProductWhLendDestoryQueueListener implements ChannelAwareMessageListener {

    private Logger logger = LoggerFactory.getLogger(ProductWhLendDestoryQueueListener.class);

    private boolean isInit = false;

    @Resource
    private ServiceMessageHandle serviceMessageHandle;

    @Resource
    private WhLendDestroyingService whLendDestroyingService;

    @Override
    public void onMessage(Message message, Channel channel) throws Exception {
        // 校验容器加载时所有bean是否均已实例化
        if (!isInit) {
            RabbitmqListenerSqlInit.validateSqlerInit(this.getClass());
            this.isInit = true;
        }
        String body = new String(message.getBody(), "UTF-8");
        logger.info("ProductWhLendDestoryQueueListener message body -> "+body);
        if (StringUtils.isBlank(body)) {
            return;
        }
        List<WhLendDestroying> whLendDestroyings = JSON.parseArray(body,WhLendDestroying.class);
        whLendDestroyingService.receiveWhLendDestroying(whLendDestroyings);
        // 一次性消费，移除队列
        serviceMessageHandle.basicACK(message, channel);
    }
}
