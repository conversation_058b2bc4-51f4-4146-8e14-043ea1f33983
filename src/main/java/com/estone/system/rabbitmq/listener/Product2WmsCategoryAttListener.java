package com.estone.system.rabbitmq.listener;

import com.alibaba.fastjson.JSON;
import com.estone.sku.service.WhSkuExpandAttrService;
import com.estone.system.rabbitmq.common.RabbitmqListenerSqlInit;
import com.estone.system.rabbitmq.handle.ServiceMessageHandle;
import com.estone.system.rabbitmq.model.WhSkuExpandAttrMessage;
import com.rabbitmq.client.Channel;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.core.ChannelAwareMessageListener;

import javax.annotation.Resource;

/**
 * @Description: 产品系服装sku属性
 * 
 * @ClassName: ProductReceiptSkuToWmsListener
 * @Author: Administrator
 * @Date: 2019/08/30
 * @Version: 0.0.1
 */
public class Product2WmsCategoryAttListener implements ChannelAwareMessageListener {

    private Logger logger = LoggerFactory.getLogger(Product2WmsCategoryAttListener.class);

    private boolean isInit = false;

    @Resource
    private WhSkuExpandAttrService whSkuExpandAttrService;

    @Resource
    private ServiceMessageHandle serviceMessageHandle;

    @Override
    public void onMessage(Message message, Channel channel) throws Exception {
        // 校验容器加载时所有bean是否均已实例化
        if (!isInit) {
            RabbitmqListenerSqlInit.validateSqlerInit(this.getClass());
            this.isInit = true;
        }
        // 获取消息体
        String body = new String(message.getBody(), "UTF-8");
        logger.warn("ProductReceiptSkuToWmsListener message body -> " + body);
        // 业务处理
        doService(body);
        // 一次性消费，移除队列
        serviceMessageHandle.basicACK(message, channel);
    }

    private void doService(String body) {
        try {
            WhSkuExpandAttrMessage obj = JSON.parseObject(body, WhSkuExpandAttrMessage.class);

            if (null == obj) {
                return;
            }
            // 保存服装类目属性
            whSkuExpandAttrService.saveWhSkuExpandAttr(obj);
        }
        catch (Exception e) {
            logger.error("SKU获取服装属性失败", e);
        }
    }

}
