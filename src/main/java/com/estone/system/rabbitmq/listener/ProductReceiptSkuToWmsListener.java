package com.estone.system.rabbitmq.listener;

import javax.annotation.Resource;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.core.ChannelAwareMessageListener;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.estone.system.rabbitmq.bean.AmqMessageQueryCondition;
import com.estone.system.rabbitmq.common.AmqMessageModuleName;
import com.estone.system.rabbitmq.common.RabbitmqListenerSqlInit;
import com.estone.system.rabbitmq.handle.ServiceMessageHandle;
import com.estone.system.rabbitmq.service.AmqMessageService;
import com.rabbitmq.client.Channel;

/**
 * @Description: 产品系统sku信息变更消费后消息回执
 * 
 * @ClassName: ProductReceiptSkuToWmsListener
 * @Author: Administrator
 * @Date: 2019/08/30
 * @Version: 0.0.1
 */
public class ProductReceiptSkuToWmsListener implements ChannelAwareMessageListener {

    private Logger logger = LoggerFactory.getLogger(ProductReceiptSkuToWmsListener.class);

    private boolean isInit = false;

    @Resource
    private AmqMessageService amqMessageService;

    @Resource
    private ServiceMessageHandle serviceMessageHandle;

    @Override
    public void onMessage(Message message, Channel channel) throws Exception {
        // 校验容器加载时所有bean是否均已实例化
        if (!isInit) {
            RabbitmqListenerSqlInit.validateSqlerInit(this.getClass());
            this.isInit = true;
        }
        // 获取消息体
        String body = new String(message.getBody(), "UTF-8");
        logger.warn("ProductReceiptSkuToWmsListener message body -> " + body);
        // 业务处理
        doService(body);
        // 一次性消费，移除队列
        serviceMessageHandle.basicACK(message, channel);
    }

    private void doService(String body) {
        try {
            JSONObject obj = JSON.parseObject(body);

            if (null == obj) {
                return;
            }

            String messagePrimaryKey = obj.getString("messagePrimaryKey");

            if (StringUtils.isBlank(messagePrimaryKey)) {
                logger.warn("ProductReceiptSkuToWmsListener messagePrimaryKey is null !");
                return;
            }

            AmqMessageQueryCondition query = new AmqMessageQueryCondition();
            query.setModuleName(AmqMessageModuleName.PUSH_PRODUCT_SKU_INFO.getCode());
            query.setRelevantParam(messagePrimaryKey);
            amqMessageService.doReceiptSendMessage(query);

        }
        catch (Exception e) {
            logger.error("SKU消息回执失败", e);
        }
    }

}
