package com.estone.system.rabbitmq.listener;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.estone.apv.bean.*;
import com.estone.apv.common.ApvOrderType;
import com.estone.apv.common.ApvPlatform;
import com.estone.apv.common.ApvShearSign;
import com.estone.apv.common.ApvStatus;
import com.estone.apv.enums.ApvExpressStatus;
import com.estone.apv.enums.ApvOversizeStatus;
import com.estone.apv.enums.ApvTaxTypeEnum;
import com.estone.apv.service.*;
import com.estone.checkin.service.PurchaseApvOutStockMatchService;
import com.estone.combineSku.bean.WhCombineSku;
import com.estone.combineSku.bean.WhCombineSkuQueryCondition;
import com.estone.combineSku.service.WhCombineSkuService;
import com.estone.common.enums.CountryEnum;
import com.estone.common.util.*;
import com.estone.elasticsearch.model.AliexpressProductListing;
import com.estone.elasticsearch.service.EsProductListingService;
import com.estone.sku.bean.WhSku;
import com.estone.sku.bean.WhSkuQueryCondition;
import com.estone.sku.enums.SkuFlags;
import com.estone.sku.service.WhSkuService;
import com.estone.system.param.bean.SystemParam;
import com.estone.system.rabbitmq.common.RabbitmqListenerSqlInit;
import com.estone.system.rabbitmq.handle.ServiceMessageHandle;
import com.estone.system.rabbitmq.model.PushOrderResultMessage;
import com.estone.system.rabbitmq.producer.RabbitmqProducerService;
import com.estone.warehouse.bean.WhWarehouse;
import com.estone.warehouse.service.WhApvReturnService;
import com.estone.warehouse.service.WhWarehouseService;
import com.rabbitmq.client.Channel;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.ArrayUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.core.ChannelAwareMessageListener;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @ClassName: OmsPushApvQueueListener
 * @Description: oms推单消费者监听器
 * <AUTHOR>
 * @Date 2018年12月31日
 */
public class OmsPushApvQueueListener implements ChannelAwareMessageListener {

    private Logger logger = LoggerFactory.getLogger(OmsPushApvQueueListener.class);

    private boolean isInit = false;

    @Resource
    private WhApvService whApvService;

    @Resource
    private WhApvItemService whApvItemService;

    @Resource
    private WhSkuService whSkuService;

    @Resource
    private WhWarehouseService whWarehouseService;

    @Resource
    private ServiceMessageHandle serviceMessageHandle;

    @Resource
    private RabbitmqProducerService rabbitmqProducerService;

    @Resource
    private ApvWaybillService apvWaybillService;

    @Resource
    private ApvExpressService apvExpressService;

    @Resource
    private WhApvReturnService whApvReturnService;

    @Resource
    private AliExpressCallService aliExpressCallService;

    @Resource
    private ApvOversizeService apvOversizeService;

    @Autowired
    private PurchaseApvOutStockMatchService purchaseApvOutStockMatchService;

    @Resource
    private WhCombineSkuService whCombineSkuService;

    @Resource
    private EsProductListingService esProductListingService;

    @Resource
    private ApvTrackService apvTrackService;

    public void onMessage(Message message, Channel channel) throws Exception {
        // 校验容器加载时所有bean是否均已实例化
        if (!isInit) {
            RabbitmqListenerSqlInit.validateSqlerInit(this.getClass());
            this.isInit = true;
        }
        // 获取消息体
        String body = new String(message.getBody(), "UTF-8");
        logger.warn("omsPushApvQueueListener message body -> " + body);
        // 回传PMS消息体
        PushOrderResultMessage returnMessage = new PushOrderResultMessage();
        // 业务处理
        boolean result = doService(body, returnMessage);
        if (!result) {
            returnMessage.setStatus(false);
        }
        // 一次性消费，移除队列
        serviceMessageHandle.basicACK(message, channel);
        // 处理结果回传oms
        rabbitmqProducerService.notifyOmsOnPushOrder(returnMessage);
    }

    /**
     * @Title: doService
     * @Description: oms推单业务处理
     * @Date 2019年1月3日_上午9:26:47
     * @param body 消息体
     * @param returnMessage
     * @return
     */
    private boolean doService(String body, PushOrderResultMessage returnMessage) {
        try {
            JSONObject obj = JSON.parseObject(body);
            String jsonBody = obj.getString("body");

            if (StringUtils.isBlank(jsonBody)) {
                returnMessage.setStatus(false);
                returnMessage.setApvNo(null);
                returnMessage.setDdNo(null);
                returnMessage.setPushTime(null);
                returnMessage.setMessage("消息体为空");
                return false;
            }

            WhApv whApv = JSON.parseObject(jsonBody, WhApv.class);

            return doService(whApv, returnMessage);
        }
        catch (Exception e) {
            logger.error("解析mq消息体异常！", e);
            returnMessage.setStatus(false);
            returnMessage.setApvNo(null);
            returnMessage.setDdNo(null);
            returnMessage.setPushTime(null);
            returnMessage.setMessage("解析mq消息体异常");
            return false;
        }
    }

    /**
     * @Title: doService
     * @Description: wms写入apv发货单信息
     * @Date 2019年1月3日_上午9:48:29
     * @param whApv
     * @param returnMessage
     * @return
     */
    private boolean doService(WhApv whApv, PushOrderResultMessage returnMessage) {
        returnMessage.setStatus(false);
        returnMessage.setApvNo(whApv.getApvNo());
        returnMessage.setDdNo(whApv.getSalesRecordNumber());
        returnMessage.setPushTime(new Timestamp(System.currentTimeMillis()));
        try {
            if (!assembleData(whApv, returnMessage)) {
                return false;
            }
            whApv.setSellerEmail(whApv.getSale());
            // 大包发货，收货人信息
            if ((whApv.getShipStatus() != null && ApvOrderType.BALE.intCode() == whApv.getShipStatus())) {
                StringBuffer sBuf = new StringBuffer();
                sBuf.append("姓名：").append(whApv.getBuyerName()).append("\n")
                        .append("电话：").append(whApv.getBuyerMobile()).append("\n")
                        .append("详细地址：").append(whApv.getBuyerCountry()).append(",")
                        .append(whApv.getBuyerStateOrProvince()).append(",")
                        .append(whApv.getBuyerCity()).append(",")
                        .append(whApv.getBuyerStreet()).append(",").append(";");
                whApv.setBuyerEmail(sBuf.toString());
            }

            //当发货单平台为Amazon或速卖通（排除JIT类型发货单），且发货国家为欧洲的发货单标记为GPSR
            List<Integer> platformList = Arrays.asList(ApvPlatform.SMT.intCode(), ApvPlatform.AMAZON.intCode(),
                    ApvPlatform.FRUUGO.intCode(), ApvPlatform.FYNDIQ.intCode(), ApvPlatform.JOOM.intCode());

            //去除欧代或者英代标签
            if(CollectionUtils.isNotEmpty(whApv.getBuyerCheckoutList())){
                if(whApv.getBuyerCheckoutList().contains(ApvTaxTypeEnum.EUR_AGENT.getCode())) {
                    whApv.remBuyerCheckout(ApvTaxTypeEnum.EUR_AGENT);
                }
                if (whApv.getBuyerCheckoutList().contains(ApvTaxTypeEnum.UK_AGENT.getCode())){
                    whApv.remBuyerCheckout(ApvTaxTypeEnum.UK_AGENT);
                }
            }

            boolean gpsr = false;
            if (platformList.contains(whApv.getPlatform())
                    && !ApvOrderType.getJitIntCode().contains(whApv.getShipStatus())
                    && CountryEnum.getEuropeCountryCode().contains(whApv.getBuyerCountry())) {
                whApv.addBuyerCheckout(ApvTaxTypeEnum.GPSR);
                gpsr = true;
            }

            whApvService.createWhApv(whApv);
            SystemLogUtils.APVLOG.log(whApv.getId(), "APV初始发货类型", new String[][] { { whApv.getShipTypeArr() } });

            Map<String, String> platSkuIdMap = new HashMap<>();
            if (gpsr && ApvPlatform.SMT.intCode() == whApv.getPlatform()
                    && CollectionUtils.isNotEmpty(whApv.getWhApvItems())) {
                try {
                    List<AliexpressProductListing> listingInfo = esProductListingService
                            .getListingInfo(whApv.getSellerId(), whApv.getAllSkus());
                    platSkuIdMap = Optional.ofNullable(listingInfo).orElse(new ArrayList<>()).stream()
                            .filter(i -> StringUtils.isNotBlank(i.getPlatSkuId()))
                            .collect(Collectors.toMap(l -> (l.getProductId() + l.getArticleNumber()).toUpperCase(),
                                    AliexpressProductListing::getPlatSkuId));
                }
                catch (Exception e) {
                    logger.error("获取ES刊登listing失败：" + e.getMessage(), e);
                }
            }

            // 获取推单次数
            String apvPushCount = this.getApvCancelCount(whApv);
            List<WhApvItem> whApvItems = whApv.getWhApvItems();
            for (WhApvItem whApvItem : whApvItems) {
                if (ApvPlatform.JOOM.intCode() == whApv.getPlatform()
                        && StringUtils.isNotBlank(whApvItem.getOrderLineItemId())) {
                    whApvItem.setTransactionId(whApvItem.getOrderLineItemId());
                }
                else if (StringUtils.isNotBlank(whApvItem.getProductId())) {
                    String key = (whApvItem.getProductId() + whApvItem.getSku()).toUpperCase();
                    whApvItem.setTransactionId(platSkuIdMap.get(key));
                }
                if (StringUtils.isNotBlank(whApvItem.getSku())) {
                    WhSkuQueryCondition whSkuQueryCondition = new WhSkuQueryCondition();
                    whSkuQueryCondition.setSku(whApvItem.getSku());
                    WhSku whSku = whSkuService.queryWhSku(whSkuQueryCondition);
                    if (whSku != null) {
                        whApvItem.setSkuId(whSku.getId());
                    }
                    whApvItem.setApvId(whApv.getId());
                    whApvItem.setMultiAttr(apvPushCount);
                    whApvItemService.createWhApvItem(whApvItem);
                }
            }
            
            // 下载GPSR订单的PDF文件
            if (gpsr)
                apvWaybillService.downloadGpsrTag(whApv);

            String trackingNumber = whApv.getTrackingNumber();
            if (StringUtils.isNotBlank(trackingNumber) && !trackingNumber.equals(whApv.getSalesRecordNumber())
                    && !trackingNumber.equals(whApv.getApvNo())) {
                apvWaybillService.downLoadPDFFromTms(whApv.getApvNo(), whApv.getId());
            }
            // 创建大包数据
            if (ApvOrderType.getExpressIntCode().contains(whApv.getShipStatus())
                    || (whApv.getShipStatus() != null && ApvOrderType.BALE.intCode() == whApv.getShipStatus())){
                // 创建快递数据
                ApvExpress apvExpress = new ApvExpress();
                apvExpress.setApvNo(whApv.getApvNo());
                apvExpress.setStatus(ApvExpressStatus.UN_PUSH.intCode());
                apvExpressService.createApvExpress(apvExpress);
            }else if(whApv.getShipStatus() != null && ApvOrderType.PACKING.intCode() == whApv.getShipStatus()){
                // 创建超体积单数据
                ApvOversize apvOversize = new ApvOversize();
                apvOversize.setApvNo(whApv.getApvNo());
                apvOversize.setStatus(ApvOversizeStatus.UN_PUSH.intCode());
                apvOversizeService.createApvOversize(apvOversize);
            }else if(whApv.getShipStatus() != null && ApvOrderType.LACK_MERCHANDISE.intCode() == whApv.getShipStatus()){
                //缺货订单类型的发货方式，需要更新缺货订单单据信息
                List<String> skus = Optional.ofNullable(whApvItems).orElse(new ArrayList<>()).stream()
                        .map(WhApvItem::getSku)
                        .filter(StringUtils::isNotBlank)
                        .collect(Collectors.toList());
                String salesRecordNumber = whApv.getSalesRecordNumber();
                if (CollectionUtils.isNotEmpty(skus) && StringUtils.isNotBlank(salesRecordNumber)){
                    purchaseApvOutStockMatchService.updateOrder(whApv.getApvNo(), salesRecordNumber, skus);
                }
            }
            // TODO liurui 订单推旧单号
            this.handleSplitOrder(whApv);

            // 关联安检退件单
            if (whApv.getShipStatus() != null && whApv.getShipStatus().equals(ApvOrderType.SECURITY_CHECK_REFUND.intCode())){
                whApvReturnService.bindingWhApvReturn(whApv);
            }
            // 走菜鸟云打印的物流方式
            SystemParam cloudPrintLogistics = CacheUtils.SystemParamGet("print_waybill.CLOUD_PRINT_LOGISTICS");
            if (cloudPrintLogistics != null && StringUtils.isNotEmpty(cloudPrintLogistics.getParamValue())) {
                String[] cloudPrintStr = StringUtils.split(cloudPrintLogistics.getParamValue(), ",");
                // 下载速卖通菜鸟云打印面单
                if (whApv.getPlatform() != null && whApv.getPlatform() == 4 && whApv.getLogisticsCompany() != null
                        && ArrayUtils.contains(cloudPrintStr, whApv.getLogisticsCompany())) {
                    aliExpressCallService.getPdfsByCloudPrint(whApv,null);
                }
            }
            if (StringUtils.isNotBlank(apvPushCount) && StringUtils.isNotBlank(whApv.getSalesRecordNumber())) {
                String dd = whApv.getSalesRecordNumber();
                if (whApv.getSalesRecordNumber().contains("_")) {
                    dd = StringUtils.split(whApv.getSalesRecordNumber(), "_")[0];
                }
                StringRedisUtils.set(RedisConstant.APV_CANCEL_COUNT + dd, apvPushCount, 30 * 24 * 60 * 60L);
            }
            returnMessage.setStatus(true);
            returnMessage.setMessage("WMS处理成功");
            return true;
        }
        catch (Exception e) {
            logger.error("oms推单" + whApv.getApvNo() + "写入数据库失败", e);
            returnMessage.setMessage("oms推单" + whApv.getApvNo() + "写入数据库失败");
            return false;
        }

    }

    /**
     *  获取出库单推送次数
     * @param whApv
     */
    private String getApvCancelCount(WhApv whApv){
        try {
            if (StringUtils.isBlank(whApv.getSalesRecordNumber())) {
                return null;
            }
            String dd = whApv.getSalesRecordNumber();
            if (whApv.getSalesRecordNumber().contains("_")) {
                dd = StringUtils.split(whApv.getSalesRecordNumber(), "_")[0];
            }
            String countStr = StringRedisUtils.get(RedisConstant.APV_CANCEL_COUNT + dd);
            if (StringUtils.isNotBlank(countStr)) {
                Integer count = Integer.valueOf(countStr);
                return String.valueOf(count + 1);
            }
        }catch (Exception e){
            logger.error("处理出库单推送次数异常：", e);
        }
        return null;
    }

    /**
     * 处理拆单
     */
    private void handleSplitOrder(WhApv whApv) {
        if (StringUtils.isBlank("")) {
            return;
        }
        WhApvQueryCondition apvQuery = new WhApvQueryCondition();
        apvQuery.setApvNo("");
        apvQuery.setOversize(true);
        List<WhApv> whApvs = whApvService.queryWhApvAndItemList(apvQuery, null);
        if (CollectionUtils.isEmpty(whApvs)) {
            return;
        }
        WhApv originApv = whApvs.get(0);
        ApvOversize apvOversize = originApv.getApvOversize();
        if (apvOversize == null || !apvOversize.existLabel("SPLIT")) {
            return;
        }
        // 旧订单存在拆分标签 修改当前单据状态 关联旧单据
        WhApv updateApv = new WhApv();
        updateApv.setId(whApv.getId());
        if (StringUtils.equals(whApv.getApvType(), "SS")) {
            updateApv.setStatus(ApvStatus.SINGLETON_TOUCHING.intCode());
        } else if (StringUtils.equals(whApv.getApvType(), "SM")) {
            updateApv.setStatus(ApvStatus.EXCESSIVE_PARTS_TOUCHING.intCode());
        } else if (StringUtils.equals(whApv.getApvType(), "MM")) {
            updateApv.setStatus(ApvStatus.CHECK_PRINT.intCode());
        }
        whApvService.updateWhApv(updateApv);

        ApvTrack apvTrack = new ApvTrack();
        apvTrack.setApvNo(whApv.getApvNo());
        apvTrack.setSplitApvNo(originApv.getApvNo());
        apvTrackService.createApvTrack(apvTrack);
    }

    /**
     * @Title: assembleData
     * @Description: 组装apv发货单参数
     * @Date 2019年1月3日_上午9:48:07
     * @param whApv
     * @param returnMessage
     * @return
     */
    private boolean assembleData(WhApv whApv, PushOrderResultMessage returnMessage) {
        try {
            whApv.setShipStatus(whApv.getShipStatus() == null ? 0 : whApv.getShipStatus());
            whApv.setShipService("OMS");
            whApv.setStatus(ApvStatus.WAITING_ALLOT.intCode());
            whApv.setOriginalOrderId(0);// 频次设置为0
            int skuCount = 0;
            List<WhApvItem> whApvItemList = whApv.getWhApvItems();
            boolean isSku = false; // 是否存在重复SKU条目
            List<WhApvItem> createItem = new ArrayList<>();
            Map<String, WhApvItem> skuMap = new HashMap<>();
            List<String> skus = new ArrayList<String>();

            List<String> combinationSkuList = whApvItemList.stream()
                    .filter(whApvItem -> whApvItem.getIsCombinationSku() != null && whApvItem.getIsCombinationSku())
                    .map(WhApvItem::getSku)
                    .collect(Collectors.toList());
            List<WhCombineSku> whCombineSkus=new ArrayList<>();
            if (CollectionUtils.isNotEmpty(combinationSkuList)) {
                WhCombineSkuQueryCondition qCondition = new WhCombineSkuQueryCondition();
                qCondition.setSpu(StringUtils.join(combinationSkuList, ","));
                whCombineSkus = whCombineSkuService.queryWhCombineSkus(qCondition, null);
            }

            Map<String, String> combineSkuMap = Optional.ofNullable(whCombineSkus).orElse(new ArrayList<>()).stream().collect(Collectors.toMap(WhCombineSku::getSpu, WhCombineSku::getName, (k1, k2) -> k1));
            for (WhApvItem whApvItem : whApvItemList) {
                skuCount += whApvItem.getSaleQuantity();
                if (StringUtils.isNotBlank(whApvItem.getSku())) {
                    if (whApvItem.getIsCombinationSku() != null && whApvItem.getIsCombinationSku()){
                        whApvItem.setSkuTitle(combineSkuMap.get(whApvItem.getSku()));
                    }else{
                        skus.add(whApvItem.getSku());
                    }
                    WhApvItem item = skuMap.get(whApvItem.getSku());
                    if (item != null) {
                        isSku = true; // 存在重复SKU条目
                        item.setSaleQuantity(item.getSaleQuantity() + whApvItem.getSaleQuantity());
                    }
                    else {
                        createItem.add(whApvItem);
                        skuMap.put(whApvItem.getSku(), whApvItem);
                    }
                }
            }
            // 存在重复SKU条目
            if (isSku) {
                whApvItemList = createItem;
                whApv.setWhApvItems(createItem);
                if (whApvItemList.size() > 1) {
                    whApv.setApvType("MM");
                }
                else {
                    whApv.setApvType("SM");
                }
            }
            if (CollectionUtils.isNotEmpty(skus)) {
                assembleApvOrderType(whApv, whApvItemList, skus);
                if (!validateWarehouse(whApv, skus, returnMessage)) {
                    return false;
                }

            }
            // ITEM数量和SKU数量
            whApv.setFeeOrCredit(Double.valueOf(whApvItemList.size()));
            whApv.setSystemPrice(Double.valueOf(skuCount));
            returnMessage.setStatus(true);
            returnMessage.setMessage("组装参数成功");

            return true;
        }
        catch (Exception e) {
            logger.error("组装apv发货单参数异常", e);
            returnMessage.setMessage("组装apv发货单参数异常");
            return false;
        }
    }

    /**
     * 组装发货类型
     *
     * @param whApv
     * @param apvItem
     * @param skus
     */
    private void assembleApvOrderType(WhApv whApv, List<WhApvItem> apvItem, List<String> skus) {
        WhSkuQueryCondition query = new WhSkuQueryCondition();
        query.setSkus(skus);
        List<WhSku> whSkuList = whSkuService.queryWhSkus(query, null);
        if (CollectionUtils.isEmpty(whSkuList)) {
            return;
        }
        this.handlePriority(whApv);
        String skuFlags = "";
        for (WhSku whSku : whSkuList) {
            if (whSku.getShearSign() != null && whSku.getShearSign() == 1) {
                skuFlags += SkuFlags.CLIPPING.intCode() + ",";
            }
            if (whSku.getNoStockUp() != null && whSku.getNoStockUp() == 1) {
                skuFlags += SkuFlags.NOT_STOCKED.intCode() + ",";
            }
            if (StringUtils.isBlank(skuFlags) && !ApvShearSign.CLIPPING.intCode().equals(whApv.getApvShearSign())) {
                /*if(ApvOrderType.EXPRESS_BUSINESS.intCode() != whApv.getShipStatus()
                        && ApvOrderType.SECURITY_CHECK_REFUND.intCode() != whApv.getShipStatus()
                        && whApv.containTag("183")){
                    whApv.setShipStatus(ApvOrderType.OVERSIZE.intCode());
                }*/
                return;
            }
            ApvOrderType apvOrderType = ApvOrderType.build(whApv.getShipStatus() + "");
            if (apvOrderType == null) {
                return;
            }
            switch (apvOrderType) {
                case BASICS:
//                    if (skuFlags.contains(SkuFlags.CLIPPING.intCode().toString())) {
//                        whApv.setShipStatus(ApvOrderType.CLIPPING.intCode());
//                    }
                    //剪标标识由OMS推送
                    /*if(whApv.containTag("183")){
                        whApv.setShipStatus(ApvOrderType.OVERSIZE.intCode());
                    }else */
                    if (ApvShearSign.CLIPPING.intCode().equals(whApv.getApvShearSign())){
                        whApv.setShipStatus(ApvOrderType.CLIPPING.intCode());
                    }
                    else if (skuFlags.contains(SkuFlags.NOT_STOCKED.intCode().toString())) {
                        whApv.setShipStatus(ApvOrderType.NOT_STOCKED.intCode());
                    }
                    /*
                     * else if
                     * (skuFlags.contains(SkuFlags.SPECIAL_PURCHASE.intCode().
                     * toString())) {
                     * whApv.setShipStatus(ApvOrderType.SPECIAL_PURCHASE.intCode
                     * ()); }
                     */
                    break;
                case EXPRESS_BUSINESS:
//                    if (skuFlags.contains(SkuFlags.CLIPPING.intCode().toString())) {
//                        whApv.setShipStatus(ApvOrderType.EXPRESS_CLIPPING.intCode());
//                    }
                    if (ApvShearSign.CLIPPING.intCode().equals(whApv.getApvShearSign())){
                        whApv.setShipStatus(ApvOrderType.EXPRESS_CLIPPING.intCode());
                    }
                    /*
                     * else if
                     * (skuFlags.contains(SkuFlags.SPECIAL_PURCHASE.intCode().
                     * toString())) {
                     * whApv.setShipStatus(ApvOrderType.EXPRESS_SPECIAL_PURCHASE
                     * .intCode()); }
                     */
                    else if (skuFlags.contains(SkuFlags.NOT_STOCKED.intCode().toString())) {
                        whApv.setShipStatus(ApvOrderType.EXPRESS_NOT_STOCKED.intCode());
                    }
                    break;
                case FBA:
//                    if (skuFlags.contains(SkuFlags.CLIPPING.intCode().toString())) {
//                        whApv.setShipStatus(ApvOrderType.FBA_CLIPPING.intCode());
//                    }
                    /*if(whApv.containTag("183")){
                        whApv.setShipStatus(ApvOrderType.OVERSIZE.intCode());
                    }else */
                    if (ApvShearSign.CLIPPING.intCode().equals(whApv.getApvShearSign())){
                        whApv.setShipStatus(ApvOrderType.FBA_CLIPPING.intCode());
                    }
                    else if (skuFlags.contains(SkuFlags.NOT_STOCKED.intCode().toString())) {
                        whApv.setShipStatus(ApvOrderType.FBA_NOT_STOCKED.intCode());
                    }
                    /*
                     * else if
                     * (skuFlags.contains(SkuFlags.SPECIAL_PURCHASE.intCode().
                     * toString())) {
                     * whApv.setShipStatus(ApvOrderType.FBA_SPECIAL_PURCHASE.
                     * intCode()); }
                     */
                    break;
                default:
                    /*if(whApv.containTag("183") && ApvOrderType.SECURITY_CHECK_REFUND.intCode() != whApv.getShipStatus()){
                        whApv.setShipStatus(ApvOrderType.OVERSIZE.intCode());
                    }*/
                    break;
            }
        }
    }

    /**
     * 处理发货类型优先级
     */
    private void handlePriority(WhApv whApv){
        if (StringUtils.isBlank(whApv.getShipTypeArr())) {
            return;
        }
        if (StringUtils.contains(whApv.getShipTypeArr(), ",")) {
            List<String> types = CommonUtils.splitList(whApv.getShipTypeArr(), ",");
            List<ApvOrderType> typeEnums = types.stream().map(ApvOrderType::build).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(typeEnums)) {
                return;
            }
            typeEnums.sort(Comparator.comparing(ApvOrderType::getPriority));
            whApv.setShipStatus(typeEnums.get(0).intCode());
        } else {
            ApvOrderType build = ApvOrderType.build(whApv.getShipTypeArr());
            if (build != null) {
                whApv.setShipStatus(build.intCode());
            }
        }
    }


    /**
     * @Description: 仓库校验
     *
     * @param whApv
     * @param skus
     * @param returnMessage
     * @return
     * @Author: Administrator
     * @Date: 2019/04/29
     * @Version: 0.0.1
     */
    private boolean validateWarehouse(WhApv whApv, List<String> skus, PushOrderResultMessage returnMessage) {
        try {
            if (StringRedisUtils.exists(RedisKeys.getOmsCancleApvCacheKey(whApv.getApvNo()))) {
                returnMessage.setMessage("该订单已取消");
                return false;
            }

            if (CollectionUtils.isEmpty(skus)) {
                returnMessage.setMessage("该订单没有SKU条目");
                return false;
            }

            WhWarehouse whWarehouse = whWarehouseService.queryOriginalWhWarehouse(true);
            if (null == whWarehouse) {
                returnMessage.setMessage("WMS未设置默认仓库属性，请联系系统管理员");
                return false;
            }

            Set<Integer> warehouseIds = new HashSet<Integer>();
            List<String> wmsSkus = new ArrayList<String>();

            WhSkuQueryCondition query = new WhSkuQueryCondition();
            query.setSkus(skus);
            List<WhSku> whSkuList = whSkuService.queryWhSkus(query, null);
            for (WhSku whSku : whSkuList) {
                Integer warehouseId = whSku.getWarehouseId();
                String sku = whSku.getSku();
                if (null == warehouseId || warehouseId == 0) {
                    returnMessage.setMessage("WMS系统中" + sku + "没有仓库属性，仓库编号：" + whWarehouse.getId());
                    return false;
                }
                warehouseIds.add(warehouseId);
                wmsSkus.add(sku);
            }

            skus.removeAll(wmsSkus);
            if (CollectionUtils.isNotEmpty(skus)) {
                returnMessage.setMessage("WMS仓库" + whWarehouse.getId() + "没有以下SKU：" + JSON.toJSONString(skus));
                return false;
            }

            if (Integer.valueOf(2).equals(whWarehouse.getId())
                    && Integer.valueOf(ApvOrderType.SECURITY_CHECK_REFUND.intCode()).equals(whApv.getShipStatus())) {
                returnMessage.setMessage("安检退件的单只能推送到汉海达，请检查后重新推送");
                return false;
            }

            if (Integer.valueOf(1).equals(whWarehouse.getId())
                    && Integer.valueOf(ApvOrderType.SECURITY_CHECK_REFUND.intCode()).equals(whApv.getShipStatus())) {
                return true;
            }

            if (warehouseIds.size() > 1) {
                if (!Integer.valueOf(1).equals(whWarehouse.getId())) {// 跨仓
                    returnMessage.setMessage("该订单是跨仓订单，应该推送到汉海达仓，请检查后重新推送");
                    return false;
                }
            }
            else if (warehouseIds.size() == 1) {
                Integer warehouseId = warehouseIds.iterator().next();
                if (Integer.valueOf(1).equals(whWarehouse.getId()) && !Integer.valueOf(1).equals(warehouseId)) {// 本仓汉海达
                    returnMessage.setMessage("该订单是美景仓订单，应该推送到美景仓，请检查后重新推送");
                    return false;
                }

                if (Integer.valueOf(2).equals(whWarehouse.getId()) && !Integer.valueOf(2).equals(warehouseId)) {// 本仓美景仓
                    returnMessage.setMessage("该订单是汉海达仓订单，应该推送到汉海达仓，请检查后重新推送");
                    return false;
                }
            }
            else {
                returnMessage.setMessage("该订单SKU没有仓库属性，请检查后重新推送");
                return false;
            }
        }
        catch (Exception e) {
            logger.warn(e.getMessage());
            returnMessage.setMessage("WMS校验仓库属性系统异常");
            return false;
        }

        return true;
    }

}