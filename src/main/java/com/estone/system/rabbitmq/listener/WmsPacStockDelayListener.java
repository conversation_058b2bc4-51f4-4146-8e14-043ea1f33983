package com.estone.system.rabbitmq.listener;

import com.alibaba.fastjson.JSON;
import com.estone.common.util.Constant;
import com.estone.common.util.RedisConstant;
import com.estone.common.util.StringRedisUtils;
import com.estone.core.mq.Queues;
import com.estone.core.mq.RabbitMqExchange;
import com.estone.pac.bean.PacNewStock;
import com.estone.pac.bean.PacNewStockDTO;
import com.estone.pac.bean.PacNewStockQueryCondition;
import com.estone.pac.bean.TakeStockDTO;
import com.estone.pac.dao.PacNewStockDao;
import com.estone.pac.enums.RecordSourceEnum;
import com.estone.pac.enums.TakeStockReasonEnum;
import com.estone.pac.service.PacNewStockService;
import com.estone.pac.service.TakeStockRecordService;
import com.estone.system.rabbitmq.common.RabbitmqListenerSqlInit;
import com.estone.system.rabbitmq.handle.ServiceMessageHandle;
import com.rabbitmq.client.Channel;
import jodd.util.StringUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.core.ChannelAwareMessageListener;
import org.springframework.amqp.rabbit.core.RabbitTemplate;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;


/**
 * <AUTHOR>
 * @Date 2022/6/10 15:27
 **/
public class WmsPacStockDelayListener implements ChannelAwareMessageListener {
    private Logger logger = LoggerFactory.getLogger(WmsPacStockDelayListener.class);

    @Resource
    private PacNewStockService pacNewStockService;

    @Resource
    private PacNewStockDao pacNewStockDao;

    @Resource
    private TakeStockRecordService takeStockRecordService;

    @Resource
    private ServiceMessageHandle serviceMessageHandle;

    @Resource
    private RabbitTemplate rabbitTemplate;

    private static final Integer MAX_SEND=20;

    //设置十分钟过期时间
    private static Long expirationTime=1000*60*10L;

    private boolean isInit = false;
    @Override
    public void onMessage(Message message, Channel channel) throws Exception {
        // 校验容器加载时所有bean是否均已实例化
        if (!isInit) {
            RabbitmqListenerSqlInit.validateSqlerInit(this.getClass());
            this.isInit = true;
        }
        // 获取消息体
        String body = new String(message.getBody(), "UTF-8");
        logger.warn("WmsPacStockDelayListener message body -> " + body);
        doService(body);
        // 一次性消费，移除队列
        serviceMessageHandle.basicACK(message, channel);
    }

    private void doService(String body) {
        List<String> basicNACKkuSList=new ArrayList<>();
        try {
            if (StringUtils.isBlank(body)) {
                return;
            }
            List<String> skuList = JSON.parseArray(body, String.class);
            if (CollectionUtils.isEmpty(skuList)) {
                return;
            }

            //向上取整
            int limit=(int) Math.ceil((double) skuList.size()/MAX_SEND);
            //分组
            List<List<String>> splitList = Stream.iterate(0, n -> n + 1).limit(limit).parallel().map(a -> skuList.stream().skip(a * MAX_SEND).limit(MAX_SEND).parallel().collect(Collectors.toList())).collect(Collectors.toList());

            //预警值
            Integer earlyWarningStock = Optional.ofNullable((String)StringRedisUtils.get(RedisConstant.EARLY_WARNING_STOCK)).map(a -> Integer.parseInt(a)).orElse(0);
            PacNewStockQueryCondition queryCondition = new PacNewStockQueryCondition();
            String ownerUserId = StringRedisUtils.get(Constant.PAC_STOCK_OWNER_USER_ID);
            if (StringUtil.isBlank(ownerUserId)) {
                logger.error("优选仓货主ID为空！");
                basicNACKkuSList.addAll(skuList);
                throw new RuntimeException("优选仓货主ID为空,请联系开发人员!");
            }
            queryCondition.setOwnerUserId(ownerUserId);

            for (List<String> list : splitList) {

                queryCondition.setSkuList(list);
                List<PacNewStock> pacNewStocks = pacNewStockDao.queryPacNewStockList(queryCondition, null);
                if (CollectionUtils.isEmpty(pacNewStocks)) {
                    basicNACKkuSList.addAll(list);
                    continue;
                }
                List<PacNewStock> updatePacNewStockList = pacNewStocks.stream().map(a -> {
                    PacNewStock pacNewStock = new PacNewStock();
                    pacNewStock.setId(a.getId());
                    pacNewStock.setItemId(a.getItemId());
                    pacNewStock.setSku(a.getSku());
                    return pacNewStock;
                }).collect(Collectors.toList());

                List<String> skus = updatePacNewStockList.stream().map(s -> s.getSku()).collect(Collectors.toList());

                Map<String, PacNewStock> newStockMap = updatePacNewStockList.stream().collect(Collectors.toMap(PacNewStock::getSku, a -> a));
                if (MapUtils.isEmpty(newStockMap) || CollectionUtils.isEmpty(skus)) {
                    continue;
                }

                List<String> itemIds = updatePacNewStockList.stream().map(s -> s.getItemId()).collect(Collectors.toList());

                //获取优选仓良品可用、良品占用库存
                if (!pacNewStockService.calculatePacStockQuantity(newStockMap, itemIds)) {
                    pacNewStockService.calculateDifferenceQuantity(updatePacNewStockList,earlyWarningStock,false);
                    basicNACKkuSList.addAll(list);
                    continue;
                }
                //计算本地可以库存数量
                pacNewStockService.calculateLocalSurplusQuantity(newStockMap, skus);
                //计算WMS待分配订单
                pacNewStockService.calculateWaitingAllotQuantity(newStockMap, skus);
                //计算OMS预分配订单
                pacNewStockService.calculateOmsPredistributionQuantity(newStockMap, skus);
                //计算待盘点库存并对比差异
                pacNewStockService.calculateWaitingCheckQuantity(newStockMap, skus);
                //比对差异
                pacNewStockService.calculateDifferenceQuantity(updatePacNewStockList,earlyWarningStock,true);
                List<TakeStockDTO> takeStockDTOList=new ArrayList<>();
                for (PacNewStock pacNewStock : updatePacNewStockList) {
                    if (pacNewStock.getQuantity()==null){
                        basicNACKkuSList.add(pacNewStock.getSku());
                        continue;
                    }
                    Integer localSurplusQuantity = Optional.ofNullable(pacNewStock.getLocalSurplusQuantity()).orElse(0);
                    Integer waitingAllotQuantity = Optional.ofNullable(pacNewStock.getWaitingAllotQuantity()).orElse(0);
                    Integer omsPredisQuantity = Optional.ofNullable(pacNewStock.getOmsPredistributionQuantity()).orElse(0);
                    //本地仓可用-WMS待分配订单占用-OMS预分配订单占用
                    int calculateQuantity = localSurplusQuantity - waitingAllotQuantity - omsPredisQuantity;

                    Integer waitingCheckQuantity = Optional.ofNullable(pacNewStock.getWaitingCheckQuantity()).orElse(0);
                    Integer quantity = Optional.ofNullable(pacNewStock.getQuantity()).orElse(0);
                    Integer lockQuantity = Optional.ofNullable(pacNewStock.getLockQuantity()).orElse(0);

                    Integer differenceQuantity = Optional.ofNullable(pacNewStock.getDifferenceQuantity()).orElse(0);

                    TakeStockDTO takeStockDTO =new TakeStockDTO();
                    takeStockDTO.setSku(pacNewStock.getSku());
                    takeStockDTO.setTakeStockReason(TakeStockReasonEnum.COMPARISON_STOCK.intCode());
                    takeStockDTO.setTakeStockTime(new Timestamp(System.currentTimeMillis()));
                    takeStockDTO.setRecordSource(RecordSourceEnum.SYSTEM_ADD.intCode());
                    takeStockDTO.setEarlyWarningStock(earlyWarningStock);

                    takeStockDTO.setChangeStock(calculateQuantity);
                    //可用库存是否大于预警值
                    if (localSurplusQuantity>earlyWarningStock) {
                        //根据差异生成库存盘点记录
                        takeStockDTO.setChangeNumber(-differenceQuantity);
                        takeStockDTO.setTakeStockNumber(-differenceQuantity);
                    }else{
                        //盘点记录变动数量=0-待盘数量-良品可用-良品占用
                        takeStockDTO.setChangeNumber(0-waitingCheckQuantity-quantity-lockQuantity);
                        takeStockDTO.setTakeStockNumber(0-waitingCheckQuantity-quantity-lockQuantity);
                    }
                    takeStockDTOList.add(takeStockDTO);
                }
                takeStockRecordService.doBatchCreateRecordAndLog(takeStockDTOList,null);
            }
        }
        catch (Exception e) {
            logger.error("SKU比对库存新增盘点记录失败！", e);
        }
        if (CollectionUtils.isNotEmpty(basicNACKkuSList)) {
            //回传入库单间隔10分钟比对库存差异
            rabbitTemplate.convertAndSend(RabbitMqExchange.AMAZON_API_DIRECT_EXCHANGE, Queues.WMS_PAC_STOCK_DELAY_QUEUE,JSON.toJSONString(basicNACKkuSList), message -> {
                message.getMessageProperties().setExpiration(expirationTime+ "");
                return message;
            });
        }

    }

}
