package com.estone.system.log.service.impl;

import java.util.ArrayList;
import java.util.List;

import javax.annotation.Resource;

import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import com.estone.system.log.bean.WhSystemLog;
import com.estone.system.log.bean.WhSystemLogQueryCondition;
import com.estone.system.log.dao.WhSystemLogDao;
import com.estone.system.log.service.WhSystemLogService;
import com.whq.tool.component.Pager;
import com.whq.tool.exception.BusinessException;
import com.whq.tool.exception.DBErrorCode;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import com.whq.tool.sqler.SqlerException;

@Service("whSystemLogService")
public class WhSystemLogServiceImpl implements WhSystemLogService {
    private static final Logger logger = LoggerFactory.getLogger(WhSystemLogServiceImpl.class);

    @Resource
    private WhSystemLogDao whSystemLogDao;

    /**
     * This method corresponds to the database table wh_system_log
     *
     * @mbggenerated Thu Aug 16 10:59:27 CST 2018
     */
    public WhSystemLog getWhSystemLog(Integer id) {
        WhSystemLog whSystemLog = whSystemLogDao.queryWhSystemLog(id);
        return whSystemLog;
    }

    /**
     * This method corresponds to the database table wh_system_log
     *
     * @mbggenerated Thu Aug 16 10:59:27 CST 2018
     */
    public WhSystemLog getWhSystemLogDetail(Integer id) {
        WhSystemLog whSystemLog = whSystemLogDao.queryWhSystemLog(id);
        // 关联查询
        return whSystemLog;
    }

    /**
     * This method corresponds to the database table wh_system_log
     *
     * @mbggenerated Thu Aug 16 10:59:27 CST 2018
     */
    public WhSystemLog queryWhSystemLog(WhSystemLogQueryCondition query) {
        Assert.notNull(query);
        WhSystemLog whSystemLog = whSystemLogDao.queryWhSystemLog(query);
        return whSystemLog;
    }

    /**
     * This method corresponds to the database table wh_system_log
     *
     * @mbggenerated Thu Aug 16 10:59:27 CST 2018
     */
    public List<WhSystemLog> queryAllWhSystemLogs() {
        return whSystemLogDao.queryWhSystemLogList();
    }

    /**
     * This method corresponds to the database table wh_system_log
     *
     * @mbggenerated Thu Aug 16 10:59:27 CST 2018
     */
    public List<WhSystemLog> queryWhSystemLogs(WhSystemLogQueryCondition query, Pager pager) {
        Assert.notNull(query);
        if (pager != null && pager.isQueryCount()) {
            int count = whSystemLogDao.queryWhSystemLogCount(query);
            pager.setTotalCount(count);
            if (count == 0) {
                return new ArrayList<WhSystemLog>();
            }
        }
        List<WhSystemLog> whSystemLogs = whSystemLogDao.queryWhSystemLogList(query, pager);
        return whSystemLogs;
    }

    /**
     * This method corresponds to the database table wh_system_log
     *
     * @mbggenerated Thu Aug 16 10:59:27 CST 2018
     */
    public void createWhSystemLog(WhSystemLog whSystemLog) {
        try {
            whSystemLogDao.createWhSystemLog(whSystemLog);
        }
        catch (SqlerException e) {
            logger.error(e.getMessage(), e);
            throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
        }
    }

    /**
     * This method corresponds to the database table wh_system_log
     *
     * @mbggenerated Thu Aug 16 10:59:27 CST 2018
     */
    public void batchCreateWhSystemLog(List<WhSystemLog> entityList) {
        if (CollectionUtils.isNotEmpty(entityList)) {
            try {
                whSystemLogDao.batchCreateWhSystemLog(entityList);
            }
            catch (SqlerException e) {
                logger.error(e.getMessage(), e);
                throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
            }
        }
    }

    /**
     * This method corresponds to the database table wh_system_log
     *
     * @mbggenerated Thu Aug 16 10:59:27 CST 2018
     */
    public void deleteWhSystemLog(Integer id) {
        try {
            whSystemLogDao.deleteWhSystemLog(id);
        }
        catch (SqlerException e) {
            logger.error(e.getMessage(), e);
            throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
        }
    }

    /**
     * This method corresponds to the database table wh_system_log
     *
     * @mbggenerated Thu Aug 16 10:59:27 CST 2018
     */
    public void updateWhSystemLog(WhSystemLog whSystemLog) {
        try {
            whSystemLogDao.updateWhSystemLog(whSystemLog);
        }
        catch (SqlerException e) {
            logger.error(e.getMessage(), e);
            throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
        }
    }

    /**
     * This method corresponds to the database table wh_system_log
     *
     * @mbggenerated Thu Aug 16 10:59:27 CST 2018
     */
    public void batchUpdateWhSystemLog(List<WhSystemLog> entityList) {
        if (CollectionUtils.isNotEmpty(entityList)) {
            try {
                whSystemLogDao.batchUpdateWhSystemLog(entityList);
            }
            catch (SqlerException e) {
                logger.error(e.getMessage(), e);
                throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
            }
        }
    }

    @Override
    public void deleteByIds(List<Integer> ids,String tableName) {
        if (CollectionUtils.isNotEmpty(ids)) {
            try {
                whSystemLogDao.deleteByIds(ids,tableName);
            }
            catch (SqlerException e) {
                logger.error(e.getMessage(), e);
                throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
            }
        }
    }
}