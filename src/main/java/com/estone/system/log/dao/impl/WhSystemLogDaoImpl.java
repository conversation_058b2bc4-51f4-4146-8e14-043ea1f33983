package com.estone.system.log.dao.impl;

import java.sql.Timestamp;
import java.util.List;
import java.util.Optional;

import com.estone.common.util.RedisConstant;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;
import org.springframework.util.Assert;

import com.estone.common.enums.LogModule;
import com.estone.common.util.CommonUtils;
import com.estone.system.log.bean.WhSystemLog;
import com.estone.system.log.bean.WhSystemLogQueryCondition;
import com.estone.system.log.dao.WhSystemLogDao;
import com.estone.system.log.dao.mapper.WhSystemLogDBField;
import com.estone.system.log.dao.mapper.WhSystemLogMapper;
import com.whq.tool.component.Pager;
import com.whq.tool.sqler.SqlerRequest;
import com.whq.tool.sqler.analyzer.DataType;
import com.estone.common.util.SqlerTemplate;

@Repository("whSystemLogDao")
public class WhSystemLogDaoImpl implements WhSystemLogDao {

    private void setQueryCondition(SqlerRequest request, WhSystemLogQueryCondition query) {
        if (query == null) {
            return;
        }
        // 添加查询条件
        request.addDataParam(WhSystemLogDBField.RELEVANCE_ID, DataType.INT, query.getRelevanceId());
        request.addDataParam(WhSystemLogDBField.MODULE, DataType.STRING, query.getModule());
        // 搜索条件不允许出现空字符
        request.setAllowBlank(false);

        // SKU日志，默认不查询
        if (LogModule.SKU.getCode().equals(query.getModule()) && !"true".equals(query.getAllLog())) {
            request.addSqlDataParam("sku_log_ignore", "AND content NOT LIKE '修改sku信息 - 更改后的信息%'");
        }

        request.addDataParam(WhSystemLogDBField.ID, DataType.INT, query.getId());
        if (query.getTableIndex() == null) {
            request.addSqlDataParam("table_index", "wh_system_log");
        }
        else {
            request.addSqlDataParam("table_index", "wh_system_log_" + query.getTableIndex().toString());
        }
    }

    /**
     * This method corresponds to the database table wh_system_log
     *
     * @mbggenerated Thu Aug 16 10:59:27 CST 2018
     */
    public int queryWhSystemLogCount(WhSystemLogQueryCondition query) {
        SqlerRequest request = new SqlerRequest("queryWhSystemLogCount");
        request.setReadOnly(true);
        setQueryCondition(request, query);
        return SqlerTemplate.queryForInt(request);
    }

    /**
     * This method corresponds to the database table wh_system_log
     *
     * @mbggenerated Thu Aug 16 10:59:27 CST 2018
     */
    public List<WhSystemLog> queryWhSystemLogList() {
        SqlerRequest request = new SqlerRequest("queryWhSystemLogList");
        request.setReadOnly(true);
        return SqlerTemplate.query(request, new WhSystemLogMapper());
    }

    /**
     * This method corresponds to the database table wh_system_log
     *
     * @mbggenerated Thu Aug 16 10:59:27 CST 2018
     */
    public List<WhSystemLog> queryWhSystemLogList(WhSystemLogQueryCondition query, Pager pager) {
        SqlerRequest request = new SqlerRequest("queryWhSystemLogList");
        request.setReadOnly(true);
        setQueryCondition(request, query);
        if (pager != null) {
            request.addFetch(pager.getPageNo(), pager.getPageSize());
        }
        return SqlerTemplate.query(request, new WhSystemLogMapper());
    }

    /**
     * This method corresponds to the database table wh_system_log
     *
     * @mbggenerated Thu Aug 16 10:59:27 CST 2018
     */
    public WhSystemLog queryWhSystemLog(Integer primaryKey) {
        Assert.notNull(primaryKey);
        SqlerRequest request = new SqlerRequest("queryWhSystemLogByPrimaryKey");
        request.setReadOnly(true);
        request.addDataParam(WhSystemLogDBField.ID, DataType.INT, primaryKey);
        return SqlerTemplate.queryForObject(request, new WhSystemLogMapper());
    }

    /**
     * This method corresponds to the database table wh_system_log
     *
     * @mbggenerated Thu Aug 16 10:59:27 CST 2018
     */
    public WhSystemLog queryWhSystemLog(WhSystemLogQueryCondition query) {
        SqlerRequest request = new SqlerRequest("queryWhSystemLog");
        request.setReadOnly(true);
        setQueryCondition(request, query);
        return SqlerTemplate.queryForObject(request, new WhSystemLogMapper());
    }

    /**
     * This method corresponds to the database table wh_system_log
     *
     * @mbggenerated Thu Aug 16 10:59:27 CST 2018
     */
    public void createWhSystemLog(WhSystemLog entity) {
        if (entity.getRelevanceId() == null) {
            return;
        }
        SqlerRequest request = new SqlerRequest("createWhSystemLog");
        request.addDataParam(WhSystemLogDBField.RELEVANCE_ID, DataType.INT, entity.getRelevanceId());
        request.addDataParam(WhSystemLogDBField.MODULE, DataType.STRING, entity.getModule());
        request.addDataParam(WhSystemLogDBField.CREATION_DATE, DataType.TIMESTAMP,
                new Timestamp(System.currentTimeMillis()));
        request.addDataParam(WhSystemLogDBField.CREATE_BY, DataType.INT, entity.getCreateBy());
        request.addDataParam(WhSystemLogDBField.CONTENT, DataType.STRING, entity.getContent());
        if (StringUtils.equalsIgnoreCase(RedisConstant.SKU_INTERCEPT_CONFIG,entity.getModule())
                || StringUtils.startsWithIgnoreCase(entity.getModule(),"LBX")){
            request.addSqlDataParam("table_index", "wh_system_log");
        }else {
            request.addSqlDataParam("table_index", "wh_system_log_" + CommonUtils.tableIndex(entity.getRelevanceId()));
        }
        Integer autoIncrementId = SqlerTemplate.executeAndReturn(request);
        entity.setId(autoIncrementId);
    }

    /**
     * This method corresponds to the database table wh_system_log
     *
     * @mbggenerated Thu Aug 16 10:59:27 CST 2018
     */
    public void updateWhSystemLog(WhSystemLog entity) {
        SqlerRequest request = new SqlerRequest("updateWhSystemLogByPrimaryKey");
        request.addDataParam(WhSystemLogDBField.ID, DataType.INT, entity.getId());

        request.addDataParam(WhSystemLogDBField.RELEVANCE_ID, DataType.INT, entity.getRelevanceId());
        request.addDataParam(WhSystemLogDBField.MODULE, DataType.STRING, entity.getModule());

        request.addDataParam(WhSystemLogDBField.CREATE_BY, DataType.INT, entity.getCreateBy());
        request.addDataParam(WhSystemLogDBField.CONTENT, DataType.STRING, entity.getContent());
        SqlerTemplate.execute(request);
    }

    /**
     * This method corresponds to the database table wh_system_log
     *
     * @mbggenerated Thu Aug 16 10:59:27 CST 2018
     */
    public void batchCreateWhSystemLog(List<WhSystemLog> entityList) {
        if (entityList != null && !entityList.isEmpty()) {
            SqlerRequest request = new SqlerRequest("createWhSystemLog");
            for (WhSystemLog entity : entityList) {
                request.addBatchDataParam(WhSystemLogDBField.RELEVANCE_ID, DataType.INT, entity.getRelevanceId());
                request.addBatchDataParam(WhSystemLogDBField.MODULE, DataType.STRING, entity.getModule());
                request.addBatchDataParam(WhSystemLogDBField.CREATION_DATE, DataType.TIMESTAMP,
                        new Timestamp(System.currentTimeMillis()));
                request.addBatchDataParam(WhSystemLogDBField.CREATE_BY, DataType.INT, entity.getCreateBy());
                request.addBatchDataParam(WhSystemLogDBField.CONTENT, DataType.STRING, entity.getContent());
                request.addBatch();
            }
            SqlerTemplate.batchUpdate(request);
        }
    }

    /**
     * This method corresponds to the database table wh_system_log
     *
     * @mbggenerated Thu Aug 16 10:59:27 CST 2018
     */
    public void batchUpdateWhSystemLog(List<WhSystemLog> entityList) {
        if (entityList != null && !entityList.isEmpty()) {
            SqlerRequest request = new SqlerRequest("updateWhSystemLogByPrimaryKey");
            for (WhSystemLog entity : entityList) {
                request.addBatchDataParam(WhSystemLogDBField.ID, DataType.INT, entity.getId());

                request.addBatchDataParam(WhSystemLogDBField.RELEVANCE_ID, DataType.INT, entity.getRelevanceId());
                request.addBatchDataParam(WhSystemLogDBField.MODULE, DataType.STRING, entity.getModule());

                request.addBatchDataParam(WhSystemLogDBField.CREATE_BY, DataType.INT, entity.getCreateBy());
                request.addBatchDataParam(WhSystemLogDBField.CONTENT, DataType.STRING, entity.getContent());
                request.addBatch();
            }
            SqlerTemplate.batchUpdate(request);
        }
    }

    /**
     * This method corresponds to the database table wh_system_log
     *
     * @mbggenerated Thu Aug 16 10:59:27 CST 2018
     */
    public void deleteWhSystemLog(Integer primaryKey) {
        SqlerRequest request = new SqlerRequest("deleteWhSystemLogByPrimaryKey");
        request.addDataParam(WhSystemLogDBField.ID, DataType.INT, primaryKey);
        SqlerTemplate.execute(request);
    }

    @Override
    public void deleteByIds(List<Integer> ids, String tableName) {
        if (CollectionUtils.isEmpty(ids)) return;
        SqlerRequest request = new SqlerRequest("deleteSystemLogByIds");
        request.addSqlDataParam("id_list","("+ StringUtils.join(ids,",")+")");
        request.addSqlDataParam("table_index", Optional.ofNullable(tableName).orElse("wh_system_log"));
        SqlerTemplate.execute(request);
    }
}