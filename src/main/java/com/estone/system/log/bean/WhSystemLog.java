package com.estone.system.log.bean;

import com.estone.common.util.TaglibUtils;

import java.io.Serializable;
import java.sql.Timestamp;

public class WhSystemLog implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 
     * This field corresponds to the database column wh_system_log.id
     *
     * @mbggenerated Thu Aug 16 10:59:27 CST 2018
     */
    private Integer id;

    /**
     * 关联的主键 This field corresponds to the database column
     * wh_system_log.relevance_id
     *
     * @mbggenerated Thu Aug 16 10:59:27 CST 2018
     */
    private Integer relevanceId;

    /**
     * 定义模块 This field corresponds to the database column wh_system_log.module
     *
     * @mbggenerated Thu Aug 16 10:59:27 CST 2018
     */
    private String module;

    /**
     * 创建时间 This field corresponds to the database column
     * wh_system_log.creation_date
     *
     * @mbggenerated Thu Aug 16 10:59:27 CST 2018
     */
    private Timestamp creationDate;

    /**
     * 创建人 This field corresponds to the database column wh_system_log.create_by
     *
     * @mbggenerated Thu Aug 16 10:59:27 CST 2018
     */
    private Integer createBy;

    /**
     * 日志内容 This field corresponds to the database column wh_system_log.content
     *
     * @mbggenerated Thu Aug 16 10:59:27 CST 2018
     */
    private String content;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getRelevanceId() {
        return relevanceId;
    }

    public void setRelevanceId(Integer relevanceId) {
        this.relevanceId = relevanceId;
    }

    public String getModule() {
        return module;
    }

    public void setModule(String module) {
        this.module = module;
    }

    public Timestamp getCreationDate() {
        return creationDate;
    }

    public void setCreationDate(Timestamp creationDate) {
        this.creationDate = creationDate;
    }

    public Integer getCreateBy() {
        return createBy;
    }

    public void setCreateBy(Integer createBy) {
        this.createBy = createBy;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getCreateByStr(){
        return TaglibUtils.getEmployeeNameByUserId(createBy);
    }
}