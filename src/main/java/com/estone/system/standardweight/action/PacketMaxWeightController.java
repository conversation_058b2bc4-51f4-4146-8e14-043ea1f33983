package com.estone.system.standardweight.action;

import com.estone.common.SelectJson;
import com.estone.scan.deliver.bean.WhCollectCompany;
import com.estone.scan.deliver.service.WhCollectCompanyService;
import com.estone.system.standardweight.bean.PacketMaxWeightConfig;
import com.estone.system.standardweight.domain.PacketMaxWeightDo;
import com.estone.system.standardweight.util.PacketMaxWeightUtils;
import com.whq.tool.action.BaseController;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 结袋最大重量拦截配置
 */
@Controller
@RequestMapping(value = "system/packetMaxWeight")
public class PacketMaxWeightController extends BaseController {

    @Resource
    private WhCollectCompanyService whCollectCompanyService;

    @RequestMapping(method = { RequestMethod.GET })
    public String init(@ModelAttribute("domain") PacketMaxWeightDo domain) {
        querySystemParams(domain);
        return "system/system_packet_max_weight_list";
    }

    private void initFormData(@ModelAttribute("domain") PacketMaxWeightDo domain) {
        List<WhCollectCompany> whCollectCompanies = whCollectCompanyService.queryAllWhCollectCompanys();
        if (CollectionUtils.isNotEmpty(whCollectCompanies)) {
            String selectJsonStr = SelectJson.getListById(whCollectCompanies.toArray());
            domain.setCompanySelectJson(selectJsonStr);
        }
    }

    private void querySystemParams(@ModelAttribute("domain") PacketMaxWeightDo domain) {
        domain.setMaxWeightList(PacketMaxWeightUtils.getPacketMaxWeightConfigs());
    }

    @RequestMapping(value = "update", method = { RequestMethod.GET })
    public String toUpdateExpressWeightProportion(@ModelAttribute("domain") PacketMaxWeightDo domain) {
        initFormData(domain);
        querySystemParams(domain);
        return "system/system_packet_max_weight_update";
    }

    @ResponseBody
    @RequestMapping(value = "update", method = { RequestMethod.POST })
    public String updateQcProportion(@RequestBody List<PacketMaxWeightConfig> maxWeightConfigs) {
        return PacketMaxWeightUtils.setPacketMaxWeightConfigs2Redis(maxWeightConfigs);
    }

    @RequestMapping(value = "delete", method = { RequestMethod.GET })
    public String deleteQcProportion(@ModelAttribute("domain") PacketMaxWeightDo domain) {
        PacketMaxWeightUtils.deletePacketMaxWeightConfigs();
        return "redirect:/system/packetMaxWeight";
    }

}