package com.estone.system.standardweight.util;

import com.alibaba.fastjson.JSON;
import com.estone.common.util.CacheUtils;
import com.estone.common.util.SpringUtils;
import com.estone.system.param.bean.SystemParam;
import com.estone.system.param.service.SystemParamService;
import com.estone.system.standardweight.bean.ExpressWeightProportion;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 *  快递交运重量拦截配置
 */
@Slf4j
public class ExpressWeightProportionUtils {

    private static SystemParamService systemParamService = SpringUtils.getBean(SystemParamService.class);

    private final static String CODE_KEY_WEIGHT = "WMS_EXPRESS_WEIGHT_PROPORTION.WMS_EXPRESS_WEIGHT_PROPORTION";

    public static List<ExpressWeightProportion> getExpressWeightProportions() {
        SystemParam systemParam = CacheUtils.SystemParamGet(CODE_KEY_WEIGHT);
        if (systemParam == null){
            return null;
        }
        List<ExpressWeightProportion> ExpressWeightProportions = null;
        if (StringUtils.isNotBlank(systemParam.getParamValue())) {
            ExpressWeightProportions = JSON.parseArray(systemParam.getParamValue(), ExpressWeightProportion.class);
            Collections.sort(ExpressWeightProportions);
        }
        return ExpressWeightProportions;
    }

    public static String setExpressWeightProportions2Redis(List<ExpressWeightProportion> expressWeightProportions) {
        String result = null;
        if (CollectionUtils.isNotEmpty(expressWeightProportions)) {
            List<ExpressWeightProportion> list = new ArrayList<ExpressWeightProportion>();
            for (ExpressWeightProportion proportion : expressWeightProportions) {
                if (proportion == null || proportion.getLeft() == null) {
                    log.info("左右区间值为空");
                    continue;
                }
                log.info(JSON.toJSONString(proportion));
                list.add(proportion);
            }
            if (CollectionUtils.isNotEmpty(list)) {
                Collections.sort(list);
                String ExpressWeightProportionsJson = JSON.toJSONString(list);
                SystemParam systemParam = CacheUtils.SystemParamGet(CODE_KEY_WEIGHT);
                if (null != systemParam) {
                    systemParam.setParamValue(ExpressWeightProportionsJson);
                    systemParamService.updateSystemParam(systemParam);
                }
                else {
                    systemParam = new SystemParam();
                    systemParam.setParamCode(CODE_KEY_WEIGHT.split("\\.")[0]);
                    systemParam.setParamKey(CODE_KEY_WEIGHT.split("\\.")[0]);
                    systemParam.setParamName("快递交运拦截配置");
                    systemParam.setParamValue(ExpressWeightProportionsJson);
                    systemParam.setParamType(5);
                    systemParam.setParamDisplay(true);
                    systemParam.setParamEnabled(true);
                    systemParamService.createSystemParam(systemParam);
                }
                log.info(String.format("update %s set value %s", CODE_KEY_WEIGHT, ExpressWeightProportionsJson));
            }
        }
        return result;
    }

    public static ExpressWeightProportion getExpressWeightProportion(Double weight) {
        ExpressWeightProportion proportion = null;
        List<ExpressWeightProportion> ExpressWeightProportions = getExpressWeightProportions();
        if (CollectionUtils.isNotEmpty(ExpressWeightProportions)) {
            for (ExpressWeightProportion ExpressWeightProportion : ExpressWeightProportions) {
                if (ExpressWeightProportion.getLeft() != null && ExpressWeightProportion.getRight() != null
                        && weight >= ExpressWeightProportion.getLeft()
                        && weight < ExpressWeightProportion.getRight()) {
                    proportion = ExpressWeightProportion;
                    break;
                }
                else if (ExpressWeightProportion.getIsMax() != null && ExpressWeightProportion.getIsMax()
                        && weight >= ExpressWeightProportion.getLeft()) {
                    proportion = ExpressWeightProportion;
                }
            }
        }
        /*
         * if (proportion != null) { //qcQuantity = (int)
         * Math.round(weight*proportion);// 四舍五入取整 }
         */
        return proportion;
    }

    public static void deleteExpressWeightProportions() {
        SystemParam systemParam = CacheUtils.SystemParamGet(CODE_KEY_WEIGHT);
        log.info(String.format("DELETE %s", CODE_KEY_WEIGHT));
        if (systemParam != null && systemParam.getParamId() != null) {
            systemParam.setParamValue("");
            systemParamService.updateSystemParam(systemParam);
        }
    }

    /**
     * 数据库标准重量数据处理
     * @param proportions
     * @return
     */
    public static List<ExpressWeightProportion> getExpressWeightProportions(List<ExpressWeightProportion> proportions) {
        if (CollectionUtils.isEmpty(proportions)) {
            proportions = new ArrayList<ExpressWeightProportion>();
            ExpressWeightProportion min = new ExpressWeightProportion();
            min.setIsMin(true);
            min.setIsDiff(false);
            min.setLeft(0d);
            ExpressWeightProportion max = new ExpressWeightProportion();
            max.setIsMax(true);
            max.setIsDiff(false);
            max.setLeft(1000d);
            proportions.add(min);
            // proportions.add(max);
        }
        return proportions;
    }

}
