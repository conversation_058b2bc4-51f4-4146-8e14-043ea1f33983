package com.estone.system.role.dao.impl;

import java.sql.Timestamp;
import java.util.List;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Repository;
import org.springframework.util.Assert;

import com.estone.common.util.CommonUtils;
import com.estone.common.util.Constant;
import com.estone.system.role.bean.Role;
import com.estone.system.role.bean.RoleQueryCondition;
import com.estone.system.role.dao.RoleDao;
import com.estone.system.role.dao.mapper.RoleDBField;
import com.estone.system.role.dao.mapper.RoleMapper;
import com.estone.system.user.bean.SaleUser;
import com.estone.system.user.dao.mapper.SaleUserDBField;
import com.whq.tool.component.Pager;
import com.whq.tool.context.DataContextHolder;
import com.whq.tool.sqler.SqlerRequest;
import com.whq.tool.sqler.analyzer.DataType;
import com.estone.common.util.SqlerTemplate;

@Repository("roleDao")
public class RoleDaoImpl implements RoleDao {

    private void setQueryCondition(SqlerRequest request, RoleQueryCondition query) {
        if (query == null) {
            return;
        }
        // 添加查询条件
        // 搜索条件不允许出现空字符
        request.setAllowBlank(false);

        request.addDataParam(RoleDBField.ROLE_ID, DataType.INT, query.getRoleId());

        request.addDataParam(RoleDBField.ROLE_NAME, DataType.STRING, query.getRoleName());
        
        if (CollectionUtils.isNotEmpty(query.getRoleIdList())) {
            if (query.getRoleIdList().size() > 1) {
                request.addDataParam("role_id_list", DataType.INT, query.getRoleIdList());
            }else {
                request.addDataParam(RoleDBField.ROLE_ID, DataType.INT, query.getRoleIdList().get(0));
            }
        }
        
        if(StringUtils.isNotBlank(query.getRoleIds())){
            if(StringUtils.contains(query.getRoleIds(), ",")){
                request.addDataParam("role_id_list", DataType.INT, CommonUtils.splitIntList(query.getRoleIds(), ","));
            }else{
                request.addDataParam(RoleDBField.ROLE_ID, DataType.INT, query.getRoleIds()); 
            }
        }
        
        if(StringUtils.isNotBlank(query.getLikeRoleName())){
            request.addRightFuzzyDataParam("like_rolename", query.getLikeRoleName());
        }
        if (StringUtils.isNotBlank(query.getMenuCode())){
            request.addDataParam("menuCode", DataType.STRING, query.getMenuCode());
        }
    }

    /**
     * This method corresponds to the database table t_role
     *
     * @mbggenerated Thu Sep 14 17:48:49 CST 2017
     */
    public int queryRoleCount(RoleQueryCondition query) {
        SqlerRequest request = new SqlerRequest("queryRoleCount");
        setQueryCondition(request, query);
        return SqlerTemplate.queryForInt(request);
    }

    /**
     * This method corresponds to the database table t_role
     *
     * @mbggenerated Thu Sep 14 17:48:49 CST 2017
     */
    public List<Role> queryRoleList() {
        SqlerRequest request = new SqlerRequest("queryRoleList");
        return SqlerTemplate.query(request, new RoleMapper());
    }

    /**
     * This method corresponds to the database table t_role
     *
     * @mbggenerated Thu Sep 14 17:48:49 CST 2017
     */
    public List<Role> queryRoleList(RoleQueryCondition query, Pager pager) {
        SqlerRequest request = new SqlerRequest("queryRoleList");
        setQueryCondition(request, query);
        if (pager != null) {
            request.addFetch(pager.getPageNo(), pager.getPageSize());
        }
        return SqlerTemplate.query(request, new RoleMapper());
    }

    /**
     * This method corresponds to the database table t_role
     *
     * @mbggenerated Thu Sep 14 17:48:49 CST 2017
     */
    public Role queryRole(Integer primaryKey) {
        Assert.notNull(primaryKey);
        SqlerRequest request = new SqlerRequest("queryRoleByPrimaryKey");
        request.addDataParam(RoleDBField.ROLE_ID, DataType.INT, primaryKey);
        return SqlerTemplate.queryForObject(request, new RoleMapper());
    }

    /**
     * This method corresponds to the database table t_role
     *
     * @mbggenerated Thu Sep 14 17:48:49 CST 2017
     */
    public Role queryRole(RoleQueryCondition query) {
        SqlerRequest request = new SqlerRequest("queryRole");
        setQueryCondition(request, query);
        return SqlerTemplate.queryForObject(request, new RoleMapper());
    }

    /**
     * This method corresponds to the database table t_role
     *
     * @mbggenerated Thu Sep 14 17:48:49 CST 2017
     */
    public void createRole(Role entity) {
        SqlerRequest request = new SqlerRequest("createRole");
        request.addDataParam(RoleDBField.ROLE_NAME, DataType.STRING, entity.getRoleName());
        request.addDataParam(RoleDBField.WORKDAY_DETAILS, DataType.STRING, entity.getWorkdayDetails());
        request.addDataParam(RoleDBField.WORKTIME_START, DataType.STRING, entity.getWorktimeStart());
        request.addDataParam(RoleDBField.WORKTIME_END, DataType.STRING, entity.getWorktimeEnd());
        request.addDataParam(RoleDBField.CREATION_DATE, DataType.TIMESTAMP, new Timestamp(System.currentTimeMillis()));
        request.addDataParam(RoleDBField.CREATED_BY, DataType.INT, entity.getCreatedBy() == null
                ? Integer.valueOf(DataContextHolder.getContext(Constant.SALE_USER_ID).toString()) : entity.getCreatedBy());
        request.addDataParam(RoleDBField.LAST_UPDATE_DATE, DataType.TIMESTAMP,
                new Timestamp(System.currentTimeMillis()));
        request.addDataParam(RoleDBField.LAST_UPDATED_BY, DataType.INT, entity.getLastUpdatedBy() == null
                ? Integer.valueOf(DataContextHolder.getContext(Constant.SALE_USER_ID).toString()) : entity.getLastUpdatedBy());
        request.addDataParam(RoleDBField.REMARK, DataType.STRING, entity.getRemark());
        Integer autoIncrementId = SqlerTemplate.executeAndReturn(request);
        entity.setRoleId(autoIncrementId);
    }

    /**
     * This method corresponds to the database table t_role
     *
     * @mbggenerated Thu Sep 14 17:48:49 CST 2017
     */
    public void updateRole(Role entity) {
        SqlerRequest request = new SqlerRequest("updateRoleByPrimaryKey");
        request.addDataParam(RoleDBField.ROLE_ID, DataType.INT, entity.getRoleId());

        request.addDataParam(RoleDBField.ROLE_NAME, DataType.STRING, entity.getRoleName());
        request.addDataParam(RoleDBField.WORKDAY_DETAILS, DataType.STRING, entity.getWorkdayDetails());
        request.addDataParam(RoleDBField.WORKTIME_START, DataType.STRING, entity.getWorktimeStart());
        request.addDataParam(RoleDBField.WORKTIME_END, DataType.STRING, entity.getWorktimeEnd());

        request.addDataParam(RoleDBField.LAST_UPDATE_DATE, DataType.TIMESTAMP,
                new Timestamp(System.currentTimeMillis()));
        request.addDataParam(RoleDBField.LAST_UPDATED_BY, DataType.INT, entity.getLastUpdatedBy() == null
                ? Integer.valueOf(DataContextHolder.getContext(Constant.SALE_USER_ID).toString()) : entity.getLastUpdatedBy());
        request.addDataParam(RoleDBField.REMARK, DataType.STRING, entity.getRemark());
        SqlerTemplate.execute(request);
    }

    /**
     * This method corresponds to the database table t_role
     *
     * @mbggenerated Thu Sep 14 17:48:49 CST 2017
     */
    public void batchCreateRole(List<Role> entityList) {
        if (entityList != null && !entityList.isEmpty()) {
            SqlerRequest request = new SqlerRequest("createRole");
            for (Role entity : entityList) {
                request.addBatchDataParam(RoleDBField.ROLE_NAME, DataType.STRING, entity.getRoleName());
                request.addBatchDataParam(RoleDBField.WORKDAY_DETAILS, DataType.STRING, entity.getWorkdayDetails());
                request.addBatchDataParam(RoleDBField.WORKTIME_START, DataType.STRING, entity.getWorktimeStart());
                request.addBatchDataParam(RoleDBField.WORKTIME_END, DataType.STRING, entity.getWorktimeEnd());
                request.addBatchDataParam(RoleDBField.CREATION_DATE, DataType.TIMESTAMP,
                        new Timestamp(System.currentTimeMillis()));
                request.addBatchDataParam(RoleDBField.CREATED_BY, DataType.INT, entity.getCreatedBy() == null
                        ? Integer.valueOf(DataContextHolder.getContext(Constant.SALE_USER_ID).toString()) : entity.getCreatedBy());
                request.addBatchDataParam(RoleDBField.LAST_UPDATE_DATE, DataType.TIMESTAMP,
                        new Timestamp(System.currentTimeMillis()));
                request.addBatchDataParam(RoleDBField.LAST_UPDATED_BY, DataType.INT, entity.getLastUpdatedBy() == null
                        ? Integer.valueOf(DataContextHolder.getContext(Constant.SALE_USER_ID).toString()) : entity.getLastUpdatedBy());
                request.addBatchDataParam(RoleDBField.REMARK, DataType.STRING, entity.getRemark());
                request.addBatch();
            }
            SqlerTemplate.batchUpdate(request);
        }
    }

    /**
     * This method corresponds to the database table t_role
     *
     * @mbggenerated Thu Sep 14 17:48:49 CST 2017
     */
    public void batchUpdateRole(List<Role> entityList) {
        if (entityList != null && !entityList.isEmpty()) {
            SqlerRequest request = new SqlerRequest("updateRoleByPrimaryKey");
            for (Role entity : entityList) {
                request.addBatchDataParam(RoleDBField.ROLE_ID, DataType.INT, entity.getRoleId());

                request.addBatchDataParam(RoleDBField.ROLE_NAME, DataType.STRING, entity.getRoleName());
                request.addBatchDataParam(RoleDBField.WORKDAY_DETAILS, DataType.STRING, entity.getWorkdayDetails());
                request.addBatchDataParam(RoleDBField.WORKTIME_START, DataType.STRING, entity.getWorktimeStart());
                request.addBatchDataParam(RoleDBField.WORKTIME_END, DataType.STRING, entity.getWorktimeEnd());

                request.addBatchDataParam(RoleDBField.LAST_UPDATE_DATE, DataType.TIMESTAMP,
                        new Timestamp(System.currentTimeMillis()));
                request.addBatchDataParam(RoleDBField.LAST_UPDATED_BY, DataType.INT, entity.getLastUpdatedBy() == null
                        ? Integer.valueOf(DataContextHolder.getContext(Constant.SALE_USER_ID).toString()) : entity.getLastUpdatedBy());
                request.addBatchDataParam(RoleDBField.REMARK, DataType.STRING, entity.getRemark());
                request.addBatch();
            }
            SqlerTemplate.batchUpdate(request);
        }
    }

    /**
     * This method corresponds to the database table t_role
     *
     * @mbggenerated Thu Sep 14 17:48:49 CST 2017
     */
    public void deleteRole(Integer primaryKey) {
        SqlerRequest request = new SqlerRequest("deleteRoleByPrimaryKey");
        request.addDataParam(RoleDBField.ROLE_ID, DataType.INT, primaryKey);
        SqlerTemplate.execute(request);
    }

    @Override
    public void deleteUserRoleByUserId(Integer userId) {
        Assert.notNull(userId);

        SqlerRequest request = new SqlerRequest("deleteUserRole");

        request.addDataParam(SaleUserDBField.USER_ID, DataType.INT, userId);

        SqlerTemplate.execute(request);

    }

    @Override
    public void deleteUserRoleByUserIdInRoleList(Integer userId, List<Integer> roleIdList) {
        Assert.notNull(userId);

        SqlerRequest request = new SqlerRequest("deleteUserRole");

        request.addDataParam(SaleUserDBField.USER_ID, DataType.INT, userId);
        request.addDataParam("role_id_list", DataType.INT, roleIdList);

        SqlerTemplate.execute(request);

    }

    @Override
    public void deleteUserRoleByRoleId(Integer roleId) {
        Assert.notNull(roleId);

        SqlerRequest request = new SqlerRequest("deleteUserRole");

        request.addDataParam(RoleDBField.ROLE_ID, DataType.INT, roleId);

        SqlerTemplate.execute(request);

    }

    @Override
    public void createUserRole(SaleUser user, List<Role> roleList) {
        Assert.notNull(user);
        Assert.notEmpty(roleList);

        SqlerRequest request = new SqlerRequest("createUserRole");

        Integer userId = user.getUserId();
        for (Role role : roleList) {
            Integer roleId = role.getRoleId();
            request.addBatchDataParam(SaleUserDBField.USER_ID, DataType.INT, userId);
            request.addBatchDataParam(RoleDBField.ROLE_ID, DataType.INT, roleId);
            request.addBatch();
        }

        SqlerTemplate.batchUpdate(request);

    }

    @Override
    public List<Role> queryRoleByUserId(Integer userId) {
        SqlerRequest request = new SqlerRequest("queryUserRoleList");
        request.addDataParam(SaleUserDBField.USER_ID, DataType.INT, userId);
        return SqlerTemplate.query(request, new RoleMapper());
    }

    @Override
    public List<Role> queryRolesByCreator(Integer creator) {
        SqlerRequest request = new SqlerRequest("queryRoleList");
        request.addDataParam(RoleDBField.CREATED_BY, DataType.INT, creator);
        return SqlerTemplate.query(request, new RoleMapper());
    }
}