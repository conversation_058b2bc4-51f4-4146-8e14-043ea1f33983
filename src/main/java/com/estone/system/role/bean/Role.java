package com.estone.system.role.bean;

import java.io.Serializable;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;

import com.estone.system.permission.bean.Permission;
import com.estone.system.user.bean.SaleUser;

public class Role implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 
     * This field corresponds to the database column t_role.role_id
     *
     * @mbggenerated Thu Sep 14 17:48:49 CST 2017
     */
    private Integer roleId;

    /**
     * 
     * This field corresponds to the database column t_role.role_name
     *
     * @mbggenerated Thu Sep 14 17:48:49 CST 2017
     */
    private String roleName;

    /**
     * 工作日 This field corresponds to the database column t_role.workday_details
     *
     * @mbggenerated Thu Sep 14 17:48:49 CST 2017
     */
    private String workdayDetails;

    /**
     * 工作时间 This field corresponds to the database column t_role.worktime_start
     *
     * @mbggenerated Thu Sep 14 17:48:49 CST 2017
     */
    private String worktimeStart;

    /**
     * 
     * This field corresponds to the database column t_role.worktime_end
     *
     * @mbggenerated Thu Sep 14 17:48:49 CST 2017
     */
    private String worktimeEnd;

    /**
     * 
     * This field corresponds to the database column t_role.creation_date
     *
     * @mbggenerated Thu Sep 14 17:48:49 CST 2017
     */
    private Timestamp creationDate;

    /**
     * 
     * This field corresponds to the database column t_role.created_by
     *
     * @mbggenerated Thu Sep 14 17:48:49 CST 2017
     */
    private Integer createdBy;

    /**
     * 
     * This field corresponds to the database column t_role.last_update_date
     *
     * @mbggenerated Thu Sep 14 17:48:49 CST 2017
     */
    private Timestamp lastUpdateDate;

    /**
     * 
     * This field corresponds to the database column t_role.last_updated_by
     *
     * @mbggenerated Thu Sep 14 17:48:49 CST 2017
     */
    private Integer lastUpdatedBy;

    /**
     * 
     * This field corresponds to the database column t_role.remark
     *
     * @mbggenerated Thu Sep 14 17:48:49 CST 2017
     */
    private String remark;
    
    
    private List<SaleUser> users = new ArrayList<SaleUser>();
    
    /**
     * 权限
     */
    private List<Permission> permissions = new ArrayList<Permission>();
    
    

    public List<SaleUser> getUsers() {
        return users;
    }

    public void setUsers(List<SaleUser> users) {
        this.users = users;
    }

    public List<Permission> getPermissions() {
        return permissions;
    }

    public void setPermissions(List<Permission> permissions) {
        this.permissions = permissions;
    }

    public Integer getRoleId() {
        return roleId;
    }

    public void setRoleId(Integer roleId) {
        this.roleId = roleId;
    }

    public String getRoleName() {
        return roleName;
    }

    public void setRoleName(String roleName) {
        this.roleName = roleName;
    }

    public String getWorkdayDetails() {
        return workdayDetails;
    }

    public void setWorkdayDetails(String workdayDetails) {
        this.workdayDetails = workdayDetails;
    }

    public String getWorktimeStart() {
        return worktimeStart;
    }

    public void setWorktimeStart(String worktimeStart) {
        this.worktimeStart = worktimeStart;
    }

    public String getWorktimeEnd() {
        return worktimeEnd;
    }

    public void setWorktimeEnd(String worktimeEnd) {
        this.worktimeEnd = worktimeEnd;
    }

    public Timestamp getCreationDate() {
        return creationDate;
    }

    public void setCreationDate(Timestamp creationDate) {
        this.creationDate = creationDate;
    }

    public Integer getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(Integer createdBy) {
        this.createdBy = createdBy;
    }

    public Timestamp getLastUpdateDate() {
        return lastUpdateDate;
    }

    public void setLastUpdateDate(Timestamp lastUpdateDate) {
        this.lastUpdateDate = lastUpdateDate;
    }

    public Integer getLastUpdatedBy() {
        return lastUpdatedBy;
    }

    public void setLastUpdatedBy(Integer lastUpdatedBy) {
        this.lastUpdatedBy = lastUpdatedBy;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }
}