package com.estone.system.menu.util;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import com.estone.common.component.ZtreeNode;
import com.estone.system.menu.bean.Menu;
import com.estone.system.permission.bean.Permission;

public class MenuUtils {

    /**
     * 菜单树编辑
     * 
     * @param menus
     * @return
     */
    public static List<ZtreeNode> transformToZtreeNode(List<Menu> menus) {
        List<ZtreeNode> tree = new ArrayList<ZtreeNode>();

        if (CollectionUtils.isEmpty(menus)) {
            return tree;
        }

        for (Menu menu : menus) {
            String permissionCode = menu.getMenuCode();
            ZtreeNode node = new ZtreeNode();
            node.setId(permissionCode);
            node.setValue(permissionCode);
            node.setpId(menu.getParentId());
            node.setName(menu.getMenuName());

            node.setMenuId(menu.getMenuId());
            node.setUrl(menu.getUrl());
            node.setPriority(menu.getPriority());
            node.setOldOrNew(menu.getOldOrNew());

            tree.add(node);
        }

        return tree;
    }

    public static List<ZtreeNode> transformToZtreeNodeForPermission(List<Permission> permissions) {
        List<ZtreeNode> tree = new ArrayList<ZtreeNode>();

        if (CollectionUtils.isEmpty(permissions)) {
            return tree;
        }

        for (Permission per : permissions) {
            String permissionCode = per.getPermissionCode();
            ZtreeNode node = new ZtreeNode();
            node.setId(permissionCode);
            node.setValue(permissionCode);
            node.setpId(per.getParentId());
            node.setName(per.getPermissionName());

            node.setMenuId(per.getPermissionId());
            node.setPriority(per.getPriority());

            tree.add(node);
        }

        return tree;
    }

    public static List<Menu> transfromToTree(List<Menu> menuList) {

        if (CollectionUtils.isEmpty(menuList)) {
            return new ArrayList<Menu>();
        }

        // 找出根节点，加载自定义菜单
        List<Menu> rootMenu = new ArrayList<Menu>();
        for (Menu menu : menuList) {
            // 从缓存取到的对象会关联，所以这里要清空一下
            menu.getChildren().clear();

            if (String.valueOf("-1").equals(menu.getParentId())) {
                rootMenu.add(menu);
            }
        }

        // 排序根菜单
        Collections.sort(rootMenu);

        // 递归找出子菜单
        for (Menu menu : rootMenu) {
            recursion(menu, menuList, 1);

            // 二级
            List<Menu> children = menu.getChildren();
            Collections.sort(children);

            // 三级
            for (Menu child : children) {
                Collections.sort(child.getChildren());
            }
        }

        return rootMenu;
    }

    /**
     * 递归添加树节点
     * 
     * <p>
     * TODO 方法功能描述
     * 
     * @param parentMenu
     * @param children
     * @param depth
     * @return void
     */
    private static void recursion(Menu parentMenu, List<Menu> children, int depth) {
        depth++;

        // 是否找到子节点
        for (Menu child : children) {
            if (StringUtils.equals(parentMenu.getMenuCode(), child.getParentId())) {
                parentMenu.addChildren(child);

                recursion(child, children, depth);
            }
        }

        // 加载自定义菜单(只存在第三层)
        if (depth == 3) {
            // loadCustomMenu(parentMenu);
        }

    }
}
