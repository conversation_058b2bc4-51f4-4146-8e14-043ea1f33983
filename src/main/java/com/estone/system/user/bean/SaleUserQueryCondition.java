package com.estone.system.user.bean;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class SaleUserQueryCondition extends SaleUser {
    private static final long serialVersionUID = 1L;

    private String likeUserName;

    private String likeName;

    /**
     * 角色
     */
    private Integer roleId;

    /**
     * 角色ID列表
     */
    private List<Integer> roleIdList = new ArrayList<Integer>();

    private List<Integer> userIdList = new ArrayList<Integer>();

    private List<Long> emplIdList = new ArrayList<Long>();

    /**
     * 部门ID集合
     */
    private String departmentIds;

    private List<Integer> departmentIdList = new ArrayList<Integer>();

    /**
     * 角色名称
     */
    private String roleName;

    private List<String> userNameList = new ArrayList<String>();

    public String getLikeUserName() {
        return likeUserName;
    }

    public void setLikeUserName(String likeUserName) {
        this.likeUserName = likeUserName;
    }

    public String getLikeName() {
        return likeName;
    }

    public void setLikeName(String likeName) {
        this.likeName = likeName;
    }

    public Integer getRoleId() {
        return roleId;
    }

    public void setRoleId(Integer roleId) {
        this.roleId = roleId;
    }

    public List<Integer> getRoleIdList() {
        return roleIdList;
    }

    public void setRoleIdList(List<Integer> roleIdList) {
        this.roleIdList = roleIdList;
    }

    public List<Integer> getUserIdList() {
        return userIdList;
    }

    public void setUserIdList(List<Integer> userIdList) {
        this.userIdList = userIdList;
    }

    public String getDepartmentIds() {
        return departmentIds;
    }

    public void setDepartmentIds(String departmentIds) {
        this.departmentIds = departmentIds;
    }

    public List<Integer> getDepartmentIdList() {
        return departmentIdList;
    }

    public void setDepartmentIdList(List<Integer> departmentIdList) {
        this.departmentIdList = departmentIdList;
    }

    public String getRoleName() {
        return roleName;
    }

    public void setRoleName(String roleName) {
        this.roleName = roleName;
    }

}