package com.estone.system.user.bean;

import java.io.Serializable;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;

import com.estone.system.department.bean.Department;
import com.estone.system.role.bean.Role;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

@Data
public class SaleUser implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * PDA临时存储变量
     */
    private String token;

    /**
     * 
     * This field corresponds to the database column t_user.user_id
     *
     * @mbggenerated Fri Sep 15 10:23:32 CST 2017
     */
    private Integer userId;

    /**
     * 
     * This field corresponds to the database column t_user.name
     *
     * @mbggenerated Fri Sep 15 10:23:32 CST 2017
     */
    private String name;

    /**
     * 
     * This field corresponds to the database column t_user.job_number
     *
     * @mbggenerated Fri Sep 15 10:23:32 CST 2017
     */
    private String jobNumber;

    /**
     * 
     * This field corresponds to the database column t_user.username
     *
     * @mbggenerated Fri Sep 15 10:23:32 CST 2017
     */
    private String username;

    /**
     * 
     * This field corresponds to the database column t_user.password
     *
     * @mbggenerated Fri Sep 15 10:23:32 CST 2017
     */
    @JsonIgnore
    private String password;

    /**
     * 
     * This field corresponds to the database column t_user.department_id
     *
     * @mbggenerated Fri Sep 15 10:23:32 CST 2017
     */
    private Integer departmentId;

    /**
     * 
     * This field corresponds to the database column t_user.creation_date
     *
     * @mbggenerated Fri Sep 15 10:23:32 CST 2017
     */
    private Timestamp creationDate;

    /**
     * 
     * This field corresponds to the database column t_user.created_by
     *
     * @mbggenerated Fri Sep 15 10:23:32 CST 2017
     */
    private Integer createdBy;

    /**
     * 
     * This field corresponds to the database column t_user.last_update_date
     *
     * @mbggenerated Fri Sep 15 10:23:32 CST 2017
     */
    private Timestamp lastUpdateDate;

    /**
     * 
     * This field corresponds to the database column t_user.last_updated_by
     *
     * @mbggenerated Fri Sep 15 10:23:32 CST 2017
     */
    private Integer lastUpdatedBy;

    /**
     * 
     * This field corresponds to the database column t_user.remark
     *
     * @mbggenerated Fri Sep 15 10:23:32 CST 2017
     */
    private String remark;

    /**
     * 是否可用 This field corresponds to the database column t_user.enabled
     *
     * @mbggenerated Fri Sep 15 10:23:32 CST 2017
     */
    private Boolean enabled;

    /**
     * 联系人 This field corresponds to the database column t_user.contact_details
     *
     * @mbggenerated Fri Sep 15 10:23:32 CST 2017
     */
    private String contactDetails;

    /**
     * 上级主管 This field corresponds to the database column t_user.supervisor
     *
     * @mbggenerated Fri Sep 15 10:23:32 CST 2017
     */
    private String supervisor;

    private String empName;

    private String empNo;

    private Long empId;

    /**
     * 角色ID列表
     */
    private List<Integer> roleIdList = new ArrayList<Integer>();

    /**
     * 所属角色
     */
    private List<Role> roles = new ArrayList<Role>();

    /**
     * 部门
     */
    private Department department = new Department();

    /**
     * 上级管理
     */
    private SaleUser supervisorSaleUser;

    public List<Integer> getRoleIdList() {
        return roleIdList;
    }

    public void setRoleIdList(List<Integer> roleIdList) {
        this.roleIdList = roleIdList;
    }

    public List<Role> getRoles() {
        return roles;
    }

    public void setRoles(List<Role> roles) {
        this.roles = roles;
    }

    public Integer getUserId() {
        return userId;
    }

    public void setUserId(Integer userId) {
        this.userId = userId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getJobNumber() {
        return jobNumber;
    }

    public void setJobNumber(String jobNumber) {
        this.jobNumber = jobNumber;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public Integer getDepartmentId() {
        return departmentId;
    }

    public void setDepartmentId(Integer departmentId) {
        this.departmentId = departmentId;
    }

    public Timestamp getCreationDate() {
        return creationDate;
    }

    public void setCreationDate(Timestamp creationDate) {
        this.creationDate = creationDate;
    }

    public Integer getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(Integer createdBy) {
        this.createdBy = createdBy;
    }

    public Timestamp getLastUpdateDate() {
        return lastUpdateDate;
    }

    public void setLastUpdateDate(Timestamp lastUpdateDate) {
        this.lastUpdateDate = lastUpdateDate;
    }

    public Integer getLastUpdatedBy() {
        return lastUpdatedBy;
    }

    public void setLastUpdatedBy(Integer lastUpdatedBy) {
        this.lastUpdatedBy = lastUpdatedBy;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Boolean getEnabled() {
        return enabled;
    }

    public void setEnabled(Boolean enabled) {
        this.enabled = enabled;
    }

    public String getContactDetails() {
        return contactDetails;
    }

    public void setContactDetails(String contactDetails) {
        this.contactDetails = contactDetails;
    }

    public String getSupervisor() {
        return supervisor;
    }

    public void setSupervisor(String supervisor) {
        this.supervisor = supervisor;
    }

    public void addRole(Role role) {
        this.roles.add(role);
    }

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    public Department getDepartment() {
        return department;
    }

    public void setDepartment(Department department) {
        this.department = department;
    }

    public SaleUser getSupervisorSaleUser() {
        return supervisorSaleUser;
    }

    public void setSupervisorSaleUser(SaleUser supervisorSaleUser) {
        this.supervisorSaleUser = supervisorSaleUser;
    }
}