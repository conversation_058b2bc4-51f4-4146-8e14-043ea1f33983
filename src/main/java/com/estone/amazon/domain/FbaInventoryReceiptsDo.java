package com.estone.amazon.domain;

import java.util.ArrayList;
import java.util.List;

import com.estone.amazon.bean.FbaInventoryReceipts;
import com.estone.amazon.bean.FbaInventoryReceiptsQueryCondition;
import com.whq.tool.component.Pager;

public class FbaInventoryReceiptsDo {
    private FbaInventoryReceipts fbaInventoryReceipts;

    private FbaInventoryReceiptsQueryCondition query;

    private List<FbaInventoryReceipts> fbaInventoryReceiptss = new ArrayList<FbaInventoryReceipts>();

    private Pager page = new Pager();

    public FbaInventoryReceipts getFbaInventoryReceipts() {
        return fbaInventoryReceipts;
    }

    public void setFbaInventoryReceipts(FbaInventoryReceipts fbaInventoryReceipts) {
        this.fbaInventoryReceipts = fbaInventoryReceipts;
    }

    public FbaInventoryReceiptsQueryCondition getQuery() {
        return query;
    }

    public void setQuery(FbaInventoryReceiptsQueryCondition query) {
        this.query = query;
    }

    public List<FbaInventoryReceipts> getFbaInventoryReceiptss() {
        return fbaInventoryReceiptss;
    }

    public void setFbaInventoryReceiptss(List<FbaInventoryReceipts> fbaInventoryReceiptss) {
        this.fbaInventoryReceiptss = fbaInventoryReceiptss;
    }

    public Pager getPage() {
        return page;
    }

    public void setPage(Pager page) {
        this.page = page;
    }
}