package com.estone.amazon.call.report;

import com.estone.amazon.api.AmazonRequestApi;
import com.estone.amazon.model.AmazonAccount;
import com.estone.amazon.model.bo.AmazonReportsDownloadProgress;
import com.estone.amazon.utils.FbaApiUtils;
import com.estone.amazon.utils.LocalApiUtils;
import com.estone.common.util.CsvUtil;
import com.estone.common.util.DateUtils;
import com.estone.common.util.SpringUtils;
import com.estone.common.util.model.ApiResult;
import com.estone.statistics.bean.AmazonFbaFulfillmentInventoryReceiptsData;
import com.estone.statistics.bean.AmazonFbaFulfillmentRemovalOrderDetailData;
import com.estone.statistics.dao.AmazonFbaFulfillmentInventoryReceiptsDataDao;
import com.estone.statistics.dao.AmazonFbaFulfillmentRemovalOrderDetailDataDao;
import com.estone.statistics.domain.report.ReportConfig;
import jodd.util.StringUtil;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.tools.ant.util.FileUtils;
import org.springframework.stereotype.Component;

import java.io.File;
import java.sql.Timestamp;
import java.util.List;
import java.util.Map;

/**
 * @description: 亚马逊库接受报告call
 * <AUTHOR>
 * @date 2021/9/27 12:01
 */
@Slf4j
@Component
@NoArgsConstructor
public class AmazonReceiptReportCall implements AmazonReportCall{
    AmazonAccount amazonAccount;

    ReportConfig config;

    AmazonRequestApi amazonRequestApi = SpringUtils.getBean(AmazonRequestApi.class);

    AmazonFbaFulfillmentInventoryReceiptsDataDao amazonFbaFulfillmentInventoryReceiptsDataDao = SpringUtils.getBean(AmazonFbaFulfillmentInventoryReceiptsDataDao.class);

    public AmazonReceiptReportCall(AmazonAccount amazonAccount, ReportConfig config) {
        this.amazonAccount = amazonAccount;
        this.config = config;
    }
    @Override
    public void getReport() {
        ApiResult<?> apiResult = amazonRequestApi.requestAmazonReport(amazonAccount, config);
        if (apiResult.isSuccess()) {
            File file = (File) apiResult.getResult();
            // 解析文件并插入数据库
            parseFile(file,amazonAccount);
        }
    }

    @Override
    public void parseNewAmazonSpLocal(File file, AmazonReportsDownloadProgress amazonReportsDownloadProgress) {
        try {
            CsvUtil csvUtil = new CsvUtil();
            // 将账号分解 站点-账号
            Map<String, String> accountMap = LocalApiUtils.removeAccountPrefix(amazonReportsDownloadProgress.getAccountNumber());
            // 解析亚马逊物流移除订单详情报告
            List<AmazonFbaFulfillmentInventoryReceiptsData> amazonFbaFulfillmentRemovalOrderDetailData = csvUtil.getCsvData(file, AmazonFbaFulfillmentInventoryReceiptsData.class);
            amazonFbaFulfillmentInventoryReceiptsDataDao.deleteAmazonFbaFulfillmentInventoryReceiptsDataByCondition(accountMap.get("account"),amazonReportsDownloadProgress.getMerchantId(),amazonReportsDownloadProgress.getStartDate(),amazonReportsDownloadProgress.getEndDate());
            amazonFbaFulfillmentRemovalOrderDetailData.forEach(a -> {
                a.setAccountNumber(accountMap.get("account"));
                a.setMarketPlace(accountMap.get("marketPlace"));
                a.setMerchantId(amazonReportsDownloadProgress.getMerchantId());
                a.setCreationDate(new Timestamp(System.currentTimeMillis()));
                a.setLastUpdateDate(new Timestamp(System.currentTimeMillis()));
                if (StringUtil.isNotBlank(a.getReceivedDateStr())) {
                    a.setReceivedDate(new Timestamp(DateUtils.convertDateToLocalDate(a.getReceivedDateStr()).getTime()));
                }
                // 将产品名字中包含,的字符去掉
                if (StringUtil.isNotBlank(a.getProductName())) {
                    a.setProductName(a.getProductName().replaceAll("[%&',;=?$\\x22]+",""));
                }
            });
            // 新增或修改
            amazonFbaFulfillmentInventoryReceiptsDataDao.batchInsertOrUpdateAmazonFbaFulfillmentInventoryReceiptsData(amazonFbaFulfillmentRemovalOrderDetailData);
        }catch (Exception e){
            log.error(amazonReportsDownloadProgress.getAccountNumber()+"解析文件或者插入数据库失败_"+e.getMessage());
        }finally {
            FileUtils.delete(file);
        }
    }

    public void parseFile(File file, AmazonAccount amazonAccount) {
        try {
            CsvUtil csvUtil = new CsvUtil();
            // 将账号分解 站点-账号
            Map<String, String> accountMap = LocalApiUtils.removeAccountPrefix(amazonAccount.getAccountNumber());
            // 解析亚马逊物流移除订单详情报告
            List<AmazonFbaFulfillmentInventoryReceiptsData> amazonFbaFulfillmentRemovalOrderDetailData = csvUtil.getCsvData(file, AmazonFbaFulfillmentInventoryReceiptsData.class);
            amazonFbaFulfillmentInventoryReceiptsDataDao.deleteAmazonFbaFulfillmentInventoryReceiptsDataByCondition(accountMap.get("account"),amazonAccount.getMerchantId(),amazonAccount.getStartDate(),amazonAccount.getEndDate());
            amazonFbaFulfillmentRemovalOrderDetailData.forEach(a -> {
                a.setAccountNumber(accountMap.get("account"));
                a.setMarketPlace(accountMap.get("marketPlace"));
                a.setMerchantId(amazonAccount.getMerchantId());
                a.setCreationDate(new Timestamp(System.currentTimeMillis()));
                a.setLastUpdateDate(new Timestamp(System.currentTimeMillis()));
                if (StringUtil.isNotBlank(a.getReceivedDateStr())) {
                    a.setReceivedDate(new Timestamp(DateUtils.convertDateToLocalDate(a.getReceivedDateStr()).getTime()));
                }
                // 将产品名字中包含,的字符去掉
                if (StringUtil.isNotBlank(a.getProductName())) {
                    a.setProductName(a.getProductName().replaceAll("[%&',;=?$\\x22]+",""));
                }
            });
            // 新增或修改
            amazonFbaFulfillmentInventoryReceiptsDataDao.batchInsertOrUpdateAmazonFbaFulfillmentInventoryReceiptsData(amazonFbaFulfillmentRemovalOrderDetailData);
        }catch (Exception e){
            log.error(amazonAccount.getAccountNumber()+"解析文件或者插入数据库失败_"+e.getMessage());
        }finally {
            FileUtils.delete(file);
        }
    }
}
