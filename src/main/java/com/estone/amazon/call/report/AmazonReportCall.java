package com.estone.amazon.call.report;

import com.estone.amazon.model.AmazonAccount;
import com.estone.amazon.model.bo.AmazonReportsDownloadProgress;

import java.io.File;

/**
 * @description: TODO
 * <AUTHOR>
 * @date 2021/9/27 11:56
 */
public interface AmazonReportCall {
    /**
     * 获取报告
     */
    void getReport();
    /**
     * 解析新版sp亚马逊服务
     */
    void parseNewAmazonSpLocal(File file, AmazonReportsDownloadProgress amazonReportsDownloadProgress);

    void parseFile(File file, AmazonAccount amazonAccount);
}
