package com.estone.scan.weight.dao;

import java.util.List;

import com.estone.scan.weight.bean.FailedScan;
import com.estone.scan.weight.bean.FailedScanQueryCondition;
import com.whq.tool.component.Pager;

public interface FailedScanDao {
    /**
     * This method corresponds to the database table failed_scan
     *
     * @mbggenerated Mon Oct 22 14:48:17 CST 2018
     */
    int queryFailedScanCount(FailedScanQueryCondition query);

    /**
     * This method corresponds to the database table failed_scan
     *
     * @mbggenerated Mon Oct 22 14:48:17 CST 2018
     */
    List<FailedScan> queryFailedScanList();

    /**
     * This method corresponds to the database table failed_scan
     *
     * @mbggenerated Mon Oct 22 14:48:17 CST 2018
     */
    List<FailedScan> queryFailedScanList(FailedScanQueryCondition query, Pager pager);

    /**
     * This method corresponds to the database table failed_scan
     *
     * @mbggenerated Mon Oct 22 14:48:17 CST 2018
     */
    FailedScan queryFailedScan(Integer primaryKey);

    /**
     * This method corresponds to the database table failed_scan
     *
     * @mbggenerated Mon Oct 22 14:48:17 CST 2018
     */
    FailedScan queryFailedScan(FailedScanQueryCondition query);

    /**
     * This method corresponds to the database table failed_scan
     *
     * @mbggenerated Mon Oct 22 14:48:17 CST 2018
     */
    void createFailedScan(FailedScan entity);

    /**
     * This method corresponds to the database table failed_scan
     *
     * @mbggenerated Mon Oct 22 14:48:17 CST 2018
     */
    void batchCreateFailedScan(List<FailedScan> entityList);

    /**
     * This method corresponds to the database table failed_scan
     *
     * @mbggenerated Mon Oct 22 14:48:17 CST 2018
     */
    void batchUpdateFailedScan(List<FailedScan> entityList);

    /**
     * This method corresponds to the database table failed_scan
     *
     * @mbggenerated Mon Oct 22 14:48:17 CST 2018
     */
    void deleteFailedScan(Integer primaryKey);

    /**
     * This method corresponds to the database table failed_scan
     *
     * @mbggenerated Mon Oct 22 14:48:17 CST 2018
     */
    void updateFailedScan(FailedScan entity);

    List<FailedScan> queryNeedToRetryFailedScanList();

}