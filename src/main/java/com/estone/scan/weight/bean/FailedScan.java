package com.estone.scan.weight.bean;

import java.io.Serializable;
import java.sql.Timestamp;

public class FailedScan implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     * This field corresponds to the database column failed_scan.id
     *
     * @mbggenerated Mon Oct 22 14:48:17 CST 2018
     */
    private Integer id;

    /**
     * 消息
     * This field corresponds to the database column failed_scan.message
     *
     * @mbggenerated Mon Oct 22 14:48:17 CST 2018
     */
    private String message;

    /**
     * WMS是否成功
     * This field corresponds to the database column failed_scan.wms_success
     *
     * @mbggenerated Mon Oct 22 14:48:17 CST 2018
     */
    private Boolean wmsSuccess;

    /**
     * PMS是否成功
     * This field corresponds to the database column failed_scan.pms_success
     *
     * @mbggenerated Mon Oct 22 14:48:17 CST 2018
     */
    private Boolean pmsSuccess;

    /**
     * 重试次数
     * This field corresponds to the database column failed_scan.repeat_number
     *
     * @mbggenerated Mon Oct 22 14:48:17 CST 2018
     */
    private Integer repeatNumber;

    /**
     * 创建人
     * This field corresponds to the database column failed_scan.created_by
     *
     * @mbggenerated Mon Oct 22 14:48:17 CST 2018
     */
    private Integer createdBy;

    /**
     * 创建时间
     * This field corresponds to the database column failed_scan.creation_date
     *
     * @mbggenerated Mon Oct 22 14:48:17 CST 2018
     */
    private Timestamp creationDate;

    
    /**
	 * @param message
	 * @param wmsSuccess
	 * @param pmsSuccess
	 * @param repeatNumber
	 */
	public FailedScan(String message, Boolean wmsSuccess, Boolean pmsSuccess, Integer repeatNumber) {
		super();
		this.message = message;
		this.wmsSuccess = wmsSuccess;
		this.pmsSuccess = pmsSuccess;
		this.repeatNumber = repeatNumber;
	}

	public FailedScan() {
		super();
	}



	public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public Boolean getWmsSuccess() {
        return wmsSuccess;
    }

    public void setWmsSuccess(Boolean wmsSuccess) {
        this.wmsSuccess = wmsSuccess;
    }

    public Boolean getPmsSuccess() {
        return pmsSuccess;
    }

    public void setPmsSuccess(Boolean pmsSuccess) {
        this.pmsSuccess = pmsSuccess;
    }

    public Integer getRepeatNumber() {
        return repeatNumber;
    }

    public void setRepeatNumber(Integer repeatNumber) {
        this.repeatNumber = repeatNumber;
    }

    public Integer getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(Integer createdBy) {
        this.createdBy = createdBy;
    }

    public Timestamp getCreationDate() {
        return creationDate;
    }

    public void setCreationDate(Timestamp creationDate) {
        this.creationDate = creationDate;
    }

	@Override
	public String toString() {
		return "FailedScan [id=" + id + ", message=" + message + ", wmsSuccess=" + wmsSuccess + ", pmsSuccess="
				+ pmsSuccess + ", repeatNumber=" + repeatNumber + ", createdBy=" + createdBy + ", creationDate="
				+ creationDate + "]";
	}
    
}