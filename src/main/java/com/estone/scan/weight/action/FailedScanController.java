package com.estone.scan.weight.action;

import java.util.List;

import javax.annotation.Resource;
import javax.servlet.http.HttpSession;

import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import com.estone.scan.weight.bean.FailedScan;
import com.estone.scan.weight.bean.FailedScanQueryCondition;
import com.estone.scan.weight.domain.FailedScanDo;
import com.estone.scan.weight.service.FailedScanService;
import com.whq.tool.action.BaseController;
import com.whq.tool.component.Pager;
import com.whq.tool.json.ResponseJson;

@Controller
@RequestMapping(value = "scan/failedScan")
public class FailedScanController extends BaseController {
    @Resource
    private FailedScanService failedScanService;

    @RequestMapping(method = { RequestMethod.GET })
    public String init(@ModelAttribute("domain") FailedScanDo domain) {
        return "{模块}/{页面}";
    }

    private void initFormData(@ModelAttribute("domain") FailedScanDo domain) {

    }

    private void queryFailedScans(@ModelAttribute("domain") FailedScanDo domain) {
        FailedScanQueryCondition query = domain.getQuery();
        Pager page = domain.getPage();
        if (query == null) {
            query = new FailedScanQueryCondition();
            domain.setQuery(query);
        }
        List<FailedScan> failedScans = failedScanService.queryFailedScans(query, page);
        domain.setFailedScans(failedScans);
    }

    @RequestMapping(value = "search", method = { RequestMethod.POST })
    public String search(@ModelAttribute("domain") FailedScanDo domain) {
        initFormData(domain);
        queryFailedScans(domain);
        return "{模块}/{页面}";
    }

    @RequestMapping(value = "create", method = { RequestMethod.GET })
    public String toCreateFailedScan(@ModelAttribute("domain") FailedScanDo domain) {
        return "{模块}/{页面}";
    }

    @RequestMapping(value = "create", method = { RequestMethod.POST })
    public String createFailedScan(@ModelAttribute("domain") FailedScanDo domain, HttpSession session) {
        FailedScan failedScan = domain.getFailedScan();
        failedScanService.createFailedScan(failedScan);
        return "redirect:/{查询列表}";
    }

    @RequestMapping(value = "update", method = { RequestMethod.GET })
    public String toUpdateFailedScan(@ModelAttribute("domain") FailedScanDo domain,
            @RequestParam("failedScanId") Integer failedScanId) {
        FailedScan failedScan = failedScanService.getFailedScan(failedScanId);
        domain.setFailedScan(failedScan);
        return "{模块}/{页面}";
    }

    @RequestMapping(value = "update", method = { RequestMethod.POST })
    public String updateFailedScan(@ModelAttribute("domain") FailedScanDo domain, HttpSession session) {
        FailedScan failedScan = domain.getFailedScan();
        failedScanService.updateFailedScan(failedScan);
        return "redirect:/{查询列表}";
    }

    @RequestMapping(value = "delete", method = { RequestMethod.GET })
    @ResponseBody
    public ResponseJson deleteFailedScan(@ModelAttribute("domain") FailedScanDo domain,
            @RequestParam("failedScanId") Integer failedScanId) {
        ResponseJson response = new ResponseJson();
        failedScanService.deleteFailedScan(failedScanId);
        return response;
    }

}