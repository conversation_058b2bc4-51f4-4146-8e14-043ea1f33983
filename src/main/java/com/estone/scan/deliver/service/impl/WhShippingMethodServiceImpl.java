package com.estone.scan.deliver.service.impl;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import com.estone.scan.deliver.bean.*;
import com.estone.scan.deliver.service.WhCollectCompanyService;
import com.estone.scan.deliver.service.WhDeliveryCompanyService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import com.estone.scan.deliver.dao.WhShippingMethodDao;
import com.estone.scan.deliver.service.WhShippingMethodService;
import com.whq.tool.component.Pager;
import com.whq.tool.exception.BusinessException;
import com.whq.tool.exception.DBErrorCode;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import com.whq.tool.sqler.SqlerException;

@Service("whShippingMethodService")
public class WhShippingMethodServiceImpl implements WhShippingMethodService {
    private static final Logger logger = LoggerFactory.getLogger(WhShippingMethodServiceImpl.class);
    
    @Resource
    private WhShippingMethodDao whShippingMethodDao;

    @Resource
    private WhDeliveryCompanyService whDeliveryCompanyService;

        @Resource
    private WhCollectCompanyService whCollectCompanyService;



    /**
     * This method corresponds to the database table whshippingmethod
     *
     * @mbggenerated Thu Apr 04 10:15:36 CST 2019
     */
    public WhShippingMethod getWhShippingMethod(Integer id) {
        WhShippingMethod whShippingMethod = whShippingMethodDao.queryWhShippingMethod(id);
        return whShippingMethod;
    }

    /**
     * This method corresponds to the database table whshippingmethod
     *
     * @mbggenerated Thu Apr 04 10:15:36 CST 2019
     */
    public WhShippingMethod getWhShippingMethodDetail(Integer id) {
        WhShippingMethod whShippingMethod = whShippingMethodDao.queryWhShippingMethod(id);
        // 关联查询
        return whShippingMethod;
    }

    /**
     * This method corresponds to the database table whshippingmethod
     *
     * @mbggenerated Thu Apr 04 10:15:36 CST 2019
     */
    public WhShippingMethod queryWhShippingMethod(WhShippingMethodQueryCondition query) {
        Assert.notNull(query);
        WhShippingMethod whShippingMethod = whShippingMethodDao.queryWhShippingMethod(query);
        return whShippingMethod;
    }

    /**
     * This method corresponds to the database table whshippingmethod
     *
     * @mbggenerated Thu Apr 04 10:15:36 CST 2019
     */
    @Cacheable(value = "LOGISTICS_CACHE")
    public List<WhShippingMethod> queryAllWhShippingMethods() {
        return whShippingMethodDao.queryWhShippingMethodList();
    }

    /**
     * This method corresponds to the database table whshippingmethod
     *
     * @mbggenerated Thu Apr 04 10:15:36 CST 2019
     */
    public List<WhShippingMethod> queryWhShippingMethods(WhShippingMethodQueryCondition query, Pager pager) {
        Assert.notNull(query);
        if (pager != null && pager.isQueryCount())
        {
            int count = whShippingMethodDao.queryWhShippingMethodCount(query);
            pager.setTotalCount(count);
            if ( count == 0)
            {
                return new ArrayList<WhShippingMethod>();
            }
        }
        List<WhShippingMethod> whShippingMethods = whShippingMethodDao.queryWhShippingMethodList(query, pager);
        return whShippingMethods;
    }

    /**
     * This method corresponds to the database table whshippingmethod
     *
     * @mbggenerated Thu Apr 04 10:15:36 CST 2019
     */
    public void createWhShippingMethod(WhShippingMethod whShippingMethod) {
        try
        {
            whShippingMethodDao.createWhShippingMethod(whShippingMethod);
        }
        catch (SqlerException e)
        {
            logger.error(e.getMessage(), e);
            throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
        }
    }

    /**
     * This method corresponds to the database table whshippingmethod
     *
     * @mbggenerated Thu Apr 04 10:15:36 CST 2019
     */
    public void batchCreateWhShippingMethod(List<WhShippingMethod> entityList) {
        if (CollectionUtils.isNotEmpty(entityList))
        {
            try
            {
                whShippingMethodDao.batchCreateWhShippingMethod(entityList);
            }
            catch (SqlerException e)
            {
                logger.error(e.getMessage(), e);
                throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
            }
        }
    }

    /**
     * This method corresponds to the database table whshippingmethod
     *
     * @mbggenerated Thu Apr 04 10:15:36 CST 2019
     */
    public void deleteWhShippingMethod(Integer id) {
        try
        {
            whShippingMethodDao.deleteWhShippingMethod(id);
        }
        catch (SqlerException e)
        {
            logger.error(e.getMessage(), e);
            throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
        }
    }

    /**
     * This method corresponds to the database table whshippingmethod
     *
     * @mbggenerated Thu Apr 04 10:15:36 CST 2019
     */
    public void updateWhShippingMethod(WhShippingMethod whShippingMethod) {
        try
        {
            whShippingMethodDao.updateWhShippingMethod(whShippingMethod);
        }
        catch (SqlerException e)
        {
            logger.error(e.getMessage(), e);
            throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
        }
    }

    /**
     * This method corresponds to the database table whshippingmethod
     *
     * @mbggenerated Thu Apr 04 10:15:36 CST 2019
     */
    public void batchUpdateWhShippingMethod(List<WhShippingMethod> entityList) {
        if (CollectionUtils.isNotEmpty(entityList))
        {
            try
            {
                whShippingMethodDao.batchUpdateWhShippingMethod(entityList);
            }
            catch (SqlerException e)
            {
                logger.error(e.getMessage(), e);
                throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
            }
        }
    }
    
    public List<WhShippingMethod> queryWhShippingMethodDetailsList(WhShippingMethodQueryCondition query, Pager pager) {
        Assert.notNull(query);
        if (pager != null && pager.isQueryCount())
        {
            int count = whShippingMethodDao.queryWhShippingMethodDetailCount(query);
            pager.setTotalCount(count);
            if ( count == 0)
            {
                return new ArrayList<WhShippingMethod>();
            }
        }
        List<WhShippingMethod> whShippingMethods = whShippingMethodDao.queryWhShippingMethodDetailList(query, pager);
        return whShippingMethods;
    }

    @Cacheable(value = "LOGISTICS_CACHE", key = "#code")
    public WhShippingMethod getWhShippingMethodDetailByShippingMethodCode(String code){
        WhShippingMethod whShippingMethod = null;
        WhShippingMethodQueryCondition methodQuery = new WhShippingMethodQueryCondition();
        methodQuery.setCode(code);
        List<WhShippingMethod> result = this.queryWhShippingMethodDetailsList(methodQuery, null);
        if (CollectionUtils.isNotEmpty(result)) {
            whShippingMethod = result.get(0);
        }
        return whShippingMethod;
    }
    
    @CacheEvict(value = "LOGISTICS_CACHE", allEntries = true)
    public void pushWhShippingMethods(List<WhShippingMethod> shippingMethods){
        List<WhShippingMethod> existList = queryAllWhShippingMethods();
        List<WhShippingMethod> createList = new ArrayList<>();
        List<WhShippingMethod> updateList = new ArrayList<>();
        Map<Long, WhShippingMethod> map = new HashMap<>();
        if (CollectionUtils.isNotEmpty(existList)) {
            for (WhShippingMethod method : existList) {
                map.put(method.getId(), method);
            }
            logger.info("pushWhShippingMethods size:"+existList.size()+", existList:" + existList);
        }
        
        for (WhShippingMethod shippingMethod : shippingMethods) {
            WhShippingMethod exist = map.get(shippingMethod.getId());
            if (exist != null) {
                updateList.add(shippingMethod);
            }else {
                createList.add(shippingMethod);
            }
        }
        if (CollectionUtils.isNotEmpty(updateList)) {
            this.batchUpdateWhShippingMethod(updateList);
            logger.info("pushWhShippingMethods size:"+updateList.size()+", updateList:" + updateList);
        }
        if (CollectionUtils.isNotEmpty(createList)) {
            this.batchCreateWhShippingMethod(createList);
            logger.info("pushWhShippingMethods size:"+createList.size()+", createList:" + createList);
        }
    }
    
    @CacheEvict(value = "LOGISTICS_CACHE", allEntries = true)
    public void deleteWhShippingMethods(List<WhShippingMethod> shippingMethods){
        if (CollectionUtils.isNotEmpty(shippingMethods)) {
            for (WhShippingMethod shippingMethod : shippingMethods) {
                this.deleteWhShippingMethod(Integer.valueOf(shippingMethod.getId().toString()));
                logger.info("deleteWhShippingMethods id:"+shippingMethod.getId());
            }
        }
    }
    
    public WhShippingMethod getWhShippingMethodDetailByDeliveryCompanyCode(String deliveryCompanyCode){
        WhShippingMethod result = null;
        WhShippingMethodQueryCondition query = new WhShippingMethodQueryCondition();
        query.setDeliveryCompanyCode(deliveryCompanyCode);
        List<WhShippingMethod> shippingMethods = this.queryWhShippingMethodDetailsList(query, null);
        if (CollectionUtils.isNotEmpty(shippingMethods)) {
            result = shippingMethods.get(0);
        }
        return result;
    }

    @Override
    public WhDeliveryCompany getWhDeliveryCompanyDetailByDeliveryCompanyCode(String deliveryCompanyCode){
        if (StringUtils.isBlank(deliveryCompanyCode)){
            return null;
        }
        WhDeliveryCompany result = null;
        WhDeliveryCompanyQueryCondition query = new WhDeliveryCompanyQueryCondition();
        query.setCode(deliveryCompanyCode);
        List<WhDeliveryCompany> list = whDeliveryCompanyService.queryWhDeliveryCompanys(query, null);
        if (CollectionUtils.isNotEmpty(list)) {
            result = list.get(0);
            if (result.getCollectcompanyid() != null){
                result.setWhCollectCompany(whCollectCompanyService.getWhCollectCompany(Integer.valueOf(result.getCollectcompanyid().toString())));
            }
        }
        return result;
    }
}