package com.estone.scan.deliver.service.impl;

import com.estone.apv.bean.WhApv;
import com.estone.apv.bean.WhApvItem;
import com.estone.common.util.CacheUtils;
import com.estone.scan.deliver.service.IDeliverCheckWeight;
import com.estone.scan.deliver.util.DeliverOrderUtils;
import com.estone.sku.bean.WhSku;
import com.estone.system.standardweight.bean.StandardWeightProportion;
import com.estone.system.standardweight.util.NonStandardWeightProportionUtils;
import com.estone.system.standardweight.util.StandardWeightProportionUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @Description 单品单件拦截逻辑
 * @date 2020/8/28 12:05
 */
@Slf4j
@Service("ssOrderDeliverCheckWeightHelper")
public class SsOrderDeliverCheckWeightHelper implements IDeliverCheckWeight {

    @Override
    public String checkStandardWeight(WhApv apv, Double actualWeight) {
        // 交运环节增加重量拦截， 对 | 称重重量 - 标准重量 | 在范围内的且称重重量>=单品净重量的允许交运通过；
        // 超出对应区间范围的，交运拦截包裹，提示： 重量误差超出允许范围，请确认！
        // 在对应区间范围内，称重重量<单品净重拦截的提示：重量低于单品净重，请确认!
        // 新品前10个包裹重量与SKU净重对比，>=SKU净重的允许通过，否则拦截并提示：重量低于SKU净重；
        // SKU增加是否是规则产品， 规则产品走拦截规则， 不规则产品判断>=SKU净重才允许通过
        // 1. 扫描小软件界面增加 [强制通过] 按钮,对拦截的包裹, 点击强制通过后, 重新称重后允许通过 ;
        // 2.强制通过时需要判断: 重量误差 ( | 称重重量 - 标准重量 | / 称重重量 )超过100%的不允许强制通过；

        // 2019-5-10 WMS-754 规则产品走拦截规则，判断：
        // 1、包裹中不包含不规则SKU = 通过
        // 2、包裹重量 > 规则SKU重量 = 通过
        // 3、包裹重量 <= 规则SKU重量 = 拦截
        String result = null;
        apv.setActualWeight(actualWeight);
        apv.calculationStandardWeight();// 计算标准重量
        actualWeight = apv.getActualWeight();// 称重重量
        Double skuWeightCount = apv.getSkuWeightCount();// 商品净重
        Double weightDiffPercentage = apv.getWeightDiffPercentage();// 重量差百分比
        Double skuStandardWeightCount = apv.getSkuStandardWeightCount(); // 标准重量
        Double skuStandardWeightDiff = apv.getSkuStandardWeightDiff();// 重量差

        // 是否为规则产品 1：全部为规则,2：全部为不规则,3：一些为规则 一些为不规则
        int isSpeckfication = apv.getIsSpeckfication();
        Double speckficationCount = apv.getSpeckficationCount();// SKU规则商品重量
        String isSkuWeight = CacheUtils.SystemParamGet("SWITCH.DELIVER_INTERCEPT").getParamValue();
        Integer type = apv.getWeightProportionType();// 标准重量配置

        if (skuWeightCount == null || skuWeightCount == 0) {
            result = "checkStandardWeight|notAllowedForce=商品净重为0，请确认!";
            return result;
        }

        StandardWeightProportion proportion;
        if (isSpeckfication == 1) {
            // 规则
            proportion = StandardWeightProportionUtils.getStandardWeightProportion(skuStandardWeightCount, type);
        }else{
            List<WhApvItem> items = apv.getWhApvItems();
            if (CollectionUtils.isNotEmpty(items) && items.get(0).getWhSku() != null
                    && (items.get(0).getWhSku().getNetWeight() == null || items.get(0).getWhSku().getNetWeight() == 0)){
                // 不规则SKU且无标准重量
                return DeliverOrderUtils.checkNonStandardWeight(apv, actualWeight);
            }
            // 全部不规则或部分不规则
            proportion = NonStandardWeightProportionUtils.getStandardWeightProportion(skuStandardWeightCount, type);
        }
        if (proportion == null) {
            return null;
        }
        boolean boolRight = false;
        if (skuStandardWeightDiff > 0 && skuStandardWeightDiff > proportion.getLeftDiff()){
            boolRight = true;
        }
        if (skuStandardWeightDiff < 0 && Math.abs(skuStandardWeightDiff) > proportion.getRightDiff()){
            boolRight = true;
        }
        if (boolRight) {
            if (weightDiffPercentage > 1) {
                result = "checkStandardWeight|notAllowedForce=重量误差超出允许范围，请确认!";
            } else {
                result = "checkStandardWeight|" + apv.getApvNo() + "=重量误差超出允许范围，请确认!";
            }
        } else if (actualWeight < (skuWeightCount - 5) && StringUtils.isNotBlank(isSkuWeight)) {
            // 在允许范围内
            result = "checkStandardWeight=重量低于(SKU净重-5g)，请确认!";
        }
        return result;
    }
}
