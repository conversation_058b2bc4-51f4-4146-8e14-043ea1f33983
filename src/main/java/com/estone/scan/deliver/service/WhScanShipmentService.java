package com.estone.scan.deliver.service;

import com.estone.common.util.model.ApiResult;
import com.estone.scan.deliver.bean.*;
import com.whq.tool.component.Pager;
import com.whq.tool.json.ResponseJson;

import java.util.List;
import java.util.Map;

public interface WhScanShipmentService {
    /**
     * This method corresponds to the database table wh_scan_shipment
     *
     * @mbggenerated Thu Dec 06 18:17:42 CST 2018
     */
    List<WhScanShipment> queryAllWhScanShipments();

    /**
     * This method corresponds to the database table wh_scan_shipment
     *
     * @mbggenerated Thu Dec 06 18:17:42 CST 2018
     */
    List<WhScanShipment> queryWhScanShipments(WhScanShipmentQueryCondition query, Pager pager);

    /**
     * This method corresponds to the database table wh_scan_shipment
     *
     * @mbggenerated Thu Dec 06 18:17:42 CST 2018
     */
    WhScanShipment getWhScanShipment(Integer id);

    /**
     * This method corresponds to the database table wh_scan_shipment
     *
     * @mbggenerated Thu Dec 06 18:17:42 CST 2018
     */
    WhScanShipment getWhScanShipmentDetail(Integer id);

    /**
     * This method corresponds to the database table wh_scan_shipment
     *
     * @mbggenerated Thu Dec 06 18:17:42 CST 2018
     */
    WhScanShipment queryWhScanShipment(WhScanShipmentQueryCondition query);

    /**
     * This method corresponds to the database table wh_scan_shipment
     *
     * @mbggenerated Thu Dec 06 18:17:42 CST 2018
     */
    void createWhScanShipment(WhScanShipment whScanShipment);

    /**
     * This method corresponds to the database table wh_scan_shipment
     *
     * @mbggenerated Thu Dec 06 18:17:42 CST 2018
     */
    void batchCreateWhScanShipment(List<WhScanShipment> entityList);

    /**
     * This method corresponds to the database table wh_scan_shipment
     *
     * @mbggenerated Thu Dec 06 18:17:42 CST 2018
     */
    void deleteWhScanShipment(Integer id);

    /**
     * This method corresponds to the database table wh_scan_shipment
     *
     * @mbggenerated Thu Dec 06 18:17:42 CST 2018
     */
    void updateWhScanShipment(WhScanShipment whScanShipment);

    /**
     * This method corresponds to the database table wh_scan_shipment
     *
     * @mbggenerated Thu Dec 06 18:17:42 CST 2018
     */
    void batchUpdateWhScanShipment(List<WhScanShipment> entityList);

    WhScanShipment getWhScanShipmentByBagNo(String bagNo);

    String generatePocketCard(DeliverDTO deliverDTO);

    Map<String,List<String>> doGeneratePocketCardLazada(DeliverDTO deliverDTO) throws Exception;

    /**
     * LAZADA揽收码
     * @param bagNo
     * @param deliverDTO
     * @return
     */
    String doGenerateLazadaLanshou(String bagNo, DeliverDTO deliverDTO);

    int undoIntoCar(Integer id);

    int downloadDetailCount(WhScanShipmentQueryCondition query);

    List<Map<String, Object>> downloadDetail(WhScanShipmentQueryCondition query,Pager pager);

    boolean pushScanShipmentToTmsById(Integer scanShipmentId);

    ApiResult pushScanShipmentToTms(ScanShipment2TmsDTO scanShipmentDto);

    ApiResult pushScanDeliverOrdersToTms(List<DeliverOrder2TmsDTO> deliverOrders);

    List<Map<String, Object>> queryFailedPushToTmsIds(String startDate);

    WhScanShipment queryWhScanShipmentsGroup(WhScanShipmentQueryCondition query);

    List<String> queryShopeeApvNos(Integer packCarId);

    boolean discard(Integer id);

    List<String> getLanShouTag(List<String> apvList);

    List<String> getCaiNiaoLanShouTag(DeliverDTO deliverDTO);

    ResponseJson syncCaiNiaoLanShouTag(Integer id);

    ResponseJson doApplyLanShouTag(Integer packCarId, List<String> apvList);

    List<Map<String, Object>> queryNoPackBagNos(WhScanShipmentQueryCondition query);

    /**
     * 根据装车id查询所有的发货单号
     * @param packCarId
     * @return java.util.List<java.lang.String>
     * <AUTHOR>
     * @date 2022/1/18 18:29
     */
    List<String> queryYstnListByPackCarId(Integer packCarId);

    /**
     * 推送结袋卡数到TMS
     * @param id
     */
    void pushShipmentToTms(Integer id);

    /**
     * 消息队列推送结袋卡数据到TMS
     */
    void pushTmsShipmentInfo(ScanShipment2TmsDTO scanShipmentDto,List<DeliverOrder2TmsDTO> deliverOrders, WhScanShipment scanShipment);

    ResponseJson importLoadInfo(List<String> list);

    /**
     * 用于组装得到结袋卡中相关箱子信息列表
     *
     * @param id        结袋卡订单ID
     * @param printType 操作类型。0为展示，1为打印
     * @return 列表集合
     */
    List<PackCardApvBoxDetailRecordVO> generatePackCardRecordList(Integer id, Integer printType);

    String generateTransferBagCard(DeliverDTO deliverDTO);

    /**
     * 生成中转仓揽收单
     *全托管传null ,半托管 是否打印A4  true-打印A4    false-打印100*100
     */
    String generatePickupShippingMarkPdf(DeliverDTO deliverDTO,Boolean isPrintA4) throws Exception;

    /**
     * 生成本地仓JIT揽收单
     * 全托管传null ,半托管 是否打印A4  true-打印A4    false-打印100*100
     */
    String generateJitShippingMarkPdf(DeliverDTO deliverDTO,Boolean isPrintA4) throws Exception;

    /**
     * 生成拼多多结袋卡信息
     * @param deliverDTO
     * @return
     */
    String generateTemuBagCard(DeliverDTO deliverDTO);

    /**
     * 生成海外仓结袋卡信息
     */
    WhScanShipment generateAsnBagCard(DeliverDTO deliverDTO);

    /**
     * 加运美下单
     */
    ResponseJson generateJymExpress(String receiveHouseId, List<WhScanShipment> scanShipmentList);

    ResponseJson jymExpressValidator(WhScanShipment scanShipment, String expressOrderNo);

    ResponseJson kyeExpressValidator(WhScanShipment scanShipment, String expressOrderNo);

    /**
     * 跨越下单
     */
    ResponseJson generateKyExpress(String receiveHouseId, List<WhScanShipment> scanShipmentList);

    void deleteWhScanShipmentAndApv(Integer scanShipmentId);
}