package com.estone.scan.deliver.service.impl;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import com.estone.scan.deliver.bean.WhDeliveryCompany;
import com.estone.scan.deliver.bean.WhDeliveryCompanyQueryCondition;
import com.estone.scan.deliver.dao.WhCollectCompanyDao;
import com.estone.scan.deliver.dao.WhDeliveryCompanyDao;
import com.estone.scan.deliver.service.WhDeliveryCompanyService;
import com.whq.tool.component.Pager;
import com.whq.tool.exception.BusinessException;
import com.whq.tool.exception.DBErrorCode;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import com.whq.tool.sqler.SqlerException;

@Service("whDeliveryCompanyService")
public class WhDeliveryCompanyServiceImpl implements WhDeliveryCompanyService {
    private static final Logger logger = LoggerFactory.getLogger(WhDeliveryCompanyServiceImpl.class);

    @Resource
    private WhDeliveryCompanyDao whDeliveryCompanyDao;
    
    @Resource
    private WhCollectCompanyDao whCollectCompanyDao;

    /**
     * This method corresponds to the database table whdeliverycompany
     *
     * @mbggenerated Thu Apr 04 10:41:47 CST 2019
     */
    public WhDeliveryCompany getWhDeliveryCompany(Integer id) {
        WhDeliveryCompany whDeliveryCompany = whDeliveryCompanyDao.queryWhDeliveryCompany(id);
        return whDeliveryCompany;
    }

    /**
     * This method corresponds to the database table whdeliverycompany
     *
     * @mbggenerated Thu Apr 04 10:41:47 CST 2019
     */
    public WhDeliveryCompany getWhDeliveryCompanyDetail(Integer id) {
        WhDeliveryCompany whDeliveryCompany = whDeliveryCompanyDao.queryWhDeliveryCompany(id);
        // 关联查询
        return whDeliveryCompany;
    }

    /**
     * This method corresponds to the database table whdeliverycompany
     *
     * @mbggenerated Thu Apr 04 10:41:47 CST 2019
     */
    public WhDeliveryCompany queryWhDeliveryCompany(WhDeliveryCompanyQueryCondition query) {
        Assert.notNull(query);
        WhDeliveryCompany whDeliveryCompany = whDeliveryCompanyDao.queryWhDeliveryCompany(query);
        return whDeliveryCompany;
    }

    /**
     * This method corresponds to the database table whdeliverycompany
     *
     * @mbggenerated Thu Apr 04 10:41:47 CST 2019
     */
    public List<WhDeliveryCompany> queryAllWhDeliveryCompanys() {
        return whDeliveryCompanyDao.queryWhDeliveryCompanyList();
    }

    /**
     * This method corresponds to the database table whdeliverycompany
     *
     * @mbggenerated Thu Apr 04 10:41:47 CST 2019
     */
    public List<WhDeliveryCompany> queryWhDeliveryCompanys(WhDeliveryCompanyQueryCondition query, Pager pager) {
        Assert.notNull(query);
        if (pager != null && pager.isQueryCount())
        {
            int count = whDeliveryCompanyDao.queryWhDeliveryCompanyCount(query);
            pager.setTotalCount(count);
            if ( count == 0)
            {
                return new ArrayList<WhDeliveryCompany>();
            }
        }
        List<WhDeliveryCompany> whDeliveryCompanys = whDeliveryCompanyDao.queryWhDeliveryCompanyList(query, pager);
        return whDeliveryCompanys;
    }

    /**
     * This method corresponds to the database table whdeliverycompany
     *
     * @mbggenerated Thu Apr 04 10:41:47 CST 2019
     */
    public void createWhDeliveryCompany(WhDeliveryCompany whDeliveryCompany) {
        try
        {
            whDeliveryCompanyDao.createWhDeliveryCompany(whDeliveryCompany);
        }
        catch (SqlerException e)
        {
            logger.error(e.getMessage(), e);
            throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
        }
    }

    /**
     * This method corresponds to the database table whdeliverycompany
     *
     * @mbggenerated Thu Apr 04 10:41:47 CST 2019
     */
    public void batchCreateWhDeliveryCompany(List<WhDeliveryCompany> entityList) {
        if (CollectionUtils.isNotEmpty(entityList))
        {
            try
            {
                whDeliveryCompanyDao.batchCreateWhDeliveryCompany(entityList);
            }
            catch (SqlerException e)
            {
                logger.error(e.getMessage(), e);
                throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
            }
        }
    }

    /**
     * This method corresponds to the database table whdeliverycompany
     *
     * @mbggenerated Thu Apr 04 10:41:47 CST 2019
     */
    public void deleteWhDeliveryCompany(Integer id) {
        try
        {
            whDeliveryCompanyDao.deleteWhDeliveryCompany(id);
        }
        catch (SqlerException e)
        {
            logger.error(e.getMessage(), e);
            throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
        }
    }

    /**
     * This method corresponds to the database table whdeliverycompany
     *
     * @mbggenerated Thu Apr 04 10:41:47 CST 2019
     */
    public void updateWhDeliveryCompany(WhDeliveryCompany whDeliveryCompany) {
        try
        {
            whDeliveryCompanyDao.updateWhDeliveryCompany(whDeliveryCompany);
        }
        catch (SqlerException e)
        {
            logger.error(e.getMessage(), e);
            throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
        }
    }

    /**
     * This method corresponds to the database table whdeliverycompany
     *
     * @mbggenerated Thu Apr 04 10:41:47 CST 2019
     */
    public void batchUpdateWhDeliveryCompany(List<WhDeliveryCompany> entityList) {
        if (CollectionUtils.isNotEmpty(entityList))
        {
            try
            {
                whDeliveryCompanyDao.batchUpdateWhDeliveryCompany(entityList);
            }
            catch (SqlerException e)
            {
                logger.error(e.getMessage(), e);
                throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
            }
        }
    }
    
    public void pushWhDeliveryCompanys(List<WhDeliveryCompany> deliveryCompanys){
        List<WhDeliveryCompany> existList = queryAllWhDeliveryCompanys();
        List<WhDeliveryCompany> createList = new ArrayList<>();
        List<WhDeliveryCompany> updateList = new ArrayList<>();
        Map<Long, WhDeliveryCompany> map = new HashMap<>();
        if (CollectionUtils.isNotEmpty(existList)) {
            for (WhDeliveryCompany company : existList) {
                map.put(company.getId(), company);
            }
            logger.info("pushWhDeliveryCompanys size:"+existList.size()+", existList:" + existList);
        }
        
        for (WhDeliveryCompany deliveryCompany : deliveryCompanys) {
            WhDeliveryCompany exist = map.get(deliveryCompany.getId());
            if (exist != null) {
                updateList.add(deliveryCompany);
            }else {
                createList.add(deliveryCompany);
            }
        }
        if (CollectionUtils.isNotEmpty(updateList)) {
            this.batchUpdateWhDeliveryCompany(updateList);
            logger.info("pushWhDeliveryCompanys size:"+updateList.size()+", updateList:" + updateList);
        }
        if (CollectionUtils.isNotEmpty(createList)) {
            this.batchCreateWhDeliveryCompany(createList);
            logger.info("pushWhDeliveryCompanys size:"+createList.size()+", createList:" + createList);
        }
    }
    
    public void deleteWhDeliveryCompanys(List<WhDeliveryCompany> deliveryCompanys){
        if (CollectionUtils.isNotEmpty(deliveryCompanys)) {
            for (WhDeliveryCompany deliveryCompany : deliveryCompanys) {
                this.deleteWhDeliveryCompany(Integer.valueOf(deliveryCompany.getId().toString()));
                logger.info("deleteWhDeliveryCompanys id:"+deliveryCompany.getId());
            }
        }
    }
}