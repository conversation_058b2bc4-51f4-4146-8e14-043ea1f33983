package com.estone.scan.deliver.enums;

/**
 * 装车类型
 */
public enum LoadTypeEnum {

    LOCAL("本地仓装车", 0),

    TRANSFER("中转仓装车", 1),

    ;

    private Integer code;

    private String name;

    private LoadTypeEnum(String name, Integer code) {
        this.name = name;
        this.code = code;
    }

    public Integer getCode() {
        return code;
    }

    public Integer intCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
