package com.estone.scan.deliver.response;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LazbaoBoxInfoData {

    private String boxNo; // 箱号

    private Integer length; // 长度(cm)

    private Integer width; // 宽度(cm)

    private Integer height; // 高度(cm)

    private Integer volume; // 体积(cm³)

    private Integer weight; // 重量(g)

    private String containerLoadTime; // 装柜时间

    private String packStatus; // 箱子状态

    private String packStatusValue; // 箱子状态值
}

