package com.estone.scan.deliver.dao;

import com.estone.scan.deliver.bean.WhShippingMethod;
import com.estone.scan.deliver.bean.WhShippingMethodQueryCondition;
import com.whq.tool.component.Pager;
import java.util.List;

public interface WhShippingMethodDao {
    /**
     * This method corresponds to the database table whshippingmethod
     *
     * @mbggenerated Thu Apr 04 10:15:36 CST 2019
     */
    int queryWhShippingMethodCount(WhShippingMethodQueryCondition query);

    /**
     * This method corresponds to the database table whshippingmethod
     *
     * @mbggenerated Thu Apr 04 10:15:36 CST 2019
     */
    List<WhShippingMethod> queryWhShippingMethodList();

    /**
     * This method corresponds to the database table whshippingmethod
     *
     * @mbggenerated Thu Apr 04 10:15:36 CST 2019
     */
    List<WhShippingMethod> queryWhShippingMethodList(WhShippingMethodQueryCondition query, Pager pager);

    /**
     * This method corresponds to the database table whshippingmethod
     *
     * @mbggenerated Thu Apr 04 10:15:36 CST 2019
     */
    WhShippingMethod queryWhShippingMethod(Integer primaryKey);

    /**
     * This method corresponds to the database table whshippingmethod
     *
     * @mbggenerated Thu Apr 04 10:15:36 CST 2019
     */
    WhShippingMethod queryWhShippingMethod(WhShippingMethodQueryCondition query);

    /**
     * This method corresponds to the database table whshippingmethod
     *
     * @mbggenerated Thu Apr 04 10:15:36 CST 2019
     */
    void createWhShippingMethod(WhShippingMethod entity);

    /**
     * This method corresponds to the database table whshippingmethod
     *
     * @mbggenerated Thu Apr 04 10:15:36 CST 2019
     */
    void batchCreateWhShippingMethod(List<WhShippingMethod> entityList);

    /**
     * This method corresponds to the database table whshippingmethod
     *
     * @mbggenerated Thu Apr 04 10:15:36 CST 2019
     */
    void batchUpdateWhShippingMethod(List<WhShippingMethod> entityList);

    /**
     * This method corresponds to the database table whshippingmethod
     *
     * @mbggenerated Thu Apr 04 10:15:36 CST 2019
     */
    void deleteWhShippingMethod(Integer primaryKey);

    /**
     * This method corresponds to the database table whshippingmethod
     *
     * @mbggenerated Thu Apr 04 10:15:36 CST 2019
     */
    void updateWhShippingMethod(WhShippingMethod entity);
    
    
    int queryWhShippingMethodDetailCount(WhShippingMethodQueryCondition query);

    List<WhShippingMethod> queryWhShippingMethodDetailList(WhShippingMethodQueryCondition query, Pager pager);
}