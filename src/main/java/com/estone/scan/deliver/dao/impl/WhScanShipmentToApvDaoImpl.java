package com.estone.scan.deliver.dao.impl;

import java.util.List;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Repository;
import org.springframework.util.Assert;

import com.estone.scan.deliver.bean.WhScanShipmentToApv;
import com.estone.scan.deliver.bean.WhScanShipmentToApvQueryCondition;
import com.estone.scan.deliver.dao.WhScanShipmentToApvDao;
import com.estone.scan.deliver.dao.mapper.WhScanShipmentToApvDBField;
import com.estone.scan.deliver.dao.mapper.WhScanShipmentToApvMapper;
import com.whq.tool.component.Pager;
import com.whq.tool.sqler.SqlerRequest;
import com.whq.tool.sqler.analyzer.DataType;
import com.estone.common.util.SqlerTemplate;

@Repository("whScanShipmentToApvDao")
public class WhScanShipmentToApvDaoImpl implements WhScanShipmentToApvDao {

    private void setQueryCondition(SqlerRequest request, WhScanShipmentToApvQueryCondition query) {
        if (query == null)
        {
            return;
        }
        // 添加查询条件
        // 搜索条件不允许出现空字符
        request.setAllowBlank(false);
        
        request.addDataParam(WhScanShipmentToApvDBField.ID, DataType.INT, query.getId());
        request.addDataParam(WhScanShipmentToApvDBField.SCAN_SHIPMENT_ID, DataType.INT, query.getScanShipmentId());
        request.addDataParam(WhScanShipmentToApvDBField.APV_NO, DataType.STRING, query.getApvNo());   
        request.addDataParam("apvNoList", DataType.STRING, query.getApvNoList());
        if (CollectionUtils.isNotEmpty(query.getScanShipmentIdList())) {
            request.addBatchDataParam("scanShipmentIdList", DataType.INT, query.getScanShipmentIdList());
        }
    }

    /**
     * This method corresponds to the database table wh_scan_shipment_to_apv
     *
     * @mbggenerated Mon Dec 24 13:59:34 CST 2018
     */
    public int queryWhScanShipmentToApvCount(WhScanShipmentToApvQueryCondition query) {
        SqlerRequest request = new SqlerRequest("queryWhScanShipmentToApvCount");
        setQueryCondition(request, query);
        return SqlerTemplate.queryForInt(request);
    }

    /**
     * This method corresponds to the database table wh_scan_shipment_to_apv
     *
     * @mbggenerated Mon Dec 24 13:59:34 CST 2018
     */
    public List<WhScanShipmentToApv> queryWhScanShipmentToApvList() {
        SqlerRequest request = new SqlerRequest("queryWhScanShipmentToApvList");
        return SqlerTemplate.query(request, new WhScanShipmentToApvMapper());
    }

    /**
     * This method corresponds to the database table wh_scan_shipment_to_apv
     *
     * @mbggenerated Mon Dec 24 13:59:34 CST 2018
     */
    public List<WhScanShipmentToApv> queryWhScanShipmentToApvList(WhScanShipmentToApvQueryCondition query, Pager pager) {
        SqlerRequest request = new SqlerRequest("queryWhScanShipmentToApvList");
        setQueryCondition(request, query);
        if(pager != null)
        {
            request.addFetch(pager.getPageNo(), pager.getPageSize());
        }
        return SqlerTemplate.query(request, new WhScanShipmentToApvMapper());
    }

    /**
     * This method corresponds to the database table wh_scan_shipment_to_apv
     *
     * @mbggenerated Mon Dec 24 13:59:34 CST 2018
     */
    public WhScanShipmentToApv queryWhScanShipmentToApv(Integer primaryKey) {
        Assert.notNull(primaryKey);
        SqlerRequest request = new SqlerRequest("queryWhScanShipmentToApvByPrimaryKey");
        request.addDataParam(WhScanShipmentToApvDBField.ID, DataType.INT, primaryKey);
        return SqlerTemplate.queryForObject(request, new WhScanShipmentToApvMapper());
    }

    /**
     * This method corresponds to the database table wh_scan_shipment_to_apv
     *
     * @mbggenerated Mon Dec 24 13:59:34 CST 2018
     */
    public WhScanShipmentToApv queryWhScanShipmentToApv(WhScanShipmentToApvQueryCondition query) {
        SqlerRequest request = new SqlerRequest("queryWhScanShipmentToApv");
        setQueryCondition(request, query);
        return SqlerTemplate.queryForObject(request, new WhScanShipmentToApvMapper());
    }

    /**
     * This method corresponds to the database table wh_scan_shipment_to_apv
     *
     * @mbggenerated Mon Dec 24 13:59:34 CST 2018
     */
    public void createWhScanShipmentToApv(WhScanShipmentToApv entity) {
        SqlerRequest request = new SqlerRequest("createWhScanShipmentToApv");
        request.addDataParam(WhScanShipmentToApvDBField.SCAN_SHIPMENT_ID, DataType.INT, entity.getScanShipmentId());
        request.addDataParam(WhScanShipmentToApvDBField.APV_NO, DataType.STRING, entity.getApvNo());
        request.addDataParam(WhScanShipmentToApvDBField.LAZBAO_BOX_NO, DataType.STRING, entity.getLazbaoBoxNo());
        request.addDataParam(WhScanShipmentToApvDBField.SHIP_CODE, DataType.STRING, entity.getShipCode());
        Integer autoIncrementId = SqlerTemplate.executeAndReturn(request);
        entity.setId(autoIncrementId);
    }

    /**
     * This method corresponds to the database table wh_scan_shipment_to_apv
     *
     * @mbggenerated Mon Dec 24 13:59:34 CST 2018
     */
    public void updateWhScanShipmentToApv(WhScanShipmentToApv entity) {
        SqlerRequest request = new SqlerRequest("updateWhScanShipmentToApvByPrimaryKey");
        request.addDataParam(WhScanShipmentToApvDBField.ID, DataType.INT, entity.getId());
        
        request.addDataParam(WhScanShipmentToApvDBField.SCAN_SHIPMENT_ID, DataType.INT, entity.getScanShipmentId());
        request.addDataParam(WhScanShipmentToApvDBField.APV_NO, DataType.STRING, entity.getApvNo());
        request.addDataParam(WhScanShipmentToApvDBField.LAZBAO_BOX_NO, DataType.STRING, entity.getLazbaoBoxNo());
        request.addDataParam(WhScanShipmentToApvDBField.SHIP_CODE, DataType.STRING, entity.getShipCode());
        SqlerTemplate.execute(request);
    }

    /**
     * This method corresponds to the database table wh_scan_shipment_to_apv
     *
     * @mbggenerated Mon Dec 24 13:59:34 CST 2018
     */
    public void batchCreateWhScanShipmentToApv(List<WhScanShipmentToApv> entityList) {
        if (entityList != null && !entityList.isEmpty())
        {
            SqlerRequest request = new SqlerRequest("createWhScanShipmentToApv");
            for (WhScanShipmentToApv entity : entityList)
            {
                request.addBatchDataParam(WhScanShipmentToApvDBField.SCAN_SHIPMENT_ID, DataType.INT, entity.getScanShipmentId());
                request.addBatchDataParam(WhScanShipmentToApvDBField.APV_NO, DataType.STRING, entity.getApvNo());
                request.addBatchDataParam(WhScanShipmentToApvDBField.LAZBAO_BOX_NO, DataType.STRING, entity.getLazbaoBoxNo());
                request.addBatchDataParam(WhScanShipmentToApvDBField.SHIP_CODE, DataType.STRING, entity.getShipCode());
                request.addBatch();
            }
            SqlerTemplate.batchUpdate(request);
        }
    }

    /**
     * This method corresponds to the database table wh_scan_shipment_to_apv
     *
     * @mbggenerated Mon Dec 24 13:59:34 CST 2018
     */
    public void batchUpdateWhScanShipmentToApv(List<WhScanShipmentToApv> entityList) {
        if (entityList != null && !entityList.isEmpty())
        {
            SqlerRequest request = new SqlerRequest("updateWhScanShipmentToApvByPrimaryKey");
            for (WhScanShipmentToApv entity : entityList)
            {
                request.addBatchDataParam(WhScanShipmentToApvDBField.ID, DataType.INT, entity.getId());
                
                request.addBatchDataParam(WhScanShipmentToApvDBField.SCAN_SHIPMENT_ID, DataType.INT, entity.getScanShipmentId());
                request.addBatchDataParam(WhScanShipmentToApvDBField.APV_NO, DataType.STRING, entity.getApvNo());
                request.addBatchDataParam(WhScanShipmentToApvDBField.LAZBAO_BOX_NO, DataType.STRING, entity.getLazbaoBoxNo());
                request.addBatchDataParam(WhScanShipmentToApvDBField.SHIP_CODE, DataType.STRING, entity.getShipCode());
                request.addBatch();
            }
            SqlerTemplate.batchUpdate(request);
        }
    }

    /**
     * This method corresponds to the database table wh_scan_shipment_to_apv
     *
     * @mbggenerated Mon Dec 24 13:59:34 CST 2018
     */
    public void deleteWhScanShipmentToApv(Integer primaryKey) {
        SqlerRequest request = new SqlerRequest("deleteWhScanShipmentToApvByPrimaryKey");
        request.addDataParam(WhScanShipmentToApvDBField.ID, DataType.INT, primaryKey);
        SqlerTemplate.execute(request);
    }
}