package com.estone.scan.deliver.action;

import com.alibaba.fastjson.JSON;
import com.estone.common.util.CacheUtils;
import com.estone.common.util.HttpUtils;
import com.estone.common.util.model.ApiRequestParam;
import com.estone.common.util.model.ApiResult;
import com.estone.scan.deliver.bean.WhShippingMethodVolumeInfo;
import com.estone.scan.deliver.bean.WhShippingMethodVolumeInfoQueryCondition;
import com.estone.scan.deliver.domain.WhShippingMethodVolumeInfoDo;
import com.estone.scan.deliver.service.WhShippingMethodVolumeInfoService;
import com.estone.system.param.bean.SystemParam;
import com.whq.tool.action.BaseController;
import com.whq.tool.component.Pager;
import com.whq.tool.component.StatusCode;
import com.whq.tool.json.ResponseJson;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping(value = "whShippingMethodVolumeInfo")
@Slf4j
public class WhShippingMethodVolumeInfoController extends BaseController {

    private Logger logger = LoggerFactory.getLogger(WhShippingMethodVolumeInfoController.class);
    @Resource
    private WhShippingMethodVolumeInfoService whShippingMethodVolumeInfoService;

    @RequestMapping(method = {RequestMethod.GET})
    public String init(@ModelAttribute("domain") WhShippingMethodVolumeInfoDo domain) {
        return "{模块}/{页面}";
    }

    private void initFormData(@ModelAttribute("domain") WhShippingMethodVolumeInfoDo domain) {

    }

    private void queryWhShippingMethodVolumeInfos(@ModelAttribute("domain") WhShippingMethodVolumeInfoDo domain) {
        WhShippingMethodVolumeInfoQueryCondition query = domain.getQuery();
        Pager page = domain.getPage();
        if (query == null) {
            query = new WhShippingMethodVolumeInfoQueryCondition();
            domain.setQuery(query);
        }
        List<WhShippingMethodVolumeInfo> whShippingMethodVolumeInfos = whShippingMethodVolumeInfoService.queryWhShippingMethodVolumeInfos(query, page);
        domain.setWhShippingMethodVolumeInfos(whShippingMethodVolumeInfos);
    }

    @RequestMapping(value = "search", method = {RequestMethod.POST})
    public String search(@ModelAttribute("domain") WhShippingMethodVolumeInfoDo domain) {
        initFormData(domain);
        queryWhShippingMethodVolumeInfos(domain);
        return "{模块}/{页面}";
    }

    @RequestMapping(value = "create", method = {RequestMethod.GET})
    public String toCreateWhShippingMethodVolumeInfo(@ModelAttribute("domain") WhShippingMethodVolumeInfoDo domain) {
        return "{模块}/{页面}";
    }

    @RequestMapping(value = "create", method = {RequestMethod.POST})
    public String createWhShippingMethodVolumeInfo(@ModelAttribute("domain") WhShippingMethodVolumeInfoDo domain) {
        WhShippingMethodVolumeInfo whShippingMethodVolumeInfo = domain.getWhShippingMethodVolumeInfo();
        whShippingMethodVolumeInfoService.createWhShippingMethodVolumeInfo(whShippingMethodVolumeInfo);
        return "redirect:/{查询列表}";
    }

    @RequestMapping(value = "update", method = {RequestMethod.GET})
    public String toUpdateWhShippingMethodVolumeInfo(@ModelAttribute("domain") WhShippingMethodVolumeInfoDo domain, @RequestParam("whShippingMethodVolumeInfoId") Integer whShippingMethodVolumeInfoId) {
        WhShippingMethodVolumeInfo whShippingMethodVolumeInfo = whShippingMethodVolumeInfoService.getWhShippingMethodVolumeInfo(whShippingMethodVolumeInfoId);
        domain.setWhShippingMethodVolumeInfo(whShippingMethodVolumeInfo);
        return "{模块}/{页面}";
    }

    @RequestMapping(value = "update", method = {RequestMethod.POST})
    public String updateWhShippingMethodVolumeInfo(@ModelAttribute("domain") WhShippingMethodVolumeInfoDo domain) {
        WhShippingMethodVolumeInfo whShippingMethodVolumeInfo = domain.getWhShippingMethodVolumeInfo();
        whShippingMethodVolumeInfoService.updateWhShippingMethodVolumeInfo(whShippingMethodVolumeInfo);
        return "redirect:/{查询列表}";
    }

    @RequestMapping(value = "delete", method = {RequestMethod.GET})
    @ResponseBody
    public ResponseJson deleteWhShippingMethodVolumeInfo(@ModelAttribute("domain") WhShippingMethodVolumeInfoDo domain, @RequestParam("whShippingMethodVolumeInfoId") Integer whShippingMethodVolumeInfoId) {
        ResponseJson response = new ResponseJson();
        whShippingMethodVolumeInfoService.deleteWhShippingMethodVolumeInfo(whShippingMethodVolumeInfoId);
        return response;
    }

    @RequestMapping(value = "reflash", method = {RequestMethod.POST})
    public ResponseJson reflash(@RequestParam("code") String code, @RequestParam("country") String country) {
        ResponseJson response = new ResponseJson(StatusCode.FAIL);
        if (StringUtils.isBlank(code) || StringUtils.isBlank(country)) {
            response.setMessage("参数有误");
        }
        SystemParam systemParam = CacheUtils.SystemParamGet("TMS_PARAM.REFLASH_WH_SHIPPING_METHOD_VOLUME_INFO");

        if (systemParam == null || StringUtils.isBlank(systemParam.getParamValue())) {
            response.setMessage("未配置URL参数!");
            return response;
        }
        String url = systemParam.getParamValue();
        ApiResult<WhShippingMethodVolumeInfo> apiResult = new ApiResult<>();
        apiResult.setSuccess(false);
        Map<String, String> map = new HashMap<>();
        map.put("shippingMethodCode", code);
        map.put("country", country);
        ApiRequestParam param = new ApiRequestParam();
        param.setArgs(JSON.toJSONString(map));
        param.setMethod("findBySmCodeAndCountry");
        try {
            apiResult = HttpUtils.post(url, HttpUtils.ACCESS_TOKEN, param, ApiResult.class);
            logger.info("更新交运尺寸拦截规则: apiResult[ " + JSON.toJSONString(apiResult) + " ]");
            if (apiResult.getResult() != null) {
                WhShippingMethodVolumeInfo volumeInfo = JSON.parseObject(JSON.toJSONString(apiResult.getResult()), WhShippingMethodVolumeInfo.class);
                if (volumeInfo.getId() != null && StringUtils.isNotBlank(volumeInfo.getShippingMethodCode()) && StringUtils.isNotBlank(volumeInfo.getShippingMethodCode())) {
                    whShippingMethodVolumeInfoService.pushWhShippingMethodVolumeInfos(Arrays.asList(volumeInfo));
                }
            }
            response.setMessage(JSON.toJSONString(apiResult));
            response.setStatus(StatusCode.SUCCESS);
        } catch (Exception e) {
            response.setMessage(e.getMessage());
            logger.error(e.getMessage(), e);
        }
        whShippingMethodVolumeInfoService.getWhShippingMethodVolumeInfoByCodeAndCountry(code, country);
        return response;
    }

    public static void main(String[] args) {
        //String url = "http://************:80/shippingMethodVolumeInfo";
        String url = "http://*************/shippingMethodVolumeInfo";
        ApiResult<WhShippingMethodVolumeInfo> apiResult = new ApiResult<>();
        apiResult.setSuccess(false);
        Map<String, String> map = new HashMap<>();
        map.put("shippingMethodCode", "SMT_CPOSP+_DG");
        map.put("country", "Spain");
        ApiRequestParam param = new ApiRequestParam();
        param.setArgs(JSON.toJSONString(map));
        param.setMethod("findBySmCodeAndCountry");
        try {
            apiResult = HttpUtils.post(url, HttpUtils.ACCESS_TOKEN, param, ApiResult.class);
            log.info("更新交运尺寸拦截规则: apiResult[ " + JSON.toJSONString(apiResult) + " ]");
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            apiResult.setErrorMsg(e.getMessage());
        }
    }
}