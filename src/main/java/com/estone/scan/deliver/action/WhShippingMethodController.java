package com.estone.scan.deliver.action;

import java.util.List;

import javax.annotation.Resource;
import javax.servlet.http.HttpSession;

import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import com.estone.scan.deliver.bean.WhShippingMethod;
import com.estone.scan.deliver.bean.WhShippingMethodQueryCondition;
import com.estone.scan.deliver.domain.WhShippingMethodDo;
import com.estone.scan.deliver.service.WhShippingMethodService;
import com.whq.tool.action.BaseController;
import com.whq.tool.component.Pager;
import com.whq.tool.json.ResponseJson;

@Controller
@RequestMapping(value = "scan/whShippingMethod")
public class WhShippingMethodController extends BaseController {
    @Resource
    private WhShippingMethodService whShippingMethodService;

    @RequestMapping(method = { RequestMethod.GET })
    public String init(@ModelAttribute("domain") WhShippingMethodDo domain) {
        return "{模块}/{页面}";
    }

    private void initFormData(@ModelAttribute("domain") WhShippingMethodDo domain) {

    }

    private void queryWhShippingMethods(@ModelAttribute("domain") WhShippingMethodDo domain) {
        WhShippingMethodQueryCondition query = domain.getQuery();
        Pager page = domain.getPage();
        if (query == null) {
            query = new WhShippingMethodQueryCondition();
            domain.setQuery(query);
        }
        List<WhShippingMethod> whShippingMethods = whShippingMethodService.queryWhShippingMethods(query, page);
        domain.setWhShippingMethods(whShippingMethods);
    }

    @RequestMapping(value = "search", method = { RequestMethod.POST })
    public String search(@ModelAttribute("domain") WhShippingMethodDo domain) {
        initFormData(domain);
        queryWhShippingMethods(domain);
        return "{模块}/{页面}";
    }

    @RequestMapping(value = "create", method = { RequestMethod.GET })
    public String toCreateWhShippingMethod(@ModelAttribute("domain") WhShippingMethodDo domain) {
        return "{模块}/{页面}";
    }

    @RequestMapping(value = "create", method = { RequestMethod.POST })
    public String createWhShippingMethod(@ModelAttribute("domain") WhShippingMethodDo domain, HttpSession session) {
        WhShippingMethod whShippingMethod = domain.getWhShippingMethod();
        whShippingMethodService.createWhShippingMethod(whShippingMethod);
        return "redirect:/{查询列表}";
    }

    @RequestMapping(value = "update", method = { RequestMethod.GET })
    public String toUpdateWhShippingMethod(@ModelAttribute("domain") WhShippingMethodDo domain,
            @RequestParam("whShippingMethodId") Integer whShippingMethodId) {
        WhShippingMethod whShippingMethod = whShippingMethodService.getWhShippingMethod(whShippingMethodId);
        domain.setWhShippingMethod(whShippingMethod);
        return "{模块}/{页面}";
    }

    @RequestMapping(value = "update", method = { RequestMethod.POST })
    public String updateWhShippingMethod(@ModelAttribute("domain") WhShippingMethodDo domain, HttpSession session) {
        WhShippingMethod whShippingMethod = domain.getWhShippingMethod();
        whShippingMethodService.updateWhShippingMethod(whShippingMethod);
        return "redirect:/{查询列表}";
    }

    @RequestMapping(value = "delete", method = { RequestMethod.GET })
    @ResponseBody
    public ResponseJson deleteWhShippingMethod(@ModelAttribute("domain") WhShippingMethodDo domain,
            @RequestParam("whShippingMethodId") Integer whShippingMethodId) {
        ResponseJson response = new ResponseJson();
        whShippingMethodService.deleteWhShippingMethod(whShippingMethodId);
        return response;
    }
}