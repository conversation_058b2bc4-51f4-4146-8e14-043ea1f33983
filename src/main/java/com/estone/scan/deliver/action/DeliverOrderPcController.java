package com.estone.scan.deliver.action;

import com.estone.common.util.*;
import com.estone.common.util.model.ApiResult;
import com.estone.scan.deliver.bean.DeliverDTO;
import com.estone.scan.deliver.bean.DeliverRecordDTO;
import com.estone.scan.deliver.bean.DeliverScanRecordDTO;
import com.estone.scan.deliver.domain.DeliverScanDomain;
import com.estone.system.rabbitmq.bean.AmqMessage;
import com.estone.system.rabbitmq.service.AmqMessageService;
import com.estone.system.rabbitmq.utils.AssembleMessageDataUtils;
import com.whq.tool.context.DataContextHolder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Comparator;
import java.util.Date;
import java.util.concurrent.TimeUnit;


/**
 * PC端交运
 * <AUTHOR>
 */
@Slf4j
@Controller
@RequestMapping(value = "scan/pcDeliverOrder")
public class DeliverOrderPcController {

    @Resource
    private DeliverOrderController deliverOrderController;

    @Resource
    private AmqMessageService amqMessageService;

    @GetMapping("toDeliverScan")
    public String toDeliverScan(@ModelAttribute("domain") DeliverScanDomain domain){
        Integer userId = DataContextHolder.getUserId();
        Object recordObj = StringRedisUtils.hGet(RedisConstant.DELIVER_SCAN_PC, String.valueOf(userId));
        if (recordObj != null) {
            DeliverScanRecordDTO dto = (DeliverScanRecordDTO) recordObj;
            if (CollectionUtils.isNotEmpty(dto.getContents())) {
                double weightTotal = dto.getContents().stream().filter(c -> c.getWeight() != null).mapToDouble(DeliverRecordDTO::getWeight).sum();
                domain.setWeightTotal(weightTotal);
            }
            dto.getContents().sort(Comparator.comparing(DeliverRecordDTO::getDeliverTime).reversed());
            domain.setScanRecordDTO(dto);
            domain.setSfmCode(dto.getSfmCode());
        }
        return "deliver/deliverScanPc";
    }

    @ResponseBody
    @PostMapping("checkAndDeliver")
    public ApiResult<?> checkAndDeliver(@RequestBody DeliverDTO deliverDTO){
        Integer userId = DataContextHolder.getUserId();
        try {
            if (RedissonLockUtil.tryLock(RedisKeys.getDeliverScanPcKey(String.valueOf(userId)), TimeUnit.SECONDS, 5, 300)) {
                try {
                    String username = DataContextHolder.getUsername();
                    deliverDTO.setScanner(username + "(" + userId + ")");
                    ApiResult<?> version = deliverOrderController.getVersion();
                    if (!version.isSuccess()) {
                        return version;
                    }
                    deliverDTO.setDeliverVervion((String) version.getResult());
                    String sfmCode = deliverDTO.getLogisticsCompanyCode();
                    deliverDTO.setLogisticsCompanyCode(sfmCode.substring(0, sfmCode.indexOf("(")));


                    // 交运校验
                    ApiResult<?> apiResult = deliverOrderController.checkApvAndCustomerorder(deliverDTO);
                    apiResult = handleResMsg(apiResult.getResult().toString());
                    if (!apiResult.isSuccess()) {
                        return apiResult;
                    }
                    String apvNo = apiResult.getResult().toString();
                    if (StringUtils.contains(apvNo, ",")) {
                        apvNo = apvNo.split(",")[0];
                    }

                    // 扫描记录存入Redis
                    Object recordObj = StringRedisUtils.hGet(RedisConstant.DELIVER_SCAN_PC, String.valueOf(userId));
                    DeliverScanRecordDTO scanRecordDTO;
                    if (recordObj != null) {
                        scanRecordDTO = (DeliverScanRecordDTO) recordObj;
                    } else {
                        scanRecordDTO = new DeliverScanRecordDTO();
                        scanRecordDTO.setSfmCode(sfmCode);
                    }
                    DeliverRecordDTO deliverRecordDTO = new DeliverRecordDTO(apvNo, deliverDTO.getOrderNo(), deliverDTO.getActualWeight(),
                            DateUtils.formatDate(new Date(), DateUtils.STANDARD_DATE_PATTERN), deliverDTO.getScanner());
                    scanRecordDTO.getContents().add(deliverRecordDTO);
                    StringRedisUtils.hSet(RedisConstant.DELIVER_SCAN_PC, String.valueOf(userId), scanRecordDTO);

                    // 加入交运队列
                    AmqMessage amqMessage = AssembleMessageDataUtils.assembleDeliverScan(deliverRecordDTO);
                    amqMessageService.createAmqMessage(amqMessage);

                    return ApiResult.newSuccess(deliverRecordDTO);
                } catch (Exception e) {
                    log.error("PC交运异常:"+ e.getMessage(), e);
                    return ApiResult.newError("PC交运异常:"+ e.getMessage());
                }
            }else {
                return ApiResult.newError("请勿重复提交!");
            }
        } finally {
            RedissonLockUtil.unlock(RedisKeys.getDeliverScanPcKey(String.valueOf(userId)));
        }
    }

    @ResponseBody
    @PostMapping("generatePocketCard")
    public ApiResult<?> generatePocketCard(@RequestBody DeliverDTO deliverDTO){
        Integer userId = DataContextHolder.getUserId();
        if (userId == null) {
            return ApiResult.newError("用户ID信息缺失");
        }
        try {
            if (RedissonLockUtil.tryLock(RedisKeys.getDeliverScanPcKey(String.valueOf(userId)), TimeUnit.SECONDS, 5, 300)) {
                try {
                    String username = DataContextHolder.getUsername();
                    String sfmCode = deliverDTO.getLogisticsCompanyCode();
                    deliverDTO.setLogisticsCompanyCode(sfmCode.substring(0, sfmCode.indexOf("(")));
                    deliverDTO.setScanner(username);
                    ApiResult<?> apiResult = deliverOrderController.generatePocketCard(deliverDTO);
                    if (!apiResult.isSuccess()) {
                        return apiResult;
                    }
                    StringRedisUtils.hDel(RedisConstant.DELIVER_SCAN_PC, String.valueOf(userId));
                    return apiResult;
                } catch (Exception e) {
                    log.error("PC结袋异常:"+ e.getMessage(), e);
                    return ApiResult.newError("PC结袋异常:"+ e.getMessage());
                }
            }else {
                return ApiResult.newError("请勿重复提交结袋!");
            }
        } finally {
            RedissonLockUtil.unlock(RedisKeys.getDeliverScanPcKey(String.valueOf(userId)));
        }
    }

    public ApiResult<?> handleResMsg(String result){
        if (result.indexOf("isOverWeight") != -1) {
            return ApiResult.newError("订单超重");
        }
        else if (result.indexOf("isDeliveryForbidden") != -1) {
            return ApiResult.newError("该订单被禁止发货");
        }
        else if (result.indexOf("errorLogisticsCompanyCode") != -1) {
            return ApiResult.newError("当前单号与物流公司不匹配");
        }
        else if (result.indexOf("shippedProduct") != -1) {
            //"shippedProduct"+"="+deliverTime;
            String deliverTime = "";
            if (result.contains("=")) {
                deliverTime = result.split("=")[1];
            }
            return ApiResult.newError("该发货单已经交运！交运时间="+deliverTime);
        }
        else if (result.indexOf("shippingOrderNoIsOver") != -1) {
            return ApiResult.newError("追踪号超期失效请到追踪号超时订单界面处理后重发");
        }
        else if (result.indexOf("isRefundOrder") != -1) {
            return ApiResult.newError("订单已申请退款不能发货");
        }
        else if (result.indexOf("errorYST") != -1) {
            return ApiResult.newError("该渠道不允许扫描YST号");
        }
        else if (result.indexOf("notFond") != -1) {
            return ApiResult.newError("没有匹配到发货单");
        }
        else if (result.indexOf("notWaitingDeliver") != -1) {
            return ApiResult.newError("WMS不是等待发货状态");
        }
        else if (result.indexOf("checkStandardWeight") != -1) {
            return ApiResult.newError("重量误差超出允许范围");
        }
        else if (StringUtils.isNotEmpty(result) && !result.startsWith("YST")) {
            return ApiResult.newError(result);
        }
        return ApiResult.newSuccess(result);
    }

}
