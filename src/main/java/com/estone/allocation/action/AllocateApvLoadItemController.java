package com.estone.allocation.action;

import com.estone.allocation.bean.AllocateApvLoadItem;
import com.estone.allocation.bean.AllocateApvLoadItemQueryCondition;
import com.estone.allocation.domain.AllocateApvLoadItemDo;
import com.estone.allocation.service.AllocateApvLoadItemService;
import com.whq.tool.action.BaseController;
import com.whq.tool.component.Pager;
import com.whq.tool.json.ResponseJson;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @Description 调拨装车推送装车数量
 * @date 2020/6/5 16:08
 * @param: null
 * @return
 */
@RestController
@RequestMapping(value = "allocateApvLoadItem")
public class AllocateApvLoadItemController extends BaseController {
    @Resource
    private AllocateApvLoadItemService allocateApvLoadItemService;

    @RequestMapping(method = {RequestMethod.GET})
    public String init(@ModelAttribute("domain") AllocateApvLoadItemDo domain) {
        return "{模块}/{页面}";
    }

    private void initFormData(@ModelAttribute("domain") AllocateApvLoadItemDo domain) {

    }

    private void queryAllocateApvLoadItems(@ModelAttribute("domain") AllocateApvLoadItemDo domain) {
        AllocateApvLoadItemQueryCondition query = domain.getQuery();
        Pager page = domain.getPage();
        if (query == null) {
            query = new AllocateApvLoadItemQueryCondition();
            domain.setQuery(query);
        }
        List<AllocateApvLoadItem> allocateApvLoadItems = allocateApvLoadItemService.queryAllocateApvLoadItems(query, page);
        domain.setAllocateApvLoadItems(allocateApvLoadItems);

    }

    @RequestMapping(value = "search", method = {RequestMethod.POST})
    public String search(@ModelAttribute("domain") AllocateApvLoadItemDo domain) {
        initFormData(domain);
        queryAllocateApvLoadItems(domain);
        return "{模块}/{页面}";
    }

    @RequestMapping(value = "create", method = {RequestMethod.GET})
    public String toCreateAllocateApvLoadItem(@ModelAttribute("domain") AllocateApvLoadItemDo domain) {
        return "{模块}/{页面}";
    }

    @RequestMapping(value = "create", method = {RequestMethod.POST})
    public String createAllocateApvLoadItem(@ModelAttribute("domain") AllocateApvLoadItemDo domain) {
        AllocateApvLoadItem allocateApvLoadItem = domain.getAllocateApvLoadItem();
        allocateApvLoadItemService.createAllocateApvLoadItem(allocateApvLoadItem);
        return "redirect:/{查询列表}";
    }

    @RequestMapping(value = "update", method = {RequestMethod.GET})
    public String toUpdateAllocateApvLoadItem(@ModelAttribute("domain") AllocateApvLoadItemDo domain, @RequestParam("allocateApvLoadItemId") Integer allocateApvLoadItemId) {
        AllocateApvLoadItem allocateApvLoadItem = allocateApvLoadItemService.getAllocateApvLoadItem(allocateApvLoadItemId);
        domain.setAllocateApvLoadItem(allocateApvLoadItem);
        return "{模块}/{页面}";
    }

    @RequestMapping(value = "update", method = {RequestMethod.POST})
    public String updateAllocateApvLoadItem(@ModelAttribute("domain") AllocateApvLoadItemDo domain) {
        AllocateApvLoadItem allocateApvLoadItem = domain.getAllocateApvLoadItem();
        allocateApvLoadItemService.updateAllocateApvLoadItem(allocateApvLoadItem);
        return "redirect:/{查询列表}";
    }

    @RequestMapping(value = "delete", method = {RequestMethod.GET})
    @ResponseBody
    public ResponseJson deleteAllocateApvLoadItem(@ModelAttribute("domain") AllocateApvLoadItemDo domain, @RequestParam("allocateApvLoadItemId") Integer allocateApvLoadItemId) {
        ResponseJson response = new ResponseJson();
        allocateApvLoadItemService.deleteAllocateApvLoadItem(allocateApvLoadItemId);
        return response;
    }
}