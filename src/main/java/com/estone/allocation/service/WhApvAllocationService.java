package com.estone.allocation.service;

import java.util.List;
import java.util.Map;

import com.estone.allocation.bean.*;
import com.estone.checkin.bean.WhCheckIn;
import com.estone.warehouse.bean.WhStock;
import com.whq.tool.component.Pager;
import com.whq.tool.json.ResponseJson;

public interface WhApvAllocationService {

    // 查询调拨SKU详情
    List<WhApvAllocationItem> queryApvAllocationSkuList(List<String> skuList);

    // 修改调拨单详情
    void batchUpdateApvAllocationItem(List<WhApvAllocationItem> entityList);

    // 拣货差异更新调拨数据
    ResponseJson updateAllocationQuantity(List<String> skus, WhApvAllocation apvAllocation, List<WhApvAllocationItem> diffItems);

    // 审核状态退回重新拣货
    ResponseJson doBackAllocationPicking(String allocationNo);

    // 待处理到待拣货
    public void doTransferAllocationPick(WhApvAllocation allocation, List<WhApvAllocationItem> itemList);

    // 库存调拨单详情数量
    int queryApvAllocationDetailCount(WhApvAllocationQueryCondition query);

    // 查询调拨单详情
    WhApvAllocationItem queryWhApvAllocationItem(WhApvAllocationItemQueryCondition query);

    List<WhApvAllocationItem> queryWhApvAllocationItemList(WhApvAllocationItemQueryCondition query);

    // 根据条件查询调拨单
    WhApvAllocation queryApvAllocation(WhApvAllocationQueryCondition query);

    // 分页查询调拨单
    List<WhApvAllocation> queryApvAllocationList(WhApvAllocationQueryCondition query, Pager pager);

    // 根据条件查询调拨单详情
    WhApvAllocation queryApvAllocationDetail(WhApvAllocationQueryCondition query);

    // 分页查询调拨单详情
    List<WhApvAllocation> queryApvAllocationDetailList(WhApvAllocationQueryCondition query, Pager pager);

    // 查询调拨单详情列表
    List<WhApvAllocationItem> queryApvAllocationDetails(WhApvAllocationQueryCondition query);

    // 创建调拨单
    void createApvAllocation(WhApvAllocationQueryCondition entity);

    ResponseJson createOrUpdateApvAllocation(WhApvAllocationQueryCondition entity);

    /**
     * 目的仓接收库存调拨单数据
     * @param entity
     * @return
     */
    ResponseJson updateReceivedApvAllocation(List<String> skus, WhApvAllocationQueryCondition entity);

    // 修改调拨单
    ResponseJson updateApvAllocation(WhApvAllocationQueryCondition entity);

    // 提交拣货
    ResponseJson batchCommitWhApvAllocation(List<String> skus, WhApvAllocation apvAllocation, Integer sectionNum);

    // 审核通过
    ResponseJson batchAuditWhApvAllocation(WhApvAllocationQueryCondition query);

    // 确认签收
    ResponseJson batchConfirmWhApvAllocation(WhApvAllocationQueryCondition query);

    // 废弃调拨单
    ResponseJson batchDiscardWhApvAllocation(WhApvAllocationQueryCondition query);

    /*********************************************************************************************************************************/

    // 调拨拣货
    ResponseJson updateApvAllocationByPick(WhApvAllocationItemQueryCondition query);

    // 调拨装箱
    ResponseJson updateApvAllocationByBox(List<WhApvAllocationItem> whApvAllocationItems);

    // 调拨打板
    ResponseJson updateApvAllocationByBoard(WhApvAllocationItemQueryCondition query);

    // 调拨装车
    ResponseJson updateApvAllocationByLoad(WhApvAllocationItemQueryCondition query);

    // 调拨入库
    ResponseJson updateApvAllocationByUp(WhApvAllocationItemQueryCondition query);

    // 修改调拨单状态
    ResponseJson updateApvAllocationStatus(String allocationNo, int oldStatus, int newStatus);

    ResponseJson updateApvAllocationStatusByOldStatus(String allocationNo, int oldStatus, int newStatus);

    ResponseJson updateAllocationStatusByUp(String allocationNo, int newStatus);

    int updateApvAllocationByPush(WhApvAllocation entity);

    /** 库存调拨装车完成修改状态 */
    void batchUpdateApvAllocationByLoading(List<Integer> allocationIdList);

    /** 订单调拨装车完成修改状态 */
    void batchUpdateOrderAllocationByLoading(List<Integer> allocationIdList);

    // 库存调拨->调拨单 已入库 已完成 状态 推送 修改发货仓调拨库存
    List<AllocationInventory> queryAlloCheckInData(List<String> allocationNoList);

    // 查询调拨需求中已完成的调拨单对应的调拨需求的item
    List<AllocationInventory> queryApvItemByFinish();

    // 订单调拨->调拨单 已完成 查询调出仓apv是否已交运，若交运则修改调入仓调拨库存
    List<String> queryAlloApvNosOnLoad(List<String> apvNoList, List<Integer> statusList);

    int updateApvAllocationTrunk(WhApvAllocationItem entity);

    //
    // 订单调拨打板
    ResponseJson updateOrderAllocationByBoard(WhApvAllocationItemQueryCondition query);

    // 订单调拨装车
    ResponseJson updateOrderAllocationByLoad(WhApvAllocationItemQueryCondition query);

    List<WhApvAllocation> queryApvAllocationList(WhApvAllocationQueryCondition query);

    List<SyncAllocation> querySyncAllocationAndItemList(WhApvAllocationQueryCondition query, Pager pager);

    void updateSyncAllocationByPrimaryKey(List<WhApvAllocation> entityList);

    void updateSyncAllocationItemByPrimaryKey(List<WhApvAllocationItem> entityList);

    // 库存调拨单盘点装箱数量
    ResponseJson doSingleAuditWhApvAllocation(List<String> skus, WhApvAllocationItem allocationItem, Integer boxNum);

    // 查询调拨上架详情
    List<WhApvAllocationUpItem> queryAllocationUpItemList(WhApvAllocationQueryCondition query, Pager pager);

    void createExpBatchAndBindUuid(WhCheckIn whCheckIn, String sku, String allocationOrderNo, Integer inId);

    /**
     * 更新调拨单状态
     * @param query
     */
    void updateAllocationStatus(WhApvAllocationQueryCondition query);

    ResponseJson getSkuInfo(List<String> collect, Map<Integer, WhStock> importMap);
}
