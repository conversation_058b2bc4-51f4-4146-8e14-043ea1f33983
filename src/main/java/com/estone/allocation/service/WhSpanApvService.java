package com.estone.allocation.service;

import com.estone.allocation.bean.WhSpanApv;
import com.estone.allocation.bean.WhSpanApvQueryCondition;
import com.whq.tool.component.Pager;
import java.util.List;

public interface WhSpanApvService {
    List<WhSpanApv> queryAllWhSpanApvs();

    List<WhSpanApv> queryWhSpanApvs(WhSpanApvQueryCondition query, Pager pager);

    WhSpanApv getWhSpanApv(Integer id);

    WhSpanApv getWhSpanApvDetail(Integer id);

    WhSpanApv queryWhSpanApv(String apvNo);

    void createWhSpanApv(String apvNo);

    void batchCreateWhSpanApv(List<WhSpanApv> entityList);

    void deleteWhSpanApv(Integer id);

    void updateWhSpanApv(WhSpanApv whSpanApv);

    void batchUpdateWhSpanApv(List<WhSpanApv> entityList);
}