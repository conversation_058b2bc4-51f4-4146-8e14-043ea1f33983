package com.estone.allocation.service;

import java.util.List;

import com.estone.apv.bean.WhApv;
import com.estone.apv.bean.WhApvItem;
import com.estone.system.user.bean.SaleUser;
import com.whq.tool.json.ResponseJson;

public interface AcrossInventoryRecordService {

    /**
     * @Description: 跨仓订单库存匹配
     *
     * @param saleUser 当前操作用户
     * @param whApv 发货单
     * @param status 修改的状态
     * @return
     * @Author: Administrator
     * @Date: 2019/04/11
     * @Version: 0.0.1
     */
    public ResponseJson doMatchAcrossSku(SaleUser saleUser, WhApv whApv, Integer status);

    /**
     * @Description: 跨仓订单匹配库存http调用目的仓库存服务
     *
     * @param destList 操作的sku库存
     * @return
     * @Author: Administrator
     * @Date: 2019/04/11
     * @Version: 0.0.1
     */
    public ResponseJson doHttpMatchAcrossSku(List<WhApvItem> destList);

    /**
     * @Description: 跨仓订单移动到待分配
     *
     * @param saleUser 当前操作用户
     * @param whApv 发货单
     * @param status 修改的状态
     * @return
     * @Author: Administrator
     * @Date: 2019/04/11
     * @Version: 0.0.1
     */
    public ResponseJson doMoveBackAcrossSku(SaleUser saleUser, WhApv whApv, Integer status);

    /**
     * @Description: 跨仓订单取消发货
     *
     * @param whApv 发货单
     * @return
     * @Author: Administrator
     * @Date: 2019/04/11
     * @Version: 0.0.1
     */
    public ResponseJson doCancleBackAcrossSku(WhApv whApv);

    /**
     * @Description: 跨仓订单回退可用库存http请求
     *
     * @param destList sku库存详情
     * @param type http操作类型
     * @param isMergeTask 是否合成了调拨需求
     * @return
     * @Author: Administrator
     * @Date: 2019/04/11
     * @Version: 0.0.1
     */
    public ResponseJson doHttpRollbackAcrossSku(List<WhApvItem> destList, int type, boolean isMergeTask);
}