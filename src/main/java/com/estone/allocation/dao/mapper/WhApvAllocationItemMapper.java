package com.estone.allocation.dao.mapper;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.Optional;

import org.springframework.jdbc.core.RowMapper;

import com.estone.allocation.bean.WhApvAllocationItem;

public class WhApvAllocationItemMapper implements RowMapper<WhApvAllocationItem> {

    /**
     * This method corresponds to the database table wh_apv_allocation_item
     *
     * @mbggenerated Tue Apr 02 12:40:56 CST 2019
     */
    public WhApvAllocationItem mapRow(ResultSet rs, int rowNum) throws SQLException {
        WhApvAllocationItem entity = new WhApvAllocationItem();
        entity.setAllocationItemId(rs.getObject(WhApvAllocationItemDBField.ALLOCATION_ITEM_ID) == null ? null
                : rs.getInt(WhApvAllocationItemDBField.ALLOCATION_ITEM_ID));
        entity.setAllocationId(rs.getObject(WhApvAllocationItemDBField.ALLOCATION_ID) == null ? null
                : rs.getInt(WhApvAllocationItemDBField.ALLOCATION_ID));
        entity.setAllocationNo(rs.getString(WhApvAllocationItemDBField.ALLOCATION_NO));
        entity.setSku(rs.getString(WhApvAllocationItemDBField.SKU));
        entity.setSkuName(rs.getString(WhApvAllocationItemDBField.SKU_NAME));
        entity.setAllocationNum(rs.getObject(WhApvAllocationItemDBField.ALLOCATION_NUM) == null ? null : rs.getInt(WhApvAllocationItemDBField.ALLOCATION_NUM));
        entity.setCreateBy(rs.getObject(WhApvAllocationItemDBField.CREATE_BY) == null ? null
                : rs.getInt(WhApvAllocationItemDBField.CREATE_BY));
        entity.setCreateTime(rs.getTimestamp(WhApvAllocationItemDBField.CREATE_TIME));
        entity.setPickStatus(rs.getObject(WhApvAllocationItemDBField.PICK_STATUS) == null ? null
                : rs.getInt(WhApvAllocationItemDBField.PICK_STATUS));
        entity.setPickNum(rs.getObject(WhApvAllocationItemDBField.PICK_NUM) == null ? null
                : rs.getInt(WhApvAllocationItemDBField.PICK_NUM));
        entity.setPickBy(rs.getObject(WhApvAllocationItemDBField.PICK_BY) == null ? null
                : rs.getInt(WhApvAllocationItemDBField.PICK_BY));
        entity.setPickTime(rs.getTimestamp(WhApvAllocationItemDBField.PICK_TIME));
        entity.setBoxStatus(rs.getObject(WhApvAllocationItemDBField.BOX_STATUS) == null ? null
                : rs.getInt(WhApvAllocationItemDBField.BOX_STATUS));
        entity.setBoxNum(rs.getObject(WhApvAllocationItemDBField.BOX_NUM) == null ? null
                : rs.getInt(WhApvAllocationItemDBField.BOX_NUM));
        entity.setBoxNo(rs.getString(WhApvAllocationItemDBField.BOX_NO));
        entity.setBoxBy(rs.getObject(WhApvAllocationItemDBField.BOX_BY) == null ? null
                : rs.getInt(WhApvAllocationItemDBField.BOX_BY));
        entity.setBoxTime(rs.getTimestamp(WhApvAllocationItemDBField.BOX_TIME));
        entity.setBoardStatus(rs.getObject(WhApvAllocationItemDBField.BOARD_STATUS) == null ? null
                : rs.getInt(WhApvAllocationItemDBField.BOARD_STATUS));
        entity.setBoardNo(rs.getString(WhApvAllocationItemDBField.BOARD_NO));
        entity.setBoardBy(rs.getObject(WhApvAllocationItemDBField.BOARD_BY) == null ? null
                : rs.getInt(WhApvAllocationItemDBField.BOARD_BY));
        entity.setBoardTime(rs.getTimestamp(WhApvAllocationItemDBField.BOARD_TIME));
        entity.setLoadStatus(rs.getObject(WhApvAllocationItemDBField.LOAD_STATUS) == null ? null
                : rs.getInt(WhApvAllocationItemDBField.LOAD_STATUS));
        entity.setLoadBy(rs.getObject(WhApvAllocationItemDBField.LOAD_BY) == null ? null
                : rs.getInt(WhApvAllocationItemDBField.LOAD_BY));
        entity.setLoadTime(rs.getTimestamp(WhApvAllocationItemDBField.LOAD_TIME));
        entity.setUpStatus(rs.getObject(WhApvAllocationItemDBField.UP_STATUS) == null ? null
                : rs.getInt(WhApvAllocationItemDBField.UP_STATUS));
        entity.setUpNum(rs.getObject(WhApvAllocationItemDBField.UP_NUM) == null ? null
                : rs.getInt(WhApvAllocationItemDBField.UP_NUM));
        entity.setUpBy(rs.getObject(WhApvAllocationItemDBField.UP_BY) == null ? null
                : rs.getInt(WhApvAllocationItemDBField.UP_BY));
        entity.setUpTime(rs.getTimestamp(WhApvAllocationItemDBField.UP_TIME));
        entity.setIsAudit(rs.getObject(WhApvAllocationItemDBField.IS_AUDIT) == null ? null
                : rs.getBoolean(WhApvAllocationItemDBField.IS_AUDIT));
        entity.setHistoryPickNum(rs.getObject(WhApvAllocationItemDBField.HISTORY_PICK_NUM) == null ? null
                : rs.getInt(WhApvAllocationItemDBField.HISTORY_PICK_NUM));
        entity.setPutStatus(rs.getObject(WhApvAllocationItemDBField.PUT_STATUS) == null ? null
                : rs.getInt(WhApvAllocationItemDBField.PUT_STATUS));
        entity.setInventoryStatus(rs.getObject(WhApvAllocationItemDBField.INVENTORY_STATUS) == null ? null
                : rs.getInt(WhApvAllocationItemDBField.INVENTORY_STATUS));
        entity.setPutBy(rs.getObject(WhApvAllocationItemDBField.PUT_BY) == null ? null
                : rs.getInt(WhApvAllocationItemDBField.PUT_BY));
        entity.setPutTime(rs.getTimestamp(WhApvAllocationItemDBField.PUT_TIME));
        entity.setStockId(rs.getObject(WhApvAllocationItemDBField.STOCK_ID) == null ? null : rs.getInt(WhApvAllocationItemDBField.STOCK_ID));
        entity.setJsonStr(rs.getString(WhApvAllocationItemDBField.JSON_STR));
        return entity;
    }
}