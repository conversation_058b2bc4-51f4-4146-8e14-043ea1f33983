package com.estone.allocation.dao.mapper;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.HashMap;
import java.util.Map;

import org.springframework.jdbc.core.RowMapper;

import com.estone.allocation.bean.WhApvAllocation;
import com.estone.allocation.bean.WhApvAllocationItem;
import com.estone.sku.bean.WhSku;

public class WhApvAllocationMapper implements RowMapper<WhApvAllocation> {

    /** 查询调拨单页面相应的数据 */
    private boolean isPageQuery = false;

    /** 查询调拨单item详情 */
    private boolean isItemQuery = false;

    public WhApvAllocationMapper() {

    }

    public WhApvAllocationMapper(boolean isPageQuery, boolean isItemQuery) {
        this.isPageQuery = isPageQuery;
        this.isItemQuery = isItemQuery;
    }

    private Map<Integer, WhApvAllocation> exist = new HashMap<Integer, WhApvAllocation>();

    @Override
    public WhApvAllocation mapRow(ResultSet rs, int rowNum) throws SQLException {
        Integer allocationId = rs.getInt(WhApvAllocationDBField.ALLOCATION_ID);

        WhApvAllocation entity = exist.get(allocationId);

        boolean isExist = false;

        if (null == entity) {
            entity = new WhApvAllocation();

            entity.setAllocationId(rs.getObject(WhApvAllocationDBField.ALLOCATION_ID) == null ? null
                    : rs.getInt(WhApvAllocationDBField.ALLOCATION_ID));
            entity.setAllocationNo(rs.getString(WhApvAllocationDBField.ALLOCATION_NO));
            entity.setDeliveryWarehouseId(rs.getObject(WhApvAllocationDBField.DELIVERY_WAREHOUSE_ID) == null ? null
                    : rs.getInt(WhApvAllocationDBField.DELIVERY_WAREHOUSE_ID));
            entity.setDestWarehouseId(rs.getObject(WhApvAllocationDBField.DEST_WAREHOUSE_ID) == null ? null
                    : rs.getInt(WhApvAllocationDBField.DEST_WAREHOUSE_ID));
            entity.setAllocationType(rs.getInt(WhApvAllocationDBField.ALLOCATION_TYPE));
            entity.setTransportType(rs.getInt(WhApvAllocationDBField.TRANSPORT_TYPE));
            entity.setFreight(rs.getBigDecimal(WhApvAllocationDBField.FREIGHT));
            entity.setAllocationStatus(rs.getObject(WhApvAllocationDBField.ALLOCATION_STATUS) == null ? null
                    : rs.getInt(WhApvAllocationDBField.ALLOCATION_STATUS));
            entity.setCreateBy(rs.getObject(WhApvAllocationDBField.CREATE_BY) == null ? null
                    : rs.getInt(WhApvAllocationDBField.CREATE_BY));
            entity.setCreateTime(rs.getTimestamp(WhApvAllocationDBField.CREATE_TIME));
            entity.setAuditBy(rs.getObject(WhApvAllocationDBField.AUDIT_BY) == null ? null
                    : rs.getInt(WhApvAllocationDBField.AUDIT_BY));
            entity.setAuditTime(rs.getTimestamp(WhApvAllocationDBField.AUDIT_TIME));
            entity.setConfirmBy(rs.getObject(WhApvAllocationDBField.CONFIRM_BY) == null ? null
                    : rs.getInt(WhApvAllocationDBField.CONFIRM_BY));
            entity.setConfirmTime(rs.getTimestamp(WhApvAllocationDBField.CONFIRM_TIME));
            entity.setUpdateBy(rs.getObject(WhApvAllocationDBField.UPDATE_BY) == null ? null
                    : rs.getInt(WhApvAllocationDBField.UPDATE_BY));
            entity.setUpdateTime(rs.getTimestamp(WhApvAllocationDBField.UPDATE_TIME));
            entity.setRemark(rs.getString(WhApvAllocationDBField.REMARK));
            entity.setIsPush(rs.getObject(WhApvAllocationDBField.IS_PUSH) ==null?null:rs.getInt(WhApvAllocationDBField.IS_PUSH));

            if (isPageQuery) {
                // 查询页面相应的数据
                entity.setBoxCount(rs.getObject("boxCount") == null ? 0 : rs.getInt("boxCount"));
                entity.setSkuSpeciesCount(rs.getObject("skuSpeciesCount") == null ? 0 : rs.getInt("skuSpeciesCount"));
                entity.setSkuPcsCount(rs.getObject("skuPcsCount") == null ? 0 : rs.getInt("skuPcsCount"));
                entity.setSubTaskCount(rs.getObject("subTaskCount") == null ? 0 : rs.getInt("subTaskCount"));
            }

            exist.put(allocationId, entity);
        }
        else {
            isExist = true;
        }

        if (isItemQuery) {
            // 查询调拨单item详情
            WhApvAllocationItem item = new WhApvAllocationItem();
            item.setAllocationItemId(rs.getObject(WhApvAllocationItemDBField.ALLOCATION_ITEM_ID) == null ? null
                    : rs.getInt(WhApvAllocationItemDBField.ALLOCATION_ITEM_ID));
            item.setAllocationId(rs.getObject(WhApvAllocationItemDBField.ALLOCATION_ID) == null ? null
                    : rs.getInt(WhApvAllocationItemDBField.ALLOCATION_ID));
            item.setAllocationNo(rs.getString(WhApvAllocationItemDBField.ALLOCATION_NO));
            item.setSku(rs.getString(WhApvAllocationItemDBField.SKU));
            item.setSkuName(rs.getString(WhApvAllocationItemDBField.SKU_NAME));
            item.setAllocationNum(rs.getObject(WhApvAllocationItemDBField.ALLOCATION_NUM) == null ? null : rs.getInt(WhApvAllocationItemDBField.ALLOCATION_NUM));
            item.setStockId(rs.getObject(WhApvAllocationItemDBField.STOCK_ID) == null ? null : rs.getInt(WhApvAllocationItemDBField.STOCK_ID));
            item.setJsonStr(rs.getString(WhApvAllocationItemDBField.JSON_STR));
            item.setCreateBy(rs.getObject(WhApvAllocationItemDBField.CREATE_BY) == null ? null
                    : rs.getInt(WhApvAllocationItemDBField.CREATE_BY));
            item.setCreateTime(rs.getTimestamp(WhApvAllocationItemDBField.CREATE_TIME));
            item.setPickStatus(rs.getInt(WhApvAllocationItemDBField.PICK_STATUS));
            item.setPickNum(rs.getObject(WhApvAllocationItemDBField.PICK_NUM) == null ? null
                    : rs.getInt(WhApvAllocationItemDBField.PICK_NUM));
            item.setPickBy(rs.getObject(WhApvAllocationItemDBField.PICK_BY) == null ? null
                    : rs.getInt(WhApvAllocationItemDBField.PICK_BY));
            item.setPickTime(rs.getTimestamp(WhApvAllocationItemDBField.PICK_TIME));
            item.setBoxStatus(rs.getInt(WhApvAllocationItemDBField.BOX_STATUS));
            item.setBoxNum(rs.getObject(WhApvAllocationItemDBField.BOX_NUM) == null ? null
                    : rs.getInt(WhApvAllocationItemDBField.BOX_NUM));
            item.setBoxNo(rs.getString(WhApvAllocationItemDBField.BOX_NO));
            item.setBoxBy(rs.getObject(WhApvAllocationItemDBField.BOX_BY) == null ? null
                    : rs.getInt(WhApvAllocationItemDBField.BOX_BY));
            item.setBoxTime(rs.getTimestamp(WhApvAllocationItemDBField.BOX_TIME));
            item.setBoardStatus(rs.getInt(WhApvAllocationItemDBField.BOARD_STATUS));
            item.setBoardNo(rs.getString(WhApvAllocationItemDBField.BOARD_NO));
            item.setBoardBy(rs.getObject(WhApvAllocationItemDBField.BOARD_BY) == null ? null
                    : rs.getInt(WhApvAllocationItemDBField.BOARD_BY));
            item.setBoardTime(rs.getTimestamp(WhApvAllocationItemDBField.BOARD_TIME));
            item.setLoadStatus(rs.getInt(WhApvAllocationItemDBField.LOAD_STATUS));
            item.setLoadBy(rs.getObject(WhApvAllocationItemDBField.LOAD_BY) == null ? null
                    : rs.getInt(WhApvAllocationItemDBField.LOAD_BY));
            item.setLoadTime(rs.getTimestamp(WhApvAllocationItemDBField.LOAD_TIME));
            item.setUpStatus(rs.getInt(WhApvAllocationItemDBField.UP_STATUS));
            item.setUpNum(rs.getObject(WhApvAllocationItemDBField.UP_NUM) == null ? null
                    : rs.getInt(WhApvAllocationItemDBField.UP_NUM));
            item.setUpBy(rs.getObject(WhApvAllocationItemDBField.UP_BY) == null ? null
                    : rs.getInt(WhApvAllocationItemDBField.UP_BY));
            item.setUpTime(rs.getTimestamp(WhApvAllocationItemDBField.UP_TIME));
            item.setSurplusQuantity(rs.getObject(WhApvAllocationItemDBField.SURPLUS_QUANTITY) == null ? 0
                    : rs.getInt(WhApvAllocationItemDBField.SURPLUS_QUANTITY));
            item.setLocationNumber(rs.getString(WhApvAllocationItemDBField.LOCATION_NUMBER));
            item.setImageUrl(rs.getString(WhApvAllocationItemDBField.IMAGE_URL));
            item.setIsAudit(rs.getObject(WhApvAllocationItemDBField.IS_AUDIT) == null ? null
                    : rs.getBoolean(WhApvAllocationItemDBField.IS_AUDIT));
            item.setHistoryPickNum(rs.getObject(WhApvAllocationItemDBField.HISTORY_PICK_NUM) == null ? null
                    : rs.getInt(WhApvAllocationItemDBField.HISTORY_PICK_NUM));
            item.setPutStatus(rs.getObject(WhApvAllocationItemDBField.PUT_STATUS) == null ? null
                    : rs.getInt(WhApvAllocationItemDBField.PUT_STATUS));
            item.setPutBy(rs.getObject(WhApvAllocationItemDBField.PUT_BY) == null ? null
                    : rs.getInt(WhApvAllocationItemDBField.PUT_BY));
            item.setPutTime(rs.getTimestamp(WhApvAllocationItemDBField.PUT_TIME));

            WhSku whSku = new WhSku();
            whSku.setSku(rs.getString("whSku.sku"));
            whSku.setName(rs.getString("whSku.name"));
            whSku.setWarehouseId(rs.getObject("whSku.warehouse_id") == null ? null : rs.getInt("whSku.warehouse_id"));
            whSku.setLocationNumber(rs.getString("whSku.location_number"));
            whSku.setWeight(rs.getObject("whSku.weight") == null ? null : rs.getDouble("whSku.weight"));
            whSku.setLength(rs.getObject("whSku.length") == null ? null : rs.getDouble("whSku.length"));
            whSku.setWidth(rs.getObject("whSku.width") == null ? null : rs.getDouble("whSku.width"));
            whSku.setHeight(rs.getObject("whSku.height") == null ? null : rs.getDouble("whSku.height"));

            item.setWhSku(whSku);

            entity.addAllocationItems(item);
        }

        return isExist ? null : entity;
    }

}
