package com.estone.allocation.dao.mapper;

public interface WhApvAllocationDemandItemDBField {
    String ID = "demand_item.id";

    String TASK_ID = "demand_item.task_id";

    String APV_ID = "demand_item.apv_id";

    String APV_NO = "demand_item.apv_no";

    String CREATE_BY = "demand_item.create_by";

    String CREATED_DATE = "demand_item.created_date";

    String IS_PUSH = "demand_item.is_push";

	String APV_STATUS = "demand_item.apv_status";

    String APV_ITEM = "demand_item.apv_item";

    String APV_DATA_JSON = "demand_item.apv_data_json";
}