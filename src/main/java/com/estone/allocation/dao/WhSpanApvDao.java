package com.estone.allocation.dao;

import com.estone.allocation.bean.WhSpanApv;
import com.estone.allocation.bean.WhSpanApvQueryCondition;
import com.whq.tool.component.Pager;
import java.util.List;

public interface WhSpanApvDao {
    int queryWhSpanApvCount(WhSpanApvQueryCondition query);

    List<WhSpanApv> queryWhSpanApvList();

    List<WhSpanApv> queryWhSpanApvList(WhSpanApvQueryCondition query, Pager pager);

    WhSpanApv queryWhSpanApv(Integer primaryKey);

    WhSpanApv queryWhSpanApv(WhSpanApvQueryCondition query);

    void createWhSpanApv(WhSpanApv entity);

    void batchCreateWhSpanApv(List<WhSpanApv> entityList);

    void batchUpdateWhSpanApv(List<WhSpanApv> entityList);

    void deleteWhSpanApv(Integer primaryKey);

    void updateWhSpanApv(WhSpanApv entity);
}