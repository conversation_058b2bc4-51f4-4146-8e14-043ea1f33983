package com.estone.allocation.domain;

import java.util.ArrayList;
import java.util.List;

import com.estone.allocation.bean.AllocationDownloadQueryCondition;
import com.estone.allocation.bean.WhApvAllocation;
import com.estone.allocation.bean.WhApvAllocationQueryCondition;
import com.estone.allocation.bean.WhApvAllocationUpItem;
import com.whq.tool.component.Pager;

public class WhApvAllocationDo {

    private WhApvAllocation whApvAllocation;

    private WhApvAllocationQueryCondition query = new WhApvAllocationQueryCondition();

    private List<WhApvAllocation> whApvAllocations = new ArrayList<WhApvAllocation>();

    private Pager page = new Pager();

    /** 调拨单状态 */
    private String statusSelectJosn;

    /** 订单调拨单状态 */
    private String statusOrderSelectJson;

    private AllocationDownloadQueryCondition downloadQuery = new AllocationDownloadQueryCondition();

    // 调拨上架详情
    private List<WhApvAllocationUpItem> allocationUpItems = new ArrayList<>();

    public List<WhApvAllocationUpItem> getAllocationUpItems() {
        return allocationUpItems;
    }

    public void setAllocationUpItems(List<WhApvAllocationUpItem> allocationUpItems) {
        this.allocationUpItems = allocationUpItems;
    }

    public AllocationDownloadQueryCondition getDownloadQuery() {
        return downloadQuery;
    }

    public void setDownloadQuery(AllocationDownloadQueryCondition downloadQuery) {
        this.downloadQuery = downloadQuery;
    }

    public WhApvAllocation getWhApvAllocation() {
        return whApvAllocation;
    }

    public void setWhApvAllocation(WhApvAllocation whApvAllocation) {
        this.whApvAllocation = whApvAllocation;
    }

    public WhApvAllocationQueryCondition getQuery() {
        return query;
    }

    public void setQuery(WhApvAllocationQueryCondition query) {
        this.query = query;
    }

    public List<WhApvAllocation> getWhApvAllocations() {
        return whApvAllocations;
    }

    public void setWhApvAllocations(List<WhApvAllocation> whApvAllocations) {
        this.whApvAllocations = whApvAllocations;
    }

    public Pager getPage() {
        return page;
    }

    public void setPage(Pager page) {
        this.page = page;
    }

    public String getStatusSelectJosn() {
        return statusSelectJosn;
    }

    public void setStatusSelectJosn(String statusSelectJosn) {
        this.statusSelectJosn = statusSelectJosn;
    }

    public String getStatusOrderSelectJson() {
        return statusOrderSelectJson;
    }

    public void setStatusOrderSelectJson(String statusOrderSelectJson) {
        this.statusOrderSelectJson = statusOrderSelectJson;
    }

}
