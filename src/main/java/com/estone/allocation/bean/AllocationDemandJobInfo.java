package com.estone.allocation.bean;

import lombok.Data;

/**
 * xxl-job info
 *
 * <AUTHOR>  2016-1-12 18:25:49
 */
@Data
public class AllocationDemandJobInfo {

	private int id = 3121;				// 主键ID
	private int jobGroup = 5024;		// 执行器主键ID
	private String jobDesc = "两仓自动调拨需求";
	private String author = "fangxin";		// 负责人
	private String executorRouteStrategy = "FIRST";	// 执行器路由策略
	private String executorHandler = "CalcAutoAlloctionDemandJobHandler";		    // 执行器，任务Handler名称
	private String executorBlockStrategy = "COVER_EARLY";	// 阻塞处理策略
	private String jobCron = "0 0 19 * * ?";
}
