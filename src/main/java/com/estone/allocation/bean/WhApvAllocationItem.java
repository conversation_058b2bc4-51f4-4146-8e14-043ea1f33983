package com.estone.allocation.bean;

import com.alibaba.fastjson.JSON;
import com.estone.sku.bean.WhSku;
import lombok.Data;
import org.apache.commons.lang.ArrayUtils;
import org.apache.commons.lang.StringUtils;

import java.io.Serializable;
import java.sql.Timestamp;

@Data
public class WhApvAllocationItem implements Serializable, Comparable<WhApvAllocationItem> {
    /**
     * 
     */
    private static final long serialVersionUID = 1L;
    /** 主键 */
    private Integer allocationItemId;
    /** 调拨单id */
    private Integer allocationId;
    /** 调拨单号 */
    private String allocationNo;
    /** sku */
    private String sku;
    /**
     * 库存ID
     */
    private Integer stockId;
    /**
     * 保质期售后结算信息
     */
    private String jsonStr;
    /** 调拨数量 */
    private Integer allocationNum;
    /** 创建人 */
    private Integer createBy;
    /** 创建时间 */
    private Timestamp createTime;

    /** 装箱状态 0=未装箱 1=已装箱 */
    private Integer boxStatus;
    /** 装箱数量 */
    private Integer boxNum;
    /** 箱号 */
    private String boxNo;
    /** 装箱人 */
    private Integer boxBy;
    /** 装箱时间 */
    private Timestamp boxTime;

    /** 拣货状态 0=未拣货 1=拣货中 2=拣货完成 */
    private Integer pickStatus;
    /** 拣货数量 */
    private Integer pickNum;
    /** 拣货人 */
    private Integer pickBy;
    /** 拣货时间 */
    private Timestamp pickTime;

    /** 打板状态 0=未打板 1=已打板 */
    private Integer boardStatus;
    /** 打板号 */
    private String boardNo;
    /** 打板人 */
    private Integer boardBy;
    /** 打板时间 */

    private Timestamp boardTime;
    /** 装车状态 0=未装车 1=已装车 */
    private Integer loadStatus;
    /** 装车人 */
    private Integer loadBy;
    /** 装车时间 */

    private Timestamp loadTime;
    /** 上架状态 0=未上架 1=部分上架 2=已上架 */
    private Integer upStatus;
    /** 上架数量 */
    private Integer upNum;
    /** 上架人 */
    private Integer upBy;
    /** 上架时间 */
    private Timestamp upTime;
    /** 条目是否审核 */
    private Boolean isAudit;
    /** 历史拣货数量 */
    private Integer historyPickNum;
    /** 点数状态 */
    private Integer putStatus;
    /** 点数人 */
    private Integer putBy;
    /** 点数时间 */
    private Timestamp putTime;

    private Integer isPackingAgain;// 是否二次包装
    private String locationNo;// 仓库新系统库位
    private Boolean useOlderPackage;// 是否带原包装发货 1:是，0:否
    private String productFlag;// 产品标识 0:易伤 ，1:易损，2:原装
    private Integer inedQuantity;// 已入库

    // 可用库存
    private Integer surplusQuantity;
    // sku名称
    private String skuName;
    // 库位
    private String locationNumber;
    // 商品图片
    private String imageUrl;

    /** 修改的可用库存 */
    private Integer allotSurplusQuantity;
    /** 修改的拣货库存 */
    private Integer allotPickQuantity;
    /** 修改的仓库库存 */
    private Integer alloQuantity;

    private WhSku whSku;

    private Integer apvId;

    private String apvNo;

    private String subTaskNo; // 子任务号

    private Integer loadBackQuantity;// 装车时退回的可用库存

    // 未返架库内返架单数
    private Integer waitReturnQuantity;
    // 未返架海外退件单数
    private Integer waitAbroadReturnQuantity;
    // 未返架调拨单数
    private Integer waitAllocationQuantity;
    // 未上架入库单数
    private Integer waitUpQuantity;
    // 未出库订单数
    private Integer waitDeliveryQuantity;
    // sku仓库属性
    private Integer warehouseId;

    private String skuQcCategoryDesc; //产品系统质检备注

    private String skuFeature; //仓库质检备注

    private Integer inventoryStatus;// 是否盘点装修:0未盘点，1已盘点

    /**
     * 条形码
     */
    private String skuBarCode;
    private AfterSaleAndExpInfo afterSaleAndExpInfo;

    /****
     * 2A > 2B > 2C 2A-01>2A-02
     * 
     */
    @Override
    public int compareTo(WhApvAllocationItem o) {
        // 旧版本
        String StockLocation1 = transformStockLocation(locationNumber);
        String StockLocation2 = transformStockLocation(o.getLocationNumber());

        String[] str1s = StockLocation1.split("-");
        if (str1s.length == 4) {
            if (str1s[0].length() == 2) {
                str1s[0] = str1s[0].substring(0, 1) + "0" + str1s[0].substring(1, 2);
            }
            if (str1s[1].length() == 1) {
                str1s[1] = "0" + str1s[1];
            }
            if (str1s[2].length() == 1) {
                str1s[2] = "0" + str1s[2];
            }
            if (str1s[3].length() == 1) {
                str1s[3] = "0" + str1s[3];
            }
            StockLocation1 = str1s[0] + "-" + str1s[1] + "-" + str1s[2] + "-" + str1s[3];
        }

        String[] str2s = StockLocation2.split("-");
        if (str2s.length == 4) {
            if (str2s[0].length() == 2) {
                str2s[0] = str2s[0].substring(0, 1) + "0" + str2s[0].substring(1, 2);
            }
            if (str2s[1].length() == 1) {
                str2s[1] = "0" + str2s[1];
            }
            if (str2s[2].length() == 1) {
                str2s[2] = "0" + str2s[2];
            }
            if (str2s[3].length() == 1) {
                str2s[3] = "0" + str2s[3];
            }
            StockLocation2 = str2s[0] + "-" + str2s[1] + "-" + str2s[2] + "-" + str2s[3];
        }

        int result = StockLocation1.compareTo(StockLocation2);

        if (result == 0) {
            return sku.compareTo(o.getSku());
        }
        return result;
    }

    // changed by liuguolin at 2017-12-26 新增新仓库对比规则。2G>2A>2B>2C>2D>2F>2E
    String[] newAreaSeqArr = new String[] { "2K", "2A", "2B", "2C", "2D", "2F", "2E" };
    String[] newAreaReplaceArr = new String[] { "2A", "2B", "2C", "2D", "2E", "2F", "2G" };
    // 旧仓库规则
    String[] areaSeqArr = new String[] {"Y", "D", "E", "F", "G", "H", "J", "K", "L", "M"};;
    String[] areaReplaceArr = new String[] {"A", "B", "C", "D", "E", "F", "G", "H", "I", "J"};

    private String transformStockLocation(String stockLocation) {
        String transformStockLocation = null;
        if (StringUtils.isNotEmpty(stockLocation)) {
            stockLocation = stockLocation.toUpperCase();
            String areaCode = stockLocation.substring(0, 1);
            if (ArrayUtils.contains(areaSeqArr, areaCode)) {
                int areaCodeIndex = ArrayUtils.indexOf(areaSeqArr, areaCode);
                String areaReplaceCode = areaReplaceArr[areaCodeIndex];
                stockLocation = stockLocation.replaceFirst(areaCode, areaReplaceCode);
                transformStockLocation = stockLocation;
            }
            else if ("2".equals(areaCode)) {
                String newAreaCode = stockLocation.substring(0, 2);
                if (ArrayUtils.contains(newAreaSeqArr, newAreaCode)) {
                    int newAreaCodeIndex = ArrayUtils.indexOf(newAreaSeqArr, newAreaCode);
                    String newAreaReplaceCode = newAreaReplaceArr[newAreaCodeIndex];
                    stockLocation = stockLocation.replaceFirst(newAreaCode, newAreaReplaceCode);
                    return stockLocation;
                }
            }
        }
        transformStockLocation = stockLocation;
        if (transformStockLocation == null) {
            transformStockLocation = "Z";
        }
        return transformStockLocation;
    }
    @Data
    public static class AfterSaleAndExpInfo {
        //{"batchNo":"EXP230731000005","expDate":"2023-11-27","batchQty":10,"afterSaleId":24,"afterSaleQty":10}
        private String batchNo;
        private String expDate;
        private String proDate;
        private Integer days;
        private Integer batchQty;
        private Integer afterSaleId;
        private Integer afterSaleQty;
        private String vendorCode;

        private String vendorName;
    }

    public AfterSaleAndExpInfo getAfterSaleAndExpInfo() {
        if (StringUtils.isBlank(jsonStr))
            return null;
        return JSON.parseObject(jsonStr, AfterSaleAndExpInfo.class);
    }
}
