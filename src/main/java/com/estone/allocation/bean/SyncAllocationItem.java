package com.estone.allocation.bean;

import java.sql.Timestamp;

public class SyncAllocationItem {

    /** 主键 */
    private Integer allocationItemId;
    /** 调拨单号 */
    private String allocationNo;
    /** sku */
    private String sku;
    /** 上架状态 0=未上架 1=部分上架 2=已上架 */
    private Integer upStatus;
    /** 上架数量 */
    private Integer upNum;
    /** 上架人 */
    private Integer upBy;
    /** 上架时间 */
    private Timestamp upTime;

    public Integer getAllocationItemId() {
        return allocationItemId;
    }

    public void setAllocationItemId(Integer allocationItemId) {
        this.allocationItemId = allocationItemId;
    }

    public String getAllocationNo() {
        return allocationNo;
    }

    public void setAllocationNo(String allocationNo) {
        this.allocationNo = allocationNo;
    }

    public String getSku() {
        return sku;
    }

    public void setSku(String sku) {
        this.sku = sku;
    }

    public Integer getUpStatus() {
        return upStatus;
    }

    public void setUpStatus(Integer upStatus) {
        this.upStatus = upStatus;
    }

    public Integer getUpNum() {
        return upNum;
    }

    public void setUpNum(Integer upNum) {
        this.upNum = upNum;
    }

    public Integer getUpBy() {
        return upBy;
    }

    public void setUpBy(Integer upBy) {
        this.upBy = upBy;
    }

    public Timestamp getUpTime() {
        return upTime;
    }

    public void setUpTime(Timestamp upTime) {
        this.upTime = upTime;
    }

}
