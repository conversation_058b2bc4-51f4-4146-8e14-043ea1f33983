package com.estone.allocation.util;

import java.util.HashMap;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;

import com.alibaba.fastjson.JSON;
import com.estone.common.enums.WarehousePropertyEnum;
import com.estone.common.util.CacheUtils;
import com.estone.common.util.SpringUtils;
import com.estone.system.param.bean.SystemParam;
import com.estone.warehouse.bean.WhWarehouse;
import com.estone.warehouse.service.WhWarehouseService;
import com.whq.tool.exception.BusinessException;

import lombok.extern.slf4j.Slf4j;

@Slf4j
public class WarehouseProperties {

    /** 本地仓库 */
    private Integer localWarehouseId;

    /** 库存调拨->推送调拨单至目的仓url */
    private Map<Integer, String> warehouseIpUrl = new HashMap<Integer, String>();

    public Integer getLocalWarehouseId() {
        return localWarehouseId;
    }

    public void setLocalWarehouseId(Integer localWarehouseId) {
        this.localWarehouseId = localWarehouseId;
    }

    public Map<Integer, String> getWarehouseIpUrl() {
        return warehouseIpUrl;
    }

    public void setWarehouseIpUrl(Map<Integer, String> warehouseIpUrl) {
        this.warehouseIpUrl = warehouseIpUrl;
    }

    /**
     * @Description: 根据仓库编号获取仓库ip
     *
     * @param warehouseId
     * @param properties
     * @return
     * @Author: Administrator
     * @Date: 2019/04/12
     * @Version: 0.0.1
     */
    public static String getIp(Integer warehouseId, WarehouseProperties properties) {
        if (null != properties) {
            return properties.getWarehouseIpUrl().get(warehouseId);
        }
        return null;
    }

    /**
     * 通用获取地址
     *
     * @param api
     * @return
     */
    public static String getApiUrl(String api) {
        WarehouseProperties properties = WarehouseProperties.getWarehouseProperties();
        if (null == properties) {
            throw new BusinessException("ALLOCATION.WAREHOUSE_PROPERTIES paramValue parseObject is null!");
        }
        if (StringUtils.isBlank(api))
            return null;
        Integer warehouseId = CacheUtils.getLocalWarehouseId();
        if (WarehousePropertyEnum.HHD.intCode() == warehouseId) {
            warehouseId = WarehousePropertyEnum.NN.intCode();
        } else if (WarehousePropertyEnum.NN.intCode() == warehouseId) {
            warehouseId = WarehousePropertyEnum.HHD.intCode();
        }
        String ip = getIp(warehouseId, properties);
        if (StringUtils.isNotBlank(ip)) {
            return ip.concat(api);
        }
        return null;
    }

    /**
     * @Description: 查询发货仓已完成的库存调拨单，在目的仓入库了多少url
     *
     * @param warehouseId
     * @param properties
     * @return
     * @Author: Administrator
     * @Date: 2019/04/12
     * @Version: 0.0.1
     */
    public static String getQueryCheckInUrl(Integer warehouseId, WarehouseProperties properties) {
        String ip = getIp(warehouseId, properties);
        if (StringUtils.isNotBlank(ip)) {
            return ip.concat(InventoryProperties.queryCheckInUrl);
        }
        return null;
    }

    /**
     * @Description: 查询已完成的订单调拨单，发货单apv是否在调出仓已完成url
     *
     * @param warehouseId
     * @param properties
     * @return
     * @Author: Administrator
     * @Date: 2019/04/12
     * @Version: 0.0.1
     */
    public static String getQueryApvNosOnLoadUrl(Integer warehouseId, WarehouseProperties properties) {
        String ip = getIp(warehouseId, properties);
        if (StringUtils.isNotBlank(ip)) {
            return ip.concat(InventoryProperties.queryApvNosOnLoadUrl);
        }
        return null;
    }

    /**
     * @Description: 回退跨仓可用库存（URL）
     *
     * @param warehouseId
     * @param properties
     * @return
     * @Author: Administrator
     * @Date: 2019/04/12
     * @Version: 0.0.1
     */
    public static String getRollbackSurplusUrl(Integer warehouseId, WarehouseProperties properties) {
        String ip = getIp(warehouseId, properties);
        if (StringUtils.isNotBlank(ip)) {
            return ip.concat(InventoryProperties.rollbackSurplusUrl);
        }
        return null;
    }

    /**
     * @Description: 库存调拨->推送调拨单至目的仓url
     *
     * @param warehouseId
     * @param properties
     * @return
     * @Author: Administrator
     * @Date: 2019/04/12
     * @Version: 0.0.1
     */
    public static String getDestWarehouseIdStockUrl(Integer warehouseId, WarehouseProperties properties) {
        String ip = getIp(warehouseId, properties);
        if (StringUtils.isNotBlank(ip)) {
            return ip.concat(InventoryProperties.destWarehouseIdStockUrl);
        }
        return null;
    }

    /**
     * @Description: 订单调拨->生成跨仓拣货任务推送调拨需求至目的仓url
     *
     * @param warehouseId
     * @param properties
     * @return
     * @Author: Administrator
     * @Date: 2019/04/12
     * @Version: 0.0.1
     */
    public static String getDestWarehouseIdOrderUrl(Integer warehouseId, WarehouseProperties properties) {
        String ip = getIp(warehouseId, properties);
        if (StringUtils.isNotBlank(ip)) {
            return ip.concat(InventoryProperties.destWarehouseIdOrderUrl);
        }
        return null;
    }

    /**
     * @Description: 订单调拨->跨仓订单库存匹配url
     *
     * @param warehouseId
     * @param properties
     * @return
     * @Author: Administrator
     * @Date: 2019/04/12
     * @Version: 0.0.1
     */
    public static String getOutOfStockWarehouseUrl(Integer warehouseId, WarehouseProperties properties) {
        String ip = getIp(warehouseId, properties);
        if (StringUtils.isNotBlank(ip)) {
            return ip.concat(InventoryProperties.outOfStockWarehouseUrl);
        }
        return null;
    }

    public static String getUpdateSkuWarehouseUrl(Integer warehouseId, WarehouseProperties properties) {
        String ip = getIp(warehouseId, properties);
        if (StringUtils.isNotBlank(ip)) {
            return ip.concat(InventoryProperties.updateSkuWarehouseUrl);
        }
        return null;
    }

    public static String getQueryAlloctionAssetPriceUrl(Integer warehouseId, WarehouseProperties properties) {
        String ip = getIp(warehouseId, properties);
        if (StringUtils.isNotBlank(ip)) {
            return ip.concat(InventoryProperties.queryAlloctionAssetPriceUrl);
        }
        return null;
    }

    // 获取调拨相关参数
    public static WarehouseProperties getWarehouseProperties() {
        try {
            SystemParam systemParam = CacheUtils.SystemParamGet("ALLOCATION.WAREHOUSE_PROPERTIES");

            if (null == systemParam) {
                return null;
            }

            String paramValue = systemParam.getParamValue();
            if (StringUtils.isBlank(paramValue)) {
                return null;
            }

            WarehouseProperties properties = JSON.parseObject(paramValue, WarehouseProperties.class);

            WhWarehouseService whWarehouseService = SpringUtils.getBean(WhWarehouseService.class);
            WhWarehouse warehouse = whWarehouseService.queryOriginalWhWarehouse(true);
            if (null != warehouse && null != warehouse.getIsDefault() && warehouse.getIsDefault()) {
                if (null != properties) {
                    properties.setLocalWarehouseId(warehouse.getId());
                }
            }

            return properties;
        }
        catch (Exception e) {
            log.error(e.getMessage(), e);
            return null;
        }
    }

    /**
     * @Description: 订单调拨->生成跨仓拣货任务推送调拨需求Item至目的仓url
     *
     * @param warehouseId
     * @param properties
     * @return
     * @Author: Administrator
     * @Date: 2019/04/12
     * @Version: 0.0.1
     */
    public static String getDestWarehouseIdOrderItemUrl(Integer warehouseId, WarehouseProperties properties) {
        String ip = getIp(warehouseId, properties);
        if (StringUtils.isNotBlank(ip)) {
            return ip.concat(InventoryProperties.destWarehouseIdOrderItemUrl);
        }
        return null;
    }

    /**
     * 推送订单调拨装车数量到老仓URL
     * @param warehouseId
     * @param properties
     * @return
     */
    public static String getPushLoadDataToOldWarehouseUrl(Integer warehouseId, WarehouseProperties properties) {
        String ip = getIp(warehouseId, properties);
        if (StringUtils.isNotBlank(ip)) {
            return ip.concat(InventoryProperties.pushLoadDataToOldWarehouseUrl);
        }
        return null;
    }

    /**
     * @Description: 根据调拨单号查询调拨单url
     *
     * @param warehouseId
     * @param properties
     * @return
     * @Author: Administrator
     * @Date: 2019/07/08
     * @Version: 0.0.1
     */
    public static String getPushAllocationStatusUrl(Integer warehouseId, WarehouseProperties properties) {
        String ip = getIp(warehouseId, properties);
        if (StringUtils.isNotBlank(ip)) {
            return ip.concat(InventoryProperties.pushAllocationStatusUrl);
        }
        return null;
    }

    public static void main(String[] args) {
        WarehouseProperties warehouseProperties = new WarehouseProperties();

        warehouseProperties.setLocalWarehouseId(1);// 发货仓

        warehouseProperties.getWarehouseIpUrl().put(1, "http://*************:8080/wms");// 汉海达
        warehouseProperties.getWarehouseIpUrl().put(2, "http://*************:8080/wms");// 美景仓

        String paramValue = JSON.toJSONString(warehouseProperties);
        WarehouseProperties properties = JSON.parseObject(paramValue, WarehouseProperties.class);
        // 调拨系统参数配置： ALLOCATION.WAREHOUSE_PROPERTIES
        System.out.println(JSON.toJSONString(properties));
        String url = WarehouseProperties.getDestWarehouseIdOrderUrl(2, properties);
        System.out.println(url);
    }

}
