package com.estone.model.bo;

import java.io.Serializable;

public class EbayDevAccount implements Serializable {

	private static final long serialVersionUID = 1L;

	private Long id;

	private String devid;

	private String devname;

	private String devpassword;

	private String prodname;

	private String prodappid;

	private String prodcertid;

	private String prodruname;

	private String soapserver = "https://api.ebay.com/wsapi";

	private String epsserver = "https://api.ebay.com/ws/api.dll";

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public String getDevid() {
		return devid;
	}

	public void setDevid(String devid) {
		this.devid = devid;
	}

	public String getDevname() {
		return devname;
	}

	public void setDevname(String devname) {
		this.devname = devname;
	}

	public String getDevpassword() {
		return devpassword;
	}

	public void setDevpassword(String devpassword) {
		this.devpassword = devpassword;
	}

	public String getProdname() {
		return prodname;
	}

	public void setProdname(String prodname) {
		this.prodname = prodname;
	}

	public String getProdappid() {
		return prodappid;
	}

	public void setProdappid(String prodappid) {
		this.prodappid = prodappid;
	}

	public String getProdcertid() {
		return prodcertid;
	}

	public void setProdcertid(String prodcertid) {
		this.prodcertid = prodcertid;
	}

	public String getProdruname() {
		return prodruname;
	}

	public void setProdruname(String prodruname) {
		this.prodruname = prodruname;
	}

	public String getSoapserver() {
		return soapserver;
	}

	public void setSoapserver(String soapserver) {
		this.soapserver = soapserver;
	}

	public String getEpsserver() {
		return epsserver;
	}

	public void setEpsserver(String epsserver) {
		this.epsserver = epsserver;
	}

}
