package com.estone.model.bo;

import java.io.Serializable;
import java.util.Date;

public class User implements Serializable {
    private static final long serialVersionUID = 4166207046376701349L;

    private Long id;

    private String password;

    private String userName;

    private String encryptedPassword;

    private String encryptedUserName;

    private String imaccount;

    private Long employeeId;

    private Long managerId;

    private Boolean inactive;

    private Date lastLoginTime;

    private String groupName;

    private Long saleChannelAccountGroupId;
    
    private String employeeName;
    
    private String empNo;
    
    private String title;
    

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getEmpNo() {
        return empNo;
    }

    public void setEmpNo(String empNo) {
        this.empNo = empNo;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password == null ? null : password.trim();
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName == null ? null : userName.trim();
    }

    public String getEncryptedPassword() {
        return encryptedPassword;
    }

    public void setEncryptedPassword(String encryptedPassword) {
        this.encryptedPassword = encryptedPassword == null ? null : encryptedPassword.trim();
    }

    public String getEncryptedUserName() {
        return encryptedUserName;
    }

    public void setEncryptedUserName(String encryptedUserName) {
        this.encryptedUserName = encryptedUserName == null ? null : encryptedUserName.trim();
    }

    public String getImaccount() {
        return imaccount;
    }

    public void setImaccount(String imaccount) {
        this.imaccount = imaccount == null ? null : imaccount.trim();
    }

    public Long getEmployeeId() {
        return employeeId;
    }

    public void setEmployeeId(Long employeeId) {
        this.employeeId = employeeId;
    }

    public Long getManagerId() {
        return managerId;
    }

    public void setManagerId(Long managerId) {
        this.managerId = managerId;
    }

    public Boolean getInactive() {
        return inactive;
    }

    public void setInactive(Boolean inactive) {
        this.inactive = inactive;
    }

    public Date getLastLoginTime() {
        return lastLoginTime;
    }

    public void setLastLoginTime(Date lastLoginTime) {
        this.lastLoginTime = lastLoginTime;
    }

    public String getGroupName() {
        return groupName;
    }

    public void setGroupName(String groupName) {
        this.groupName = groupName == null ? null : groupName.trim();
    }

    public Long getSaleChannelAccountGroupId() {
        return saleChannelAccountGroupId;
    }

    public void setSaleChannelAccountGroupId(Long saleChannelAccountGroupId) {
        this.saleChannelAccountGroupId = saleChannelAccountGroupId;
    }

    public String getEmployeeName() {
        return employeeName;
    }

    public void setEmployeeName(String employeeName) {
        this.employeeName = employeeName;
    }
}