package com.estone.transfer.action;

import static java.util.stream.Collectors.toList;

import java.io.OutputStream;
import java.util.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import com.estone.common.enums.ExportType;
import com.estone.common.util.*;
import com.estone.picking.domain.WhPickingTaskDo;
import com.estone.sku.bean.WhSku;
import com.estone.sku.bean.WhSkuQueryCondition;
import com.estone.sku.service.WhSkuService;
import com.estone.sku.service.WhUniqueSkuService;
import com.estone.transfer.bean.WhTransitReturn;
import com.estone.transfer.bean.WhTransitReturnItem;
import com.estone.transfer.bean.WhTransitReturnQueryCondition;
import com.estone.transfer.domain.WhTransitReturnDo;
import com.estone.transfer.service.WhTransitReturnService;
import com.estone.warehouse.enums.ReturnStatus;
import com.whq.tool.action.BaseController;
import com.whq.tool.component.Pager;
import com.whq.tool.component.StatusCode;
import com.whq.tool.json.ResponseJson;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Controller
@RequestMapping(value = "transit/return")
public class WhTransitReturnController extends BaseController {
    @Resource
    private WhTransitReturnService whTransitReturnService;

    @Resource
    private WhUniqueSkuService whUniqueSkuService;

    @Resource
    private WhSkuService whSkuService;

    @RequestMapping(method = { RequestMethod.GET })
    public String init(@ModelAttribute("domain") WhTransitReturnDo domain) {
        initFormData(domain);
//        queryWhTransitReturns(domain);
        return "transfer/transferReturnList";
    }

    private void initFormData(@ModelAttribute("domain") WhTransitReturnDo domain) {
        domain.setStatusList(ReturnStatus.values());

        // 是否有差异
        List<String[]> isQuantityDiffList = new ArrayList<>();
        isQuantityDiffList.add(new String[] { "true", "是" });
        isQuantityDiffList.add(new String[] { "false", "否" });
        domain.setIsQuantityDiffList(isQuantityDiffList);
    }

    private void queryWhTransitReturns(@ModelAttribute("domain") WhTransitReturnDo domain) {
        WhTransitReturnQueryCondition query = domain.getQuery();
        Pager page = domain.getPage();
        if (query == null) {
            query = new WhTransitReturnQueryCondition();
            domain.setQuery(query);
        }
        List<WhTransitReturn> whTransitReturns = whTransitReturnService.queryWhTransitReturnAndItems(query, page);
        domain.setWhTransitReturns(whTransitReturns);
    }

    @RequestMapping(value = "search", method = { RequestMethod.POST })
    public String search(@ModelAttribute("domain") WhTransitReturnDo domain) {
        initFormData(domain);
        queryWhTransitReturns(domain);
        return "transfer/transferReturnList";
    }

    @RequestMapping(value = "transferItems", method = { RequestMethod.GET })
    public String returnItems(@ModelAttribute("domain") WhTransitReturnDo domain, @RequestParam("id") int id) {
        WhTransitReturnQueryCondition query = new WhTransitReturnQueryCondition();
        query.setId(id);
        List<WhTransitReturn> whReturns = whTransitReturnService.queryWhTransitReturnAndItems(query, null);
        domain.setWhTransitReturn(whReturns.get(0));
        return "transfer/transferItemList";
    }


    @RequestMapping(value = "confirmTransferItems", method = {RequestMethod.GET})
    public String confirmReturnItems(@ModelAttribute("domain") WhTransitReturnDo domain, @RequestParam("id") int id) {
        WhTransitReturnQueryCondition query = new WhTransitReturnQueryCondition();
        query.setId(id);
        List<WhTransitReturn> whReturns = whTransitReturnService.queryWhTransitReturnAndItems(query, null);
        domain.setWhTransitReturn(whReturns.get(0));
        return "transfer/confirmTransferItemList";
    }

    @PostMapping(value = "confirm")
    @ResponseBody
    public ResponseJson confirm(@ModelAttribute("domain") WhTransitReturnDo domain) {
        ResponseJson responseJson = new ResponseJson(StatusCode.FAIL);
        WhTransitReturn transitReturn = domain.getWhTransitReturn();
        if (Objects.isNull(transitReturn) || StringUtils.isBlank(transitReturn.getBoxNo())
                || CollectionUtils.isEmpty(transitReturn.getReturnItems())) {
            responseJson.setMessage("无确认更新的数据");
            return responseJson;
        }
        return whTransitReturnService.confirmReturnOrder(transitReturn);
    }


    @RequestMapping(value = "create", method = { RequestMethod.GET })
    public String toCreateWhTransitReturn(@ModelAttribute("domain") WhTransitReturnDo domain) {
        WhTransitReturn whReturn = new WhTransitReturn();
        whReturn.setReturnNo(CreateTaskNoUtils.createTransitReturnNo());
        domain.setWhTransitReturn(whReturn);
        return "transfer/transferReturnAdd";
    }

    @RequestMapping(value = "create", method = { RequestMethod.POST })
    @ResponseBody
    public ResponseJson createWhTransitReturn(@ModelAttribute("domain") WhTransitReturnDo domain) {
        ResponseJson responseJson = new ResponseJson(StatusCode.FAIL);
        if (domain.getWhTransitReturn() == null) {
            responseJson.setMessage("参数为空！");
            return responseJson;
        }
        try {
            WhTransitReturnQueryCondition queryCondition = new WhTransitReturnQueryCondition();
            queryCondition.setReturnNo(domain.getWhTransitReturn().getReturnNo());
            WhTransitReturn whReturn = whTransitReturnService.queryWhTransitReturn(queryCondition);
            if (whReturn != null) {
                responseJson.setMessage(whReturn.getReturnNo() + "返架单号已存在,请不要重复提交");
                return responseJson;
            }
            if (CollectionUtils.isEmpty(domain.getWhTransitReturn().getReturnItems())) {
                responseJson.setMessage("没有要创建的明细单！");
                return responseJson;
            }
            WhTransitReturn domainReturn = domain.getWhTransitReturn();
            List<String> skuList = domainReturn.getReturnItems().stream().map(item -> item.getSku())
                    .collect(toList());

            if (skuList.size() == 0) {
                responseJson.setMessage("没有要创建的明细单！");
                return responseJson;
            }
            domainReturn.setStatus(ReturnStatus.WAIT.intCode());
            return whTransitReturnService.createWhReturnAndItem(skuList, Arrays.asList(domain.getWhTransitReturn()));
        } catch (RuntimeException e) {
            responseJson.setMessage(e.getMessage());
            return responseJson;
        }
    }

    @RequestMapping(value = "delete", method = { RequestMethod.GET })
    @ResponseBody
    public ResponseJson deleteWhTransitReturn(@ModelAttribute("domain") WhTransitReturnDo domain,
            @RequestParam("whTransitReturnId") Integer whTransitReturnId) {
        ResponseJson response = new ResponseJson();
        whTransitReturnService.deleteWhTransitReturn(whTransitReturnId);
        return response;
    }

    @RequestMapping(value = "apvSkuDetail", method = { RequestMethod.POST })
    @ResponseBody
    public ResponseJson getApvSkuDetail(@RequestParam("sku") String sku, @RequestParam("uuid") String uuid,
            @RequestParam("step") Integer step) {
        ResponseJson responseJson = new ResponseJson();
        responseJson.setStatus(StatusCode.FAIL);
        String erroeMes = whUniqueSkuService.checkScanUniqueSku(uuid, step);
        if (StringUtils.isNotBlank(erroeMes)) {
            responseJson.setMessage(erroeMes);
            return responseJson;
        }
        if (StringUtils.isNotBlank(sku)) {
            // 兼容sku编码和唯一码
            sku = CompatibleSkuUtils.getSku(sku);
            try {
                WhSkuQueryCondition query = new WhSkuQueryCondition();
                query.setSku(sku);
                WhSku whSku = whSkuService.queryWhSku(query);
                if (null == whSku) {
                    responseJson.setMessage("SKU不存在");
                    return responseJson;
                }

                if (!Integer.valueOf(CacheUtils.getLocalWarehouseId()).equals(whSku.getWarehouseId())) {
                    responseJson.setMessage("不是本仓SKU，不可返架");
                    return responseJson;
                }

                Map<String, Object> map = new HashMap<>();
                map.put("whApvAndWhSku", whTransitReturnService.updateApvSkuDetail(Arrays.asList(sku),uuid));
                responseJson.setStatus(StatusCode.SUCCESS);
                responseJson.setBody(map);
            }
            catch (RuntimeException e) {
                responseJson.setMessage("系统加库存不成功，原因：" + e.getMessage());
                return responseJson;
            }
        }
        return responseJson;
    }

    @RequestMapping(value = "downloadmode", method = { RequestMethod.GET })
    public String toSelectDownloadmode(@ModelAttribute("domain") WhPickingTaskDo domain) {
        return "warehouse/returnList_download_mode";
    }
    @RequestMapping(value = "downloadmode1", method = { RequestMethod.GET })
    public String toSelectDownloadmode1(@ModelAttribute("domain") WhPickingTaskDo domain) {
        return "warehouse/returnList_download_mode1";
    }

    private static String[] HEADERS = { "返架单号", "周转筐", "SKU", "站点", "店铺", "名称", "应返数量", "已返数量", "匹配库存", "返架员",
            "创建日期", "领取日期", "返架完成日期" };

    /**
     * 导出
     *
     * @param domain
     * @param exportType
     * @param ids
     * @param response
     */
    @SuppressWarnings("incomplete-switch")
    @RequestMapping(value = "download", method = { RequestMethod.POST })
    @ResponseBody
    public void download(@ModelAttribute("domain") WhTransitReturnDo domain,
            @RequestParam("exportType") String exportType,
            @RequestParam(value = "ids", required = false) List<Integer> ids, HttpServletResponse response) {

        WhTransitReturnQueryCondition query = domain.getQuery();

        List<WhTransitReturn> whReturns = null;

        ExportType exportTypeEnum = ExportType.build(exportType);

        if (query == null) {
            query = new WhTransitReturnQueryCondition();
        }
        // 兼容含有“=”的sku
        query.setSku(CompatibleSkuUtils.getSku(query.getSku()));

        // 查询导出的数据
        switch (exportTypeEnum) {
            case ALL: {
                query.setIds(null);
                whReturns = whTransitReturnService.queryWhTransitReturnAndItems(query, null);
            }
                break;
            case PAGE: {
                Pager page = domain.getPage();
                query.setIds(null);
                whReturns = whTransitReturnService.queryWhTransitReturnAndItems(query, page);

            }
                break;
            case CHECKED: {
                if (CollectionUtils.isNotEmpty(ids)) {
                    query = new WhTransitReturnQueryCondition();
                    query.setIds(ids);
                    whReturns = whTransitReturnService.queryWhTransitReturnAndItems(query, null);
                }
            }
                break;
        }

        OutputStream os = null;
        try {
            String fileName = "返架单" + System.currentTimeMillis() + ".xls";
            response.setContentType("application/ms-excel");
            response.setHeader("Content-Disposition",
                    "attachment; filename=" + java.net.URLEncoder.encode(fileName, "UTF-8"));
            os = response.getOutputStream();
            log.warn("download file name: " + fileName);
            final List<List<String>> apvData = new ArrayList<List<String>>();
            POIUtils.createExcel(HEADERS, whReturns, whReturn -> {

                apvData.clear();

                for (WhTransitReturnItem item : whReturn.getReturnItems()) {
                    List<String> apvlist = new ArrayList<String>(HEADERS.length);

                    apvlist.add(POIUtils.transferObj2Str(whReturn.getReturnNo()));
                    apvlist.add(POIUtils.transferObj2Str(whReturn.getBoxNo()));
                    apvlist.add(POIUtils.transferObj2Str(item.getSku()));
                    apvlist.add(POIUtils.transferObj2Str(item.getSite()));
                    apvlist.add(POIUtils.transferObj2Str(item.getSkuStore()));
                    apvlist.add(POIUtils.transferObj2Str(item.getWhSku().getName()));
                    apvlist.add(POIUtils.transferObj2Str(item.getQuantity()));
                    apvlist.add(POIUtils.transferObj2Str(item.getCompleteQuantity()));
                    apvlist.add(POIUtils.transferObj2Str(item.getStockQuantity()));
                    apvlist.add(
                            POIUtils.transferObj2Str(TaglibUtils.getEmployeeNameByUserId(whReturn.getReturnUser())));
                    apvlist.add(POIUtils.transferObj2Str(item.getCreationDate()));
                    apvlist.add(POIUtils.transferObj2Str(whReturn.getReceiveDate()));
                    apvlist.add(POIUtils.transferObj2Str(item.getLastUpdateDate()));
                    apvData.add(apvlist);
                }

                return apvData;

            }, true, os);
            log.warn("---task execute end---");
        }
        catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        finally {
            IOUtils.closeQuietly(os);
        }
    }


    @PostMapping(value = "exceptionComplete")
    @ResponseBody
    public ResponseJson exceptionComplete(@RequestParam("ids") List<Integer> ids) {
        ResponseJson responseJson = new ResponseJson(StatusCode.FAIL);
        if (CollectionUtils.isEmpty(ids)) {
            responseJson.setMessage("请选择要操作的单据!");
            return responseJson;
        }
        WhTransitReturnQueryCondition query = new WhTransitReturnQueryCondition();
        query.setIds(ids);
        // 返架中的可以异常完成
        query.getStatusList().add(ReturnStatus.ING.intCode());
        List<WhTransitReturn> whReturns = whTransitReturnService.queryWhTransitReturnAndItems(query, null);
        if (CollectionUtils.isEmpty(whReturns)) {
            responseJson.setMessage("没有找到相关的返架单或返架单状态不是返架中！");
            return responseJson;
        }
        for (WhTransitReturn whReturn : whReturns) {
            whTransitReturnService.completeWhReturn(whReturn.getId(), true);
        }
        responseJson.setMessage("异常完成成功！");
        responseJson.setStatus(StatusCode.SUCCESS);
        return responseJson;
    }
}