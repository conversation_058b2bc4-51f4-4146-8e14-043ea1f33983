package com.estone.transfer.enums;

/**
 * 
 * @ClassName: SmCodeStatus
 * @Description: 发货类型
 * <AUTHOR>
 * @date 2021年7月21日
 * @version 0.0.1
 *
 */
public enum SmCodeStatus {

    AIR_TRANSPORT("0", "空派"),

    OCEAN_SHIPPING("1", "海派"),

    EXPRESS("2", "快递"),

    TRUCK_TRANSPORT("3", "卡航"),

    TRAIN_TRANSPORT("4", "铁运"),

    SELF_DELIVERY("5", "自行发货"),

    BUY_OFF_DELIVERY("6", "揽收发货");

    private String code;

    private String name;

    private SmCodeStatus(String code, String name) {
        this.name = name;
        this.code = code;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Integer intCode() {
        return Integer.valueOf(this.code);
    }

    public boolean equals(Integer status) {
        return Integer.valueOf(this.code).equals(status);
    }

    public static SmCodeStatus build(String code) {
        SmCodeStatus[] values = values();

        for (SmCodeStatus type : values) {
            if (type.code.equals(code)) {
                return type;
            }
        }

        return null;
    }

    public static String getNameByCode(String code) {
        SmCodeStatus[] values = values();
        for (SmCodeStatus type : values) {
            if (type.code.equals(code)) {
                return type.getName();
            }
        }
        return null;
    }

}
