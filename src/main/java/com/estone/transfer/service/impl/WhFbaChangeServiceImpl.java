package com.estone.transfer.service.impl;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.estone.apv.bean.WhApvOutStockChain;
import com.estone.apv.bean.WhApvOutStockChainCancel;
import com.estone.apv.bean.WhApvOutStockChainCancelQueryCondition;
import com.estone.apv.bean.WhApvOutStockChainQueryCondition;
import com.estone.apv.service.WhApvOutStockChainCancelService;
import com.estone.apv.service.WhApvOutStockChainService;
import com.estone.common.enums.ExportType;
import com.estone.common.executors.ExecutorUtils;
import com.estone.common.util.CreateTaskNoUtils;
import com.estone.common.util.FtpUtils;
import com.estone.common.util.POIUtils;
import com.estone.common.util.SystemLogUtils;
import com.estone.statistics.enums.AssetOrderType;
import com.estone.temu.enums.CancelTypeEnum;
import com.estone.transfer.bean.*;
import com.estone.transfer.dao.WhFbaAllocationDao;
import com.estone.transfer.dao.WhFbaChangeDao;
import com.estone.transfer.enums.AsnPrepareStatus;
import com.estone.transfer.enums.FBAChangeDeliveryTypeEnum;
import com.estone.transfer.enums.FBAChangeOrderItemStatusEnum;
import com.estone.transfer.service.TransferStockService;
import com.estone.transfer.service.WhFbaAllocationItemService;
import com.estone.transfer.service.WhFbaChangeService;
import com.estone.transfer.service.WhTransitReturnService;
import com.estone.warehouse.enums.ReturnStatus;
import com.estone.warehouse.util.EasyExcelUtils;
import com.whq.tool.component.Pager;
import com.whq.tool.component.StatusCode;
import com.whq.tool.exception.BusinessException;
import com.whq.tool.exception.DBErrorCode;
import com.whq.tool.json.ResponseJson;
import com.whq.tool.sqler.SqlerException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.ArrayUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.io.File;
import java.sql.Timestamp;
import java.util.*;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service("whFbaChangeService")
@Slf4j
public class WhFbaChangeServiceImpl implements WhFbaChangeService {
    @Resource
    private WhFbaChangeDao whFbaChangeDao;
    @Resource
    private WhTransitReturnService whTransitReturnService;
    @Resource
    private TransferStockService transferStockService;
    @Resource
    private WhFbaAllocationItemService whFbaAllocationItemService;
    @Resource
    private WhFbaAllocationDao whFbaAllocationDao;
    @Resource
    private WhApvOutStockChainCancelService whApvOutStockChainCancelService;
    @Resource
    private WhApvOutStockChainService whApvOutStockChainService;

    /**
     * 最大线程数
     */
    public static final int EXPORT_EXCEL_POOL_MAX_THREADS = 1;
    /**
     * 导出excel时，每个sheet只导出500000条
     */
    public static final int SPLIT_SIZE = 500000;

    ThreadPoolExecutor transfeExcelPool = ExecutorUtils.newFixedThreadPool(10);


    @Override
    public WhFbaChange getWhFbaChange(Integer id) {
        WhFbaChange whFbaChange = whFbaChangeDao.queryWhFbaChange(id);
        return whFbaChange;
    }

    @Override
    public WhFbaChange getWhFbaChangeDetail(Integer id) {
        WhFbaChange whFbaChange = whFbaChangeDao.queryWhFbaChange(id);
        // 关联查询
        return whFbaChange;
    }

    @Override
    public WhFbaChange queryWhFbaChange(WhFbaChangeQueryCondition query) {
        Assert.notNull(query, "query is null!");
        WhFbaChange whFbaChange = whFbaChangeDao.queryWhFbaChange(query);
        return whFbaChange;
    }

    @Override
    public List<WhFbaChange> queryAllWhFbaChanges() {
        return whFbaChangeDao.queryWhFbaChangeList();
    }

    @Override
    public List<WhFbaChange> queryWhFbaChanges(WhFbaChangeQueryCondition query, Pager pager) {
        Assert.notNull(query, "query is null!");
        if (pager != null && pager.isQueryCount()) {
            int count = whFbaChangeDao.queryWhFbaChangeCount(query);
            pager.setTotalCount(count);
            if (count == 0) {
                return new ArrayList<WhFbaChange>();
            }
        }
        List<WhFbaChange> whFbaChanges = whFbaChangeDao.queryWhFbaChangeList(query, pager);
        return whFbaChanges;
    }

    @Override
    public void createWhFbaChange(WhFbaChange whFbaChange) {
        try {
            whFbaChangeDao.createWhFbaChange(whFbaChange);
        } catch (SqlerException e) {
            log.error(e.getMessage(), e);
            throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
        }
    }

    @Override
    public void batchCreateWhFbaChange(List<WhFbaChange> entityList) {
        if (CollectionUtils.isNotEmpty(entityList)) {
            try {
                whFbaChangeDao.batchCreateWhFbaChange(entityList);
            } catch (SqlerException e) {
                log.error(e.getMessage(), e);
                throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
            }
        }
    }

    @Override
    public void batchCreateWhFbaChangeByWhFbaAllocationItems(List<WhFbaAllocationItem> whFbaAllocationItems,Integer cancelType) {
        if (CollectionUtils.isEmpty(whFbaAllocationItems)) {
            log.info("无fbaAllocationItem明细");
            return;
        }
        Map<String, List<WhFbaAllocationItem>> fbaAllocationItemMap = whFbaAllocationItems.stream().collect(Collectors.groupingBy(f -> f.getFbaId() + f.getFnSku()));
        Map<Integer, List<WhFbaAllocationItem>> fbaIdMap = whFbaAllocationItems.stream()
                .collect(Collectors.groupingBy(WhFbaAllocationItem::getFbaId));
        WhFbaAllocationQueryCondition queryCondition = new WhFbaAllocationQueryCondition();
        queryCondition.setIds(new ArrayList<>(fbaIdMap.keySet()));
        queryCondition.setQueryWhAsnExtra(true);
        List<WhFbaAllocation> whFbaAllocations = whFbaAllocationDao.queryWhFbaAllocationAndItems(queryCondition, null);
        if (CollectionUtils.isEmpty(whFbaAllocations)) {
            log.info("无fbaAllocation明细");
            return;
        }

        Map<Integer, WhFbaAllocation> fbaAllocationIdMap = whFbaAllocations.stream().collect(Collectors.toMap(WhFbaAllocation::getId, Function.identity()));
        List<WhFbaChange> whFbaChanges = new ArrayList<>();
        for (Map.Entry<Integer, List<WhFbaAllocationItem>> allocationItems : fbaIdMap.entrySet()) {
            Integer fbaId = allocationItems.getKey();
            WhFbaAllocation whFbaAllocation = fbaAllocationIdMap.get(fbaId);
            if (Objects.isNull(whFbaAllocation)) {
                throw new BusinessException("id = " + fbaId + "的whFbaAllocation对象不存在!");
            }
            for (WhFbaAllocationItem whFbaAllocationItem : allocationItems.getValue()) {
                Integer pickQuantity = Optional.ofNullable(whFbaAllocationItem.getPickQuantity()).orElse(0);
                //判断fnSku有没有分箱
                List<WhFbaAllocationItem> whFbaAllocationItemList = fbaAllocationItemMap.get(whFbaAllocationItem.getFbaId() + whFbaAllocationItem.getFnSku());
                if (CollectionUtils.isNotEmpty(whFbaAllocationItemList) &&  whFbaAllocationItemList.size()>1 && whFbaAllocationItem.getBoxNo()!=null) {
                    Integer quantity =Optional.ofNullable(whFbaAllocationItemList.get(0).getPickQuantity()).orElse(0);
                    int sum = whFbaAllocationItemList.stream().mapToInt(i ->Optional.ofNullable(i.getLoadingQuantity()).orElse(0) ).sum();
                    if (sum>0 && sum == quantity) {
                        pickQuantity=whFbaAllocationItemList.stream().filter(f->whFbaAllocationItem.getBoxNo().equals(f.getBoxNo())).mapToInt(i ->Optional.ofNullable(i.getLoadingQuantity()).orElse(0) ).sum();
                    }
                    if (sum>0 && sum != quantity && whFbaAllocationItem.getBoxNo()!=1) {
                       continue;
                    }
                }

                WhFbaChange whFbaChange = new WhFbaChange();
                if (whFbaAllocationItem.getBoxNo()!=null) {
                    whFbaChange.setBoxNo(whFbaAllocationItem.getBoxNo()+"");
                }
                whFbaChange.setFbaAllocationItemId(whFbaAllocationItem.getId());
                whFbaChange.setFbaNo(whFbaAllocation.getFbaNo());
                whFbaChange.setShipmentId(whFbaAllocation.getShipmentId());
                whFbaChange.setFnSku(whFbaAllocationItem.getFnSku());
                whFbaChange.setSku(whFbaAllocationItem.getProductSku());
                whFbaChange.setFnQuantity(whFbaAllocationItem.getQuantity());
                whFbaChange.setQuantity(whFbaAllocationItem.getSkuQuantity());
                whFbaChange.setReturnQuantity(pickQuantity);

                TransferStock transferStock = getTransferStockByFbaAllocations(whFbaAllocation, whFbaAllocationItem);
                Integer pickReturnQuantity = Optional.ofNullable(transferStock.getPickReturnQuantity()).orElse(0)
                        + Optional.ofNullable(whFbaAllocationItem.getSkuQuantity()).orElse(0);
                whFbaChange.setPickReturnQuantity(pickReturnQuantity);

                whFbaChange.setChangeStatus(whFbaAllocation.getStatus());
                //发货单状态为待发货状态退回到带装箱状态
                if (AsnPrepareStatus.WAITING_DELIVER.intCode().equals(whFbaAllocation.getStatus())) {
                    whFbaChange.setStatus(AsnPrepareStatus.WAITING_BOX.intCode());
                } else {
                    whFbaChange.setStatus(whFbaAllocation.getStatus());
                }
                whFbaChange.setChangeTime(new Timestamp(System.currentTimeMillis()));
                FBAChangeDeliveryTypeEnum deliveryTypeEnum = whFbaAllocation.isTransfer()
                        ? FBAChangeDeliveryTypeEnum.TRANSFER_STOCK
                        : whFbaAllocation.isFba() ? FBAChangeDeliveryTypeEnum.FBA_OUT_STOCK
                                : FBAChangeDeliveryTypeEnum.OVERSEA_WAREHOUSE_STOCK;
                whFbaChange.setDeliveryType(deliveryTypeEnum.getCode());
                whFbaChange.setOrderItemStatus(FBAChangeOrderItemStatusEnum.WAITING_DEAL.getCode());
                whFbaChange.setCancelType(cancelType);
                if (Objects.nonNull(whFbaChange.getReturnQuantity()) && !Objects.equals(0, whFbaChange.getReturnQuantity())) {
                    whFbaChanges.add(whFbaChange);
                } else {
                    log.info("FbaAllocation_item.id=" + whFbaChange.getFbaAllocationItemId() + ",sku=" + whFbaChange.getSku() + ",应返架数量为0，为此不生成返架记录!");
                }
            }
        }

        whFbaChangeDao.batchCreateWhFbaChange(whFbaChanges);
    }

    /**
     * 通过订单得到库存信息
     *
     * @param whFbaAllocation     订单
     * @param whFbaAllocationItem 订单明细对象
     * @return 中转仓库存对象
     */
    private TransferStock getTransferStockByFbaAllocations(WhFbaAllocation whFbaAllocation, WhFbaAllocationItem whFbaAllocationItem) {
        TransferStockQueryCondition stockQuery = new TransferStockQueryCondition();
        if (!whFbaAllocation.isFba())
            stockQuery.setSite(whFbaAllocationItem.getSite());
        stockQuery.setSku(whFbaAllocationItem.getProductSku());
        stockQuery.setStore(whFbaAllocation.getUnprefixedAccountNumber());
        List<TransferStock> transferStockList = transferStockService.queryTransferStocks(stockQuery, null);
        if (CollectionUtils.isEmpty(transferStockList)) {
            throw new BusinessException("店铺: " + whFbaAllocation.getAccountNumber() + ",sellSku:" + whFbaAllocationItem.getSellSku() + "库存记录为空");
        }
        return transferStockList.get(0);
    }


    @Override
    public void deleteWhFbaChange(Integer id) {
        try {
            whFbaChangeDao.deleteWhFbaChange(id);
        } catch (SqlerException e) {
            log.error(e.getMessage(), e);
            throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
        }
    }

    @Override
    public void updateWhFbaChange(WhFbaChange whFbaChange) {
        try {
            whFbaChangeDao.updateWhFbaChange(whFbaChange);
        } catch (SqlerException e) {
            log.error(e.getMessage(), e);
            throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
        }
    }

    @Override
    public void batchUpdateWhFbaChange(List<WhFbaChange> entityList) {
        if (CollectionUtils.isNotEmpty(entityList)) {
            try {
                whFbaChangeDao.batchUpdateWhFbaChange(entityList);
            } catch (SqlerException e) {
                log.error(e.getMessage(), e);
                throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
            }
        }
    }


    @Override
    public void download(WhFbaChangeQueryCondition queryCondition, Pager pager, String exportType, String[] headers, String uuid) {
        if (Objects.isNull(queryCondition) || StringUtils.isBlank(exportType) || ArrayUtils.isEmpty(headers)) {
            log.info("数据对象为空，导出失败！");
            return;
        }
        long startTime = System.currentTimeMillis();

        ExportType exportTypeEnum = ExportType.build(exportType);

        List<List<String>> exportList = new ArrayList<>();
        List<WhFbaChange> records = new ArrayList<>();
        File file = null;
        String path = EasyExcelUtils.EXPORT_BASE_PATH;
        String fileName = "返架Sku记录" + System.currentTimeMillis() + ".xlsx";
        ExcelWriter excelWriter = null;
        //处理分sheet导出excel的线程池
        ThreadPoolExecutor exportExcelPool = null;

        try {
            file = new File(path + "/" + fileName);
            excelWriter = EasyExcel.write(fileName).head(EasyExcelUtils.getHead(headers)).build();

            log.warn("download file name: " + fileName);

            // 查询导出的数据
            switch (exportTypeEnum) {
                //全量导出需要循环分sheet处理
                case ALL: {

                    //由于线程池shutdown后不允许再打开。所以重新创建一个线程池，否则下面的阻塞不生效
                    exportExcelPool = ExecutorUtils.newFixedThreadPool(EXPORT_EXCEL_POOL_MAX_THREADS);

                    //分页，默认每页50w
                    AtomicInteger pageNumber = new AtomicInteger(1);
                    final Integer pageSize = SPLIT_SIZE;

                    if (Objects.isNull(pager)) {
                        pager = new Pager();
                    }
                    pager.setPageNo(pageNumber.get());
                    pager.setPageSize(pageSize);

                    while (true) {
                        records = this.queryWhFbaChanges(queryCondition, pager);
                        if (CollectionUtils.isEmpty(records)) {
                            break;
                        }

                        //转化为list
                        exportList = getExportList(records, headers);

                        //内部类中直接使用excelWriter报错,非要这么搞一下(或者用final修饰)
                        ExcelWriter finalExcelWriter = excelWriter;
                        List<List<String>> finalWhApvsExportList = exportList;

                        //这个变量是用于在excel文件中显示第几页
                        int num = pageNumber.get();

                        //每查出一页数据就丢到线程池中异步处理,这里线程池只有一个线程,所以还是单线程写文件,只是为了将读取数据和写文件隔开异步处理
                        ExecutorUtils.execute(exportExcelPool, new Runnable() {
                            @Override
                            public void run() {
                                WriteSheet writeSheet = EasyExcel.writerSheet("第" + num + "页").build();
                                finalExcelWriter.write(finalWhApvsExportList, writeSheet);
                            }
                        }, "allocate_location_match_record-export");

                        //当前条数不满50w时也没必要查下一页了
                        if (records.size() < pageSize) {
                            break;
                        }

                        //页码+1
                        pager.setPageNo(pageNumber.addAndGet(1));
                    }

                    //先使用shutdown 然后使用awaitTermination再等待线程池中任务执行结束后再执行主线程,必须先shutdown然后awaitTermination,否则会线程死锁
                    //注意:shutdown后无法再打开,所以上面每次都要重新定义线程池(size为1)
                    exportExcelPool.shutdown();
                    exportExcelPool.awaitTermination(Long.MAX_VALUE, TimeUnit.MINUTES);

                    //这里直接结束，上面会异步写入文件，写入完毕后return，然后进入finally关闭资源
                    return;
                }
                case PAGE: {
                    records = this.queryWhFbaChanges(queryCondition, pager);
                    if (CollectionUtils.isEmpty(records)) {
                        break;
                    }

                    //转化为list
                    exportList = getExportList(records, headers);

                    //写入第一页
                    WriteSheet writeSheet = EasyExcel.writerSheet("第1页").build();
                    excelWriter.write(exportList, writeSheet);
                    break;
                }
                case CHECKED: {
                    Assert.notEmpty(queryCondition.getIds(), "勾选列表为空!");

                    records = this.queryWhFbaChanges(queryCondition, pager);
                    if (CollectionUtils.isEmpty(records)) {
                        break;
                    }

                    //转化为list
                    exportList = getExportList(records, headers);

                    //写入第一页
                    WriteSheet writeSheet = EasyExcel.writerSheet("第1页").build();
                    excelWriter.write(exportList, writeSheet);

                    break;
                }
            }

            log.warn("---task execute end 耗时：---" + (System.currentTimeMillis() - startTime) / 1000);
        } catch (Exception e) {
            log.warn(e.getMessage());
        } finally {
            //关流
            if (excelWriter != null) {
                excelWriter.finish();
            }

            try {
                //上传文件服务器 & 发送webSocket消息
                log.warn("本地暂存文件路径================>" + file.getPath());
                FtpUtils.exportFileForSocket(file, uuid);
            } catch (Exception e) {
                log.warn(e.getMessage());
            }
        }
    }

    @Override
    public ResponseJson generateWaitOrder(List<Integer> ids) {
        ResponseJson responseJson = new ResponseJson(StatusCode.FAIL);
        WhFbaChangeQueryCondition queryCondition = new WhFbaChangeQueryCondition();
        queryCondition.setIds(ids);
        List<WhFbaChange> whFbaChanges = whFbaChangeDao.queryWhFbaChangeList(queryCondition, null);
        if (CollectionUtils.isEmpty(whFbaChanges)){
            responseJson.setMessage("未查询待返架条目！");
            return responseJson;
        }
        StringBuilder result = new StringBuilder();
        List<WhFbaChange> updateWhFbaChange = new ArrayList<>();
        for (WhFbaChange whFbaChange : whFbaChanges) {

            if (!FBAChangeOrderItemStatusEnum.WAITING_DEAL.getCode().equals(whFbaChange.getOrderItemStatus())) {
                result.append(String.format("订单%s状态不正确,当前状态不是待处理\n", whFbaChange.getFbaNo()));
                continue;
            }
            WhFbaChange fbaChange=new WhFbaChange();
            fbaChange.setOrderItemStatus(FBAChangeOrderItemStatusEnum.GENERATE_WAIT.getCode());
            fbaChange.setId(whFbaChange.getId());
            updateWhFbaChange.add(fbaChange);
        }
        if (CollectionUtils.isNotEmpty(updateWhFbaChange)) {
            whFbaChangeDao.batchUpdateWhFbaChange(updateWhFbaChange);
        }
        responseJson.setMessage(result.length() == 0 ?"生成待处理订单成功" :result.toString());
        responseJson.setStatus(StatusCode.SUCCESS);
        return responseJson;

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResponseJson generateReturnOrder(List<Integer> ids) {
        ResponseJson responseJson = new ResponseJson(StatusCode.FAIL);
        if (CollectionUtils.isEmpty(ids)) {
            responseJson.setMessage("id为空");
            return responseJson;
        }
        ResponseJson checkResponse = this.checkData(ids);
        if (Objects.equals(StatusCode.FAIL, checkResponse.getStatus())) {
            return checkResponse;
        }

        WhFbaChangeQueryCondition queryCondition = new WhFbaChangeQueryCondition();
        queryCondition.setIds(ids);
        List<WhFbaChange> whFbaChanges = whFbaChangeDao.queryWhFbaChangeList(queryCondition, null);
        
        if (CollectionUtils.isEmpty(whFbaChanges)){
            responseJson.setMessage("没有要返架的数据！");
            return responseJson;
        }

        List<Integer> typeList = whFbaChanges.stream().map(WhFbaChange::getDeliveryType).distinct().collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(typeList) && typeList.size() > 1
                && typeList.contains(FBAChangeDeliveryTypeEnum.TEMU_STOCK.getCode())) {
            responseJson.setMessage("拼多多发货类型的请单独筛选后再生成！");
            return responseJson;
        }

        boolean allTemu = whFbaChanges.stream().allMatch(w -> FBAChangeDeliveryTypeEnum.TEMU_STOCK.getCode().equals(w.getDeliveryType()));

        // 走拼多多流程
        if (allTemu)
            return generateTemuReturnOrder(whFbaChanges);
        


        // key -> fba_allocation_item_id
        Map<Integer, List<WhFbaChange>> whFbaChangeMap = whFbaChanges.stream()
                .collect(Collectors.toMap(WhFbaChange::getFbaAllocationItemId, v -> new ArrayList<>(Arrays.asList(v)), (v1, v2) -> {
                    v1.addAll(v2);
                    return v1;
                }));

        WhFbaAllocationItemQueryCondition itemQueryCondition = new WhFbaAllocationItemQueryCondition();
        itemQueryCondition.setIds(new ArrayList<>(whFbaChangeMap.keySet()));
        List<WhFbaAllocationItem> whFbaAllocationItems = whFbaAllocationItemService.queryWhFbaAllocationItems(itemQueryCondition, null);
        if (CollectionUtils.isEmpty(whFbaAllocationItems)) {
            throw new IllegalArgumentException("不存在allocation_item明细!");
        }

        // 将其按照订单划分
        Map<Integer, List<WhFbaAllocationItem>> allocationFbaNoMap = whFbaAllocationItems.stream()
                .collect(Collectors.toMap(WhFbaAllocationItem::getFbaId, v -> new ArrayList(Arrays.asList(v)), (v1, v2) -> {
                    v1.addAll(v2);
                    return v1;
                }));

        WhTransitReturn whTransitReturn = new WhTransitReturn();
        whTransitReturn.setReturnNo(CreateTaskNoUtils.createTransitReturnNo());
        List<WhTransitReturnItem> returnItems = new ArrayList<>();
        Set<String> skuSet = new HashSet<>();

        // key -> transferStockId,value -> 退仓明细。用于聚合生成的退货明细记录
        Map<String, WhTransitReturnItem> returnItemMap = new HashMap<>();
        for (Map.Entry<Integer, List<WhFbaAllocationItem>> entry : allocationFbaNoMap.entrySet()) {
            Integer fbaId = entry.getKey();
            List<WhFbaAllocationItem> items = entry.getValue();
            if (CollectionUtils.isEmpty(items)) {
                log.info("订单fbaId=" + fbaId + ",不存在allocation_item明细");
                continue;
            }

            WhFbaAllocation whFbaAllocation = whFbaAllocationDao.queryWhFbaAllocation(fbaId);
            if (Objects.isNull(whFbaAllocation)) {
                log.info("fbaId=" + fbaId + ",不存在对应的对象");
            }
            String fbaNo = whFbaAllocation.getFbaNo();
            List<String> skus = items.stream().map(WhFbaAllocationItem::getProductSku).collect(Collectors.toList());

            WhApvOutStockChainCancelQueryCondition chainCancelQueryCondition = new WhApvOutStockChainCancelQueryCondition();
            chainCancelQueryCondition.setRelevantNo(fbaNo);
            chainCancelQueryCondition.setSkus(skus);
            List<WhApvOutStockChainCancel> whApvOutStockChainCancels = whApvOutStockChainCancelService.queryWhApvOutStockChainCancels(chainCancelQueryCondition, null);

            // 对于部分取消的订单，其订单分配记录不移动到wh_apv_out_chain_cancel表中，为此需要从该表中查询出取消的数据对象
            if (CollectionUtils.isEmpty(whApvOutStockChainCancels)) {
                WhApvOutStockChainQueryCondition chainQueryCondition = new WhApvOutStockChainQueryCondition();
                chainQueryCondition.setRelevantNo(fbaNo);
                chainQueryCondition.setSkus(skus);
                List<WhApvOutStockChain> whApvOutStockChains = whApvOutStockChainService.queryWhApvOutStockChains(chainQueryCondition, null);
                if (CollectionUtils.isEmpty(whApvOutStockChains)) {
                    log.info("订单fbaNo=" + fbaNo + "部分取消,不存在分配的库存明细信息!");
                    continue;
                }
                whApvOutStockChainCancels = whApvOutStockChains.stream()
                        .map(whApvOutStockChain -> {
                            WhApvOutStockChainCancel whApvOutStockChainCancel = new WhApvOutStockChainCancel();
                            BeanUtils.copyProperties(whApvOutStockChain, whApvOutStockChainCancel);
                            Integer pickQuantity = Optional.ofNullable(whApvOutStockChain.getQuantity()).orElse(0)
                                    - Optional.ofNullable(whApvOutStockChain.getPickQuantity()).orElse(0);
                            whApvOutStockChainCancel.setPickQuantity(pickQuantity);
                            return whApvOutStockChainCancel;
                        })
                        .filter(whApvOutStockChainCancel -> whApvOutStockChainCancel.getPickQuantity() != 0)
                        .collect(Collectors.toList());
            }

            if (CollectionUtils.isEmpty(whApvOutStockChainCancels)) {
                log.info("订单fbaNo=" + fbaNo + ",不存在分配已取消的库存明细信息!");
                continue;
            }
            // 按sku划分库存分配记录
            Map<String, List<WhApvOutStockChainCancel>> skuWhApvOutStockMap = whApvOutStockChainCancels.stream()
                    .collect(Collectors.toMap(WhApvOutStockChainCancel::getSku, v -> new ArrayList(Arrays.asList(v)), (v1, v2) -> {
                        v1.addAll(v2);
                        return v1;
                    }));

            // 分配库存记录
            for (WhFbaAllocationItem item : items) {
                String sku = item.getProductSku();
                String store = item.getStore();
                String site = whFbaAllocation.isFba() ? null : item.getSite();
                String sellSku = item.getSellSku();
                // 总的需要返架数量
                List<WhFbaChange> fbaChanges = Optional.ofNullable(whFbaChangeMap.get(item.getId())).orElse(new ArrayList<>());
                Integer needReturnAmount = fbaChanges.stream()
                        .filter(Objects::nonNull)
                        .mapToInt(WhFbaChange::getReturnQuantity)
                        .sum();
                if (needReturnAmount == 0) {
                    log.info("需返架数量为0，为此不再生成需返架记录");
                    continue;
                }

                List<WhApvOutStockChainCancel> stockChains = skuWhApvOutStockMap.get(sku);
                if (CollectionUtils.isEmpty(stockChains)) {
                    log.info("sku=" + sku + "不存在分配已取消的库存明细信息!");
                    continue;
                }
                // 校验库存是否存在
                List<Integer> transferStockIds = stockChains.stream()
                        .map(WhApvOutStockChainCancel::getStockId)
                        .collect(Collectors.toList());
                ResponseJson checkTransferStockResponse = checkTransferStock(sku, transferStockIds);
                if (Objects.equals(checkTransferStockResponse.getStatus(), StatusCode.FAIL)) {
                    log.info(checkTransferStockResponse.getMessage());
                    continue;
                }

                // 用于匹配库存分配记录的sku，以生成返架记录
                for (WhApvOutStockChainCancel stockChain : stockChains) {
                    Integer provisionQuantity = stockChain.getPickQuantity();
                    Integer transferStockId = stockChain.getStockId();
                    if (Objects.equals(0, provisionQuantity)) {
                        log.info("sku=" + sku + ",transferStockId=" + transferStockId + ",可提供返架件数为0,为此不继续对其进行生成返架记录!");
                        continue;
                    }
                    // 可返架数量
                    Integer returnAmount = Math.min(provisionQuantity, needReturnAmount);
                    if (Objects.equals(0, returnAmount)) {
                        continue;
                    }

                    String aggregateKey = String.valueOf(transferStockId);
                    WhTransitReturnItem returnItem = returnItemMap.get(aggregateKey);
                    if (Objects.isNull(returnItem)) {
                        returnItem = new WhTransitReturnItem();
                        returnItem.setSku(sku);
                        returnItem.setSite(site);
                        returnItem.setSkuStore(store);
                        returnItem.setSellSku(sellSku);
                        returnItem.setStockId(transferStockId);
                        returnItems.add(returnItem);
                        skuSet.add(sku);
                        returnItemMap.put(aggregateKey, returnItem);
                    }

                    // 原先应返架的数量加上此次需要上架的数量就是总共需要上架的数量
                    returnItem.setQuantity(Optional.ofNullable(returnItem.getQuantity()).orElse(0) + returnAmount);


                    // 用于扣除已经生成记录的库存分配数量的变更
                    stockChain.setPickQuantity(stockChain.getPickQuantity() - returnAmount);
                    for (WhFbaChange fbaChange : fbaChanges) {
                        if (Objects.equals(returnAmount, 0)) {
                            break;
                        }
                        Integer changeAmount = Math.min(returnAmount, fbaChange.getReturnQuantity());
                        returnAmount -= changeAmount;
                        needReturnAmount -= changeAmount;
                        fbaChange.setReturnQuantity(fbaChange.getReturnQuantity() - changeAmount);
                    }
                }

            }
        }

        whTransitReturn.setReturnItems(returnItems);
        responseJson = whTransitReturnService.createWhReturnAndItem(new ArrayList<>(skuSet), new ArrayList<>(Arrays.asList(whTransitReturn)));

        if (Objects.equals(StatusCode.FAIL, responseJson.getStatus())) {
            return responseJson;
        }

        if (Objects.isNull(whTransitReturn.getId())) {
            throw new BusinessException("返架单创建失败!");
        }

        List<WhFbaChange> updateWhFbaChange = new ArrayList<>();
        whFbaChanges.stream().forEach(v -> {
            WhFbaChange whFbaChange = new WhFbaChange();
            whFbaChange.setId(v.getId());
            whFbaChange.setReturnOrderId(whTransitReturn.getId());
            whFbaChange.setOrderItemStatus(FBAChangeOrderItemStatusEnum.WAITING_RETURN_SHELF.getCode());
            updateWhFbaChange.add(whFbaChange);
        });
        whFbaChangeDao.batchUpdateWhFbaChange(updateWhFbaChange);
        responseJson.setStatus(StatusCode.SUCCESS);
        return responseJson;
    }


    /**
     * temu生成返架单
     * 
     * @param whFbaChanges
     * @return
     */
    private ResponseJson generateTemuReturnOrder(List<WhFbaChange> whFbaChanges) {
        ResponseJson responseJson = new ResponseJson(StatusCode.FAIL);
        if (CollectionUtils.isEmpty(whFbaChanges))
            return responseJson;

        List<String> apvNoList = whFbaChanges.stream().map(WhFbaChange::getFbaNo).collect(Collectors.toList());

        WhApvOutStockChainQueryCondition queryCondition = new WhApvOutStockChainQueryCondition();
        queryCondition.setRelevantNos(apvNoList);
        queryCondition.setOrderType(AssetOrderType.TEMU_PREPARE_ORDER.intCode());
        List<WhApvOutStockChain> whApvOutStockChains = whApvOutStockChainService
                .queryWhApvOutStockChains(queryCondition, null);

        WhApvOutStockChainCancelQueryCondition chainCancelQueryCondition = new WhApvOutStockChainCancelQueryCondition();
        chainCancelQueryCondition.setRelevantNos(apvNoList);
        chainCancelQueryCondition.setOrderType(AssetOrderType.TEMU_PREPARE_ORDER.intCode());
        List<WhApvOutStockChainCancel> whApvOutStockChainCancels = whApvOutStockChainCancelService
                .queryWhApvOutStockChainCancels(chainCancelQueryCondition, null);
        if (CollectionUtils.isEmpty(whApvOutStockChainCancels) && CollectionUtils.isEmpty(whApvOutStockChains)) {
            responseJson.setMessage("不存在分配库存明细信息!");
            return responseJson;
        }

        List<WhApvOutStockChainCancel> allOrderList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(whApvOutStockChains)) {
            List<WhApvOutStockChainCancel> transList = whApvOutStockChains.stream()
                    .map(w -> WhApvOutStockChainCancel.builder().relevantNo(w.getRelevantNo()).sku(w.getSku())
                            .stockId(w.getStockId()).quantity(w.getQuantity()).pickQuantity(w.getPickQuantity())
                            .girdQuantity(w.getGirdQuantity()).build())
                    .collect(Collectors.toList());
            allOrderList.addAll(transList);

        }
        if (CollectionUtils.isNotEmpty(whApvOutStockChainCancels))
            allOrderList.addAll(whApvOutStockChainCancels);

        List<Integer> stockIdList = allOrderList.stream().map(WhApvOutStockChainCancel::getStockId).distinct()
                .collect(Collectors.toList());

        // 校验库存存不存在
        TransferStockQueryCondition transferStockQueryCondition = new TransferStockQueryCondition();
        transferStockQueryCondition.setIds(stockIdList);
        List<TransferStock> transferStockList = transferStockService.queryTransferStocks(transferStockQueryCondition,
                null);
        if (CollectionUtils.isEmpty(transferStockList)) {
            responseJson.setMessage("中转仓库存记录不存在");
            return responseJson;
        }

        Map<String, WhApvOutStockChainCancel> cancelMap = allOrderList.stream()
                .collect(Collectors.toMap(c -> c.getRelevantNo() + c.getSku(), m -> m));

        Map<Integer, TransferStock> stockMap = transferStockList.stream()
                .collect(Collectors.toMap(TransferStock::getId, s -> s));

        List<WhTransitReturnItem> returnItems = new ArrayList<>();
        Set<String> skuSet = new HashSet<>();

        Map<String, WhTransitReturnItem> returnItemMap = new HashMap<>();

        for (WhFbaChange fbaChange : whFbaChanges) {
            boolean needReturn = fbaChange.getReturnQuantity() != null && fbaChange.getReturnQuantity() > 0;
            if (!needReturn)
                continue;
            WhApvOutStockChainCancel stockChain = cancelMap.get(fbaChange.getFbaNo() + fbaChange.getSku());
            if (stockChain == null)
                continue;
            Integer transferStockId = stockChain.getStockId();
            Integer provisionQuantity = stockChain.getPickQuantity();
            String sku = fbaChange.getSku();

            if (Objects.equals(0, provisionQuantity)) {
                log.info("sku=" + sku + ",transferStockId=" + transferStockId + ",可提供返架件数为0,为此不继续对其进行生成返架记录!");
                continue;
            }
            // 可返架数量
            Integer returnAmount = Math.min(provisionQuantity, fbaChange.getReturnQuantity());
            if (Objects.equals(0, returnAmount)) {
                continue;
            }

            TransferStock stock = stockMap.get(transferStockId);
            if (stock == null)
                continue;

            String aggregateKey = String.valueOf(transferStockId);
            WhTransitReturnItem returnItem = returnItemMap.get(aggregateKey);
            if (Objects.isNull(returnItem)) {
                returnItem = new WhTransitReturnItem();
                returnItem.setSku(sku);
                returnItem.setSite(stock.getSite());
                returnItem.setSkuStore(stock.getStore());
                returnItem.setSellSku(sku);
                returnItem.setStockId(transferStockId);
                returnItems.add(returnItem);
                skuSet.add(sku);
                returnItemMap.put(aggregateKey, returnItem);
            }
            // 原先应返架的数量加上此次需要上架的数量就是总共需要上架的数量
            returnItem.setQuantity(Optional.ofNullable(returnItem.getQuantity()).orElse(0) + returnAmount);
        }

        WhTransitReturn whTransitReturn = new WhTransitReturn();
        whTransitReturn.setReturnNo(CreateTaskNoUtils.createTransitReturnNo());
        whTransitReturn.setReturnItems(returnItems);
        responseJson = whTransitReturnService.createWhReturnAndItem(new ArrayList<>(skuSet),
                new ArrayList<>(Arrays.asList(whTransitReturn)));

        if (Objects.equals(StatusCode.FAIL, responseJson.getStatus())) {
            return responseJson;
        }

        if (Objects.isNull(whTransitReturn.getId())) {
            throw new BusinessException("返架单创建失败!");
        }

        List<WhFbaChange> updateWhFbaChange = new ArrayList<>();
        whFbaChanges.stream().forEach(v -> {
            WhFbaChange whFbaChange = new WhFbaChange();
            whFbaChange.setId(v.getId());
            whFbaChange.setReturnOrderId(whTransitReturn.getId());
            whFbaChange.setOrderItemStatus(FBAChangeOrderItemStatusEnum.WAITING_RETURN_SHELF.getCode());
            updateWhFbaChange.add(whFbaChange);
        });
        whFbaChangeDao.batchUpdateWhFbaChange(updateWhFbaChange);
        responseJson.setStatus(StatusCode.SUCCESS);
        return responseJson;

    }
    /**
     * 用于校验库存是否存在
     *
     * @param Ids 库存Id
     * @return 校验结果
     */
    private ResponseJson checkTransferStock(String sku, List<Integer> Ids) {
        ResponseJson responseJson = new ResponseJson(StatusCode.FAIL);
        if (CollectionUtils.isEmpty(Ids)) {
            responseJson.setMessage("库存不存在!");
            return responseJson;
        }
        // 校验库存存不存在
        TransferStockQueryCondition transferStockQueryCondition = new TransferStockQueryCondition();
        transferStockQueryCondition.setIds(Ids);
        List<TransferStock> transferStock = transferStockService.queryTransferStocks(transferStockQueryCondition, null);
        if (Objects.isNull(transferStock)) {
            responseJson.setMessage("sku" + sku + "在中转仓中不存在库存对象!");
            return responseJson;
        }

        responseJson.setStatus(StatusCode.SUCCESS);
        return responseJson;
    }

    /**
     * 用于检查数据是否符合生成返架任务
     *
     * @param whFbaChangeIds 要进行返架任务生成的Id
     * @return
     */
    private ResponseJson checkData(List<Integer> whFbaChangeIds) {
        ResponseJson responseJson = new ResponseJson(StatusCode.FAIL);
        if (CollectionUtils.isEmpty(whFbaChangeIds)) {
            responseJson.setMessage("要生成返架任务的条数为空!");
            return responseJson;
        }
        WhFbaChangeQueryCondition queryCondition = new WhFbaChangeQueryCondition();
        queryCondition.setIds(whFbaChangeIds);
        List<WhFbaChange> whFbaChanges = whFbaChangeDao.queryWhFbaChangeList(queryCondition, null);
        if (CollectionUtils.isEmpty(whFbaChanges)) {
            responseJson.setMessage("对应待返架sku信息不存在!");
            return responseJson;
        }
        List<WhFbaChange> notPassData = whFbaChanges.stream()
                .filter(v -> !Objects.equals(FBAChangeOrderItemStatusEnum.GENERATE_WAIT.getCode(), v.getOrderItemStatus()))
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(notPassData)) {
            responseJson.setMessage("存在非生成待返架状态的返架sku被勾选!");
            return responseJson;
        }
        responseJson.setStatus(StatusCode.SUCCESS);
        return responseJson;
    }

    /**
     * 用于将匹配记录对象转化为数据导出对象
     *
     * @param records       匹配记录对象
     * @param selectHeaders 导出的标题项
     * @return 数据导出对象结果集
     */
    private List<List<String>> getExportList(List<WhFbaChange> records, String[] selectHeaders) {
        if (CollectionUtils.isEmpty(records) || ArrayUtils.isEmpty(selectHeaders)) {
            log.info("无需要导出数据!");
            return new ArrayList<>();
        }

        //转化为list
        List<Future<List<String>>> futures = new ArrayList<>();
        for (WhFbaChange item : records) {
            futures.add(transfeExcelPool.submit(new Callable<List<String>>() {
                @Override
                public List<String> call() throws Exception {
                    List<String> exportLine = new ArrayList<>();
                    if (ArrayUtils.contains(selectHeaders, "编号")) {
                        exportLine.add(POIUtils.transferObj2Str(item.getId()));
                    }
                    if (ArrayUtils.contains(selectHeaders, "中转仓发货单号")) {
                        exportLine.add(POIUtils.transferObj2Str(item.getFbaNo()));
                    }
                    if (ArrayUtils.contains(selectHeaders, "变更前状态")) {
                        exportLine.add(POIUtils.transferObj2Str(AsnPrepareStatus.getNameByCode(item.getChangeStatus()+"")));
                    }
                    if (ArrayUtils.contains(selectHeaders, "发货类型")) {
                        exportLine.add(POIUtils.transferObj2Str(item.getDeliveryTypeStr()));
                    }
                    if (ArrayUtils.contains(selectHeaders, "取消类型")) {
                        exportLine.add(POIUtils.transferObj2Str(CancelTypeEnum.getNameByCode(item.getCancelType()+"")));
                    }
                    if (ArrayUtils.contains(selectHeaders, "箱号")) {
                        exportLine.add(POIUtils.transferObj2Str(item.getBoxNo()));
                    }
                    if (ArrayUtils.contains(selectHeaders, "FNSku")) {
                        exportLine.add(POIUtils.transferObj2Str(item.getFnSku()));
                    }
                    if (ArrayUtils.contains(selectHeaders, "sku")) {
                        exportLine.add(POIUtils.transferObj2Str(item.getSku()));
                    }
                    if (ArrayUtils.contains(selectHeaders, "库位")) {
                        exportLine.add(POIUtils.transferObj2Str(item.getLocationNumber()));
                    }
                    if (ArrayUtils.contains(selectHeaders, "应返架数量")) {
                        exportLine.add(POIUtils.transferObj2Str(item.getReturnQuantity()));
                    }
                    if (ArrayUtils.contains(selectHeaders, "已返架数量")) {
                        exportLine.add(POIUtils.transferObj2Str(item.getAccomplishmentQuantity()));
                    }
                    if (ArrayUtils.contains(selectHeaders, "关联返架单")) {
                        exportLine.add(POIUtils.transferObj2Str(item.getReturnOrderNo()));
                    }
                    if (ArrayUtils.contains(selectHeaders, "状态")) {
                        exportLine.add(POIUtils.transferObj2Str(item.getOrderItemStatusStr()));
                    }
                    if (ArrayUtils.contains(selectHeaders, "创建时间")) {
                        exportLine.add(POIUtils.transferObj2Str(item.getChangeTime()));
                    }
                    return exportLine;
                }
            }));
        }
        List<List<String>> allList = new ArrayList<>();
        for (Future<List<String>> future : futures) {
            try {
                allList.add(future.get());
            } catch (InterruptedException e) {
                log.error(e.getMessage(), e);
            } catch (ExecutionException e) {
                log.error(e.getMessage(), e);
            }
        }

        return allList;
    }
}