package com.estone.transfer.service.impl;

import java.sql.Timestamp;
import java.util.*;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import com.estone.temu.enums.CancelTypeEnum;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSONObject;
import com.estone.apv.bean.WhApvOutStockChain;
import com.estone.apv.bean.WhApvOutStockChainQueryCondition;
import com.estone.apv.enums.WhApvOutStockChainStatusEnum;
import com.estone.apv.service.WhApvOutStockChainCancelService;
import com.estone.apv.service.WhApvOutStockChainService;
import com.estone.common.SaleChannel;
import com.estone.elasticsearch.model.EsSaleAccount;
import com.estone.elasticsearch.model.request.EsSaleAccountRequest;
import com.estone.elasticsearch.service.EsSaleAccountService;
import com.estone.sku.bean.WhSku;
import com.estone.sku.bean.WhSkuQueryCondition;
import com.estone.sku.service.AfterSaleSettlementService;
import com.estone.sku.service.WhSkuService;
import com.estone.statistics.enums.AssetOrderType;
import com.estone.statistics.enums.DrpTurnoverOderType;
import com.estone.system.rabbitmq.bean.AmqMessage;
import com.estone.system.rabbitmq.common.AmqMessageModuleName;
import com.estone.system.rabbitmq.model.OmsFbaSkuMessage;
import com.estone.system.rabbitmq.service.AmqMessageService;
import com.estone.system.rabbitmq.utils.AssembleMessageDataUtils;
import com.estone.transfer.bean.*;
import com.estone.transfer.enums.*;
import com.estone.transfer.service.*;
import com.estone.transfer.utils.TransferStockUtils;
import com.estone.warehouse.aspect.StockServicelock;
import com.estone.warehouse.bean.WhStock;
import com.estone.warehouse.bean.WhStockLog;
import com.estone.warehouse.enums.LocationTagEnum;
import com.estone.warehouse.enums.StockLogStep;
import com.estone.warehouse.enums.StockLogType;
import com.estone.warehouse.service.WhStockLogService;
import com.estone.warehouse.service.WhStockService;
import com.estone.warehouse.util.FrozenStockUtils;
import com.whq.tool.context.DataContextHolder;
import com.whq.tool.exception.BusinessException;

import lombok.extern.slf4j.Slf4j;

@Service("whFbaUpdateStockService")
@Slf4j
public class WhFbaUpdateStockServiceImpl implements WhFbaUpdateStockService {

    @Resource
    private WhStockService whStockService;

    @Resource
    private WhStockLogService whStockLogService;

    @Resource
    private AmqMessageService amqMessageService;

    @Resource
    private TransferStockCountService transferStockCountService;

    @Resource
    private TransferStockService transferStockService;

    @Resource
    private WhTransitStockLogService stockLogService;

    @Resource
    private WhSkuService whSkuService;

    @Resource
    private EsSaleAccountService esSaleAccountService;

    @Resource
    private WhFbaChangeService whFbaChangeService;

    @Resource
    private WhApvOutStockChainService whApvOutStockChainService;

    @Resource
    private WhApvOutStockChainCancelService whApvOutStockChainCancelService;
    @Resource
    private TransitBatchHandleService transitBatchHandleService;

    @Resource
    private AfterSaleSettlementService afterSaleSettlementService;

    // 调拨
    @Override
    // @StockServicelock
    public void doAllot(List<String> allSku, List<String> skus, List<WhFbaAllocationData> allocationDatas,
            String shipmentId, Map<String, WhFbaAllocationData> itemMap, Integer alloId, String alloNo) {
        // 查询本仓
        Map<String, List<WhStock>> stockMap = whStockService.getWhStocksMapBySkuList(skus);
        if (stockMap == null) {
            throw new RuntimeException("sku: " + skus + "库存记录为空");
        }

        List<WhStock> stockList = new ArrayList<WhStock>();
        List<WhStockLog> stockLogs = new ArrayList<WhStockLog>();
        /** 库存变更推送 */
       // List<AmqMessage> msgList = new ArrayList<>();

        StockLogStep logStep = StockLogStep.FBA_DEMAND_ALLOT;

        // 需要更新的库存
        List<TransferStock> updateList = new ArrayList<>();
        List<WhTransitStockLog> whStockLogList = new ArrayList<>();
        Map<Integer,Integer> outQuantityMap = new HashMap<>();
        List<Integer> transferStockIdList = new ArrayList<>();
        //操作中转仓库存统计
        TransferStockQueryCondition queryCount = new TransferStockQueryCondition();
        queryCount.setSkus(StringUtils.join(skus,","));
        List<TransferStockCount> stockCountList = transferStockCountService.queryTransferStocks(queryCount,null);
        Map<String, TransferStockCount> stockCountMap = stockCountList.stream().collect(Collectors.toMap(TransferStockCount::getSku, item -> item));

        for (WhFbaAllocationData data : allocationDatas) {
            String sku = data.getSku();
            String store = data.getNoPrefixAccountNumber();

            List<WhStock> whStocks = stockMap.get(sku);
            if (CollectionUtils.isEmpty(whStocks)) throw new RuntimeException("sku: " + sku + "库存记录为空");

            // 扣库存顺序 本地-》存货-》售后结算
            whStocks = whStocks.stream()
                    .sorted(Comparator.comparing(w -> w.getLocationTag(),Comparator.nullsFirst(Comparator.naturalOrder())))
                    .collect(Collectors.toList());

            /**
             * 减：可用
             * 加：已分配
             */
            Integer operateQuantitySum = data.getAllocationQuantity() == null ? 0 : data.getAllocationQuantity();
            int surplusQuantitySum = whStocks.stream().mapToInt(WhStock::getSurplusQuantity).sum();
            if ((surplusQuantitySum - operateQuantitySum) < 0) {
                log.error("sku:" + sku + "可用库存不能为负: surplusQuantity[ " + surplusQuantitySum + " ], operateQuantity[ " + operateQuantitySum + " ]");
                throw new RuntimeException(String.format("sku:%s可用库存不能为负: 可用库存[%s], 扣可用[%s]", sku, surplusQuantitySum, operateQuantitySum));
            }
            for (WhStock whStock : whStocks) {
                if (operateQuantitySum <= 0) {
                    continue;
                }
                WhStock updateStock = WhStock.buildUpdateStock(whStock);

                // 当前sku库位需要调拨的数量
                Integer operateQuantity = operateQuantitySum;

                Integer surplusQuantity = Optional.ofNullable(whStock.getSurplusQuantity()).orElse(0);
                if (surplusQuantity <= 0) {
                    continue;
                }
                if (surplusQuantity - operateQuantitySum > 0) {
                    updateStock.setSurplusQuantity(surplusQuantity - operateQuantitySum);
                    operateQuantitySum = 0;
                }
                else {
                    updateStock.setSurplusQuantity(0);
                    operateQuantity = surplusQuantity;
                    operateQuantitySum -= surplusQuantity;
                }
                outQuantityMap.put(whStock.getId(),operateQuantity);
                stockList.add(updateStock);
                stockLogs.add(new WhStockLog(sku, StockLogType.USABLE_STOCK, whStock.getId(),
                        whStock.getLocationNumber(), logStep, -operateQuantity, surplusQuantity, shipmentId));

                if (null == stockCountMap.get(sku)) {
                    WhSkuQueryCondition skuQuery = new WhSkuQueryCondition();
                    skuQuery.setSku(sku);
                    WhSku whSku = whSkuService.queryWhSku(skuQuery);
                    TransferStockCount stockCount = new TransferStockCount();
                    stockCount.setSku(sku);
                    if (whSku != null) {
                        stockCount.setSkuName(whSku.getName());
                        stockCount.setLocation(whSku.getLocationNumber());
                        stockCount.setImg(whSku.getImageUrl());
                        stockCount.setWarehouseId(whSku.getWarehouseId());
                    }
                    stockCount.setLastUpdatedBy(DataContextHolder.getUserId());
                    stockCount.setLastUpdateDate(new Timestamp(System.currentTimeMillis()));
                    transferStockCountService.createTransferStockCount(stockCount);
                    stockCountMap.put(sku,stockCount);
                }
                // 操作中转仓明细
                TransferStockQueryCondition stockQuery = new TransferStockQueryCondition();
                stockQuery.setSku(itemMap.get(sku).getSku());
                stockQuery.setStore(store);
                stockQuery.setRemark(data.getSaleChannel());
                List<TransferStock> dbStockList = transferStockService.queryTransferStocks(stockQuery, null);
                if (CollectionUtils.isNotEmpty(dbStockList)) {
                    dbStockList = dbStockList.stream()
                            .filter(d -> d.getStockId() == null || whStock.getId().equals(d.getStockId()))
                            .collect(Collectors.toList());
                }

                Integer transferSurplusQuantity = 0;
                TransferStock transferStock = null;
                if (CollectionUtils.isNotEmpty(dbStockList)) {
                    transferStock = dbStockList.get(0);
                    transferSurplusQuantity = transferStock.getSurplusQuantity() == null ? 0
                            : transferStock.getSurplusQuantity();
                    TransferStock updateTransferStock = TransferStock.buildUpdateStock(transferStock);
                    updateTransferStock.setSurplusQuantity(transferSurplusQuantity + operateQuantity);
                    if (updateTransferStock.getStockId() == null) {
                        updateTransferStock.setLocationNumber(whStock.getLocationNumber());
                        updateTransferStock.setStockId(whStock.getId());
                    }
                    if (whStock.existLocationTag(LocationTagEnum.PRESTORE)){
                        transferStock.addLocationTag(LocationTagEnum.PRESTORE);
                    } else {
                        transferStock.remLocationTag(LocationTagEnum.PRESTORE);
                    }
                    updateTransferStock.setLocationTag(transferStock.getLocationTag());
                    updateTransferStock.setSite(data.getSite());
                    data.setSaleChannel(transferStock.getRemark());
                    updateList.add(updateTransferStock);
                    transferStockIdList.add(updateTransferStock.getId());
                }
                else {// 入库时没有对应的sku库存
                    transferStock = new TransferStock();
                    transferStock.setSku(sku);
                    transferStock.setStockId(whStock.getId());
                    transferStock.setLocationNumber(whStock.getLocationNumber());
                    transferStock.setSurplusQuantity(operateQuantity);
                    if (whStock.existLocationTag(LocationTagEnum.PRESTORE)){
                        transferStock.addLocationTag(LocationTagEnum.PRESTORE);
                    }
                    transferStock.setLocationTag(transferStock.getLocationTag());
                    transferStock.setStore(store);
                    EsSaleAccount esAccount = esSaleAccountService.getSaleAccountBySellerId(store, "accountNumber");
                    if (StringUtils.isNotBlank(data.getSaleChannel())) {
                        esAccount = esSaleAccountService.getSaleAccountBySellerId( "accountNumber",store,data.getSaleChannel());
                    }
                    if (esAccount == null) {
                        EsSaleAccountRequest esSaleAccountRequest = new EsSaleAccountRequest();
                        esSaleAccountRequest.setAccountNumberList(Collections.singletonList(store));
                        List<EsSaleAccount> accountInfoList = esSaleAccountService
                                .getAccountInfoList(esSaleAccountRequest);
                        Map<String, EsSaleAccount> accountMap = Optional.ofNullable(accountInfoList)
                                .orElse(new ArrayList<>()).stream()
                                .collect(Collectors.toMap(a -> StringUtils.contains(a.getAccountNumber(), "-")
                                        ? a.getAccountNumber().substring(a.getAccountNumber().indexOf("-") + 1)
                                        : a.getAccountNumber(), a -> a, (k1, k2) -> k1));
                        esAccount = accountMap.get(store);
                    }
                    if (esAccount==null || StringUtils.isBlank(esAccount.getSaleChannel())) {
                        throw new RuntimeException("ES中没有找到对应账号信息，账号：" + store + ",平台：" + data.getSaleChannel());
                    }
                    String accountSite = data.getSite();
                    String saleChannel = null;
                    if (esAccount != null) {
                        accountSite = StringUtils.isNotBlank(esAccount.getAccountSite()) ? esAccount.getAccountSite()
                                : esAccount.getSaleChannel();
                        saleChannel = esAccount.getSaleChannel();
                    }
                    transferStock.setSite(
                            StringUtils.equalsIgnoreCase(saleChannel, SaleChannel.CHANNEL_AMAZON) ? data.getSite()
                                    : accountSite);
                    transferStock.setRemark(saleChannel);
                    data.setSaleChannel(transferStock.getRemark());
                    transferStock.setLastUpdatedBy(DataContextHolder.getUserId());
                    transferStock.setLastUpdateDate(new Timestamp(System.currentTimeMillis()));
                    transferStockService.createTransferStock(transferStock);
                    transferStockIdList.add(transferStock.getId());
                }
                whStockLogList.add(new WhTransitStockLog(sku, store, transferStock.getSite(),
                        TransferStockLogType.USABLE_STOCK, transferStock.getId(), transferStock.getLocationNumber(),
                        StockLogStep.FBA_DEMAND_ALLOT, operateQuantity, transferSurplusQuantity, shipmentId));
            }
        }
        //更新中转仓库存明细
        if (CollectionUtils.isNotEmpty(updateList)){
            transferStockService.batchUpdateTransferStock(updateList);
        }
        // 添加库存变动日志
        if (CollectionUtils.isNotEmpty(whStockLogList)) {
            stockLogService.batchAddWhStockLog(whStockLogList);
        }

        if (CollectionUtils.isNotEmpty(stockList)) {
            whStockService.batchUpdateWhStock(stockList);
            // 校验是否扣减售后结算库存
            afterSaleSettlementService.calcAfterSaleSettlement(outQuantityMap,
                    StockLogType.STOCK_ALLOCATION.intCode(),
                    AssetOrderType.ALLOCATION_ORDER.intCode(), alloId, alloNo);
            // 记录调拨入库时间
            TransferStockUtils.updateTransferTime(transferStockIdList, DrpTurnoverOderType.ALLOCATION_IN.intCode());
            // 记录调拨出库时间
            Map<Integer, String> stockIdMap = stockList.stream()
                    .collect(Collectors.toMap(WhStock::getId, WhStock::getSku,(v1,v2)->v2));
            FrozenStockUtils.updateTimeAndUser(stockIdMap, DrpTurnoverOderType.ALLOCATION_OUT.intCode());
        }
        if (CollectionUtils.isNotEmpty(stockLogs)) {
            whStockLogService.batchAddWhStockLog(stockLogs);
        }

    }

    @Override
    @StockServicelock
    public void cancelStartPick(WhFbaAllocation whFbaAllocation) {
        String fbaNo = whFbaAllocation.getFbaNo();

        WhApvOutStockChainQueryCondition queryCondition=new WhApvOutStockChainQueryCondition();
        queryCondition.setRelevantNo(fbaNo);
        List<WhApvOutStockChain> whApvOutStockChains = whApvOutStockChainService.queryWhApvOutStockChains(queryCondition, null);
        Map<String, List<WhApvOutStockChain>> whApvOutStockChainMap = Optional.ofNullable(whApvOutStockChains).orElse(new ArrayList<>()).stream().collect(Collectors.groupingBy(WhApvOutStockChain::getSku));
        if (MapUtils.isEmpty(whApvOutStockChainMap)) {
            throw new BusinessException("fbaNo: " +fbaNo + ",库存关联数据不存在！");
        }

        List<TransferStock> stockList = new ArrayList<>();
        List<WhTransitStockLog> stockLogs = new ArrayList<>();
        /** 库存变更推送 */
        List<AmqMessage> msgList = new ArrayList<>();
        StockLogStep logStep = StockLogStep.FBA_CANCEL;
        for (WhFbaAllocationItem item : whFbaAllocation.buildGroupItems()) {
            // 已装箱取消 推装箱数量
            if (whFbaAllocation.getStatus() > AsnPrepareStatus.WAITING_BOX.intCode()){
                item.setPickQuantity(item.getLoadingQuantity());
            }
            TransferStockQueryCondition stockQuery = new TransferStockQueryCondition();
           /* stockQuery.setSite(item.getSite());
            stockQuery.setSku(item.getProductSku());
            stockQuery.setStore(whFbaAllocation.getAccountNumber());*/
            List<WhApvOutStockChain> apvOutStockChains = whApvOutStockChainMap.get(item.getProductSku());
            List<Integer> idList = Optional.ofNullable(apvOutStockChains).orElse(new ArrayList<>()).stream().map(WhApvOutStockChain::getStockId).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(idList)){
                throw new BusinessException("fbaNo: " +fbaNo + ",库存关联WhApvOutStockChain表库存Id不存在！");
            }
            stockQuery.setIds(idList);
            List<TransferStock> transferStockList = transferStockService.queryTransferStocks(stockQuery,null);
            if (CollectionUtils.isEmpty(transferStockList)) {
                throw new BusinessException("店铺: " + whFbaAllocation.getAccountNumber() + ",sellSku:"+item.getSellSku()+"库存记录为空");
            }
            String sku = item.getProductSku();
            for (TransferStock whStock : transferStockList) {
                List<WhApvOutStockChain> whApvOutStockChainList = apvOutStockChains.stream().filter(a -> whStock.getId().equals(a.getStockId())).collect(Collectors.toList());
                if (CollectionUtils.isEmpty(whApvOutStockChainList) || whApvOutStockChainList.get(0)==null) {
                    continue;
                }
                WhApvOutStockChain whApvOutStockChain = whApvOutStockChainList.get(0);
                /**
                 * 加：取消/可用
                 * 减：已拣/已分配
                 */
                Integer quantity = Optional.ofNullable(whApvOutStockChain.getQuantity()).orElse(0);
                if (quantity <= 0) {
                    continue;
                }
                Integer picked =  Optional.ofNullable(whApvOutStockChain.getPickQuantity()).orElse(0);
                Integer notPick = quantity - picked;
                // 拣货缺货 播种已经退已分配库存
                if (whFbaAllocation.getStatus()>=AsnPrepareStatus.PICK_STOCK_OUT.intCode()){
                    notPick = 0;
                }

                TransferStock updateStock = TransferStock.buildUpdateStock(whStock);
                Integer surplusQuantity = whStock.getSurplusQuantity()==null?0:whStock.getSurplusQuantity();
                Integer allotQuantity = whStock.getAllotQuantity()==null?0:whStock.getAllotQuantity();
                Integer pickQuantity = whStock.getPickQuantity()==null?0:whStock.getPickQuantity();
                Integer cancelQuantity = whStock.getPickReturnQuantity()==null?0:whStock.getPickReturnQuantity();

                updateStock.setSurplusQuantity(surplusQuantity + notPick);
                updateStock.setAllotQuantity(allotQuantity - notPick);
                updateStock.setPickQuantity(pickQuantity - picked);
                updateStock.setPickReturnQuantity(cancelQuantity + picked);
                stockList.add(updateStock);
                if (notPick > 0) {
                    stockLogs.add(new WhTransitStockLog(sku,whStock.getStore(), updateStock.getSite(),
                            TransferStockLogType.USABLE_STOCK,whStock.getId(),whStock.getLocationNumber(), logStep, notPick, surplusQuantity, fbaNo));
                    stockLogs.add(new WhTransitStockLog(sku,whStock.getStore(), updateStock.getSite(),
                            TransferStockLogType.ALLOCATED,whStock.getId(),whStock.getLocationNumber(), logStep, -notPick, allotQuantity, fbaNo));
                }
                if (picked > 0) {

                    stockLogs.add(new WhTransitStockLog(sku,whStock.getStore(), updateStock.getSite(),
                            TransferStockLogType.PICKED_STOCK,whStock.getId(),whStock.getLocationNumber(), logStep, -picked, pickQuantity, fbaNo));
                    stockLogs.add(new WhTransitStockLog(sku,whStock.getStore(), updateStock.getSite(),
                            TransferStockLogType.PICKED_RETURN_STOCK,whStock.getId(),whStock.getLocationNumber(), logStep, picked, cancelQuantity, fbaNo));
                }
                if (notPick+picked>0){
                    // 推送fba库存变更到oms
                    OmsFbaSkuMessage omsFbaSkuMessage = new OmsFbaSkuMessage(whStock.getStore(),item.getSite(),sku, notPick+picked);
                    msgList.add(AssembleMessageDataUtils.assembleooFbaStockData(omsFbaSkuMessage));

                    // 推送中转仓库存变更到redis
                    msgList.add(AssembleMessageDataUtils.assembleDataToFinance(AmqMessageModuleName.PUSH_TRANSFER_STOCK_INFO.getCode(),
                            amqMessage -> {
                                amqMessage.setRelevantParam(sku);
                                Map<String,Object> map = new HashMap<>();
                                map.put("sku",sku);
                                map.put("count_surplus",(quantity));
                                map.put("saleChannel",(whStock.getRemark()));
                                // 消息体
                                String messageBody = JSONObject.toJSONString(map);
                                return messageBody;
                            }));
                }
            }
        }

        // 记录变更到whFbaChange中
        whFbaChangeService.batchCreateWhFbaChangeByWhFbaAllocationItems(whFbaAllocation.getItems(), CancelTypeEnum.WHOLE_ORDER_CANCEL.intCode());

        //更新中转仓库存明细
        if (CollectionUtils.isNotEmpty(stockList)){
            transferStockService.batchUpdateTransferStock(stockList);
        }

        // 添加库存变动批次明细
        transitBatchHandleService.createTransitBatchDetail(stockLogs, QuantityType.CHECK_IN,
                TransitBatchOrderType.CANCEL_ORDER, TransitBatchStockType.FBA, null);

        // 添加库存变动日志
        if (CollectionUtils.isNotEmpty(stockLogs)) {
            stockLogService.batchAddWhStockLog(stockLogs);
        }
        // 推送库存变更给oms
        if (CollectionUtils.isNotEmpty(msgList)){
            amqMessageService.batchCreateAmqMessage(msgList);
        }

        // 清除出库库存分配关联表
        whApvOutStockChainService.deleteWhApvOutStockChainByIds(whApvOutStockChains.stream().map(w -> w.getId()).collect(Collectors.toList()));
        // 迁移到分配取消表，用于拣货计算
        whApvOutStockChainCancelService.save(whApvOutStockChains, WhApvOutStockChainStatusEnum.CANCEL);
    }
}
