package com.estone.transfer.service.impl;

import com.estone.transfer.bean.JitPickBox;
import com.estone.transfer.bean.JitPickBoxQueryCondition;
import com.estone.transfer.dao.JitPickBoxDao;
import com.estone.transfer.service.JitPickBoxService;
import com.whq.tool.component.Pager;
import com.whq.tool.exception.BusinessException;
import com.whq.tool.exception.DBErrorCode;
import com.whq.tool.sqler.SqlerException;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

@Service("jitPickBoxService")
@Slf4j
public class JitPickBoxServiceImpl implements JitPickBoxService {
    @Resource
    private JitPickBoxDao jitPickBoxDao;

    @Override
    public JitPickBox getJitPickBox(Integer id) {
        JitPickBox jitPickBox = jitPickBoxDao.queryJitPickBox(id);
        return jitPickBox;
    }

    @Override
    public JitPickBox getJitPickBoxDetail(Integer id) {
        JitPickBox jitPickBox = jitPickBoxDao.queryJitPickBox(id);
        // 关联查询
        return jitPickBox;
    }

    @Override
    public JitPickBox queryJitPickBox(JitPickBoxQueryCondition query) {
        Assert.notNull(query, "query is null!");
        JitPickBox jitPickBox = jitPickBoxDao.queryJitPickBox(query);
        return jitPickBox;
    }

    @Override
    public List<JitPickBox> queryAllJitPickBoxs() {
        return jitPickBoxDao.queryJitPickBoxList();
    }

    @Override
    public List<JitPickBox> queryJitPickBoxs(JitPickBoxQueryCondition query, Pager pager) {
        Assert.notNull(query, "query is null!");
        if (pager != null && pager.isQueryCount()) {
            int count = jitPickBoxDao.queryJitPickBoxCount(query);
            pager.setTotalCount(count);
            if ( count == 0) {
                return new ArrayList<JitPickBox>();
            }
        }
        List<JitPickBox> jitPickBoxs = jitPickBoxDao.queryJitPickBoxList(query, pager);
        return jitPickBoxs;
    }

    @Override
    public void createJitPickBox(JitPickBox jitPickBox) {
        try {
            jitPickBoxDao.createJitPickBox(jitPickBox);
        }
        catch (SqlerException e) {
            log.error(e.getMessage(), e);
            throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
        }
    }

    @Override
    public void batchCreateJitPickBox(List<JitPickBox> entityList) {
        if (CollectionUtils.isNotEmpty(entityList)) {
            try {
                jitPickBoxDao.batchCreateJitPickBox(entityList);
            }
            catch (SqlerException e) {
                log.error(e.getMessage(), e);
                throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
            }
        }
    }

    @Override
    public void deleteJitPickBox(Integer id) {
        try {
            jitPickBoxDao.deleteJitPickBox(id);
        }
        catch (SqlerException e) {
            log.error(e.getMessage(), e);
            throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
        }
    }

    @Override
    public void updateJitPickBox(JitPickBox jitPickBox) {
        try {
            jitPickBoxDao.updateJitPickBox(jitPickBox);
        }
        catch (SqlerException e) {
            log.error(e.getMessage(), e);
            throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
        }
    }

    @Override
    public void batchUpdateJitPickBox(List<JitPickBox> entityList) {
        if (CollectionUtils.isNotEmpty(entityList)) {
            try {
                jitPickBoxDao.batchUpdateJitPickBox(entityList);
            }
            catch (SqlerException e) {
                log.error(e.getMessage(), e);
                throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
            }
        }
    }
}