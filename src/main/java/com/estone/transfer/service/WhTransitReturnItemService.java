package com.estone.transfer.service;

import com.estone.transfer.bean.WhTransitReturnItem;
import com.estone.transfer.bean.WhTransitReturnItemQueryCondition;
import com.estone.warehouse.bean.WhReturnItemQueryCondition;
import com.whq.tool.component.Pager;
import java.util.List;

public interface WhTransitReturnItemService {
    List<WhTransitReturnItem> queryAllWhTransitReturnItems();

    List<WhTransitReturnItem> queryWhTransitReturnItems(WhTransitReturnItemQueryCondition query, Pager pager);

    WhTransitReturnItem getWhTransitReturnItem(Integer id);

    WhTransitReturnItem getWhTransitReturnItemDetail(Integer id);

    WhTransitReturnItem queryWhTransitReturnItem(WhTransitReturnItemQueryCondition query);

    void createWhTransitReturnItem(WhTransitReturnItem whTransitReturnItem);

    void batchCreateWhTransitReturnItem(List<WhTransitReturnItem> entityList);

    void deleteWhTransitReturnItem(Integer id);

    void batchDeleteWhTransitReturnItem(List<Integer> ids);

    int updateWhTransitReturnItem(WhTransitReturnItem whTransitReturnItem);

    void batchUpdateWhTransitReturnItem(List<WhTransitReturnItem> entityList);

    int queryItemCount(WhTransitReturnItemQueryCondition query);
}