package com.estone.transfer.service;

import com.estone.transfer.bean.OwnerUserChange;
import com.estone.transfer.bean.OwnerUserChangeItem;
import com.estone.transfer.bean.OwnerUserChangeQueryCondition;
import com.whq.tool.component.Pager;
import java.util.List;

public interface OwnerUserChangeService {
    List<OwnerUserChange> queryAllOwnerUserChanges();

    List<OwnerUserChange> queryOwnerUserChanges(OwnerUserChangeQueryCondition query, Pager pager);

    OwnerUserChange getOwnerUserChange(Integer id);

    OwnerUserChange getOwnerUserChangeDetail(Integer id);

    OwnerUserChange queryOwnerUserChange(OwnerUserChangeQueryCondition query);

    void createOwnerUserChange(OwnerUserChange ownerUserChange);

    void batchCreateOwnerUserChange(List<OwnerUserChange> entityList);

    void deleteOwnerUserChange(Integer id);

    void updateOwnerUserChange(OwnerUserChange ownerUserChange);

    void batchUpdateOwnerUserChange(List<OwnerUserChange> entityList);

    /**
     * 插入货主变更单的主数据和item数据,并且修改对应的库存,对第三个表的操作写在一个方法中,方法名以do开头(do开头的方法有事务保护)
     * @param itemList
     * @return void
     * <AUTHOR>
     * @date 2021/11/30 14:36
     */
    void doSaveOwnerUserChange(List<OwnerUserChangeItem> itemList);
}