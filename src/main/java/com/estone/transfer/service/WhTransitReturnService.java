package com.estone.transfer.service;

import com.estone.android.domain.AndroidProductDo;
import com.estone.transfer.bean.WhFbaChange;
import com.estone.transfer.bean.WhTransitReturn;
import com.estone.transfer.bean.WhTransitReturnItem;
import com.estone.transfer.bean.WhTransitReturnQueryCondition;
import com.estone.warehouse.bean.WhApvAndWhSku;
import com.whq.tool.component.Pager;
import com.whq.tool.json.ResponseJson;

import java.util.List;

public interface WhTransitReturnService {
    List<WhTransitReturn> queryAllWhTransitReturns();

    List<WhTransitReturn> queryWhTransitReturns(WhTransitReturnQueryCondition query, Pager pager);

    WhTransitReturn getWhTransitReturn(Integer id);

    WhTransitReturn getWhTransitReturnDetail(Integer id);

    WhTransitReturn queryWhTransitReturn(WhTransitReturnQueryCondition query);

    void createWhTransitReturn(WhTransitReturn whTransitReturn);

    void batchCreateWhTransitReturn(List<WhTransitReturn> entityList);

    void deleteWhTransitReturn(Integer id);

    int updateWhTransitReturn(WhTransitReturn whTransitReturn);

    void batchUpdateWhTransitReturn(List<WhTransitReturn> entityList);

    List<WhTransitReturn> queryWhTransitReturnAndItems(WhTransitReturnQueryCondition query, Pager pager);

    WhTransitReturn completeWhReturn(Integer id, boolean error);

    ResponseJson createWhReturnAndItem(List<String> skuList, List<WhTransitReturn> whReturnList);

    WhApvAndWhSku updateApvSkuDetail(List<String> skuList,String uuid);

    ResponseJson scanBoxNoUpdateReturnStatusAndUpQuantity(List<String> skuList, WhTransitReturn whReturn, List<WhFbaChange> whFbaChanges);

    ResponseJson updateReturnItemAndComplete(WhTransitReturnItem whReturnItem, AndroidProductDo domain,List<WhFbaChange> fbaChanges);

    ResponseJson confirmReturnOrder(WhTransitReturn whTransitReturn);


}