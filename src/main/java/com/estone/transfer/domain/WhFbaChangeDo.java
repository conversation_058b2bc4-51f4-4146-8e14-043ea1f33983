package com.estone.transfer.domain;

import com.estone.transfer.bean.WhFbaChange;
import com.estone.transfer.bean.WhFbaChangeQueryCondition;
import com.whq.tool.component.Pager;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;
@Data
public class WhFbaChangeDo {
    private WhFbaChange whFbaChange;

    private WhFbaChangeQueryCondition query;

    private List<WhFbaChange> whFbaChanges = new ArrayList<WhFbaChange>();

    private Pager page = new Pager();

    private String orderItemStatusJson;

    private String deliveryTypeJson;

    private String statusJson;

    private String cancelTypeJson;

    private String changeStatusJson;

    public WhFbaChange getWhFbaChange() {
        return whFbaChange;
    }

    public void setWhFbaChange(WhFbaChange whFbaChange) {
        this.whFbaChange = whFbaChange;
    }

    public WhFbaChangeQueryCondition getQuery() {
        return query;
    }

    public void setQuery(WhFbaChangeQueryCondition query) {
        this.query = query;
    }

    public List<WhFbaChange> getWhFbaChanges() {
        return whFbaChanges;
    }

    public void setWhFbaChanges(List<WhFbaChange> whFbaChanges) {
        this.whFbaChanges = whFbaChanges;
    }

    public Pager getPage() {
        return page;
    }

    public void setPage(Pager page) {
        this.page = page;
    }
}