package com.estone.transfer.domain;

import com.estone.transfer.bean.TransferStockTimeRelation;
import com.estone.transfer.bean.TransferStockTimeRelationQueryCondition;
import com.whq.tool.component.Pager;
import java.util.ArrayList;
import java.util.List;

public class TransferStockTimeRelationDo {
    private TransferStockTimeRelation transferStockTimeRelation;

    private TransferStockTimeRelationQueryCondition query;

    private List<TransferStockTimeRelation> transferStockTimeRelations = new ArrayList<TransferStockTimeRelation>();

    private Pager page = new Pager();

    public TransferStockTimeRelation getTransferStockTimeRelation() {
        return transferStockTimeRelation;
    }

    public void setTransferStockTimeRelation(TransferStockTimeRelation transferStockTimeRelation) {
        this.transferStockTimeRelation = transferStockTimeRelation;
    }

    public TransferStockTimeRelationQueryCondition getQuery() {
        return query;
    }

    public void setQuery(TransferStockTimeRelationQueryCondition query) {
        this.query = query;
    }

    public List<TransferStockTimeRelation> getTransferStockTimeRelations() {
        return transferStockTimeRelations;
    }

    public void setTransferStockTimeRelations(List<TransferStockTimeRelation> transferStockTimeRelations) {
        this.transferStockTimeRelations = transferStockTimeRelations;
    }

    public Pager getPage() {
        return page;
    }

    public void setPage(Pager page) {
        this.page = page;
    }
}