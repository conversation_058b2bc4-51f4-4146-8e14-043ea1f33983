package com.estone.transfer.domain;

import java.util.List;
import java.util.Map;

import com.estone.picking.bean.WhPickingTask;
import com.estone.picking.bean.WhPickingTaskSku;
import com.estone.transfer.bean.WhFbaAllocation;
import com.estone.transfer.bean.WhFbaAllocationItem;

import com.estone.warehouse.bean.WhBox;
import lombok.Data;

@Data
public class FbaGridDo {

    /**
     * 错误消息
     */
    private String errorMsg;

    /**
     * 扫描完成的apv数量
     */
    private Integer successApvCount;

    private Integer pickQuantity;

    private Integer gridQuantity;

    /**
     * 周转框
     */
    private String boxNo;

    private String sku;

    private String uuid;

    private List<WhFbaAllocation> whFbaAllocationList;

    private List<WhFbaAllocationItem> taskItemList; //fNSKU

    private List<WhFbaAllocationItem> lessGridItemList;// 播种差异（播种<已拣）

    private List<WhFbaAllocationItem> lessPickItemList;// 少拣的

    private List<WhFbaAllocationItem> cancelGridItemList;// 取消的

    private List<WhPickingTaskSku> whPickingTaskSkuList;

    private boolean fbaAllGrid;//发货单对应的FNSKU全部播种完成

    private boolean taskAllGrid;//拣货任务全部播种完成

    private WhFbaAllocationItem fnSkuItem;

    private Integer scanNumber;

    private Integer taskType;

    private Integer gridStatus;

    private String lastFnSku;

    private WhPickingTask whPickingTask;

    private String fbaNo;

    private String fnSku;

    private String boxCayi;

    private String boxStockOut;

    private WhBox whBox;

    private String taskNo;

    private Double fnSkuTollWeight;

    private boolean suitSku;
}