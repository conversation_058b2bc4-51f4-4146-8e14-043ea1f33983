package com.estone.transfer.dao.impl;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import com.estone.apv.dao.mapper.WhApvDBField;
import com.estone.transfer.enums.AsnPrepareStatus;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.stereotype.Repository;
import org.springframework.util.Assert;

import com.estone.transfer.bean.WhFbaAllocationItem;
import com.estone.transfer.bean.WhFbaAllocationItemQueryCondition;
import com.estone.transfer.dao.WhFbaAllocationItemDao;
import com.estone.transfer.dao.mapper.WhFbaAllocationItemDBField;
import com.estone.transfer.dao.mapper.WhFbaAllocationItemMapper;
import com.whq.tool.component.Pager;
import com.whq.tool.sqler.SqlerRequest;
import com.whq.tool.sqler.analyzer.DataType;
import com.estone.common.util.SqlerTemplate;

@Repository("whFbaAllocationItemDao")
public class WhFbaAllocationItemDaoImpl implements WhFbaAllocationItemDao {

    private void setQueryCondition(SqlerRequest request, WhFbaAllocationItemQueryCondition query) {
        if (query == null) {
            return;
        }
        // 添加查询条件
        // 搜索条件不允许出现空字符
        request.setAllowBlank(false);
        
        request.addDataParam(WhFbaAllocationItemDBField.ID, DataType.INT, query.getId());
        request.addDataParam("ids", DataType.INT, query.getIds());
        request.addDataParam(WhFbaAllocationItemDBField.FBA_ID, DataType.INT, query.getFbaId());
        request.addDataParam(WhFbaAllocationItemDBField.PRODUCT_SKU, DataType.STRING, query.getProductSku());
        request.addDataParam(WhFbaAllocationItemDBField.STORE, DataType.STRING, query.getStore());
        request.addDataParam(WhFbaAllocationItemDBField.SELL_SKU, DataType.STRING, query.getSellSku());
        request.addDataParam(WhFbaAllocationItemDBField.FN_SKU, DataType.STRING, query.getFnSku());
        request.addDataParam(WhFbaAllocationItemDBField.GRID_STATUS, DataType.STRING, query.getGridStatus());
        request.addDataParam(WhFbaAllocationItemDBField.GRID_BY, DataType.STRING, query.getGridBy());
        request.addDataParam(WhFbaAllocationItemDBField.TAG_BY, DataType.INT, query.getTagBy());
        request.addDataParam(WhFbaAllocationItemDBField.TAG_TIME, DataType.TIMESTAMP, query.getTagTime());
        request.addDataParam(WhFbaAllocationItemDBField.PROCESS_TYPE, DataType.INT, query.getProcessType());
        request.addDataParam(WhFbaAllocationItemDBField.RE_PROCESS, DataType.BOOLEAN, query.getReProcess());
    }

    @Override
    public int queryWhFbaAllocationItemCount(WhFbaAllocationItemQueryCondition query) {
        SqlerRequest request = new SqlerRequest("queryWhFbaAllocationItemCount");
        setQueryCondition(request, query);
        return SqlerTemplate.queryForInt(request);
    }

    @Override
    public List<WhFbaAllocationItem> queryWhFbaAllocationItemList() {
        SqlerRequest request = new SqlerRequest("queryWhFbaAllocationItemList");
        return SqlerTemplate.query(request, new WhFbaAllocationItemMapper());
    }

    @Override
    public List<WhFbaAllocationItem> queryWhFbaAllocationItemList(WhFbaAllocationItemQueryCondition query, Pager pager) {
        SqlerRequest request = new SqlerRequest("queryWhFbaAllocationItemList");
        setQueryCondition(request, query);
        if(pager != null) {
            request.addFetch(pager.getPageNo(), pager.getPageSize());
        }
        return SqlerTemplate.query(request, new WhFbaAllocationItemMapper());
    }

    @Override
    public WhFbaAllocationItem queryWhFbaAllocationItem(Integer primaryKey) {
        Assert.notNull(primaryKey, "primaryKey is null!");
        SqlerRequest request = new SqlerRequest("queryWhFbaAllocationItemByPrimaryKey");
        request.addDataParam(WhFbaAllocationItemDBField.ID, DataType.INT, primaryKey);
        return SqlerTemplate.queryForObject(request, new WhFbaAllocationItemMapper());
    }

    @Override
    public WhFbaAllocationItem queryWhFbaAllocationItem(WhFbaAllocationItemQueryCondition query) {
        SqlerRequest request = new SqlerRequest("queryWhFbaAllocationItem");
        setQueryCondition(request, query);
        return SqlerTemplate.queryForObject(request, new WhFbaAllocationItemMapper());
    }

    @Override
    public void createWhFbaAllocationItem(WhFbaAllocationItem entity) {
        SqlerRequest request = new SqlerRequest("createWhFbaAllocationItem");
        request.addDataParam(WhFbaAllocationItemDBField.FBA_ID, DataType.INT, entity.getFbaId());
        request.addDataParam(WhFbaAllocationItemDBField.BOX_NO, DataType.INT, entity.getBoxNo());
        request.addDataParam(WhFbaAllocationItemDBField.WAREHOUSE_ID, DataType.INT, entity.getWarehouseId());
        request.addDataParam(WhFbaAllocationItemDBField.PRODUCT_SKU, DataType.STRING, entity.getProductSku());
        request.addDataParam(WhFbaAllocationItemDBField.PRODUCT_BARCODE, DataType.STRING, entity.getProductBarcode());
        request.addDataParam(WhFbaAllocationItemDBField.STORE, DataType.STRING, entity.getStore());
        request.addDataParam(WhFbaAllocationItemDBField.SITE, DataType.STRING, entity.getSite());
        request.addDataParam(WhFbaAllocationItemDBField.SKU_TAGS, DataType.STRING, entity.getSkuTags());
        request.addDataParam(WhFbaAllocationItemDBField.SKU_SUIT_NUM, DataType.INT, entity.getSkuSuitNum());
        request.addDataParam(WhFbaAllocationItemDBField.FN_SKU, DataType.STRING, entity.getFnSku());
        request.addDataParam(WhFbaAllocationItemDBField.SELL_SKU, DataType.STRING, entity.getSellSku());
        request.addDataParam(WhFbaAllocationItemDBField.QUANTITY, DataType.INT, entity.getQuantity());
        request.addDataParam(WhFbaAllocationItemDBField.SKU_QUANTITY, DataType.INT, entity.getSkuQuantity());
        request.addDataParam(WhFbaAllocationItemDBField.LOAD_NUM, DataType.INT, entity.getLoadNum());
        request.addDataParam(WhFbaAllocationItemDBField.SUIT_FLAG, DataType.INT, entity.getSuitFlag());
        request.addDataParam(WhFbaAllocationItemDBField.LOADING_QUANTITY, DataType.INT, entity.getLoadingQuantity());
        request.addDataParam(WhFbaAllocationItemDBField.PUTAWAY_QUANTITY, DataType.INT, entity.getPutawayQuantity());
        request.addDataParam(WhFbaAllocationItemDBField.ALLOT_QUANTITY, DataType.INT, entity.getAllotQuantity());
        request.addDataParam(WhFbaAllocationItemDBField.PICK_QUANTITY, DataType.INT, entity.getPickQuantity());
        request.addDataParam(WhFbaAllocationItemDBField.GRID_QUANTITY, DataType.INT, entity.getGridQuantity());
        request.addDataParam(WhFbaAllocationItemDBField.PUTAWAY_DIFF, DataType.INT, entity.getPutawayDiff());
        request.addDataParam(WhFbaAllocationItemDBField.PRODUCT_LENGTH, DataType.DOUBLE, entity.getProductLength());
        request.addDataParam(WhFbaAllocationItemDBField.PRODUCT_WIDTH, DataType.DOUBLE, entity.getProductWidth());
        request.addDataParam(WhFbaAllocationItemDBField.PRODUCT_HEIGHT, DataType.DOUBLE, entity.getProductHeight());
        request.addDataParam(WhFbaAllocationItemDBField.PRODUCT_WEIGHT, DataType.DOUBLE, entity.getProductWeight());
        request.addDataParam(WhFbaAllocationItemDBField.TOLL_WEIGHT, DataType.DOUBLE, entity.getTollWeight());
        request.addDataParam(WhFbaAllocationItemDBField.FREIGHT, DataType.DOUBLE, entity.getFreight());
        request.addDataParam(WhFbaAllocationItemDBField.BOX_COST, DataType.DOUBLE, entity.getBoxCost());
        request.addDataParam(WhFbaAllocationItemDBField.SKU_COST, DataType.DOUBLE, entity.getSkuCost());
        request.addDataParam(WhFbaAllocationItemDBField.OTHER_PRICE, DataType.DOUBLE, entity.getOtherPrice());
        request.addDataParam(WhFbaAllocationItemDBField.PRODUCT_PRICE, DataType.DOUBLE, entity.getProductPrice());
        request.addDataParam(WhFbaAllocationItemDBField.SELL_SKU_NAME, DataType.STRING, entity.getSellSkuName());
        request.addDataParam(WhFbaAllocationItemDBField.SKU_IMG, DataType.STRING, entity.getSkuImg());
        request.addDataParam(WhFbaAllocationItemDBField.FN_SKU_TAGS, DataType.STRING, entity.getFnSkuTags());
        request.addDataParam(WhFbaAllocationItemDBField.GRID_STATUS, DataType.INT, entity.getGridStatus());
        request.addDataParam(WhFbaAllocationItemDBField.GRID_BY, DataType.INT, entity.getGridBy());
        request.addDataParam(WhFbaAllocationItemDBField.GRID_TIME, DataType.TIMESTAMP, entity.getGridTime());
        request.addDataParam(WhFbaAllocationItemDBField.TAG_BY, DataType.INT, entity.getTagBy());
        request.addDataParam(WhFbaAllocationItemDBField.TAG_TIME, DataType.TIMESTAMP, entity.getTagTime());
        request.addDataParam(WhFbaAllocationItemDBField.PROCESS_TYPE, DataType.INT, entity.getProcessType());
        request.addDataParam(WhFbaAllocationItemDBField.RE_PROCESS, DataType.BOOLEAN, entity.getReProcess());
        request.addDataParam(WhFbaAllocationItemDBField.REMARK, DataType.STRING, entity.getRemark());
        request.addDataParam(WhFbaAllocationItemDBField.TAG, DataType.STRING, entity.getTag());
        request.addDataParam(WhFbaAllocationItemDBField.TEMU_CODE_URL, DataType.STRING, entity.getTemuCodeUrl());
        request.addDataParam(WhFbaAllocationItemDBField.TEMU_TAG_URL, DataType.STRING, entity.getTemuTagUrl());
        request.addDataParam(WhFbaAllocationItemDBField.SKU_BARCODE, DataType.STRING, entity.getSkuBarcode());
        request.addDataParam(WhFbaAllocationItemDBField.SC_ITEM_ID, DataType.LONG, entity.getScItemId());
        Integer autoIncrementId = SqlerTemplate.executeAndReturn(request);
        entity.setId(autoIncrementId);
    }

    @Override
    public void updateWhFbaAllocationItem(WhFbaAllocationItem entity) {
        SqlerRequest request = new SqlerRequest("updateWhFbaAllocationItemByPrimaryKey");
        request.addDataParam(WhFbaAllocationItemDBField.ID, DataType.INT, entity.getId());
        
        request.addDataParam(WhFbaAllocationItemDBField.FBA_ID, DataType.INT, entity.getFbaId());
        request.addDataParam(WhFbaAllocationItemDBField.BOX_NO, DataType.INT, entity.getBoxNo());
        request.addDataParam(WhFbaAllocationItemDBField.WAREHOUSE_ID, DataType.INT, entity.getWarehouseId());
        request.addDataParam(WhFbaAllocationItemDBField.PRODUCT_SKU, DataType.STRING, entity.getProductSku());
        request.addDataParam(WhFbaAllocationItemDBField.PRODUCT_BARCODE, DataType.STRING, entity.getProductBarcode());
        request.addDataParam(WhFbaAllocationItemDBField.STORE, DataType.STRING, entity.getStore());
        request.addDataParam(WhFbaAllocationItemDBField.SITE, DataType.STRING, entity.getSite());
        request.addDataParam(WhFbaAllocationItemDBField.FN_SKU, DataType.STRING, entity.getFnSku());
        request.addDataParam(WhFbaAllocationItemDBField.SKU_TAGS, DataType.STRING, entity.getSkuTags());
        request.addDataParam(WhFbaAllocationItemDBField.SELL_SKU, DataType.STRING, entity.getSellSku());
        request.addDataParam(WhFbaAllocationItemDBField.QUANTITY, DataType.INT, entity.getQuantity());
        request.addDataParam(WhFbaAllocationItemDBField.SKU_QUANTITY, DataType.INT, entity.getSkuQuantity());
        request.addDataParam(WhFbaAllocationItemDBField.SKU_SUIT_NUM, DataType.INT, entity.getSkuSuitNum());
        request.addDataParam(WhFbaAllocationItemDBField.LOAD_NUM, DataType.INT, entity.getLoadNum());
        request.addDataParam(WhFbaAllocationItemDBField.SUIT_FLAG, DataType.INT, entity.getSuitFlag());
        request.addDataParam(WhFbaAllocationItemDBField.LOADING_QUANTITY, DataType.INT, entity.getLoadingQuantity());
        request.addDataParam(WhFbaAllocationItemDBField.PUTAWAY_QUANTITY, DataType.INT, entity.getPutawayQuantity());
        request.addDataParam(WhFbaAllocationItemDBField.ALLOT_QUANTITY, DataType.INT, entity.getAllotQuantity());
        request.addDataParam(WhFbaAllocationItemDBField.PICK_QUANTITY, DataType.INT, entity.getPickQuantity());
        request.addDataParam(WhFbaAllocationItemDBField.GRID_QUANTITY, DataType.INT, entity.getGridQuantity());
        request.addDataParam(WhFbaAllocationItemDBField.PUTAWAY_DIFF, DataType.INT, entity.getPutawayDiff());
        request.addDataParam(WhFbaAllocationItemDBField.PRODUCT_LENGTH, DataType.DOUBLE, entity.getProductLength());
        request.addDataParam(WhFbaAllocationItemDBField.PRODUCT_WIDTH, DataType.DOUBLE, entity.getProductWidth());
        request.addDataParam(WhFbaAllocationItemDBField.PRODUCT_HEIGHT, DataType.DOUBLE, entity.getProductHeight());
        request.addDataParam(WhFbaAllocationItemDBField.PRODUCT_WEIGHT, DataType.DOUBLE, entity.getProductWeight());
        request.addDataParam(WhFbaAllocationItemDBField.TOLL_WEIGHT, DataType.DOUBLE, entity.getTollWeight());
        request.addDataParam(WhFbaAllocationItemDBField.FREIGHT, DataType.DOUBLE, entity.getFreight());
        request.addDataParam(WhFbaAllocationItemDBField.BOX_COST, DataType.DOUBLE, entity.getBoxCost());
        request.addDataParam(WhFbaAllocationItemDBField.SKU_COST, DataType.DOUBLE, entity.getSkuCost());
        request.addDataParam(WhFbaAllocationItemDBField.OTHER_PRICE, DataType.DOUBLE, entity.getOtherPrice());
        request.addDataParam(WhFbaAllocationItemDBField.PRODUCT_PRICE, DataType.DOUBLE, entity.getProductPrice());
        request.addDataParam(WhFbaAllocationItemDBField.SELL_SKU_NAME, DataType.STRING, entity.getSellSkuName());
        request.addDataParam(WhFbaAllocationItemDBField.SKU_IMG, DataType.STRING, entity.getSkuImg());
        request.addDataParam(WhFbaAllocationItemDBField.FN_SKU_TAGS, DataType.STRING, entity.getFnSkuTags());
        request.addDataParam(WhFbaAllocationItemDBField.GRID_STATUS, DataType.INT, entity.getGridStatus());
        request.addDataParam(WhFbaAllocationItemDBField.GRID_BY, DataType.INT, entity.getGridBy());
        request.addDataParam(WhFbaAllocationItemDBField.GRID_TIME, DataType.TIMESTAMP, entity.getGridTime());
        request.addDataParam(WhFbaAllocationItemDBField.TAG_BY, DataType.INT, entity.getTagBy());
        request.addDataParam(WhFbaAllocationItemDBField.TAG_TIME, DataType.TIMESTAMP, entity.getTagTime());
        request.addDataParam(WhFbaAllocationItemDBField.REMARK, DataType.STRING, entity.getRemark());
        request.addDataParam(WhFbaAllocationItemDBField.PROCESS_TYPE, DataType.INT, entity.getProcessType());
        request.addDataParam(WhFbaAllocationItemDBField.RE_PROCESS, DataType.BOOLEAN, entity.getReProcess());
        request.addDataParam(WhFbaAllocationItemDBField.TEMU_CODE_URL, DataType.STRING, entity.getTemuCodeUrl());
        request.addDataParam(WhFbaAllocationItemDBField.TEMU_TAG_URL, DataType.STRING, entity.getTemuTagUrl());
        request.addDataParam(WhFbaAllocationItemDBField.SKU_BARCODE, DataType.STRING, entity.getSkuBarcode());
        request.addDataParam(WhFbaAllocationItemDBField.SC_ITEM_ID, DataType.LONG, entity.getScItemId());
        request.addDataParam(WhFbaAllocationItemDBField.LOAD_BY, DataType.INT, entity.getLoadBy());
        request.addDataParam(WhFbaAllocationItemDBField.LOAD_TIME, DataType.TIMESTAMP, entity.getLoadTime());
        request.addDataParam(WhFbaAllocationItemDBField.COMPANY_NAME, DataType.STRING, entity.getCompanyName());
        request.addSqlDataParam("RESET_SHIPPING_COST",entity.getResetCostSql());
        SqlerTemplate.execute(request);
    }

    @Override
    public List<String> selectFbaAccount() {
        SqlerRequest request = new SqlerRequest("selectFbaAccount");
        List<Map<String, Object>> maps = SqlerTemplate.queryForList(request);
        List<String> account_number = maps.stream().map(m -> (String)m.get("account_number")).collect(Collectors.toList());
        return account_number;
    }

    @Override
    public void batchCreateWhFbaAllocationItem(List<WhFbaAllocationItem> entityList) {
        if (entityList != null && !entityList.isEmpty()) {
            SqlerRequest request = new SqlerRequest("createWhFbaAllocationItem");
            for (WhFbaAllocationItem entity : entityList) {
                request.addBatchDataParam(WhFbaAllocationItemDBField.FBA_ID, DataType.INT, entity.getFbaId());
                request.addBatchDataParam(WhFbaAllocationItemDBField.BOX_NO, DataType.INT, entity.getBoxNo());
                request.addBatchDataParam(WhFbaAllocationItemDBField.WAREHOUSE_ID, DataType.INT, entity.getWarehouseId());
                request.addBatchDataParam(WhFbaAllocationItemDBField.PRODUCT_SKU, DataType.STRING, entity.getProductSku());
                request.addBatchDataParam(WhFbaAllocationItemDBField.PRODUCT_BARCODE, DataType.STRING, entity.getProductBarcode());
                request.addBatchDataParam(WhFbaAllocationItemDBField.STORE, DataType.STRING, entity.getStore());
                request.addBatchDataParam(WhFbaAllocationItemDBField.SITE, DataType.STRING, entity.getSite());
                request.addBatchDataParam(WhFbaAllocationItemDBField.FN_SKU, DataType.STRING, entity.getFnSku());
                request.addBatchDataParam(WhFbaAllocationItemDBField.SELL_SKU, DataType.STRING, entity.getSellSku());
                request.addBatchDataParam(WhFbaAllocationItemDBField.SKU_TAGS, DataType.STRING, entity.getSkuTags());
                request.addBatchDataParam(WhFbaAllocationItemDBField.QUANTITY, DataType.INT, entity.getQuantity());
                request.addBatchDataParam(WhFbaAllocationItemDBField.SKU_QUANTITY, DataType.INT, entity.getSkuQuantity());
                request.addBatchDataParam(WhFbaAllocationItemDBField.SKU_SUIT_NUM, DataType.INT, entity.getSkuSuitNum());
                request.addBatchDataParam(WhFbaAllocationItemDBField.LOAD_NUM, DataType.INT, entity.getLoadNum());
                request.addBatchDataParam(WhFbaAllocationItemDBField.SUIT_FLAG, DataType.INT, entity.getSuitFlag());
                request.addBatchDataParam(WhFbaAllocationItemDBField.LOADING_QUANTITY, DataType.INT, entity.getLoadingQuantity());
                request.addBatchDataParam(WhFbaAllocationItemDBField.PUTAWAY_QUANTITY, DataType.INT, entity.getPutawayQuantity());
                request.addBatchDataParam(WhFbaAllocationItemDBField.ALLOT_QUANTITY, DataType.INT, entity.getAllotQuantity());
                request.addBatchDataParam(WhFbaAllocationItemDBField.PICK_QUANTITY, DataType.INT, entity.getPickQuantity());
                request.addBatchDataParam(WhFbaAllocationItemDBField.GRID_QUANTITY, DataType.INT, entity.getGridQuantity());
                request.addBatchDataParam(WhFbaAllocationItemDBField.PUTAWAY_DIFF, DataType.INT, entity.getPutawayDiff());
                request.addBatchDataParam(WhFbaAllocationItemDBField.PRODUCT_LENGTH, DataType.DOUBLE, entity.getProductLength());
                request.addBatchDataParam(WhFbaAllocationItemDBField.PRODUCT_WIDTH, DataType.DOUBLE, entity.getProductWidth());
                request.addBatchDataParam(WhFbaAllocationItemDBField.PRODUCT_HEIGHT, DataType.DOUBLE, entity.getProductHeight());
                request.addBatchDataParam(WhFbaAllocationItemDBField.PRODUCT_WEIGHT, DataType.DOUBLE, entity.getProductWeight());
                request.addBatchDataParam(WhFbaAllocationItemDBField.TOLL_WEIGHT, DataType.DOUBLE, entity.getTollWeight());
                request.addBatchDataParam(WhFbaAllocationItemDBField.FREIGHT, DataType.DOUBLE, entity.getFreight());
                request.addBatchDataParam(WhFbaAllocationItemDBField.BOX_COST, DataType.DOUBLE, entity.getBoxCost());
                request.addBatchDataParam(WhFbaAllocationItemDBField.SKU_COST, DataType.DOUBLE, entity.getSkuCost());
                request.addBatchDataParam(WhFbaAllocationItemDBField.OTHER_PRICE, DataType.DOUBLE, entity.getOtherPrice());
                request.addBatchDataParam(WhFbaAllocationItemDBField.PRODUCT_PRICE, DataType.DOUBLE, entity.getProductPrice());
                request.addBatchDataParam(WhFbaAllocationItemDBField.SELL_SKU_NAME, DataType.STRING, entity.getSellSkuName());
                request.addBatchDataParam(WhFbaAllocationItemDBField.SKU_IMG, DataType.STRING, entity.getSkuImg());
                request.addBatchDataParam(WhFbaAllocationItemDBField.FN_SKU_TAGS, DataType.STRING, entity.getFnSkuTags());
                request.addBatchDataParam(WhFbaAllocationItemDBField.GRID_STATUS, DataType.INT, entity.getGridStatus());
                request.addBatchDataParam(WhFbaAllocationItemDBField.GRID_BY, DataType.INT, entity.getGridBy());
                request.addBatchDataParam(WhFbaAllocationItemDBField.GRID_TIME, DataType.TIMESTAMP, entity.getGridTime());
                request.addBatchDataParam(WhFbaAllocationItemDBField.TAG_BY, DataType.INT, entity.getTagBy());
                request.addBatchDataParam(WhFbaAllocationItemDBField.TAG_TIME, DataType.TIMESTAMP, entity.getTagTime());
                request.addBatchDataParam(WhFbaAllocationItemDBField.PROCESS_TYPE, DataType.INT, entity.getProcessType());
                request.addBatchDataParam(WhFbaAllocationItemDBField.RE_PROCESS, DataType.BOOLEAN, entity.getReProcess());
                request.addBatchDataParam(WhFbaAllocationItemDBField.TAG, DataType.STRING, entity.getTag());
                request.addBatchDataParam(WhFbaAllocationItemDBField.TEMU_CODE_URL, DataType.STRING, entity.getTemuCodeUrl());
                request.addBatchDataParam(WhFbaAllocationItemDBField.TEMU_TAG_URL, DataType.STRING, entity.getTemuTagUrl());
                request.addBatchDataParam(WhFbaAllocationItemDBField.SKU_BARCODE, DataType.STRING, entity.getSkuBarcode());
                request.addBatchDataParam(WhFbaAllocationItemDBField.SC_ITEM_ID, DataType.LONG, entity.getScItemId());
                request.addBatchDataParam(WhFbaAllocationItemDBField.REMARK, DataType.STRING, entity.getRemark());
                request.addBatch();
            }
            SqlerTemplate.batchUpdate(request);
        }
    }

    @Override
    public void batchUpdateWhFbaAllocationItem(List<WhFbaAllocationItem> entityList) {
        if (entityList != null && !entityList.isEmpty()) {
            SqlerRequest request = new SqlerRequest("updateWhFbaAllocationItemByPrimaryKey");
            for (WhFbaAllocationItem entity : entityList) {
                request.addBatchDataParam(WhFbaAllocationItemDBField.ID, DataType.INT, entity.getId());
                
                request.addBatchDataParam(WhFbaAllocationItemDBField.FBA_ID, DataType.INT, entity.getFbaId());
                request.addBatchDataParam(WhFbaAllocationItemDBField.BOX_NO, DataType.INT, entity.getBoxNo());
                request.addBatchDataParam(WhFbaAllocationItemDBField.WAREHOUSE_ID, DataType.INT, entity.getWarehouseId());
                request.addBatchDataParam(WhFbaAllocationItemDBField.PRODUCT_SKU, DataType.STRING, entity.getProductSku());
                request.addBatchDataParam(WhFbaAllocationItemDBField.PRODUCT_BARCODE, DataType.STRING, entity.getProductBarcode());
                request.addBatchDataParam(WhFbaAllocationItemDBField.STORE, DataType.STRING, entity.getStore());
                request.addBatchDataParam(WhFbaAllocationItemDBField.SITE, DataType.STRING, entity.getSite());
                request.addBatchDataParam(WhFbaAllocationItemDBField.FN_SKU, DataType.STRING, entity.getFnSku());
                request.addBatchDataParam(WhFbaAllocationItemDBField.SKU_TAGS, DataType.STRING, entity.getSkuTags());
                request.addBatchDataParam(WhFbaAllocationItemDBField.SELL_SKU, DataType.STRING, entity.getSellSku());
                request.addBatchDataParam(WhFbaAllocationItemDBField.QUANTITY, DataType.INT, entity.getQuantity());
                request.addBatchDataParam(WhFbaAllocationItemDBField.SKU_QUANTITY, DataType.INT, entity.getSkuQuantity());
                request.addBatchDataParam(WhFbaAllocationItemDBField.SKU_SUIT_NUM, DataType.INT, entity.getSkuSuitNum());
                request.addBatchDataParam(WhFbaAllocationItemDBField.LOAD_NUM, DataType.INT, entity.getLoadNum());
                request.addBatchDataParam(WhFbaAllocationItemDBField.SUIT_FLAG, DataType.INT, entity.getSuitFlag());
                request.addBatchDataParam(WhFbaAllocationItemDBField.LOADING_QUANTITY, DataType.INT, entity.getLoadingQuantity());
                request.addBatchDataParam(WhFbaAllocationItemDBField.PUTAWAY_QUANTITY, DataType.INT, entity.getPutawayQuantity());
                request.addBatchDataParam(WhFbaAllocationItemDBField.ALLOT_QUANTITY, DataType.INT, entity.getAllotQuantity());
                request.addBatchDataParam(WhFbaAllocationItemDBField.PICK_QUANTITY, DataType.INT, entity.getPickQuantity());
                request.addBatchDataParam(WhFbaAllocationItemDBField.GRID_QUANTITY, DataType.INT, entity.getGridQuantity());
                request.addBatchDataParam(WhFbaAllocationItemDBField.PUTAWAY_DIFF, DataType.INT, entity.getPutawayDiff());
                request.addBatchDataParam(WhFbaAllocationItemDBField.PRODUCT_LENGTH, DataType.DOUBLE, entity.getProductLength());
                request.addBatchDataParam(WhFbaAllocationItemDBField.PRODUCT_WIDTH, DataType.DOUBLE, entity.getProductWidth());
                request.addBatchDataParam(WhFbaAllocationItemDBField.PRODUCT_HEIGHT, DataType.DOUBLE, entity.getProductHeight());
                request.addBatchDataParam(WhFbaAllocationItemDBField.PRODUCT_WEIGHT, DataType.DOUBLE, entity.getProductWeight());
                request.addBatchDataParam(WhFbaAllocationItemDBField.TOLL_WEIGHT, DataType.DOUBLE, entity.getTollWeight());
                request.addBatchDataParam(WhFbaAllocationItemDBField.FREIGHT, DataType.DOUBLE, entity.getFreight());
                request.addBatchDataParam(WhFbaAllocationItemDBField.BOX_COST, DataType.DOUBLE, entity.getBoxCost());
                request.addBatchDataParam(WhFbaAllocationItemDBField.SKU_COST, DataType.DOUBLE, entity.getSkuCost());
                request.addBatchDataParam(WhFbaAllocationItemDBField.OTHER_PRICE, DataType.DOUBLE, entity.getOtherPrice());
                request.addBatchDataParam(WhFbaAllocationItemDBField.PRODUCT_PRICE, DataType.DOUBLE, entity.getProductPrice());
                request.addBatchDataParam(WhFbaAllocationItemDBField.SELL_SKU_NAME, DataType.STRING, entity.getSellSkuName());
                request.addBatchDataParam(WhFbaAllocationItemDBField.SKU_IMG, DataType.STRING, entity.getSkuImg());
                request.addBatchDataParam(WhFbaAllocationItemDBField.FN_SKU_TAGS, DataType.STRING, entity.getFnSkuTags());
                request.addBatchDataParam(WhFbaAllocationItemDBField.GRID_STATUS, DataType.INT, entity.getGridStatus());
                request.addBatchDataParam(WhFbaAllocationItemDBField.GRID_BY, DataType.INT, entity.getGridBy());
                request.addBatchDataParam(WhFbaAllocationItemDBField.GRID_TIME, DataType.TIMESTAMP, entity.getGridTime());
                request.addBatchDataParam(WhFbaAllocationItemDBField.TAG_BY, DataType.INT, entity.getTagBy());
                request.addBatchDataParam(WhFbaAllocationItemDBField.TAG_TIME, DataType.TIMESTAMP, entity.getTagTime());
                request.addBatchDataParam(WhFbaAllocationItemDBField.PROCESS_TYPE, DataType.INT, entity.getProcessType());
                request.addBatchDataParam(WhFbaAllocationItemDBField.RE_PROCESS, DataType.BOOLEAN, entity.getReProcess());
                request.addBatchDataParam(WhFbaAllocationItemDBField.TEMU_CODE_URL, DataType.STRING, entity.getTemuCodeUrl());
                request.addBatchDataParam(WhFbaAllocationItemDBField.TEMU_TAG_URL, DataType.STRING, entity.getTemuTagUrl());
                request.addBatchDataParam(WhFbaAllocationItemDBField.SKU_BARCODE, DataType.STRING, entity.getSkuBarcode());
                request.addBatchDataParam(WhFbaAllocationItemDBField.SC_ITEM_ID, DataType.LONG, entity.getScItemId());
                request.addBatchDataParam(WhFbaAllocationItemDBField.LOAD_BY, DataType.INT, entity.getLoadBy());
                request.addBatchDataParam(WhFbaAllocationItemDBField.LOAD_TIME, DataType.TIMESTAMP, entity.getLoadTime());
                request.addBatchDataParam(WhFbaAllocationItemDBField.COMPANY_NAME, DataType.STRING, entity.getCompanyName());
                request.addBatchDataParam(WhFbaAllocationItemDBField.TAG, DataType.STRING, entity.getTag());
                request.addBatchDataParam(WhFbaAllocationItemDBField.TEMU_TAG_URL, DataType.STRING, entity.getTemuTagUrl());
                request.addBatchDataParam(WhFbaAllocationItemDBField.REMARK, DataType.STRING, entity.getRemark());
                request.addBatch();
            }
            SqlerTemplate.batchUpdate(request);
        }
    }

    @Override
    public void deleteWhFbaAllocationItem(Integer primaryKey) {
        SqlerRequest request = new SqlerRequest("deleteWhFbaAllocationItemByPrimaryKey");
        request.addDataParam(WhFbaAllocationItemDBField.ID, DataType.INT, primaryKey);
        SqlerTemplate.execute(request);
    }

    @Override
    public void removeBoxInfo(Integer id) {
        SqlerRequest request = new SqlerRequest("removeBoxInfoByPrimaryKey");
        request.addDataParam(WhFbaAllocationItemDBField.ID, DataType.INT, id);
        SqlerTemplate.execute(request);
    }

    @Override
    public void clearTagMsg(Integer fbaId,String fnSku) {
        Assert.notNull(fbaId);
        Assert.notNull(fnSku);
        SqlerRequest request = new SqlerRequest("clearTagMsg");
        request.addDataParam(WhFbaAllocationItemDBField.FBA_ID, DataType.INT, fbaId);
        request.addDataParam(WhFbaAllocationItemDBField.FN_SKU, DataType.STRING, fnSku);
        SqlerTemplate.execute(request);
    }

    @Override
    public void updateItemByFbaIdAndFnSku(WhFbaAllocationItem entity) {
        Assert.notNull(entity);
        Assert.notNull(entity.getFbaId());
        Assert.notNull(entity.getFnSku());
        SqlerRequest request = new SqlerRequest("updateItemByFbaIdAndFnSku");
        request.addDataParam(WhFbaAllocationItemDBField.FBA_ID, DataType.INT, entity.getFbaId());
        request.addDataParam(WhFbaAllocationItemDBField.FN_SKU, DataType.STRING, entity.getFnSku());
        request.addDataParam(WhFbaAllocationItemDBField.PROCESS_TYPE, DataType.INT, entity.getProcessType());
        request.addDataParam(WhFbaAllocationItemDBField.RE_PROCESS, DataType.BOOLEAN, entity.getReProcess());
        SqlerTemplate.execute(request);
    }

    @Override
    public void updateItemByFbaIdAndSku(WhFbaAllocationItem entity) {
        Assert.notNull(entity);
        Assert.notNull(entity.getFbaId());
        Assert.notNull(entity.getProductSku());
        SqlerRequest request = new SqlerRequest("updateItemByFbaIdAndSku");
        request.addDataParam(WhFbaAllocationItemDBField.FBA_ID, DataType.INT, entity.getFbaId());
        request.addDataParam(WhFbaAllocationItemDBField.PRODUCT_SKU, DataType.STRING, entity.getProductSku());
        request.addDataParam(WhFbaAllocationItemDBField.GRID_QUANTITY, DataType.INT, entity.getGridQuantity());
        request.addDataParam(WhFbaAllocationItemDBField.GRID_BY, DataType.INT, entity.getGridBy());
        request.addDataParam(WhFbaAllocationItemDBField.GRID_TIME, DataType.TIMESTAMP, entity.getGridTime());
        request.addDataParam(WhFbaAllocationItemDBField.GRID_STATUS, DataType.INT, entity.getGridStatus());
        SqlerTemplate.execute(request);
    }

    @Override
    public List<WhFbaAllocationItem> queryFreightPrice(WhFbaAllocationItemQueryCondition queryCondition) {
        SqlerRequest request = new SqlerRequest("queryFreightPrice");
        setQueryCondition(request, queryCondition);
        return SqlerTemplate.query(request, new RowMapper<WhFbaAllocationItem>() {
            @Override
            public WhFbaAllocationItem mapRow(ResultSet rs, int rowNum) throws SQLException {
                WhFbaAllocationItem entity = new WhFbaAllocationItem();
                entity.setStore(rs.getString(WhFbaAllocationItemDBField.STORE));
                entity.setProductSku(rs.getString(WhFbaAllocationItemDBField.PRODUCT_SKU));
                entity.setFnSku(rs.getString(WhFbaAllocationItemDBField.FN_SKU));
                entity.setFreight(rs.getObject(WhFbaAllocationItemDBField.FREIGHT) == null ? null : rs.getDouble(WhFbaAllocationItemDBField.FREIGHT));
                return entity;
            }
        });
    }

    @Override
    public int undoLbx(List<Integer> itemIdList,Integer state) {
        SqlerRequest request = new SqlerRequest("undoLbx");
        request.addDataParam("itemIdList", DataType.INT, itemIdList);
        if (AsnPrepareStatus.LOADED.intCode().equals(state)){
            request.addSqlDataParam("NULL_FIELD", "load_by = null , load_time = null , company_name=null");
        }
        if (AsnPrepareStatus.DELIVER.intCode().equals(state)){
            request.addSqlDataParam("NULL_FIELD", "product_length = null, product_width = null, product_height=null, product_weight= null");
        }
        return SqlerTemplate.execute(request);

    }
}