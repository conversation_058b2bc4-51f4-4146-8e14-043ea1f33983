package com.estone.transfer.dao.impl;

import java.util.List;

import org.springframework.stereotype.Repository;
import org.springframework.util.Assert;

import com.estone.common.util.SqlerTemplate;
import com.estone.transfer.bean.JitPickBoxItem;
import com.estone.transfer.bean.JitPickBoxItemQueryCondition;
import com.estone.transfer.dao.JitPickBoxItemDao;
import com.estone.transfer.dao.mapper.JitPickBoxItemDBField;
import com.estone.transfer.dao.mapper.JitPickBoxItemMapper;
import com.whq.tool.component.Pager;
import com.whq.tool.sqler.SqlerRequest;
import com.whq.tool.sqler.analyzer.DataType;

@Repository("jitPickBoxItemDao")
public class JitPickBoxItemDaoImpl implements JitPickBoxItemDao {

    private void setQueryCondition(SqlerRequest request, JitPickBoxItemQueryCondition query) {
        if (query == null) {
            return;
        }
        // 添加只读权限
        if (query.getReadOnly() != null && query.getReadOnly())
        {
            request.setReadOnly(true);
        }
        // 添加查询条件
        // 搜索条件不允许出现空字符
        request.setAllowBlank(false);
        
        request.addDataParam(JitPickBoxItemDBField.ID, DataType.INT, query.getId());
    }

    @Override
    public int queryJitPickBoxItemCount(JitPickBoxItemQueryCondition query) {
        SqlerRequest request = new SqlerRequest("queryJitPickBoxItemCount");
        setQueryCondition(request, query);
        return SqlerTemplate.queryForInt(request);
    }

    @Override
    public List<JitPickBoxItem> queryJitPickBoxItemList() {
        SqlerRequest request = new SqlerRequest("queryJitPickBoxItemList");
        return SqlerTemplate.query(request, new JitPickBoxItemMapper());
    }

    @Override
    public List<JitPickBoxItem> queryJitPickBoxItemList(JitPickBoxItemQueryCondition query, Pager pager) {
        SqlerRequest request = new SqlerRequest("queryJitPickBoxItemList");
        setQueryCondition(request, query);
        if(pager != null) {
            request.addFetch(pager.getPageNo(), pager.getPageSize());
        }
        return SqlerTemplate.query(request, new JitPickBoxItemMapper());
    }

    @Override
    public JitPickBoxItem queryJitPickBoxItem(Integer primaryKey) {
        Assert.notNull(primaryKey, "primaryKey is null!");
        SqlerRequest request = new SqlerRequest("queryJitPickBoxItemByPrimaryKey");
        request.addDataParam(JitPickBoxItemDBField.ID, DataType.INT, primaryKey);
        return SqlerTemplate.queryForObject(request, new JitPickBoxItemMapper());
    }

    @Override
    public JitPickBoxItem queryJitPickBoxItem(JitPickBoxItemQueryCondition query) {
        SqlerRequest request = new SqlerRequest("queryJitPickBoxItem");
        setQueryCondition(request, query);
        return SqlerTemplate.queryForObject(request, new JitPickBoxItemMapper());
    }

    @Override
    public void createJitPickBoxItem(JitPickBoxItem entity) {
        SqlerRequest request = new SqlerRequest("createJitPickBoxItem");
        request.addDataParam(JitPickBoxItemDBField.BOX_ID, DataType.INT, entity.getBoxId());
        request.addDataParam(JitPickBoxItemDBField.FBA_ID, DataType.INT, entity.getFbaId());
        request.addDataParam(JitPickBoxItemDBField.STATUS, DataType.INT, entity.getStatus());
        request.addDataParam(JitPickBoxItemDBField.PICK_BY, DataType.INT, entity.getPickBy());
        request.addDataParam(JitPickBoxItemDBField.PICK_TIME, DataType.TIMESTAMP, entity.getPickTime());
        Integer autoIncrementId = SqlerTemplate.executeAndReturn(request);
        entity.setId(autoIncrementId);
    }

    @Override
    public void updateJitPickBoxItem(JitPickBoxItem entity) {
        SqlerRequest request = new SqlerRequest("updateJitPickBoxItemByPrimaryKey");
        request.addDataParam(JitPickBoxItemDBField.ID, DataType.INT, entity.getId());
        
        request.addDataParam(JitPickBoxItemDBField.BOX_ID, DataType.INT, entity.getBoxId());
        request.addDataParam(JitPickBoxItemDBField.FBA_ID, DataType.INT, entity.getFbaId());
        request.addDataParam(JitPickBoxItemDBField.STATUS, DataType.INT, entity.getStatus());
        request.addDataParam(JitPickBoxItemDBField.PICK_BY, DataType.INT, entity.getPickBy());
        request.addDataParam(JitPickBoxItemDBField.PICK_TIME, DataType.TIMESTAMP, entity.getPickTime());
        SqlerTemplate.execute(request);
    }

    @Override
    public void batchCreateJitPickBoxItem(List<JitPickBoxItem> entityList) {
        if (entityList != null && !entityList.isEmpty()) {
            SqlerRequest request = new SqlerRequest("createJitPickBoxItem");
            for (JitPickBoxItem entity : entityList) {
                request.addBatchDataParam(JitPickBoxItemDBField.BOX_ID, DataType.INT, entity.getBoxId());
                request.addBatchDataParam(JitPickBoxItemDBField.FBA_ID, DataType.INT, entity.getFbaId());
                request.addBatchDataParam(JitPickBoxItemDBField.STATUS, DataType.INT, entity.getStatus());
                request.addBatchDataParam(JitPickBoxItemDBField.PICK_BY, DataType.INT, entity.getPickBy());
                request.addBatchDataParam(JitPickBoxItemDBField.PICK_TIME, DataType.TIMESTAMP, entity.getPickTime());
                request.addBatch();
            }
            SqlerTemplate.batchUpdate(request);
        }
    }

    @Override
    public void batchUpdateJitPickBoxItem(List<JitPickBoxItem> entityList) {
        if (entityList != null && !entityList.isEmpty()) {
            SqlerRequest request = new SqlerRequest("updateJitPickBoxItemByPrimaryKey");
            for (JitPickBoxItem entity : entityList) {
                request.addBatchDataParam(JitPickBoxItemDBField.ID, DataType.INT, entity.getId());
                
                request.addBatchDataParam(JitPickBoxItemDBField.BOX_ID, DataType.INT, entity.getBoxId());
                request.addBatchDataParam(JitPickBoxItemDBField.FBA_ID, DataType.INT, entity.getFbaId());
                request.addBatchDataParam(JitPickBoxItemDBField.STATUS, DataType.INT, entity.getStatus());
                request.addBatchDataParam(JitPickBoxItemDBField.PICK_BY, DataType.INT, entity.getPickBy());
                request.addBatchDataParam(JitPickBoxItemDBField.PICK_TIME, DataType.TIMESTAMP, entity.getPickTime());
                request.addBatch();
            }
            SqlerTemplate.batchUpdate(request);
        }
    }

    @Override
    public void deleteJitPickBoxItem(Integer primaryKey) {
        SqlerRequest request = new SqlerRequest("deleteJitPickBoxItemByPrimaryKey");
        request.addDataParam(JitPickBoxItemDBField.ID, DataType.INT, primaryKey);
        SqlerTemplate.execute(request);
    }
}