package com.estone.transfer.dao.impl;

import java.sql.Timestamp;
import java.util.List;

import org.springframework.stereotype.Repository;
import org.springframework.util.Assert;

import com.estone.common.util.SqlerTemplate;
import com.estone.transfer.bean.AsnPickBox;
import com.estone.transfer.bean.AsnPickBoxQueryCondition;
import com.estone.transfer.dao.AsnPickBoxDao;
import com.estone.transfer.dao.mapper.AsnPickBoxDBField;
import com.estone.transfer.dao.mapper.AsnPickBoxMapper;
import com.whq.tool.component.Pager;
import com.whq.tool.context.DataContextHolder;
import com.whq.tool.sqler.SqlerRequest;
import com.whq.tool.sqler.analyzer.DataType;

@Repository("asnPickBoxDao")
public class AsnPickBoxDaoImpl implements AsnPickBoxDao {

    private void setQueryCondition(SqlerRequest request, AsnPickBoxQueryCondition query) {
        if (query == null) {
            return;
        }
        // 添加只读权限
        if (query.getReadOnly() != null && query.getReadOnly())
        {
            request.setReadOnly(true);
        }
        // 添加查询条件
        // 搜索条件不允许出现空字符
        request.setAllowBlank(false);
        
        request.addDataParam(AsnPickBoxDBField.ID, DataType.INT, query.getId());
        request.addDataParam(AsnPickBoxDBField.PICK_UP_ORDER_NO, DataType.STRING, query.getPickUpOrderNo());
        request.addDataParam(AsnPickBoxDBField.STATUS, DataType.INT, query.getStatus());
        request.addDataParam(AsnPickBoxDBField.NUMBER, DataType.INT, query.getNumber());
    }

    @Override
    public int queryAsnPickBoxCount(AsnPickBoxQueryCondition query) {
        SqlerRequest request = new SqlerRequest("queryAsnPickBoxCount");
        setQueryCondition(request, query);
        return SqlerTemplate.queryForInt(request);
    }

    @Override
    public List<AsnPickBox> queryAsnPickBoxList() {
        SqlerRequest request = new SqlerRequest("queryAsnPickBoxList");
        return SqlerTemplate.query(request, new AsnPickBoxMapper());
    }

    @Override
    public List<AsnPickBox> queryAsnPickBoxList(AsnPickBoxQueryCondition query, Pager pager) {
        SqlerRequest request = new SqlerRequest("queryAsnPickBoxList");
        setQueryCondition(request, query);
        if(pager != null) {
            request.addFetch(pager.getPageNo(), pager.getPageSize());
        }
        return SqlerTemplate.query(request, new AsnPickBoxMapper());
    }

    @Override
    public AsnPickBox queryAsnPickBox(Integer primaryKey) {
        Assert.notNull(primaryKey, "primaryKey is null!");
        SqlerRequest request = new SqlerRequest("queryAsnPickBoxByPrimaryKey");
        request.addDataParam(AsnPickBoxDBField.ID, DataType.INT, primaryKey);
        return SqlerTemplate.queryForObject(request, new AsnPickBoxMapper());
    }

    @Override
    public AsnPickBox queryAsnPickBox(AsnPickBoxQueryCondition query) {
        SqlerRequest request = new SqlerRequest("queryAsnPickBox");
        setQueryCondition(request, query);
        return SqlerTemplate.queryForObject(request, new AsnPickBoxMapper());
    }

    @Override
    public void createAsnPickBox(AsnPickBox entity) {
        SqlerRequest request = new SqlerRequest("createAsnPickBox");
        request.addDataParam(AsnPickBoxDBField.PICK_UP_ORDER_NO, DataType.STRING, entity.getPickUpOrderNo());
        request.addDataParam(AsnPickBoxDBField.NUMBER, DataType.INT, entity.getNumber());
        request.addDataParam(AsnPickBoxDBField.STATUS, DataType.INT, entity.getStatus());
        request.addDataParam(AsnPickBoxDBField.CREATION_DATE, DataType.TIMESTAMP, new Timestamp(System.currentTimeMillis()));
        request.addDataParam(AsnPickBoxDBField.CREATE_BY, DataType.INT, entity.getCreateBy() == null ? DataContextHolder.getUserId() : entity.getCreateBy());
        request.addDataParam(AsnPickBoxDBField.LAST_UPDATE_DATE, DataType.TIMESTAMP, new Timestamp(System.currentTimeMillis()));
        request.addDataParam(AsnPickBoxDBField.LAST_UPDATED_BY, DataType.INT, entity.getLastUpdatedBy() == null ? DataContextHolder.getUserId() : entity.getLastUpdatedBy());
        Integer autoIncrementId = SqlerTemplate.executeAndReturn(request);
        entity.setId(autoIncrementId);
    }

    @Override
    public void updateAsnPickBox(AsnPickBox entity) {
        SqlerRequest request = new SqlerRequest("updateAsnPickBoxByPrimaryKey");
        request.addDataParam(AsnPickBoxDBField.ID, DataType.INT, entity.getId());
        
        request.addDataParam(AsnPickBoxDBField.PICK_UP_ORDER_NO, DataType.STRING, entity.getPickUpOrderNo());
        request.addDataParam(AsnPickBoxDBField.NUMBER, DataType.INT, entity.getNumber());
        request.addDataParam(AsnPickBoxDBField.STATUS, DataType.INT, entity.getStatus());
        
        request.addDataParam(AsnPickBoxDBField.CREATE_BY, DataType.INT, entity.getCreateBy());
        request.addDataParam(AsnPickBoxDBField.LAST_UPDATE_DATE, DataType.TIMESTAMP, new Timestamp(System.currentTimeMillis()));
        request.addDataParam(AsnPickBoxDBField.LAST_UPDATED_BY, DataType.INT, entity.getLastUpdatedBy() == null ? DataContextHolder.getUserId() : entity.getLastUpdatedBy());
        SqlerTemplate.execute(request);
    }

    @Override
    public void batchCreateAsnPickBox(List<AsnPickBox> entityList) {
        if (entityList != null && !entityList.isEmpty()) {
            SqlerRequest request = new SqlerRequest("createAsnPickBox");
            for (AsnPickBox entity : entityList) {
                request.addBatchDataParam(AsnPickBoxDBField.PICK_UP_ORDER_NO, DataType.STRING, entity.getPickUpOrderNo());
                request.addBatchDataParam(AsnPickBoxDBField.NUMBER, DataType.INT, entity.getNumber());
                request.addBatchDataParam(AsnPickBoxDBField.STATUS, DataType.INT, entity.getStatus());
                request.addBatchDataParam(AsnPickBoxDBField.CREATION_DATE, DataType.TIMESTAMP, new Timestamp(System.currentTimeMillis()));
                request.addBatchDataParam(AsnPickBoxDBField.CREATE_BY, DataType.INT, entity.getCreateBy());
                request.addBatchDataParam(AsnPickBoxDBField.LAST_UPDATE_DATE, DataType.TIMESTAMP, new Timestamp(System.currentTimeMillis()));
                request.addBatchDataParam(AsnPickBoxDBField.LAST_UPDATED_BY, DataType.INT, entity.getLastUpdatedBy() == null ? DataContextHolder.getUserId() : entity.getLastUpdatedBy());
                request.addBatch();
            }
            SqlerTemplate.batchUpdate(request);
        }
    }

    @Override
    public void batchUpdateAsnPickBox(List<AsnPickBox> entityList) {
        if (entityList != null && !entityList.isEmpty()) {
            SqlerRequest request = new SqlerRequest("updateAsnPickBoxByPrimaryKey");
            for (AsnPickBox entity : entityList) {
                request.addBatchDataParam(AsnPickBoxDBField.ID, DataType.INT, entity.getId());
                
                request.addBatchDataParam(AsnPickBoxDBField.PICK_UP_ORDER_NO, DataType.STRING, entity.getPickUpOrderNo());
                request.addBatchDataParam(AsnPickBoxDBField.NUMBER, DataType.INT, entity.getNumber());
                request.addBatchDataParam(AsnPickBoxDBField.STATUS, DataType.INT, entity.getStatus());
                
                request.addBatchDataParam(AsnPickBoxDBField.CREATE_BY, DataType.INT, entity.getCreateBy());
                request.addBatchDataParam(AsnPickBoxDBField.LAST_UPDATE_DATE, DataType.TIMESTAMP, new Timestamp(System.currentTimeMillis()));
                request.addBatchDataParam(AsnPickBoxDBField.LAST_UPDATED_BY, DataType.INT, entity.getLastUpdatedBy() == null ? DataContextHolder.getUserId() : entity.getLastUpdatedBy());
                request.addBatch();
            }
            SqlerTemplate.batchUpdate(request);
        }
    }

    @Override
    public void deleteAsnPickBox(Integer primaryKey) {
        SqlerRequest request = new SqlerRequest("deleteAsnPickBoxByPrimaryKey");
        request.addDataParam(AsnPickBoxDBField.ID, DataType.INT, primaryKey);
        SqlerTemplate.execute(request);
    }
}