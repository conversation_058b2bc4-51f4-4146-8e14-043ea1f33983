package com.estone.transfer.dao.mapper;

import com.estone.transfer.bean.WhFbaAllocationData;
import java.sql.ResultSet;
import java.sql.SQLException;
import org.springframework.jdbc.core.RowMapper;

public class WhFbaAllocationDataMapper implements RowMapper<WhFbaAllocationData> {

    public WhFbaAllocationData mapRow(ResultSet rs, int rowNum) throws SQLException {
        WhFbaAllocationData entity = new WhFbaAllocationData();
        entity.setId(rs.getObject(WhFbaAllocationDataDBField.ID) == null ? null : rs.getInt(WhFbaAllocationDataDBField.ID));
        entity.setTaskId(rs.getObject(WhFbaAllocationDataDBField.TASK_ID) == null ? null : rs.getInt(WhFbaAllocationDataDBField.TASK_ID));
        entity.setSite(rs.getString(WhFbaAllocationDataDBField.SITE));
        entity.setAccountNumber(rs.getString(WhFbaAllocationDataDBField.ACCOUNT_NUMBER));
        entity.setShipmentId(rs.getString(WhFbaAllocationDataDBField.SHIPMENT_ID));
        entity.setSku(rs.getString(WhFbaAllocationDataDBField.SKU));
        entity.setAllocationQuantity(rs.getObject(WhFbaAllocationDataDBField.ALLOCATION_QUANTITY) == null ? null : rs.getInt(WhFbaAllocationDataDBField.ALLOCATION_QUANTITY));
        entity.setOrderQuantity(rs.getObject(WhFbaAllocationDataDBField.ORDER_QUANTITY) == null ? null : rs.getInt(WhFbaAllocationDataDBField.ORDER_QUANTITY));
        entity.setCreatedDate(rs.getString(WhFbaAllocationDataDBField.CREATED_DATE));
        entity.setModifiedDate(rs.getTimestamp(WhFbaAllocationDataDBField.MODIFIED_DATE));
        return entity;
    }
}