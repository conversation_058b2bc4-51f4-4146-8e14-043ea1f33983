package com.estone.transfer.dao.mapper;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.HashMap;
import java.util.Map;

import org.springframework.jdbc.core.RowMapper;

import com.estone.transfer.bean.TransferStockDetail;
import com.estone.transfer.bean.TransferStockRelation;

public class GroupTransferStockMapper implements RowMapper<TransferStockDetail> {

    private boolean queryRelation = false;

    private Map<String, TransferStockDetail> exist = new HashMap<String, TransferStockDetail>();

    public GroupTransferStockMapper() {

    }

    public GroupTransferStockMapper(boolean queryRelation) {
        this.queryRelation = queryRelation;
    }

    public TransferStockDetail mapRow(ResultSet rs, int rowNum) throws SQLException {
        TransferStockDetail entity = null;
        if (!queryRelation) {
            entity = getMapRow(rs, "");
            return entity;
        }

        String id = rs.getString("" + WhFbaAllocationDBField.ID);
        entity = exist.get(id);
        if (entity == null) {
            entity = getMapRow(rs, "");
            exist.put(id, entity);
            if (queryRelation) {
                entity.getRelationList().add(getMapRowRelation(rs, "r."));

            }
            return entity;
        }
        else {
            if (queryRelation) {
                entity.getRelationList().add(getMapRowRelation(rs, "r."));
            }
            return null;
        }
    }

    public TransferStockDetail getMapRow(ResultSet rs, String prefix) throws SQLException {
        TransferStockDetail entity = new TransferStockDetail();
        entity.setId(rs.getObject(TransferStockDBField.ID) == null ? null : rs.getInt(TransferStockDBField.ID));
        entity.setSku(rs.getString(TransferStockDBField.SKU));
        entity.setStore(rs.getString(TransferStockDBField.STORE));
        entity.setRemark(rs.getString(TransferStockDBField.REMARK));
        entity.setSite(rs.getString(TransferStockDBField.SITE));
        entity.setImg(rs.getString(TransferStockCountDBField.IMG));
        entity.setSkuName(rs.getString(TransferStockCountDBField.SKU_NAME));
        entity.setStatus(rs.getString(TransferStockCountDBField.STATUS));
        entity.setSaleAttributeSettingStr(rs.getString(TransferStockCountDBField.SALE_ATTRIBUTE_SETTING_STR));
        entity.setThirtyDaysSalesDays(rs.getString(TransferStockCountDBField.THIRTY_DAYS_SALES_DAYS));
        entity.setThirtyDaysSalesOrders(rs.getString(TransferStockCountDBField.THIRTY_DAYS_SALES_ORDERS));
        entity.setLastUpTime(rs.getTimestamp(TransferStockCountDBField.LAST_UP_TIME));
        entity.setLocationNumber(rs.getString(TransferStockDBField.LOCATION_NUMBER));
        entity.setLocationTag(rs.getString(TransferStockDBField.LOCATION_TAG));
        entity.setOnWayQuantity(rs.getObject(TransferStockCountDBField.ON_WAY_QUANTITY) == null ? 0 : rs.getInt(TransferStockCountDBField.ON_WAY_QUANTITY));
        entity.setWaitingQcQuantity(rs.getObject(TransferStockCountDBField.WAITING_QC_QUANTITY) == null ? 0 : rs.getInt(TransferStockCountDBField.WAITING_QC_QUANTITY));
        entity.setWaitingUpQuantity(rs.getObject(TransferStockCountDBField.WAITING_UP_QUANTITY) == null ? 0 : rs.getInt(TransferStockCountDBField.WAITING_UP_QUANTITY));
        entity.setUpQuantity(rs.getObject(TransferStockCountDBField.UP_QUANTITY) == null ? 0 : rs.getInt(TransferStockCountDBField.UP_QUANTITY));
        entity.setSurplusQuantity(rs.getObject(TransferStockDBField.SURPLUS_QUANTITY) == null ? 0 : rs.getInt(TransferStockDBField.SURPLUS_QUANTITY));
        entity.setAllotQuantity(rs.getObject(TransferStockDBField.ALLOT_QUANTITY) == null ? 0 : rs.getInt(TransferStockDBField.ALLOT_QUANTITY));
        entity.setPickQuantity(rs.getObject(TransferStockDBField.PICK_QUANTITY) == null ? 0 : rs.getInt(TransferStockDBField.PICK_QUANTITY));
        entity.setPickReturnQuantity(rs.getObject(TransferStockDBField.PICK_RETURN_QUANTITY) == null ? 0 : rs.getInt(TransferStockDBField.PICK_RETURN_QUANTITY));
        entity.setFirstDeliverQuantity(rs.getObject(TransferStockDBField.FIRST_DELIVER_QUANTITY) == null ? 0 : rs.getInt(TransferStockDBField.FIRST_DELIVER_QUANTITY));
        entity.setStockId(rs.getObject(TransferStockDBField.STOCK_ID) == null ? 0 : rs.getInt(TransferStockDBField.STOCK_ID));
        entity.setInAllocateTime(rs.getTimestamp("allocation_in_time"));
        entity.setOutAllocateTime(rs.getTimestamp("allocation_out_time"));
        return entity;
    }

    private TransferStockRelation getMapRowRelation(ResultSet rs, String prefix) throws SQLException {
        TransferStockRelation entity = new TransferStockRelation();
        entity.setSellSku(rs.getString(prefix + TransferStockRelationDBField.SELL_SKU));
        entity.setFnSku(rs.getString(prefix + TransferStockRelationDBField.FN_SKU));
        entity.setFlag(rs.getObject(prefix + TransferStockRelationDBField.FLAG) == null ? null
                : rs.getInt(prefix + TransferStockRelationDBField.FLAG));
        return entity;
    }
}