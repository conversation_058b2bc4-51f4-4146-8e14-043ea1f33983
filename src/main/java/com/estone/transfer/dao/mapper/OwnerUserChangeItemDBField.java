package com.estone.transfer.dao.mapper;

public interface OwnerUserChangeItemDBField {
    String ID = "id";

    String OWNER_USER_CHANGE_ID = "owner_user_change_id";

    String OLD_OWNER_USER_ID = "old_owner_user_id";

    String NEW_OWNER_USER_ID = "new_owner_user_id";

    String SKU = "sku";

    String ALLOT_NUM = "allot_num";

    String STOCK_TYPE = "stock_type";

    String CREATE_BY = "create_by";

    String CREATION_DATE = "creation_date";

    String LAST_UPDATED_BY = "last_updated_by";

    String LAST_UPDATE_DATE = "last_update_date";
}