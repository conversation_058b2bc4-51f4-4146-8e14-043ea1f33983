package com.estone.transfer.dao.mapper;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

import com.estone.apv.dao.mapper.WhApvDBField;
import com.estone.transfer.bean.AsnPickBox;
import com.estone.transfer.bean.WhFbaAllocationQueryCondition;
import org.apache.commons.lang3.StringUtils;
import org.springframework.jdbc.core.RowMapper;

import com.estone.asn.bean.WhAsnExtra;
import com.estone.asn.dao.mapper.WhAsnExtraDBField;
import com.estone.transfer.bean.WhFbaAllocation;
import com.estone.transfer.bean.WhFbaAllocationItem;

public class WhFbaAllocationMapper implements RowMapper<WhFbaAllocation> {
    private boolean hasItem = false;

    private boolean queryWhAsnExtra = false;

    private boolean queryLoadTime = false;

    private boolean queryApvTrack = false;

    private WhFbaAllocationQueryCondition query = null;

    private Map<String, WhFbaAllocation> exist = new HashMap<String, WhFbaAllocation>();

    public WhFbaAllocationMapper() {

    }

    public WhFbaAllocationMapper(boolean hasItem) {
        this.hasItem = hasItem;
    }

    public WhFbaAllocationMapper(boolean hasItem, boolean queryWhAsnExtra) {
        this.hasItem = hasItem;
        this.queryWhAsnExtra = queryWhAsnExtra;
    }

    public WhFbaAllocationMapper(boolean hasItem, boolean queryWhAsnExtra, boolean queryLoadTime,boolean queryApvTrack, WhFbaAllocationQueryCondition query) {
        this.hasItem = hasItem;
        this.queryWhAsnExtra = queryWhAsnExtra;
        this.queryLoadTime = queryLoadTime;
        this.queryApvTrack = queryApvTrack;
        this.query = query;
    }

    public WhFbaAllocation mapRow(ResultSet rs, int rowNum) throws SQLException {
        WhFbaAllocation entity = null;
        if (!hasItem) {
            entity = getMapRow(rs, "");
            return entity;
        }

        String id = rs.getString("fa." + WhFbaAllocationDBField.ID);
        entity = exist.get(id);
        if (entity == null) {
            entity = getMapRow(rs, "fa.");
            exist.put(id, entity);
            if (hasItem) {
                entity.getItems().add(getMapRowItem(rs, "fai."));

            }
            if (queryWhAsnExtra){
                entity.setWhAsnExtra(getMapRowExtra(rs,"ae."));
            }
            if(Objects.nonNull(query) && Objects.nonNull(query.getQueryAsnPickBoxNumber()) && query.getQueryAsnPickBoxNumber()){
                entity.setAsnPickBox(getMapRowAsnPickBox(rs, "apb."));
            }
            return entity;
        }
        else {
            if (hasItem) {
                entity.getItems().add(getMapRowItem(rs, "fai."));
            }
            if (queryWhAsnExtra){
                entity.setWhAsnExtra(getMapRowExtra(rs,"ae."));
            }
            if(Objects.nonNull(query) && Objects.nonNull(query.getQueryAsnPickBoxNumber()) && query.getQueryAsnPickBoxNumber()){
                entity.setAsnPickBox(getMapRowAsnPickBox(rs, "apb."));
            }
            return null;
        }
    }

    private WhFbaAllocation getMapRow(ResultSet rs, String prefix) throws SQLException {
        WhFbaAllocation entity = new WhFbaAllocation();
        entity.setId(rs.getObject(prefix + WhFbaAllocationDBField.ID) == null ? null
                : rs.getInt(prefix + WhFbaAllocationDBField.ID));
        entity.setFbaNo(rs.getString(prefix + WhFbaAllocationDBField.FBA_NO));
        entity.setShipmentId(rs.getString(prefix + WhFbaAllocationDBField.SHIPMENT_ID));
        entity.setPurposeHouse(rs.getString(prefix + WhFbaAllocationDBField.PURPOSE_HOUSE));
        entity.setAccountNumber(rs.getString(prefix + WhFbaAllocationDBField.ACCOUNT_NUMBER));
        String site = rs.getString(prefix + WhFbaAllocationDBField.SITE);
        if (entity.isFba()) {
            entity.setSite(null);
            entity.setAmazonSite(site);
        }
        else {
            entity.setSite(site);
        }
        entity.setStatus(rs.getObject(prefix + WhFbaAllocationDBField.STATUS) == null ? null
                : rs.getInt(prefix + WhFbaAllocationDBField.STATUS));
        if (hasItem) {
            entity.setPickBoxStatus(rs.getObject(prefix + WhFbaAllocationDBField.PICK_BOX_STATUS) == null ? null
                    : rs.getInt(prefix + WhFbaAllocationDBField.PICK_BOX_STATUS));
            entity.setNumber(rs.getObject(prefix + WhFbaAllocationDBField.NUMBER) == null ? null
                    : rs.getInt(prefix + WhFbaAllocationDBField.NUMBER));
        }
        entity.setSmCode(rs.getString(prefix + WhFbaAllocationDBField.SM_CODE));
        entity.setShippingMethod(rs.getString(prefix + WhFbaAllocationDBField.SHIPPING_METHOD));
        entity.setShippingMethodByTms(rs.getString(prefix + WhFbaAllocationDBField.SHIPPING_METHOD_BY_TMS));
        entity.setShippingCompany(rs.getString(prefix + WhFbaAllocationDBField.SHIPPING_COMPANY));
        entity.setTrackingNumber(rs.getString(prefix + WhFbaAllocationDBField.TRACKING_NUMBER));
        entity.setTrackingNumberByTms(rs.getString(prefix + WhFbaAllocationDBField.TRACKING_NUMBER_BY_TMS));
        entity.setShippingOrderNo(rs.getString(prefix + WhFbaAllocationDBField.SHIPPING_ORDER_NO));
        entity.setPushTime(rs.getTimestamp(prefix + WhFbaAllocationDBField.PUSH_TIME));
        entity.setBoxPushBy(rs.getObject(prefix + WhFbaAllocationDBField.BOX_PUSH_BY) == null ? null
                : rs.getInt(prefix + WhFbaAllocationDBField.BOX_PUSH_BY));
        entity.setBoxPushTime(rs.getTimestamp(prefix + WhFbaAllocationDBField.BOX_PUSH_TIME));
        entity.setConfirmTime(rs.getTimestamp(prefix + WhFbaAllocationDBField.CONFIRM_TIME));
        entity.setDeliverBy(rs.getObject(prefix + WhFbaAllocationDBField.DELIVER_BY) == null ? null
                : rs.getInt(prefix + WhFbaAllocationDBField.DELIVER_BY));
        entity.setDeliverTime(rs.getTimestamp(prefix + WhFbaAllocationDBField.DELIVER_TIME));
        entity.setCancelTime(rs.getTimestamp(prefix + WhFbaAllocationDBField.CANCEL_TIME));
        entity.setDepartureTime(rs.getTimestamp(prefix + WhFbaAllocationDBField.DEPARTURE_TIME));
        entity.setOverseasUpTime(rs.getTimestamp(prefix + WhFbaAllocationDBField.OVERSEAS_UP_TIME));
        entity.setTaskNo(rs.getString(prefix + WhFbaAllocationDBField.TASK_NO));
        entity.setPdfUrl(rs.getString(prefix + WhFbaAllocationDBField.PDF_URL));
        entity.setBatNo(rs.getString(prefix + WhFbaAllocationDBField.BAT_NO));
        entity.setTags(rs.getString(prefix + WhFbaAllocationDBField.TAGS));
        entity.setTransitType(rs.getObject(prefix + WhFbaAllocationDBField.TRANSIT_TYPE) == null ? null
                : rs.getInt(prefix + WhFbaAllocationDBField.TRANSIT_TYPE));
        entity.setSalesperson(rs.getString(prefix + WhFbaAllocationDBField.SALESPERSON));
        entity.setRemark(rs.getString(prefix + WhFbaAllocationDBField.REMARK));
        entity.setRejectReason(rs.getString(prefix + WhFbaAllocationDBField.REJECT_REASON));
        entity.setCheckBy(rs.getObject(prefix + WhFbaAllocationDBField.CHECK_BY) == null ? null
                : rs.getInt(prefix + WhFbaAllocationDBField.CHECK_BY));
        entity.setCheckTime(rs.getTimestamp(prefix + WhFbaAllocationDBField.CHECK_TIME));
        entity.setPickOut(rs.getObject(prefix + WhFbaAllocationDBField.PICK_OUT) == null ? null : rs.getBoolean(prefix + WhFbaAllocationDBField.PICK_OUT));
        entity.setSplitRegionFlag(rs.getObject(prefix + WhFbaAllocationDBField.SPLIT_REGION_FLAG) == null ? null : rs.getBoolean(prefix + WhFbaAllocationDBField.SPLIT_REGION_FLAG));
        entity.setLogisticsAging(rs.getString(prefix + WhFbaAllocationDBField.LOGISTICS_AGING));
        entity.setApvType(rs.getString(prefix+WhFbaAllocationDBField.APV_TYPE));
        entity.setReceiveTime(rs.getTimestamp(prefix + WhFbaAllocationDBField.RECEIVE_TIME));
        entity.setMergeTime(rs.getTimestamp(prefix + WhFbaAllocationDBField.MERGE_TIME));
        entity.setIsReturn(rs.getObject(prefix+WhFbaAllocationDBField.IS_RETURN) == null ? null
                : rs.getBoolean(prefix+WhFbaAllocationDBField.IS_RETURN));
        entity.setIsAsn(rs.getObject(prefix+WhFbaAllocationDBField.IS_ASN) == null ? null
                : rs.getBoolean(prefix+WhFbaAllocationDBField.IS_ASN));
        entity.setExceptionOrder(rs.getObject(prefix+WhFbaAllocationDBField.EXCEPTION_ORDER) == null ? null
                : rs.getBoolean(prefix+WhFbaAllocationDBField.EXCEPTION_ORDER));
        if (this.queryLoadTime){
            entity.setLoadTime(rs.getTimestamp(prefix + WhFbaAllocationDBField.LOAD_TIME));
        }
        if (this.queryApvTrack){
            entity.setPickTime(rs.getTimestamp(prefix + "pickTime"));
            entity.setSowTime(rs.getTimestamp(prefix + "sowTime"));
            entity.setPackTime(rs.getTimestamp(prefix + "packTime"));
            entity.setSowUser(rs.getObject(prefix + "sowUser") == null ? null : rs.getInt(prefix + "sowUser"));
            entity.setPackUser(rs.getObject(prefix + "packUser") == null ? null : rs.getInt(prefix + "packUser"));
        }
        entity.setLoadId(rs.getObject(prefix+WhFbaAllocationDBField.LOAD_ID) == null ? null : rs.getInt(prefix +WhFbaAllocationDBField.LOAD_ID));
        if (query != null && query.isQueryShipmentIds()){
            String shipmentIdStr = rs.getString(prefix + "shipmentIdStr");
            if (StringUtils.isNotBlank(shipmentIdStr)) {
                entity.setShipmentId(shipmentIdStr);
            }
        }
        return entity;
    }

    private WhFbaAllocationItem getMapRowItem(ResultSet rs, String prefix) throws SQLException {
        WhFbaAllocationItem entity = new WhFbaAllocationItem();
        entity.setId(rs.getObject(prefix + WhFbaAllocationItemDBField.ID) == null ? null
                : rs.getInt(prefix + WhFbaAllocationItemDBField.ID));
        entity.setFbaId(rs.getObject(prefix + WhFbaAllocationItemDBField.FBA_ID) == null ? null
                : rs.getInt(prefix + WhFbaAllocationItemDBField.FBA_ID));
        entity.setFbaNo(rs.getString("fa." + WhFbaAllocationDBField.FBA_NO));
        entity.setBoxNo(rs.getObject(prefix + WhFbaAllocationItemDBField.BOX_NO) == null ? null
                : rs.getInt(prefix + WhFbaAllocationItemDBField.BOX_NO));
        entity.setWarehouseId(rs.getObject(prefix + WhFbaAllocationItemDBField.WAREHOUSE_ID) == null ? null
                : rs.getInt(prefix + WhFbaAllocationItemDBField.WAREHOUSE_ID));
        entity.setProductSku(rs.getString(prefix + WhFbaAllocationItemDBField.PRODUCT_SKU));
        entity.setProductBarcode(rs.getString(prefix + WhFbaAllocationItemDBField.PRODUCT_BARCODE));
        entity.setStore(rs.getString(prefix + WhFbaAllocationItemDBField.STORE));
        entity.setSite(rs.getString(prefix + WhFbaAllocationItemDBField.SITE));
        entity.setFnSku(rs.getString(prefix + WhFbaAllocationItemDBField.FN_SKU));
        entity.setSellSku(rs.getString(prefix + WhFbaAllocationItemDBField.SELL_SKU));
        entity.setSkuTags(rs.getString(prefix + WhFbaAllocationItemDBField.SKU_TAGS));
        entity.setQuantity(rs.getObject(prefix + WhFbaAllocationItemDBField.QUANTITY) == null ? null
                : rs.getInt(prefix + WhFbaAllocationItemDBField.QUANTITY));
        entity.setSkuQuantity(rs.getObject(prefix + WhFbaAllocationItemDBField.SKU_QUANTITY) == null ? null
                : rs.getInt(prefix + WhFbaAllocationItemDBField.SKU_QUANTITY));
        entity.setSkuSuitNum(rs.getObject(prefix + WhFbaAllocationItemDBField.SKU_SUIT_NUM) == null ? null
                : rs.getInt(prefix + WhFbaAllocationItemDBField.SKU_SUIT_NUM));
        entity.setLoadNum(rs.getObject(prefix + WhFbaAllocationItemDBField.LOAD_NUM) == null ? null
                : rs.getInt(prefix + WhFbaAllocationItemDBField.LOAD_NUM));
        entity.setSuitFlag(rs.getObject(prefix + WhFbaAllocationItemDBField.SUIT_FLAG) == null ? null
                : rs.getInt(prefix + WhFbaAllocationItemDBField.SUIT_FLAG));
        entity.setLoadingQuantity(rs.getObject(prefix + WhFbaAllocationItemDBField.LOADING_QUANTITY) == null ? null
                : rs.getInt(prefix + WhFbaAllocationItemDBField.LOADING_QUANTITY));
        entity.setPutawayQuantity(rs.getObject(prefix + WhFbaAllocationItemDBField.PUTAWAY_QUANTITY) == null ? null
                : rs.getInt(prefix + WhFbaAllocationItemDBField.PUTAWAY_QUANTITY));
        entity.setAllotQuantity(rs.getObject(prefix + WhFbaAllocationItemDBField.ALLOT_QUANTITY) == null ? null
                : rs.getInt(prefix + WhFbaAllocationItemDBField.ALLOT_QUANTITY));
        entity.setPickQuantity(rs.getObject(prefix + WhFbaAllocationItemDBField.PICK_QUANTITY) == null ? null
                : rs.getInt(prefix + WhFbaAllocationItemDBField.PICK_QUANTITY));
        entity.setPutawayDiff(rs.getObject(prefix + WhFbaAllocationItemDBField.PUTAWAY_DIFF) == null ? null
                : rs.getInt(prefix + WhFbaAllocationItemDBField.PUTAWAY_DIFF));
        entity.setProductLength(rs.getObject(prefix + WhFbaAllocationItemDBField.PRODUCT_LENGTH) == null ? null
                : rs.getDouble(prefix + WhFbaAllocationItemDBField.PRODUCT_LENGTH));
        entity.setProductWidth(rs.getObject(prefix + WhFbaAllocationItemDBField.PRODUCT_WIDTH) == null ? null
                : rs.getDouble(prefix + WhFbaAllocationItemDBField.PRODUCT_WIDTH));
        entity.setProductHeight(rs.getObject(prefix + WhFbaAllocationItemDBField.PRODUCT_HEIGHT) == null ? null
                : rs.getDouble(prefix + WhFbaAllocationItemDBField.PRODUCT_HEIGHT));
        entity.setProductWeight(rs.getObject(prefix + WhFbaAllocationItemDBField.PRODUCT_WEIGHT) == null ? null
                : rs.getDouble(prefix + WhFbaAllocationItemDBField.PRODUCT_WEIGHT));
        entity.setTollWeight(rs.getObject(prefix + WhFbaAllocationItemDBField.TOLL_WEIGHT) == null ? null
                : rs.getDouble(prefix + WhFbaAllocationItemDBField.TOLL_WEIGHT));
        entity.setFreight(rs.getObject(prefix + WhFbaAllocationItemDBField.FREIGHT) == null ? null
                : rs.getDouble(prefix + WhFbaAllocationItemDBField.FREIGHT));
        entity.setBoxCost(rs.getObject(prefix + WhFbaAllocationItemDBField.BOX_COST) == null ? null
                : rs.getDouble(prefix + WhFbaAllocationItemDBField.BOX_COST));
        entity.setSkuCost(rs.getObject(prefix + WhFbaAllocationItemDBField.SKU_COST) == null ? null
                : rs.getDouble(prefix + WhFbaAllocationItemDBField.SKU_COST));
        entity.setOtherPrice(rs.getObject(prefix + WhFbaAllocationItemDBField.OTHER_PRICE) == null ? null
                : rs.getDouble(prefix + WhFbaAllocationItemDBField.OTHER_PRICE));
        entity.setProductPrice(rs.getObject(prefix + WhFbaAllocationItemDBField.PRODUCT_PRICE) == null ? null
                : rs.getDouble(prefix + WhFbaAllocationItemDBField.PRODUCT_PRICE));
        entity.setSellSkuName(rs.getString(prefix + WhFbaAllocationItemDBField.SELL_SKU_NAME));
        entity.setAllotLocationNumbers(rs.getString(prefix + WhFbaAllocationItemDBField.ALLOT_LOCATION_NUMBERS));
        entity.setSkuImg(rs.getString(prefix + WhFbaAllocationItemDBField.SKU_IMG));
        entity.setFnSkuTags(rs.getString(prefix + WhFbaAllocationItemDBField.FN_SKU_TAGS));
        entity.setGridQuantity(rs.getObject(prefix + WhFbaAllocationItemDBField.GRID_QUANTITY) == null ? null
                : rs.getInt(prefix + WhFbaAllocationItemDBField.GRID_QUANTITY));
        entity.setGridStatus(rs.getObject(prefix + WhFbaAllocationItemDBField.GRID_STATUS) == null ? null
                : rs.getInt(prefix + WhFbaAllocationItemDBField.GRID_STATUS));
        entity.setGridBy(rs.getObject(prefix + WhFbaAllocationItemDBField.GRID_BY) == null ? null
                : rs.getInt(prefix + WhFbaAllocationItemDBField.GRID_BY));
        entity.setGridTime(rs.getTimestamp(prefix + WhFbaAllocationItemDBField.GRID_TIME));
        entity.setTagBy(rs.getObject(prefix + WhFbaAllocationItemDBField.TAG_BY) == null ? null
                : rs.getInt(prefix + WhFbaAllocationItemDBField.TAG_BY));
        entity.setTagTime(rs.getTimestamp(prefix + WhFbaAllocationItemDBField.TAG_TIME));

        entity.setProcessType(rs.getObject(prefix + WhFbaAllocationItemDBField.PROCESS_TYPE) == null ? null
                : rs.getInt(prefix + WhFbaAllocationItemDBField.PROCESS_TYPE));
        entity.setReProcess(rs.getObject(prefix + WhFbaAllocationItemDBField.RE_PROCESS) == null ? null
                : rs.getBoolean(prefix + WhFbaAllocationItemDBField.RE_PROCESS));
        entity.setName(rs.getString(prefix + "name"));
        entity.setTag(rs.getString(prefix + WhFbaAllocationItemDBField.TAG));
        entity.setTemuTagUrl(rs.getString(prefix + WhFbaAllocationItemDBField.TEMU_TAG_URL));
        entity.setTemuCodeUrl(rs.getString(prefix + WhFbaAllocationItemDBField.TEMU_CODE_URL));
        entity.setSkuBarcode(rs.getString(prefix + WhFbaAllocationItemDBField.SKU_BARCODE));
        entity.setScItemId(rs.getObject(prefix + WhFbaAllocationItemDBField.SC_ITEM_ID) == null ? null
                : rs.getLong(prefix + WhFbaAllocationItemDBField.SC_ITEM_ID));
        entity.setLoadBy(rs.getObject(prefix + WhFbaAllocationItemDBField.LOAD_BY) == null ? null
                : rs.getInt(prefix + WhFbaAllocationItemDBField.LOAD_BY));
        entity.setLoadTime(rs.getTimestamp(prefix + WhFbaAllocationItemDBField.LOAD_TIME));
        entity.setCompanyName(rs.getString(prefix+WhFbaAllocationItemDBField.COMPANY_NAME));

        return entity;
    }

    private WhAsnExtra getMapRowExtra(ResultSet rs, String prefix) throws SQLException {
        WhAsnExtra extra = new WhAsnExtra();
        extra.setId(rs.getObject(prefix  + WhAsnExtraDBField.ID) == null ? null : rs.getInt(prefix + WhAsnExtraDBField.ID));
        extra.setWhAsnId(rs.getObject(prefix + WhAsnExtraDBField.WH_ASN_ID) == null ? null : rs.getInt(prefix + WhAsnExtraDBField.WH_ASN_ID));
        extra.setReceiptPerson(rs.getString(prefix + WhAsnExtraDBField.RECEIPT_PERSON));
        extra.setReceiptCountry(rs.getString(prefix + WhAsnExtraDBField.RECEIPT_COUNTRY));
        extra.setReceiptArea(rs.getString(prefix + WhAsnExtraDBField.RECEIPT_AREA));
        extra.setReceiptCity(rs.getString(prefix + WhAsnExtraDBField.RECEIPT_CITY));
        extra.setReceiptAddress(rs.getString(prefix + WhAsnExtraDBField.RECEIPT_ADDRESS));
        extra.setReceiptAddress2(rs.getString(prefix + WhAsnExtraDBField.RECEIPT_ADDRESS2));
        extra.setZipcode(rs.getString(prefix + WhAsnExtraDBField.ZIPCODE));
        extra.setMemo(rs.getString(prefix + WhAsnExtraDBField.MEMO));
        extra.setCreatedDate(rs.getTimestamp(prefix + WhAsnExtraDBField.CREATED_DATE));
        extra.setModifiedDate(rs.getTimestamp(prefix + WhAsnExtraDBField.MODIFIED_DATE));
        extra.setPackageMethod(rs.getObject(prefix  + WhAsnExtraDBField.PACKAGE_METHOD) == null ? null : rs.getInt(prefix + WhAsnExtraDBField.PACKAGE_METHOD));
        extra.setType(rs.getObject(prefix  +WhAsnExtraDBField.TYPE) == null ? null : rs.getInt(prefix  +WhAsnExtraDBField.TYPE));
        extra.setPrintSkuTagUrl(rs.getString(prefix  + WhAsnExtraDBField.PRINT_SKU_TAG_URL));
        extra.setPrintEnvironmentalProtectionTagUrl(rs.getString(prefix  + WhAsnExtraDBField.PRINT_ENVIRONMENTAL_PROTECTION_TAG_URL));
        extra.setPrintTextileTagUrl(rs.getString(prefix  + WhAsnExtraDBField.PRINT_TEXTILE_TAG_URL));
        extra.setBizType(rs.getObject(prefix  +WhAsnExtraDBField.BIZ_TYPE) == null ? null : rs.getInt(prefix  +WhAsnExtraDBField.BIZ_TYPE));
        extra.setPurchaseOrderNo(rs.getString(prefix + WhAsnExtraDBField.PURCHASE_ORDER_NO));
        extra.setOrderId(rs.getString(prefix + WhAsnExtraDBField.ORDER_ID));
        extra.setPickupOrderId(rs.getString(prefix + WhAsnExtraDBField.PICK_UP_ORDER_ID));
        extra.setWarehouseCode(rs.getString(prefix + WhAsnExtraDBField.WAREHOUSE_CODE));
        extra.setTemuTagUrl(rs.getString(prefix + WhAsnExtraDBField.TEMU_TAG_URL));
        extra.setTemuCodeUrl(rs.getString(prefix + WhAsnExtraDBField.TEMU_CODE_URL));
        extra.setBoxMarkUrl(rs.getString(prefix + WhAsnExtraDBField.BOX_MARK_URL));
        extra.setCollectLabelUrl(rs.getString(prefix + WhAsnExtraDBField.COLLECT_LABEL_URL));
        extra.setPhoneNumber(rs.getString(prefix + WhAsnExtraDBField.PHONE_NUMBER));
        extra.setConsignOrderNo(rs.getString(prefix + WhAsnExtraDBField.CONSIGN_ORDER_NO));
        return extra;
    }


    private AsnPickBox getMapRowAsnPickBox(ResultSet rs, String prefix) throws SQLException {
        AsnPickBox asnPickBox = new AsnPickBox();
//        asnPickBox.setId(rs.getObject(prefix +AsnPickBoxDBField.ID) == null ? null : rs.getInt(prefix +AsnPickBoxDBField.ID));
//        asnPickBox.setPickUpOrderNo(rs.getString(prefix +AsnPickBoxDBField.PICK_UP_ORDER_NO));
        asnPickBox.setNumber(rs.getObject(prefix +AsnPickBoxDBField.NUMBER) == null ? null : rs.getInt(prefix +AsnPickBoxDBField.NUMBER));
//        asnPickBox.setStatus(rs.getObject(prefix +AsnPickBoxDBField.STATUS) == null ? null : rs.getInt(prefix +AsnPickBoxDBField.STATUS));
//        asnPickBox.setCreationDate(rs.getTimestamp(prefix +AsnPickBoxDBField.CREATION_DATE));
//        asnPickBox.setCreateBy(rs.getObject(prefix +AsnPickBoxDBField.CREATE_BY) == null ? null : rs.getInt(prefix +AsnPickBoxDBField.CREATE_BY));
//        asnPickBox.setLastUpdateDate(rs.getTimestamp(prefix +AsnPickBoxDBField.LAST_UPDATE_DATE));
//        asnPickBox.setLastUpdatedBy(rs.getObject(prefix +AsnPickBoxDBField.LAST_UPDATED_BY) == null ? null : rs.getInt(prefix +AsnPickBoxDBField.LAST_UPDATED_BY));
        return asnPickBox;
    }
}