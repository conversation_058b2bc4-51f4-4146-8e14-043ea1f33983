package com.estone.transfer.dao;

import com.estone.transfer.bean.WhFbaAllocationData;
import com.estone.transfer.bean.WhFbaAllocationDataQueryCondition;
import com.whq.tool.component.Pager;
import java.util.List;

public interface WhFbaAllocationDataDao {
    int queryWhFbaAllocationDataCount(WhFbaAllocationDataQueryCondition query);

    List<WhFbaAllocationData> queryWhFbaAllocationDataList();

    List<WhFbaAllocationData> queryWhFbaAllocationDataList(WhFbaAllocationDataQueryCondition query, Pager pager);

    WhFbaAllocationData queryWhFbaAllocationData(Integer primaryKey);

    WhFbaAllocationData queryWhFbaAllocationData(WhFbaAllocationDataQueryCondition query);

    void createWhFbaAllocationData(WhFbaAllocationData entity);

    void batchCreateWhFbaAllocationData(List<WhFbaAllocationData> entityList);

    void batchUpdateWhFbaAllocationData(List<WhFbaAllocationData> entityList);

    void deleteWhFbaAllocationData(Integer primaryKey);

    void updateWhFbaAllocationData(WhFbaAllocationData entity);
}