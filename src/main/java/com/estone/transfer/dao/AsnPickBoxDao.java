package com.estone.transfer.dao;

import com.estone.transfer.bean.AsnPickBox;
import com.estone.transfer.bean.AsnPickBoxQueryCondition;
import com.whq.tool.component.Pager;
import java.util.List;

public interface AsnPickBoxDao {
    int queryAsnPickBoxCount(AsnPickBoxQueryCondition query);

    List<AsnPickBox> queryAsnPickBoxList();

    List<AsnPickBox> queryAsnPickBoxList(AsnPickBoxQueryCondition query, Pager pager);

    AsnPickBox queryAsnPickBox(Integer primaryKey);

    AsnPickBox queryAsnPickBox(AsnPickBoxQueryCondition query);

    void createAsnPickBox(AsnPickBox entity);

    void batchCreateAsnPickBox(List<AsnPickBox> entityList);

    void batchUpdateAsnPickBox(List<AsnPickBox> entityList);

    void deleteAsnPickBox(Integer primaryKey);

    void updateAsnPickBox(AsnPickBox entity);
}