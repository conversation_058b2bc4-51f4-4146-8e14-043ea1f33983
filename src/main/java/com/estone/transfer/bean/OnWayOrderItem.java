package com.estone.transfer.bean;

import java.io.Serializable;
import java.sql.Timestamp;

import com.estone.asn.bean.WhAsnExtra;
import org.apache.commons.lang.StringUtils;

import lombok.Data;

@Data
public class OnWayOrderItem implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键id database column on_way_order_item.id
     */
    private Integer id;

    /**
     * 头程在途ID database column on_way_order_item.o_id
     */
    private Integer oId;

    /**
     * SKU database column on_way_order_item.sku
     */
    private String sku;

    /**
     * FNSKU database column on_way_order_item.fn_sku
     */
    private String fnSku;

    /**
     * sellSku database column on_way_order_item.sell_sku
     */
    private String sellSku;

    /**
     * 装箱数量：装箱环节填写的数量 database column on_way_order_item.loading_quantity
     */
    private Integer loadingQuantity;

    /**
     * 海外仓上架数量 database column on_way_order_item.overseas_up_quantity
     */
    private Integer overseasUpQuantity;

    /**
     * 上架差异：=海外仓上架数量-装箱数量 database column on_way_order_item.up_diff
     */
    private Integer upDiff;

    /**
     * 预警天数 database column on_way_order_item.warning_days
     */
    private Integer warningDays;

    /**
     * 海外仓上架时间 database column on_way_order_item.overseas_up_time
     */
    private Timestamp overseasUpTime;

    /**
     * 在途预警
     */
    private Integer onWayWarning;

    private OnWayOrder onWayOrder;

    private WhAsnExtra whAsnExtra;

    private WhFbaAllocation allocation;

    /**
     * 日志信息拼接
     * 
     * @param item
     * @return
     */
    public String getLabelDataMes(OnWayOrderItem item) {
        if (item == null || item.getId() == null || !item.getId().equals(id))
            return null;
        StringBuffer msg = new StringBuffer();
        if (overseasUpQuantity != null && !overseasUpQuantity.equals(item.getOverseasUpQuantity())) {
            String overseasUpQuantityStr = item.getOverseasUpQuantity() + "==>" + overseasUpQuantity;
            msg.append(",海外上架数量: " + overseasUpQuantityStr);
        }
        if (overseasUpTime != null && !overseasUpTime.equals(item.getOverseasUpTime())) {
            String overseasUpTimeStr = item.getOverseasUpTime() + "==>" + overseasUpTime;
            msg.append(",海外上架时间: " + overseasUpTimeStr);
        }
        if (!StringUtils.equalsIgnoreCase(warningDays + "", item.getWarningDays() + "")) {
            String warningDaysStr = item.getWarningDays() + "==>" + warningDays;
            msg.append(",预警天数: " + warningDaysStr);
        }
        if (!StringUtils.equalsIgnoreCase(onWayWarning + "", item.getOnWayWarning() + "")) {
            String onWayWarningStr = item.getOnWayWarning() + "==>" + onWayWarning;
            msg.append(",在途预警: " + onWayWarningStr);
        }
        if (msg == null || msg.length() == 0)
            return "";
        return String.format("SKU: %s, %s;", sku, msg.toString());
    }
}