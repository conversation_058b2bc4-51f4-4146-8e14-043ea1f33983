package com.estone.transfer.bean;

import lombok.Data;

import java.util.List;

@Data
public class WhFbaChangeQueryCondition extends WhFbaChange {
    private static final long serialVersionUID = 1L;

    private String startChangeTime;

    private String endChangeTime;

    private String skuSplit;

    private String shipmentIdList;

    private String fbaNoList;

    private Boolean isDownload;

    private List<Integer> ids;

    private String site;

    private String store;

    private List<Integer> orderItemStatusList;

    private List<Integer> fbaAllocationItemIds;

    private List<Integer> deliveryTypes;

}