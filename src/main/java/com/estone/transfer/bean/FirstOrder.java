package com.estone.transfer.bean;

import java.io.Serializable;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.List;

import lombok.Data;

@Data
public class FirstOrder implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID database column firstorder.id
     */
    private Long id;

    /**
     * 发货单号 database column firstorder.apvNo
     */
    private String apvNo;

    /**
     * 批次号 database column firstorder.batNo
     */
    private String batNo;

    /**
     * 运输方式 database column firstorder.transportType
     */
    private String transportType;

    /**
     * 发货仓 database column firstorder.deWarehouse
     */
    private String deWarehouse;

    /**
     * 目的仓 database column firstorder.obWarehouse
     */
    private String obWarehouse;

    /**
     * 预估重量(kg) database column firstorder.esWeight
     */
    private Double esWeight;

    /**
     * 实际重量(kg) database column firstorder.actWeight
     */
    private Double actWeight;

    /**
     * 体积(立方厘米) database column firstorder.volume
     */
    private Double volume;

    /**
     * 状态（10：待合单，20：已合单，30：已取消） database column firstorder.status
     */
    private String status;

    /**
     * 操作人 database column firstorder.operator
     */
    private String operator;

    /**
     * 创建时间 database column firstorder.createTime
     */
    private Timestamp createTime;

    /**
     * 合单时间 database column firstorder.combinedTime
     */
    private Timestamp combinedTime;

    /**
     * 更新时间 database column firstorder.updateTime
     */
    private Timestamp updateTime;

    // 货件编号
    private String shipmentNo;
    // 店铺销售账号
    private String accountNumber;
    // 国家(二字码)
    private String country;
    // 邮编
    private String zipCode;

    // 条目-装箱子信息
    private List<FirstOrderItem> firstOrderItemVOList;

    private List<FirstOrderItem> firstOrderItemList;

    private String batStatus;

    // 已交运取消信息推到TMS
    private String msg;
    /**
     * 收件人省/州 database column firstbatorder.province
     */
    private String province;

    /**
     * 收件人城市 database column firstbatorder.city
     */
    private String city;

    /**
     * 收件人地址 database column firstbatorder.address
     */
    private String address;

    /**
     * 收件人名称 database column firstbatorder.receiveName
     */
    private String receiveName;

    // 收件人电话
    private String receiveMobile;

    /**
     * 销售人员
     */
    private String salesperson;

    /**
     * 所属平台
     */
    private String platform;

    /**
     * 销售确认状态字段
     * 1 - 待销售确认
     * 2 - 销售确认发货
     * 3 - 销售确认不发货
     */
    private Integer salesConfirmState;

    //是否是海外头程单交运
    private String orderDelivered ;

    //物流时效
    private String logisticsAging;

    private String firstOrderDelivered ;

}