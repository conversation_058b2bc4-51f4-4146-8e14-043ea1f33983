package com.estone.transfer.bean;

import java.io.Serializable;
import java.sql.Timestamp;

import com.estone.sku.bean.WhSku;

import lombok.Data;

@Data
public class WhTransitReturnItem implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     *  database column wh_transit_return_item.id
     */
    private Integer id;

    /**
     *  database column wh_transit_return_item.return_id
     */
    private Integer returnId;

    /**
     * sku database column wh_transit_return_item.sku
     */
    private String sku;

    /**
     * sellSku database column wh_transit_return_item.site
     */
    private String site;


    /**
     * 状态 1 待返架 2 返架完成 3 异常返架 database column wh_transit_return_item.status
     */
    private Integer status;

    /**
     *  database column wh_transit_return_item.quantity
     */
    private Integer quantity;

    /**
     *  database column wh_transit_return_item.complete_quantity
     */
    private Integer completeQuantity;

    /**
     * 创建时间 database column wh_transit_return_item.creation_date
     */
    private Timestamp creationDate;

    /**
     * 修改时间 database column wh_transit_return_item.last_update_date
     */
    private Timestamp lastUpdateDate;

    /**
     * 操作库存的数量 database column wh_transit_return_item.stock_quantity
     */
    private Integer stockQuantity;

    /**
     * 店铺 database column wh_transit_return_item.sku_store
     */
    private String skuStore;

    private WhSku whSku;

    private String sellSku;

    private Integer updateQuantity;

    private Integer stockId;

    public String getStatusName() {
        String name = null;
        if (this.status != null) {
            if (status.equals(1)) {
                name = "待返架";
            }
            else if (status.equals(2)) {
                name = "完成返架";
            }
            else if (status.equals(3)) {
                name = "异常返架";
            }
        }
        return name;
    }

    public String getItemLockKeys() {
        return skuStore + "-" + site + "-" + sku;
    }
}