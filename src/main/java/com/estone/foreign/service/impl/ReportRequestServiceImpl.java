package com.estone.foreign.service.impl;

import java.sql.Timestamp;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.estone.allocation.util.WarehouseProperties;
import com.estone.common.util.DateUtils;
import com.estone.common.util.DbTemplateUtils;
import com.estone.core.mq.Queues;
import com.estone.core.mq.RabbitMqExchange;
import com.estone.foreign.service.ReportRequestService;
import com.estone.statistics.bean.WhAssetChangeItemCalcBean;
import com.estone.statistics.service.AssetChangeItemService;
import com.estone.statistics.service.WhAssetChangeItemService;
import com.estone.system.rabbitmq.bean.AmqMessage;
import com.estone.system.rabbitmq.common.AmqMessageModuleName;
import com.estone.system.rabbitmq.service.AmqMessageService;
import com.whq.tool.context.DataContextHolder;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service("reportRequestService")
public class ReportRequestServiceImpl implements ReportRequestService {
    @Resource
    private AmqMessageService amqMessageService;

    @Override
    public void pushAssetSumToReport() {
        Integer warehouseId = WarehouseProperties.getWarehouseProperties().getLocalWarehouseId();
        String startDate = DateUtils.dateToString(new Date(),"yyyy-MM-dd");
        List<Map<String, Object>> assetSumList = getAssetSum();

        if (CollectionUtils.isEmpty(assetSumList)){
            log.info("无满足条件的sku");
            return;
        }
        log.info("pushAssetSumToReport Count:"+assetSumList.size());
        Map<String,Object> msgMap = new HashMap<>();
        msgMap.put("warehouse",warehouseId);
        msgMap.put("date",assetSumList);
        String message = JSON.toJSONString(msgMap);
        AmqMessage amqMessage = new AmqMessage();
        amqMessage.setModuleName(AmqMessageModuleName.PUSH_REPORT_ASSET_SUM.getCode());
        amqMessage.setExchange(RabbitMqExchange.PUSH_WMS_DIRECT_EXCHANGE);
        amqMessage.setQueue(Queues.PUSH_REPORT_ASSET_SUM_QUEUE);
        amqMessage.setRoutingKey(Queues.PUSH_REPORT_ASSET_SUM_QUEUE);
        amqMessage.setMessageBody(message);
        amqMessage.setRelevantParam(startDate);
        Integer createBy = 1;
        if (null != DataContextHolder.getUserId()) {
            createBy = DataContextHolder.getUserId();
        }
        amqMessage.setCreateBy(createBy);
        amqMessage.setCreateTime(new Timestamp(System.currentTimeMillis()));
        amqMessage.setSendStatus(false);
        amqMessage.setRetryLimit(0);
        amqMessageService.createAmqMessage(amqMessage);
    }

    private List<Map<String, Object>> getAssetSum(){
        List<Map<String, Object>> assetSumList = DbTemplateUtils.executeSqlScript(
                "SELECT SUM( end_stock ) AS inventoryQuantity, SUM( end_purchase_cost_price * end_stock ) AS inventoryAmount "
                        + "FROM( SELECT sku,end_stock,end_purchase_cost_price FROM wh_drp_turnover_itme_local o "
                        + "WHERE id IN ( SELECT MAX( id ) FROM wh_drp_turnover_itme_local GROUP BY sku ) "
                        + "UNION ALL "
                        + "SELECT sku,end_stock,end_purchase_cost_price FROM wh_drp_turnover_itme_transfer  "
                        + "WHERE id IN ( SELECT MAX( id ) FROM wh_drp_turnover_itme_transfer GROUP BY sku ) "
                        + ") t");

        List<Map<String, Object>> assetUnsalableSkuSumList = DbTemplateUtils.executeSqlScript(
                "SELECT SUM(end_stock) as unabInventoryQuantity,SUM(end_purchase_cost_price * end_stock) as unabInventoryAmount "
                        + "FROM (SELECT sku, end_stock, end_purchase_cost_price "
                        + "FROM wh_drp_turnover_itme_local o WHERE id in "
                        + "( SELECT MAX(wa.id) FROM wh_drp_turnover_itme_local wa INNER JOIN "
                        + "wh_sku_sale_statistic_record ws on wa.sku = ws.sku where ws.sale_attribute_setting_str in "
                        + "('长呆滞','短呆滞','滞销') group by wa.sku) UNION ALL SELECT sku,end_stock,end_purchase_cost_price FROM wh_drp_turnover_itme_transfer o "
                        + " WHERE id IN ( SELECT MAX( wa.id ) FROM wh_drp_turnover_itme_transfer wa "
                        + " INNER JOIN wh_sku_sale_statistic_record ws ON wa.sku = ws.sku "
                        + " WHERE ws.sale_attribute_setting_str IN ( '长呆滞', '短呆滞', '滞销' ) GROUP BY wa.sku "
                        + ")) t");

        assetSumList.addAll(assetUnsalableSkuSumList);
        return assetSumList;
    }
}
