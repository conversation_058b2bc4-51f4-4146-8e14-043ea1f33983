package com.estone.foreign.bean;

import lombok.Data;

import java.io.Serializable;

/**
 * @Description: ${description}
 * @Author: Yimeil
 * @Date: 2020/5/26 15:48
 * @Version: 1.0.0
 */
@Data
public class CheckInQuantityInfoDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    public CheckInQuantityInfoDTO() {

    }

    public CheckInQuantityInfoDTO(String purchaseOrderNo, Integer checkInUpQuantity, Integer checkInExceptionQuantity,
            Integer qcExceptionQuantity) {
        this.purchaseOrderNo = purchaseOrderNo;
        this.checkInUpQuantity = checkInUpQuantity;
        this.checkInExceptionQuantity = checkInExceptionQuantity;
        this.qcExceptionQuantity = qcExceptionQuantity;
    }

    /**
     * 采购单号
     */
    private String purchaseOrderNo;

    /**
     * 采购单上架数量
     */
    private Integer checkInUpQuantity = 0;

    /**
     * 采购单点数入库异常数量
     */
    private Integer checkInExceptionQuantity = 0;

    /**
     * 采购单QC异常数量
     */
    private Integer qcExceptionQuantity = 0;

}
