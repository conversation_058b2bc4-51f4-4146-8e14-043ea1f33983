package com.estone.foreign.bean;

import java.util.Date;
import java.util.List;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonFormat;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * @Description:接口参数
 * @Author: Yimeil
 * @Date: 2022/4/20 17:50
 * @Version: 1.0.0
 */
@Data
@Accessors(chain = true)
public class ReturnFormOrderDTO {

    /**
     * 类型
     */
    private String type;

    /**
     * 退换货单号
     */
    private String returnFormNo;

    /**
     * 入库物流公司
     */
    private String inboundCompany;

    /**
     * 入库物流单号
     */
    private String inboundTrackingNumber;

    private List<OrderInfo> orderList;

    @Data
    public static class SkuReturnInfo {
        private String sku;

        @JSONField(name = "quantity")
        private Integer returnQuantity;
    }

    @Data
    public static class OrderInfo {
        /**
         * 退换货单号
         */
        @JSONField(name = "returnFormNo")
        private String returnOrderNo;

        /**
         * 旺旺名称
         */
        @JSONField(name = "supplier")
        private String vendorName;

        /**
         * 创建人
         */
        @JSONField(name = "pushByName")
        private String creater;

        /**
         * 创建时间
         */
        @JSONField(name = "pushTime")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
        private Date createTime;

        /**
         * 员工id
         */
        @JSONField(name = "pushBy")
        private Long employeeId;

        /**
         * 运费支付方式
         */
        @JSONField(name = "payWay")
        private String freightWay;

        /**
         * 运费承担方
         */
        @JSONField(name = "freightCarrier")
        private String freightBearer;

        /**
         * 收件人
         */
        @JSONField(name = "receiver")
        private String recipients;

        /**
         * 联系方式
         */
        @JSONField(name = "receiverPhone")
        private String cellPhone;

        /**
         * 退货地址
         */
        private String returnAddress;

        /**
         * 备注
         */
        @JSONField(name = "remark")
        private String remarkInfo;

        /**
         * 退换货类型
         */
        private String returnGoodsType;

        /**
         * 退回原因
         */
        @JSONField(name = "backRemark")
        private String reason;

        /**
         * 供应商编码
         */
        private String vendorCode;

        @JSONField(name = "itemList")
        private List<SkuReturnInfo> skuReturnInfoList;
    }
}
