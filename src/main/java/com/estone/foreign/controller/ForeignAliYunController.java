package com.estone.foreign.controller;
import com.estone.pac.service.PacConsignOrderService;
import com.estone.system.rabbitmq.bean.AmqMessage;
import com.estone.system.rabbitmq.service.AmqMessageService;
import com.whq.tool.component.StatusCode;
import com.whq.tool.json.ResponseJson;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import javax.annotation.Resource;


/**
 * 阿里云对外接口
 * <AUTHOR>
 * @Date 2021/12/9 10:00
 **/
@Slf4j
@Controller
@RequestMapping(value = "/foreign/AliYun")
public class ForeignAliYunController {
    @Resource
    private PacConsignOrderService pacConsignOrderService;

    /**
     * 阿里云下发出库取消通知
     * @return
     */
    @RequestMapping(value="pacOrderCancel", method = {RequestMethod.POST})
    @ResponseBody
    public ResponseJson pacOrderCancel(@RequestBody AmqMessage amqMessage) {
        ResponseJson response = new ResponseJson();
        response.setStatus(StatusCode.FAIL);
        try {
            response=pacConsignOrderService.pacOrderCancel(amqMessage,false);
        } catch (Exception e) {
            log.error("优选仓仓库取消通知失败！"+e);
            if (e.getMessage().contains("库存扣减失败!")) {
                response.setStatus(StatusCode.UNAUTHORIZED);
            }
            response.setMessage(e.getMessage());
            return response;
        }
        return response;
    }

}
