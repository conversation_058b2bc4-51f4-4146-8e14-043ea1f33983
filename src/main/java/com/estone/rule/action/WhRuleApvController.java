package com.estone.rule.action;

import com.estone.apv.bean.SaleChannel;
import com.estone.apv.service.WhApvService;
import com.estone.rule.bean.WhRuleApv;
import com.estone.rule.bean.WhRuleApvQueryCondition;
import com.estone.rule.domain.WhRuleApvDo;
import com.estone.rule.service.WhRuleApvService;
import com.whq.tool.action.BaseController;
import com.whq.tool.component.Pager;
import com.whq.tool.json.ResponseJson;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpSession;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Controller
@RequestMapping(value = "rule/apvs")
public class WhRuleApvController extends BaseController {
    @Resource
    private WhRuleApvService whRuleApvService;

    @Resource
    private WhApvService whApvService;

    @RequestMapping(method = { RequestMethod.GET })
    public String init(@ModelAttribute("domain") WhRuleApvDo domain) {
        initFormData(domain);
        queryWhRuleApvs(domain);
        return "rule/ruleApvList";
    }

    private void initFormData(@ModelAttribute("domain") WhRuleApvDo domain) {
        List<SaleChannel> saleChannels = whApvService.getSaleChannels();
        Map<String, String> map = new HashMap<>();
        for (SaleChannel saleChannel : saleChannels) {
            map.put(saleChannel.getId().toString(), saleChannel.getName());
        }
        domain.setMap(map);
    }

    private void queryWhRuleApvs(@ModelAttribute("domain") WhRuleApvDo domain) {
        WhRuleApvQueryCondition query = domain.getQuery();
        Pager page = domain.getPage();
        if (query == null) {
            query = new WhRuleApvQueryCondition();
            domain.setQuery(query);
        }
        List<WhRuleApv> whRuleApvs = whRuleApvService.queryWhRuleApvs(query, page);
        domain.setWhRuleApvs(whRuleApvs);
    }

    @RequestMapping(value = "search", method = { RequestMethod.POST })
    public String search(@ModelAttribute("domain") WhRuleApvDo domain) {
        initFormData(domain);
        queryWhRuleApvs(domain);
        return "rule/ruleApvList";
    }

    @RequestMapping(value = "create", method = { RequestMethod.GET })
    public String toCreateWhRuleApv(@ModelAttribute("domain") WhRuleApvDo domain) {
        return "rule/ruleApvAdd";
    }

    @RequestMapping(value = "create", method = { RequestMethod.POST })
    public String createWhRuleApv(@ModelAttribute("domain") WhRuleApvDo domain, HttpSession session) {
        WhRuleApv whRuleApv = domain.getWhRuleApv();
        whRuleApvService.createWhRuleApv(whRuleApv);
        return "redirect:/rule/apvs";
    }

    @RequestMapping(value = "update", method = { RequestMethod.GET })
    public String toUpdateWhRuleApv(@ModelAttribute("domain") WhRuleApvDo domain,
            @RequestParam("whRuleApvId") Integer whRuleApvId) {
        WhRuleApv whRuleApv = whRuleApvService.getWhRuleApv(whRuleApvId);
        domain.setWhRuleApv(whRuleApv);
        return "rule/ruleApvUpdate";
    }

    @RequestMapping(value = "update", method = { RequestMethod.POST })
    public String updateWhRuleApv(@ModelAttribute("domain") WhRuleApvDo domain, HttpSession session) {
        WhRuleApv whRuleApv = domain.getWhRuleApv();
        whRuleApvService.updateWhRuleApv(whRuleApv);
        return "redirect:/rule/apvs";
    }

    @RequestMapping(value = "delete", method = { RequestMethod.GET })
    @ResponseBody
    public ResponseJson deleteWhRuleApv(@ModelAttribute("domain") WhRuleApvDo domain,
            @RequestParam("whRuleApvId") Integer whRuleApvId) {
        ResponseJson response = new ResponseJson();
        WhRuleApv whRuleApv = new WhRuleApv();
        whRuleApv.setId(whRuleApvId);
        whRuleApv.setLevel(-1);
        whRuleApvService.updateWhRuleApv(whRuleApv);
        return response;
    }
}