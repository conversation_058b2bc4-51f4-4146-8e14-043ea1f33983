package com.estone.rule.action;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.estone.checkin.enums.ShippingCompanyEnum;
import com.estone.rule.enums.ShippingCostRuleEnum;
import com.estone.common.SelectJson;
import com.estone.common.enums.LogModule;
import com.estone.common.util.CacheUtils;
import com.estone.common.util.CommonUtils;
import com.estone.common.util.SystemLogUtils;
import com.estone.rule.bean.*;
import com.estone.rule.common.ProvinceUtil;
import com.estone.rule.domain.WhShippingCostTemplateSettingDo;
import com.estone.rule.service.WhShippingCostTemplateItemSettingService;
import com.estone.rule.service.WhShippingCostTemplateSettingService;
import com.estone.system.param.bean.SystemParam;
import com.whq.tool.action.BaseController;
import com.whq.tool.component.Pager;
import com.whq.tool.component.StatusCode;
import com.whq.tool.context.DataContextHolder;
import com.whq.tool.json.ResponseJson;
import jodd.util.StringUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Controller
@RequestMapping(value = "shippingCostTemplate")
public class WhShippingCostTemplateSettingController extends BaseController {

    private final static SystemLogUtils COSTSETTING = SystemLogUtils.create(LogModule.COSTSETTING.getCode());

    @Resource
    private WhShippingCostTemplateSettingService whShippingCostTemplateSettingService;

    @Resource
    private WhShippingCostTemplateItemSettingService whShippingCostTemplateItemSettingService;

    @RequestMapping(method = { RequestMethod.GET })
    public String init(@ModelAttribute("domain") WhShippingCostTemplateSettingDo domain) {
        initFormData(domain, null);
        queryWhShippingCostTemplateSettings(domain);
        return "rule/shippingCostTemplateList";
    }

    private void initFormData(@ModelAttribute("domain") WhShippingCostTemplateSettingDo domain,
            @RequestParam(value = "id", required = false) Integer id) {
        // 获取所有省份
        SystemParam systemParam = CacheUtils.SystemParamGet("PROVINCE.PROVINCE");
        List<Province> provinceList = JSONArray.parseArray(systemParam.getParamValue(), Province.class);
        domain.setProvinceList(JSON.toJSONString(provinceList));
        if (id != null) {
            WhShippingCostTemplateItemSettingQueryCondition query = new WhShippingCostTemplateItemSettingQueryCondition();
            query.setTemplateId(id);
            List<WhShippingCostTemplateItemSetting> itemSettings = whShippingCostTemplateItemSettingService
                    .queryWhShippingCostTemplateItemSettings(query, null);
            if (CollectionUtils.isNotEmpty(itemSettings)) {
                List<String> selectedList = new ArrayList<String>();
                for (WhShippingCostTemplateItemSetting whShippingCostTemplateItemSetting : itemSettings) {
                    selectedList.addAll(CommonUtils.splitList(whShippingCostTemplateItemSetting.getProvinces(), ","));
                }
                domain.setSelectedList(JSON.toJSONString(selectedList));
            }
        }
        SystemParam systemShippingCompany = CacheUtils.SystemParamGet("SPECIFIED_DESC.EXPRESS_COMPANY");
        if (systemShippingCompany != null && StringUtil.isNotBlank(systemShippingCompany.getParamValue())) {
            domain.setShippingCompanys(systemShippingCompany.getParamValue());
        }
        else {
            String list = SelectJson.getList(ShippingCompanyEnum.values());
            domain.setShippingCompanys(list);// 物流公司
        }
        domain.setShippingCostRules(SelectJson.getList(ShippingCostRuleEnum.values()));//运费规则
    }

    private void queryWhShippingCostTemplateSettings(@ModelAttribute("domain") WhShippingCostTemplateSettingDo domain) {
        WhShippingCostTemplateSettingQueryCondition query = domain.getQuery();
        Pager page = domain.getPage();
        if (query == null) {
            query = new WhShippingCostTemplateSettingQueryCondition();
            domain.setQuery(query);
        }
        List<WhShippingCostTemplateSetting> whShippingCostTemplateSettings = whShippingCostTemplateSettingService
                .queryWhShippingCostTemplateSettings(query, page);
        domain.setWhShippingCostTemplateSettings(whShippingCostTemplateSettings);
    }

    @RequestMapping(value = "search", method = { RequestMethod.POST })
    public String search(@ModelAttribute("domain") WhShippingCostTemplateSettingDo domain) {
        queryWhShippingCostTemplateSettings(domain);
        return "rule/shippingCostTemplateList";
    }

    @RequestMapping(value = "edit", method = { RequestMethod.GET })
    public String toUpdateWhShippingCostTemplateSetting(
            @ModelAttribute("domain") WhShippingCostTemplateSettingDo domain,
            @RequestParam(value = "id", required = false) Integer id) {
        initFormData(domain, id);
        if (id != null) {
            WhShippingCostTemplateSetting whShippingCostTemplateSetting = whShippingCostTemplateSettingService
                    .getWhShippingCostTemplateSetting(id);
            domain.setWhShippingCostTemplateSetting(whShippingCostTemplateSetting);
            if (StringUtils.isBlank(whShippingCostTemplateSetting.getShippingCostRule()))
                whShippingCostTemplateSetting.setShippingCostRule(ShippingCostRuleEnum.NONE.getCode());
        }else{
            WhShippingCostTemplateSetting whShippingCostTemplateSetting = new WhShippingCostTemplateSetting();
            whShippingCostTemplateSetting.setShippingCostRule(ShippingCostRuleEnum.NONE.getCode());
            domain.setWhShippingCostTemplateSetting(whShippingCostTemplateSetting);
        }
        return "rule/shippingCostTemplateEdit";
    }

    @RequestMapping(value = "save", method = { RequestMethod.POST })
    @ResponseBody
    public ResponseJson updateWhShippingCostTemplateSetting(
            @RequestBody WhShippingCostTemplateSetting whShippingCostTemplateSetting) {
        ResponseJson responseJson = new ResponseJson();
        responseJson.setStatus(StatusCode.FAIL);
        if (whShippingCostTemplateSetting == null || whShippingCostTemplateSetting.getId() == null) {
            responseJson.setMessage("参数为空！");
            return responseJson;
        }
        WhShippingCostTemplateSetting dbSetting = whShippingCostTemplateSettingService
                .getWhShippingCostTemplateSetting(whShippingCostTemplateSetting.getId());
        if (dbSetting != null) {
            List<WhShippingCostTemplateItemSetting> whShippingCostTemplateItemSettings = whShippingCostTemplateSetting
                    .getWhShippingCostTemplateItemSettingList();
            if (CollectionUtils.isEmpty(whShippingCostTemplateItemSettings)){
                responseJson.setMessage("请填写运费配置明细！");
                return responseJson;
            }
            if (StringUtils.isNotBlank(whShippingCostTemplateSetting.getLength())
                    || StringUtils.isNotBlank(whShippingCostTemplateSetting.getWidth())
                    || StringUtils.isNotBlank(whShippingCostTemplateSetting.getHeight())
                    || whShippingCostTemplateSetting.getWeight()!= null) {
                if (StringUtils.isBlank(whShippingCostTemplateSetting.getLength())
                        || StringUtils.isBlank(whShippingCostTemplateSetting.getWidth())
                        || StringUtils.isBlank(whShippingCostTemplateSetting.getHeight())
                        || whShippingCostTemplateSetting.getWeight()== null) {
                    responseJson.setMessage("请填写完整的体积重规则！");
                    return responseJson;
                }
            }
            whShippingCostTemplateSettingService.updateWhShippingCostTemplateSetting(whShippingCostTemplateSetting);
            String logStr = "";
            if (!whShippingCostTemplateSetting.getTemplateName().equals(dbSetting.getTemplateName())) {
                logStr += "<br/>修改模板名称：原值：" + dbSetting.getTemplateName() + " ,新值："
                        + whShippingCostTemplateSetting.getTemplateName();
            }
            //
            boolean flag = false;
            if (!StringUtils.equalsIgnoreCase(whShippingCostTemplateSetting.getShippingCostRule(),dbSetting.getShippingCostRule())) {
                logStr += "<br/>修改运费规则：原值：" + ShippingCostRuleEnum.getNameByCode(dbSetting.getShippingCostRule()) + " ,新值："
                        + ShippingCostRuleEnum.getNameByCode(whShippingCostTemplateSetting.getShippingCostRule());
                if (StringUtils.equalsIgnoreCase(ShippingCostRuleEnum.NONE.getCode(),whShippingCostTemplateSetting.getShippingCostRule())){
                    flag = true;
                }
            }
            if (!StringUtils.equalsIgnoreCase(whShippingCostTemplateSetting.getLength(),dbSetting.getLength())) {
                logStr += "<br/>修改体积重-长：原值：" + dbSetting.getLength() + " ,新值："
                        + whShippingCostTemplateSetting.getLength();
            }
            if (!StringUtils.equalsIgnoreCase(whShippingCostTemplateSetting.getWidth(), dbSetting.getWidth())) {
                logStr += "<br/>修改体积重-宽：原值：" + dbSetting.getWidth() + " ,新值："
                        + whShippingCostTemplateSetting.getWidth();
            }
            if (!StringUtils.equalsIgnoreCase(whShippingCostTemplateSetting.getHeight(), dbSetting.getHeight())) {
                logStr += "<br/>修改体积重-高：原值：" + dbSetting.getHeight() + " ,新值："
                        + whShippingCostTemplateSetting.getHeight();
            }
            if (!StringUtils.equalsIgnoreCase(whShippingCostTemplateSetting.getWeight()+"", dbSetting.getWeight()+"")) {
                logStr += "<br/>修改体积重-重：原值：" + dbSetting.getWeight() + " ,新值："
                        + whShippingCostTemplateSetting.getWeight();
            }
            List<WhShippingCostTemplateItemSetting> dbItems = dbSetting.getWhShippingCostTemplateItemSettingList();

            for (WhShippingCostTemplateItemSetting item : whShippingCostTemplateItemSettings) {
                if (item.getId() == null || item.getId() == 0) {
                    whShippingCostTemplateItemSettingService.createWhShippingCostTemplateItemSetting(item);
                    logStr += "<br/>新增明细：" + item.getId() + " ，地区："
                            + ProvinceUtil.getProvinceNames(CommonUtils.splitList(item.getProvinces(), ",")) + " ，首重："
                            + item.getFirstWeight() + " ，首重运费：" + item.getFirstWeightCost() + "　，续重："
                            + item.getContinuedWeight() + " ，续重运费：" + item.getContinuedWeightCost()+" ，区间值下阈值：" + item.getMinWeight()
                            + " ，区间值上阈值：" + item.getMaxWeight();
                }
                else {
                    for (WhShippingCostTemplateItemSetting dbItem : dbItems) {
                        if (item.getId().equals(dbItem.getId())) {
                            item.setMegerFlag(flag);
                            whShippingCostTemplateItemSettingService.updateWhShippingCostTemplateItemSetting(item);
                            if (!StringUtils.equalsIgnoreCase(item.getMinWeight()+"",dbItem.getMinWeight()+"")) {
                                logStr += "<br/>修改明细" + dbItem.getId() + "区间值下阈值，原值：" + dbItem.getMinWeight() + " ,新值："
                                        + item.getMinWeight();
                            }
                            if (!StringUtils.equalsIgnoreCase(item.getMaxWeight()+"",dbItem.getMaxWeight()+"")) {
                                logStr += "<br/>修改明细" + dbItem.getId() + "区间值上阈值，原值：" + dbItem.getMaxWeight() + " ,新值："
                                        + item.getMaxWeight();
                            }
                            if (!item.getProvinces().equals(dbItem.getProvinces())) {
                                logStr += "<br/>修改明细" + dbItem.getId() + "地区，原值：" + dbItem.getProvinceName() + " ,新值："
                                        + ProvinceUtil
                                                .getProvinceNames(CommonUtils.splitList(item.getProvinces(), ","));
                            }
                            if (!item.getFirstWeight().equals(dbItem.getFirstWeight())) {
                                logStr += "<br/>修改明细" + dbItem.getId() + "首重，原值：" + dbItem.getFirstWeight() + " ,新值："
                                        + item.getFirstWeight();
                            }
                            if (!item.getFirstWeightCost().equals(dbItem.getFirstWeightCost())) {
                                logStr += "<br/>修改明细" + dbItem.getId() + "首重运费，原值：" + dbItem.getFirstWeightCost()
                                        + " ,新值：" + item.getFirstWeightCost();
                            }
                            if (!item.getContinuedWeight().equals(dbItem.getContinuedWeight())) {
                                logStr += "<br/>修改明细" + dbItem.getId() + "续重，原值：" + dbItem.getContinuedWeight()
                                        + " ,新值：" + item.getContinuedWeight();
                            }
                            if (!item.getContinuedWeightCost().equals(dbItem.getContinuedWeightCost())) {
                                logStr += "<br/>修改明细" + dbItem.getId() + "续重运费，原值：" + dbItem.getContinuedWeightCost()
                                        + " ,新值：" + item.getContinuedWeightCost();
                            }
                        }
                    }
                }
            }
            if (StringUtils.isNotBlank(logStr)){
                COSTSETTING.log(whShippingCostTemplateSetting.getId(), logStr);
            }
            responseJson.setStatus(StatusCode.SUCCESS);
        }
        return responseJson;
    }

    @RequestMapping(value = "create", method = { RequestMethod.POST })
    @ResponseBody
    public ResponseJson createWhShippingCostTemplateSetting(
            @RequestBody WhShippingCostTemplateSetting whShippingCostTemplateSetting) {
        ResponseJson responseJson = new ResponseJson();
        responseJson.setStatus(StatusCode.FAIL);
        if (whShippingCostTemplateSetting == null
                || StringUtils.isBlank(whShippingCostTemplateSetting.getShippingType())) {
            responseJson.setMessage("参数为空！");
            return responseJson;
        }
        WhShippingCostTemplateSettingQueryCondition query = new WhShippingCostTemplateSettingQueryCondition();
        query.setShippingType(whShippingCostTemplateSetting.getShippingType());
        WhShippingCostTemplateSetting dbSetting = whShippingCostTemplateSettingService
                .queryWhShippingCostTemplateSetting(query);
        if (dbSetting != null) {
            responseJson.setMessage("所选快递方式已存在，请重新选择！");
            return responseJson;
        }
        List<WhShippingCostTemplateItemSetting> whShippingCostTemplateItemSettings = whShippingCostTemplateSetting
                .getWhShippingCostTemplateItemSettingList();
        if (CollectionUtils.isEmpty(whShippingCostTemplateItemSettings)){
            responseJson.setMessage("请填写运费配置明细！");
            return responseJson;
        }
        if (StringUtils.isNotBlank(whShippingCostTemplateSetting.getLength())
                || StringUtils.isNotBlank(whShippingCostTemplateSetting.getWidth())
                    || StringUtils.isNotBlank(whShippingCostTemplateSetting.getHeight())
                        || whShippingCostTemplateSetting.getWeight()!= null) {
            if (StringUtils.isBlank(whShippingCostTemplateSetting.getLength())
                    || StringUtils.isBlank(whShippingCostTemplateSetting.getWidth())
                    || StringUtils.isBlank(whShippingCostTemplateSetting.getHeight())
                    || whShippingCostTemplateSetting.getWeight()== null) {
                responseJson.setMessage("请填写完整的体积重规则！");
                return responseJson;
            }

        }
        whShippingCostTemplateSetting.setCreateBy(DataContextHolder.getUserId());
        whShippingCostTemplateSetting.setCreatedDate(new Timestamp(System.currentTimeMillis()));
        whShippingCostTemplateSettingService.createWhShippingCostTemplateSetting(whShippingCostTemplateSetting);
        if (CollectionUtils.isNotEmpty(whShippingCostTemplateItemSettings)) {
            for (WhShippingCostTemplateItemSetting itemSetting : whShippingCostTemplateItemSettings) {
               /* if (StringUtils.isBlank(itemSetting.getProvinces())){
                    responseJson.setMessage("请填写运费配置明细！");
                    return responseJson;
                }*/
                itemSetting.setTemplateId(whShippingCostTemplateSetting.getId());
            }
            whShippingCostTemplateItemSettingService
                    .batchCreateWhShippingCostTemplateItemSetting(whShippingCostTemplateItemSettings);
        }
        COSTSETTING.log(whShippingCostTemplateSetting.getId(), "新增运费模板");
        responseJson.setStatus(StatusCode.SUCCESS);
        return responseJson;
    }

    @RequestMapping(value = "editProvince", method = { RequestMethod.POST })
    @ResponseBody
    public ResponseJson editProvince(@ModelAttribute("domain") WhShippingCostTemplateSettingDo domain,
            @RequestParam(value = "itemId", required = false) Integer itemId) {
        ResponseJson responseJson = new ResponseJson();
        if (itemId != null) {
            WhShippingCostTemplateItemSetting itemSetting = whShippingCostTemplateItemSettingService
                    .getWhShippingCostTemplateItemSetting(itemId);
            Map<String, Object> map = new HashMap<>();
            map.put("itemSetting", itemSetting);
            responseJson.setBody(map);
        }
        return responseJson;
    }

    @RequestMapping(value = "delete", method = { RequestMethod.GET })
    @ResponseBody
    public ResponseJson deleteWhShippingCostTemplateSetting(
            @ModelAttribute("domain") WhShippingCostTemplateSettingDo domain, @RequestParam("id") Integer id) {
        ResponseJson response = new ResponseJson();
        if (id == null) {
            response.setStatus(StatusCode.FAIL);
            response.setMessage("参数为空！");
            return response;
        }
        whShippingCostTemplateSettingService.deleteWhShippingCostTemplateSetting(id);
        //删除明细
        whShippingCostTemplateItemSettingService.deleteItemByTemplateId(id);
        return response;
    }
}