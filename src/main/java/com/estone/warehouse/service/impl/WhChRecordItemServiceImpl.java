package com.estone.warehouse.service.impl;

import com.estone.warehouse.bean.WhChRecordItem;
import com.estone.warehouse.bean.WhChRecordItemQueryCondition;
import com.estone.warehouse.dao.WhChRecordItemDao;
import com.estone.warehouse.service.WhChRecordItemService;
import com.whq.tool.component.Pager;
import com.whq.tool.exception.BusinessException;
import com.whq.tool.exception.DBErrorCode;
import com.whq.tool.sqler.SqlerException;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

@Service("whChRecordItemService")
@Slf4j
public class WhChRecordItemServiceImpl implements WhChRecordItemService {
    @Resource
    private WhChRecordItemDao whChRecordItemDao;

    @Override
    public WhChRecordItem getWhChRecordItem(Integer id) {
        WhChRecordItem whChRecordItem = whChRecordItemDao.queryWhChRecordItem(id);
        return whChRecordItem;
    }

    @Override
    public WhChRecordItem getWhChRecordItemDetail(Integer id) {
        WhChRecordItem whChRecordItem = whChRecordItemDao.queryWhChRecordItem(id);
        // 关联查询
        return whChRecordItem;
    }

    @Override
    public WhChRecordItem queryWhChRecordItem(WhChRecordItemQueryCondition query) {
        Assert.notNull(query, "query is null!");
        WhChRecordItem whChRecordItem = whChRecordItemDao.queryWhChRecordItem(query);
        return whChRecordItem;
    }

    @Override
    public List<WhChRecordItem> queryAllWhChRecordItems() {
        return whChRecordItemDao.queryWhChRecordItemList();
    }

    @Override
    public List<WhChRecordItem> queryWhChRecordItems(WhChRecordItemQueryCondition query, Pager pager) {
        Assert.notNull(query, "query is null!");
        if (pager != null && pager.isQueryCount()) {
            int count = whChRecordItemDao.queryWhChRecordItemCount(query);
            pager.setTotalCount(count);
            if ( count == 0) {
                return new ArrayList<WhChRecordItem>();
            }
        }
        List<WhChRecordItem> whChRecordItems = whChRecordItemDao.queryWhChRecordItemList(query, pager);
        return whChRecordItems;
    }

    @Override
    public void createWhChRecordItem(WhChRecordItem whChRecordItem) {
        try {
            whChRecordItemDao.createWhChRecordItem(whChRecordItem);
        }
        catch (SqlerException e) {
            log.error(e.getMessage(), e);
            throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
        }
    }

    @Override
    public void batchCreateWhChRecordItem(List<WhChRecordItem> entityList) {
        if (CollectionUtils.isNotEmpty(entityList)) {
            try {
                whChRecordItemDao.batchCreateWhChRecordItem(entityList);
            }
            catch (SqlerException e) {
                log.error(e.getMessage(), e);
                throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
            }
        }
    }

    @Override
    public void deleteWhChRecordItem(Integer id) {
        try {
            whChRecordItemDao.deleteWhChRecordItem(id);
        }
        catch (SqlerException e) {
            log.error(e.getMessage(), e);
            throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
        }
    }

    @Override
    public void updateWhChRecordItem(WhChRecordItem whChRecordItem) {
        try {
            whChRecordItemDao.updateWhChRecordItem(whChRecordItem);
        }
        catch (SqlerException e) {
            log.error(e.getMessage(), e);
            throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
        }
    }

    @Override
    public void batchUpdateWhChRecordItem(List<WhChRecordItem> entityList) {
        if (CollectionUtils.isNotEmpty(entityList)) {
            try {
                whChRecordItemDao.batchUpdateWhChRecordItem(entityList);
            }
            catch (SqlerException e) {
                log.error(e.getMessage(), e);
                throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
            }
        }
    }
}