package com.estone.warehouse.service.impl;

import com.estone.asn.bean.WhAsn;
import com.estone.asn.bean.WhAsnItem;
import com.estone.pac.bean.TakeStockDTO;
import com.estone.pac.enums.TakeStockReasonEnum;
import com.estone.pac.service.TakeStockRecordService;
import com.estone.statistics.bean.WhAssetChangeItem;
import com.estone.statistics.enums.AssetInventoryType;
import com.estone.statistics.enums.AssetOrderType;
import com.estone.statistics.service.WhAssetChangeItemService;
import com.estone.system.rabbitmq.bean.AmqMessage;
import com.estone.system.rabbitmq.model.ProductSkuMessage;
import com.estone.system.rabbitmq.service.AmqMessageService;
import com.estone.system.rabbitmq.utils.AssembleMessageDataUtils;
import com.estone.warehouse.bean.WhStock;
import com.estone.warehouse.bean.WhStockLog;
import com.estone.warehouse.bean.WhStockQueryCondition;
import com.estone.warehouse.enums.StockLogStep;
import com.estone.warehouse.enums.StockLogType;
import com.estone.warehouse.service.WhAsnUpdateStockService;
import com.estone.warehouse.service.WhStockLogService;
import com.estone.warehouse.service.WhStockService;
import com.whq.tool.exception.BusinessException;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @Description 海外仓调拨单库存接口实现类
 * @date 2020/11/24 11:30
 */
@Service("whAsnUpdateStockService")
public class WhAsnUpdateStockServiceImpl implements WhAsnUpdateStockService {

    private Logger log = LoggerFactory.getLogger(WhAsnUpdateStockServiceImpl.class);

    @Resource
    private WhStockService whStockService;

    @Resource
    private WhStockLogService whStockLogService;

    @Resource
    private TakeStockRecordService takeStockRecordService;

    /**
     * @return full 是否匹配完成
     * @Description 海外仓调拨单分配库存
     * <AUTHOR>
     * @date 2020/11/24 11:48
     * @param: whAsn
     */
    @Override
    public boolean allot(WhAsn whAsn) {
        boolean full = true;
        String orderNo = whAsn.getReceivingCode();
        List<String> skus = whAsn.buildGroupItems().stream().map(item -> item.getProductSku()).collect(Collectors.toList());
        Map<String, WhStock> stockMap = whStockService.getWhStockMapBySkuList(skus);
        if (stockMap == null) {
            stockMap = new HashMap<String, WhStock>();
        }
        List<WhStock> stockList = new ArrayList<WhStock>();
        List<WhStockLog> stockLogs = new ArrayList<WhStockLog>();
        List<TakeStockDTO> takeStockDTOList = new ArrayList<>();//盘点记录

        StockLogStep logStep = StockLogStep.ASN_ALLOT;
        for (WhAsnItem whAsnItem : whAsn.getGroupItems()) {
            String sku = whAsnItem.getProductSku();
            WhStock whStock = stockMap.get(sku);
            if (null == whStock) {
                full = false;
                continue;
            }
            /**
             * 减：可用
             * 加：已分配
             */
            Integer quantity = whAsnItem.getQuantity() == null ? 0 : whAsnItem.getQuantity();
            Integer allot = whAsnItem.getAllotQuantity() == null ? 0 : whAsnItem.getAllotQuantity();
            Integer operateQuantity = quantity - allot;
            if (operateQuantity <= 0) {
                continue;
            }

            WhStock updateStock = WhStock.buildUpdateStock(stockMap.get(sku));
            Integer surplusQuantity = whStock.getSurplusQuantity();
            Integer allotQuantity = whStock.getAllotQuantity();

            if (surplusQuantity <= 0) {
                full = false;
                continue;
            }
            if ((surplusQuantity - operateQuantity) < 0) {
                operateQuantity = surplusQuantity;
            }
            whAsnItem.setAllotQuantity(allot + operateQuantity);

            updateStock.setSurplusQuantity(surplusQuantity - operateQuantity);
            updateStock.setAllotQuantity(allotQuantity + operateQuantity);
            stockList.add(updateStock);
            stockLogs.add(new WhStockLog(sku, StockLogType.USABLE_STOCK, logStep, -operateQuantity, surplusQuantity, orderNo));
            stockLogs.add(new WhStockLog(sku, StockLogType.ALLOCATED, logStep, operateQuantity, allotQuantity, orderNo));
            // 盘点记录
            takeStockDTOList.add(new TakeStockDTO(sku, TakeStockReasonEnum.OVERSEAS_WAREHOUSE_ALLOCATION.intCode(),
                    orderNo, -operateQuantity, new Timestamp(System.currentTimeMillis()), 1));
            if (!whAsnItem.getQuantity().equals(whAsnItem.getAllotQuantity())) {
                full = false;
            }
        }
        // 匹配已分配数量
        whAsn.buildItemQuantity(1);
        if (CollectionUtils.isNotEmpty(stockList)) {
            whStockService.batchUpdateWhStock(stockList);
        }
        if (CollectionUtils.isNotEmpty(stockLogs)) {
            whStockLogService.batchAddWhStockLog(stockLogs);
        }
        // 添加盘点记录
        takeStockRecordService.doBatchCreateRecordAndLog(takeStockDTOList, null);

        return full;
    }


    /**
     * @return void
     * @Description 海外仓调拨单取消库存（拣货前）
     * <AUTHOR>
     * @date 2020/11/24 11:49
     * @param: whAsn
     */
    @Override
    public void cancel(WhAsn whAsn) {
        String orderNo = whAsn.getReceivingCode();
        List<String> skus = whAsn.buildGroupItems().stream().map(item -> item.getProductSku()).collect(Collectors.toList());
        Map<String, WhStock> stockMap = whStockService.getWhStockMapBySkuList(skus);
        if (stockMap == null) {
            stockMap = new HashMap<String, WhStock>();
        }
        List<WhStock> stockList = new ArrayList<WhStock>();
        List<WhStockLog> stockLogs = new ArrayList<WhStockLog>();
        List<TakeStockDTO> takeStockDTOList = new ArrayList<>(); // 盘点记录
        StockLogStep logStep = StockLogStep.ASN_CANCEL;
        for (WhAsnItem whAsnItem : whAsn.getGroupItems()) {
            String sku = whAsnItem.getProductSku();
            WhStock whStock = stockMap.get(sku);
            if (null == whStock) {
                throw new BusinessException("sku: " + sku + "库存记录为空");
            }
            /**
             * 加：可用
             * 减：已分配
             */
            Integer allot = whAsnItem.getAllotQuantity() == null ? 0 : whAsnItem.getAllotQuantity();
            if (allot == 0) {
                continue;
            }

            WhStock updateStock = WhStock.buildUpdateStock(stockMap.get(sku));
            Integer surplusQuantity = whStock.getSurplusQuantity();
            Integer allotQuantity = whStock.getAllotQuantity();
            if ((allotQuantity - allot) < 0) {
                log.error("orderNo:" + orderNo + ",sku:" + whStock.getSku() + "已分配库存不能为负: allotQuantity[ " + allotQuantity + " ], allot[ " + allot + " ]");
                throw new RuntimeException(String.format("sku:%s已分配库存不能为负: 已分配库存[%s], 扣已分配[%s]", whStock.getSku(), allotQuantity, allot));
            }

            updateStock.setSurplusQuantity(surplusQuantity + allot);
            updateStock.setAllotQuantity(allotQuantity - allot);
            stockList.add(updateStock);
            stockLogs.add(new WhStockLog(sku, StockLogType.USABLE_STOCK, logStep, allot, surplusQuantity, orderNo));
            stockLogs.add(new WhStockLog(sku, StockLogType.ALLOCATED, logStep, -allot, allotQuantity, orderNo));
            // 盘点记录
            takeStockDTOList
                    .add(new TakeStockDTO(sku, TakeStockReasonEnum.OVERSEAS_WAREHOUSE_ALLOCATION_CANCELLATION.intCode(),
                            orderNo, allot, new Timestamp(System.currentTimeMillis()),1));
        }
        if (CollectionUtils.isNotEmpty(stockList)) {
            whStockService.batchUpdateWhStock(stockList);
        }
        if (CollectionUtils.isNotEmpty(stockLogs)) {
            whStockLogService.batchAddWhStockLog(stockLogs);
        }
        // 添加盘点记录
        takeStockRecordService.doBatchCreateRecordAndLog(takeStockDTOList,null);
    }


    /**
     * @return void
     * @Description 海外仓调拨单取消库存（拣货开始后）
     * <AUTHOR>
     * @date 2020/11/24 18:02
     * @param: whAsn
     */
    @Override
    public void cancelStartPick(WhAsn whAsn) {
        String orderNo = whAsn.getReceivingCode();
        List<String> skus = whAsn.buildGroupItems().stream().map(item -> item.getProductSku()).collect(Collectors.toList());
        Map<String, WhStock> stockMap = whStockService.getWhStockMapBySkuList(skus);
        if (stockMap == null) {
            stockMap = new HashMap<String, WhStock>();
        }
        List<WhStock> stockList = new ArrayList<WhStock>();
        List<WhStockLog> stockLogs = new ArrayList<WhStockLog>();
        List<TakeStockDTO> takeStockDTOList = new ArrayList<>();// 盘点记录
        StockLogStep logStep = StockLogStep.ASN_CANCEL;
        for (WhAsnItem whAsnItem : whAsn.getGroupItems()) {
            String sku = whAsnItem.getProductSku();
            WhStock whStock = stockMap.get(sku);
            if (null == whStock) {
                throw new BusinessException("sku: " + sku + "库存记录为空");
            }
            /**
             * 加：取消/可用
             * 减：已拣/已分配
             */
            Integer quantity = whAsnItem.getAllotQuantity() == null ? 0 : whAsnItem.getAllotQuantity();
            if (quantity <= 0) {
                continue;
            }
            Integer picked = whAsnItem.getPickQuantity() == null ? 0 : whAsnItem.getPickQuantity();
            Integer notPick = quantity - picked;

            WhStock updateStock = WhStock.buildUpdateStock(stockMap.get(sku));
            Integer surplusQuantity = whStock.getSurplusQuantity();
            Integer allotQuantity = whStock.getAllotQuantity();
            Integer pickQuantity = whStock.getPickQuantity();
            Integer cancelQuantity = whStock.getCancelQuantity();

            updateStock.setSurplusQuantity(surplusQuantity + notPick);
            updateStock.setAllotQuantity(allotQuantity - notPick);
            updateStock.setPickQuantity(pickQuantity - picked);
            updateStock.setCancelQuantity(cancelQuantity + picked);
            stockList.add(updateStock);
            if (notPick > 0) {
                stockLogs.add(new WhStockLog(sku, StockLogType.USABLE_STOCK, logStep, notPick, surplusQuantity, orderNo));
                stockLogs.add(new WhStockLog(sku, StockLogType.ALLOCATED, logStep, -notPick, allotQuantity, orderNo));
                // 盘点记录
                takeStockDTOList.add(
                        new TakeStockDTO(sku, TakeStockReasonEnum.OVERSEAS_WAREHOUSE_ALLOCATION_CANCELLATION.intCode(),
                                orderNo, notPick, new Timestamp(System.currentTimeMillis()),1));
            }
            if (picked > 0) {
                stockLogs.add(new WhStockLog(sku, StockLogType.PICKED_STOCK, logStep, -picked, pickQuantity, orderNo));
                stockLogs.add(new WhStockLog(sku, StockLogType.CANCEL_STOCK, logStep, picked, cancelQuantity, orderNo));
            }
        }
        if (CollectionUtils.isNotEmpty(stockList)) {
            whStockService.batchUpdateWhStock(stockList);
        }
        if (CollectionUtils.isNotEmpty(stockLogs)) {
            whStockLogService.batchAddWhStockLog(stockLogs);
        }
        // 添加盘点记录
        takeStockRecordService.doBatchCreateRecordAndLog(takeStockDTOList, null);
    }

    /**
     * 本仓交运
     *
     * @param whAsn
     */
    @Override
    public void deliverBySelf(WhAsn whAsn) {
        String orderNo = whAsn.getReceivingCode();
        List<String> skus = whAsn.buildGroupItems().stream().map(item -> item.getProductSku()).collect(Collectors.toList());
        Map<String, WhStock> stockMap = whStockService.getWhStockMapBySkuList(skus);
        if (stockMap == null) {
            stockMap = new HashMap<String, WhStock>();
        }
        List<WhStock> stockList = new ArrayList<WhStock>();
        List<WhStockLog> stockLogs = new ArrayList<WhStockLog>();
        StockLogStep logStep = StockLogStep.ASN_DELIVER;
        for (WhAsnItem whAsnItem : whAsn.getGroupItems()) {
            String sku = whAsnItem.getProductSku();
            WhStock whStock = stockMap.get(sku);
            if (null == whStock) {
                throw new BusinessException("sku: " + sku + "库存记录为空");
            }
            /**
             * 加：已交运、已拣返架
             * 减：已拣
             */
            Integer loadingQuantity = whAsnItem.getLoadingQuantity() == null ? 0 : whAsnItem.getLoadingQuantity();
            Integer pickedQuantity = whAsnItem.getPickQuantity() == null ? 0 : whAsnItem.getPickQuantity();

            WhStock updateStock = WhStock.buildUpdateStock(stockMap.get(sku));
            Integer deliverQuantity = whStock.getDeliverQuantity();
            Integer pickQuantity = whStock.getPickQuantity();
            Integer pickReturnQuantity = whStock.getPickReturnQuantity();

            updateStock.setDeliverQuantity(deliverQuantity + loadingQuantity);
            updateStock.setPickQuantity(pickQuantity - pickedQuantity);
            updateStock.setPickReturnQuantity(pickReturnQuantity + (pickedQuantity - loadingQuantity));
            stockList.add(updateStock);

            stockLogs.add(new WhStockLog(sku, StockLogType.PICKED_STOCK, logStep, -pickedQuantity, pickQuantity, orderNo));
            stockLogs.add(new WhStockLog(sku, StockLogType.DELIVER, logStep, loadingQuantity, deliverQuantity, orderNo));
            if ((pickedQuantity - loadingQuantity) > 0) {
                stockLogs.add(new WhStockLog(sku, StockLogType.PICKED_RETURN_STOCK, logStep, (pickedQuantity - loadingQuantity), pickReturnQuantity, orderNo));
            }
        }
        if (CollectionUtils.isNotEmpty(stockList)) {
            whStockService.batchUpdateWhStock(stockList);
        }
        if (CollectionUtils.isNotEmpty(stockLogs)) {
            whStockLogService.batchAddWhStockLog(stockLogs);
        }
    }

    /**
     * 跨仓交运（新仓库存）
     *
     * @param whAsn
     */
    @Override
    public void deliver(WhAsn whAsn) {
        String orderNo = whAsn.getReceivingCode();
        List<String> skus = whAsn.buildGroupItems().stream().map(item -> item.getProductSku()).collect(Collectors.toList());
        Map<String, WhStock> stockMap = whStockService.getWhStockMapBySkuList(skus);
        if (stockMap == null) {
            stockMap = new HashMap<String, WhStock>();
        }
        List<WhStock> stockList = new ArrayList<WhStock>();
        List<WhStockLog> stockLogs = new ArrayList<WhStockLog>();
        StockLogStep logStep = StockLogStep.ASN_DELIVER;
        for (WhAsnItem whAsnItem : whAsn.getGroupItems()) {
            String sku = whAsnItem.getProductSku();
            WhStock whStock = stockMap.get(sku);
            if (null == whStock) {
                throw new BusinessException("sku: " + sku + "库存记录为空");
            }
            /**
             * 加：已交运
             * 减：订单调拨
             */
            Integer quantity = whAsnItem.getLoadingQuantity() == null ? 0 : whAsnItem.getLoadingQuantity();
            if (quantity <= 0) {
                continue;
            }
            WhStock updateStock = WhStock.buildUpdateStock(stockMap.get(sku));
            Integer deliverQuantity = whStock.getDeliverQuantity();
            Integer orderAllocationQuantity = whStock.getOrderAllocationQuantity();

            updateStock.setDeliverQuantity(deliverQuantity + quantity);
            updateStock.setOrderAllocationQuantity(orderAllocationQuantity - quantity);
            stockList.add(updateStock);

            stockLogs.add(new WhStockLog(sku, StockLogType.ORDER_ALLOCATION, logStep, -quantity, orderAllocationQuantity, orderNo));
            stockLogs.add(new WhStockLog(sku, StockLogType.DELIVER, logStep, quantity, deliverQuantity, orderNo));

        }
        if (CollectionUtils.isNotEmpty(stockList)) {
            whStockService.batchUpdateWhStock(stockList);
        }
        if (CollectionUtils.isNotEmpty(stockLogs)) {
            whStockLogService.batchAddWhStockLog(stockLogs);
        }
    }

    /**
     * 拣货下一步，库存逻辑
     * 
     * @param sku
     * @param pickQuantity
     * @param taskNo
     * @return
     */
    @Override
    public boolean singlePick(String sku, Integer pickQuantity, String taskNo) {
        WhStockQueryCondition query = new WhStockQueryCondition();
        query.setSku(sku);
        WhStock whStock = whStockService.queryWhStock(query);
        if (whStock == null) {
            log.error("queryWhStock result is null: " + sku);
            throw new RuntimeException(String.format("sku:%s未查到库存记录", sku));
        }
        /**
         * 减：已分配
         * 加：已拣库存
         * 加：可用
         */
        Integer allotQuantity = whStock.getAllotQuantity();
        Integer oldPickQuantity = whStock.getPickQuantity();
        Integer surplusQuantity = whStock.getSurplusQuantity();
        if ((allotQuantity - pickQuantity) < 0) {
            log.error("orderNo:" + taskNo + ",sku:" + whStock.getSku() + "已分配库存不能为负: allotQuantity[ " + allotQuantity + " ], pickQuantity[ " + pickQuantity + " ]");
            throw new RuntimeException(String.format("sku:%s已分配库存不能为负: 已分配库存[%s], 扣已拣[%s]", whStock.getSku(), allotQuantity, pickQuantity));
        }

        WhStock updateStock = WhStock.buildUpdateStock(whStock);
        updateStock.setAllotQuantity(allotQuantity - pickQuantity);
        updateStock.setPickQuantity(oldPickQuantity + pickQuantity);
        updateStock.setSurplusQuantity(surplusQuantity);

        List<WhStockLog> stockLogs = new ArrayList<>();

        WhStockLog stockLog = new WhStockLog(sku, StockLogType.ALLOCATED, StockLogStep.ASN_PICK,
                -pickQuantity, allotQuantity, taskNo);
        stockLogs.add(stockLog);

        if (pickQuantity > 0) {
            WhStockLog stockLog2 = new WhStockLog(sku, StockLogType.PICKED_STOCK, StockLogStep.ASN_PICK,
                    pickQuantity, oldPickQuantity, taskNo);
            stockLogs.add(stockLog2);
        }
        whStockService.updateWhStock(updateStock);
        whStockLogService.batchAddWhStockLog(stockLogs);
        return true;
    }

    /**
     * 装箱为0的SKU退已拣返架
     *
     * @param whAsn
     */
    @Override
    public void box(WhAsn whAsn) {
        String orderNo = whAsn.getReceivingCode();
        List<String> skus = whAsn.buildGroupItems().stream().map(item -> item.getProductSku()).collect(Collectors.toList());
        Map<String, WhStock> stockMap = whStockService.getWhStockMapBySkuList(skus);
        if (stockMap == null) {
            stockMap = new HashMap<String, WhStock>();
        }
        List<WhStock> stockList = new ArrayList<WhStock>();
        List<WhStockLog> stockLogs = new ArrayList<WhStockLog>();
        StockLogStep logStep = StockLogStep.ASN_BOX;
        for (WhAsnItem whAsnItem : whAsn.getGroupItems()) {
            String sku = whAsnItem.getProductSku();
            WhStock whStock = stockMap.get(sku);
            if (null == whStock) {
                throw new BusinessException("sku: " + sku + "库存记录为空");
            }
            /**
             * 加：已拣返架
             * 减：已拣
             */
            Integer quantity = whAsnItem.getPickQuantity() == null ? 0 : whAsnItem.getPickQuantity();
            if (quantity <= 0) {
                continue;
            }
            WhStock updateStock = WhStock.buildUpdateStock(stockMap.get(sku));
            Integer pickQuantity = whStock.getPickQuantity();
            Integer pickReturnQuantity = whStock.getPickReturnQuantity();
            if ((pickQuantity - quantity) < 0) {
                log.error("orderNo:" + orderNo + ",sku:" + whStock.getSku() + "已拣库存不能为负: pickQuantity[ " + pickQuantity + " ], quantity[ " + quantity + " ]");
                throw new RuntimeException(String.format("sku:%s已拣库存不能为负: 已拣库存[%s], 扣已拣[%s]", whStock.getSku(), pickQuantity, quantity));
            }
            updateStock.setPickQuantity(pickQuantity - quantity);
            updateStock.setPickReturnQuantity(pickReturnQuantity + quantity);
            stockList.add(updateStock);

            stockLogs.add(new WhStockLog(sku, StockLogType.PICKED_STOCK, logStep, -quantity, pickQuantity, orderNo));
            stockLogs.add(new WhStockLog(sku, StockLogType.PICKED_RETURN_STOCK, logStep, quantity, pickReturnQuantity, orderNo));

        }
        if (CollectionUtils.isNotEmpty(stockList)) {
            whStockService.batchUpdateWhStock(stockList);
        }
        if (CollectionUtils.isNotEmpty(stockLogs)) {
            whStockLogService.batchAddWhStockLog(stockLogs);
        }
    }

}
