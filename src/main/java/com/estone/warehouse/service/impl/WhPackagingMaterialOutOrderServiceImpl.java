package com.estone.warehouse.service.impl;

import com.estone.checkin.bean.MaterialCheckInItem;
import com.estone.common.util.CreateTaskNoUtils;
import com.estone.common.util.SystemLogUtils;
import com.estone.system.user.bean.SaleUser;
import com.estone.system.user.bean.SaleUserQueryCondition;
import com.estone.system.user.service.SaleUserService;
import com.estone.warehouse.aspect.MaterialStockLock;
import com.estone.warehouse.aspect.TransitStockLock;
import com.estone.warehouse.bean.*;
import com.estone.warehouse.dao.WhPackagingMaterialOutOrderDao;
import com.estone.warehouse.enums.*;
import com.estone.warehouse.service.WhInventoryDetailsService;
import com.estone.warehouse.service.WhPackagingMaterialInventoryService;
import com.estone.warehouse.service.WhPackagingMaterialOutOrderService;
import com.estone.warehouse.service.WhPackagingMaterialStockService;
import com.whq.tool.component.Pager;
import com.whq.tool.component.StatusCode;
import com.whq.tool.context.DataContextHolder;
import com.whq.tool.exception.BusinessException;
import com.whq.tool.exception.DBErrorCode;
import com.whq.tool.json.ResponseJson;
import com.whq.tool.sqler.SqlerException;

import java.sql.Timestamp;
import java.util.*;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.aop.framework.AopContext;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

@Service("whPackagingMaterialOutOrderService")
@Slf4j
public class WhPackagingMaterialOutOrderServiceImpl implements WhPackagingMaterialOutOrderService {
    @Resource
    private WhPackagingMaterialOutOrderDao whPackagingMaterialOutOrderDao;

    @Resource
    private WhPackagingMaterialStockService whPackagingMaterialStockService;

    @Resource
    private WhInventoryDetailsService whInventoryDetailsService;

    @Resource
    private SaleUserService saleUserService;

    @Override
    public WhPackagingMaterialOutOrder getWhPackagingMaterialOutOrder(Integer id) {
        WhPackagingMaterialOutOrder whPackagingMaterialOutOrder = whPackagingMaterialOutOrderDao.queryWhPackagingMaterialOutOrder(id);
        return whPackagingMaterialOutOrder;
    }

    @Override
    public WhPackagingMaterialOutOrder getWhPackagingMaterialOutOrderDetail(Integer id) {
        WhPackagingMaterialOutOrder whPackagingMaterialOutOrder = whPackagingMaterialOutOrderDao.queryWhPackagingMaterialOutOrder(id);
        // 关联查询
        return whPackagingMaterialOutOrder;
    }

    @Override
    public WhPackagingMaterialOutOrder queryWhPackagingMaterialOutOrder(WhPackagingMaterialOutOrderQueryCondition query) {
        Assert.notNull(query, "query is null!");
        WhPackagingMaterialOutOrder whPackagingMaterialOutOrder = whPackagingMaterialOutOrderDao.queryWhPackagingMaterialOutOrder(query);
        return whPackagingMaterialOutOrder;
    }

    @Override
    public List<WhPackagingMaterialOutOrder> queryAllWhPackagingMaterialOutOrders() {
        return whPackagingMaterialOutOrderDao.queryWhPackagingMaterialOutOrderList();
    }

    @Override
    public List<WhPackagingMaterialOutOrder> queryWhPackagingMaterialOutOrders(WhPackagingMaterialOutOrderQueryCondition query, Pager pager) {
        Assert.notNull(query, "query is null!");
        if (pager != null && pager.isQueryCount()) {
            int count = whPackagingMaterialOutOrderDao.queryWhPackagingMaterialOutOrderCount(query);
            pager.setTotalCount(count);
            if ( count == 0) {
                return new ArrayList<WhPackagingMaterialOutOrder>();
            }
        }
        List<WhPackagingMaterialOutOrder> whPackagingMaterialOutOrders = whPackagingMaterialOutOrderDao.queryWhPackagingMaterialOutOrderList(query, pager);
        return whPackagingMaterialOutOrders;
    }

    @Override
    public void createWhPackagingMaterialOutOrder(WhPackagingMaterialOutOrder whPackagingMaterialOutOrder) {
        try {
            whPackagingMaterialOutOrderDao.createWhPackagingMaterialOutOrder(whPackagingMaterialOutOrder);
        }
        catch (SqlerException e) {
            log.error(e.getMessage(), e);
            throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
        }
    }

    @Override
    public void batchCreateWhPackagingMaterialOutOrder(List<WhPackagingMaterialOutOrder> entityList) {
        if (CollectionUtils.isNotEmpty(entityList)) {
            try {
                whPackagingMaterialOutOrderDao.batchCreateWhPackagingMaterialOutOrder(entityList);
            }
            catch (SqlerException e) {
                log.error(e.getMessage(), e);
                throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
            }
        }
    }

    @Override
    public void deleteWhPackagingMaterialOutOrder(Integer id) {
        try {
            whPackagingMaterialOutOrderDao.deleteWhPackagingMaterialOutOrder(id);
        }
        catch (SqlerException e) {
            log.error(e.getMessage(), e);
            throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
        }
    }

    @Override
    public void updateWhPackagingMaterialOutOrder(WhPackagingMaterialOutOrder whPackagingMaterialOutOrder) {
        try {
            whPackagingMaterialOutOrderDao.updateWhPackagingMaterialOutOrder(whPackagingMaterialOutOrder);
        }
        catch (SqlerException e) {
            log.error(e.getMessage(), e);
            throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
        }
    }

    @Override
    public void batchUpdateWhPackagingMaterialOutOrder(List<WhPackagingMaterialOutOrder> entityList) {
        if (CollectionUtils.isNotEmpty(entityList)) {
            try {
                whPackagingMaterialOutOrderDao.batchUpdateWhPackagingMaterialOutOrder(entityList);
            }
            catch (SqlerException e) {
                log.error(e.getMessage(), e);
                throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
            }
        }
    }

    @Override
    @MaterialStockLock
    public void doCreateWhPackagingMaterialOutOrder(List<String> materialArticleNumberList,List<WhPackagingMaterialOutOrder> whPackagingMaterialOutOrders) throws Exception {
        WhPackagingMaterialOutOrderQueryCondition queryCondition=new WhPackagingMaterialOutOrderQueryCondition();
        queryCondition.setMaterialArticleNumberList(materialArticleNumberList);
        List<WhPackagingMaterialOutOrder> materialOutOrderList = whPackagingMaterialOutOrderDao.queryWhPackagingMaterialOutOrderList(queryCondition, null);
        boolean anyMatch = Optional.ofNullable(materialOutOrderList).orElse(new ArrayList<>()).stream().anyMatch(a -> MaterialOutOrderEnums.WAITING_CHECK.intCode().equals(a.getStatus()));
        if (anyMatch){
            throw new Exception(String.format("%s 耗材型号存在待审核数据！",materialArticleNumberList));
        }

        for (WhPackagingMaterialOutOrder packagingMaterialOutOrder : whPackagingMaterialOutOrders) {
            //创建出库单
            String hcTaskNo = CreateTaskNoUtils.createHCTaskNo();
            packagingMaterialOutOrder.setId(null);
            packagingMaterialOutOrder.setOrderNo(hcTaskNo);
            packagingMaterialOutOrder.setStatus(MaterialOutOrderEnums.WAITING_CHECK.intCode());
            whPackagingMaterialOutOrderDao.createWhPackagingMaterialOutOrder(packagingMaterialOutOrder);
            SystemLogUtils.WH_MATERIAL_OUT_ORDER.log(packagingMaterialOutOrder.getId(),"生成耗材出库单");
        }
    }

    @Override
    @MaterialStockLock
    public void doMaterialInventoryBatchAudit(List<String> materialArticleNumberList,List<WhPackagingMaterialOutOrder> materialOutOrderList) throws Exception {
        WhPackagingMaterialStockQueryCondition queryCondition=new WhPackagingMaterialStockQueryCondition();
        queryCondition.setMaterialArticleNumberStr(StringUtils.join(materialArticleNumberList,","));
        List<WhPackagingMaterialStock> whPackagingMaterialStockList = whPackagingMaterialStockService.queryWhPackagingMaterialStocks(queryCondition, null);
        if (CollectionUtils.isEmpty(whPackagingMaterialStockList)) {
            throw new Exception("耗材库存数据不存在！");
        }
        Map<String, WhPackagingMaterialStock> materialStockMap = whPackagingMaterialStockList.stream().collect(Collectors.toMap(WhPackagingMaterialStock::getMaterialArticleNumber, a -> a));
        List<WhPackagingMaterialStock> updateMaterialStock=new ArrayList<>();
        List<WhInventoryDetails> whInventoryDetailsList=new ArrayList<>();
        for (WhPackagingMaterialOutOrder packagingMaterialOutOrder : materialOutOrderList) {
            if (materialStockMap.get(packagingMaterialOutOrder.getMaterialArticleNumber())==null) {
                throw new Exception(String.format("[%s]耗材库存数据不存在！",packagingMaterialOutOrder.getMaterialArticleNumber()));
            }
            WhPackagingMaterialStock whPackagingMaterialStock = materialStockMap.get(packagingMaterialOutOrder.getMaterialArticleNumber());
            //可用库存
            Integer surplusQuantity = Optional.ofNullable(whPackagingMaterialStock.getSurplusQuantity()).orElse(0);
            //出库数量
            Integer quantity = Optional.ofNullable(packagingMaterialOutOrder.getQuantity()).orElse(0);
            if(surplusQuantity<quantity) {
                throw new Exception(String.format("耗材型号%s,可用库存%s,出库数量%s,可用库存不足！", whPackagingMaterialStock.getMaterialArticleNumber(), surplusQuantity, quantity));
            }
            whPackagingMaterialStock.setSurplusQuantity(surplusQuantity-quantity);
            packagingMaterialOutOrder.setStatus(MaterialOutOrderEnums.COMPLETED.intCode());
            updateMaterialStock.add(whPackagingMaterialStock);

            WhInventoryDetails whInventoryDetails = new WhInventoryDetails();
            whInventoryDetails.setOrderNo(packagingMaterialOutOrder.getOrderNo());
            whInventoryDetails.setName(whPackagingMaterialStock.getName());
            whInventoryDetails.setArticleNumber(packagingMaterialOutOrder.getMaterialArticleNumber());
            whInventoryDetails.setOrderType(MaterialOrderType.ORDER.intCode());
            whInventoryDetails.setStockType(MaterialStockType.NORMAL_OUT.intCode());
            whInventoryDetails.setChangeStock(-quantity);
            whInventoryDetails.setInitialStock(surplusQuantity);
            whInventoryDetails.setClosingStock(whInventoryDetails.getInitialStock()+whInventoryDetails.getChangeStock());
            whInventoryDetailsList.add(whInventoryDetails);
        }

        if (CollectionUtils.isNotEmpty(whInventoryDetailsList)) {
            whInventoryDetailsService.batchCreateWhInventoryDetails(whInventoryDetailsList);
        }

        if (CollectionUtils.isNotEmpty(updateMaterialStock)) {
            whPackagingMaterialStockService.batchUpdateWhPackagingMaterialStock(updateMaterialStock);
        }

        whPackagingMaterialOutOrderDao.batchUpdateWhPackagingMaterialOutOrder(materialOutOrderList);


        materialOutOrderList.forEach(outOrder -> SystemLogUtils.WH_MATERIAL_OUT_ORDER.log(outOrder.getId(), "耗材出库单审批-状态变更",
                new String[][] { { "历史状态", MaterialOutOrderEnums.WAITING_CHECK.getName() },
                        { "更改状态", MaterialOutOrderEnums.COMPLETED.getName()}}));

    }

    @Override
    public Map<String,Integer> countMaterialArticleDosage(List<String> materialArticleNumberList) {
        List<WhPackagingMaterialOutOrder> whPackagingMaterialOutOrders = whPackagingMaterialOutOrderDao.countMaterialArticleDosage(materialArticleNumberList);
        return whPackagingMaterialOutOrders.stream().collect(Collectors.toMap(WhPackagingMaterialOutOrder::getMaterialArticleNumber,WhPackagingMaterialOutOrder::getQuantity));
    }

    @Override
    public ResponseJson packagingMaterialOutOrderList(List<WhPackagingMaterialOutOrder> outOrderList) throws Exception {
        ResponseJson responseJson = new ResponseJson();
        responseJson.setStatus(StatusCode.FAIL);
        boolean allMatch = outOrderList.stream().allMatch(outOrder -> StringUtils.isNotBlank(outOrder.getMaterialArticleNumber()));
        if (!allMatch){
            responseJson.setMessage("存在为空的耗材型号！");
            return responseJson;
        }
        StringBuffer errorMessage = new StringBuffer();
        outOrderList.stream().filter(outOrder ->Optional.ofNullable(outOrder.getQuantity()).orElse(0)<0).forEach(outOrder -> {
            errorMessage.append(String.format("耗材型号%s,出库数量不能小于0！", outOrder.getMaterialArticleNumber()));
        });
        if (errorMessage.length()>0) {
            responseJson.setMessage(errorMessage.toString());
            return responseJson;
        }

        List<String> nameList = outOrderList.stream().map(WhPackagingMaterialOutOrder::getRemark).filter(StringUtils::isNotBlank).collect(Collectors.toList());
        Map<String, Integer> map=new HashMap<>();
        if (CollectionUtils.isNotEmpty(nameList)) {
            SaleUserQueryCondition queryCondition = new SaleUserQueryCondition();
            queryCondition.setUserNameList(nameList);
            List<SaleUser> saleUsers = saleUserService.querySaleUsers(queryCondition, null);
            map=Optional.ofNullable(saleUsers).orElse(new ArrayList<>()).stream().collect(Collectors.toMap(SaleUser::getName, SaleUser::getUserId, (v1, v2) -> v1));
           
        }
        List<String> materialArticleNumberList = outOrderList.stream().map(WhPackagingMaterialOutOrder::getMaterialArticleNumber).filter(StringUtils::isNotBlank).collect(Collectors.toList());
        Map<String, Integer> quantityMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(materialArticleNumberList)) {
            WhPackagingMaterialStockQueryCondition stockQueryCondition=new WhPackagingMaterialStockQueryCondition();
            stockQueryCondition.setMaterialArticleNumberStr(StringUtils.join(materialArticleNumberList,","));
            List<WhPackagingMaterialStock> whPackagingMaterialStocks = whPackagingMaterialStockService.queryWhPackagingMaterialStocks(stockQueryCondition, null);
            quantityMap =Optional.ofNullable(whPackagingMaterialStocks).orElse(new ArrayList<>()).stream().collect(Collectors.toMap(WhPackagingMaterialStock::getMaterialArticleNumber, WhPackagingMaterialStock::getSurplusQuantity, (v1, v2) -> v1));
        }
        outOrderList = new ArrayList<>(outOrderList.stream().collect(Collectors.toMap(WhPackagingMaterialOutOrder::getMaterialArticleNumber, a -> a, (o1, o2) -> {
            o1.setQuantity(Optional.ofNullable(o1.getQuantity()).orElse(0) + Optional.ofNullable(o2.getQuantity()).orElse(0));
            o1.setRemark(Optional.ofNullable(o1.getRemark()).orElse(o2.getRemark()));
            o1.setReceiveBy(Optional.ofNullable(o1.getReceiveBy()).orElse(o2.getReceiveBy()));
            return o1;
        })).values());
        StringBuilder userError = new StringBuilder();
        StringBuilder stockError = new StringBuilder();
        StringBuilder error = new StringBuilder();
        for (WhPackagingMaterialOutOrder whPackagingMaterialOutOrder : outOrderList) {
            Integer createBy = map.get(whPackagingMaterialOutOrder.getRemark());
            if (createBy == null && StringUtils.isNotBlank(whPackagingMaterialOutOrder.getRemark())) {
                userError.append(whPackagingMaterialOutOrder.getRemark());
                userError.append(",");
                continue;
            }
            if (createBy != null) {
                whPackagingMaterialOutOrder.setReceiveBy(createBy);
            }

            Integer quantity = Optional.ofNullable(whPackagingMaterialOutOrder.getQuantity()).orElse(0);
            Integer stockQuantity = quantityMap.get(whPackagingMaterialOutOrder.getMaterialArticleNumber());

            if (stockQuantity == null){
                error.append(whPackagingMaterialOutOrder.getMaterialArticleNumber());
                error.append(",");
                continue;
            }
            if (stockQuantity < quantity) {
                stockError.append(whPackagingMaterialOutOrder.getMaterialArticleNumber());
                stockError.append(",");
                continue;
            }
            whPackagingMaterialOutOrder.setRemark(null);
        }
        if (userError.length()>0) {
            errorMessage.append(userError);
            errorMessage.append("用户不存在！");
            errorMessage.append("</br>");
        }
        if (stockError.length()>0) {
            errorMessage.append(stockError);
            errorMessage.append("耗材库存不足！");
            errorMessage.append("</br>");
        }
        if (error.length()>0) {
            errorMessage.append(error);
            errorMessage.append("耗材库存记录不存在！");
        }

        if (errorMessage.length()>0) {
            responseJson.setMessage(errorMessage.toString());
            return responseJson;
        }
        ((WhPackagingMaterialOutOrderService)AopContext.currentProxy()).doCreateWhPackagingMaterialOutOrder(materialArticleNumberList,outOrderList);
        responseJson.setStatus(StatusCode.SUCCESS);
        return responseJson;
    }
}