package com.estone.warehouse.service.impl;

import com.estone.warehouse.bean.WhCount;
import com.estone.warehouse.bean.WhCountQueryCondition;
import com.estone.warehouse.dao.WhCountDao;
import com.estone.warehouse.service.WhCountService;
import com.estone.warehouse.util.WhCountUtils;
import com.whq.tool.component.Pager;
import com.whq.tool.exception.BusinessException;
import com.whq.tool.exception.DBErrorCode;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import com.whq.tool.sqler.SqlerException;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;

@Service("whCountService")
public class WhCountServiceImpl implements WhCountService {
    private static final Logger logger = LoggerFactory.getLogger(WhCountServiceImpl.class);

    @Resource
    private WhCountDao whCountDao;

    /**
     * This method corresponds to the database table wh_count
     *
     * @mbggenerated Thu Apr 04 11:38:01 CST 2019
     */
    public WhCount getWhCount(Integer id) {
        WhCount whCount = whCountDao.queryWhCount(id);
        return whCount;
    }

    /**
     * This method corresponds to the database table wh_count
     *
     * @mbggenerated Thu Apr 04 11:38:01 CST 2019
     */
    public WhCount getWhCountDetail(Integer id) {
        WhCount whCount = whCountDao.queryWhCount(id);
        // 关联查询
        return whCount;
    }

    /**
     * This method corresponds to the database table wh_count
     *
     * @mbggenerated Thu Apr 04 11:38:01 CST 2019
     */
    public WhCount queryWhCount(WhCountQueryCondition query) {
        Assert.notNull(query);
        WhCount whCount = whCountDao.queryWhCount(query);
        return whCount;
    }

    /**
     * This method corresponds to the database table wh_count
     *
     * @mbggenerated Thu Apr 04 11:38:01 CST 2019
     */
    public List<WhCount> queryAllWhCounts() {
        return whCountDao.queryWhCountList();
    }

    /**
     * This method corresponds to the database table wh_count
     *
     * @mbggenerated Thu Apr 04 11:38:01 CST 2019
     */
    public List<WhCount> queryWhCounts(WhCountQueryCondition query, Pager pager) {
        Assert.notNull(query);
        if (pager != null && pager.isQueryCount()) {
            int count = whCountDao.queryWhCountCount(query);
            pager.setTotalCount(count);
            if (count == 0) {
                return new ArrayList<WhCount>();
            }
        }
        List<WhCount> whCounts = whCountDao.queryWhCountList(query, pager);
        return whCounts;
    }

    /**
     * This method corresponds to the database table wh_count
     *
     * @mbggenerated Thu Apr 04 11:38:01 CST 2019
     */
    public void createWhCount(WhCount whCount) {
        try {
            whCountDao.createWhCount(whCount);
        }
        catch (SqlerException e) {
            logger.error(e.getMessage(), e);
            throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
        }
    }

    /**
     * This method corresponds to the database table wh_count
     *
     * @mbggenerated Thu Apr 04 11:38:01 CST 2019
     */
    public void batchCreateWhCount(List<WhCount> entityList) {
        if (CollectionUtils.isNotEmpty(entityList)) {
            try {
                whCountDao.batchCreateWhCount(entityList);
            }
            catch (SqlerException e) {
                logger.error(e.getMessage(), e);
                throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
            }
        }
    }

    /**
     * This method corresponds to the database table wh_count
     *
     * @mbggenerated Thu Apr 04 11:38:01 CST 2019
     */
    public void deleteWhCount(Integer id) {
        try {
            whCountDao.deleteWhCount(id);
        }
        catch (SqlerException e) {
            logger.error(e.getMessage(), e);
            throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
        }
    }

    /**
     * This method corresponds to the database table wh_count
     *
     * @mbggenerated Thu Apr 04 11:38:01 CST 2019
     */
    public void updateWhCount(WhCount whCount) {
        try {
            whCountDao.updateWhCount(whCount);
        }
        catch (SqlerException e) {
            logger.error(e.getMessage(), e);
            throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
        }
    }

    /**
     * This method corresponds to the database table wh_count
     *
     * @mbggenerated Thu Apr 04 11:38:01 CST 2019
     */
    public void batchUpdateWhCount(List<WhCount> entityList) {
        if (CollectionUtils.isNotEmpty(entityList)) {
            try {
                whCountDao.batchUpdateWhCount(entityList);
            }
            catch (SqlerException e) {
                logger.error(e.getMessage(), e);
                throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
            }
        }
    }

    @Override
    public List<Map<String, Object>> queryWHOSCountList(WhCountQueryCondition query, Pager pager) {
        if (pager != null && pager.isQueryCount()) {
            int count = whCountDao.queryWhCountCount(query);
            pager.setTotalCount(count);
            if (count == 0) {
                return new ArrayList<Map<String, Object>>();
            }
        }
        List<Map<String, Object>> whosCountList = whCountDao.queryWHOSCountList(query, pager);
        WhCountUtils.caculateDays(whosCountList, query);

        Map<String, Object> countMap = new HashMap<>();
        Map<String, Integer> avgCountMap = new HashMap<>();
        Map<String, Object> averageMap = new HashMap<>();
        // 统计数据
        whosCountList.stream().forEach(a -> a.keySet().forEach(b -> {
            if (b.equals("groupDay")) {
                countMap.put(b, "汇总");
            }
            else {
                BigDecimal b1 = new BigDecimal(countMap.get(b) == null ? "0" : countMap.get(b).toString());
                BigDecimal b2 = new BigDecimal(a.get(b) == null ? "0" : a.get(b).toString());
                Object countMapValue = b1.add(b2);

                if (b.equals("inventoryLoss")) {
                    countMapValue = -(-Integer.parseInt(countMap.get(b) == null ? "0" : countMap.get(b).toString())
                            - Integer.parseInt(a.get(b) == null ? "0" : a.get(b).toString()));
                }
                if (b.equals("inventoryLossAmount")) {
                    countMapValue = b1.subtract(b2.abs());
                }
                countMap.put(b, countMapValue);
            }
        }));

        whosCountList.add(countMap);
        if (whosCountList.size() > 1) {
            // 计算百分比
            whosCountList.forEach(map -> {
                map.put("loadedRate", getRateBy(Integer.parseInt(map.get("packedAndLoadedOrder").toString()),
                        Integer.parseInt(map.get("packedOrder").toString())));
                map.put("shipedRateBy24h", getRateBy(Integer.parseInt(map.get("shipedBy24h").toString()),
                        Integer.parseInt(map.get("orderShiped").toString())));
                map.put("shipedRateBy36h", getRateBy(Integer.parseInt(map.get("shipedBy36h").toString()),
                        Integer.parseInt(map.get("orderShiped").toString())));
                map.put("loadedRateBy24h", getRateBy(Integer.parseInt(map.get("loadedBy24h").toString()),
                        Integer.parseInt(map.get("loadedOrder").toString())));
                map.put("loadedRateBy36h", getRateBy(Integer.parseInt(map.get("loadedBy36h").toString()),
                        Integer.parseInt(map.get("loadedOrder").toString())));
                map.put("upLocationRateBy24h", getRateBy(Integer.parseInt(map.get("upLocationBy24h").toString()),
                        Integer.parseInt(map.get("upQuantity").toString())));
                map.put("upLocationRateBy48h", getRateBy(Integer.parseInt(map.get("upLocationBy48h").toString()),
                        Integer.parseInt(map.get("upQuantity").toString())));
                map.put("onceMergeDeliverAndLoadingOrderRate",
                        getRateBy(Integer.parseInt(map.get("onceMergeDeliverAndLoadingOrderCount").toString()),
                                Integer.parseInt(map.get("orderShiped").toString())));
                map.put("onceAndTwiceMergeDeliverAndLoadingOrderRate",
                        getRateBy(Integer.parseInt(map.get("onceAndTwiceMergeDeliverAndLoadingOrderCount").toString()),
                                Integer.parseInt(map.get("orderShiped").toString())));
                map.put("ngSkuRate",
                        getNgRateBy(Integer.parseInt(map.get("ngSkuCount").toString()),
                                Integer.parseInt(map.get("exceptionSkuCount").toString()),
                                Integer.parseInt(map.get("skuCount").toString())));
                map.put("ngPcsRate",
                        getNgRateBy(Integer.parseInt(map.get("ngQuantitySum").toString()),
                                Integer.parseInt(map.get("exceptionQuantitySum").toString()),
                                Integer.parseInt(map.get("pcsCount").toString())));
                map.put("averagePurchaseOrder",
                        getRateBy(Integer.parseInt(map.get("recordOrderQuantitySum").toString()),
                                Integer.parseInt(map.get("recordOrderCount").toString())));
                map.put("averageSku", getRateBy(Integer.parseInt(map.get("pcsCount").toString()),
                        Integer.parseInt(map.get("skuCount").toString())));
                map.put("averageShipedOrder", getRateBy(Integer.parseInt(map.get("skuShiped").toString()),
                        Integer.parseInt(map.get("orderShiped").toString())));
                if (query.getDateType() == 3) {
                    map.put("monthInventorySkuRate",
                            getRateBy(Integer.parseInt(map.get("monthInventorySku").toString()),
                                    Integer.parseInt(map.get("inventorySkuCount").toString())));
                    map.put("monthInventoryPcsRate",
                            getRateBy(Integer.parseInt(map.get("monthInventoryPcs").toString()),
                                    Integer.parseInt(map.get("inventoryPcsCount").toString())));
                    map.put("inventoryAmount", Double.parseDouble(map.get("inventoryProfitAmount").toString())
                            + Double.parseDouble(map.get("inventoryLossAmount").toString()));
                    map.put("inventoryAmountRate",
                            getAmountRate(Double.parseDouble(map.get("inventoryProfitAmount").toString()),
                                    Double.parseDouble(map.get("inventoryLossAmount").toString()),
                                    Double.parseDouble(map.get("amount").toString())));
                    map.put("inventoryProfitAmountRate",
                            getAmountRate(Double.parseDouble(map.get("inventoryProfitAmount").toString()), null,
                                    Double.parseDouble(map.get("amount").toString())));
                    map.put("inventoryLossAmountRate",
                            getAmountRate(null, Double.parseDouble(map.get("inventoryLossAmount").toString()),
                                    Double.parseDouble(map.get("amount").toString())));
                }

            });
            List<String> countKeys = Arrays.asList("surplusQuantity", "allotQuantity",
                    "haveAvailableInventorySkuQuantity", "availableStockStatis", "transferStockSum", "transferSkuCount",
                    "totalStockQty", "totalSurplusQty", "totalSkuCount", "days");
            // 计算平均值
            int i = whosCountList.size();
            for (Map<String, Object> map : whosCountList) {
                i--;
                final int j = i;
                map.keySet().forEach(key -> {
                    if (key.equals("groupDay")) {
                        averageMap.put(key, "平均值");
                    }else if (countKeys.contains(key)){
                        averageMap.put(key, map.get(key));
                    }
                    else {
                        Integer count = avgCountMap.get(key) == null ? 0
                                : Integer.parseInt(avgCountMap.get(key).toString());
                        BigDecimal bv = new BigDecimal(map.get(key).toString());
                        if (bv.compareTo(BigDecimal.ZERO) != 0) {
                            count++;
                            avgCountMap.put(key, count);
                            if (j == 0 && !key.contains("Rate") && !key.contains("average") && count - 1 > 0) {
                                averageMap.put(key, getAmountRate(0.0, Double.valueOf(map.get(key).toString()),
                                        Double.valueOf(String.valueOf(count - 1))));
                            }
                        }
                    }
                });
            }
            // 交换位置，把平均值放在汇总前面，便于前端展示
            whosCountList.add(whosCountList.size() - 1, averageMap);
        }
        return whosCountList;
    }

    public Double getRateBy(Integer oderByHours, Integer order) {
        Double oderByHoursRate = 0.0;
        if (oderByHours >= 0 && order > 0) {
            if (order > 0) {
                Double shipedBy24hDouble = Double.valueOf(oderByHours);
                Double orderPushedDouble = Double.valueOf(order);
                oderByHoursRate = (double) Math.round((shipedBy24hDouble / orderPushedDouble) * 10000) / 10000.0;// 保留两位小数
            }
        }
        return oderByHoursRate;
    }

    public Double getNgRateBy(Integer pNgOrder, Integer exceptionOrder, Integer pOrder) {
        Double ngRate = 0.0;
        if (pNgOrder >= 0 && exceptionOrder >= 0 && pOrder > 0) {
            Integer ngAndExceptionOrder = pNgOrder + exceptionOrder;
            Double ngOrder = Double.valueOf(ngAndExceptionOrder);
            Double order = Double.valueOf(String.valueOf(pOrder + ngAndExceptionOrder));
            ngRate = (double) Math.round((ngOrder / order) * 10000) / 10000.0;// 保留两位小数
        }
        return ngRate;
    }

    public Double getAmountRate(Double profitAmount, Double lossAmount, Double amount) {
        Double amountRate = 0.0;
        if (profitAmount == null) {
            profitAmount = 0.0;
        }
        if (lossAmount == null) {
            lossAmount = 0.0;
        }
        if (amount > 0) {
            amountRate = (double) Math.round(((profitAmount + lossAmount) / amount) * 10000) / 10000.0;// 保留两位小数
        }
        return amountRate;
    }

    @Override
    public Integer getPcsByTime(Date startTime, Date endTime) {
        return whCountDao.queryPcsByTime(startTime, endTime);
    }
}