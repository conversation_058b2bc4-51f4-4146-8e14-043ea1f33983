package com.estone.warehouse.service.impl;

import static java.util.stream.Collectors.toList;

import java.sql.Timestamp;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import com.estone.android.domain.AndroidProductDo;
import com.estone.apv.bean.ApvTrack;
import com.estone.apv.bean.ApvTrackQueryCondition;
import com.estone.apv.bean.WhApv;
import com.estone.apv.bean.WhApvQueryCondition;
import com.estone.apv.common.ApvOrderBy;
import com.estone.apv.common.ApvStatus;
import com.estone.apv.service.ApvTrackService;
import com.estone.apv.service.WhApvLockService;
import com.estone.apv.service.WhApvService;
import com.estone.checkin.enums.CheckInWhType;
import com.estone.common.util.*;
import com.estone.common.util.model.ApiResult;
import com.estone.pac.enums.PacOrderUploadStatus;
import com.estone.pac.utils.PacSendUtil;
import com.estone.sku.bean.WhSku;
import com.estone.sku.bean.WhSkuExtend;
import com.estone.sku.bean.WhSkuExtendQueryCondition;
import com.estone.sku.bean.WhSkuQueryCondition;
import com.estone.sku.service.WhSkuExtendService;
import com.estone.sku.service.WhSkuService;
import com.estone.system.param.bean.SystemParam;
import com.estone.warehouse.aspect.StockServicelock;
import com.estone.warehouse.bean.*;
import com.estone.warehouse.dao.WhReturnDao;
import com.estone.warehouse.enums.*;
import com.estone.warehouse.service.*;
import com.whq.tool.component.Pager;
import com.whq.tool.component.StatusCode;
import com.whq.tool.context.DataContextHolder;
import com.whq.tool.exception.BusinessException;
import com.whq.tool.exception.DBErrorCode;
import com.whq.tool.json.ResponseJson;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import com.whq.tool.sqler.SqlerException;

@Service("whReturnService")
public class WhReturnServiceImpl implements WhReturnService {
    private static final Logger logger = LoggerFactory.getLogger(WhReturnServiceImpl.class);

    @Resource
    private WhReturnDao whReturnDao;

    @Resource
    private WhBoxService whBoxService;

    @Resource
    private WhReturnItemService whReturnItemService;

    @Resource
    private WhApvLockService whApvLockService;

    @Resource
    private ApvTrackService apvTrackService;

    @Resource
    private WhApvService whApvService;

    @Resource
    private WhSkuService whSkuService;

    @Resource
    private ReturnUpdateStockService returnUpdateStockService;

    @Resource
    private AllocationReturnUpdateStockService allocationReturnUpdateStockService;

    @Resource
    private AllocateReturnOrderService allocateReturnOrderService;

    @Resource
    private WhBatchReturnService whBatchReturnService;

    @Resource
    private WhBatchReturnSkuService whBatchReturnSkuService;

    @Resource
    private WhBatchReturnUuidService whBatchReturnUuidService;

    @Resource
    private WhStockService whStockService;

    @Resource
    private FrozenStockService frozenStockService;

    @Resource
    private WhPickInventoryDemandService whPickInventoryDemandService;

    @Resource
    private WhReturnExceptionItemService whReturnExceptionItemService;

    @Resource
    private WhSkuExtendService whSkuExtendService;

    /**
     * This method corresponds to the database table wh_return
     *
     * @mbggenerated Wed Oct 10 16:54:45 CST 2018
     */
    public WhReturn getWhReturn(Integer id) {
        WhReturn whReturn = whReturnDao.queryWhReturn(id);
        return whReturn;
    }

    public WhReturn completeWhReturn(Integer id, boolean error) {
        WhReturn whReturn = getWhReturn(id);
        if (Objects.isNull(whReturn)) {
            return whReturn;
        }

        Timestamp completeDate = new Timestamp(System.currentTimeMillis());
        WhReturn item = new WhReturn();
        item.setId(id);
        item.setCompleteDate(completeDate);
        if (error) {
            item.setStatus(ReturnStatus.ERROR.intCode());
            SystemLogUtils.RETURNLOG.log(id, "异常完成");
        } else {
            item.setStatus(ReturnStatus.COMPLETE.intCode());
            SystemLogUtils.RETURNLOG.log(id, "返架完成");
        }
        if (updateWhReturn(item) > 0) {
            whBoxService.updateWhBoxOfUnbinding(whReturn.getBoxNo(),
                    new String[][]{{"返架任务ID", id.toString()}});
        }
        recordCompleteDate(whReturn, completeDate);
        return whReturn;
    }

    /**
     * 用于记录返架单中sku的返架完成时间
     *
     * @param whReturn     返架单
     * @param completeDate 返架完成时间
     */
    private void recordCompleteDate(WhReturn whReturn, Timestamp completeDate) {
        if (Objects.isNull(whReturn)) {
            return;
        }

        WhReturnItemQueryCondition queryCondition = new WhReturnItemQueryCondition();
        queryCondition.setReturnId(whReturn.getId());
        List<WhReturnItem> whReturnItems = whReturnItemService.queryWhReturnItems(queryCondition, null);
        List<String> skus = Optional.ofNullable(whReturnItems)
                .orElse(new ArrayList<>())
                .stream()
                .map(WhReturnItem::getSku)
                .distinct()
                .collect(Collectors.toList());

        this.recordCompleteDate(skus, completeDate);
    }

    /**
     * 用于记录sku返架完成时间
     *
     * @param skus         sku列表
     * @param completeDate 返架完成时间
     */
    private void recordCompleteDate(List<String> skus, Timestamp completeDate) {
        if (CollectionUtils.isEmpty(skus)) {
            return;
        }
        WhSkuExtendQueryCondition whSkuExtendQueryCondition = new WhSkuExtendQueryCondition();
        whSkuExtendQueryCondition.setSkuList(skus);
        List<WhSkuExtend> whSkuExtends = whSkuExtendService.queryWhSkuExtends(whSkuExtendQueryCondition, null);
        Map<String, WhSkuExtend> whSkuExtendMap = Optional.ofNullable(whSkuExtends)
                .orElse(new ArrayList<>())
                .stream()
                .collect(Collectors.toMap(WhSkuExtend::getSku, Function.identity()));

        List<WhSkuExtend> updateList = new ArrayList<>();
        List<WhSkuExtend> insertList = new ArrayList<>();
        for (String sku : skus) {
            WhSkuExtend whSkuExtend = whSkuExtendMap.get(sku);
            if (Objects.isNull(whSkuExtend)) {
                whSkuExtend = new WhSkuExtend();
                whSkuExtend.setSku(sku);
                insertList.add(whSkuExtend);
            } else {
                updateList.add(whSkuExtend);
            }
            whSkuExtend.setLastReturnCompleteDate(completeDate);
        }
        whSkuExtendService.batchUpdateWhSkuExtend(updateList);
        whSkuExtendService.batchCreateWhSkuExtend(insertList);
    }

    @Override
    @StockServicelock
    public ResponseJson batchCompleteExceptionWhReturn(List<String> skuList, WhReturnItem whReturnItem, Integer type) {
        ResponseJson response = new ResponseJson(StatusCode.FAIL);
        if (whReturnItem == null) {
            response.setMessage("参数为空！");
            return response;
        }

        if (!returnUpdateStockService.updateStockByReturnUp(whReturnItem, type, true)) {
            throw new RuntimeException(whReturnItem.getReturnId() + "");
        }
        this.completeWhReturn(whReturnItem.getReturnId(), true);

        response.setStatus(StatusCode.SUCCESS);
        return response;
    }

    /**
     * 删除主表和明细表数据
     */
    public void deleteWhReturnAndItems(Integer id) {
        WhReturn whReturn = getWhReturn(id);
        if (whReturn != null) {
            if (whReturn.getStatus() != null && whReturn.getStatus().equals(WhReturnStatus.WAITING_ALLOT.intCode())) {
                deleteWhReturn(id);
                whReturnItemService.deleteWhReturnItemByWhReturnId(id);
            }
        }
    }

    /**
     * This method corresponds to the database table wh_return
     *
     * @mbggenerated Wed Oct 10 16:54:45 CST 2018
     */
    public WhReturn getWhReturnDetail(Integer id) {
        WhReturn whReturn = whReturnDao.queryWhReturn(id);
        // 关联查询
        return whReturn;
    }

    /**
     * This method corresponds to the database table wh_return
     *
     * @mbggenerated Wed Oct 10 16:54:45 CST 2018
     */
    public WhReturn queryWhReturn(WhReturnQueryCondition query) {
        Assert.notNull(query);
        WhReturn whReturn = whReturnDao.queryWhReturn(query);
        return whReturn;
    }

    /**
     * This method corresponds to the database table wh_return
     *
     * @mbggenerated Wed Oct 10 16:54:45 CST 2018
     */
    public List<WhReturn> queryAllWhReturns() {
        return whReturnDao.queryWhReturnList();
    }

    /**
     * This method corresponds to the database table wh_return
     *
     * @mbggenerated Wed Oct 10 16:54:45 CST 2018
     */
    public List<WhReturn> queryWhReturns(WhReturnQueryCondition query, Pager pager) {
        Assert.notNull(query);
        if (pager != null && pager.isQueryCount()) {
            int count = whReturnDao.queryWhReturnCount(query);
            pager.setTotalCount(count);
            if (count == 0) {
                return new ArrayList<WhReturn>();
            }
        }
        List<WhReturn> whReturns = whReturnDao.queryWhReturnList(query, pager);
        return whReturns;
    }

    /**
     * This method corresponds to the database table wh_return
     *
     * @mbggenerated Wed Oct 10 16:54:45 CST 2018
     */
    public void createWhReturn(WhReturn whReturn) {
        try {
            whReturn.setStatus(ReturnStatus.WAIT.intCode());
            whReturnDao.createWhReturn(whReturn);
            String localip = "************";
            Environment env = SpringUtils.getBean(Environment.class);
            if(!StringUtils.contains(env.getProperty("spring.profiles.active"),"prod")){
                localip = "************";
//                localip = "localhost:8181";
            }
            String content = "<a target='_blank' href='http://"+localip+"/wms/warehouse/returns/returnItems?id="
                    + whReturn.getId() + "'>" + whReturn.getReturnNo() + "</a>";
            whBoxService.updateWhBoxOfBindingPre(whReturn.getBoxNo(), whReturn.getId().toString(),
                    new String[][]{{"返架编号", content}});
        } catch (SqlerException e) {
            logger.error(e.getMessage(), e);
            throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
        }
    }

    /**
     * This method corresponds to the database table wh_return
     *
     * @mbggenerated Wed Oct 10 16:54:45 CST 2018
     */
    public void batchCreateWhReturn(List<WhReturn> entityList) {
        if (CollectionUtils.isNotEmpty(entityList)) {
            try {
                whReturnDao.batchCreateWhReturn(entityList);
            } catch (SqlerException e) {
                logger.error(e.getMessage(), e);
                throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
            }
        }
    }

    /**
     * This method corresponds to the database table wh_return
     *
     * @mbggenerated Wed Oct 10 16:54:45 CST 2018
     */
    public void deleteWhReturn(Integer id) {
        try {
            whReturnDao.deleteWhReturn(id);
        } catch (SqlerException e) {
            logger.error(e.getMessage(), e);
            throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
        }
    }

    /**
     * This method corresponds to the database table wh_return
     *
     * @mbggenerated Wed Oct 10 16:54:45 CST 2018
     */
    public int updateWhReturn(WhReturn whReturn) {
        try {
            return whReturnDao.updateWhReturn(whReturn);
        } catch (SqlerException e) {
            logger.error(e.getMessage(), e);
            throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
        }
    }

    /**
     * This method corresponds to the database table wh_return
     *
     * @mbggenerated Wed Oct 10 16:54:45 CST 2018
     */
    public void batchUpdateWhReturn(List<WhReturn> entityList) {
        if (CollectionUtils.isNotEmpty(entityList)) {
            try {
                whReturnDao.batchUpdateWhReturn(entityList);
            } catch (SqlerException e) {
                logger.error(e.getMessage(), e);
                throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
            }
        }
    }

    @Override
    public List<WhReturn> queryWhReturnListAndItems(WhReturnQueryCondition query, Pager pager) {
        Assert.notNull(query);
        if (pager != null && pager.isQueryCount()) {
            int count = whReturnDao.queryWhReturnListAndItemCount(query);
            pager.setTotalCount(count);
            if (count == 0) {
                return new ArrayList<WhReturn>();
            }
        }
        List<WhReturn> whReturns = whReturnDao.queryWhReturnListAndItems(query, pager);
        return whReturns;
    }

    @Override
    @StockServicelock
    public WhApvAndWhSku updateApvSkuDetail(List<String> skuList, String exportType) {
        WhApvAndWhSku whApvAndWhSku = new WhApvAndWhSku();
        String message = null;
        if (CollectionUtils.isNotEmpty(skuList)) {
            String sku = UniqueSkuUtil.getSku(skuList.get(0))==null?null: UniqueSkuUtil.getSku(skuList.get(0)).toUpperCase();

            if (exportType == null || exportType.equals("1")) {
                message = "默认进入库内返架！";
                return queryWhSku(sku, message);
            } else {
                whApvAndWhSku = doMatchApv(sku);
            }
        }

        return whApvAndWhSku;
    }

    private WhApvAndWhSku queryWhSku(String sku, String message) {
        WhApvAndWhSku whApvAndWhSku = new WhApvAndWhSku();
        // 查找不到订单 进到库内返架
        WhSkuQueryCondition whSkuQueryCondition = new WhSkuQueryCondition();
        whSkuQueryCondition.setSku(sku);
        WhSku whsku = whSkuService.queryWhSku(whSkuQueryCondition);
        if (null == whsku) {
            return null;
        } else {
            whApvAndWhSku.setWhSku(whsku);
            whApvAndWhSku.setSku(sku);
            whApvAndWhSku.setType(2);
            whApvAndWhSku.setMessage(message);
        }
        return whApvAndWhSku;
    }

    @Override
    @StockServicelock
    public ResponseJson scanBoxNoUpdateReturnStatusAndUpQuantity(List<String> skuList, WhReturn whReturn,
                                                                 List<WhReturnItem> whReturnItems) {
        ResponseJson response = new ResponseJson(StatusCode.FAIL);

        // 待返架更新状态到返架中
        if (whReturn.getStatus().equals(ReturnStatus.WAIT.intCode())) {
            whReturn.setStatus(ReturnStatus.ING.intCode());
            whReturn.setReturnUser(DataContextHolder.getUserId());
            whReturn.setReceiveDate(new Timestamp(System.currentTimeMillis()));
            this.updateWhReturn(whReturn);
            SystemLogUtils.RETURNLOG.log(whReturn.getId(), "扫描周转框");
            if (Integer.valueOf(ReturnType.ALLOCATION.intCode()).equals(whReturn.getType())) {
                if (CollectionUtils.isNotEmpty(whReturnItems)) {
                    for (WhReturnItem whReturnItem : whReturnItems) {
                        if (StringUtils.isNotBlank(whReturnItem.getReturnOrderNo())) {
                            // 关联返架列表不处理库存
                            continue;
                        }
                        //本仓的库内返架才扣库存
                        if ((whReturn.getFlag() == null || whReturn.getFlag().equals(CheckInWhType.LOCAL.intCode()))
                                && !returnUpdateStockService.updateStockByUpScanReturnBoxNo(
                                Arrays.asList(whReturnItem.getSku()), whReturnItem, whReturn.getType())) {
                            throw new RuntimeException("扫描周转框,修改上架中库存失败！");
                        }
                    }
                }
            }
        }
        response.setStatus(StatusCode.SUCCESS);
        return response;
    }

    /**
     * 库内返架匹配订单
     *
     * @param sku
     * @return
     */
    public WhApvAndWhSku doMatchApv(String sku) {
        WhApvAndWhSku whApvAndWhSku = new WhApvAndWhSku();
        String message = null;

        whApvAndWhSku.setSku(sku);

        WhApvQueryCondition query = new WhApvQueryCondition();
        query.setSku(sku);

        // 单品单件
        query.setApvType("SS");

        // 扫描正常状态的发货单，包含海外仓标识
        query.setNormalApv(true);

        // 发货单状态列表
        List<Integer> statusList = new ArrayList<Integer>();
        statusList.add(ApvStatus.SINGLETON_TOUCHING.intCode());// 单件合单
        statusList.add(ApvStatus.PICKING_STOCKOUT_NOT.intCode());// 拣货缺货
        statusList.add(ApvStatus.ALLOT.intCode());// 已分配
        statusList.add(ApvStatus.WAITING_ALLOT.intCode());// 待分配
        statusList.add(ApvStatus.STOCKOUT_NOT.intCode());// 缺货

        query.setStatusList(statusList);

        // apv排序 单件合单》拣货缺货》已分配》待分配》缺货
        query.setOrderBy(ApvOrderBy.CUSTOM_APV_STATUS_ASC.intCode());

        List<WhApv> whApvs = whApvLockService.queryWhApvBySinglesingleAndReturns(query, null);

        if (CollectionUtils.isNotEmpty(whApvs)) {
            WhApv whApv = new WhApv();
            boolean isPrintUrl = false;
            for (WhApv tempWhApv : whApvs) {
                // 提前测试获取面单，如果获取得到，则就匹配订单，如果获取不到，则直接进行库内返架
                SystemParam systemParam = CacheUtils.SystemParamGet("OMS_PARAM.PRINT_URL");
                String url = systemParam.getParamValue();
                ApiResult<?> apiResult = HttpUtils.get(url + tempWhApv.getApvNo(), "", ApiResult.class);
                if (null != apiResult && apiResult.isSuccess()) {
                    if (Integer.valueOf(ApvStatus.WAITING_ALLOT.intCode()).equals(tempWhApv.getStatus())
                            || Integer.valueOf(ApvStatus.STOCKOUT_NOT.intCode()).equals(tempWhApv.getStatus())) {
                        // 待分配、缺货 TMS同步追踪号，成功则匹配订单，失败不匹配
                        if (!whApvService.updateWhApvTrackingNumberById(tempWhApv.getId())) {
                            break;
                        }
                    }
                    isPrintUrl = true;
                    whApv = tempWhApv;
                    break;
                }
            }

            if (!isPrintUrl) {
                message = "订单无同步物流记录进入库内返架！";
                return queryWhSku(sku, message);
            }

            Integer beforStatus = whApv.getStatus();
            // 库内返架匹配订单改库存
            boolean isReturn = returnUpdateStockService.updateStockByToMatchApv(sku, whApv);
            if (isReturn) {
                ApvTrackQueryCondition trackQuery = new ApvTrackQueryCondition();
                trackQuery.setApvNo(whApv.getApvNo());
                List<ApvTrack> apvTracks = apvTrackService.queryApvTracks(trackQuery, null);

                // 没有apv轨迹
                if (CollectionUtils.isEmpty(apvTracks)) {
                    ApvTrack apvTrack = new ApvTrack();
                    apvTrack.setApvNo(whApv.getApvNo());
                    apvTrack.setPackUser(DataContextHolder.getUserId());
                    apvTrack.setPackFinishTime(new Timestamp(System.currentTimeMillis()));
                    apvTrackService.createApvTrack(apvTrack);

                } else {
                    // 包装完成
                    ApvTrack apvTrack = new ApvTrack();
                    apvTrack.setApvNo(whApv.getApvNo());
                    apvTrack.setPackUser(DataContextHolder.getUserId());
                    apvTrack.setPackFinishTime(new Timestamp(System.currentTimeMillis()));
                    apvTrackService.updateApvTrackByApvNo(apvTrack);
                }

                // 推送pms包装
                whApvService.pushPackingFinish(whApv);
                //TODO 包装完成，推送状态到菜鸟
                PacSendUtil.wmsOrderStatusUpload(whApv.getApvNo(), PacOrderUploadStatus.WMS_PACKAGE.getCode());

                SystemLogUtils.APVLOG.log(whApv.getId(), "库内返架单件包装发货单状态变更",
                        new String[][]{{"历史状态", ApvStatus.getNameByCode(beforStatus.toString())},
                                {"更改状态", ApvStatus.getNameByCode(ApvStatus.WAITING_DELIVER.getCode())}});

                whApvAndWhSku.setWhApv(whApv);
                whApvAndWhSku.setType(1);

            } else {
                // 加库存不成功，返回null，报异常让程序回滚
                throw new RuntimeException("库内返架匹配发货单改库存失败！");
            }
        } else {
            message = "查询不到订单进入库内返架！";
            return queryWhSku(sku, message);
        }
        return whApvAndWhSku;
    }

    @Override
    // @StockServicelock
    public ResponseJson createWhReturnAndItem(List<String> skuList, List<WhReturn> whReturnList) {
        ResponseJson response = new ResponseJson(StatusCode.FAIL);
        if (CollectionUtils.isEmpty(whReturnList)) {
            response.setMessage("参数为空！");
            return response;
        }
        String successIdStr = "";
        for (WhReturn whReturn : whReturnList) {
            List<WhReturnItem> items = whReturn.getWhReturnItems();
            if (items.size() == 0) {
                response.setMessage(whReturn.getReturnNo() + " 没有要返架的SKU！");
                return response;
            }
            this.createWhReturn(whReturn);
            if (whReturn.getId() != null) {
                for (WhReturnItem item : items) {
                    if (item.getQuantity() == null || item.getQuantity() == 0) {
                        throw new RuntimeException("保存失败 ！sku[" + item.getSku() + "]，没有返架数量！");
                    }
                    item.setReturnId(whReturn.getId());
                    whReturnItemService.createWhReturnItem(item);

                    // 创建返架不在操作库存(原来的不关联返架列表的还是要操作)
                    if (Integer.valueOf(ReturnType.ALLOCATION.intCode()).equals(whReturn.getType()) && StringUtils.isBlank(item.getReturnOrderNo())) {
                        if (!allocationReturnUpdateStockService
                                .updateStockBySavAllocationReturn(Arrays.asList(item.getSku()), item)) {
                            throw new RuntimeException("调拨返架修改库存失败,新增调拨返架单失败 ！sku[" + item.getSku() + "]");
                        }
                    }
                    /*
                     * else if
                     * (!returnUpdateStockService.updateStockBySaveReturn(Arrays.asList(item.getSku(
                     * )), item)) { throw new RuntimeException("库内返架修改库存失败，新增库内返架单失败 ！sku[" +
                     * item.getSku() + "]"); }
                     */
                }
                if (StringUtils.isNotBlank(items.get(0).getReturnOrderNo())) {
                    allocateReturnOrderService.updateStatusByCreateReturn(items.get(0).getReturnOrderNo(), AllocateReturnOrderStatus.RETURNNING.intCode());
                }
                successIdStr = successIdStr + "," + whReturn.getId();
            }
        }
        response.setStatus(StatusCode.SUCCESS);
        if (StringUtils.isNotBlank(successIdStr)) {
            response.setMessage("新增" + ReturnType.getNameByCode(whReturnList.get(0).getType().toString()) + "单成功，编号：【"
                    + successIdStr + "]");
        }
        return response;
    }

    @Override
    @StockServicelock
    public ResponseJson updateReturnItemAndComplete(List<String> skuList, WhReturnItem whReturnItem,
                                                    AndroidProductDo domain, Integer type, String batchNo) {
        ResponseJson response = new ResponseJson(StatusCode.FAIL);
        if (Integer.valueOf(ReturnType.ALLOCATION.intCode()).equals(type) && StringUtils.isNotBlank(whReturnItem.getReturnOrderNo())) {
            // 查询历史关联的返架单总共上架的数量
            AllocateReturnOrderQueryCondition query = new AllocateReturnOrderQueryCondition();
            query.setOrderNo(whReturnItem.getReturnOrderNo());
            query.setQueryReturn(true);
            List<AllocateReturnOrder> allocateReturnOrders = allocateReturnOrderService.queryAllocateReturnOrderAndItems(query, null);
            if (CollectionUtils.isNotEmpty(allocateReturnOrders)) {
                for (AllocateReturnOrderItem orderItem : allocateReturnOrders.get(0).getAllocateReturnOrderItems()) {
                    if (orderItem.getSku().equals(whReturnItem.getSku())) {
                        Integer stocked = 0;
                        for (WhReturnItem item : orderItem.getWhReturnItems()) {
                            stocked += item.getStockQuantity() == null ? 0 : item.getStockQuantity();
                        }
                        Integer mateQuantity = orderItem.getMateQuantity() == null ? 0 : orderItem.getMateQuantity();
                        // 当已上架的数量小于匹配库存时，上架才操作调拨库存
                        if (stocked < mateQuantity) {
                            whReturnItem.setMateJianStock(mateQuantity - stocked);
                        }
                        break;
                    }
                }
            }
        }

        if (!returnUpdateStockService.updateStockByReturnUp(whReturnItem, type, false)) {
            throw new RuntimeException(ReturnType.getNameByCode(type.toString()) + "上架加可用库存失败！");
        }

        WhReturnItem item = new WhReturnItem();
        item.setId(domain.getReturnItemId());
        item.setCompleteQuantity(whReturnItem.getCompleteQuantity());
        item.setStatus(domain.getStatus());
        if (Integer.valueOf(ReturnType.RETURN.intCode()).equals(type)) {
            // 只操作库内返架（加可用库存成功后回填操作库存的数量）
            item.setStockQuantity(whReturnItem.getStockQuantity());
        }
        if (Integer.valueOf(ReturnType.ALLOCATION.intCode()).equals(type) && StringUtils.isNotBlank(whReturnItem.getReturnOrderNo())) {
            // 调拨返架处理库存数量
            item.setStockQuantity(whReturnItem.getStockQuantity());
        }
        if (whReturnItemService.updateWhReturnItem(item) > 0) {
            WhReturnItemQueryCondition query = new WhReturnItemQueryCondition();
            query.getStatusList().add(ReturnItemStatus.ERROR_COMPLETE);
            query.getStatusList().add(ReturnItemStatus.WAIT);
            query.setReturnId(domain.getReturnId());

            if (whReturnItemService.queryWhReturnItemCount(query) == 0) {
                this.completeWhReturn(domain.getReturnId(), false);
            }
            if (Integer.valueOf(ReturnType.ALLOCATION.intCode()).equals(type) && StringUtils.isNotBlank(whReturnItem.getReturnOrderNo())) {
                WhReturn whReturn = new WhReturn();
                whReturn.setId(domain.getReturnId());
                whReturn.getWhReturnItems().add(item);
                allocateReturnOrderService.completeOrder(whReturnItem.getReturnOrderNo(), whReturn);
            }
            if (StringUtils.isNotEmpty(batchNo))
                updateBatchOrder(batchNo, whReturnItem);

        }


        // 外借在途、已拣返架、取消、拣选缺货、冻结、已拣库存为零的SKU, 库内返架完成之后生成盘点任务
        if (Integer.valueOf(ReturnType.RETURN.intCode()).equals(type)) {
            createReturnInventory(whReturnItem, batchNo);

        }

        response.setStatus(StatusCode.SUCCESS);
        return response;
    }

    private void createReturnInventory(WhReturnItem whReturnItem , String batchNo) {
        if (whReturnItem==null || StringUtils.isBlank(whReturnItem.getSku())) {
            return;
        }
        String sku = whReturnItem.getSku();
        try {
            WhStockQueryCondition query = new WhStockQueryCondition();
            query.setId(whReturnItem.getStockId());
            query.setSku(whReturnItem.getSku());
            WhStock stock = whStockService.queryWhStock(query);

            if (stock == null) {
                return;
            }

            //已拣返架
            Integer pickReturnQuantity = Optional.ofNullable(stock.getPickReturnQuantity()).orElse(0);
            //取消
            Integer cancelQuantity = Optional.ofNullable(stock.getCancelQuantity()).orElse(0);
            //拣货缺货
            Integer pickNotQuantity = Optional.ofNullable(stock.getPickNotQuantity()).orElse(0);
            //已拣库存
            Integer pickQuantity = Optional.ofNullable(stock.getPickQuantity()).orElse(0);

            if (pickReturnQuantity != 0 || cancelQuantity != 0 || pickNotQuantity != 0 || pickQuantity != 0) {
                return;
            }

            FrozenStockQueryCondition queryCondition = new FrozenStockQueryCondition();
            queryCondition.setSku(sku);
            queryCondition.setStockId(whReturnItem.getStockId());
            FrozenStock frozenStock = frozenStockService.queryFrozenStock(queryCondition);
            //外借在途
            Integer lendOnwayQuantity = 0;
            //冻结
            Integer frozenQuantity = 0;
            if (frozenStock != null) {
                lendOnwayQuantity = Optional.ofNullable(frozenStock.getLendOnwayQuantity()).orElse(0);
                frozenQuantity = Optional.ofNullable(frozenStock.getFrozenQuantity()).orElse(0);
            }
            if (lendOnwayQuantity != 0 || frozenQuantity != 0) {
                return;
            }
            WhPickInventoryDemandQueryCondition demandQueryCondition = new WhPickInventoryDemandQueryCondition();
            demandQueryCondition.setSku(sku);
            demandQueryCondition.setPickTaskNo(batchNo);
            demandQueryCondition.setStockId(whReturnItem.getStockId());
            List<WhPickInventoryDemand> whPickInventoryDemands = whPickInventoryDemandService.queryWhPickInventoryDemands(demandQueryCondition, null);
            if (CollectionUtils.isNotEmpty(whPickInventoryDemands)) {
                return;
            }

            WhPickInventoryDemand demand = new WhPickInventoryDemand();
            demand.setSku(sku);
            demand.setPickTaskNo(batchNo);
            demand.setStockId(whReturnItem.getStockId());
            demand.setDemandType(InventoryDemandType.RETURN.intCode());
            demand.setStatus(PickInventoryDemandStatus.UNGENERATE.intCode());
            whPickInventoryDemandService.createWhPickInventoryDemand(demand);
            if (demand.getId() != null) {
                SystemLogUtils.PICKINVENTORYDEMAND.log(demand.getId(), "库内返架生成盘点需求",
                        new String[][]{{"返架单号", batchNo}});
            }

        } catch (Exception e) {
            logger.error("sku:" + sku + " 库内返架完成之后生成盘点任务失败!", e);
        }
    }

    /**
     * 更新批次状态和数量
     *
     * @param batchNo
     * @param item
     */
    private void updateBatchOrder(String batchNo, WhReturnItem item) {
        WhBatchReturnQueryCondition queryCondition = new WhBatchReturnQueryCondition();
        queryCondition.setOrderNo(batchNo);
        queryCondition.setType(CreateTaskNoUtils.KNFJ);
        WhBatchReturn whBatchReturn = whBatchReturnService.getWhBatchReturnDetailByOrderNo(queryCondition);
        if (whBatchReturn == null) return;
        //获取退货批次明细数据
        List<WhBatchReturnSku> whBatchReturnSkus = whBatchReturn.getWhBatchReturnSkus();
        if (CollectionUtils.isEmpty(whBatchReturnSkus)) return;

        WhBatchReturnSku whBatchReturnSku = whBatchReturnSkus.stream().
                filter(w -> StringUtils.equalsIgnoreCase(w.getSku(), item.getSku())).findFirst().orElse(new WhBatchReturnSku());
        int returnningQuantity = whBatchReturnSku.getReturnningQuantity() == null ? 0 : whBatchReturnSku.getReturnningQuantity();
        if (returnningQuantity == 0) return;
        int completeQuantity = whBatchReturnSku.getCompleteQuantity() == null ? 0 : whBatchReturnSku.getCompleteQuantity();
        if (returnningQuantity < item.getUpdateQuantity()) return;
        WhBatchReturnSku returnSku = new WhBatchReturnSku();
        returnSku.setId(whBatchReturnSku.getId());
        // 已完成数量
        returnSku.setCompleteQuantity(completeQuantity + item.getUpdateQuantity());
        // 返架中数量
        returnSku.setReturnningQuantity(returnningQuantity - item.getUpdateQuantity());
        if (whBatchReturnSku.getQuantity().equals(returnSku.getCompleteQuantity())) {
            returnSku.setStatus(WhBatchReturnSkuStatus.COMPLETE.intCode());

            whBatchReturnSku.setCompleteQuantity(returnSku.getCompleteQuantity());
            whBatchReturnSku.setReturnningQuantity(returnSku.getReturnningQuantity());
            // 判断批次所有数量是否都已返架
            if (whBatchReturnSkus.stream().allMatch(w -> w.getQuantity().equals(w.getCompleteQuantity()))) {
                whBatchReturnService.completeWhBatchReturn(whBatchReturn, false);
                if (CollectionUtils.isNotEmpty(whBatchReturn.getWhBatchReturnUuids())) {
                    //更新唯一码批次唯一码状态
                    List<WhBatchReturnUuid> uuidList = whBatchReturn.getWhBatchReturnUuids().stream().map(w -> {
                        WhBatchReturnUuid whB = new WhBatchReturnUuid();
                        whB.setId(w.getId());
                        whB.setStatus(WhBatchReturnStatus.COMPLETE.intCode());
                        return whB;
                    }).collect(Collectors.toList());
                    whBatchReturnUuidService.batchUpdateWhBatchReturnUuid(uuidList);
                }
            }
        }
        whBatchReturnSkuService.updateWhBatchReturnSku(returnSku);
    }

    /**
     * 库内返架批次自动完成或异常完成
     *
     * @param whBatchReturn
     * @param batchNo
     */
    public void doComplateWhBatchReturn(WhBatchReturn whBatchReturn, String batchNo) {
        if (StringUtils.isBlank(batchNo)) {
            return;
        }
        WhReturnQueryCondition condition = new WhReturnQueryCondition();
        condition.setBatchNo(batchNo);
        condition.setType(ReturnType.RETURN.intCode());
        List<WhReturn> whReturns = whReturnDao.queryWhReturnList(condition, null);
        List<Integer> status = whReturns.stream().map(WhReturn::getStatus).distinct().collect(Collectors.toList());
        if (status.contains(ReturnStatus.ING.intCode()) || status.contains(ReturnStatus.WAIT.intCode())) {
            // 该批次包含未完成的返架单
            return;
        }
        boolean errorComplete = false;
        if (status.contains(ReturnStatus.ERROR.intCode())) {
            errorComplete = true;
        }
        if (whBatchReturn == null || whBatchReturn.getId() == null) {
            WhBatchReturnQueryCondition queryCondition = new WhBatchReturnQueryCondition();
            queryCondition.setOrderNo(batchNo);
            queryCondition.setType(CreateTaskNoUtils.KNFJ);
            whBatchReturn = whBatchReturnService.getWhBatchReturnDetailByOrderNo(queryCondition);
        }

        List<WhBatchReturnSku> whBatchReturnSkus = whBatchReturn.getWhBatchReturnSkus();
        // 更新sku
        List<WhBatchReturnSku> collect = whBatchReturnSkus.stream().map(w -> {
            WhBatchReturnSku whB = new WhBatchReturnSku();
            whB.setId(w.getId());
            whB.setStatus(WhBatchReturnSkuStatus.EXCEPTION_COMPLETE.intCode());
            return whB;
        }).collect(Collectors.toList());
        // 更新批次sku状态
        whBatchReturnSkuService.batchUpdateWhBatchReturnSku(collect);
        // 更新批次状态
        whBatchReturnService.completeWhBatchReturn(whBatchReturn, errorComplete);
        if (CollectionUtils.isNotEmpty(whBatchReturn.getWhBatchReturnUuids())) {
            //更新唯一码批次唯一码状态
            List<WhBatchReturnUuid> uuidList = whBatchReturn.getWhBatchReturnUuids().stream().map(w -> {
                WhBatchReturnUuid whB = new WhBatchReturnUuid();
                whB.setId(w.getId());
                whB.setStatus(WhBatchReturnStatus.EXCEPTION_COMPLETE.intCode());
                return whB;
            }).collect(Collectors.toList());
            whBatchReturnUuidService.batchUpdateWhBatchReturnUuid(uuidList);
        }
    }

    /**
     * 库内退件
     * 
     * @param whBox
     * @param returnType
     * @return
     */
    @Override
    public ResponseJson doKNReturn(WhBox whBox, Integer returnType) {
        ResponseJson responseJson = new ResponseJson(StatusCode.FAIL);
        // 扫描周转框领取
        WhReturn whReturn = this.getWhReturn(Integer.valueOf(whBox.getRelationNo()));
        if (whReturn == null) {
            responseJson.setMessage("任务已经被取消！");
            return responseJson;
        }
        if (returnType != null && !returnType.equals(whReturn.getType())) {
            responseJson.setMessage("该返架单类型为:" + ReturnType.getNameByCode(String.valueOf(whReturn.getType())));
            return responseJson;
        }

        if (ReturnStatus.ING.intCode().equals(whReturn.getStatus())
                && !DataContextHolder.getUserId().equals(whReturn.getReturnUser())) {
            responseJson.setMessage("该返架单正在被" + TaglibUtils.getEmployeeNameByUserId(whReturn.getReturnUser()) + "上架！");
            return responseJson;
        }

        WhReturnItemQueryCondition itemQuery = new WhReturnItemQueryCondition();
        itemQuery.setReturnId(Integer.valueOf(whBox.getRelationNo()));
        List<WhReturnItem> whReturnItems = whReturnItemService.queryWhReturnItems(itemQuery, null);
        if (CollectionUtils.isNotEmpty(whReturnItems)) {
            List<String> skuList = whReturnItems.stream().map(WhReturnItem::getSku).collect(toList());
            responseJson = this.scanBoxNoUpdateReturnStatusAndUpQuantity(skuList, whReturn, whReturnItems);
            // 修改返架批次状态 待返架->返架中
            if (StringUtils.isNotBlank(whReturn.getBatchNo())) {
                WhBatchReturnQueryCondition batchCondition = new WhBatchReturnQueryCondition();
                batchCondition.setOrderNo(whReturn.getBatchNo());
                batchCondition.setType(CreateTaskNoUtils.KNFJ);
                WhBatchReturn whBatchReturn = whBatchReturnService.queryWhBatchReturn(batchCondition);
                if (whBatchReturn != null && whBatchReturn.getId() != null && whBatchReturn.getStatus() != null
                        && whBatchReturn.getStatus() < WhBatchReturnStatus.UPING.intCode()) {
                    WhBatchReturn aReturn = new WhBatchReturn();
                    aReturn.setStatus(WhBatchReturnStatus.UPING.intCode());
                    aReturn.setId(whBatchReturn.getId());
                    whBatchReturnService.updateWhBatchReturn(aReturn);
                }
            }
        }
        return responseJson;
    }
}