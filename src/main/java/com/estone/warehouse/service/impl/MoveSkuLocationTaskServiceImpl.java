package com.estone.warehouse.service.impl;

import com.estone.common.util.SystemLogUtils;
import com.estone.warehouse.bean.MoveSkuLocationTask;
import com.estone.warehouse.bean.MoveSkuLocationTaskItem;
import com.estone.warehouse.bean.MoveSkuLocationTaskItemQueryCondition;
import com.estone.warehouse.bean.MoveSkuLocationTaskQueryCondition;
import com.estone.warehouse.dao.MoveSkuLocationTaskDao;
import com.estone.warehouse.enums.MoveLocationTaskStatus;
import com.estone.warehouse.service.MoveSkuLocationTaskItemService;
import com.estone.warehouse.service.MoveSkuLocationTaskService;
import com.whq.tool.component.Pager;
import com.whq.tool.exception.BusinessException;
import com.whq.tool.exception.DBErrorCode;
import com.whq.tool.sqler.SqlerException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@Service("moveSkuLocationTaskService")
@Slf4j
public class MoveSkuLocationTaskServiceImpl implements MoveSkuLocationTaskService {

    private Logger logger = LoggerFactory.getLogger(MoveSkuLocationTaskServiceImpl.class);
    @Resource
    private MoveSkuLocationTaskDao moveSkuLocationTaskDao;

    @Resource
    private MoveSkuLocationTaskItemService moveSkuLocationTaskItemService;

    @Override
    public MoveSkuLocationTask getMoveSkuLocationTask(Integer id) {
        MoveSkuLocationTask moveSkuLocationTask = moveSkuLocationTaskDao.queryMoveSkuLocationTask(id);
        return moveSkuLocationTask;
    }

    @Override
    public MoveSkuLocationTask getMoveSkuLocationTaskDetail(Integer id) {
        MoveSkuLocationTask moveSkuLocationTask = moveSkuLocationTaskDao.queryMoveSkuLocationTask(id);
        // 关联查询
        // 关联查询
        MoveSkuLocationTaskItemQueryCondition query = new MoveSkuLocationTaskItemQueryCondition();
        query.setTaskNo(moveSkuLocationTask.getTaskNo());
        List<MoveSkuLocationTaskItem> items = moveSkuLocationTaskItemService.queryMoveSkuLocationTaskItems(query, null);
        moveSkuLocationTask.setItems(items);
        return moveSkuLocationTask;
    }

    @Override
    public MoveSkuLocationTask queryMoveSkuLocationTask(MoveSkuLocationTaskQueryCondition query) {
        Assert.notNull(query, "query is null!");
        MoveSkuLocationTask moveSkuLocationTask = moveSkuLocationTaskDao.queryMoveSkuLocationTask(query);
        return moveSkuLocationTask;
    }

    @Override
    public List<MoveSkuLocationTask> queryAllMoveSkuLocationTasks() {
        return moveSkuLocationTaskDao.queryMoveSkuLocationTaskList();
    }

    @Override
    public List<MoveSkuLocationTask> queryMoveSkuLocationTasks(MoveSkuLocationTaskQueryCondition query, Pager pager) {
        Assert.notNull(query, "query is null!");
        if (pager != null && pager.isQueryCount()) {
            int count = moveSkuLocationTaskDao.queryMoveSkuLocationTaskCount(query);
            pager.setTotalCount(count);
            if (count == 0) {
                return new ArrayList<MoveSkuLocationTask>();
            }
        }
        List<MoveSkuLocationTask> moveSkuLocationTasks = moveSkuLocationTaskDao.queryMoveSkuLocationTaskList(query, pager);
        return moveSkuLocationTasks;
    }

    @Override
    public void createMoveSkuLocationTask(MoveSkuLocationTask moveSkuLocationTask) {
        try {
            moveSkuLocationTaskDao.createMoveSkuLocationTask(moveSkuLocationTask);
        } catch (SqlerException e) {
            log.error(e.getMessage(), e);
            throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
        }
    }

    @Override
    public void batchCreateMoveSkuLocationTask(List<MoveSkuLocationTask> entityList) {
        if (CollectionUtils.isNotEmpty(entityList)) {
            try {
                moveSkuLocationTaskDao.batchCreateMoveSkuLocationTask(entityList);
            } catch (SqlerException e) {
                log.error(e.getMessage(), e);
                throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
            }
        }
    }

    @Override
    public void deleteMoveSkuLocationTask(Integer id) {
        try {
            moveSkuLocationTaskDao.deleteMoveSkuLocationTask(id);
        } catch (SqlerException e) {
            log.error(e.getMessage(), e);
            throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
        }
    }

    @Override
    public void updateMoveSkuLocationTask(MoveSkuLocationTask moveSkuLocationTask) {
        try {
            moveSkuLocationTaskDao.updateMoveSkuLocationTask(moveSkuLocationTask);
        } catch (SqlerException e) {
            log.error(e.getMessage(), e);
            throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
        }
    }

    @Override
    public void batchUpdateMoveSkuLocationTask(List<MoveSkuLocationTask> entityList) {
        if (CollectionUtils.isNotEmpty(entityList)) {
            try {
                moveSkuLocationTaskDao.batchUpdateMoveSkuLocationTask(entityList);
            } catch (SqlerException e) {
                log.error(e.getMessage(), e);
                throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
            }
        }
    }

    @Override
    public void createTask(List<MoveSkuLocationTaskItem> items) {
        // TODO 校验新库位规则-规则待定
        //checkNewLocation(newLocation);
        MoveSkuLocationTask task = new MoveSkuLocationTask();
        // TODO 创建任务号-规则待定
        //task.setTaskNo(CreateTaskNoUtils.createMoveLocationTaskNo());
        task.setTaskStatus(MoveLocationTaskStatus.WAIT_RECEIVE.intCode());
        task.setSkuCount(items.size());
        task.setPcsCount(items.stream().mapToInt(MoveSkuLocationTaskItem::getQuantity).sum());
        createMoveSkuLocationTask(task);

        for (MoveSkuLocationTaskItem item : items) {
            item.setTaskNo(task.getTaskNo());
            item.setStatus(MoveLocationTaskStatus.WAIT_RECEIVE.intCode());
        }
        moveSkuLocationTaskItemService.batchCreateMoveSkuLocationTaskItem(items);
        SystemLogUtils.MOVESKULOCATIONTASKLOG.log(task.getId(), "新建SKU移库单");
    }

    @Override
    public void checkNewLocation(String newLocation) {
        if (StringUtils.isNotBlank(newLocation)) {

        }
    }
}