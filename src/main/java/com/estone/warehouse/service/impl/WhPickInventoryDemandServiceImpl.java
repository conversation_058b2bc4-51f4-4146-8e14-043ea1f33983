package com.estone.warehouse.service.impl;

import com.alibaba.fastjson.JSON;
import com.estone.apv.bean.WhApv;
import com.estone.apv.bean.WhApvItem;
import com.estone.apv.bean.WhApvOutStockChain;
import com.estone.common.util.CreateTaskNoUtils;
import com.estone.common.util.SkuLocationCompareUtils;
import com.estone.common.util.SystemLogUtils;
import com.estone.common.util.TaglibUtils;
import com.estone.picking.bean.WhPickingTask;
import com.estone.picking.bean.WhPickingTaskItem;
import com.estone.picking.bean.WhPickingTaskItemQueryCondition;
import com.estone.picking.bean.WhPickingTaskQueryCondition;
import com.estone.picking.service.WhPickingTaskItemService;
import com.estone.picking.service.WhPickingTaskService;
import com.estone.warehouse.action.WhPickInventoryDemandController;
import com.estone.warehouse.bean.*;
import com.estone.warehouse.dao.WhInventoryTaskDao;
import com.estone.warehouse.dao.WhInventoryTaskItemDao;
import com.estone.warehouse.dao.WhPickInventoryDemandDao;
import com.estone.warehouse.enums.InventoryDemandType;
import com.estone.warehouse.service.PickInventoryDemandToApvService;
import com.estone.warehouse.service.WhPickInventoryDemandService;
import com.estone.warehouse.service.WhRecordService;
import com.estone.warehouse.service.WhStockService;
import com.google.common.collect.Lists;
import com.whq.tool.component.Pager;
import com.whq.tool.component.StatusCode;
import com.whq.tool.exception.BusinessException;
import com.whq.tool.exception.DBErrorCode;
import com.whq.tool.json.ResponseJson;
import com.whq.tool.sqler.SqlerException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service("whPickInventoryDemandService")
public class WhPickInventoryDemandServiceImpl implements WhPickInventoryDemandService {
    @Resource
    private WhPickInventoryDemandDao whPickInventoryDemandDao;

    @Resource
    private WhInventoryTaskDao whInventoryTaskDao;

    @Resource
    private WhInventoryTaskItemDao whInventoryTaskItemDao;

    @Resource
    private WhRecordService whRecordService;

    @Resource
    private PickInventoryDemandToApvService pickInventoryDemandToApvService;

    @Resource
    private WhPickingTaskService whPickingTaskService;

    @Resource
    private WhStockService whStockService;

    @Resource
    private WhPickingTaskItemService whPickingTaskItemService;

    /**
     * This method corresponds to the database table wh_pick_inventory_demand
     *
     * @mbggenerated Tue Jul 02 16:57:09 CST 2019
     */
    public WhPickInventoryDemand getWhPickInventoryDemand(Integer id) {
        WhPickInventoryDemand whPickInventoryDemand = whPickInventoryDemandDao.queryWhPickInventoryDemand(id);
        return whPickInventoryDemand;
    }

    /**
     * This method corresponds to the database table wh_pick_inventory_demand
     *
     * @mbggenerated Tue Jul 02 16:57:09 CST 2019
     */
    public WhPickInventoryDemand getWhPickInventoryDemandDetail(Integer id) {
        WhPickInventoryDemand whPickInventoryDemand = whPickInventoryDemandDao.queryWhPickInventoryDemand(id);
        // 关联查询
        return whPickInventoryDemand;
    }

    /**
     * This method corresponds to the database table wh_pick_inventory_demand
     *
     * @mbggenerated Tue Jul 02 16:57:09 CST 2019
     */
    public WhPickInventoryDemand queryWhPickInventoryDemand(WhPickInventoryDemandQueryCondition query) {
        Assert.notNull(query);
        WhPickInventoryDemand whPickInventoryDemand = whPickInventoryDemandDao.queryWhPickInventoryDemand(query);
        return whPickInventoryDemand;
    }

    /**
     * This method corresponds to the database table wh_pick_inventory_demand
     *
     * @mbggenerated Tue Jul 02 16:57:09 CST 2019
     */
    public List<WhPickInventoryDemand> queryAllWhPickInventoryDemands() {
        return whPickInventoryDemandDao.queryWhPickInventoryDemandList();
    }

    /**
     * This method corresponds to the database table wh_pick_inventory_demand
     *
     * @mbggenerated Tue Jul 02 16:57:09 CST 2019
     */
    public List<WhPickInventoryDemand> queryWhPickInventoryDemands(WhPickInventoryDemandQueryCondition query,
                                                                   Pager pager) {
        Assert.notNull(query);
        if (pager != null && pager.isQueryCount()) {
            int count = whPickInventoryDemandDao.queryWhPickInventoryDemandCount(query);
            pager.setTotalCount(count);
            if (count == 0) {
                return new ArrayList<WhPickInventoryDemand>();
            }
        }
        List<WhPickInventoryDemand> whPickInventoryDemands = whPickInventoryDemandDao
                .queryWhPickInventoryDemandList(query, pager);
        return whPickInventoryDemands;
    }

    /**
     * This method corresponds to the database table wh_pick_inventory_demand
     *
     * @mbggenerated Tue Jul 02 16:57:09 CST 2019
     */
    public void createWhPickInventoryDemand(WhPickInventoryDemand whPickInventoryDemand) {
        try {
            whPickInventoryDemandDao.createWhPickInventoryDemand(whPickInventoryDemand);
        } catch (SqlerException e) {
            log.error(e.getMessage(), e);
            throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
        }
    }

    /**
     * This method corresponds to the database table wh_pick_inventory_demand
     *
     * @mbggenerated Tue Jul 02 16:57:09 CST 2019
     */
    public void batchCreateWhPickInventoryDemand(List<WhPickInventoryDemand> entityList) {
        if (CollectionUtils.isNotEmpty(entityList)) {
            try {
                whPickInventoryDemandDao.batchCreateWhPickInventoryDemand(entityList);
            } catch (SqlerException e) {
                log.error(e.getMessage(), e);
                throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
            }
        }
    }

    /**
     * This method corresponds to the database table wh_pick_inventory_demand
     *
     * @mbggenerated Tue Jul 02 16:57:09 CST 2019
     */
    public void deleteWhPickInventoryDemand(Integer id) {
        try {
            whPickInventoryDemandDao.deleteWhPickInventoryDemand(id);
        } catch (SqlerException e) {
            log.error(e.getMessage(), e);
            throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
        }
    }

    /**
     * This method corresponds to the database table wh_pick_inventory_demand
     *
     * @mbggenerated Tue Jul 02 16:57:09 CST 2019
     */
    public void updateWhPickInventoryDemand(WhPickInventoryDemand whPickInventoryDemand) {
        try {
            whPickInventoryDemandDao.updateWhPickInventoryDemand(whPickInventoryDemand);
        } catch (SqlerException e) {
            log.error(e.getMessage(), e);
            throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
        }
    }

    /**
     * This method corresponds to the database table wh_pick_inventory_demand
     *
     * @mbggenerated Tue Jul 02 16:57:09 CST 2019
     */
    public int[] batchUpdateWhPickInventoryDemand(List<WhPickInventoryDemand> entityList) {
        if (CollectionUtils.isNotEmpty(entityList)) {
            try {
                return whPickInventoryDemandDao.batchUpdateWhPickInventoryDemand(entityList);
            } catch (SqlerException e) {
                log.error(e.getMessage(), e);
                throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
            }
        }
        return new int[]{0};
    }

    @Override
    public List<WhPickInventoryDemand> queryWhPickInventoryDemandAndInventoryTaskList(
            WhPickInventoryDemandQueryCondition query, Pager pager) {
        Assert.notNull(query);
        if (pager != null && pager.isQueryCount()) {
            int count = whPickInventoryDemandDao.queryWhPickInventoryDemandAndInventoryTaskCount(query);
            pager.setTotalCount(count);
            if (count == 0) {
                return new ArrayList<WhPickInventoryDemand>();
            }
        }
        List<WhPickInventoryDemand> whPickInventoryDemands = whPickInventoryDemandDao
                .queryWhPickInventoryDemandAndInventoryTaskList(query, pager);
        return whPickInventoryDemands;
    }

    @Override
    public void generatePickInventoryDemand(InventoryDemandParam param, Integer currentUser, List<WhApvOutStockChain> inventoryDemandStockChain) {
        log.info("********** generatePickInventoryDemand param: " + JSON.toJSONString(param));
        try {
            param.setDemandType(InventoryDemandType.PICKOUTOFSTOCK.intCode());
            if (!checkApvPickInventoryDemandExist(param) || CollectionUtils.isEmpty(inventoryDemandStockChain)) {
                return;
            }

            List<WhApvOutStockChain> whApvOutStockChainList = inventoryDemandStockChain.stream().collect(Collectors.toMap(WhApvOutStockChain::getStockId, a -> a, (o1, o2) -> {
                o1.setQuantity(Optional.ofNullable(o1.getQuantity()).orElse(0) + Optional.ofNullable(o2.getQuantity()).orElse(0));
                o1.setPickQuantity(Optional.ofNullable(o1.getPickQuantity()).orElse(0) + Optional.ofNullable(o2.getPickQuantity()).orElse(0));
                return o1;
            })).values().stream().collect(Collectors.toList());
            for (WhApvOutStockChain whApvOutStockChain : whApvOutStockChainList) {
                Integer quantity = Optional.ofNullable(whApvOutStockChain.getQuantity()).orElse(0);
                Integer pickQuantity = Optional.ofNullable(whApvOutStockChain.getPickQuantity()).orElse(0);
                if (pickQuantity >= quantity) {
                    continue;
                }
                param.setStockId(whApvOutStockChain.getStockId());
                param.setSku(whApvOutStockChain.getSku());
                param.setNeedQuantity(quantity);
                param.setPickQuantity(pickQuantity);
                generatePickInventoryDemand(param);
            }

        } catch (Exception e) {
            log.error("******* create PickInventoryDemand failed");
        }
    }

    @Override
    public void cancelOrderToAddDemandToApv(WhApv apv) {
        String taskNo = apv.getCompleteStatus();
        Integer apvId = apv.getId();
        List<WhApvItem> items = apv.getWhApvItems();
        if (StringUtils.isNotBlank(taskNo) && apvId != null && CollectionUtils.isNotEmpty(items)) {
            WhPickingTaskQueryCondition taskQuery = new WhPickingTaskQueryCondition();
            taskQuery.setTaskNo(taskNo);
            WhPickingTask task = whPickingTaskService.queryWhPickingTask(taskQuery);
            if (task != null) {
                //WhPickingTaskSkuQueryCondition taskSkuQuery = new WhPickingTaskSkuQueryCondition();
                //taskSkuQuery.setTaskId(task.getId());
                //List<WhPickingTaskSku> taskSkus = whPickingTaskSkuService.queryWhPickingTaskSkus(taskSkuQuery, null);

                PickInventoryDemandToApvQueryCondition query = new PickInventoryDemandToApvQueryCondition();
                query.setTaskId(task.getId());
                query.setApvId(apvId);
                List<PickInventoryDemandToApv> list = pickInventoryDemandToApvService.queryPickInventoryDemandToApvs(query, null);
                if (CollectionUtils.isEmpty(list)) {
                    List<PickInventoryDemandToApv> demandToApvs = new ArrayList<>();
                    for (WhApvItem item : items) {
                        PickInventoryDemandToApv demandToApv = new PickInventoryDemandToApv();
                        demandToApv.setTaskId(task.getId());
                        demandToApv.setSku(item.getSku());
                        demandToApv.setApvId(apvId);
                        demandToApvs.add(demandToApv);
                    }
                    pickInventoryDemandToApvService.batchCreatePickInventoryDemandToApv(demandToApvs);
                }
            }
        }
    }


    public boolean checkApvPickInventoryDemandExist(InventoryDemandParam param) {
        boolean result = true;
        if (CollectionUtils.isNotEmpty(param.getFailedApvIds()) && param.getTaskId() != null) {
            Set<Integer> apvIdSet = new HashSet<>();
            PickInventoryDemandToApvQueryCondition query = new PickInventoryDemandToApvQueryCondition();
            query.setTaskId(param.getTaskId());
            query.setApvIdList(param.getFailedApvIds());
            List<PickInventoryDemandToApv> list = pickInventoryDemandToApvService.queryPickInventoryDemandToApvs(query, null);
            boolean hasTaskSkuId = false;
            if (CollectionUtils.isNotEmpty(list)) {
                List<Integer> existApvIds = new ArrayList<>();
                for (PickInventoryDemandToApv exist : list) {
                    existApvIds.add(exist.getApvId());
                    if (param.getTaskSkuId() != null && param.getTaskSkuId().equals(exist.getTaskSkuId())) {
                        hasTaskSkuId = true;
                    }
                }
                for (Integer apvId : param.getFailedApvIds()) {
                    if (!existApvIds.contains(apvId)) {
                        apvIdSet.add(apvId);
                    }
                }
                if (CollectionUtils.isEmpty(apvIdSet)) {
                    return false;
                }
            } else {
                apvIdSet.addAll(param.getFailedApvIds());
            }
            if (CollectionUtils.isNotEmpty(apvIdSet) && param.getTaskSkuId() != null && StringUtils.isNotBlank(param.getSku())
                    && param.getTaskId() != null) {
                List<PickInventoryDemandToApv> demandToApvs = new ArrayList<>();
                int i = 0;
                for (Integer apvId : apvIdSet) {
                    PickInventoryDemandToApv demandToApv = new PickInventoryDemandToApv();
                    demandToApv.setApvId(apvId);
                    demandToApv.setTaskId(param.getTaskId());
                    demandToApv.setSku(param.getSku());
                    if (i == 0 && !hasTaskSkuId) {
                        demandToApv.setTaskSkuId(param.getTaskSkuId());
                        i++;
                    }
                    demandToApvs.add(demandToApv);
                }
                pickInventoryDemandToApvService.batchCreatePickInventoryDemandToApv(demandToApvs);
            }
        }
        return result;
    }

    @Override
    public boolean generatePickInventoryDemand(InventoryDemandParam param) {
        boolean result = false;
        if (param != null && param.notNull()) {
            WhStockQueryCondition query = new WhStockQueryCondition();
            query.setId(param.getStockId());
            query.setSku(param.getSku());
            query.setLocationNumber(param.getLocationNumber());
            WhStock whStock = whStockService.queryWhStock(query);

            if (whStock != null) {
                Integer locationQuantity = whStock.getLocationQuantity() == null ? 0 : whStock.getLocationQuantity();
                WhPickInventoryDemand demand = new WhPickInventoryDemand(param, locationQuantity);
                demand.setStatus(PickInventoryDemandStatus.UNGENERATE.intCode());
                this.createWhPickInventoryDemand(demand);
                if (demand.getId() != null) {
                    SystemLogUtils.PICKINVENTORYDEMAND.log(demand.getId(), "生成盘点需求",
                            new String[][]{{"拣货任务号", demand.getPickTaskNo() + "拣货缺货"},
                                    {"拣货人", TaglibUtils.getEmployeeNameByUserId(demand.getCreationUser())},
                                    {"库存", String.valueOf(demand.getQuantity())}});

                    result = true;
                }
            }
        } else {
            log.error("generatePickInventoryDemand failed, param is null--> param: " + JSON.toJSONString(param));
        }
        return result;
    }

    /**
     * 生成盘点任务前校验
     */
    @Override
    public ResponseJson checkToGenerateTask(List<Integer> idList, List<String> locationRegions, Integer maxSkuNumber, Boolean checked,Integer inventoryNumber) {
        ResponseJson response = new ResponseJson();
        response.setStatus(StatusCode.FAIL);

        WhPickInventoryDemandQueryCondition demandQuery = new WhPickInventoryDemandQueryCondition();
        demandQuery.setIds(idList);
        demandQuery.setLocationRegions(locationRegions);
        demandQuery.setStatus(PickInventoryDemandStatus.UNGENERATE.intCode());
        List<WhPickInventoryDemand> demands = whPickInventoryDemandDao.queryWhPickInventoryDemandList(demandQuery, null);
        if (CollectionUtils.isEmpty(demands)) {
            response.setMessage("没有符合条件的SKU，请重新选择");
            return response;
        }

        Set<Integer> stockIdSet = new HashSet<>();
        Map<Integer, Integer> stockIdToIdMap = new HashMap<>();
        List<Integer> filterIds = new ArrayList<>();
        // 过滤重复的SKU
        List<Integer> demandTypes = new ArrayList<>();
        for (int i = 0; i < demands.size(); i++) {
            WhPickInventoryDemand demand = demands.get(i);
            // 为空是默认是拣货缺货
            Integer demandType = demand.getDemandType() == null ? InventoryDemandType.PICKOUTOFSTOCK.intCode() : demand.getDemandType();
            if (!demandTypes.contains(demandType)) {
                demandTypes.add(demandType);
            }
            if (!stockIdSet.contains(demand.getStockId())) {
                stockIdSet.add(demand.getStockId());
                filterIds.add(demand.getId());
                stockIdToIdMap.put(demand.getStockId(), demand.getId());
            }
        }
        List<Integer> ids = new ArrayList<>(filterIds);
        // 当勾选了Id时，不同需求类型不能合并生成盘点任务
        if (demandTypes.size() > 1 && CollectionUtils.isNotEmpty(idList)) {
            response.setMessage("不同需求类型不能合并生成盘点任务，请重新选择");
            return response;
        }

        WhInventoryTaskItemQueryCondition itemQuery = new WhInventoryTaskItemQueryCondition();
        itemQuery.setStockIds(new ArrayList<>(stockIdSet));
        List<WhInventoryTaskItem> items = whInventoryTaskItemDao.queryWhInventoryTaskItemDetailList(itemQuery, null);
        if (CollectionUtils.isNotEmpty(items)) {
            // 带了确认继续标识
            if (checked != null && checked) {
                for (WhInventoryTaskItem taskItem : items) {
                    // 不是已废弃或者，子父状态不都是已完成的需要排除
                    if (!((taskItem.getStatus().equals(InventoryTaskStatus.COMPLETED.intCode())
                            && taskItem.getTaskStatus().equals(InventoryTaskStatus.COMPLETED.intCode()))
                            ||  (taskItem.getStatus().equals(InventoryTaskStatus.COMPLETED.intCode())
                            && taskItem.getTaskStatus().equals(InventoryTaskStatus.DISCARDED.intCode()))
                            || taskItem.getStatus().equals(InventoryTaskStatus.DISCARDED.intCode()))) {
                        ids.remove(stockIdToIdMap.get(taskItem.getStockId()));
                    }
                }
                if (CollectionUtils.isEmpty(ids)) {
                    response.setMessage("您选择的SKU还有未完成的盘点任务，请重新选择");
                } else {
                    return this.createInventoryTask(ids, maxSkuNumber,inventoryNumber);
                }
            } else {
                // 第一次请求需要校验
                List<Integer> stockIds = new ArrayList<>();
                for (WhInventoryTaskItem taskItem : items) {
                    // 不是已废弃或者，子父状态不都是已完成的需要排除
                    if (!((taskItem.getStatus().equals(InventoryTaskStatus.COMPLETED.intCode())
                            && taskItem.getTaskStatus().equals(InventoryTaskStatus.COMPLETED.intCode()))
                            ||  (taskItem.getStatus().equals(InventoryTaskStatus.COMPLETED.intCode())
                            && taskItem.getTaskStatus().equals(InventoryTaskStatus.DISCARDED.intCode()))
                            || taskItem.getStatus().equals(InventoryTaskStatus.DISCARDED.intCode()))) {
                        if (!stockIds.contains(taskItem.getStockId())) {
                            stockIds.add(taskItem.getStockId());
                        }
                        ids.remove(stockIdToIdMap.get(taskItem.getStockId()));
                    }
                }
                if (CollectionUtils.isEmpty(stockIds)) {
                    return this.createInventoryTask(ids, maxSkuNumber,inventoryNumber);
                } else {
                    if (CollectionUtils.isNotEmpty(ids)) {
                        response.setMessage(StringUtils.join(stockIds, ",") + "还有未完成的盘点任务，是否将符合条件的其他SKU库存ID生成盘点任务");
                        response.setExceptionCode("check");
                    } else {
                        response.setMessage("您选择的SKU还有未完成的盘点任务，请重新选择");
                    }
                }
            }
        } else {
            return this.createInventoryTask(ids, maxSkuNumber,inventoryNumber);
        }
        return response;
    }

    /**
     * 用于根据需求ID创建单个任务sku数最多为maxSkuNumber个的盘点任务
     *
     * @param ids          盘点需求的ID
     * @param maxSkuNumber 单个盘点任务最大sku数
     * @return 相关提示
     */
    public ResponseJson createInventoryTask(List<Integer> ids, Integer maxSkuNumber, Integer inventoryNumber) {
        ResponseJson response = new ResponseJson();
        response.setStatus(StatusCode.SUCCESS);
        if (CollectionUtils.isEmpty(ids)) {
            response.setStatus(StatusCode.FAIL);
            response.setMessage("没有符合条件的SKU，请重新选择");
            return response;
        }
        WhPickInventoryDemandQueryCondition query = new WhPickInventoryDemandQueryCondition();
        query.setIds(ids);
        query.setStatus(PickInventoryDemandStatus.UNGENERATE.intCode());
        List<WhPickInventoryDemand> demands = whPickInventoryDemandDao.queryWhPickInventoryDemandList(query, null);
        if (CollectionUtils.isEmpty(demands)) {
            response.setStatus(StatusCode.FAIL);
            response.setMessage("没有符合条件的SKU，请重新选择");
            return response;
        }
        // 将其按照需求类型分类
        Map<Integer, List<WhPickInventoryDemand>> demandTypeMap = new HashMap<>();
        for (WhPickInventoryDemand demand : demands) {
            // 为空是默认是拣货缺货
            Integer demandType = demand.getDemandType() == null ? InventoryDemandType.PICKOUTOFSTOCK.intCode() : demand.getDemandType();
            demandTypeMap.computeIfAbsent(demandType, v -> new ArrayList<>()).add(demand);
        }

        StringBuilder stringBuilder = new StringBuilder();
        for (Map.Entry<Integer, List<WhPickInventoryDemand>> entry : demandTypeMap.entrySet()) {
            List<WhPickInventoryDemand> demandList = entry.getValue();
            if (CollectionUtils.isEmpty(demandList)){
                continue;
            }
            Map<String, List<WhPickInventoryDemand>> skuDemandMap = demandList.stream().collect(Collectors.groupingBy(WhPickInventoryDemand::getSku));
            List<Map.Entry<String, List<WhPickInventoryDemand>>> skuDemandList = new ArrayList<>(skuDemandMap.entrySet());
            Integer skuNum = maxSkuNumber;
            // 未设置单个任务最大sku数
            if (Objects.isNull(maxSkuNumber) || maxSkuNumber <= 0) {
                skuNum = skuDemandList.size();
            }

            for (List<Map.Entry<String, List<WhPickInventoryDemand>>> list : Lists.partition(skuDemandList, skuNum)) {
                List<Integer> singleTaskIds = list.stream()
                        .map(Map.Entry::getValue)
                        .flatMap(Collection::stream)
                        .map(WhPickInventoryDemand::getId)
                        .collect(Collectors.toList());
                ResponseJson responseJson = this.createInventoryTask(singleTaskIds,inventoryNumber);
                if (Objects.equals(StatusCode.FAIL, responseJson.getStatus())) {
                    stringBuilder.append(responseJson.getMessage() + ",id:" + JSON.toJSONString(singleTaskIds));
                    stringBuilder.append("<br/>");
                }
            }
        }
        if (stringBuilder.length() != 0) {
            response.setStatus(StatusCode.FAIL);
            response.setMessage(stringBuilder.toString());
        }
        return response;
    }

    /**
     * 生成盘点任务
     */
    @Override
    public ResponseJson createInventoryTask(List<Integer> ids,Integer inventoryNumber) {
        ResponseJson response = new ResponseJson();
        response.setStatus(StatusCode.FAIL);
        if (CollectionUtils.isEmpty(ids)) {
            response.setMessage("没有符合条件的SKU，请重新选择");
            return response;
        }
        WhPickInventoryDemandQueryCondition query = new WhPickInventoryDemandQueryCondition();
        query.setIds(ids);
        query.setStatus(PickInventoryDemandStatus.UNGENERATE.intCode());
        List<WhPickInventoryDemand> demands = whPickInventoryDemandDao.queryWhPickInventoryDemandList(query, null);
        if (CollectionUtils.isEmpty(demands)) {
            response.setMessage("没有符合条件的SKU，请重新选择");
            return response;
        }
        if (ids.size() > 300) {
            response.setMessage("用于生成单个盘点任务中符合条件的数量不能超过300条!");
            return response;
        }

        // 生成盘点任务前先对Item进行库位排序
        SkuLocationCompareUtils.compare(demands);
        Integer maxLevel = whInventoryTaskDao.queryWhInventoryTaskGradeMax();
        if (maxLevel == null) {
            maxLevel = 0;
        }

        WhInventoryTask task = new WhInventoryTask();
        String taskNoPrefix = CreateTaskNoUtils.createInventoryTaskNo();
        //task.setTaskType(InventoryTaskType.PICKOUTOFSTOCK.intCode());
        task.setTaskType(demands.get(0).getDemandType() == null ? InventoryTaskType.PICKOUTOFSTOCK.intCode() : demands.get(0).getDemandType());
        task.setStatus(InventoryTaskStatus.UNRECEIVED.intCode());
        task.setTaskLevel(InventoryTaskLevel.FIRST.intCode());
        task.setTaskNoPrefix(taskNoPrefix);
        task.setTaskNo(taskNoPrefix + "A");
        task.setGrade(maxLevel + 1);// 新生成默认置顶
        task.setInventoryCount(inventoryNumber);
        whInventoryTaskDao.createWhInventoryTask(task);
        if (task.getId() != null) {
            List<WhInventoryTaskItem> taskItems = new ArrayList<>();
            List<WhPickInventoryDemand> updateDemands = new ArrayList<>();
            for (WhPickInventoryDemand demand : demands) {
                WhPickInventoryDemand updateDemand = new WhPickInventoryDemand();
                updateDemand.setId(demand.getId());
                updateDemand.setTaskLevel(task.getTaskLevel());
                updateDemand.setStatus(PickInventoryDemandStatus.UNRECEIVED.intCode());
                updateDemand.setTaskNoPrefix(taskNoPrefix);
                updateDemands.add(updateDemand);

                WhInventoryTaskItem taskItem = new WhInventoryTaskItem();
                taskItem.setSku(demand.getSku());
                taskItem.setStockId(demand.getStockId());
                taskItem.setDemandId(demand.getId());
                taskItem.setTaskId(task.getId());
                taskItem.setTaskLevel(task.getTaskLevel());
                taskItem.setStatus(InventoryTaskStatus.UNRECEIVED.intCode());
                taskItems.add(taskItem);
            }
            whInventoryTaskItemDao.batchCreateWhInventoryTaskItem(taskItems);
            whPickInventoryDemandDao.batchUpdateWhPickInventoryDemand(updateDemands);

            SystemLogUtils.INVENTORYTASK.log(task.getId(), "生成盘点任务-" + task.getTaskLevelName(),
                    new String[][]{{"盘点任务号", task.getTaskNo()}, {"SKU数", String.valueOf(taskItems.size())}});

            for (WhPickInventoryDemand demand : demands) {
                SystemLogUtils.PICKINVENTORYDEMAND.log(demand.getId(), "生成盘点任务-" + task.getTaskLevelName(),
                        new String[][]{{"盘点任务号", task.getTaskNo()}});
            }
            response.setStatus(StatusCode.SUCCESS);
            response.setMessage(task.getTaskNo());
        } else {
            response.setMessage("创建任务失败");
        }
        return response;
    }

    /**
     * 废弃盘点需求
     */
    @Override
    public boolean discard(Integer id) {
        boolean result = false;
        WhPickInventoryDemand demand = whPickInventoryDemandDao.queryWhPickInventoryDemand(id);
        if (demand != null) {
            if (demand.getStatus().equals(PickInventoryDemandStatus.UNGENERATE.intCode())) {
                WhPickInventoryDemand update = new WhPickInventoryDemand();
                update.setId(demand.getId());
                update.setStatus(PickInventoryDemandStatus.DISCARDED.intCode());
                update.setLastStatus(PickInventoryDemandStatus.UNGENERATE.intCode());
                if (whPickInventoryDemandDao.updateWhPickInventoryDemand(update) >= 1) {
                    SystemLogUtils.PICKINVENTORYDEMAND.log(demand.getId(), "废弃盘点需求",
                            new String[][]{{"历史状态", demand.getStatusName()}, {"变更状态", update.getStatusName()}});
                    result = true;
                } else {
                    SystemLogUtils.PICKINVENTORYDEMAND.log(demand.getId(), "废弃盘点需求失败",
                            new String[][]{{"失败原因", "更新失败"}});
                }
            } else {
                SystemLogUtils.PICKINVENTORYDEMAND.log(demand.getId(), "废弃盘点需求失败",
                        new String[][]{{"失败原因", "不是待生成"}});
            }
        } else {
            log.warn("WhPickInventoryDemand not exist!  id-->" + id);
        }
        return result;
    }

    @Override
    public ResponseJson batchGenerateDemand(List<Integer> stockIds) {
        ResponseJson response = new ResponseJson();
        response.setStatus(StatusCode.FAIL);

        WhStockQueryCondition query = new WhStockQueryCondition();
        query.setIds(stockIds);
        List<WhStock> stockList = whStockService.queryWhStocks(query, null);
        if (CollectionUtils.isEmpty(stockList)) {
            response.setMessage("库存记录不存在！");
            return response;
        }

        stockIds = stockList.stream().map(WhStock::getId).collect(Collectors.toList());

        WhPickInventoryDemandQueryCondition demandQuery = new WhPickInventoryDemandQueryCondition();
        demandQuery.setStockIds(stockIds);
        demandQuery.setStatus(PickInventoryDemandStatus.UNGENERATE.intCode());
        List<WhPickInventoryDemand> demands = whPickInventoryDemandDao.queryWhPickInventoryDemandList(demandQuery,
                null);

        if (CollectionUtils.isNotEmpty(demands)) {
            response.setMessage("还有未生成盘点任务的的库存ID，请重新选择："
                    + StringUtils.join(demands.stream().map(item -> item.getStockId()).collect(Collectors.toList()), ","));
            return response;
        }

        WhInventoryTaskItemQueryCondition itemQuery = new WhInventoryTaskItemQueryCondition();
        itemQuery.setStockIds(stockIds);
        List<WhInventoryTaskItem> items = whInventoryTaskItemDao.queryWhInventoryTaskItemDetailList(itemQuery, null);
        if (CollectionUtils.isNotEmpty(items)) {
            // 第一次请求需要校验
            List<Integer> stockIdList = new ArrayList<>();
            for (WhInventoryTaskItem taskItem : items) {
                // 不是已废弃或者，子父状态不都是已完成的需要排除
                //线上存在子表已完成，父表已废弃的，需要排除这一类型
                boolean check = ((taskItem.getStatus().equals(InventoryTaskStatus.COMPLETED.intCode())
                        && taskItem.getTaskStatus().equals(InventoryTaskStatus.COMPLETED.intCode()))
                        ||  (taskItem.getStatus().equals(InventoryTaskStatus.COMPLETED.intCode())
                        && taskItem.getTaskStatus().equals(InventoryTaskStatus.DISCARDED.intCode()))
                        || taskItem.getStatus().equals(InventoryTaskStatus.DISCARDED.intCode()));
                if (!check) {
                    if (!stockIdList.contains(taskItem.getStockId())) {
                        stockIdList.add(taskItem.getStockId());
                    }
                }
            }
            if (CollectionUtils.isNotEmpty(stockIdList)) {
                response.setMessage("您选择的库存ID还有未完成的盘点任务，请重新选择: " + StringUtils.join(stockIdList, ","));
                return response;
            }
        }

        for (WhStock whStock : stockList) {
            Integer locationQuantity = whStock.getLocationQuantity() == null ? 0 : whStock.getLocationQuantity();
            WhPickInventoryDemand demand = new WhPickInventoryDemand();
            demand.setSku(whStock.getSku());
            demand.setStockId(whStock.getId());
            demand.setQuantity(locationQuantity);
            demand.setDemandType(InventoryDemandType.MANUAL.intCode());
            demand.setStatus(PickInventoryDemandStatus.UNGENERATE.intCode());
            this.createWhPickInventoryDemand(demand);
            if (demand.getId() != null) {
                SystemLogUtils.PICKINVENTORYDEMAND.log(demand.getId(), "手动生成盘点需求",
                        new String[][]{{"库存", String.valueOf(demand.getQuantity())}});
            }
        }
        response.setStatus(StatusCode.SUCCESS);
        return response;
    }


    @Override
    public void generateInventoryDemand(WhPickingTask whPickingTask) {
        if (Objects.isNull(whPickingTask)) {
            return;
        }
        WhPickingTaskItemQueryCondition whPickingTaskItemQueryCondition = new WhPickingTaskItemQueryCondition();
        whPickingTaskItemQueryCondition.setIsAsn(whPickingTask.getIsAsn());
        whPickingTaskItemQueryCondition.setTaskId(whPickingTask.getId());

        List<WhPickingTaskItem> whPickingTaskItems = whPickingTaskItemService.queryPickingTaskItemAndOutStockChains(whPickingTaskItemQueryCondition
                , null);
        if (CollectionUtils.isEmpty(whPickingTaskItems)) {
            return;
        }
        // 得到所有播种数目与拣货数目不等的sku
        List<WhApvOutStockChain> outStockChains = whPickingTaskItems.stream()
                .map(WhPickingTaskItem::getWhApvItems)
                .filter(CollectionUtils::isNotEmpty)
                .flatMap(Collection::stream)
                .map(WhApvItem::getWhApvOutStockChains)
                .filter(CollectionUtils::isNotEmpty)
                .flatMap(Collection::stream)
                .filter(v -> v.getPickQuantity() > v.getGirdQuantity())
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(outStockChains)){
            log.info("不存在播种差异，为此不生成盘点需求！");
        }

        // 聚合相同的库存的sku
        outStockChains = outStockChains.stream()
                .collect(Collectors.groupingBy(WhApvOutStockChain::getStockId))
                .values()
                .stream()
                .map(list -> {
                    WhApvOutStockChain whApvOutStockChain = new WhApvOutStockChain();
                    for (WhApvOutStockChain chain : list) {
                        whApvOutStockChain.setStockId(chain.getStockId());
                        whApvOutStockChain.setSku(chain.getSku());
                        Integer chainPickQuantity = Optional.ofNullable(chain.getPickQuantity()).orElse(0);
                        Integer stockChainPickQuantity = Optional.ofNullable(whApvOutStockChain.getPickQuantity()).orElse(0);
                        whApvOutStockChain.setPickQuantity(stockChainPickQuantity + chainPickQuantity);
                        Integer chainGridQuantity = Optional.ofNullable(chain.getGirdQuantity()).orElse(0);
                        Integer stockChainGridQuantity = Optional.ofNullable(whApvOutStockChain.getGirdQuantity()).orElse(0);
                        whApvOutStockChain.setGirdQuantity(chainGridQuantity + stockChainGridQuantity);
                    }
                    return whApvOutStockChain;
                })
                .collect(Collectors.toList());

        for (WhApvOutStockChain whApvOutStockChain:outStockChains) {
            InventoryDemandParam param = new InventoryDemandParam();
            param.setDemandType(InventoryDemandType.DISCREPANCY_OF_SOWING.intCode());
            param.setStockId(whApvOutStockChain.getStockId());
            param.setTaskType(whPickingTask.getTaskType());
            param.setSku(whApvOutStockChain.getSku());
            param.setPickQuantity(whApvOutStockChain.getGirdQuantity());
            param.setNeedQuantity(whApvOutStockChain.getPickQuantity());
            param.setTaskNo(whPickingTask.getTaskNo());
            this.generatePickInventoryDemand(param);
        }
    }
}