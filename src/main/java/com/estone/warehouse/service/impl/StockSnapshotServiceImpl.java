package com.estone.warehouse.service.impl;

import com.estone.checkin.enums.CheckInWhType;
import com.estone.warehouse.bean.StockSnapshot;
import com.estone.warehouse.bean.StockSnapshotQueryCondition;
import com.estone.warehouse.dao.StockSnapshotDao;
import com.estone.warehouse.service.StockSnapshotService;
import com.whq.tool.component.Pager;
import com.whq.tool.exception.BusinessException;
import com.whq.tool.exception.DBErrorCode;
import com.whq.tool.sqler.SqlerException;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

@Service("stockSnapshotService")
@Slf4j
public class StockSnapshotServiceImpl implements StockSnapshotService {
    @Resource
    private StockSnapshotDao stockSnapshotDao;

    @Override
    public StockSnapshot getStockSnapshot(Integer id) {
        StockSnapshot stockSnapshot = stockSnapshotDao.queryStockSnapshot(id);
        return stockSnapshot;
    }

    @Override
    public StockSnapshot getStockSnapshotDetail(Integer id) {
        StockSnapshot stockSnapshot = stockSnapshotDao.queryStockSnapshot(id);
        // 关联查询
        return stockSnapshot;
    }

    @Override
    public StockSnapshot queryStockSnapshot(StockSnapshotQueryCondition query) {
        Assert.notNull(query, "query is null!");
        StockSnapshot stockSnapshot = stockSnapshotDao.queryStockSnapshot(query);
        return stockSnapshot;
    }

    @Override
    public List<StockSnapshot> queryAllStockSnapshots() {
        return stockSnapshotDao.queryStockSnapshotList();
    }

    @Override
    public List<StockSnapshot> queryStockSnapshots(StockSnapshotQueryCondition query, Pager pager) {
        Assert.notNull(query, "query is null!");
        if (pager != null && pager.isQueryCount()) {
            int count = stockSnapshotDao.queryStockSnapshotCount(query);
            pager.setTotalCount(count);
            if ( count == 0) {
                return new ArrayList<StockSnapshot>();
            }
        }
        List<StockSnapshot> stockSnapshots = stockSnapshotDao.queryStockSnapshotList(query, pager);
        return stockSnapshots;
    }

    @Override
    public void createStockSnapshot(StockSnapshot stockSnapshot) {
        try {
            stockSnapshotDao.createStockSnapshot(stockSnapshot);
        }
        catch (SqlerException e) {
            log.error(e.getMessage(), e);
            throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
        }
    }

    @Override
    public void batchCreateStockSnapshot(List<StockSnapshot> entityList) {
        if (CollectionUtils.isNotEmpty(entityList)) {
            try {
                stockSnapshotDao.batchCreateStockSnapshot(entityList);
            }
            catch (SqlerException e) {
                log.error(e.getMessage(), e);
                throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
            }
        }
    }

    @Override
    public void deleteStockSnapshot(Integer id) {
        try {
            stockSnapshotDao.deleteStockSnapshot(id);
        }
        catch (SqlerException e) {
            log.error(e.getMessage(), e);
            throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
        }
    }

    @Override
    public void deleteByCountDate(String countDate,CheckInWhType checkInWhType) {
        try {
            stockSnapshotDao.deleteByCountDate(countDate,checkInWhType);
        }
        catch (SqlerException e) {
            log.error(e.getMessage(), e);
            throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
        }
    }

    @Override
    public void updateStockSnapshot(StockSnapshot stockSnapshot) {
        try {
            stockSnapshotDao.updateStockSnapshot(stockSnapshot);
        }
        catch (SqlerException e) {
            log.error(e.getMessage(), e);
            throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
        }
    }

    @Override
    public void batchUpdateStockSnapshot(List<StockSnapshot> entityList) {
        if (CollectionUtils.isNotEmpty(entityList)) {
            try {
                stockSnapshotDao.batchUpdateStockSnapshot(entityList);
            }
            catch (SqlerException e) {
                log.error(e.getMessage(), e);
                throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
            }
        }
    }

    @Override
    public List<StockSnapshot> queryStockSnapshotByCheckInType(CheckInWhType checkInType) {
        return stockSnapshotDao.queryStockSnapshotByCheckInType(checkInType);
    }
}