package com.estone.warehouse.service.impl;

import com.estone.common.executors.ExecutorUtils;
import com.estone.common.util.POIUtils;
import com.estone.exquisite.service.BaseService;
import com.estone.transfer.bean.TransferStock;
import com.estone.transfer.service.TransferStockService;
import com.estone.warehouse.bean.WhStock;
import com.estone.warehouse.bean.WhStockChangeRecord;
import com.estone.warehouse.bean.WhStockChangeRecordQueryCondition;
import com.estone.warehouse.bean.WhStockQueryCondition;
import com.estone.warehouse.dao.WhStockChangeRecordDao;
import com.estone.warehouse.enums.OrderTypeEnum;
import com.estone.warehouse.enums.WhAllocateTypeEnum;
import com.estone.warehouse.service.WhStockChangeRecordService;
import com.estone.warehouse.service.WhStockService;
import com.whq.tool.component.Pager;
import com.whq.tool.exception.BusinessException;
import com.whq.tool.exception.DBErrorCode;
import com.whq.tool.sqler.SqlerException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Vector;
import java.util.concurrent.ExecutorService;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

@Service("whStockChangeRecordService")
@Slf4j
public class WhStockChangeRecordServiceImpl extends BaseService<WhStockChangeRecordQueryCondition, WhStockChangeRecord> implements WhStockChangeRecordService {
    @Resource
    private WhStockChangeRecordDao whStockChangeRecordDao;

    @Resource
    private WhStockService whStockService;

    @Resource
    private TransferStockService transferStockService;

    // 异步生成进销存明显数据
    private final static ExecutorService executors = ExecutorUtils.newFixedThreadPool(10);

    @Override
    public List<WhStockChangeRecord> list(WhStockChangeRecordQueryCondition search, Pager pager) {
        Assert.notNull(search, "query is null!");
        if (pager != null && pager.isQueryCount()) {
            int count = whStockChangeRecordDao.queryWhStockChangeRecordCount(search);
            pager.setTotalCount(count);
            if ( count == 0) {
                return new ArrayList<WhStockChangeRecord>();
            }
        }
        List<WhStockChangeRecord> whStockChangeRecords = whStockChangeRecordDao.queryWhStockChangeRecordList(search, pager);
        return whStockChangeRecords;
    }

    @Override
    public void createWhStockChangeRecord(WhStockChangeRecord whStockChangeRecord) {
        try {
            whStockChangeRecordDao.createWhStockChangeRecord(whStockChangeRecord);
        }
        catch (SqlerException e) {
            log.error(e.getMessage(), e);
            throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
        }
    }

    @Override
    public void batchCreateWhStockChangeRecord(List<WhStockChangeRecord> entityList) {
        if (CollectionUtils.isNotEmpty(entityList)) {
            try {
                whStockChangeRecordDao.batchCreateWhStockChangeRecord(entityList);
            }
            catch (SqlerException e) {
                log.error(e.getMessage(), e);
                throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
            }
        }
    }

    @Override
    public void deleteWhStockChangeRecord(Integer id) {
        try {
            whStockChangeRecordDao.deleteWhStockChangeRecord(id);
        }
        catch (SqlerException e) {
            log.error(e.getMessage(), e);
            throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
        }
    }

    @Override
    public void updateWhStockChangeRecord(WhStockChangeRecord whStockChangeRecord) {
        try {
            whStockChangeRecordDao.updateWhStockChangeRecord(whStockChangeRecord);
        }
        catch (SqlerException e) {
            log.error(e.getMessage(), e);
            throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
        }
    }

    @Override
    public void batchUpdateWhStockChangeRecord(List<WhStockChangeRecord> entityList) {
        if (CollectionUtils.isNotEmpty(entityList)) {
            try {
                whStockChangeRecordDao.batchUpdateWhStockChangeRecord(entityList);
            }
            catch (SqlerException e) {
                log.error(e.getMessage(), e);
                throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
            }
        }
    }

    @Override
    public List<List<String>> download(WhStockChangeRecordQueryCondition search, Pager pager) {
        List<WhStockChangeRecord> records = whStockChangeRecordDao.queryWhStockChangeRecordList(search, pager);
        if (CollectionUtils.isEmpty(records)) {
            return Collections.EMPTY_LIST;
        }
        Vector<List<String>> whStocks = new Vector<List<String>>();
        for (WhStockChangeRecord record : records) {
            List<String> rowList = new ArrayList<>();
            rowList.add(POIUtils.transferObj2Str(record.getCreationDate()));
            rowList.add(POIUtils.transferObj2Str(record.getWarehouse()));
            rowList.add(POIUtils.transferObj2Str(record.getStockType()));
            rowList.add(POIUtils.transferObj2Str(OrderTypeEnum.getNameByCode(record.getOrderType()+"")));
            rowList.add(POIUtils.transferObj2Str(record.getRelationNo()));
            rowList.add(POIUtils.transferObj2Str(record.getStockId()));
            rowList.add(POIUtils.transferObj2Str(record.getSku()));
            rowList.add(POIUtils.transferObj2Str(record.getUuid()));
            rowList.add(POIUtils.transferObj2Str(record.getBeforeQuantity()));
            rowList.add(POIUtils.transferObj2Str(record.getChangeQuantity()));
            rowList.add(POIUtils.transferObj2Str(record.getAfterQuantity()));
            whStocks.add(rowList);
        }
        return whStocks;
    }

    /**
     * 生成进销存明细
     */
    @Override
    public void generateStockChangeRecord(Integer stockId, Integer qty, String relationNo,
                                                         WhAllocateTypeEnum warehouseTypeE, OrderTypeEnum orderTypeE, Boolean isNewStock){
         generateStockChangeRecord(stockId, qty, relationNo, warehouseTypeE, orderTypeE, isNewStock, null);
    }

    /**
     * 生成进销存明细
     */
    @Override
    public void generateStockChangeRecord(Integer stockId, Integer qty, String relationNo,
                                                         WhAllocateTypeEnum warehouseTypeE, OrderTypeEnum orderTypeE, Boolean isNewStock, String uuid){
        if (stockId == null || qty == null || qty == 0){
            return;
        }
        executors.execute(() -> {
            try {
                long start = System.currentTimeMillis();
                log.info("generateStockChangeRecord start:" + stockId);
                WhStockChangeRecord lastRecord = whStockChangeRecordDao.getLastRecordByStockId(stockId);
                Integer prevAfterQty = 0;
                String sku = null;
                if (lastRecord != null) {
                    prevAfterQty = lastRecord.getAfterQuantity();
                    sku = lastRecord.getSku();
                } else {
                    if (WhAllocateTypeEnum.FBA.equals(warehouseTypeE)) {
                        TransferStock transferStock = transferStockService.getTransferStock(stockId);
                        if (transferStock == null) {
                            log.warn("generateStockChangeRecord not find transStock: stockId:" + stockId);
                            return ;
                        }
                        sku = transferStock.getSku();
                        // 中转仓在仓=可用+已分配+已拣+已拣返架
                        prevAfterQty = (transferStock.getSurplusQuantity() == null ? 0 : transferStock.getSurplusQuantity())
                                + (transferStock.getAllotQuantity() == null ? 0 : transferStock.getAllotQuantity())
                                + (transferStock.getPickQuantity() == null ? 0 : transferStock.getPickQuantity())
                                +(transferStock.getPickReturnQuantity() == null ? 0 : transferStock.getPickReturnQuantity());
                    } else {
                        WhStockQueryCondition query = new WhStockQueryCondition();
                        query.setId(stockId);
                        List<WhStock> whStocks = whStockService.queryWhStocks(query, null);
                        if (CollectionUtils.isEmpty(whStocks)) {
                            log.warn("generateStockChangeRecord not find whStock: stockId:" + stockId);
                            return ;
                        }
                        WhStock whStock = whStocks.get(0);
                        sku = whStock.getSku();
                        // 获取最新的在仓库存
                        // 在仓=可用+已分配+已捡+拣货缺货+已捡返架+取消+订单调拨+库存调拨+冻结
                        prevAfterQty = whStock.getQuantity()==null?0:whStock.getQuantity();
                    }
                }
                if (Boolean.TRUE.equals(isNewStock)) {
                    // 新创建库存记录 期初库存为0
                    prevAfterQty = 0;
                }
                WhStockChangeRecord record = new WhStockChangeRecord();
                record.setWarehouse(warehouseTypeE.getName());
                record.setOrderType(orderTypeE.intCode());
                record.setRelationNo(relationNo);
                record.setStockId(stockId);
                record.setSku(sku);
                record.setUuid(uuid);
                record.setBeforeQuantity(prevAfterQty);
                record.setChangeQuantity(qty);
                record.setAfterQuantity(prevAfterQty + qty);
                if (qty > 0) {
                    if (OrderTypeEnum.ALLOCATION.equals(orderTypeE) || OrderTypeEnum.PRESTORAGE_TRANSFER.equals(orderTypeE)
                            || OrderTypeEnum.COMBINE.equals(orderTypeE)) {
                        record.setStockType("库内入库");
                    } else {
                        record.setStockType("正常入库");
                    }
                } else {
                    if (OrderTypeEnum.ALLOCATION.equals(orderTypeE) || OrderTypeEnum.PRESTORAGE_TRANSFER.equals(orderTypeE)
                            || OrderTypeEnum.COMBINE.equals(orderTypeE)) {
                        record.setStockType("库内出库");
                    } else {
                        record.setStockType("正常出库");
                    }
                    if (OrderTypeEnum.RFO_CHECKIN.equals(orderTypeE)){
                        record.setStockType("正常入库");
                    }
                }
                whStockChangeRecordDao.createWhStockChangeRecord(record);
                log.info("generateStockChangeRecord start:" + stockId + ",耗时：" + (System.currentTimeMillis() - start));
            }catch (Exception e){
                log.error("generateStockChangeRecord params: stockId:" + stockId + ", qty:" + qty + ", relationNo:" + relationNo);
                log.error("generateStockChangeRecord error: " + e.getMessage(), e);
            }
        });
    }

}