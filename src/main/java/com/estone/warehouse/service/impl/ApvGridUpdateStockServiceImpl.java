package com.estone.warehouse.service.impl;

import java.util.*;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import com.estone.apv.bean.WhApv;
import com.estone.apv.bean.WhApvQueryCondition;
import com.estone.apv.common.ApvOrderType;
import com.estone.apv.service.WhApvService;
import com.estone.picking.service.WhPickingTaskService;
import com.estone.statistics.enums.AssetOrderType;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSONObject;
import com.estone.apv.bean.WhApvOutStockChain;
import com.estone.apv.bean.WhApvOutStockChainQueryCondition;
import com.estone.apv.enums.WhApvOutStockChainStatusEnum;
import com.estone.apv.service.WhApvOutStockChainService;
import com.estone.picking.enums.PickingTaskType;
import com.estone.system.rabbitmq.bean.AmqMessage;
import com.estone.system.rabbitmq.common.AmqMessageModuleName;
import com.estone.system.rabbitmq.model.OmsFbaSkuMessage;
import com.estone.system.rabbitmq.service.AmqMessageService;
import com.estone.system.rabbitmq.utils.AssembleMessageDataUtils;
import com.estone.transfer.bean.*;
import com.estone.transfer.enums.TransferStockLogType;
import com.estone.transfer.service.TransferStockService;
import com.estone.transfer.service.WhTransitStockLogService;
import com.estone.warehouse.bean.InventoryDemandParam;
import com.estone.warehouse.bean.WhStock;
import com.estone.warehouse.bean.WhStockLog;
import com.estone.warehouse.bean.WhStockQueryCondition;
import com.estone.warehouse.enums.InventoryDemandType;
import com.estone.warehouse.enums.StockLogStep;
import com.estone.warehouse.enums.StockLogType;
import com.estone.warehouse.service.ApvGridUpdateStockService;
import com.estone.warehouse.service.WhPickInventoryDemandService;
import com.estone.warehouse.service.WhStockLogService;
import com.estone.warehouse.service.WhStockService;
import com.whq.tool.exception.BusinessException;

/**
 * <AUTHOR>
 * @version 1.0
 * @Description 订单播种库存操作实现
 * @date 2019/12/30 17:21
 */
@Service("apvGridUpdateStockService")
public class ApvGridUpdateStockServiceImpl implements ApvGridUpdateStockService {

    private Logger log = LoggerFactory.getLogger(ApvGridUpdateStockServiceImpl.class);


    @Resource
    private WhStockService whStockService;

    @Resource
    private WhStockLogService whStockLogService;

    @Resource
    private TransferStockService transferStockService;

    @Resource
    private WhTransitStockLogService stockLogService;

    @Resource
    private AmqMessageService amqMessageService;

    @Resource
    private WhApvOutStockChainService whApvOutStockChainService;
    
    @Resource
    private WhPickInventoryDemandService whPickInventoryDemandService;

    @Resource
    private WhApvService whApvService;

    @Resource
    private WhPickingTaskService whPickingTaskService;

    public Map<Integer, WhStock> getWhStockMapBySkuList(List<String> skus) {
        WhStockQueryCondition query = new WhStockQueryCondition();
        query.setSkus(skus);
        List<WhStock> whStocks = whStockService.queryWhStocks(query, null);
        if (CollectionUtils.isEmpty(whStocks)) {
            log.error("queryWhStocks result is null: " + skus);
            return null;
        }

        // 库存集合Map
        Map<Integer, WhStock> stockMap = whStocks.stream().collect(Collectors.toMap(WhStock::getId, s -> s));
        return stockMap;
    }

    /**
     * 播种拣货缺货退已拣和已分配
     *
     * @param skus
     * @param pickMap
     * @param allotMap
     * @return
     */
    @Override
    public boolean pickReturn(List<String> skus, Map<String, Integer> pickMap, Map<String, Integer> allotMap,
            String taskNo, List<Integer> stockOutApvIds) {
        // 获取拣货库存记录
        List<WhApvOutStockChain> whApvOutStockChains = getWhApvOutStockChains(stockOutApvIds, skus, null,null);
        if (CollectionUtils.isEmpty(whApvOutStockChains)) {
            throw new RuntimeException(String.format("sku:%s无库存拣货记录，拣货缺货退库存失败", JSONObject.toJSONString(skus)));
        }

        WhApvQueryCondition query = new WhApvQueryCondition();
        query.setApvIds(stockOutApvIds);
        List<WhApv> whapvList = whApvService.queryWhApvs(query, null);
        if (CollectionUtils.isEmpty(whapvList)) {
            throw new RuntimeException(String.format("没有查询到apvId:%s 订单", JSONObject.toJSONString(stockOutApvIds)));
        }
        List<Integer> shipStatuList = Arrays.asList(ApvOrderType.JIT_FULL_MANAGEMENT.intCode(), ApvOrderType.JIT_HALF_MANAGEMENT.intCode());
        List<String> cancelApvNoList = whapvList
                .stream()
                .filter(whApv -> shipStatuList.contains(whApv.getShipStatus())
                        && whApv.getOriginalOrderId() != null
                        && whApv.getOriginalOrderId() >= 2)
                .map(WhApv::getApvNo)
                .collect(Collectors.toList());

        stockOutApvIds=whapvList.stream().filter(w->!cancelApvNoList.contains(w.getApvNo())).map(WhApv::getId).collect(Collectors.toList());

        Map<String, List<WhApvOutStockChain>> apvOutStockMap = whApvOutStockChains.stream()
                .collect(Collectors.groupingBy(WhApvOutStockChain::getSku));

        Map<Integer, WhStock> stockMap = getWhStockMapBySkuList(skus);
        if (stockMap == null) {
            throw new BusinessException("sku: " + JSONObject.toJSONString(skus) + "库存记录为空");
        }

        List<WhStock> stockList = new ArrayList<WhStock>();
        List<WhStock> updateList = new ArrayList<WhStock>();
        List<WhStockLog> stockLogs = new ArrayList<WhStockLog>();
        StockLogStep logStep = StockLogStep.APV_GRID;

        for (String sku : skus) {
            List<WhApvOutStockChain> skuOutStockList = apvOutStockMap.get(sku);
            if (CollectionUtils.isEmpty(skuOutStockList))
                throw new BusinessException("sku: " + sku + "库存记录为空");
            List<Integer> list = skuOutStockList.stream().map(WhApvOutStockChain::getStockId)
                    .collect(Collectors.toList());
            boolean noneMatch = stockMap.keySet().stream().noneMatch(s -> list.contains(s));
            if (noneMatch)
                throw new BusinessException("sku: " + sku + "库存记录为空");
            /**
             * 减：已拣 加：拣货缺货库存 减：已分配库存 加：可用库存
             */
            Integer picked = pickMap.get(sku) == null ? 0 : pickMap.get(sku);
            Integer allot = allotMap.get(sku) == null ? 0 : allotMap.get(sku);

            Integer pickNum = skuOutStockList.stream()
                    .mapToInt(s -> s.getPickQuantity() == null ? 0 : s.getPickQuantity()).sum();
            Integer allotNum = skuOutStockList.stream().mapToInt(s -> s.getQuantity() == null ? 0 : s.getQuantity())
                    .sum();

            if (picked > 0 && pickNum < picked) {
                log.error("orderNo:" + taskNo + ",sku:" + sku + "已拣库存不能为负: pickeQuantity[ " + pickNum + " ], picked[ "
                        + picked + " ]");
                throw new RuntimeException(String.format("sku:%s已拣库存不能为负: 已拣库存[%s], 扣已拣[%s]", sku, pickNum, picked));
            }
            if (allot > 0 && allotNum < allot) {
                log.error("orderNo:" + taskNo + ",sku:" + sku + "已分配库存不能为负: allotQuantity[ " + allotNum + " ], allot[ "
                        + allot + " ]");
                throw new RuntimeException(String.format("sku:%s已分配库存不能为负: 已分配库存[%s], 扣已分配[%s]", sku, allotNum, allot));

            }
            skuOutStockList.sort(Comparator.comparing(WhApvOutStockChain::getPickDiff).reversed());
            for (WhApvOutStockChain stockChain : skuOutStockList) {
                if (picked <= 0 && allot <= 0)
                    break;
                if ((stockChain.getPickQuantity() == null || stockChain.getPickQuantity() == 0)
                        && (stockChain.getQuantity() == null || stockChain.getQuantity() == 0))
                    continue;
                Integer stockId = stockChain.getStockId();
                WhStock whStock = stockMap.get(stockId);
                if (null == whStock)
                    continue;

                WhStock updateStock = WhStock.buildUpdateStock(whStock);
                if (picked > 0 && stockChain.getPickQuantity() != null
                        && stockChain.getPickQuantity() > 0) {
                    Integer apvPick = stockChain.getPickQuantity();
                    Integer pickedQty = picked;
                    if (apvPick <= picked) {
                        pickedQty = apvPick;
                    }
                    Integer pickQuantity = whStock.getPickQuantity();
                    Integer pickOutQuantity = whStock.getPickNotQuantity();
                    Integer cancelQuantity = Optional.ofNullable(whStock.getCancelQuantity()).orElse(0);
                    if (pickedQty > pickQuantity) {
                        log.error("orderNo:" + taskNo + ",sku:" + sku + "已拣库存不能为负: pickQuantity[ " + pickQuantity
                                + " ], pick[ " + pickedQty + " ]");
                        throw new RuntimeException(
                                String.format("sku:%s已拣库存不能为负: 已拣库存[%s], 扣已拣[%s]", sku, pickQuantity, pickedQty));
                    }

                    updateStock.setPickQuantity(pickQuantity - pickedQty);

                    //jit多品二次合单拣货缺货 扣已拣到取消
                    if (cancelApvNoList.contains(stockChain.getRelevantNo())) {
                        updateStock.setCancelQuantity(cancelQuantity + pickedQty);
                    }else{
                        updateStock.setPickNotQuantity(pickOutQuantity + pickedQty);
                    }
                    stockList.add(updateStock);
                    updateList.add(updateStock);
                    stockLogs.add(new WhStockLog(sku, StockLogType.PICKED_STOCK, stockId, whStock.getLocationNumber(),
                            logStep, -pickedQty, pickQuantity, taskNo));
                    if (cancelApvNoList.contains(stockChain.getRelevantNo())) {
                        stockLogs.add(new WhStockLog(sku, StockLogType.CANCEL_STOCK, stockId,
                                whStock.getLocationNumber(), logStep, pickedQty, cancelQuantity, taskNo));
                    }else{
                        stockLogs.add(new WhStockLog(sku, StockLogType.PICK_LACK_STOCK, stockId,
                                whStock.getLocationNumber(), logStep, pickedQty, pickOutQuantity, taskNo));
                    }

                    picked -= apvPick;
                }
                if (allot > 0 && stockChain.getQuantity() != null && stockChain.getQuantity() > 0) {
                    Integer apvAllot = stockChain.getQuantity();
                    Integer allotQty = allot;
                    if (apvAllot <= allot) {
                        allotQty = apvAllot;
                    }
                    Integer allotQuantity = whStock.getAllotQuantity();
                    Integer surplusQuantity = whStock.getSurplusQuantity();
                    if (allotQty > allotQuantity){
                        log.error("orderNo:" + taskNo + ",sku:" + sku + "已分配库存不能为负: allotQuantity[ " + allotQuantity + " ], allot[ "
                                + allotQty + " ]");
                        throw new RuntimeException(String.format("sku:%s已分配库存不能为负: 已分配库存[%s], 扣已分配[%s]", sku, allotQuantity, allotQty));
                    }
                    
                    updateStock.setAllotQuantity(allotQuantity - allotQty);
                    updateStock.setSurplusQuantity(surplusQuantity + allotQty);
                    stockList.add(updateStock);

                    stockLogs.add(new WhStockLog(sku, StockLogType.ALLOCATED, stockId, whStock.getLocationNumber(),
                            logStep, -allotQty, allotQuantity, taskNo));

                    stockLogs.add(new WhStockLog(sku, StockLogType.USABLE_STOCK, stockId, whStock.getLocationNumber(),
                            logStep, allotQty, surplusQuantity, taskNo));
                    allot -= apvAllot;
                }
            }
        }

        if (CollectionUtils.isNotEmpty(stockList)) {
            whStockService.batchUpdateWhStock(stockList);
        }
        whStockLogService.batchAddWhStockLog(stockLogs);

        whPickingTaskService.cancelJITOrder(whapvList, whApvOutStockChains);
        return true;
    }

    /**
     * 捞取中转仓库存
     * 
     * @param allocationList
     * @return
     */
    @Override
    public Map<Integer, TransferStock> getTransferStockMap(List<WhFbaAllocation> allocationList) {
        Map<Integer, TransferStock> stockMap = new HashMap<Integer, TransferStock>();
        if (CollectionUtils.isNotEmpty(allocationList)) {
            List<String> skus = new ArrayList<>();
            List<String> storeList = new ArrayList<>();
            List<String> siteList = new ArrayList<>();
            for (WhFbaAllocation allocation : allocationList) {

                skus.addAll(allocation.getItems().stream().map(item -> item.getProductSku())
                        .collect(Collectors.toList()));
                storeList.addAll(allocation.getItems().stream().map(item -> item.getStore())
                        .collect(Collectors.toList()));
                siteList.addAll(allocation.getItems().stream().map(item -> item.getSite())
                        .collect(Collectors.toList()));
            }
            TransferStockQueryCondition query = new TransferStockQueryCondition();
            skus = skus.stream().distinct().collect(Collectors.toList());
            storeList = storeList.stream().distinct().collect(Collectors.toList());
            siteList = siteList.stream().distinct().collect(Collectors.toList());
            query.setSkuList(skus);
            query.setStoreList(storeList);
            if (!allocationList.get(0).isFba()){
                query.setSiteList(siteList);
            }
            List<TransferStock> dbStockList = transferStockService.queryTransferStocks(query, null);

            if (CollectionUtils.isNotEmpty(dbStockList)) {
                stockMap = dbStockList.stream().collect(Collectors.toMap(TransferStock::getId, t -> t));
            }
        }
        return stockMap;
    }

    @Override
    public boolean asnPreparePickReturn(List<String> skus, Map<String, Integer> pickMap, Map<String, Integer> allotMap,
            String taskNo, List<WhFbaAllocation> allocationList) {
        // 获取拣货库存记录
        List<WhApvOutStockChain> whApvOutStockChains = getWhApvOutStockChains(null, skus,allocationList, AssetOrderType.ASN_PREPARE_ORDER.intCode());
        if (CollectionUtils.isEmpty(whApvOutStockChains)) {
            throw new RuntimeException(String.format("sku:%s无库存拣货记录，拣货缺货退库存失败", JSONObject.toJSONString(skus)));
        }

        Map<String, List<WhApvOutStockChain>> apvOutStockMap = whApvOutStockChains.stream()
                .collect(Collectors.groupingBy(WhApvOutStockChain::getSku));

        Map<Integer, TransferStock> stockMap = getTransferStockMap(allocationList);

        List<TransferStock> stockList = new ArrayList<TransferStock>();
        List<WhTransitStockLog> stockLogs = new ArrayList<WhTransitStockLog>();
        /** 库存变更推送 */
        List<AmqMessage> msgList = new ArrayList<>();

        StockLogStep logStep = StockLogStep.FBA_ALLOCATION_GRID;

        for (String sku : skus) {

            List<WhApvOutStockChain> skuOutStockChains = apvOutStockMap.get(sku);

            if (CollectionUtils.isEmpty(skuOutStockChains))
                throw new BusinessException("sku: " + sku + "已分配库存记录为空");

            List<Integer> list = skuOutStockChains.stream().map(WhApvOutStockChain::getStockId)
                    .collect(Collectors.toList());
            boolean noneMatch = stockMap.keySet().stream().noneMatch(s -> list.contains(s));
            if (noneMatch)
                throw new BusinessException("sku: " + sku + "库存记录为空");

            /**
             * 减：已分配库存 加：可用库存
             */
            Integer allot = allotMap.get(sku);

            Integer allotNum = skuOutStockChains.stream().mapToInt(s -> s.getQuantity() == null ? 0 : s.getQuantity())
                    .sum();

            if (allotNum < allot) {
                log.error("orderNo:" + taskNo + ",sku:" + sku + "已分配库存不能为负: allotQuantity[ " + allotNum + " ], allot[ "
                        + allot + " ]");
                throw new RuntimeException(String.format("sku:%s已分配库存不能为负: 已分配库存[%s], 扣已分配[%s]", sku, allotNum, allot));
            }

            TransferStock transferStock = null;
            skuOutStockChains.sort(Comparator.comparing(WhApvOutStockChain::getPickDiff).reversed());
            for (WhApvOutStockChain stockChain : skuOutStockChains) {
                if (allot == null || allot <= 0)
                    break;
                if (stockChain.getQuantity() == null || stockChain.getQuantity() == 0)
                    continue;
                Integer stockId = stockChain.getStockId();
                TransferStock stock = stockMap.get(stockId);
                if (stock == null)
                    continue;
                Integer apvAllot = stockChain.getQuantity();
                Integer allotQty = allot;
                if (apvAllot <= allot)
                    allotQty = apvAllot;
                Integer allotQuantity = stock.getAllotQuantity();
                Integer surplusQuantity = stock.getSurplusQuantity();

                if (allotQty > allotQuantity){
                    log.error("orderNo:" + taskNo + ",sku:" + sku + "已分配库存不能为负: allotQuantity[ " + allotQuantity + " ], allot[ "
                            + allotQty + " ]");
                    throw new RuntimeException(String.format("sku:%s已分配库存不能为负: 已分配库存[%s], 扣已分配[%s]", sku, allotQuantity, allotQty));
                }
                
                TransferStock updateStock = TransferStock.buildUpdateStock(stock);
                updateStock.setAllotQuantity(allotQuantity - allotQty);
                updateStock.setSurplusQuantity(surplusQuantity + allotQty);
                stockList.add(updateStock);

                stockLogs.add(
                        new WhTransitStockLog(sku, stock.getStore(), stock.getSite(), TransferStockLogType.ALLOCATED,
                                stock.getId(), stock.getLocationNumber(), logStep, -allotQty, allotQuantity, taskNo));

                stockLogs.add(
                        new WhTransitStockLog(sku, stock.getStore(), stock.getSite(), TransferStockLogType.USABLE_STOCK,
                                stock.getId(), stock.getLocationNumber(), logStep, allotQty, surplusQuantity, taskNo));

                if (transferStock == null) {
                    transferStock = new TransferStock();
                    transferStock.setSku(sku);
                    transferStock.setStore(stock.getStore());
                    transferStock.setSite(stock.getSite());
                    transferStock.setRemark(stock.getRemark());
                }
                allot = allot - apvAllot;
                
            }
            if (transferStock != null) {
                // 推送fba库存变更到oms
                assembleMsgList(msgList, transferStock, sku, allot);
            }
        }

        if (CollectionUtils.isNotEmpty(stockList)) {
            transferStockService.batchUpdateTransferStock(stockList);
        }
        // 记录中转仓库存日志
        if (CollectionUtils.isNotEmpty(stockLogs)) {
            stockLogService.batchAddWhStockLog(stockLogs);
        }
        // 推送库存变更给oms
        if (CollectionUtils.isNotEmpty(msgList)){
            amqMessageService.batchCreateAmqMessage(msgList);
        }
        return true;
    }

    @Override
    public boolean fbaPickReturn(List<String> skus, Map<String, Integer> pickMap, Map<String, Integer> allotMap,
            String taskNo, List<WhFbaAllocation> allocationList) {
        // 获取拣货库存记录
        List<WhApvOutStockChain> whApvOutStockChains = getWhApvOutStockChains(null, skus, allocationList,
                AssetOrderType.ASN_ORDER.intCode());
        if (CollectionUtils.isEmpty(whApvOutStockChains)) {
            throw new RuntimeException(String.format("sku:%s无库存拣货记录，拣货缺货退库存失败", JSONObject.toJSONString(skus)));
        }

        Map<String, List<WhApvOutStockChain>> apvOutStockMap = whApvOutStockChains.stream()
                .collect(Collectors.groupingBy(WhApvOutStockChain::getSku));

        Map<Integer, TransferStock> stockMap = getTransferStockMap(allocationList);

        List<TransferStock> stockList = new ArrayList<TransferStock>();
        List<WhTransitStockLog> stockLogs = new ArrayList<WhTransitStockLog>();
        /** 库存变更推送 */
        List<AmqMessage> msgList = new ArrayList<>();

        StockLogStep logStep = StockLogStep.FBA_ALLOCATION_GRID;

        Map<String,WhApvOutStockChain> outStockChinaMap = new HashMap<>();

        for (String sku : skus) {

            List<WhApvOutStockChain> skuOutStockList = apvOutStockMap.get(sku);
            if (CollectionUtils.isEmpty(skuOutStockList))
                throw new BusinessException("sku: " + sku + "库存记录为空");
            List<Integer> list = skuOutStockList.stream().map(WhApvOutStockChain::getStockId)
                    .collect(Collectors.toList());
            boolean noneMatch = stockMap.keySet().stream().noneMatch(s -> list.contains(s));
            if (noneMatch)
                throw new BusinessException("sku: " + sku + "库存记录为空");
            /**
             * 减：已分配库存 加：可用库存
             */
            Integer allot = allotMap == null || allotMap.get(sku) == null ? 0 : allotMap.get(sku);

            Integer picked = pickMap == null || pickMap.get(sku) == null ? 0 : pickMap.get(sku);

            Integer pickNum = skuOutStockList.stream()
                    .mapToInt(s -> s.getPickQuantity() == null ? 0 : s.getPickQuantity()).sum();
            Integer allotNum = skuOutStockList.stream().mapToInt(s -> s.getQuantity() == null ? 0 : s.getQuantity())
                    .sum();

            if ((pickNum - picked) < 0) {
                log.error("orderNo:" + taskNo + ",sku:" + sku + "已拣库存不能为负: pickQuantity[ " + pickNum + " ], picked[ "
                        + picked + " ]");
                throw new RuntimeException(String.format("sku:%s已拣库存不能为负: 已拣库存[%s], 扣已拣[%s]", sku, pickNum, picked));
            }
            if ((allotNum - allot) < 0) {
                log.error("orderNo:" + taskNo + ",sku:" + sku + "已分配库存不能为负: allotQuantity[ " + allotNum + " ], allot[ "
                        + allot + " ]");
                throw new RuntimeException(String.format("sku:%s已分配库存不能为负: 已分配库存[%s], 扣已分配[%s]", sku, allotNum, allot));
            }
            Integer returnNum = picked + allot;
            TransferStock transferStock = null;
            skuOutStockList.sort(Comparator.comparing(WhApvOutStockChain::getPickDiff).reversed());
            for (WhApvOutStockChain stockChain : skuOutStockList) {
                if (picked <= 0 && allot <= 0)
                    break;
                if ((stockChain.getPickQuantity() == null || stockChain.getPickQuantity() == 0)
                        && (stockChain.getQuantity() == null && stockChain.getQuantity() == 0))
                    continue;
                Integer stockId = stockChain.getStockId();
                TransferStock stock = stockMap.get(stockId);
                if (stock == null)
                    continue;
                TransferStock updateStock = TransferStock.buildUpdateStock(stock);
                Integer updateSurplus = 0;
                if (picked > 0 && stockChain.getPickQuantity() != null
                        && stockChain.getPickQuantity() > 0) {
                    Integer apvPick = stockChain.getPickQuantity();
                    Integer pickedQty = picked;
                    if (apvPick <= picked) {
                        pickedQty = apvPick;
                    }
                    Integer pickQuantity = stock.getPickQuantity();
                    if (pickedQty > pickQuantity) {
                        log.error("orderNo:" + taskNo + ",sku:" + sku + "已拣库存不能为负: pickQuantity[ " + pickQuantity
                                + " ], pick[ " + pickedQty + " ]");
                        throw new RuntimeException(
                                String.format("sku:%s已拣库存不能为负: 已拣库存[%s], 扣已拣[%s]", sku, pickQuantity, pickedQty));
                    }

                    updateStock.setPickQuantity(pickQuantity - pickedQty);
                    updateSurplus = updateSurplus + pickedQty;
                    stockLogs.add(new WhTransitStockLog(sku, stock.getStore(), stock.getSite(),
                            TransferStockLogType.PICKED_STOCK, stock.getId(), stock.getLocationNumber(), logStep, -pickedQty,
                            pickQuantity, taskNo));
                    picked -= apvPick;
                }

                if (allot > 0 && stockChain.getQuantity() != null && stockChain.getQuantity() > 0) {
                    Integer apvAllot = stockChain.getQuantity();
                    Integer allotQty = allot;
                    if (apvAllot <= allot) {
                        allotQty = apvAllot;
                    }
                    Integer allotQuantity = stock.getAllotQuantity();
                    if (allotQty > allotQuantity){
                        log.error("orderNo:" + taskNo + ",sku:" + sku + "已分配库存不能为负: allotQuantity[ " + allotQuantity + " ], allot[ "
                                + allotQty + " ]");
                        throw new RuntimeException(String.format("sku:%s已分配库存不能为负: 已分配库存[%s], 扣已分配[%s]", sku, allotQuantity, allotQty));
                    }

                    updateStock.setAllotQuantity(allotQuantity - allotQty);
                    updateSurplus = updateSurplus + allotQty;
                    stockLogs.add(new WhTransitStockLog(sku, stock.getStore(), stock.getSite(),
                            TransferStockLogType.ALLOCATED, stock.getId(), stock.getLocationNumber(), logStep, -allotQty,
                            allotQuantity, taskNo));
                    allot -= apvAllot;
                }

                Integer surplusQuantity = stock.getSurplusQuantity();
                updateStock.setSurplusQuantity(surplusQuantity + updateSurplus);
                stockList.add(updateStock);
                stockLogs.add(new WhTransitStockLog(sku, stock.getStore(), stock.getSite(),
                        TransferStockLogType.USABLE_STOCK, stock.getId(), stock.getLocationNumber(), logStep,
                        updateSurplus, surplusQuantity, taskNo));

                Integer gridQ = stockChain.getGirdQuantity() == null ? 0 : stockChain.getGirdQuantity();
                Integer allotQ = stockChain.getQuantity() == null ? 0 : stockChain.getQuantity();

                WhApvOutStockChain stockChina = outStockChinaMap.get(sku + stock.getStockId());
                if (stockChina == null) {
                    stockChina = new WhApvOutStockChain(stock.getSku(), null, null, stock.getStockId(), allotQ,
                            null, gridQ);
                }
                else {
                    stockChina.setQuantity(stockChina.getQuantity() + allotQ);
                    stockChina.setGirdQuantity(stockChina.getGirdQuantity() + gridQ);
                }
                outStockChinaMap.put(sku + stock.getStockId(), stockChina);

                if (transferStock == null) {
                    transferStock = new TransferStock();
                    transferStock.setSku(sku);
                    transferStock.setStore(stock.getStore());
                    transferStock.setSite(stock.getSite());
                    transferStock.setRemark(stock.getRemark());
                }
            }

            if (transferStock != null)
                assembleMsgList(msgList, transferStock, sku, returnNum);

        }
        if (CollectionUtils.isNotEmpty(stockList)) {
            transferStockService.batchUpdateTransferStock(stockList);
        }
        // 记录中转仓库存日志
        if (CollectionUtils.isNotEmpty(stockLogs)) {
            stockLogService.batchAddWhStockLog(stockLogs);
        }
        // 推送库存变更给oms
        if (CollectionUtils.isNotEmpty(msgList)){
            amqMessageService.batchCreateAmqMessage(msgList);
        }
        
        // TODO 生成拣货缺货盘点需求
        if (MapUtils.isNotEmpty(outStockChinaMap)) {
            outStockChinaMap.forEach((k, stock) -> {
                InventoryDemandParam param = new InventoryDemandParam();
                param.setTaskType(PickingTaskType.ASN_PREPARE.intCode());
                param.setDemandType(InventoryDemandType.PICKOUTOFSTOCK.intCode());
                param.setTaskNo(taskNo);
                param.setNeedQuantity(stock.getQuantity());
                param.setPickQuantity(stock.getGirdQuantity());
                param.setSku(stock.getSku());
                param.setStockId(stock.getStockId());
                whPickInventoryDemandService.generatePickInventoryDemand(param);
            });
        }
        return true;
    }

    private void assembleMsgList(List<AmqMessage> msgList,TransferStock stock,String sku,Integer quantity){
        // 推送fba库存变更到oms
        OmsFbaSkuMessage omsFbaSkuMessage = new OmsFbaSkuMessage(stock.getStore(),stock.getSite(),sku, quantity);
        msgList.add(AssembleMessageDataUtils.assembleooFbaStockData(omsFbaSkuMessage));
        // 推送中转仓库存变更到redis
        msgList.add(AssembleMessageDataUtils.assembleDataToFinance(AmqMessageModuleName.PUSH_TRANSFER_STOCK_INFO.getCode(),
                amqMessage -> {
                    amqMessage.setRelevantParam(sku);
                    Map<String,Object> map = new HashMap<>();
                    map.put("sku",sku);
                    map.put("count_surplus",(quantity));
                    map.put("saleChannel",(stock.getRemark()));
                    // 消息体
                    String messageBody = JSONObject.toJSONString(map);
                    return messageBody;
                }));
    }


    @Override
    public List<WhApvOutStockChain> getWhApvOutStockChains(List<Integer> stockOutApvIds, List<String> skuList,
            List<WhFbaAllocation> allocationList, Integer orderType) {
        if (CollectionUtils.isEmpty(stockOutApvIds) && CollectionUtils.isEmpty(allocationList))
            return null;
        List<String> apvNoList = Optional.ofNullable(allocationList).orElse(new ArrayList<>()).stream()
                .map(WhFbaAllocation::getFbaNo).collect(Collectors.toList());

        WhApvOutStockChainQueryCondition query = new WhApvOutStockChainQueryCondition();
        query.setRelevantNos(apvNoList);
        query.setApvIdList(stockOutApvIds);
        query.setOrderType(orderType);
        query.setStatusList(Arrays.asList(WhApvOutStockChainStatusEnum.GRID.intCode(),
                WhApvOutStockChainStatusEnum.PICKED.intCode(), WhApvOutStockChainStatusEnum.TOUCHING.intCode()));
        query.setSkus(skuList);
        // 获取拣货库存记录
        List<WhApvOutStockChain> whApvOutStockChains = whApvOutStockChainService.queryWhApvOutStockChains(query, null);
        return whApvOutStockChains;
    }
}
