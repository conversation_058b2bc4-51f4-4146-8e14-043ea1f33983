package com.estone.warehouse.service.impl;

import com.estone.checkin.bean.MaterialCheckInItem;
import com.estone.checkin.enums.MaterialCheckInStatus;
import com.estone.warehouse.bean.WhPackagingMaterialStock;
import com.estone.warehouse.bean.WhPackagingMaterialStockQueryCondition;
import com.estone.warehouse.service.MaterialUpdateStockService;
import com.estone.warehouse.service.WhPackagingMaterialStockService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.util.List;

/**
 * @Description:耗材库存变动
 * @Author: Yimeil
 * @Date: 2022/12/13 10:07
 * @Version: 1.0.0
 */
@Slf4j
@Service("materialUpdateStockService")
public class MaterialUpdateStockServiceImpl implements MaterialUpdateStockService {

    @Resource
    private WhPackagingMaterialStockService stockService;

    /**
     * 入库加入库中 / 审核通过 加可用，减入库中 / 废弃 减入库中
     * 
     * @param materialCheckInItem
     * @return
     */
    @Override
    public boolean updateStockByCheckIn(MaterialCheckInItem materialCheckInItem, Integer status) {
        if (materialCheckInItem == null || StringUtils.isEmpty(materialCheckInItem.getArticleNumber())
                || status == null)
            return false;

        WhPackagingMaterialStock stock = queryStock(materialCheckInItem.getArticleNumber());
        WhPackagingMaterialStock updateStock = new WhPackagingMaterialStock();
        updateStock.setId(stock.getId());

        Integer qty = materialCheckInItem.getQuantity() == null ? 0 : materialCheckInItem.getQuantity();
        Integer upQuantity = stock.getUpQuantity() == null ? 0 : stock.getUpQuantity();

        MaterialCheckInStatus materialCheckInStatus = MaterialCheckInStatus.build(status.toString());

        switch (materialCheckInStatus) {
            case WAITING_CHECK:
                Integer originalQty = materialCheckInItem.getOriginalQty() == null ? 0
                        : materialCheckInItem.getOriginalQty(); // 编辑初始数量
                qty = qty - originalQty;
                Assert.isTrue(upQuantity + qty >= 0, "入库中库存[" + upQuantity + "]小于编辑修改数量[" + qty + "]，扣入库中库存失败！");
                updateStock.setUpQuantity(upQuantity + qty);
                break;
            case PASSED:
                Assert.isTrue(upQuantity >= qty, "入库中库存[" + upQuantity + "]小于审核通过数量[" + qty + "]，扣入库中库存失败！");
                Integer surplusQuantity = stock.getSurplusQuantity() == null ? 0 : stock.getSurplusQuantity();
                updateStock.setSurplusQuantity(surplusQuantity + qty);
                updateStock.setUpQuantity(upQuantity - qty);
                break;
            /*case DISCARDED:
                Assert.isTrue(upQuantity >= qty, "入库中库存[" + upQuantity + "]小于废弃数量[" + qty + "]，扣入库中库存失败！");
                updateStock.setUpQuantity(upQuantity - qty);
                break;*/
            case REJECT:
                Assert.isTrue(upQuantity >= qty, "入库中库存[" + upQuantity + "]小于驳回数量[" + qty + "]，扣入库中库存失败！");
                updateStock.setUpQuantity(upQuantity - qty);
                break;
        }
        stockService.updateWhPackagingMaterialStock(updateStock);
        return true;
    }

    private WhPackagingMaterialStock queryStock(String articleNumber) {
        WhPackagingMaterialStockQueryCondition query = new WhPackagingMaterialStockQueryCondition();
        query.setMaterialArticleNumber(articleNumber);
        List<WhPackagingMaterialStock> whPackagingMaterialStocks = stockService.queryWhPackagingMaterialStocks(query,
                null);
        Assert.isTrue(CollectionUtils.isNotEmpty(whPackagingMaterialStocks), "货号" + articleNumber + "在耗材库存中不存在！");
        return whPackagingMaterialStocks.get(0);

    }
}
