package com.estone.warehouse.service.impl;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import com.estone.android.domain.AndroidProductDo;
import com.estone.checkin.enums.CheckInWhType;
import com.estone.common.util.CreateTaskNoUtils;
import com.estone.common.util.SystemLogUtils;
import com.estone.sku.bean.*;
import com.estone.sku.service.ExpManageItemService;
import com.estone.sku.service.ExpManageService;
import com.estone.sku.service.UniqueSkuExpRelationService;
import com.estone.statistics.enums.DrpTurnoverOderType;
import com.estone.temu.bean.TemuReturnPackage;
import com.estone.temu.bean.TemuReturnPackageItem;
import com.estone.temu.bean.TemuReturnPackageQueryCondition;
import com.estone.temu.service.TemuReturnPackageItemService;
import com.estone.temu.service.TemuReturnPackageService;
import com.estone.warehouse.aspect.StockServicelock;
import com.estone.warehouse.bean.*;
import com.estone.warehouse.dao.WhAbroadReturnItemDao;
import com.estone.warehouse.enums.AbroadReturnItemStatus;
import com.estone.warehouse.enums.WhBatchReturnSkuStatus;
import com.estone.warehouse.service.*;
import com.whq.tool.component.Pager;
import com.whq.tool.component.StatusCode;
import com.whq.tool.context.DataContextHolder;
import com.whq.tool.exception.BusinessException;
import com.whq.tool.exception.DBErrorCode;
import com.whq.tool.json.ResponseJson;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import com.whq.tool.sqler.SqlerException;

@Service("whAbroadReturnItemService")
public class WhAbroadReturnItemServiceImpl implements WhAbroadReturnItemService {
    private static final Logger logger = LoggerFactory.getLogger(WhAbroadReturnItemServiceImpl.class);

    @Resource
    private WhAbroadReturnItemDao whAbroadReturnItemDao;
    @Resource
    private WhAbroadReturnService whAbroadReturnService;
    @Resource
    private AbroadReturnUpdateStockService abroadReturnUpdateStockService;
    @Resource
    private WhBatchReturnService whBatchReturnService;
    @Resource
    private WhBatchReturnSkuService whBatchReturnSkuService;
    
    @Resource
    private UniqueSkuExpRelationService uniqueSkuExpRelationService;
    
    @Resource
    private ExpManageService expManageService;
    
    @Resource
    private ExpManageItemService expManageItemService;

    @Resource
    private WhBatchReturnUuidService whBatchReturnUuidService;
    @Resource
    private TemuReturnPackageService temuReturnPackageService;
    @Resource
    private TemuReturnPackageItemService temuReturnPackageItemService;
    /**
     * This method corresponds to the database table wh_abroad_return_item
     *
     * @mbggenerated Tue Oct 23 17:36:07 CST 2018
     */
    public WhAbroadReturnItem getWhAbroadReturnItem(Integer id) {
        WhAbroadReturnItem whAbroadReturnItem = whAbroadReturnItemDao.queryWhAbroadReturnItem(id);
        return whAbroadReturnItem;
    }

    /**
     * This method corresponds to the database table wh_abroad_return_item
     *
     * @mbggenerated Tue Oct 23 17:36:07 CST 2018
     */
    public WhAbroadReturnItem getWhAbroadReturnItemDetail(Integer id) {
        WhAbroadReturnItem whAbroadReturnItem = whAbroadReturnItemDao.queryWhAbroadReturnItem(id);
        // 关联查询
        return whAbroadReturnItem;
    }

    /**
     * This method corresponds to the database table wh_abroad_return_item
     *
     * @mbggenerated Tue Oct 23 17:36:07 CST 2018
     */
    public WhAbroadReturnItem queryWhAbroadReturnItem(WhAbroadReturnItemQueryCondition query) {
        Assert.notNull(query);
        WhAbroadReturnItem whAbroadReturnItem = whAbroadReturnItemDao.queryWhAbroadReturnItem(query);
        return whAbroadReturnItem;
    }

    /**
     * This method corresponds to the database table wh_abroad_return_item
     *
     * @mbggenerated Tue Oct 23 17:36:07 CST 2018
     */
    public List<WhAbroadReturnItem> queryAllWhAbroadReturnItems() {
        return whAbroadReturnItemDao.queryWhAbroadReturnItemList();
    }

    /**
     * This method corresponds to the database table wh_abroad_return_item
     *
     * @mbggenerated Tue Oct 23 17:36:07 CST 2018
     */
    public List<WhAbroadReturnItem> queryWhAbroadReturnItems(WhAbroadReturnItemQueryCondition query, Pager pager) {
        Assert.notNull(query);
        if (pager != null && pager.isQueryCount()) {
            int count = whAbroadReturnItemDao.queryWhAbroadReturnItemCount(query);
            pager.setTotalCount(count);
            if (count == 0) {
                return new ArrayList<WhAbroadReturnItem>();
            }
        }
        List<WhAbroadReturnItem> whAbroadReturnItems = whAbroadReturnItemDao.queryWhAbroadReturnItemList(query, pager);
        return whAbroadReturnItems;
    }

    /**
     * This method corresponds to the database table wh_abroad_return_item
     *
     * @mbggenerated Tue Oct 23 17:36:07 CST 2018
     */
    public void createWhAbroadReturnItem(WhAbroadReturnItem whAbroadReturnItem) {
        try {
            if (whAbroadReturnItem != null) {
                whAbroadReturnItem.setCompleteQuantity(0);
                whAbroadReturnItem.setStatus(AbroadReturnItemStatus.WAIT);
            }
            whAbroadReturnItemDao.createWhAbroadReturnItem(whAbroadReturnItem);
        }
        catch (SqlerException e) {
            logger.error(e.getMessage(), e);
            throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
        }
    }

    /**
     * This method corresponds to the database table wh_abroad_return_item
     *
     * @mbggenerated Tue Oct 23 17:36:07 CST 2018
     */
    public void batchCreateWhAbroadReturnItem(List<WhAbroadReturnItem> entityList) {
        if (CollectionUtils.isNotEmpty(entityList)) {
            try {
                whAbroadReturnItemDao.batchCreateWhAbroadReturnItem(entityList);
            }
            catch (SqlerException e) {
                logger.error(e.getMessage(), e);
                throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
            }
        }
    }

    /**
     * This method corresponds to the database table wh_abroad_return_item
     *
     * @mbggenerated Tue Oct 23 17:36:07 CST 2018
     */
    public void deleteWhAbroadReturnItem(Integer id) {
        try {
            whAbroadReturnItemDao.deleteWhAbroadReturnItem(id);
        }
        catch (SqlerException e) {
            logger.error(e.getMessage(), e);
            throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
        }
    }

    /**
     * This method corresponds to the database table wh_abroad_return_item
     *
     * @mbggenerated Tue Oct 23 17:36:07 CST 2018
     */
    public int updateWhAbroadReturnItem(WhAbroadReturnItem whAbroadReturnItem) {
        try {
            return whAbroadReturnItemDao.updateWhAbroadReturnItem(whAbroadReturnItem);
        }
        catch (SqlerException e) {
            logger.error(e.getMessage(), e);
            throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
        }
    }

    /**
     * This method corresponds to the database table wh_abroad_return_item
     *
     * @mbggenerated Tue Oct 23 17:36:07 CST 2018
     */
    public void batchUpdateWhAbroadReturnItem(List<WhAbroadReturnItem> entityList) {
        if (CollectionUtils.isNotEmpty(entityList)) {
            try {
                whAbroadReturnItemDao.batchUpdateWhAbroadReturnItem(entityList);
            }
            catch (SqlerException e) {
                logger.error(e.getMessage(), e);
                throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
            }
        }
    }

    /**
     * 根据关联主表ID删除数据
     */
    @Override
    public void deleteWhAbroadReturnItemByReturnId(Integer id) {
        try {
            whAbroadReturnItemDao.deleteWhAbroadReturnItemByReturnId(id);
        }
        catch (SqlerException e) {
            logger.error(e.getMessage(), e);
            throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
        }
    }

    public int queryWhAbroadReturnItemCount(WhAbroadReturnItemQueryCondition query) {
        return whAbroadReturnItemDao.queryWhAbroadReturnItemCount(query);
    }

    @Override
    @StockServicelock
    public ResponseJson updateWhAbroadReturnItemAndComplete(List<String> skuList, WhAbroadReturnItem whAbroadReturnItem,
            AndroidProductDo domain) {
        ResponseJson response = new ResponseJson(StatusCode.FAIL);
        WhAbroadReturnItem item = new WhAbroadReturnItem();
        item.setId(domain.getAbroadReturnItemId());
        item.setCompleteQuantity(whAbroadReturnItem.getCompleteQuantity());
        item.setStatus(domain.getStatus());

        if (whAbroadReturnItemDao.updateWhAbroadReturnItem(item) > 0) {
            WhAbroadReturnItemQueryCondition returnItemQueryCondition = new WhAbroadReturnItemQueryCondition();
            returnItemQueryCondition.getStatusList().add(AbroadReturnItemStatus.ERROR_COMPLETE);
            returnItemQueryCondition.getStatusList().add(AbroadReturnItemStatus.WAIT);
            returnItemQueryCondition.setReturnId(domain.getAbroadReturnId());
            int count = queryWhAbroadReturnItemCount(returnItemQueryCondition);
            if (count == 0) {
                whAbroadReturnService.completeWhAbroadReturn(domain.getAbroadReturnId(), false);
            }
            if (!abroadReturnUpdateStockService.updateStockByAbroadReturnUp(whAbroadReturnItem, false,domain.getSku())) {

                throw new RuntimeException("海外退件上架加可用库存失败！");
            }
        }

        response.setStatus(StatusCode.SUCCESS);
        return response;
    }

    @Override
    @StockServicelock
    public ResponseJson updateNormalWhAbroadReturnItemAndComplete(List<String> skuList, WhAbroadReturnItem whAbroadReturnItem,
                                                            AndroidProductDo domain, WhAbroadReturn whAbroadReturn,WhBatchReturn whBatchReturn,WhUniqueSku uniqueSku) {
        ResponseJson response = new ResponseJson(StatusCode.FAIL);
        WhAbroadReturnItem item = new WhAbroadReturnItem();
        item.setId(domain.getAbroadReturnItemId());
        item.setCompleteQuantity(whAbroadReturnItem.getCompleteQuantity());
        item.setStatus(domain.getStatus());
        item.setStockId(whAbroadReturnItem.getStockId());

        String sku = whAbroadReturnItem.getSku();
        // 退货批次是否完结
        //获取退货批次明细数据
        List<WhBatchReturnSku> whBatchReturnSkus = whBatchReturn.getWhBatchReturnSkus();
        if (CollectionUtils.isEmpty(whBatchReturnSkus)){
            throw new RuntimeException("海外退件批次明细数据不存在！");
        }

        WhBatchReturnSku whBatchReturnSku = whBatchReturnSkus.stream().
                filter(w -> StringUtils.equalsIgnoreCase(w.getSku(),sku)).findFirst().orElse(new WhBatchReturnSku());
        int returnningQuantity = whBatchReturnSku.getReturnningQuantity() == null ? 0 : whBatchReturnSku.getReturnningQuantity();
        if (returnningQuantity == 0){
            throw new RuntimeException("海外退件批次无 返架中数量！");
        }
        int completeQuantity = whBatchReturnSku.getCompleteQuantity() == null ? 0 : whBatchReturnSku.getCompleteQuantity();
        if (returnningQuantity < whAbroadReturnItem.getUpdateQuantity()){
            throw new RuntimeException("已返架数量大于 返架中数量！");
        }
        WhBatchReturnSku returnSku = new WhBatchReturnSku();
        returnSku.setId(whBatchReturnSku.getId());
        // 已完成数量
        returnSku.setCompleteQuantity(completeQuantity + whAbroadReturnItem.getUpdateQuantity());
        // 返架中数量
        returnSku.setReturnningQuantity(returnningQuantity - whAbroadReturnItem.getUpdateQuantity());
        if (whBatchReturnSku.getQuantity().equals( returnSku.getCompleteQuantity())){
            returnSku.setStatus(WhBatchReturnSkuStatus.COMPLETE.intCode());

            whBatchReturnSku.setCompleteQuantity(returnSku.getCompleteQuantity());
            whBatchReturnSku.setReturnningQuantity(returnSku.getReturnningQuantity());
            // 判断批次所有数量是否都已返架
            if (whBatchReturnSkus.stream().allMatch(w -> w.getQuantity().equals(w.getCompleteQuantity()))){
                whBatchReturnService.completeWhBatchReturn(whBatchReturn, false);
            }
        }
        whBatchReturnSkuService.updateWhBatchReturnSku(returnSku);

        // 返架单
        if (whAbroadReturnItemDao.updateWhAbroadReturnItem(item) > 0) {
            WhAbroadReturnItemQueryCondition returnItemQueryCondition = new WhAbroadReturnItemQueryCondition();
            returnItemQueryCondition.getStatusList().add(AbroadReturnItemStatus.ERROR_COMPLETE);
            returnItemQueryCondition.getStatusList().add(AbroadReturnItemStatus.WAIT);
            returnItemQueryCondition.setReturnId(domain.getAbroadReturnId());
            int count = queryWhAbroadReturnItemCount(returnItemQueryCondition);
            if (count == 0) {
                whAbroadReturnService.completeWhAbroadReturn(domain.getAbroadReturnId(), false);
            }
        }

        List<WhBatchReturnApvItem> whBatchReturnApvItems = whBatchReturn.getWhBatchReturnApvItems();
        if(CollectionUtils.isEmpty(whBatchReturnApvItems)){
            throw new RuntimeException("海外退件上架无退货批次apv数据！");
        }
        List<WhBatchReturnApvItem> skuApvItem = whBatchReturnApvItems.stream().filter(w -> StringUtils.equalsIgnoreCase(w.getSku(), sku)).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(skuApvItem)){
            throw new RuntimeException("sku["+sku+"]海外退件上架无退货批次apv数据！");
        }

        if (whBatchReturn.transferReturn()){
            WhBatchReturnUuidQueryCondition whBatchReturnUuidQueryCondition = new WhBatchReturnUuidQueryCondition();
            whBatchReturnUuidQueryCondition.setOrderNo(whAbroadReturn.getOrderNo());
            //whBatchReturnUuidQueryCondition.setApvNo(uniqueSku.getApvNo());
            whBatchReturnUuidQueryCondition.setUuid(uniqueSku.getSku()+"="+uniqueSku.getUuid());
            WhBatchReturnUuid whBatchReturnUuid = whBatchReturnUuidService.queryWhBatchReturnUuid(whBatchReturnUuidQueryCondition);
            if(whBatchReturnUuid == null || whBatchReturnUuid.getStockId() == null){
                throw new RuntimeException("当前唯一码未播种！");
            }
            abroadReturnUpdateStockService.updateStockByFbaAbroadReturnUp(whAbroadReturnItem,whBatchReturnUuid);
        } else {
            if (!abroadReturnUpdateStockService.updateStockByNormalAbroadReturnUp(whAbroadReturnItem,domain.getSku())) {
                throw new RuntimeException("海外退件上架加本地可用库存失败！");
            }
        }
        // TODO 添加保质期批次
        doBindExpBatchNoAndUniqSku(whBatchReturn.getId(),whBatchReturn.getOrderNo(), domain.getSku(), whAbroadReturnItem);

        // 更新temu退货单数量
        if (whBatchReturn.getWarehouseId() != null && whBatchReturn.getWarehouseId().equals(4)) {
            List<String> apvNoList = whBatchReturnApvItems.stream().map(WhBatchReturnApvItem::getApvNo).distinct()
                    .collect(Collectors.toList());
            updateTemuReturnQuantity(apvNoList, sku);
        }
        response.setStatus(StatusCode.SUCCESS);
        return response;
    }

    /**
     * 唯一码绑定保质期批次
     * 
     * @param relationId
     * @param uniqSku
     * @param whAbroadReturnItem
     */
    @Override
    public void doBindExpBatchNoAndUniqSku(Integer relationId, String relationNo, String uniqSku, WhAbroadReturnItem whAbroadReturnItem) {
        if (relationId == null || StringUtils.isEmpty(uniqSku) || !StringUtils.contains(uniqSku, "=") || whAbroadReturnItem == null
                || whAbroadReturnItem.getUpdateQuantity() == null || whAbroadReturnItem.getUpdateQuantity() == 0)
            return;
        Integer updateQuantity = whAbroadReturnItem.getUpdateQuantity();
        String sku = StringUtils.split(uniqSku, "=")[0];
        String uuid = StringUtils.split(uniqSku, "=")[1];
        // 查询唯一码保质期
        UniqueSkuExpRelationQueryCondition query = new UniqueSkuExpRelationQueryCondition();
        query.setSku(sku);
        List<UniqueSkuExpRelation> expRelations = uniqueSkuExpRelationService.queryUniqueSkuExpRelations(query, null);
        if (CollectionUtils.isEmpty(expRelations))
            return;
        UniqueSkuExpRelation uniqueSkuExpRelation = expRelations.stream()
                .filter(w -> StringUtils.equalsIgnoreCase(w.getUuid(), uuid)).findFirst().orElse(null);
        if (uniqueSkuExpRelation == null)
            return;

        UniqueSkuExpRelation skuExpRelation = expRelations.stream()
                .filter(w -> !StringUtils.equalsIgnoreCase(w.getUuid(), uuid)
                        && Objects.equals(w.getRelationId(), relationId)
                        && Objects.equals(w.getRelationNo(), relationNo)
                        && w.getExpStr().equalsIgnoreCase(uniqueSkuExpRelation.getExpStr())
                        && StringUtils.isNotEmpty(w.getExpNo()))
                .findFirst().orElse(null);

        // 如果唯一码存在绑定的保质期批次，则退件库存加到当前批次里；不存在则新增一个批次
        ExpManage existManage = null;
        ExpManage expManage = new ExpManage();
        List<UniqueSkuExpRelation> updateList = new ArrayList<>();

        String batchNo = StringUtils.isNotEmpty(uniqueSkuExpRelation.getExpNo()) ? uniqueSkuExpRelation.getExpNo()
                : skuExpRelation != null && StringUtils.isNotEmpty(skuExpRelation.getExpNo())
                        ? skuExpRelation.getExpNo()
                        : null;
        if (StringUtils.isNotEmpty(batchNo)) {
            ExpManageQueryCondition manageQuery = new ExpManageQueryCondition();
            manageQuery.setBatchNo(batchNo);
            existManage = expManageService.queryExpManage(manageQuery);
        }

        if (existManage != null && existManage.getId() != null) {
            expManage.setId(existManage.getId());
            expManage.setReturnQuantity(
                    (existManage.getReturnQuantity() == null ? 0 : existManage.getReturnQuantity()) + updateQuantity);
            expManage.setQuantity((existManage.getQuantity() == null ? 0 : existManage.getQuantity()) + updateQuantity);
            expManage.setBatchNo(existManage.getBatchNo());
            expManageService.updateExpManage(expManage);
            SystemLogUtils.EXP_MANAGE_LOG.log(expManage.getId(), "海外退件上架修改保质期批次",
                    new String[][] { { "唯一码", uniqSku }, { "数量", expManage.getReturnQuantity() + "" } });
        }
        else {
            expManage.setStockId(whAbroadReturnItem.getStockId());
            expManage.setBatchNo(CreateTaskNoUtils.createBatNo("EXP", "expManage"));
            expManage.setReturnQuantity(updateQuantity);
            expManage.setQuantity(updateQuantity);
            expManage.setCheckInType(CheckInWhType.LOCAL.intCode());
            expManage.setSource(DrpTurnoverOderType.RETURN_ORDER.intCode());
            expManage.setSku(sku);
            expManage.setDays(expRelations.get(0).getDays());
            expManage.setProDate(expRelations.get(0).getProDate());
            expManage.setExpDate(expRelations.get(0).getExpDate());
            expManage.setCreateBy(DataContextHolder.getUserId());
            expManage.setCreationDate(new Timestamp(System.currentTimeMillis()));
            // 创建保质期批次
            expManageService.createExpManage(expManage);
            SystemLogUtils.EXP_MANAGE_LOG.log(expManage.getId(), "海外退件上架新增保质期批次",
                    new String[][] { { "唯一码", uniqSku }, { "数量", expManage.getReturnQuantity() + "" } });

        }
        expRelations.forEach(expRelation -> {
            if (expRelation.getUuid().equals(uuid)) {
                UniqueSkuExpRelation relation = new UniqueSkuExpRelation();
                relation.setId(expRelation.getId());
                relation.setExpNo(expManage.getBatchNo());
                updateList.add(relation);
            }
        });

        // 批次明细
        ExpManageItem manageItem = new ExpManageItem();
        manageItem.setBatchNo(expManage.getBatchNo());
        manageItem.setType(DrpTurnoverOderType.RETURN_ORDER.intCode());
        manageItem.setQuantity(updateQuantity);
        manageItem.setRelationId(relationId);
        manageItem.setCreationDate(new Timestamp(System.currentTimeMillis()));
        expManageItemService.createExpManageItem(manageItem);

        // 唯一码关联
        uniqueSkuExpRelationService.batchUpdateUniqueSkuExpRelation(updateList);
    }


    /**
     * 更新temu退货单数量
     * 
     * @param apvNoList
     * @param sku
     */
    @Override
    public void updateTemuReturnQuantity(List<String> apvNoList, String sku) {
        if (CollectionUtils.isEmpty(apvNoList) || StringUtils.isBlank(sku))
            return;

        TemuReturnPackageQueryCondition query = new TemuReturnPackageQueryCondition();
        query.setPackageNoList(apvNoList);
        List<TemuReturnPackage> temuReturnPackages = temuReturnPackageService.queryTemuReturnPackages(query, null);
        if (CollectionUtils.isEmpty(temuReturnPackages)){
            query.setPurchaseSubOrderSn(StringUtils.join(apvNoList, ","));
            query.setPackageNoList(null);
            temuReturnPackages = temuReturnPackageService.queryTemuReturnPackages(query, null);
        }
        if (CollectionUtils.isEmpty(temuReturnPackages))
            return;

        temuReturnPackages.forEach(t -> {
            List<TemuReturnPackageItem> itemList = t.getItemList();
            if (CollectionUtils.isEmpty(itemList))
                return;
            itemList.forEach(i -> {
                int qty = i.getQuantity() == null ? 0 : i.getQuantity();
                int wmsReturnQty = i.getWmsReturnQty() == null ? 0 : i.getWmsReturnQty();
                if (wmsReturnQty + 1 > qty || !sku.equalsIgnoreCase(i.getSku()))
                    return;

                TemuReturnPackageItem updateItem = new TemuReturnPackageItem();
                updateItem.setId(i.getId());
                updateItem.setWmsReturnQty(wmsReturnQty + 1);
                updateItem.setWmsReturnBy(DataContextHolder.getUserId());
                updateItem.setWmsReturnTime(new Timestamp(System.currentTimeMillis()));
                temuReturnPackageItemService.updateTemuReturnPackageItem(updateItem);
                i.setWmsReturnQty(updateItem.getWmsReturnQty());
            });

            int returnQty = itemList.stream().mapToInt(i -> i.getWmsReturnQty() == null ? 0 : i.getWmsReturnQty())
                    .sum();
            TemuReturnPackage updateOrder = new TemuReturnPackage();
            updateOrder.setId(t.getId());
            updateOrder.setWmsReturnQty(returnQty);
            temuReturnPackageService.updateTemuReturnPackage(updateOrder);
        });
    }
}