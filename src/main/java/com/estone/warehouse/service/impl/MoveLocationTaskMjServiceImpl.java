package com.estone.warehouse.service.impl;

import com.alibaba.fastjson.JSON;
import com.estone.allocation.bean.WhApvAllocation;
import com.estone.allocation.bean.WhApvAllocationDemand;
import com.estone.allocation.bean.WhApvAllocationDemandQueryCondition;
import com.estone.allocation.bean.WhApvAllocationQueryCondition;
import com.estone.allocation.enums.AllocationDemandTaskStatus;
import com.estone.allocation.enums.AllocationOrderStatusEnum;
import com.estone.allocation.service.WhApvAllocationDemandService;
import com.estone.allocation.service.WhApvAllocationOrderService;
import com.estone.allocation.service.WhApvAllocationService;
import com.estone.allocation.util.WarehouseProperties;
import com.estone.checkin.bean.WhCheckIn;
import com.estone.checkin.bean.WhCheckInQueryCondition;
import com.estone.checkin.enums.CheckInStatus;
import com.estone.checkin.service.WhCheckInService;
import com.estone.common.util.CreateTaskNoUtils;
import com.estone.common.util.HttpUtils;
import com.estone.common.util.SystemLogUtils;
import com.estone.picking.enums.PickingTaskWarehouseType;
import com.estone.sku.bean.WhSku;
import com.estone.sku.bean.WhSkuQueryCondition;
import com.estone.sku.service.WhSkuService;
import com.estone.system.rabbitmq.bean.AmqMessage;
import com.estone.system.rabbitmq.dao.AmqMessageDao;
import com.estone.system.rabbitmq.model.ProductSkuMessage;
import com.estone.system.rabbitmq.utils.AssembleMessageDataUtils;
import com.estone.warehouse.aspect.StockServicelock;
import com.estone.warehouse.bean.*;
import com.estone.warehouse.dao.WhLocationDao;
import com.estone.warehouse.domain.MoveLocationTaskDo;
import com.estone.warehouse.enums.BoxStatus;
import com.estone.warehouse.enums.BoxType;
import com.estone.warehouse.enums.MoveLocationTaskStatus;
import com.estone.warehouse.service.*;
import com.whq.tool.component.StatusCode;
import com.whq.tool.context.DataContextHolder;
import com.whq.tool.json.ResponseJson;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.util.*;
import java.util.stream.Collectors;

@Service("moveLocationTaskMjService")
@Slf4j
public class MoveLocationTaskMjServiceImpl implements MoveLocationTaskMjService {
    private Logger logger = LoggerFactory.getLogger(MoveLocationTaskMjServiceImpl.class);
    @Autowired
    private WhSkuService whSkuService;

    @Autowired
    private WhLocationService whLocationService;

    @Resource
    private WhLocationDao whLocationDao;

    @Autowired
    private WhBoxService whBoxService;

    @Autowired
    private WhStockService whStockService;

    @Autowired
    private MoveLocationTaskService moveLocationTaskService;

    @Autowired
    private MoveLocationTaskStockService moveLocationTaskStockService;

    @Autowired
    private MoveLocationTaskItemService moveLocationTaskItemService;

    @Resource
    private AmqMessageDao amqMessageDao;

    @Autowired
    private WhApvAllocationDemandService whApvAllocationDemandService;
    @Autowired
    private WhApvAllocationService whApvAllocationService;
    @Autowired
    private WhApvAllocationOrderService whApvAllocationOrderService;

    @Autowired
    private WhCheckInService whCheckInService;

    /**
     * 新建
     *
     * @param skus
     * @param whSkus
     * @param shelf
     */
    @Override
    @StockServicelock
    public void createTask(List<String> skus, List<WhSku> whSkus, String shelf) {
        checkOldWarehouseShelf(shelf);
        WhStockQueryCondition stockQuery = new WhStockQueryCondition();
        stockQuery.setSkus(skus);
        List<WhStock> whStocks = whStockService.queryWhStocks(stockQuery, null);
        if (CollectionUtils.isEmpty(whStocks)) {
            throw new RuntimeException("库存不存在");
        } else {
            List<String> list = whStocks.stream().filter(item -> (item.getAllotQuantity() != null && item.getAllotQuantity() != 0)
            ).map(item -> item.getSku()).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(list)) {
                throw new RuntimeException(JSON.toJSONString(list) + "已分配不为0，不能操作！");
            }
            String mes = checkAllocationOrder(skus);
            if (StringUtils.isNotBlank(mes)) {
                throw new RuntimeException(mes);
            }
        }
        MoveLocationTask task = new MoveLocationTask();
        task.setTaskNo(CreateTaskNoUtils.createMoveLocationTaskNo());
        task.setTaskStatus(MoveLocationTaskStatus.WAIT_RECEIVE.intCode());
        task.setLocationCount(whSkus.stream().map(item -> item.getLocationNumber()).collect(Collectors.toSet()).size());
        task.setSkuCount(skus.size());
        task.setShelf(shelf);
        moveLocationTaskService.createMoveLocationTask(task);

        Map<String, WhStock> stockMap = whStocks.stream().collect(Collectors.toMap(WhStock::getSku, item -> item));
        List<MoveLocationTaskItem> items = new ArrayList<>();
        for (WhSku whSku : whSkus) {
            MoveLocationTaskItem item = new MoveLocationTaskItem();
            item.setSku(whSku.getSku());
            item.setTaskNo(task.getTaskNo());
            item.setLocation(whSku.getLocationNumber());
            item.setStatus(MoveLocationTaskStatus.WAIT_RECEIVE.intCode());
            item.setQuantity(stockMap.get(whSku.getSku()) == null ? 0 : stockMap.get(whSku.getSku()).getSurplusQuantity());
            items.add(item);
        }
        moveLocationTaskItemService.batchCreateMoveLocationTaskItem(items);
        moveLocationTaskStockService.frozenStock(items);
        SystemLogUtils.MOVELOCATIONTASKLOG.log(task.getId(), "新建库位移库单");
    }

    /**
     * 编辑
     *
     * @param skus
     * @param domain
     */
    @Override
    @StockServicelock
    public void updateTask(List<String> skus, MoveLocationTaskDo domain) {
        MoveLocationTask task = domain.getMoveLocationTask();
        List<WhSku> whSkus = domain.getWhSkus();
        String shelf = domain.getShelf();
        List<String> locations = domain.getLocations();
        Set<String> set = task.getItems().stream().map(item -> item.getLocation()).collect(Collectors.toSet());
        checkOldWarehouseShelf(shelf);
        List<String> dels = set.stream().filter(item -> !locations.contains(item)).collect(Collectors.toList());
        List<String> adds = locations.stream().filter(item -> !set.contains(item)).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(adds)) {
            MoveLocationTaskItemQueryCondition queryCondition = new MoveLocationTaskItemQueryCondition();
            queryCondition.setLocations(adds);
            List<MoveLocationTaskItem> items = moveLocationTaskItemService.queryMoveLocationTaskItems(queryCondition, null);
            //0-待领取 1-拣货中 2-待上架 4-已完成 5-已废弃
            Set<String> locationSet = items.stream().filter(item ->
                    item.getStatus().equals(MoveLocationTaskStatus.WAIT_RECEIVE.intCode()) ||
                            item.getStatus().equals(MoveLocationTaskStatus.PICKING.intCode()) ||
                            item.getStatus().equals(MoveLocationTaskStatus.WAITTING_UP.intCode()) ||
                            item.getStatus().equals(MoveLocationTaskStatus.UPING.intCode())
            ).map(item -> item.getLocation()).collect(Collectors.toSet());
            if (CollectionUtils.isNotEmpty(locationSet)) {
                throw new RuntimeException(JSON.toJSONString(locationSet) + "还有未完成的移库单，不能操作！");
            }
        }

        List<MoveLocationTaskItem> delItems = new ArrayList<>();
        for (MoveLocationTaskItem item : task.getItems()) {
            if (dels.contains(item.getLocation())) {
                delItems.add(item);
            }
        }

        WhStockQueryCondition stockQuery = new WhStockQueryCondition();
        stockQuery.setSkus(skus);
        List<WhStock> whStocks = whStockService.queryWhStocks(stockQuery, null);
        if (CollectionUtils.isEmpty(whStocks)) {
            return;
        } else {
            List<String> list = whStocks.stream().filter(item -> (item.getAllotQuantity() != null && item.getAllotQuantity() != 0)
            ).map(item -> item.getSku()).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(list)) {
                throw new RuntimeException(JSON.toJSONString(list) + "已分配不为0，不能操作！");
            }
            String mes = checkAllocationOrder(skus);
            if (StringUtils.isNotBlank(mes)) {
                throw new RuntimeException(mes);
            }
        }
        Map<String, WhStock> stockMap = whStocks.stream().collect(Collectors.toMap(WhStock::getSku, item -> item));

        List<MoveLocationTaskItem> items = new ArrayList<>();
        for (WhSku whSku : whSkus) {
            if (adds.contains(whSku.getLocationNumber())) {
                MoveLocationTaskItem item = new MoveLocationTaskItem();
                item.setSku(whSku.getSku());
                item.setTaskNo(task.getTaskNo());
                item.setLocation(whSku.getLocationNumber());
                item.setStatus(MoveLocationTaskStatus.WAIT_RECEIVE.intCode());
                item.setQuantity(stockMap.get(whSku.getSku()) == null ? 0 : stockMap.get(whSku.getSku()).getSurplusQuantity());
                items.add(item);
            }
        }

        task.setLocationCount(task.getLocationCount() + (items.stream().map(item -> item.getLocation()).collect(Collectors.toSet()).size() - dels.size()));
        task.setSkuCount(task.getSkuCount() + (items.size() - delItems.size()));
        task.setShelf(shelf);
        updateMoveLocationTask(task);

        if (CollectionUtils.isNotEmpty(items)) {
            moveLocationTaskItemService.batchCreateMoveLocationTaskItem(items);
            moveLocationTaskStockService.frozenStock(items);
        }
        if (CollectionUtils.isNotEmpty(delItems)) {
            delItems.forEach(item -> moveLocationTaskItemService.deleteMoveLocationTaskItem(item.getId()));
            moveLocationTaskStockService.cancelStock(delItems);
        }
        SystemLogUtils.MOVELOCATIONTASKLOG.log(task.getId(), "编辑库位移库单");
    }

    /**
     * 领取任务
     *
     * @return
     */
    @Override
    public ResponseJson doReceivd() {
        ResponseJson response = new ResponseJson(StatusCode.FAIL);
        MoveLocationTask task = null;
        MoveLocationTaskQueryCondition query = new MoveLocationTaskQueryCondition();
        query.setTaskStatus(MoveLocationTaskStatus.PICKING.intCode());
        query.setPickPerson(DataContextHolder.getUserId());
        task = queryMoveLocationTask(query);
        if (task == null) {
            query.setPickPerson(null);
            query.setTaskStatus(MoveLocationTaskStatus.WAIT_RECEIVE.intCode());
            query.setOderBy(" id ASC");
            List<MoveLocationTask> tasks = moveLocationTaskService.queryMoveLocationTasks(query, null);
            task = CollectionUtils.isEmpty(tasks) ? null : tasks.get(0);
        }
        if (task == null) {
            response.setMessage("没有数据");
            return response;
        }
        task = getMoveLocationTaskDetail(task.getId());
        if (MoveLocationTaskStatus.PICKING.intCode().equals(task.getTaskStatus())) {
            task.buildTask();
            response.setMessage(JSON.toJSONString(task));
            response.setStatus(StatusCode.SUCCESS);
            return response;
        }
        task.setTaskStatus(MoveLocationTaskStatus.PICKING.intCode());
        task.setPickPerson(DataContextHolder.getUserId());
        task.setReceiveDate(new Timestamp(System.currentTimeMillis()));
        updateMoveLocationTask(task);

        task.getItems().forEach(item -> item.setStatus(MoveLocationTaskStatus.PICKING.intCode()));
        moveLocationTaskItemService.batchUpdateMoveLocationTaskItem(task.getItems());
        SystemLogUtils.MOVELOCATIONTASKLOG.log(task.getId(), "领取任务");
        task.buildTask();
        response.setMessage(JSON.toJSONString(task));
        response.setStatus(StatusCode.SUCCESS);
        return response;
    }

    /**
     * 绑定周转筐
     *
     * @param taskId
     * @param boxNo
     * @return
     */
    @Override
    public ResponseJson doBindingBox(Integer taskId, String boxNo) {
        ResponseJson response = new ResponseJson(StatusCode.FAIL);
        MoveLocationTask task = moveLocationTaskService.getMoveLocationTaskDetail(taskId);
        if (task == null) {
            response.setMessage("没有数据");
            return response;
        }
        if (!MoveLocationTaskStatus.PICKING.intCode().equals(task.getTaskStatus())) {
            response.setMessage("不是拣货中不允许操作");
            return response;
        }
        if (!boxNo.startsWith("YKB")) {
            response.setMessage("不是美景的移库周转筐不允许操作");
            return response;
        }
        // 周转筐校验
        WhBoxQueryCondition query = new WhBoxQueryCondition();
        query.setBoxNo(boxNo);
        query.setStatus(BoxStatus.NOT_USED.intCode());
        WhBox whBox = whBoxService.queryWhBox(query);
        if (whBox == null) {
            response.setMessage("周转筐不存在或者不是未使用状态");
            return response;
        }
        if (!BoxType.YK.intCode().equals(whBox.getType())) {
            response.setMessage("不是移库周转筐不允许操作!");
            return response;
        }
        if (StringUtils.isNotBlank(boxNo)) {
            String[][] logs = new String[][]{{"库位移库单绑定周转筐", ""},
                    {"relationNo", task.getId().toString()}};
            int updated = whBoxService.updateWhBoxOfBinding(boxNo, task.getId().toString(), logs);
            if (updated >= 1) {
                logger.info("绑定周转码: relationNo[" + task.getId() + "], boxNo["
                        + task.getBoxNo() + "], boxUpdated[" + updated + "]");
            }
        }
        task.setBoxNo(boxNo);
        moveLocationTaskService.updateMoveLocationTask(task);
        SystemLogUtils.MOVELOCATIONTASKLOG.log(task.getId(), "库位移库单绑定周转筐");
        response.setStatus(StatusCode.SUCCESS);
        return response;
    }

    /**
     * 拣货下一条
     *
     * @param taskId
     * @param location
     * @return
     */
    @Override
    public ResponseJson doPicking(Integer taskId, String location) {
        ResponseJson response = new ResponseJson(StatusCode.FAIL);
        MoveLocationTask task = getMoveLocationTaskDetail(taskId);
        if (task == null) {
            response.setMessage("没有数据");
            return response;
        }
        if (!MoveLocationTaskStatus.PICKING.intCode().equals(task.getTaskStatus())) {
            throw new RuntimeException("不是拣货中不允许操作！");
        }
        List<MoveLocationTaskItem> items = task.getItems();
        List<MoveLocationTaskItem> upItems = task.getItems().stream().filter(item -> item.getLocation().equals(location)
                && item.getStatus().equals(MoveLocationTaskStatus.PICKING.intCode()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(upItems)) {
            throw new RuntimeException("数据不存在或已拣货！");
        }
        upItems.forEach(item ->
                {
                    item.setStatus(MoveLocationTaskStatus.WAITTING_UP.intCode());
                    item.setPickingDate(new Timestamp(System.currentTimeMillis()));
                }
        );
        List<MoveLocationTaskItem> complets = items.stream()
                .filter(item -> !item.getLocation().equals(location)
                        && item.getStatus().equals(MoveLocationTaskStatus.WAITTING_UP.intCode()))
                .collect(Collectors.toList());
        if (complets.size() == items.size() - upItems.size()) {
            task.setTaskStatus(MoveLocationTaskStatus.WAITTING_UP.intCode());
            task.setPickingEndDate(new Timestamp(System.currentTimeMillis()));
            updateMoveLocationTask(task);
        }
        moveLocationTaskItemService.batchUpdateMoveLocationTaskItem(upItems);
        //moveLocationTaskStockService.upStock(upItems);
        if (complets.size() == items.size() - upItems.size()) {
            SystemLogUtils.MOVELOCATIONTASKLOG.log(task.getId(), "库位移库单拣货完成");
        }
        response.setStatus(StatusCode.SUCCESS);
        return response;
    }

    /**
     * 上架领取
     *
     * @param boxNo
     * @return
     */
    @Override
    public ResponseJson doUpReceivd(String boxNo) {
        ResponseJson response = new ResponseJson(StatusCode.FAIL);
        MoveLocationTaskQueryCondition query = new MoveLocationTaskQueryCondition();
        query.setTaskStatus(MoveLocationTaskStatus.UPING.intCode());
        query.setUpPerson(DataContextHolder.getUserId());
        MoveLocationTask exist = queryMoveLocationTask(query);
        if (exist != null) {
            if (!exist.getBoxNo().equals(boxNo)) {
                response.setMessage("还有未完成的任务：" + exist.getTaskNo());
                return response;
            } else {
                exist = getMoveLocationTaskDetail(exist.getId());
                exist.buildTask();
                response.setMessage(JSON.toJSONString(exist));
                response.setStatus(StatusCode.SUCCESS);
                return response;
            }
        }
        if (!boxNo.startsWith("YKB")) {
            response.setMessage("不是美景的移库周转筐不允许操作");
            return response;
        }
        WhBox whBox = whBoxService.queryWhBoxByBoxNo(boxNo);
        if (whBox == null) {
            response.setMessage("周转框");
            return response;
        }
        if (!whBox.getType().equals(BoxType.YK.intCode())) {
            response.setMessage("周转框类型不对");
            return response;
        }
        String relationNo = whBox.getRelationNo();
        if (StringUtils.isBlank(relationNo)) {
            response.setMessage("周转框没有绑定数据");
            return response;
        }
        MoveLocationTask task = getMoveLocationTaskDetail(Integer.valueOf(relationNo));
        if (task == null) {
            response.setMessage("没有数据");
            return response;
        }
        if (!MoveLocationTaskStatus.WAITTING_UP.intCode().equals(task.getTaskStatus())) {
            response.setMessage("不是待上架不允许操作");
            return response;
        }
        whBox.setPresentUser(DataContextHolder.getUserId());
        SystemLogUtils.BOXLOG.log(whBox.getId(), "上架领取");
        whBoxService.updateWhBox(whBox);
        task.setUpPerson(DataContextHolder.getUserId());
        task.setTaskStatus(MoveLocationTaskStatus.UPING.intCode());
        updateMoveLocationTask(task);
        SystemLogUtils.MOVELOCATIONTASKLOG.log(task.getId(), "库位移库单上架领取");
        task.buildTask();
        response.setMessage(JSON.toJSONString(task));
        response.setStatus(StatusCode.SUCCESS);
        return response;
    }

    /**
     * 上架
     *
     * @param skus
     * @param task
     * @param location
     */
    @Override
    @StockServicelock
    public void doUp(List<String> skus, MoveLocationTask task, String location, String newLocation) {
        if (StringUtils.isNotBlank(task.getShelf())) {
            if (!newLocation.startsWith(task.getShelf())) {
                throw new RuntimeException("上架库位与移库任务中货架信息不符！");
            }
        }
        if (!MoveLocationTaskStatus.UPING.intCode().equals(task.getTaskStatus())) {
            throw new RuntimeException("不是上架中不允许操作！");
        }
        List<MoveLocationTaskItem> items = task.getItems();
        List<MoveLocationTaskItem> upItems = task.getItems().stream().filter(item -> item.getLocation().equals(location)
                && item.getStatus().equals(MoveLocationTaskStatus.WAITTING_UP.intCode()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(upItems)) {
            throw new RuntimeException("数据不存在或已上架！");
        }
        WhLocationQueryCondition locationQuery = new WhLocationQueryCondition();
        locationQuery.setLocation(location);
        WhLocation whLocation = whLocationService.queryWhLocation(locationQuery);
        if (whLocation == null) {
            throw new RuntimeException("旧库位不存在！");
        }
        checkOldWarehouseLocation(newLocation);
        // 校验老仓库位

        upItems.forEach(item ->
                {
                    item.setStatus(MoveLocationTaskStatus.COMPLETE.intCode());
                    item.setUpDate(new Timestamp(System.currentTimeMillis()));
                    item.setNewLocation(newLocation);
                }
        );
        List<MoveLocationTaskItem> complets = items.stream()
                .filter(item -> !item.getLocation().equals(location)
                        && item.getStatus().equals(MoveLocationTaskStatus.COMPLETE.intCode()))
                .collect(Collectors.toList());
        if (complets.size() == items.size() - upItems.size()) {
            task.setUpEndDate(new Timestamp(System.currentTimeMillis()));
            task.setTaskStatus(MoveLocationTaskStatus.COMPLETE.intCode());
            updateMoveLocationTask(task);
            // 解绑周转框
            String boxNo = task.getBoxNo();
            String[][] logs = new String[][]{{"库位移库单上架", ""},
                    {"relationNo", task.getId().toString()}};
            int updated = whBoxService.updateWhBoxOfUnbinding(boxNo, logs);
            if (updated >= 1) {
                logger.info("库位移库单上架后释放周转码: boxNo[" + boxNo + "],  boxUpdated[" + updated + "]");
            } else {
                throw new RuntimeException("库位移库单上架解绑周转码失败");
            }
        }
        moveLocationTaskItemService.batchUpdateMoveLocationTaskItem(upItems);
        WhSkuQueryCondition query = new WhSkuQueryCondition();
        query.setSkus(upItems.stream().map(item -> item.getSku()).collect(Collectors.toList()));
        List<WhSku> whSkus = whSkuService.queryWhSkus(query, null);
        if (CollectionUtils.isEmpty(whSkus)) {
            throw new RuntimeException("SKU不存在");
        }

        whSkus.forEach(item -> {
                    item.setLocationNumber(newLocation);
                    item.setWarehouseId(1);
                }
        );

        whLocation.setCategoryActual(whLocation.getCategoryActual() - upItems.size());
        whLocationService.updateWhLocation(whLocation);
        whSkuService.batchUpdateWhSku(whSkus);
        moveLocationTaskStockService.upMjStock(upItems);

        // 跨仓上架
        List<String> eMes = new ArrayList<>();
        boolean check = false;
        for (int i = 0; i < 3; i++) {
            if (doSpanUpLocation(upItems, eMes)) {
                check = true;
                break;
            }
        }
        if (!check) {
            throw new RuntimeException("跨仓上架失败!" + eMes);
        }
        if (CollectionUtils.isNotEmpty(whSkus)) {
            for (WhSku whSku : whSkus) {
                SystemLogUtils.SKULOG.log(whSku.getId(), "sku库位变更",
                        new String[][]{{"原库位", location}, {"新库位", newLocation}});
            }
        }
        if (complets.size() == items.size() - upItems.size()) {
            SystemLogUtils.MOVELOCATIONTASKLOG.log(task.getId(), "库位移库单上架完成");
        }

    }

    /**
     * 跨仓上架
     *
     * @param skus
     * @param items
     */
    @Override
    @StockServicelock
    public void doSpanUp(List<String> skus, List<MoveLocationTaskItem> items) {
        String location = items.get(0).getLocation();
        String newLocation = items.get(0).getNewLocation();
        // 库位变更
        WhSkuQueryCondition query = new WhSkuQueryCondition();
        query.setSkus(items.stream().map(item -> item.getSku()).collect(Collectors.toList()));
        List<WhSku> whSkus = whSkuService.queryWhSkus(query, null);
        if (CollectionUtils.isEmpty(whSkus)) {
            throw new RuntimeException("SKU不存在");
        }
        // 消息体
        List<AmqMessage> amqMessageList = new ArrayList<AmqMessage>();
        whSkus.forEach(item -> {
                    item.setLocationNumber(newLocation);
                    item.setWarehouseId(1);
                    // 组装消息体
                    ProductSkuMessage productSkuMessage = new ProductSkuMessage();
                    productSkuMessage.setSku(item.getSku());
                    productSkuMessage.setLocationNumber(newLocation);
                    productSkuMessage.setWarehouseId(1);
                    AmqMessage amqMessage = AssembleMessageDataUtils.assembleSkuData(productSkuMessage);
                    amqMessageList.add(amqMessage);
                }
        );

        WhLocationQueryCondition newLocationQuery = new WhLocationQueryCondition();
        newLocationQuery.setLocation(newLocation);
        WhLocation newWhLocation = whLocationService.queryWhLocation(newLocationQuery);
        if (newWhLocation == null) {
            throw new RuntimeException("新库位不存在！");
        }
        newWhLocation.setCategoryActual(newWhLocation.getCategoryActual() + items.size());
        whLocationService.updateWhLocation(newWhLocation);
        whSkuService.batchUpdateWhSku(whSkus);
        moveLocationTaskStockService.upHhdStock(items);

        // 创建记录，接口幂等检验
        moveLocationTaskItemService.batchCreateMoveLocationTaskItem_span(items);
        // 推送消息至产品系统
        amqMessageDao.batchCreateAmqMessage(amqMessageList);
        if (CollectionUtils.isNotEmpty(whSkus)) {
            for (WhSku whSku : whSkus) {
                SystemLogUtils.SKULOG.log(whSku.getId(), "sku库位变更",
                        new String[][]{{"原库位", location}, {"新库位", newLocation}});
            }
        }
    }

    /**
     * 废弃
     *
     * @param skus
     * @param task
     */
    @Override
    @StockServicelock
    public void doCancel(List<String> skus, MoveLocationTask task) {
        if (!MoveLocationTaskStatus.WAIT_RECEIVE.intCode().equals(task.getTaskStatus())) {
            throw new RuntimeException("不是待领取不允许废弃！");
        }
        task.setTaskStatus(MoveLocationTaskStatus.DISCARD.intCode());
        updateMoveLocationTask(task);
        task.getItems().forEach(item -> item.setStatus(MoveLocationTaskStatus.DISCARD.intCode()));
        moveLocationTaskItemService.batchUpdateMoveLocationTaskItem(task.getItems());
        moveLocationTaskStockService.cancelStock(task.getItems());
        // 解绑周转框
        if (StringUtils.isNotBlank(task.getBoxNo())) {
            String boxNo = task.getBoxNo();
            String[][] logs = new String[][]{{"库位移库单废弃", ""},
                    {"relationNo", task.getId().toString()}};
            int updated = whBoxService.updateWhBoxOfUnbinding(boxNo, logs);
            if (updated >= 1) {
                logger.info("库位移库单废弃后释放周转码: boxNo[" + boxNo + "],  boxUpdated[" + updated + "]");
            } else {
                throw new RuntimeException("库位移库单废弃解绑周转码失败");
            }
        }
        SystemLogUtils.MOVELOCATIONTASKLOG.log(task.getId(), "废弃库位移库单");
    }

    /**
     * 跨仓校验目标货架，必须是老仓
     *
     * @param shelf
     */
    public void checkOldWarehouseShelf(String shelf) {
        if (StringUtils.isNotBlank(shelf)) {
            WarehouseProperties properties = WarehouseProperties.getWarehouseProperties();
            String ip = WarehouseProperties.getIp(PickingTaskWarehouseType.OLD.intCode(), properties);
            String url = ip.concat("/foreign/moveLocationTask/checkOldWarehouseShelf");
            try {
                ResponseJson result = HttpUtils.get(url + "?shelf=" + shelf, "", ResponseJson.class);
                if (result != null && result.getStatus().equals(StatusCode.FAIL)) {
                    String errMes = "校验目标货架失败!" + result.getMessage();
                    log.error("checkOldWarehouseShelf " + errMes);
                    throw new RuntimeException(errMes);
                } else {
                    log.info("checkOldWarehouseShelf 校验目标货架成功!");
                }
            } catch (Exception e) {
                log.error("checkOldWarehouseShelf " + e.getMessage(), e);
                throw new RuntimeException("校验目标货架失败!" + e.getMessage());
            }
        }
    }

    /**
     * 校验新库位
     *
     * @param newLocation
     */
    public void checkOldWarehouseLocation(String newLocation) {
        if (StringUtils.isNotBlank(newLocation)) {
            WarehouseProperties properties = WarehouseProperties.getWarehouseProperties();
            String ip = WarehouseProperties.getIp(PickingTaskWarehouseType.OLD.intCode(), properties);
            String url = ip.concat("/foreign/moveLocationTask/checkOldWarehouseLocation");
            try {
                ResponseJson result = HttpUtils.get(url + "?location=" + newLocation, "", ResponseJson.class);
                if (result != null && result.getStatus().equals(StatusCode.FAIL)) {
                    String errMes = "校验新库位失败!" + result.getMessage();
                    log.error("checkOldWarehouseLocation " + errMes);
                    throw new RuntimeException(errMes);
                } else {
                    log.info("checkOldWarehouseLocation 校验新库位成功!");
                }
            } catch (Exception e) {
                log.error("checkOldWarehouseLocation " + e.getMessage(), e);
                throw new RuntimeException("校验新库位失败!" + e.getMessage());
            }
        }
    }

    /**
     * 跨仓上架
     *
     * @param items
     */
    private boolean doSpanUpLocation(List<MoveLocationTaskItem> items, List<String> eMes) {
        if (CollectionUtils.isNotEmpty(items)) {
            WarehouseProperties properties = WarehouseProperties.getWarehouseProperties();
            String ip = WarehouseProperties.getIp(PickingTaskWarehouseType.OLD.intCode(), properties);
            String url = ip.concat("/foreign/moveLocationTask/doSpanUp");
            try {
                ResponseJson result = HttpUtils.post(url, "", items, ResponseJson.class);
                if (result != null && result.getStatus().equals(StatusCode.FAIL)) {
                    String errMes = "跨仓上架失败!" + result.getMessage();
                    log.error("doSpanUp " + errMes);
                    eMes.add(result.getMessage());
                } else {
                    log.info("doSpanUp 跨仓上架成功!");
                    return true;
                }
            } catch (Exception e) {
                log.error("doSpanUp " + e.getMessage(), e);
                eMes.add(e.getMessage());
            }
        }
        return false;
    }

    private MoveLocationTask getMoveLocationTaskDetail(Integer id) {
        return moveLocationTaskService.getMoveLocationTaskDetail(id);
    }

    private MoveLocationTask queryMoveLocationTask(MoveLocationTaskQueryCondition query) {
        return moveLocationTaskService.queryMoveLocationTask(query);
    }

    private void updateMoveLocationTask(MoveLocationTask moveLocationTask) {
        moveLocationTaskService.updateMoveLocationTask(moveLocationTask);
    }

    /**
     * SKU对应的调拨需求已完成，调拨单已装车
     *
     * @param skus
     * @return
     */
    @Override
    public String checkAllocationOrder(List<String> skus) {
        List<Integer> taskStatusList = new ArrayList<>();
        taskStatusList.add(AllocationDemandTaskStatus.NOT_GENERATE.intCode());
        taskStatusList.add(AllocationDemandTaskStatus.GENERATING.intCode());
        taskStatusList.add(AllocationDemandTaskStatus.PICK.intCode());
        WhApvAllocationDemandQueryCondition demandQuery = new WhApvAllocationDemandQueryCondition();
        demandQuery.setSkus(skus);
        demandQuery.setTaskStatusList(taskStatusList);
        List<WhApvAllocationDemand> demands = whApvAllocationDemandService.queryWhApvAllocationDemands(demandQuery, null);
        if (CollectionUtils.isNotEmpty(demands)) {
            List<String> list = demands.stream().map(item -> item.getTaskNo()).collect(Collectors.toList());
            return "还有未完成的调拨需求: " + JSON.toJSONString(list);
        }

        // AllocationTypeEnum.INVENTORY.intCode()
        //String allocationStatusStr = query.getAllocationStatusStr();
        //domain.setStatusSelectJosn(SelectJson.getList(AllocationStatusEnum.values()));
        //domain.setStatusOrderSelectJson(SelectJson.getList(AllocationOrderStatusEnum.values()));

        WhApvAllocationQueryCondition query = new WhApvAllocationQueryCondition();
        query.setSkuStr(StringUtils.join(skus, ","));
        /*List<Integer> allocationStatusList = new ArrayList<>();
        allocationStatusList.add(AllocationStatusEnum.WAIT_COMMIT.intCode());
        allocationStatusList.add(AllocationStatusEnum.WAIT_PROCESS.intCode());
        allocationStatusList.add(AllocationStatusEnum.WAIT_PICK.intCode());
        allocationStatusList.add(AllocationStatusEnum.PICKING.intCode());
        allocationStatusList.add(AllocationStatusEnum.WAIT_BOX.intCode());
        allocationStatusList.add(AllocationStatusEnum.WAIT_AUDIT.intCode());
        allocationStatusList.add(AllocationStatusEnum.WAIT_LOAD.intCode());
        query.setAllocationStatusStr(StringUtils.join(allocationStatusList, ","));
        List<WhApvAllocation> allocationList = whApvAllocationService.queryApvAllocationList(query, null);
        if (CollectionUtils.isNotEmpty(allocationList)) {
            List<String> list = allocationList.stream().map(item -> item.getAllocationNo()).collect(Collectors.toList());
            return "还有未装车出库的调拨单: " + JSON.toJSONString(list);
        }*/
        List<Integer> allocationStatusList2 = new ArrayList<>();
        allocationStatusList2.add(AllocationOrderStatusEnum.WAIT_PRINT.intCode());
        allocationStatusList2.add(AllocationOrderStatusEnum.PRINTING.intCode());
        allocationStatusList2.add(AllocationOrderStatusEnum.PICKING.intCode());
        allocationStatusList2.add(AllocationOrderStatusEnum.WAIT_BOX.intCode());
        allocationStatusList2.add(AllocationOrderStatusEnum.WAIT_LOAD.intCode());
        query.setAllocationStatusStr(StringUtils.join(allocationStatusList2, ","));
        List<WhApvAllocation> allocationList2 = whApvAllocationOrderService.queryApvAllocationOrderList(query, null);
        if (CollectionUtils.isNotEmpty(allocationList2)) {
            List<String> list = allocationList2.stream().map(item -> item.getAllocationNo()).collect(Collectors.toList());
            return "还有未装车出库的调拨单: " + JSON.toJSONString(list);
        }
        // 拦截还未上架的入库单
        WhCheckInQueryCondition checkInQuery = new WhCheckInQueryCondition();
        List<Integer> statuses = Arrays.asList(CheckInStatus.WAITING_QC.intCode(),
                CheckInStatus.QC_NG_PENDING.intCode(), CheckInStatus.QC_NG.intCode(),
                CheckInStatus.WAITING_UP.intCode(), CheckInStatus.UPING.intCode(), CheckInStatus.UPERROR.intCode(),
                CheckInStatus.WAITING_QC.intCode());

        checkInQuery.setSkus(skus);
        checkInQuery.setStatuses(statuses);
        List<WhCheckIn> whCheckIns = whCheckInService.queryWhCheckIns(checkInQuery, null);
        if (CollectionUtils.isNotEmpty(whCheckIns)) {
            Set<String> skuSet = whCheckIns.stream().map(item -> item.getWhCheckInItem().getSku()).collect(Collectors.toSet());
            Set<String> locationSet = whCheckIns.stream().map(item -> item.getWhCheckInItem().getLocation()).collect(Collectors.toSet());
            return "还有未上架的入库单: " + JSON.toJSONString(skuSet)
                    + "； 对应库位：" + JSON.toJSONString(locationSet);
        }
        return null;
    }
}