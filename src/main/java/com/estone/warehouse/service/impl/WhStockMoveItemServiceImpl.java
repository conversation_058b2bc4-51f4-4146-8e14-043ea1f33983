package com.estone.warehouse.service.impl;

import com.estone.warehouse.bean.WhStockMoveItem;
import com.estone.warehouse.bean.WhStockMoveItemQueryCondition;
import com.estone.warehouse.dao.WhStockMoveItemDao;
import com.estone.warehouse.service.WhStockMoveItemService;
import com.whq.tool.component.Pager;
import com.whq.tool.exception.BusinessException;
import com.whq.tool.exception.DBErrorCode;
import com.whq.tool.sqler.SqlerException;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

@Service("whStockMoveItemService")
@Slf4j
public class WhStockMoveItemServiceImpl implements WhStockMoveItemService {
    @Resource
    private WhStockMoveItemDao whStockMoveItemDao;

    @Override
    public WhStockMoveItem getWhStockMoveItem(Integer id) {
        WhStockMoveItem whStockMoveItem = whStockMoveItemDao.queryWhStockMoveItem(id);
        return whStockMoveItem;
    }

    @Override
    public WhStockMoveItem getWhStockMoveItemDetail(Integer id) {
        WhStockMoveItem whStockMoveItem = whStockMoveItemDao.queryWhStockMoveItem(id);
        // 关联查询
        return whStockMoveItem;
    }

    @Override
    public WhStockMoveItem queryWhStockMoveItem(WhStockMoveItemQueryCondition query) {
        Assert.notNull(query, "query is null!");
        WhStockMoveItem whStockMoveItem = whStockMoveItemDao.queryWhStockMoveItem(query);
        return whStockMoveItem;
    }

    @Override
    public List<WhStockMoveItem> queryAllWhStockMoveItems() {
        return whStockMoveItemDao.queryWhStockMoveItemList();
    }

    @Override
    public List<WhStockMoveItem> queryWhStockMoveItems(WhStockMoveItemQueryCondition query, Pager pager) {
        Assert.notNull(query, "query is null!");
        if (pager != null && pager.isQueryCount()) {
            int count = whStockMoveItemDao.queryWhStockMoveItemCount(query);
            pager.setTotalCount(count);
            if ( count == 0) {
                return new ArrayList<WhStockMoveItem>();
            }
        }
        List<WhStockMoveItem> whStockMoveItems = whStockMoveItemDao.queryWhStockMoveItemList(query, pager);
        return whStockMoveItems;
    }

    @Override
    public void createWhStockMoveItem(WhStockMoveItem whStockMoveItem) {
        try {
            whStockMoveItemDao.createWhStockMoveItem(whStockMoveItem);
        }
        catch (SqlerException e) {
            log.error(e.getMessage(), e);
            throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
        }
    }

    @Override
    public void batchCreateWhStockMoveItem(List<WhStockMoveItem> entityList) {
        if (CollectionUtils.isNotEmpty(entityList)) {
            try {
                whStockMoveItemDao.batchCreateWhStockMoveItem(entityList);
            }
            catch (SqlerException e) {
                log.error(e.getMessage(), e);
                throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
            }
        }
    }

    @Override
    public void deleteWhStockMoveItem(Integer id) {
        try {
            whStockMoveItemDao.deleteWhStockMoveItem(id);
        }
        catch (SqlerException e) {
            log.error(e.getMessage(), e);
            throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
        }
    }

    @Override
    public void updateWhStockMoveItem(WhStockMoveItem whStockMoveItem) {
        try {
            whStockMoveItemDao.updateWhStockMoveItem(whStockMoveItem);
        }
        catch (SqlerException e) {
            log.error(e.getMessage(), e);
            throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
        }
    }

    @Override
    public void batchUpdateWhStockMoveItem(List<WhStockMoveItem> entityList) {
        if (CollectionUtils.isNotEmpty(entityList)) {
            try {
                whStockMoveItemDao.batchUpdateWhStockMoveItem(entityList);
            }
            catch (SqlerException e) {
                log.error(e.getMessage(), e);
                throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
            }
        }
    }
}