package com.estone.warehouse.service;

import com.estone.warehouse.bean.FrozenStock;
import com.estone.warehouse.bean.FrozenStockQueryCondition;
import com.whq.tool.component.Pager;
import java.util.List;

public interface FrozenStockService {
    List<FrozenStock> queryAllFrozenStocks();

    List<FrozenStock> queryFrozenStocks(FrozenStockQueryCondition query, Pager pager);

    FrozenStock getFrozenStock(Integer id);

    FrozenStock getFrozenStockDetail(Integer id);

    FrozenStock queryFrozenStock(FrozenStockQueryCondition query);

    void createFrozenStock(FrozenStock frozenStock);

    void batchCreateFrozenStock(List<FrozenStock> entityList);

    void deleteFrozenStock(Integer id);

    void updateFrozenStock(FrozenStock frozenStock);

    void batchUpdateFrozenStock(List<FrozenStock> entityList);
}