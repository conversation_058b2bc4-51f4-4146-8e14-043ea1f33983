package com.estone.warehouse.service;

import com.estone.warehouse.bean.WhReturnExceptionItem;
import com.estone.warehouse.bean.WhReturnExceptionItemQueryCondition;
import com.estone.warehouse.enums.ReturnExceptionReasonEnum;
import com.estone.warehouse.enums.ReturnExceptionTypeEnum;
import com.whq.tool.component.Pager;
import java.util.List;

/**
 * 返架异常明细
 */
public interface WhReturnExceptionItemService {
    List<WhReturnExceptionItem> queryAllWhReturnExceptionItems();

    List<WhReturnExceptionItem> queryWhReturnExceptionItems(WhReturnExceptionItemQueryCondition query, Pager pager);

    WhReturnExceptionItem getWhReturnExceptionItem(Integer id);

    WhReturnExceptionItem getWhReturnExceptionItemDetail(Integer id);

    WhReturnExceptionItem queryWhReturnExceptionItem(WhReturnExceptionItemQueryCondition query);

    void createWhReturnExceptionItem(WhReturnExceptionItem whReturnExceptionItem);

    void batchCreateWhReturnExceptionItem(List<WhReturnExceptionItem> entityList);

    void deleteWhReturnExceptionItem(Integer id);

    void createReturnExceptionItem(String returnNo, String uuid, ReturnExceptionTypeEnum returnType, ReturnExceptionReasonEnum reason);

}
