package com.estone.warehouse.service;

import java.util.List;

import com.estone.picking.bean.WhPickingTask;
import com.estone.warehouse.bean.WhBox;
import com.estone.warehouse.bean.WhBoxQueryCondition;
import com.whq.tool.component.Pager;
import com.whq.tool.json.ResponseJson;

public interface WhBoxService {
    /**
     * This method corresponds to the database table wh_box
     *
     * @mbggenerated Thu Aug 16 17:21:05 CST 2018
     */
    List<WhBox> queryAllWhBoxs();

    /**
     * This method corresponds to the database table wh_box
     *
     * @mbggenerated Thu Aug 16 17:21:05 CST 2018
     */
    List<WhBox> queryWhBoxs(WhBoxQueryCondition query, Pager pager);

    List<WhBox> queryWhSHBoxs(WhBoxQueryCondition query, Pager pager);

    /**
     * This method corresponds to the database table wh_box
     *
     * @mbggenerated Thu Aug 16 17:21:05 CST 2018
     */
    WhBox getWhBox(Integer id);

    /**
     * This method corresponds to the database table wh_box
     *
     * @mbggenerated Thu Aug 16 17:21:05 CST 2018
     */
    WhBox getWhBoxDetail(Integer id);

    /**
     * This method corresponds to the database table wh_box
     *
     * @mbggenerated Thu Aug 16 17:21:05 CST 2018
     */
    WhBox queryWhBox(WhBoxQueryCondition query);

    /**
     * This method corresponds to the database table wh_box
     *
     * @mbggenerated Thu Aug 16 17:21:05 CST 2018
     */
    void createWhBox(WhBox whBox);

    /**
     * This method corresponds to the database table wh_box
     *
     * @mbggenerated Thu Aug 16 17:21:05 CST 2018
     */
    void batchCreateWhBox(List<WhBox> entityList);

    /**
     * This method corresponds to the database table wh_box
     *
     * @mbggenerated Thu Aug 16 17:21:05 CST 2018
     */
    void deleteWhBox(Integer id);

    /**
     * This method corresponds to the database table wh_box
     *
     * @mbggenerated Thu Aug 16 17:21:05 CST 2018
     */
    void updateWhBox(WhBox whBox);

    /**
     * 打印周转框编码
     * 
     * @Description: 打印周转框编码
     * @param ids
     * @Author: wuhuiqiang
     * @Date: 2018/08/30
     * @Version: 0.0.1
     */
    void updateWhBoxPrint(List<Integer> ids);

    /**
     * 
     * @Description: 绑定周转码
     *
     * @param whBox
     * @return: void
     * @Author: qinyangkai
     * @Date: 2018/08/25
     * @Version: 0.0.1
     */
    int updateWhBoxOfBinding(String boxNo, String relationNo, String[][] logs);

    void updateWhBoxOfBindingPre(String boxNo, String relationNo, String[][] logs);

    /**
     * 解绑周转框
     * 
     * @Description:周转框解绑
     * @param whBox
     * @return
     * @Author: Administrator
     * @Date: 2018/09/14
     * @Version: 0.0.1
     */
    int updateWhBoxOfUnbinding(String boxNo, String[][] logs);

    int updateWhBoxOfUnbindingForPage(Integer boxId, String[][] logs);

    void batchUpdateWhBoxOfUnbindingForPage(List<Integer> boxIdList, String[][] logs);

    /**
     * This method corresponds to the database table wh_box
     *
     * @mbggenerated Thu Aug 16 17:21:05 CST 2018
     */
    void batchUpdateWhBox(List<WhBox> entityList);

    WhBox queryWhBoxByBoxNo(String boxNo);

    /**
     * 通过周转筐查询拣货任务
     * 
     * @param boxNo
     * @return
     */
    WhPickingTask queryWhPickingTaskByBoxNo(String boxNo);

    /**
     * 检查包装 扫描周转筐或者拣货任务号 返回拣货任务号
     * 
     * @param boxNoOrTaskNo
     * @param boxType
     * @param rsp
     */
    ResponseJson checkPackBoxNoScan(String boxNoOrTaskNo, Integer boxType);

    /**
     * 检查 热销拣货任务号扫描
     * 
     * @param taskNo
     * @param taskType
     * @return
     */
    ResponseJson checkFirePackTaskNoScan(String taskNo, Integer taskType);

    /**
     * 获取真正的周转筐
     * 
     * @param taskNoOrBoxNo
     * @return
     */
    String findBoxNo(String taskNoOrBoxNo);

    /**
     * 查看收货周转框详情
     * 
     * @param boxNo
     * @return
     */
    WhBox viewItems(String boxNo);

    /**
     * 收货周转框-解锁周转框/使用人
     * 
     * @param type 0-解锁周转框 1-解锁使用人
     * @param id
     * @param isAuto true-点数入库自动解绑 false-周转框页面手动解绑
     * @return
     */
    int updateWhBoxSHOfUnbinding(Integer type, Integer id, Boolean isAuto);

    /**
     * 收货周转框详情-解绑
     * 
     * @param boxNo
     * @param relationNo
     * @param orderType 订单类型
     * @param isAuto true-点数入库自动解绑 false-周转框页面手动解绑
     * @return
     */
    int unbindItem(String boxNo, String relationNo,Integer orderType, Boolean isAuto);

    /**
     * 添加收货周转框
     * 
     * @param whBox
     */
    void createWhBoxSH(WhBox whBox);

    List<WhBox> queryAllWhBoxsByTaskNos(List<String> taskNoList);

    void updateStatus(String boxNo, Boolean isUsed);

    void checkUnSplitCount(String boxNo);

    /**
     * 
     * @Description: 查询收获周转筐
     *
     * @param query
     * @return
     * @return: WhBox
     * @Author: qinyangkai
     * @Date: 2019/05/06
     * @Version: 0.0.1
     */
    WhBox queryWhSHBox(WhBoxQueryCondition query);
    
    /**
     * 根据领取人查找周转框
     * @param presentUser
     * @return
     */
    WhBox queryWhBoxByPresentUser(Integer presentUser);
}