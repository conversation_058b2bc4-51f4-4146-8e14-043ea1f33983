package com.estone.warehouse.service;

import com.estone.warehouse.bean.WhInventoryItem;
import com.estone.warehouse.bean.WhInventoryTaskItem;
import com.whq.tool.json.ResponseJson;

import java.util.List;
import java.util.Map;

/**
 * ---------------------------------------------------------------------------
 * Copyright (c) 2018, estone- All Rights Reserved. Project Name:wms Package
 * Name:com.estone.warehouse.service File Name:InventoryUpdateStockService.java
 * Description: Author:Yimeil Date:2019-12-13 17:14
 * ---------------------------------------------------------------------------
 */
public interface InventoryUpdateStockService {

    /**
     * 拣货缺货盘点更改可用库存
     *
     * @param taskItem
     * @return
     */
    Map<String, ResponseJson> batchUpdateStockByVerifyInventoryTask(WhInventoryTaskItem taskItem);

}
