package com.estone.warehouse.service;

import com.estone.warehouse.bean.WhSaleStockLog;
import com.estone.warehouse.bean.WhSaleStockLogQueryCondition;
import com.whq.tool.component.Pager;
import java.util.List;

public interface WhSaleStockLogService {
    List<WhSaleStockLog> queryAllWhSaleStockLogs();

    List<WhSaleStockLog> queryWhSaleStockLogs(WhSaleStockLogQueryCondition query, Pager pager);

    WhSaleStockLog getWhSaleStockLog(Integer id);

    WhSaleStockLog getWhSaleStockLogDetail(Integer id);

    WhSaleStockLog queryWhSaleStockLog(WhSaleStockLogQueryCondition query);

    void createWhSaleStockLog(WhSaleStockLog whSaleStockLog);

    void batchCreateWhSaleStockLog(List<WhSaleStockLog> entityList);

    void deleteWhSaleStockLog(Integer id);

    void updateWhSaleStockLog(WhSaleStockLog whSaleStockLog);

    void batchUpdateWhSaleStockLog(List<WhSaleStockLog> entityList);
}