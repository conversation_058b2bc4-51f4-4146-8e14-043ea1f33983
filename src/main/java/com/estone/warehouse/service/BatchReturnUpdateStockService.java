package com.estone.warehouse.service;

import com.estone.apv.bean.WhApvOutStockChain;
import com.estone.warehouse.bean.WhBatchReturnApvItem;
import com.estone.warehouse.bean.WhBatchReturnSku;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @Description 退货批次库存操作接口
 * @date 2020/11/5 17:38
 */
public interface BatchReturnUpdateStockService {

    /**
     * @return boolean
     * @Description 新增退货批次
     * <AUTHOR>
     * @date 2020/11/5 17:42
     * @param: whBatchReturnSkus
     */
    boolean createWhBatchReturnToUpdateStock(List<WhBatchReturnSku> whBatchReturnSkus,Map<Integer, Integer> stokeIdMap);

    /**
     * @return boolean
     * @Description 编辑退货批次
     * <AUTHOR>
     * @date 2020/11/5 17:42
     * @param: whBatchReturnSkus
     */
    boolean doEditWhBatchReturnToUpdateStock(List<WhBatchReturnSku> whBatchReturnSkus, Map<Integer,Integer> stokeIdMap);

    boolean createToUpdateStock(List<WhBatchReturnSku> whBatchReturnSkus,Map<Integer,Integer> stokeIdMap);

    boolean doEditToUpdateStock(List<WhBatchReturnSku> whBatchReturnSkus,Map<Integer,Integer> stokeIdMap);

    List<WhApvOutStockChain> getWhApvOutStockChainList(List<WhBatchReturnApvItem> whBatchReturnApvItems);

    boolean createWhBatchReturnToFbaUpdateStock(ArrayList<WhBatchReturnSku> whBatchReturnSkus, List<WhBatchReturnApvItem> whBatchReturnApvItems);
}
