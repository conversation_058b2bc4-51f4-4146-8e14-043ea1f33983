package com.estone.warehouse.service;

import com.estone.common.util.model.CQuery;
import com.estone.common.util.model.CQueryResult;
import com.estone.warehouse.bean.*;
import com.whq.tool.component.Pager;
import com.whq.tool.json.ResponseJson;

import java.util.List;
import java.util.Map;
import java.util.Set;

public interface WhInventoryTaskService {
    /**
     * This method corresponds to the database table wh_inventory_task
     *
     * @mbggenerated Wed Jul 03 16:15:25 CST 2019
     */
    List<WhInventoryTask> queryAllWhInventoryTasks();

    /**
     * This method corresponds to the database table wh_inventory_task
     *
     * @mbggenerated Wed Jul 03 16:15:25 CST 2019
     */
    List<WhInventoryTask> queryWhInventoryTasks(WhInventoryTaskQueryCondition query, Pager pager);

    /**
     * This method corresponds to the database table wh_inventory_task
     *
     * @mbggenerated Wed Jul 03 16:15:25 CST 2019
     */
    WhInventoryTask getWhInventoryTask(Integer id);

    /**
     * This method corresponds to the database table wh_inventory_task
     *
     * @mbggenerated Wed Jul 03 16:15:25 CST 2019
     */
    WhInventoryTask getWhInventoryTaskDetail(Integer id);

    /**
     * This method corresponds to the database table wh_inventory_task
     *
     * @mbggenerated Wed Jul 03 16:15:25 CST 2019
     */
    WhInventoryTask queryWhInventoryTask(WhInventoryTaskQueryCondition query);

    /**
     * This method corresponds to the database table wh_inventory_task
     *
     * @mbggenerated Wed Jul 03 16:15:25 CST 2019
     */
    void createWhInventoryTask(WhInventoryTask whInventoryTask);

    /**
     * This method corresponds to the database table wh_inventory_task
     *
     * @mbggenerated Wed Jul 03 16:15:25 CST 2019
     */
    void batchCreateWhInventoryTask(List<WhInventoryTask> entityList);

    /**
     * This method corresponds to the database table wh_inventory_task
     *
     * @mbggenerated Wed Jul 03 16:15:25 CST 2019
     */
    void deleteWhInventoryTask(Integer id);

    /**
     * This method corresponds to the database table wh_inventory_task
     *
     * @mbggenerated Wed Jul 03 16:15:25 CST 2019
     */
    int updateWhInventoryTask(WhInventoryTask whInventoryTask);

    /**
     * This method corresponds to the database table wh_inventory_task
     *
     * @mbggenerated Wed Jul 03 16:15:25 CST 2019
     */
    void batchUpdateWhInventoryTask(List<WhInventoryTask> entityList);
    
    List<WhInventoryTask> queryWhInventoryTaskAndItemList(WhInventoryTaskQueryCondition query, Pager pager);
    
    ResponseJson top(Integer id);
    
    boolean discard(Integer id, String type);
    
    ResponseJson receiveInventoryTaskByType(Integer receiveUser, InventoryTaskType type);
    
    ResponseJson updateInventoryItemComplete(WhInventoryTask task,WhInventoryTaskItem taskItem, Integer inventoryQuantity,Boolean next);
    
    ResponseJson updateInventoryComplete(WhInventoryTask task);
    
    boolean updateForDismissed(Integer id, String type,String message);

    Map<String, Object> queryInventoryTaskDiffSkuCount(WhInventoryTaskQueryCondition query);

    List<InventoryTaskViewExport> queryInventoryTaskViewExportByAccount(WhInventoryTaskQueryCondition query);

    ResponseJson updateForConfirmAndStock(List<String> skuList, List<WhInventoryTaskItem> inventoryTaskItems, List<String> batchNo);

    void completedTask(List<Integer> ids, Integer status);

    boolean allocation(Integer id, Integer allocationUser) throws Exception;

    ResponseJson receiveInventoryTask(Integer taskId, Integer receiveUser);

    /**
     * 查询是否存在未审核完成的盘点任务
     */
    List<Integer> queryNotAuditSku(List<Integer> stockIdList);

    /**
     * 查询盘点异常原因
     */
    List<String> queryAnomalousCauseList();

    Map<Integer, String> getUnreviewInventoryTask(Set<Integer> collect);

    CQueryResult queryInventoryReviewList(CQuery<WhInventoryTaskItemQueryCondition> query);

    void createInventoryTask(List<String> localList,Integer inventoryNumber);
}