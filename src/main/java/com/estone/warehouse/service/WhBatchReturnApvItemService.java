package com.estone.warehouse.service;

import com.estone.warehouse.bean.WhBatchReturnApvItem;
import com.estone.warehouse.bean.WhBatchReturnApvItemQueryCondition;
import com.whq.tool.component.Pager;
import java.util.List;

public interface WhBatchReturnApvItemService {
    List<WhBatchReturnApvItem> queryAllWhBatchReturnApvItems();

    List<WhBatchReturnApvItem> queryWhBatchReturnApvItems(WhBatchReturnApvItemQueryCondition query, Pager pager);

    WhBatchReturnApvItem getWhBatchReturnApvItem(Integer id);

    WhBatchReturnApvItem getWhBatchReturnApvItemDetail(Integer id);

    WhBatchReturnApvItem queryWhBatchReturnApvItem(WhBatchReturnApvItemQueryCondition query);

    void createWhBatchReturnApvItem(WhBatchReturnApvItem whBatchReturnApvItem);

    void batchCreateWhBatchReturnApvItem(List<WhBatchReturnApvItem> entityList);

    void deleteWhBatchReturnApvItem(Integer id);

    void updateWhBatchReturnApvItem(WhBatchReturnApvItem whBatchReturnApvItem);

    void batchUpdateWhBatchReturnApvItem(List<WhBatchReturnApvItem> entityList);
}