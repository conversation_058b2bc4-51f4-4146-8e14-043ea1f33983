package com.estone.warehouse.service;

import com.estone.warehouse.bean.StockMonthSnapshot;
import com.estone.warehouse.bean.StockMonthSnapshotQueryCondition;
import com.whq.tool.component.Pager;
import java.util.List;

public interface StockMonthSnapshotService {
    List<StockMonthSnapshot> queryAllStockMonthSnapshots();

    List<StockMonthSnapshot> queryStockMonthSnapshots(StockMonthSnapshotQueryCondition query, Pager pager);

    StockMonthSnapshot getStockMonthSnapshot(Integer id);

    StockMonthSnapshot getStockMonthSnapshotDetail(Integer id);

    StockMonthSnapshot queryStockMonthSnapshot(StockMonthSnapshotQueryCondition query);

    void createStockMonthSnapshot(StockMonthSnapshot stockMonthSnapshot);

    void batchCreateStockMonthSnapshot(List<StockMonthSnapshot> entityList);

    void deleteStockMonthSnapshot(Integer id);
    void batchDeleteById(List<Integer> id);

    void updateStockMonthSnapshot(StockMonthSnapshot stockMonthSnapshot);

    void batchUpdateStockMonthSnapshot(List<StockMonthSnapshot> entityList);
}