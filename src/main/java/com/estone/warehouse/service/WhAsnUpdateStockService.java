package com.estone.warehouse.service;

import com.estone.asn.bean.WhAsn;

/**
 * <AUTHOR>
 * @version 1.0
 * @Description 海外仓调拨单库存接口
 * @date 2020/11/24 11:29
 */
public interface WhAsnUpdateStockService {

    boolean allot(WhAsn whAsn);

    void cancel(WhAsn whAsn);

    void cancelStartPick(WhAsn whAsn);

    /**
     * 本仓交运
     *
     * @param whAsn
     */
    void deliverBySelf(WhAsn whAsn);

    /**
     * 跨仓交运
     *
     * @param whAsn
     */
    void deliver(WhAsn whAsn);

    boolean singlePick(String sku, Integer pickQuantity, String taskNo);

    /**
     * 装箱为0的SKU退已拣返架
     *
     * @param whAsn
     */
    void box(WhAsn whAsn);
}
