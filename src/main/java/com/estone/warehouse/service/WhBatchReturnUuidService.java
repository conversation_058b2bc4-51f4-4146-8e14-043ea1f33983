package com.estone.warehouse.service;

import com.estone.warehouse.bean.WhBatchReturnUuid;
import com.estone.warehouse.bean.WhBatchReturnUuidQueryCondition;
import com.whq.tool.component.Pager;

import java.util.List;

public interface WhBatchReturnUuidService {
    List<WhBatchReturnUuid> queryAllWhBatchReturnUuids();

    List<WhBatchReturnUuid> queryWhBatchReturnUuids(WhBatchReturnUuidQueryCondition query, Pager pager);

    WhBatchReturnUuid getWhBatchReturnUuid(Integer id);

    WhBatchReturnUuid getWhBatchReturnUuidDetail(Integer id);

    WhBatchReturnUuid queryWhBatchReturnUuid(WhBatchReturnUuidQueryCondition query);

    void createWhBatchReturnUuid(WhBatchReturnUuid whBatchReturnUuid);

    void batchCreateWhBatchReturnUuid(List<WhBatchReturnUuid> entityList);

    void deleteWhBatchReturnUuid(Integer id);

    void updateWhBatchReturnUuid(WhBatchReturnUuid whBatchReturnUuid);

    void batchUpdateWhBatchReturnUuid(List<WhBatchReturnUuid> entityList);


    /**
     * 用于添加扫描错误的唯一码的相关日志
     *
     * @param content 日志记录的内容
     * @param uuid    扫描的唯一码
     */
    void addErrorScanUUidLog(String uuid, String content);

    WhBatchReturnUuid queryZfReturnUuid(String uuid);
}