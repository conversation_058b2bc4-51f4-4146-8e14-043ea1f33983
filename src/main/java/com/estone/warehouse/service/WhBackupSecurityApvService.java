package com.estone.warehouse.service;

import com.estone.warehouse.bean.WhBackupSecurityApv;
import com.estone.warehouse.bean.WhBackupSecurityApvQueryCondition;
import com.whq.tool.component.Pager;
import java.util.List;

public interface WhBackupSecurityApvService {
    /**
     * This method corresponds to the database table wh_backup_security_apv
     *
     * @mbggenerated Thu Jun 13 15:12:44 CST 2019
     */
    List<WhBackupSecurityApv> queryAllWhBackupSecurityApvs();

    /**
     * This method corresponds to the database table wh_backup_security_apv
     *
     * @mbggenerated Thu Jun 13 15:12:44 CST 2019
     */
    List<WhBackupSecurityApv> queryWhBackupSecurityApvs(WhBackupSecurityApvQueryCondition query, Pager pager);

    /**
     * This method corresponds to the database table wh_backup_security_apv
     *
     * @mbggenerated Thu Jun 13 15:12:44 CST 2019
     */
    WhBackupSecurityApv getWhBackupSecurityApv(Integer id);

    /**
     * This method corresponds to the database table wh_backup_security_apv
     *
     * @mbggenerated Thu Jun 13 15:12:44 CST 2019
     */
    WhBackupSecurityApv getWhBackupSecurityApvDetail(Integer id);

    /**
     * This method corresponds to the database table wh_backup_security_apv
     *
     * @mbggenerated Thu Jun 13 15:12:44 CST 2019
     */
    WhBackupSecurityApv queryWhBackupSecurityApv(WhBackupSecurityApvQueryCondition query);

    /**
     * This method corresponds to the database table wh_backup_security_apv
     *
     * @mbggenerated Thu Jun 13 15:12:44 CST 2019
     */
    void createWhBackupSecurityApv(WhBackupSecurityApv whBackupSecurityApv);

    /**
     * This method corresponds to the database table wh_backup_security_apv
     *
     * @mbggenerated Thu Jun 13 15:12:44 CST 2019
     */
    void batchCreateWhBackupSecurityApv(List<WhBackupSecurityApv> entityList);

    /**
     * This method corresponds to the database table wh_backup_security_apv
     *
     * @mbggenerated Thu Jun 13 15:12:44 CST 2019
     */
    void deleteWhBackupSecurityApv(Integer id);

    /**
     * This method corresponds to the database table wh_backup_security_apv
     *
     * @mbggenerated Thu Jun 13 15:12:44 CST 2019
     */
    void updateWhBackupSecurityApv(WhBackupSecurityApv whBackupSecurityApv);

    /**
     * This method corresponds to the database table wh_backup_security_apv
     *
     * @mbggenerated Thu Jun 13 15:12:44 CST 2019
     */
    void batchUpdateWhBackupSecurityApv(List<WhBackupSecurityApv> entityList);
}