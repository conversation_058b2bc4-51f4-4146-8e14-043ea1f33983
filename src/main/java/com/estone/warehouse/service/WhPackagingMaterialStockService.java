package com.estone.warehouse.service;

import com.estone.warehouse.bean.WhPackagingMaterialStock;
import com.estone.warehouse.bean.WhPackagingMaterialStockQueryCondition;
import com.whq.tool.component.Pager;
import java.util.List;

public interface WhPackagingMaterialStockService {
    List<WhPackagingMaterialStock> queryAllWhPackagingMaterialStocks();

    List<WhPackagingMaterialStock> queryWhPackagingMaterialStocks(WhPackagingMaterialStockQueryCondition query, Pager pager);

    WhPackagingMaterialStock getWhPackagingMaterialStock(Integer id);

    WhPackagingMaterialStock getWhPackagingMaterialStockDetail(Integer id);

    WhPackagingMaterialStock queryWhPackagingMaterialStock(WhPackagingMaterialStockQueryCondition query);

    void createWhPackagingMaterialStock(WhPackagingMaterialStock whPackagingMaterialStock);

    void batchCreateWhPackagingMaterialStock(List<WhPackagingMaterialStock> entityList);

    void deleteWhPackagingMaterialStock(Integer id);

    void updateWhPackagingMaterialStock(WhPackagingMaterialStock whPackagingMaterialStock);

    void batchUpdateWhPackagingMaterialStock(List<WhPackagingMaterialStock> entityList);

    WhPackagingMaterialStock queryStockCount(WhPackagingMaterialStockQueryCondition query);
}