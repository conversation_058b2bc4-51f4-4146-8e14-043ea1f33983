package com.estone.warehouse.service;

import java.util.List;
import java.util.Map;

/**
 * @Description: 优选仓订单拣货改库存
 * @Author: Yimeil
 * @Date: 2021/9/4 17:31
 * @Version: 1.0.0
 */
public interface ApvPickUpdatePacStockService {
    /**
     * 单品拣货下一步
     * 
     * @param sku
     * @param pickQuantity
     * @param returnPickOutQuantity
     * @param taskNo
     * @return
     */
    boolean singlePick(String sku, Integer pickQuantity, Integer returnPickOutQuantity, String taskNo);

    /**
     * 多品多件拣货下一步
     * 
     * @param sku
     * @param pickQuantity
     * @param taskNo
     * @return
     */
    boolean multiplePick(String sku, Integer pickQuantity, String taskNo, boolean accross);

    /**
     * 播种拣货缺货退已拣和已分配
     * @param skus
     * @param pickMap
     * @param allotMap
     * @param taskNo
     * @return
     */
    boolean pickReturn(List<String> skus, Map<String, Integer> pickMap, Map<String, Integer> allotMap, String taskNo);
}
