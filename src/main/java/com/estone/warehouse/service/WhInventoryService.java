package com.estone.warehouse.service;

import com.estone.warehouse.bean.WhInventory;
import com.estone.warehouse.bean.WhInventoryQueryCondition;
import com.whq.tool.component.Pager;
import java.util.List;

public interface WhInventoryService {
    /**
     * This method corresponds to the database table wh_inventory
     *
     * @mbggenerated Fri Aug 17 15:40:11 CST 2018
     */
    List<WhInventory> queryAllWhInventorys();

    /**
     * This method corresponds to the database table wh_inventory
     *
     * @mbggenerated Fri Aug 17 15:40:11 CST 2018
     */
    List<WhInventory> queryWhInventorys(WhInventoryQueryCondition query, Pager pager);

    /**
     * This method corresponds to the database table wh_inventory
     *
     * @mbggenerated Fri Aug 17 15:40:11 CST 2018
     */
    WhInventory getWhInventory(Integer id);

    /**
     * This method corresponds to the database table wh_inventory
     *
     * @mbggenerated Fri Aug 17 15:40:11 CST 2018
     */
    WhInventory getWhInventoryDetail(Integer id);

    /**
     * This method corresponds to the database table wh_inventory
     *
     * @mbggenerated Fri Aug 17 15:40:11 CST 2018
     */
    WhInventory queryWhInventory(WhInventoryQueryCondition query);

    /**
     * This method corresponds to the database table wh_inventory
     *
     * @mbggenerated Fri Aug 17 15:40:11 CST 2018
     */
    void createWhInventory(WhInventory whInventory);

    /**
     * This method corresponds to the database table wh_inventory
     *
     * @mbggenerated Fri Aug 17 15:40:11 CST 2018
     */
    void batchCreateWhInventory(List<WhInventory> entityList);

    /**
     * This method corresponds to the database table wh_inventory
     *
     * @mbggenerated Fri Aug 17 15:40:11 CST 2018
     */
    void deleteWhInventory(Integer id);

    /**
     * This method corresponds to the database table wh_inventory
     *
     * @mbggenerated Fri Aug 17 15:40:11 CST 2018
     */
    void updateWhInventory(WhInventory whInventory);

    /**
     * This method corresponds to the database table wh_inventory
     *
     * @mbggenerated Fri Aug 17 15:40:11 CST 2018
     */
    void batchUpdateWhInventory(List<WhInventory> entityList);

    List<WhInventory> queryWhInventorysWithItems(WhInventoryQueryCondition query, Pager pager);
    
    void addAllocationInventory(WhInventory whInventory);

    /**
     * 未调整的sku数量
     * @param query
     * @return
     */
    int existNotAdjustSku(WhInventoryQueryCondition query);
}