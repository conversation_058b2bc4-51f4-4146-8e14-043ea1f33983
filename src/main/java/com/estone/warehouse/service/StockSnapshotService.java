package com.estone.warehouse.service;

import com.estone.checkin.enums.CheckInWhType;
import com.estone.warehouse.bean.StockSnapshot;
import com.estone.warehouse.bean.StockSnapshotQueryCondition;
import com.whq.tool.component.Pager;
import java.util.List;

public interface StockSnapshotService {
    List<StockSnapshot> queryAllStockSnapshots();

    List<StockSnapshot> queryStockSnapshots(StockSnapshotQueryCondition query, Pager pager);

    StockSnapshot getStockSnapshot(Integer id);

    StockSnapshot getStockSnapshotDetail(Integer id);

    StockSnapshot queryStockSnapshot(StockSnapshotQueryCondition query);

    void createStockSnapshot(StockSnapshot stockSnapshot);

    void batchCreateStockSnapshot(List<StockSnapshot> entityList);

    void deleteStockSnapshot(Integer id);
    void deleteByCountDate(String countDate,CheckInWhType checkInWhType);

    void updateStockSnapshot(StockSnapshot stockSnapshot);

    void batchUpdateStockSnapshot(List<StockSnapshot> entityList);

    List<StockSnapshot> queryStockSnapshotByCheckInType(CheckInWhType checkInType);
}