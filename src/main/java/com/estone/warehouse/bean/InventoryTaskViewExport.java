package com.estone.warehouse.bean;

/**
 * <AUTHOR>
 * @version 1.0
 * @Description 盘点人员统计报表模型
 * @date 2019/9/10 11:04
 */
public class InventoryTaskViewExport {

    /**
     * 盘点人
     */
    private Integer inventoryUser;

    private String inventoryUserName;

    /**
     * 盘点次数
     */
    private Integer taskCount;

    /**
     * 盘点SKU
     */
    private Integer skuCount;

    /**
     * 盘点PCS量
     */
    private Integer pcsCount;

    /**
     * 盘点准确率
     */
    private Double accuracy;

    /**
     * 总盘点次数
     */
    private Integer totalTaskCount;

    /**
     * 总盘点SKU
     */
    private Integer totalSkuCount;

    /**
     * 总盘点PCS量
     */
    private Integer totalPcsCount;

    /**
     * 总盘点准确率
     */
    private Double totalAccuracy;

    public Integer getInventoryUser() {
        return inventoryUser;
    }

    public void setInventoryUser(Integer inventoryUser) {
        this.inventoryUser = inventoryUser;
    }

    public Integer getTaskCount() {
        return taskCount;
    }

    public void setTaskCount(Integer taskCount) {
        this.taskCount = taskCount;
    }

    public Integer getSkuCount() {
        return skuCount;
    }

    public void setSkuCount(Integer skuCount) {
        this.skuCount = skuCount;
    }

    public Integer getPcsCount() {
        return pcsCount;
    }

    public void setPcsCount(Integer pcsCount) {
        this.pcsCount = pcsCount;
    }

    public Double getAccuracy() {
        return accuracy;
    }

    public void setAccuracy(Double accuracy) {
        this.accuracy = accuracy;
    }

    public Integer getTotalTaskCount() {
        return totalTaskCount;
    }

    public void setTotalTaskCount(Integer totalTaskCount) {
        this.totalTaskCount = totalTaskCount;
    }

    public Integer getTotalSkuCount() {
        return totalSkuCount;
    }

    public void setTotalSkuCount(Integer totalSkuCount) {
        this.totalSkuCount = totalSkuCount;
    }

    public Integer getTotalPcsCount() {
        return totalPcsCount;
    }

    public void setTotalPcsCount(Integer totalPcsCount) {
        this.totalPcsCount = totalPcsCount;
    }

    public Double getTotalAccuracy() {
        return totalAccuracy;
    }

    public void setTotalAccuracy(Double totalAccuracy) {
        this.totalAccuracy = totalAccuracy;
    }

    public String getInventoryUserName() {
        return inventoryUserName;
    }

    public void setInventoryUserName(String inventoryUserName) {
        this.inventoryUserName = inventoryUserName;
    }
}
