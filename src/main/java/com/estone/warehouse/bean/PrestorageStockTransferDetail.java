package com.estone.warehouse.bean;

import com.estone.checkin.enums.CheckInWhType;
import com.estone.warehouse.enums.PrestorageStockTransferOrderTypeEnum;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2023-03-09 17:39
 */
@Data
public class PrestorageStockTransferDetail {

    /**
     * id
     */
    private Integer id;

    /**
     * 仓库类型
     */
    private Integer warehouseType;

    /**
     * 任务号
     */
    private String orderNo;

    /**
     * 任务Id
     */
    private Integer orderId;

    /**
     * 任务类型
     */
    private Integer orderType;

    /**
     * sku
     */
    private String sku;

    /**
     * sku名称
     */
    private String skuName;

    /**
     * 迁出库位，当为中转仓时，其为中转仓库存ID
     */
    private String emigrationLocationNumber;

    /**
     * 迁入库位
     */
    private String immigrationLocationNumber;

    /**
     * 迁移数量
     */
    private Integer migrationSkuAmount;

    /**
     * 拣货数量
     */
    private Integer pickingSkuAmount;

    /**
     * 上架数量
     */
    private Integer uploadSkuAmount;

    /**
     * 仓库类型名称
     */
    public String getWarehouseTypeStr() {
        return CheckInWhType.getNameByCode(String.valueOf(this.getWarehouseType()));
    }

    /**
     * 订单类型名称
     */
    public String getOrderTypeStr() {
        return PrestorageStockTransferOrderTypeEnum.getInstanceByCode(this.getOrderType()).getName();
    }
}
