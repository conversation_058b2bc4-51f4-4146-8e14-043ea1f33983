package com.estone.warehouse.bean;

import lombok.Data;

import java.sql.Timestamp;

@Data
public class WhPackagingMaterialPurchaseItemDTO extends WhPackagingMaterialPurchaseItem {

    /**
     * 耗材采购明细id
     */
    private Integer itemId;

    /**
     * 耗材采购需求单号
     */
    private String taskNo;

    /**
     * 审批通过时间
     */
    private Timestamp warehouseVerifyDate;

    /**
     * 采购单号
     */
    private String purchaseOrderNo;

    /**
     * 采购数量
     */
    private Integer purchaseQuantity;

    /**
     * 已到货数量
     */
    private Integer upQuantity;

    private Integer diffQuantity;

    private String materialArticleNumber;

    public String getGatherFields(){
        return taskNo+"-"+materialArticleNumber;
    }

    private String purchaseQuantityStr;

    private String upQuantityStr;
}