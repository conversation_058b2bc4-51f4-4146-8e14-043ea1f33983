package com.estone.warehouse.bean;

import lombok.Data;

import java.util.List;

@Data
public class SkuLabelReturnQueryCondition{

    private Boolean readOnly = false;

    private String startTime;

    private String endTime;

    private Integer dateType;

    /**
     * limit 限定数据量
     */
    private int size = 10;
    /**
     * 页码
     */
    private int page = 1;

    //标签数量
    private Integer tagCount=0;

}