package com.estone.warehouse.bean;

import lombok.Data;

import java.util.List;

@Data
public class InventoryAbnormalDataQueryCondition extends InventoryAbnormalData {
    private static final long serialVersionUID = 1L;

    private Boolean readOnly = false;

    private List<Integer> ids;

    private String fromInventoryDate;

    private String endInventoryDate;

    private List<Integer> nextStepTaskIds;
}