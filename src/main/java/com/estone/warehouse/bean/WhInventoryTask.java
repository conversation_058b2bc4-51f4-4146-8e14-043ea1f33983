package com.estone.warehouse.bean;

import java.io.Serializable;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import org.apache.commons.collections.CollectionUtils;

import lombok.Data;

/**
 * 
 * @ClassName: WhInventoryTask
 * @Description: 盘点任务
 * <AUTHOR>
 * @date 2019年7月4日
 * @version 0.0.2
 *
 */
@Data
public class WhInventoryTask implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键 This field corresponds to the database column wh_inventory_task.id
     *
     * @mbggenerated Wed Jul 03 16:15:25 CST 2019
     */
    private Integer id;

    /**
     * 盘点任务号前缀 This field corresponds to the database column
     * wh_inventory_task.task_no_prefix
     *
     * @mbggenerated Wed Jul 03 16:15:25 CST 2019
     */
    private String taskNoPrefix;

    /**
     * 盘点任务号 This field corresponds to the database column
     * wh_inventory_task.task_no
     *
     * @mbggenerated Wed Jul 03 16:15:25 CST 2019
     */
    private String taskNo;

    /**
     * 盘点任务类型 This field corresponds to the database column
     * wh_inventory_task.task_type
     *
     * @mbggenerated Wed Jul 03 16:15:25 CST 2019
     */
    private Integer taskType;

    /**
     * 盘点任务状态 This field corresponds to the database column
     * wh_inventory_task.status
     *
     * @mbggenerated Wed Jul 03 16:15:25 CST 2019
     */
    private Integer status;

    /**
     * 盘点过程 This field corresponds to the database column
     * wh_inventory_task.task_level
     *
     * @mbggenerated Wed Jul 03 16:15:25 CST 2019
     */
    private Integer taskLevel;

    /**
     * 创建人 This field corresponds to the database column
     * wh_inventory_task.creation_user
     *
     * @mbggenerated Wed Jul 03 16:15:25 CST 2019
     */
    private Integer creationUser;

    /**
     * 创建时间 This field corresponds to the database column
     * wh_inventory_task.creation_date
     *
     * @mbggenerated Wed Jul 03 16:15:25 CST 2019
     */
    private Timestamp creationDate;

    /**
     * 领取人 This field corresponds to the database column
     * wh_inventory_task.receive_user
     *
     * @mbggenerated Wed Jul 03 16:15:25 CST 2019
     */
    private Integer receiveUser;

    /**
     * 领取时间 This field corresponds to the database column
     * wh_inventory_task.receive_date
     *
     * @mbggenerated Wed Jul 03 16:15:25 CST 2019
     */
    private Timestamp receiveDate;

    /**
     * 盘点人 This field corresponds to the database column
     * wh_inventory_task.inventory_user
     *
     * @mbggenerated Wed Jul 03 16:15:25 CST 2019
     */
    private Integer inventoryUser;

    /**
     * 盘点时间 This field corresponds to the database column
     * wh_inventory_task.inventory_date
     * 也是盘点审核界面的完成时间
     *
     * @mbggenerated Wed Jul 03 16:15:25 CST 2019
     */
    private Timestamp inventoryDate;

    /**
     * 审核人 This field corresponds to the database column
     * wh_inventory_task.review_user
     *
     * @mbggenerated Wed Jul 03 16:15:25 CST 2019
     */
    private Integer reviewUser;

    /**
     * 审核时间 This field corresponds to the database column
     * wh_inventory_task.review_date
     *
     * @mbggenerated Wed Jul 03 16:15:25 CST 2019
     */
    private Timestamp reviewDate;

    // 优先级
    private Integer grade;

    // 上一个状态, 更新时用
    private Integer lastStatus;

    // 盘点次数
    private Integer inventoryCount;

    private List<WhInventoryTaskItem> whInventoryTaskItems = new ArrayList<>();
    
    // 关联的盘点任务
    private WhInventoryTask firstTask;
    private WhInventoryTask repeatTask;
    private WhInventoryTask finallyTask;
    private WhInventoryTask confirmTask;

    public String getTaskLevelName() {
        if (this.taskLevel != null) {
            for (InventoryTaskLevel type : InventoryTaskLevel.values()) {
                if (type.intCode().equals(this.taskLevel)) {
                    return type.getName();
                }
            }
        }
        return null;
    }

    public String getTaskTypeName() {
        if (this.taskType != null) {
            for (InventoryTaskType type : InventoryTaskType.values()) {
                if (type.intCode().equals(this.taskType)) {
                    return type.getName();
                }
            }
        }
        return null;
    }

    public String getStatusName() {
        if (this.status != null) {
            for (InventoryTaskStatus type : InventoryTaskStatus.values()) {
                if (type.intCode().equals(this.status)) {
                    return type.getName();
                }
            }
        }
        return null;
    }

    public Integer getQuantity() {
        if (CollectionUtils.isNotEmpty(whInventoryTaskItems)) {
            return whInventoryTaskItems.size();
        }
        return null;
    }

    /**
     * 
     * @Description: 确认SKU数
     */
    public Integer getConfirmQuantity() {
        if (CollectionUtils.isNotEmpty(whInventoryTaskItems)) {
            int count = 0;
            for (WhInventoryTaskItem item : whInventoryTaskItems) {
                if (item.getConfirmQuantity() != null) {
                    count++;
                }
            }
            return count;
        }
        return null;
    }

    /**
     * 用于获取审核完成时间
     * @return
     */
    public Timestamp getLastItemAuditDate(){
        if (CollectionUtils.isEmpty(whInventoryTaskItems)){
            return null;
        }
        List<WhInventoryTaskItem> collect = whInventoryTaskItems.stream()
                .filter(item -> Objects.equals(InventoryTaskStatus.COMPLETED.intCode(), item.getStatus()))
                .filter(item -> Objects.nonNull(item.getReviewDate()))
                .sorted(Comparator.comparing(WhInventoryTaskItem::getReviewDate).reversed())
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(collect)) {
            return collect.get(0).getReviewDate();
        }
        return this.getReviewDate();
    }

    /**
     * 用于获取待审核sku数
     * @return
     */
    public Integer getWaitAuditQuantity(){
        if (CollectionUtils.isEmpty(whInventoryTaskItems)){
            return null;
        }
        return whInventoryTaskItems.stream()
                .filter(item -> Objects.equals(InventoryTaskStatus.UNREVIEW.intCode(), item.getStatus()))
                .collect(Collectors.toList())
                .size();
    }
}