package com.estone.warehouse.bean;

import lombok.Data;

import java.sql.Timestamp;

@Data
public class SkuLabelReturnVO {
    // 海外退件时间
    private Timestamp overseaReturnTime;
    //平台
    private Integer platform;

    //日期
    private String dateStr;

    //SKU标签
    private String skuLabel;

    //SMT
    private Integer smtQuantity=0;

    //虾皮
    private Integer shopeeQuantity=0;

    //亚马逊
    private Integer amazonQuantity=0;

    //Lazada
    private Integer lazadaQuantity=0;


    //Ebay
    private Integer ebayQuantity=0;

    //Wish
    private Integer wishQuantity=0;

    //Joom
    private Integer joomQuantity=0;

    //沃尔玛
    private Integer walmartQuantity=0;

    //其他平台
    private Integer otherPlatformQuantity=0;

    //汇总
    public Integer getTotalQuantity(){
        return smtQuantity + shopeeQuantity + amazonQuantity + lazadaQuantity + ebayQuantity + wishQuantity + joomQuantity + walmartQuantity + otherPlatformQuantity;
    }
}
