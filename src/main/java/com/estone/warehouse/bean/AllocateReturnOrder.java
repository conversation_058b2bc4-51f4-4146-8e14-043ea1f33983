package com.estone.warehouse.bean;

import java.io.Serializable;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;

import com.estone.warehouse.enums.AllocateReturnOrderStatus;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;

/**
 * @Description 调拨返架列表
 * <AUTHOR>
 * @date 2020/5/28 11:57
 * @version 1.0
 */
@Data
public class AllocateReturnOrder implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键 database column allocate_return_order.id
     */
    private Integer id;

    /**
     * 返架列表号 database column allocate_return_order.order_no
     */
    private String orderNo;

    /**
     * 0:待打印;1:待装车;3:待签收;5:待返架;7:返架中;9:完成;11:异常完成; database column allocate_return_order.status
     */
    private Integer status;

    /**
     * 备注 database column allocate_return_order.remark
     */
    private String remark;

    /**
     * 打印日期 database column allocate_return_order.print_date
     */
    private Timestamp printDate;

    /**
     * 打印人 database column allocate_return_order.print_by
     */
    private Integer printBy;

    /**
     * 装车日期 database column allocate_return_order.load_date
     */
    private Timestamp loadDate;

    /**
     * 装车人 database column allocate_return_order.load_by
     */
    private Integer loadBy;

    /**
     * 签收日期 database column allocate_return_order.receive_date
     */
    private Timestamp receiveDate;

    /**
     * 签收人 database column allocate_return_order.receive_by
     */
    private Integer receiveBy;

    /**
     * 创建日期 database column allocate_return_order.creation_date
     */
    private Timestamp creationDate;

    /**
     * 创建人 database column allocate_return_order.create_by
     */
    private Integer createBy;

    /**
     * 最后更新时间 database column allocate_return_order.last_update_date
     */
    private Timestamp lastUpdateDate;

    /**
     * 最后修改人 database column allocate_return_order.last_updated_by
     */
    private Integer lastUpdatedBy;

    private List<AllocateReturnOrderItem> allocateReturnOrderItems = new ArrayList<>();

    public String getStatusName(){
        if (status != null){
            return AllocateReturnOrderStatus.getNameByCode(status.toString());
        }
        return null;
    }

    /**
     * SKU种类
     * @return
     */
    public Integer getSkuCount(){
        if (CollectionUtils.isNotEmpty(allocateReturnOrderItems)){
            return allocateReturnOrderItems.size();
        }
        return null;
    }

    /**
     * 商品件数
     * @return
     */
    public Integer getQuantityCount(){
        if (CollectionUtils.isNotEmpty(allocateReturnOrderItems)){
            Integer count = 0;
            for (AllocateReturnOrderItem item :allocateReturnOrderItems){
                count += item.getQuantity();
            }
            return count;
        }
        return null;
    }

    /**
     * 匹配调拨返架数量
     * @return
     */
    public Integer getMateQuantityCount(){
        if (CollectionUtils.isNotEmpty(allocateReturnOrderItems)){
            Integer count = 0;
            for (AllocateReturnOrderItem item :allocateReturnOrderItems){
                count += item.getMateQuantity();
            }
            return count;
        }
        return null;
    }

    /**
     * 新仓列表合并行数
     * @return
     */
    public Integer getRowspan(){
        if (CollectionUtils.isNotEmpty(allocateReturnOrderItems)){
            Integer rowspan = 0;
            for (AllocateReturnOrderItem item: allocateReturnOrderItems){
                rowspan += item.getRowspan();
            }
            return rowspan;
        }
        return 1;
    }

    /**
     * @Description 数据推送至新仓后要清空相关人员ID值, 新仓关联人员不一样，防止数据匹配错误
     * <AUTHOR>
     * @date 2020/5/28 17:47
     * @param:
     * @return void
     */
    public void clearData(){
        setId(null);
        setCreateBy(null);
        setPrintBy(null);
        setLoadBy(null);
        setLastUpdatedBy(null);
    }
}