package com.estone.warehouse.bean;

import lombok.Data;

import java.util.List;

@Data
public class AllocateReturnOrderQueryCondition extends AllocateReturnOrder {
    private static final long serialVersionUID = 1L;

    private String sku;

    // 是否查调拨返架单
    private boolean queryReturn = false;

    // 调拨返架单号
    private String returnNo;
    // SKU返架状态
    private Integer returnStatus;

    private List<Integer> ids;

    // 是否有匹配差异
    private Boolean mateDiff;
    // 总返架差异
    private Boolean returnDiff;
    // 扫描差异
    private Boolean scanDiff;

    // 创建时间
    private String fromCreateDate;
    private String toCreateDate;

    // 打印时间
    private String fromPrintDate;
    private String toPrintDate;

    // 装车时间
    private String fromLoadDate;
    private String toLoadDate;

    // 签收时间
    private String fromReceiveDate;
    private String toReceiveDate;

}