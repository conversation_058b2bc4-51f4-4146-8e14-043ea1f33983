package com.estone.warehouse.domain;

import com.estone.warehouse.bean.WhAbroadReturnItem;
import com.estone.warehouse.bean.WhAbroadReturnItemQueryCondition;
import com.whq.tool.component.Pager;
import java.util.ArrayList;
import java.util.List;

public class WhAbroadReturnItemDo {
    private WhAbroadReturnItem whAbroadReturnItem;

    private WhAbroadReturnItemQueryCondition query;

    private List<WhAbroadReturnItem> whAbroadReturnItems = new ArrayList<WhAbroadReturnItem>();

    private Pager page = new Pager();

    public WhAbroadReturnItem getWhAbroadReturnItem() {
        return whAbroadReturnItem;
    }

    public void setWhAbroadReturnItem(WhAbroadReturnItem whAbroadReturnItem) {
        this.whAbroadReturnItem = whAbroadReturnItem;
    }

    public WhAbroadReturnItemQueryCondition getQuery() {
        return query;
    }

    public void setQuery(WhAbroadReturnItemQueryCondition query) {
        this.query = query;
    }

    public List<WhAbroadReturnItem> getWhAbroadReturnItems() {
        return whAbroadReturnItems;
    }

    public void setWhAbroadReturnItems(List<WhAbroadReturnItem> whAbroadReturnItems) {
        this.whAbroadReturnItems = whAbroadReturnItems;
    }

    public Pager getPage() {
        return page;
    }

    public void setPage(Pager page) {
        this.page = page;
    }
}