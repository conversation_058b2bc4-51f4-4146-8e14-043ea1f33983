package com.estone.warehouse.domain;

import com.estone.warehouse.bean.*;
import com.whq.tool.component.Pager;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class WhStockFullDo {

    private WhStockFullQueryCondition query = new WhStockFullQueryCondition();

    private List<WhStockFullDTO> stockFullList = new ArrayList<>();

    private List<WhWarehouse> warehouseList = new ArrayList<WhWarehouse>();

    private Pager page = new Pager();

    // 查询结果汇总
    private WhStockFullGroupDTO stockFullGroup;

    private String locationRegionList;

    private String locationAisleList;

    private String typeList;
}