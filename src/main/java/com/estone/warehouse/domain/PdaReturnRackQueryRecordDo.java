package com.estone.warehouse.domain;

import com.estone.warehouse.bean.PdaReturnRackQueryRecord;
import com.estone.warehouse.bean.PdaReturnRackQueryRecordQueryCondition;
import com.whq.tool.component.Pager;
import java.util.ArrayList;
import java.util.List;

public class PdaReturnRackQueryRecordDo {
    private PdaReturnRackQueryRecord pdaReturnRackQueryRecord;

    private PdaReturnRackQueryRecordQueryCondition query;

    private List<PdaReturnRackQueryRecord> pdaReturnRackQueryRecords = new ArrayList<PdaReturnRackQueryRecord>();

    private Pager page = new Pager();

    public PdaReturnRackQueryRecord getPdaReturnRackQueryRecord() {
        return pdaReturnRackQueryRecord;
    }

    public void setPdaReturnRackQueryRecord(PdaReturnRackQueryRecord pdaReturnRackQueryRecord) {
        this.pdaReturnRackQueryRecord = pdaReturnRackQueryRecord;
    }

    public PdaReturnRackQueryRecordQueryCondition getQuery() {
        return query;
    }

    public void setQuery(PdaReturnRackQueryRecordQueryCondition query) {
        this.query = query;
    }

    public List<PdaReturnRackQueryRecord> getPdaReturnRackQueryRecords() {
        return pdaReturnRackQueryRecords;
    }

    public void setPdaReturnRackQueryRecords(List<PdaReturnRackQueryRecord> pdaReturnRackQueryRecords) {
        this.pdaReturnRackQueryRecords = pdaReturnRackQueryRecords;
    }

    public Pager getPage() {
        return page;
    }

    public void setPage(Pager page) {
        this.page = page;
    }
}