package com.estone.warehouse.domain;

import java.util.ArrayList;
import java.util.List;

import com.estone.system.user.bean.SaleUser;
import com.estone.warehouse.bean.WhLocationBind;
import com.estone.warehouse.bean.WhLocationBindQueryCondition;
import com.whq.tool.component.Pager;

public class WhLocationBindDo {
    private WhLocationBind whLocationBind;

    private WhLocationBindQueryCondition query;

    private List<WhLocationBind> whLocationBinds = new ArrayList<WhLocationBind>();

    private Pager page = new Pager();

    private List<SaleUser> saleUsers = new ArrayList<SaleUser>();

    public WhLocationBind getWhLocationBind() {
        return whLocationBind;
    }

    public void setWhLocationBind(WhLocationBind whLocationBind) {
        this.whLocationBind = whLocationBind;
    }

    public WhLocationBindQueryCondition getQuery() {
        return query;
    }

    public void setQuery(WhLocationBindQueryCondition query) {
        this.query = query;
    }

    public List<WhLocationBind> getWhLocationBinds() {
        return whLocationBinds;
    }

    public void setWhLocationBinds(List<WhLocationBind> whLocationBinds) {
        this.whLocationBinds = whLocationBinds;
    }

    public Pager getPage() {
        return page;
    }

    public void setPage(Pager page) {
        this.page = page;
    }

    public List<SaleUser> getSaleUsers() {
        return saleUsers;
    }

    public void setSaleUsers(List<SaleUser> saleUsers) {
        this.saleUsers = saleUsers;
    }

}