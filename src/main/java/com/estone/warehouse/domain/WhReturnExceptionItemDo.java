package com.estone.warehouse.domain;

import com.estone.warehouse.bean.WhReturnExceptionItem;
import com.estone.warehouse.bean.WhReturnExceptionItemQueryCondition;
import com.whq.tool.component.Pager;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class WhReturnExceptionItemDo {
    private WhReturnExceptionItem whReturnExceptionItem;

    private WhReturnExceptionItemQueryCondition query;

    private List<WhReturnExceptionItem> whReturnExceptionItems = new ArrayList<WhReturnExceptionItem>();

    private Pager page = new Pager();

    private String returnTypeSelect;

    private String reasonSelect;

}
