package com.estone.warehouse.domain;

import com.estone.warehouse.bean.WhSecurityCheckReturn;
import com.estone.warehouse.bean.WhSecurityCheckReturnQueryCondition;
import com.whq.tool.component.Pager;
import java.util.ArrayList;
import java.util.List;

public class WhSecurityCheckReturnDo {
    private WhSecurityCheckReturn whSecurityCheckReturn;

    private WhSecurityCheckReturnQueryCondition query;

    private List<WhSecurityCheckReturn> whSecurityCheckReturns = new ArrayList<WhSecurityCheckReturn>();

    private Pager page = new Pager();
    
    private String viewOrUpdate;

    public WhSecurityCheckReturn getWhSecurityCheckReturn() {
        return whSecurityCheckReturn;
    }

    public void setWhSecurityCheckReturn(WhSecurityCheckReturn whSecurityCheckReturn) {
        this.whSecurityCheckReturn = whSecurityCheckReturn;
    }

    public WhSecurityCheckReturnQueryCondition getQuery() {
        return query;
    }

    public void setQuery(WhSecurityCheckReturnQueryCondition query) {
        this.query = query;
    }

    public List<WhSecurityCheckReturn> getWhSecurityCheckReturns() {
        return whSecurityCheckReturns;
    }

    public void setWhSecurityCheckReturns(List<WhSecurityCheckReturn> whSecurityCheckReturns) {
        this.whSecurityCheckReturns = whSecurityCheckReturns;
    }

    public Pager getPage() {
        return page;
    }

    public void setPage(Pager page) {
        this.page = page;
    }

    public String getViewOrUpdate() {
        return viewOrUpdate;
    }

    public void setViewOrUpdate(String viewOrUpdate) {
        this.viewOrUpdate = viewOrUpdate;
    }
    
    
}