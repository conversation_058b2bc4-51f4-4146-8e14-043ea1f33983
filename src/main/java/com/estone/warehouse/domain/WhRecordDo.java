package com.estone.warehouse.domain;

import java.util.ArrayList;
import java.util.List;

import com.estone.warehouse.bean.WhRecord;
import com.estone.warehouse.bean.WhRecordGroup;
import com.estone.warehouse.bean.WhRecordQueryCondition;
import com.estone.warehouse.bean.WhWarehouse;
import com.whq.tool.component.Pager;

public class WhRecordDo {
    private WhRecord whRecord;

    private WhRecordQueryCondition query = new WhRecordQueryCondition();

    private List<WhRecord> whRecords = new ArrayList<WhRecord>();

    private List<WhWarehouse> warehouseList = new ArrayList<WhWarehouse>();

    private Pager page = new Pager();

    // 总汇总
    private WhRecordGroup totalRecordGroup;

    // 查询结果汇总
    private WhRecordGroup resultRecordGroup;

    private String locationRegionList;

    private String locationAisleList;

    public WhRecord getWhRecord() {
        return whRecord;
    }

    public void setWhRecord(WhRecord whRecord) {
        this.whRecord = whRecord;
    }

    public WhRecordQueryCondition getQuery() {
        return query;
    }

    public void setQuery(WhRecordQueryCondition query) {
        this.query = query;
    }

    public List<WhRecord> getWhRecords() {
        return whRecords;
    }

    public void setWhRecords(List<WhRecord> whRecords) {
        this.whRecords = whRecords;
    }

    public Pager getPage() {
        return page;
    }

    public void setPage(Pager page) {
        this.page = page;
    }

    public List<WhWarehouse> getWarehouseList() {
        return warehouseList;
    }

    public void setWarehouseList(List<WhWarehouse> warehouseList) {
        this.warehouseList = warehouseList;
    }

    public WhRecordGroup getTotalRecordGroup() {
        return totalRecordGroup;
    }

    public void setTotalRecordGroup(WhRecordGroup totalRecordGroup) {
        this.totalRecordGroup = totalRecordGroup;
    }

    public WhRecordGroup getResultRecordGroup() {
        return resultRecordGroup;
    }

    public void setResultRecordGroup(WhRecordGroup resultRecordGroup) {
        this.resultRecordGroup = resultRecordGroup;
    }

    public String getLocationRegionList() {
        return locationRegionList;
    }

    public void setLocationRegionList(String locationRegionList) {
        this.locationRegionList = locationRegionList;
    }

    public String getLocationAisleList() {
        return locationAisleList;
    }

    public void setLocationAisleList(String locationAisleList) {
        this.locationAisleList = locationAisleList;
    }

}