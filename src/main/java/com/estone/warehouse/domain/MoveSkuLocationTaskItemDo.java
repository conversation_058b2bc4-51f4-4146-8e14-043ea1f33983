package com.estone.warehouse.domain;

import com.estone.warehouse.bean.MoveSkuLocationTaskItem;
import com.estone.warehouse.bean.MoveSkuLocationTaskItemQueryCondition;
import com.whq.tool.component.Pager;
import java.util.ArrayList;
import java.util.List;

public class MoveSkuLocationTaskItemDo {
    private MoveSkuLocationTaskItem moveSkuLocationTaskItem;

    private MoveSkuLocationTaskItemQueryCondition query;

    private List<MoveSkuLocationTaskItem> moveSkuLocationTaskItems = new ArrayList<MoveSkuLocationTaskItem>();

    private Pager page = new Pager();

    public MoveSkuLocationTaskItem getMoveSkuLocationTaskItem() {
        return moveSkuLocationTaskItem;
    }

    public void setMoveSkuLocationTaskItem(MoveSkuLocationTaskItem moveSkuLocationTaskItem) {
        this.moveSkuLocationTaskItem = moveSkuLocationTaskItem;
    }

    public MoveSkuLocationTaskItemQueryCondition getQuery() {
        return query;
    }

    public void setQuery(MoveSkuLocationTaskItemQueryCondition query) {
        this.query = query;
    }

    public List<MoveSkuLocationTaskItem> getMoveSkuLocationTaskItems() {
        return moveSkuLocationTaskItems;
    }

    public void setMoveSkuLocationTaskItems(List<MoveSkuLocationTaskItem> moveSkuLocationTaskItems) {
        this.moveSkuLocationTaskItems = moveSkuLocationTaskItems;
    }

    public Pager getPage() {
        return page;
    }

    public void setPage(Pager page) {
        this.page = page;
    }
}