package com.estone.warehouse.domain;

import com.estone.transfer.bean.TransferStock;
import com.estone.warehouse.bean.WhPackagingMaterialStock;
import com.estone.warehouse.bean.WhPackagingMaterialStockQueryCondition;
import com.whq.tool.component.Pager;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;
@Data
public class WhPackagingMaterialStockDo {
    private WhPackagingMaterialStock whPackagingMaterialStock;

    private WhPackagingMaterialStockQueryCondition query;

    private List<WhPackagingMaterialStock> whPackagingMaterialStocks = new ArrayList<WhPackagingMaterialStock>();

    private Pager page = new Pager();

    private String typeList;

    private WhPackagingMaterialStock stockCount;

}