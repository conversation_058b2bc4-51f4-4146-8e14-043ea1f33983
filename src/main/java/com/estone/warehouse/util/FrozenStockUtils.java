package com.estone.warehouse.util;

import java.sql.Timestamp;
import java.util.*;
import java.util.stream.Collectors;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;

import com.estone.common.util.SpringUtils;
import com.estone.sku.bean.ExpManage;
import com.estone.sku.bean.ExpManageQueryCondition;
import com.estone.sku.service.ExpManageService;
import com.estone.statistics.enums.DrpTurnoverOderType;
import com.estone.warehouse.bean.FrozenStock;
import com.estone.warehouse.bean.FrozenStockQueryCondition;
import com.estone.warehouse.service.FrozenStockService;
import com.whq.tool.context.DataContextHolder;

/**
 * @Description: 冻结库存工具类
 * @Author: Yimeil
 * @Date: 2021/8/11 14:30
 * @Version: 1.0.0
 */
public class FrozenStockUtils {
    private static FrozenStockService frozenStockService = SpringUtils.getBean(FrozenStockService.class);
    private static ExpManageService expManageService = SpringUtils.getBean(ExpManageService.class);

    public static void updateTimeAndUser(Map<Integer, String> stockIdMap, boolean move) {
        //枚举MOVE_ORDER作为移库
        if (move)
            updateTimeAndUser(stockIdMap, DrpTurnoverOderType.MOVE_ORDER.intCode());
    }

    /**
     * 修改上架时间、上架人、移库时间、移库人
     * 
     * @param stockIdMap
     * @param move
     */
    public static void updateTimeAndUser(Map<Integer, String> stockIdMap, boolean move, boolean checkInUp) {
        if (checkInUp)
            updateTimeAndUser(stockIdMap, DrpTurnoverOderType.CHECK_IN.intCode());
        //枚举MOVE_ORDER作为移库
        if (move)
            updateTimeAndUser(stockIdMap, DrpTurnoverOderType.MOVE_ORDER.intCode());
    }

    public static void updateTimeAndUser(Map<Integer, String> stockIdMap, Integer type) {
        if (MapUtils.isEmpty(stockIdMap))
            return;
        if (type == null)
            return;

        boolean move = false;
        boolean checkInUp = false;
        boolean allocationOut = false;
        boolean allocationIn = false;

        DrpTurnoverOderType step = DrpTurnoverOderType.build(type.toString());
        if (step != null) {
            switch (step) {
                // 上架时间
                case CHECK_IN:
                    checkInUp = true;
                    break;
                case MOVE_ORDER:
                    move = true;
                    break;
                case ALLOCATION_OUT:
                    allocationOut = true;
                    break;
                case ALLOCATION_IN:
                    allocationIn = true;
                    break;
            }
        }

        List<Integer> stockIdList = new ArrayList<>(stockIdMap.keySet());
        FrozenStockQueryCondition frozenQuery = new FrozenStockQueryCondition();
        frozenQuery.setStockIdList(stockIdList);
        Map<Integer, FrozenStock> frozenStockMap = new HashMap<>();
        List<FrozenStock> frozenStocks = frozenStockService.queryFrozenStocks(frozenQuery, null);
        List<FrozenStock> addFrozenList = new ArrayList<>();
        List<FrozenStock> updateFrozenList = new ArrayList<>();

        if (CollectionUtils.isNotEmpty(frozenStocks)) {
            frozenStockMap = frozenStocks.stream().collect(Collectors.toMap(FrozenStock::getStockId, f -> f));
        }
        for (Integer stockId : stockIdList) {
            FrozenStock frozen = new FrozenStock();
            frozen.setStockId(stockId);
            frozen.setSku(stockIdMap.get(stockId));
            if (move) {
                frozen.setLastMoveTime(new Timestamp(System.currentTimeMillis()));
                frozen.setLastMoveUser(DataContextHolder.getUserId());
            }
            else if (checkInUp) {
                frozen.setCheckInUpTime(new Timestamp(System.currentTimeMillis()));
                frozen.setCheckInUpUser(DataContextHolder.getUserId());
            }
            else if (allocationIn) {
                frozen.setAllocationInTime(new Timestamp(System.currentTimeMillis()));
            }
            else if (allocationOut) {
                frozen.setAllocationOutTime(new Timestamp(System.currentTimeMillis()));
            }
            else {
                frozen.setLastUpTime(new Timestamp(System.currentTimeMillis()));
                frozen.setLastUpUser(DataContextHolder.getUserId());
            }
            if (frozenStockMap.get(stockId) == null) {
                frozen.setCreateBy(DataContextHolder.getUserId());
                frozen.setLastUpdatedBy(DataContextHolder.getUserId());
                addFrozenList.add(frozen);
            }
            else {
                frozen.setId(frozenStockMap.get(stockId).getId());
                updateFrozenList.add(frozen);
            }
        }

        if (CollectionUtils.isNotEmpty(addFrozenList)) {
            frozenStockService.batchCreateFrozenStock(addFrozenList);
        }

        if (CollectionUtils.isNotEmpty(updateFrozenList)) {
            frozenStockService.batchUpdateFrozenStock(updateFrozenList);
        }
    }


    /**
     * 更新保质期时间
     * 
     * @param stockIds
     */
    public static void updateExpDate(List<Integer> stockIds) {
        if (CollectionUtils.isEmpty(stockIds))
            return;
        ExpManageQueryCondition expQuery = new ExpManageQueryCondition();
        expQuery.setStockIdList(stockIds);
        expQuery.setAllCheckOut(false);
        List<ExpManage> expManages = expManageService.queryExpManages(expQuery, null);
        if (CollectionUtils.isEmpty(expManages))
            return;
        // 根据stockId分组取最小的时间
        Map<Integer, Optional<ExpManage>> expDateMap = expManages.stream().filter(e -> e.getExpDate() != null)
                .collect(Collectors.groupingBy(ExpManage::getStockId,
                        Collectors.minBy(Comparator.comparing(ExpManage::getExpDate))));

        if (MapUtils.isEmpty(expDateMap))
            return;

        FrozenStockQueryCondition query = new FrozenStockQueryCondition();
        query.setStockIdList(new ArrayList<>(expDateMap.keySet()));
        List<FrozenStock> frozenStocks = frozenStockService.queryFrozenStocks(query, null);
        List<FrozenStock> updateFrozenList = new ArrayList<>();
        List<FrozenStock> addList = new ArrayList<>();
        if (CollectionUtils.isEmpty(frozenStocks)) {
            expDateMap.forEach((key, value) -> {
                if (value.isEmpty())
                    return;
                FrozenStock frozen = new FrozenStock();
                frozen.setStockId(key);
                frozen.setSku(value.get().getSku());
                frozen.setMinExpDate(value.get().getExpDate());
                frozen.setCreationDate(new Timestamp(System.currentTimeMillis()));
                frozen.setLastUpdateDate(new Timestamp(System.currentTimeMillis()));
                addList.add(frozen);
            });
        }
        else {
            frozenStocks.forEach(frozen -> {
                Optional<ExpManage> expManageOptional = expDateMap.get(frozen.getStockId());
                if (expManageOptional.isPresent() && (frozen.getMinExpDate() == null
                        || expManageOptional.get().getExpDate().before(frozen.getMinExpDate()))) {
                    FrozenStock updatedStock = new FrozenStock();
                    updatedStock.setId(frozen.getId());
                    updatedStock.setMinExpDate(expManageOptional.get().getExpDate());
                    updateFrozenList.add(updatedStock);
                }
            });
        }
        frozenStockService.batchCreateFrozenStock(addList);
        frozenStockService.batchUpdateFrozenStock(updateFrozenList);
    }
}
