package com.estone.warehouse.util;

import com.estone.warehouse.enums.StockLogType;
import org.apache.commons.lang.StringUtils;

/**
 * <AUTHOR>
 * @version 1.0
 * @Description TODO
 * @date 2019/12/3 10:37
 */
public class WhStockLogUtils {
    public static String tableIndex(String sku, Integer type) {
        if (StringUtils.isBlank(sku) || type == null){
            throw new IllegalArgumentException("SKU 、TYPE 不能为空!");
        }
        if(type.equals(StockLogType.FROZEN.intCode())){
            // 冻结库存日志只存入wh_stock_log_0_12 一张表中
            return 0 + "_" + type;
        }
        if(type.equals(StockLogType.BATCH_STOCK.intCode())){
            // 退货在途库存日志只存入wh_stock_log_0_13 一张表中
            return 0 + "_" + type;
        }

        if(type.equals(StockLogType.LEND_ONWAY_STOCK.intCode())){
            // 外借在途库存日志只存入wh_stock_log_0_14 一张表中
            return 0 + "_" + type;
        }

        if(type.equals(StockLogType.LEND_STOCK.intCode())){
            // 外借库存日志只存入wh_stock_log_0_15 一张表中
            return 0 + "_" + type;
        }

        if(type.equals(StockLogType.BAD_PRODUCT_STOCK.intCode())){
            // 外借库存日志只存入wh_stock_log_0_16 一张表中
            return 0 + "_" + type;
        }

        if(type.equals(StockLogType.SCRAP_STOCK.intCode())){
            // 外借库存日志只存入wh_stock_log_0_17 一张表中
            return 0 + "_" + type;
        }

        if(type.equals(StockLogType.RETURN_STOCK.intCode())){
            // 退换货库存日志只存入wh_stock_log_0_18 一张表中
            return 0 + "_" + type;
        }
        if (type.equals(StockLogType.ALLOCATION_ON_WAY_STOCK.intCode())) {
            return (7 & sku.hashCode()) + "_" + StockLogType.STOCK_ALLOCATION.intCode();
        }

        return (7 & sku.hashCode()) + "_" + type;
    }
}
