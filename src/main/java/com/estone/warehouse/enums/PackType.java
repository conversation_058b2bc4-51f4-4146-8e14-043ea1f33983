package com.estone.warehouse.enums;
/**
 * 
 * @Description:
 * 
 * @ClassName: PackType
 * @Author: qinyangkai
 * @Date: 2018年8月18日
 * @Version: 0.0.1
 */
public enum PackType {
    SINGLESINGLE_PAC("单件包装(优选仓)", "1"),
    SINGLESINGLE_100("单件包装(100)", "2"),
    SINGLESINGLE_150("单件包装(150)", "3"),
    SINGLESINGLE_EXPRES("单件包装(快递)", "4"),
    SINGLEMULTIPLE_100("单品包装(100)", "5"),
    SINGLEMULTIPLE_150("单品包装(150)", "6"),
    SINGLEMULTIPLE("多件包装", "7"),
    SINGLEMULTIPLE_PAC("多件包装(优选仓)", "8"),
    MULTIPLE("多品包装", "9"),
    SINGLESINGLE_RX("单件包装(热销)", "10"),
    SINGLEMULTIPLE_RX("多件包装(热销)", "11"),

    JITSINGLESINGLE("中转仓单件包装", "12"),
    JITSINGLEMULTIPLE("中转仓多件包装", "13"),
    JITMULTIPLE("中转仓多品包装", "14"),

    ZHIFA("直发包装", "16"),
    TEMU_PAC("拼多多包装", "17"),
    SINGLESINGLE("单件包装", "18"),
    SINGLE("单品包装", "19"),
    ;

    private String code;

    private String name;

    private PackType(String name, String code ) {
        this.name = name;
        this.code = code;
    }

    public String getCode() {
        return code;
    }

    public Integer intCode() {
        return Integer.valueOf(code);
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public static String getNameByCode(String code) {
        PackType[] values = values();
        for (PackType type : values) {
            if (type.code.equals(code)) {
                return type.getName();
            }
        }
        return null;
    }


    public static PackType build(String code) {
        PackType[] values = values();

        for (PackType type : values) {
            if (type.code.equals(code)) {
                return type;
            }
        }

        return null;
    }
}
