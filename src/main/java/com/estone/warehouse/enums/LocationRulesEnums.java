package com.estone.warehouse.enums;

/**
 * 
 * @Description: 库位规则状态
 * @Author: ch<PERSON><PERSON>yong
 * @Date: 2019/03/05
 * @Version: 0.0.1
 */
public enum LocationRulesEnums {
    STARTUP("启动", "1"), CLOSEUP("禁用", "2");

    private String code;

    private String name;

    private LocationRulesEnums(String name, String code) {
        this.name = name;
        this.code = code;
    }

    public String getCode() {
        return code;
    }

    public Integer intCode() {
        return Integer.valueOf(code);
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public static String getNameByCode(String code) {
        LocationRulesEnums[] values = values();
        for (LocationRulesEnums type : values) {
            if (type.code.equals(code)) {
                return type.getName();
            }
        }
        return null;
    }

    public static Integer getCodeByName(String name) {
        LocationRulesEnums[] values = values();
        for (LocationRulesEnums type : values) {
            if (type.name.equals(name)) {
                return Integer.valueOf(type.getCode());
            }
        }
        return null;
    }
}
