package com.estone.warehouse.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2023-03-10 10:21
 */
@AllArgsConstructor
@Getter
public enum PrintStatusEnum {
    UNCOMPLETED_PRINT(0, "未打印"),
    COMPLETED_PRINT(1, "已打印"),
    ;
    /**
     * 编号
     */
    private Integer code;

    /**
     * 名称
     */
    private String name;

    /**
     * 通过编号获取对象
     *
     * @param code 编号
     * @return 对象或为null
     */
    public static PrintStatusEnum getInstanceByCode(int code) {
        return Arrays.stream(values())
                .filter(v -> Objects.equals(v.getCode(), code))
                .findAny()
                .orElse(null);
    }
}
