package com.estone.warehouse.enums;


public enum ItemOrderTypeEnum {

    EXPRESS_RECEIPT("快递单", "1"),
    WH_CHECK_IN_EXCEPTION("入库异常单", "2"),
    ;
    private String code;

    private String name;

    private ItemOrderTypeEnum(String name, String code) {
        this.name = name;
        this.code = code;
    }

    public String getCode() {
        return code;
    }

    public Integer intCode() {
        return Integer.valueOf(code);
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public static String getNameByCode(String code) {
        ItemOrderTypeEnum[] values = values();
        for (ItemOrderTypeEnum type : values) {
            if (type.code.equals(code)) {
                return type.getName();
            }
        }
        return null;
    }
}
