package com.estone.warehouse.enums;

/**
 * <AUTHOR>
 * @version 1.0
 * @Description 调拨返架列表状态
 * @date 2020/5/28 15:58
 */
public enum AllocateReturnOrderStatus {
    //0:待打印;1:待装车;3:待签收;5:待返架;7:返架中;9:完成;11:异常完成;',
    WAIT_PRINT("待打印", "0"),
    WAIT_LOAD("待装车", "1"),
    WAIT_RECEIVE("待签收", "3"),
    WAIT_RETURN("待返架", "5"),
    RETURNNING("返架中", "7"),
    COMPLETE("完成", "9");
    //EXCEPTION_COMPLETE("异常完成", "11");

    private String name;
    private String code;

    private AllocateReturnOrderStatus(String name, String code) {
        this.name = name;
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public Integer intCode() {
        return Integer.valueOf(code);
    }

    public static String getNameByCode(String code) {
        AllocateReturnOrderStatus[] values = AllocateReturnOrderStatus.values();
        for (AllocateReturnOrderStatus status : values) {
            if (status.code.equals(code)) {
                return status.name;
            }
        }
        return null;
    }

    public static AllocateReturnOrderStatus[] getOldWarehosStatus() {
        AllocateReturnOrderStatus[] status = new AllocateReturnOrderStatus[]{WAIT_PRINT, WAIT_LOAD, COMPLETE};
        return status;
    }

    public static AllocateReturnOrderStatus[] getNewWarehosStatus() {
        AllocateReturnOrderStatus[] status = new AllocateReturnOrderStatus[]{WAIT_RECEIVE, WAIT_RETURN, RETURNNING, COMPLETE};
        return status;
    }
}
