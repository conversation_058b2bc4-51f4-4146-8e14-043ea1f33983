package com.estone.warehouse.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2023-03-17 16:52
 */
@AllArgsConstructor
@Getter
public enum PrestorageStockRuleTypeEnum {

    DOMINATE_SKU(0, "指定sku"),
    CONDITION(1, "根据条件判断"),
    ALL_SKU(2, "全部sku"),
    ;
    private Integer code;

    private String name;

    public static PrestorageStockRuleTypeEnum getInstanceByCode(Integer code) {
        return Arrays.stream(values())
                .filter(v -> Objects.equals(v.getCode(), code))
                .findAny()
                .orElse(null);
    }
}
