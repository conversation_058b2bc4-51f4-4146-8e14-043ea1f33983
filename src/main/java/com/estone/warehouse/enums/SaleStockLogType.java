package com.estone.warehouse.enums;

public enum SaleStockLogType {
    APV("APV", "APV"), // 订单处理平台库存

    SPAN_APV("SPAN_APV", "SPAN_APV"), // 跨仓订单处理平台库存

    UP("UP", "UP"), // 上架处理平台库存

    CREATE_ALLOCATION_ORDER("CREATE_ALLOCATION_ORDER", "CREATE_ALLOCATION_ORDER"), // 创建库存调拨

    INVENTORY("INVENTORY", "INVENTORY"); // 盘点处理平台库存

    private String code;

    private String name;

    private SaleStockLogType(String name, String code) {
        this.name = name;
        this.code = code;
    }

    public String getName() {
        return this.name;
    }
}
