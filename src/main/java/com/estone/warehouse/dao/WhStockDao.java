package com.estone.warehouse.dao;

import com.estone.foreign.bean.OnWayStockDTO;
import com.estone.warehouse.bean.*;
import com.whq.tool.component.Pager;

import java.util.List;
import java.util.Map;

public interface WhStockDao {
    int queryWhStockCount(WhStockQueryCondition query);

    List<WhStock> queryWhStockList();

    List<WhStock> queryWhStockList(WhStockQueryCondition query, Pager pager);

    WhStock queryWhStock(Integer primaryKey);

    WhStock queryWhStock(WhStockQueryCondition query);

    void createWhStock(WhStock entity);

    void batchCreateWhStock(List<WhStock> entityList);

    void batchUpdateWhStock(List<WhStock> entityList);

    void updateWhStock(WhStock entity);

    void deleteWhStock(Integer id);

    List<WhStock> queryStockQuantityBySkuAndType(List<String> skus,List<Integer> stockIds, Integer type);

    /**
     * 分页查询库存相关信息
     * 
     * @param query
     * @return
     */
    int queryPageStockCount(WhStockQueryCondition query);

    List<WhStock> queryPageStocks(WhStockQueryCondition query, Pager pager);


    /**
     * 查询中转仓及本地仓库存总数
     *
     * @param query
     * @return
     */
    int queryPageStockFullCount(WhStockFullQueryCondition query);
    /**
     *  分页查询中转仓及本地仓库存
     * @param query
     * @return
     */
    List<WhStockFullDTO> queryPageStocksFull(WhStockFullQueryCondition query, Pager pager);

    /**
     *  查询中转仓及本地仓库存汇总
     */
    WhStockFullGroupDTO queryStockFullGroup(WhStockFullQueryCondition query);

    WhStockGroup queryStockGroup(WhStockQueryCondition query, Boolean isTotal);

    /**
     * PMS同步最近8小时修改过sku的可用库存
     *
     * @param query
     * @return
     */
    List<WhStock> querySyncPmsStock(WhStockQueryCondition query);

    /**
     * 统计每天23点59分59秒仓库库存和可用库存
     *
     * @return
     */
    Map<String, Object> querySurplusQuantityAndAllotQuantityCount(Integer warehouseId);

    /**
     * 查询本地仓及中转仓库位库存列表
     * @param skus
     * @param locationNumber
     * @return
     */
    List<WhStock> listFullStockBySku(List<String> skus, String locationNumber);

    /**
     * 捞取总仓库库存为0的拣货、存货库存sku
     *
     * @param skus
     * @return
     */
    List<WhStock> queryZeroStockList(List<String> skus,Integer checkInType);

    /**
     * 移除标签
     * 
     * @param stockId
     */
    void removeLocationTag(Integer stockId);

    List<String> getLocationBySkuAndType(List<Integer> ids, List<String> skus, Integer type);

    List<InventoryQueryResult> queryReturnSkuStock(String sku);

    /**
     * 统计每天23点59分59秒仓库库存和可用库存
     *
     * @return
     */
    Map<String, Object> queryTransferSkuAndStockCount();

    List<String> existZfStockSkuList();
    List<OnWayStockDTO> getOnWayStockList(List<String> skus);

    List<WhStock> queryNotZeroStockList(List<String> skus);
}