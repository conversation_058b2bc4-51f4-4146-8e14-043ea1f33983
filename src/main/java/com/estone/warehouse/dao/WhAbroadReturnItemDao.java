package com.estone.warehouse.dao;

import com.estone.warehouse.bean.WhAbroadReturnItem;
import com.estone.warehouse.bean.WhAbroadReturnItemQueryCondition;
import com.whq.tool.component.Pager;
import java.util.List;

public interface WhAbroadReturnItemDao {
    /**
     * This method corresponds to the database table wh_abroad_return_item
     *
     * @mbggenerated Tue Oct 23 17:36:07 CST 2018
     */
    int queryWhAbroadReturnItemCount(WhAbroadReturnItemQueryCondition query);

    /**
     * This method corresponds to the database table wh_abroad_return_item
     *
     * @mbggenerated Tue Oct 23 17:36:07 CST 2018
     */
    List<WhAbroadReturnItem> queryWhAbroadReturnItemList();

    /**
     * This method corresponds to the database table wh_abroad_return_item
     *
     * @mbggenerated Tue Oct 23 17:36:07 CST 2018
     */
    List<WhAbroadReturnItem> queryWhAbroadReturnItemList(WhAbroadReturnItemQueryCondition query, Pager pager);

    /**
     * This method corresponds to the database table wh_abroad_return_item
     *
     * @mbggenerated Tue Oct 23 17:36:07 CST 2018
     */
    WhAbroadReturnItem queryWhAbroadReturnItem(Integer primaryKey);

    /**
     * This method corresponds to the database table wh_abroad_return_item
     *
     * @mbggenerated Tue Oct 23 17:36:07 CST 2018
     */
    WhAbroadReturnItem queryWhAbroadReturnItem(WhAbroadReturnItemQueryCondition query);

    /**
     * This method corresponds to the database table wh_abroad_return_item
     *
     * @mbggenerated Tue Oct 23 17:36:07 CST 2018
     */
    void createWhAbroadReturnItem(WhAbroadReturnItem entity);

    /**
     * This method corresponds to the database table wh_abroad_return_item
     *
     * @mbggenerated Tue Oct 23 17:36:07 CST 2018
     */
    void batchCreateWhAbroadReturnItem(List<WhAbroadReturnItem> entityList);

    /**
     * This method corresponds to the database table wh_abroad_return_item
     *
     * @mbggenerated Tue Oct 23 17:36:07 CST 2018
     */
    void batchUpdateWhAbroadReturnItem(List<WhAbroadReturnItem> entityList);

    /**
     * This method corresponds to the database table wh_abroad_return_item
     *
     * @mbggenerated Tue Oct 23 17:36:07 CST 2018
     */
    void deleteWhAbroadReturnItem(Integer primaryKey);

    /**
     * This method corresponds to the database table wh_abroad_return_item
     *
     * @mbggenerated Tue Oct 23 17:36:07 CST 2018
     */
    int updateWhAbroadReturnItem(WhAbroadReturnItem entity);
    
    void deleteWhAbroadReturnItemByReturnId(Integer returnId);
}