package com.estone.warehouse.dao;

import com.estone.warehouse.bean.WhPackagingMaterialPurchaseItem;
import com.estone.warehouse.bean.WhPackagingMaterialPurchaseItemDTO;
import com.estone.warehouse.bean.WhPackagingMaterialPurchaseItemQueryCondition;
import com.whq.tool.component.Pager;
import java.util.List;

public interface WhPackagingMaterialPurchaseItemDao {
    int queryWhPackagingMaterialPurchaseItemCount(WhPackagingMaterialPurchaseItemQueryCondition query);

    List<WhPackagingMaterialPurchaseItem> queryWhPackagingMaterialPurchaseItemList();

    List<WhPackagingMaterialPurchaseItem> queryWhPackagingMaterialPurchaseItemList(WhPackagingMaterialPurchaseItemQueryCondition query, Pager pager);

    WhPackagingMaterialPurchaseItem queryWhPackagingMaterialPurchaseItem(Integer primaryKey);

    WhPackagingMaterialPurchaseItem queryWhPackagingMaterialPurchaseItem(WhPackagingMaterialPurchaseItemQueryCondition query);

    void createWhPackagingMaterialPurchaseItem(WhPackagingMaterialPurchaseItem entity);

    void batchCreateWhPackagingMaterialPurchaseItem(List<WhPackagingMaterialPurchaseItem> entityList);

    void batchUpdateWhPackagingMaterialPurchaseItem(List<WhPackagingMaterialPurchaseItem> entityList);

    void deleteWhPackagingMaterialPurchaseItem(Integer primaryKey);

    void updateWhPackagingMaterialPurchaseItem(WhPackagingMaterialPurchaseItem entity);

    List<WhPackagingMaterialPurchaseItemDTO> queryWhPackagingMaterialPurchaseItemDtoList(WhPackagingMaterialPurchaseItemQueryCondition query, Pager pager);

    List<WhPackagingMaterialPurchaseItemDTO> queryMaterialPurchaseItemDtoList(WhPackagingMaterialPurchaseItemQueryCondition query, Pager pager);

}