package com.estone.warehouse.dao.mapper;

public interface WhChRecordDBField {
    String ID = "id";

    String CH_MOVE_STOCK_NO = "ch_move_stock_no";

    String SKU_BOX_TOTAL = "sku_box_total";

    String SKU_SPECIES = "sku_species";

    String BOX_NO = "box_no";

    String STATUS = "status";

    String CREATED_BY = "created_by";

    String CREATION_DATE = "creation_date";

    String IS_SUBMIT = "is_submit";

    String PICK_BY = "pick_by";

    String PICK_DATE = "pick_date";

    String RECEIVE_BY = "receive_by";

    String RECEIVE_DATE = "receive_date";

    String UP_BY = "up_by";

    String UP_DATE = "up_date";
}