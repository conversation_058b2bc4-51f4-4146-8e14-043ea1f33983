package com.estone.warehouse.dao.mapper;

import com.estone.warehouse.bean.StockMonthSnapshot;
import java.sql.ResultSet;
import java.sql.SQLException;
import org.springframework.jdbc.core.RowMapper;

public class StockMonthSnapshotMapper implements Row<PERSON>apper<StockMonthSnapshot> {

    public StockMonthSnapshot mapRow(ResultSet rs, int rowNum) throws SQLException {
        StockMonthSnapshot entity = new StockMonthSnapshot();
        entity.setId(rs.getObject(StockMonthSnapshotDBField.ID) == null ? null : rs.getInt(StockMonthSnapshotDBField.ID));
        entity.setSku(rs.getString(StockMonthSnapshotDBField.SKU));
        entity.setQuantity(rs.getObject(StockMonthSnapshotDBField.QUANTITY) == null ? null : rs.getInt(StockMonthSnapshotDBField.QUANTITY));
        entity.setTransferQty(rs.getObject(StockMonthSnapshotDBField.TRANSFER_QTY) == null ? null : rs.getInt(StockMonthSnapshotDBField.TRANSFER_QTY));
        entity.setNnQty(rs.getObject(StockMonthSnapshotDBField.NN_QTY) == null ? null : rs.getInt(StockMonthSnapshotDBField.NN_QTY));
        entity.setCountMonth(rs.getString(StockMonthSnapshotDBField.COUNT_MONTH));
        return entity;
    }
}