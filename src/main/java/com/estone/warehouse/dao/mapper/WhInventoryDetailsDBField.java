package com.estone.warehouse.dao.mapper;

public interface WhInventoryDetailsDBField {
    String ID = "id";

    String ARTICLE_NUMBER = "article_number";

    String NAME = "name";

    String ORDER_NO = "order_no";

    String STOCK_TYPE = "stock_type";

    String ORDER_TYPE = "order_type";

    String INITIAL_STOCK = "initial_stock";

    String CHANGE_STOCK = "change_stock";

    String CLOSING_STOCK = "closing_stock";

    String CREATION_DATE = "creation_date";

    String CREATION_BY = "creation_by";

    String LAST_UPDATE_DATE = "last_update_date";

    String LAST_UPDATED_BY = "last_updated_by";
}