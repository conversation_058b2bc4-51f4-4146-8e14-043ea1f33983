package com.estone.warehouse.dao.mapper;

import com.estone.warehouse.bean.WhInventoryDetails;
import java.sql.ResultSet;
import java.sql.SQLException;
import org.springframework.jdbc.core.RowMapper;

public class WhInventoryDetailsMapper implements RowMapper<WhInventoryDetails> {

    public WhInventoryDetails mapRow(ResultSet rs, int rowNum) throws SQLException {
        WhInventoryDetails entity = new WhInventoryDetails();
        entity.setId(rs.getObject(WhInventoryDetailsDBField.ID) == null ? null : rs.getInt(WhInventoryDetailsDBField.ID));
        entity.setArticleNumber(rs.getString(WhInventoryDetailsDBField.ARTICLE_NUMBER));
        entity.setName(rs.getString(WhInventoryDetailsDBField.NAME));
        entity.setOrderNo(rs.getString(WhInventoryDetailsDBField.ORDER_NO));
        entity.setStockType(rs.getObject(WhInventoryDetailsDBField.STOCK_TYPE) == null ? null : rs.getInt(WhInventoryDetailsDBField.STOCK_TYPE));
        entity.setOrderType(rs.getObject(WhInventoryDetailsDBField.ORDER_TYPE) == null ? null : rs.getInt(WhInventoryDetailsDBField.ORDER_TYPE));
        entity.setInitialStock(rs.getObject(WhInventoryDetailsDBField.INITIAL_STOCK) == null ? null : rs.getInt(WhInventoryDetailsDBField.INITIAL_STOCK));
        entity.setChangeStock(rs.getObject(WhInventoryDetailsDBField.CHANGE_STOCK) == null ? null : rs.getInt(WhInventoryDetailsDBField.CHANGE_STOCK));
        entity.setClosingStock(rs.getObject(WhInventoryDetailsDBField.CLOSING_STOCK) == null ? null : rs.getInt(WhInventoryDetailsDBField.CLOSING_STOCK));
        entity.setCreationDate(rs.getTimestamp(WhInventoryDetailsDBField.CREATION_DATE));
        entity.setCreationBy(rs.getObject(WhInventoryDetailsDBField.CREATION_BY) == null ? null : rs.getInt(WhInventoryDetailsDBField.CREATION_BY));
        entity.setLastUpdateDate(rs.getTimestamp(WhInventoryDetailsDBField.LAST_UPDATE_DATE));
        entity.setLastUpdatedBy(rs.getObject(WhInventoryDetailsDBField.LAST_UPDATED_BY) == null ? null : rs.getInt(WhInventoryDetailsDBField.LAST_UPDATED_BY));
        return entity;
    }
}