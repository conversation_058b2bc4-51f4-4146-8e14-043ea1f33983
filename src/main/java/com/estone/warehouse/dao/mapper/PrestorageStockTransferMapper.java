package com.estone.warehouse.dao.mapper;

import com.estone.warehouse.bean.PrestorageStockTransfer;
import com.estone.warehouse.dao.PrestorageStockTransferDao;
import org.springframework.jdbc.core.RowMapper;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.Objects;


/**
 * <AUTHOR>
 * @date 2023-03-09 16:45
 */
public class PrestorageStockTransferMapper implements RowMapper<PrestorageStockTransfer> {

    @Override
    public PrestorageStockTransfer mapRow(ResultSet rs, int rowNum) throws SQLException {
        PrestorageStockTransfer entity = new PrestorageStockTransfer();

        entity.setId(Objects.nonNull(rs.getObject(PrestorageStockTransferDBField.ID)) ? rs.getInt(PrestorageStockTransferDBField.ID) : null);
        entity.setOrderNo(rs.getString(PrestorageStockTransferDBField.ORDER_NO));
        entity.setSource(Objects.nonNull(rs.getObject(PrestorageStockTransferDBField.SOURCE))
                ? rs.getInt(PrestorageStockTransferDBField.SOURCE) : null);
        entity.setWarehouseType(Objects.nonNull(rs.getObject(PrestorageStockTransferDBField.WAREHOUSE_TYPE))
                ? rs.getInt(PrestorageStockTransferDBField.WAREHOUSE_TYPE) : null);
        entity.setType(Objects.nonNull(rs.getObject(PrestorageStockTransferDBField.TYPE)) ? rs.getInt(PrestorageStockTransferDBField.TYPE) : null);

        entity.setSkuTypeAmount(Objects.nonNull(rs.getObject(PrestorageStockTransferDBField.SKU_TYPE_AMOUNT))
                ? rs.getInt(PrestorageStockTransferDBField.SKU_TYPE_AMOUNT) : null);
        entity.setEmigrationLocationTypeAmount(Objects.nonNull(rs.getObject(PrestorageStockTransferDBField.EMIGRATION_LOCATION_TYPE_AMOUNT))
                ? rs.getInt(PrestorageStockTransferDBField.EMIGRATION_LOCATION_TYPE_AMOUNT) : null);
        entity.setImmigrationLocationTypeAmount(Objects.nonNull(rs.getObject(PrestorageStockTransferDBField.IMMIGRATION_LOCATION_TYPE_AMOUNT))
                ? rs.getInt(PrestorageStockTransferDBField.IMMIGRATION_LOCATION_TYPE_AMOUNT) : null);
        entity.setMigrationSkuAmount(Objects.nonNull(rs.getObject(PrestorageStockTransferDBField.MIGRATION_SKU_AMOUNT))
                ? rs.getInt(PrestorageStockTransferDBField.MIGRATION_SKU_AMOUNT) : null);
        entity.setPickingSkuAmount(Objects.nonNull(rs.getObject(PrestorageStockTransferDBField.PICKING_SKU_AMOUNT))
                ? rs.getInt(PrestorageStockTransferDBField.PICKING_SKU_AMOUNT) : null);

        entity.setStatus(Objects.nonNull(rs.getObject(PrestorageStockTransferDBField.STATUS))
                ? rs.getInt(PrestorageStockTransferDBField.STATUS) : null);
        entity.setPrintStatus(Objects.nonNull(rs.getObject(PrestorageStockTransferDBField.PRINT_STATUS))
                ? rs.getInt(PrestorageStockTransferDBField.PRINT_STATUS) : null);
        entity.setCreatePerson(rs.getString(PrestorageStockTransferDBField.CREATE_PERSON));
        entity.setCreateDate(rs.getTimestamp(PrestorageStockTransferDBField.CREATE_DATE));
        entity.setAcceptPerson(rs.getString(PrestorageStockTransferDBField.ACCEPT_PERSON));
        entity.setAcceptDate(rs.getTimestamp(PrestorageStockTransferDBField.ACCEPT_DATE));
        entity.setCompletedDate(rs.getTimestamp(PrestorageStockTransferDBField.COMPLETED_DATE));

        return entity;
    }
}
