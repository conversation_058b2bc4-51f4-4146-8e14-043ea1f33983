package com.estone.warehouse.dao.mapper;

import com.estone.warehouse.bean.WhOverseasReturnReport;
import java.sql.ResultSet;
import java.sql.SQLException;
import org.springframework.jdbc.core.RowMapper;

public class WhOverseasReturnReportMapper implements <PERSON><PERSON>apper<WhOverseasReturnReport> {

    public WhOverseasReturnReport mapRow(ResultSet rs, int rowNum) throws SQLException {
        WhOverseasReturnReport entity = new WhOverseasReturnReport();
        entity.setId(rs.getObject(WhOverseasReturnReportDBField.ID) == null ? null : rs.getInt(WhOverseasReturnReportDBField.ID));
        entity.setSaleAttribute(rs.getString(WhOverseasReturnReportDBField.SALE_ATTRIBUTE));
        entity.setSkuQuantity(rs.getObject(WhOverseasReturnReportDBField.SKU_QUANTITY) == null ? null : rs.getInt(WhOverseasReturnReportDBField.SKU_QUANTITY));
        entity.setPcsQuantity(rs.getObject(WhOverseasReturnReportDBField.PCS_QUANTITY) == null ? null : rs.getInt(WhOverseasReturnReportDBField.PCS_QUANTITY));
        entity.setStatisticsDate(rs.getTimestamp(WhOverseasReturnReportDBField.STATISTICS_DATE));
        return entity;
    }
}