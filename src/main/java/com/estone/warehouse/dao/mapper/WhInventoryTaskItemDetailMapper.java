package com.estone.warehouse.dao.mapper;

import com.estone.warehouse.bean.WhInventoryTaskItem;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.Objects;

import org.springframework.jdbc.core.RowMapper;

public class WhInventoryTaskItemDetailMapper implements RowMapper<WhInventoryTaskItem> {

    /**
     * This method corresponds to the database table wh_inventory_task_item
     *
     * @mbggenerated Wed Jul 03 16:16:10 CST 2019
     */
    public WhInventoryTaskItem mapRow(ResultSet rs, int rowNum) throws SQLException {
        WhInventoryTaskItem entity = new WhInventoryTaskItem();
        entity.setId(rs.getObject("i." + WhInventoryTaskItemDBField.ID) == null ? null
                : rs.getInt("i." + WhInventoryTaskItemDBField.ID));
        entity.setTaskId(rs.getObject("i." + WhInventoryTaskItemDBField.TASK_ID) == null ? null
                : rs.getInt("i." + WhInventoryTaskItemDBField.TASK_ID));
        entity.setDemandId(rs.getObject("i." + WhInventoryTaskItemDBField.DEMAND_ID) == null ? null
                : rs.getInt("i." + WhInventoryTaskItemDBField.DEMAND_ID));
        entity.setTaskLevel(rs.getObject("i." + WhInventoryTaskItemDBField.TASK_LEVEL) == null ? null
                : rs.getInt("i." + WhInventoryTaskItemDBField.TASK_LEVEL));
        entity.setSku(rs.getString("i." + WhInventoryTaskItemDBField.SKU));
        entity.setStockId(rs.getObject("i." + WhInventoryTaskItemDBField.STOCK_ID) == null ? null
                : rs.getInt("i." + WhInventoryTaskItemDBField.STOCK_ID));
        entity.setStatus(rs.getObject("i." + WhInventoryTaskItemDBField.STATUS) == null ? null
                : rs.getInt("i." + WhInventoryTaskItemDBField.STATUS));
        entity.setInventoryQuantity(rs.getObject("i." + WhInventoryTaskItemDBField.INVENTORY_QUANTITY) == null ? null
                : rs.getInt("i." + WhInventoryTaskItemDBField.INVENTORY_QUANTITY));
        entity.setDiffQuantity(rs.getObject("i." + WhInventoryTaskItemDBField.DIFF_QUANTITY) == null ? null
                : rs.getInt("i." + WhInventoryTaskItemDBField.DIFF_QUANTITY));
        entity.setConfirmQuantity(rs.getObject("i." + WhInventoryTaskItemDBField.CONFIRM_QUANTITY) == null ? null
                : rs.getInt("i." + WhInventoryTaskItemDBField.CONFIRM_QUANTITY));
        entity.setConfirmDiff(rs.getObject("i." + WhInventoryTaskItemDBField.CONFIRM_DIFF) == null ? null
                : rs.getInt("i." + WhInventoryTaskItemDBField.CONFIRM_DIFF));
        entity.setRemark(rs.getString("i." + WhInventoryTaskItemDBField.REMARK));
        entity.setCreationUser(rs.getObject("i." + WhInventoryTaskItemDBField.CREATION_USER) == null ? null
                : rs.getInt("i." + WhInventoryTaskItemDBField.CREATION_USER));
        entity.setCreationDate(rs.getTimestamp("i." + WhInventoryTaskItemDBField.CREATION_DATE));
        entity.setRepeatQuantity(rs.getObject("i." + WhInventoryTaskItemDBField.REPEAT_QUANTITY) == null ? null
                : rs.getInt("i." + WhInventoryTaskItemDBField.REPEAT_QUANTITY));
        entity.setRepeatDiffQuantity(rs.getObject("i." + WhInventoryTaskItemDBField.REPEAT_DIFF_QUANTITY) == null ? null
                : rs.getInt("i." + WhInventoryTaskItemDBField.REPEAT_DIFF_QUANTITY));
        entity.setFinallyQuantity(rs.getObject("i." + WhInventoryTaskItemDBField.FINALLY_QUANTITY) == null ? null
                : rs.getInt("i." + WhInventoryTaskItemDBField.FINALLY_QUANTITY));
        entity.setFinallyDiffQuantity(rs.getObject("i." + WhInventoryTaskItemDBField.FINALLY_DIFF_QUANTITY) == null
                ? null : rs.getInt("i." + WhInventoryTaskItemDBField.FINALLY_DIFF_QUANTITY));
        entity.setAnomalousCause(rs.getString("i." + WhInventoryTaskItemDBField.ANOMALOUS_CAUSE));
        entity.setLocalNum(rs.getObject("i." + WhInventoryTaskItemDBField.LOCAL_NUM) == null ? null
                : rs.getInt("i." + WhInventoryTaskItemDBField.LOCAL_NUM));
        entity.setReviewUser(rs.getObject("i." + WhInventoryTaskItemDBField.REVIEW_USER) == null ? null
                : rs.getInt("i." + WhInventoryTaskItemDBField.REVIEW_USER));
        entity.setReviewDate(rs.getTimestamp("i." + WhInventoryTaskItemDBField.REVIEW_DATE));
        entity.setSkuInventoryDate(rs.getTimestamp("i." + WhInventoryTaskItemDBField.SKU_INVENTORY_DATE));

        // 父表内容
        entity.setTaskNo(rs.getString("t.task_no"));
        entity.setTaskStatus(rs.getObject("t.status") == null ? null : rs.getInt("t.status"));
        entity.setReceiveUser(rs.getObject("t.receive_user") == null ? null : rs.getInt("t.receive_user"));
        entity.setReceiveDate(rs.getTimestamp("t.receive_date"));
        entity.setInventoryUser(rs.getObject("t.inventory_user") == null ? null : rs.getInt("t.inventory_user"));
        entity.setInventoryDate(rs.getTimestamp("t.inventory_date"));
        entity.setTaskType(rs.getInt("t.task_type"));
        entity.setTaskReviewDate(rs.getTimestamp("t.review_date"));
        entity.setLocation(rs.getString("location_number"));
        entity.setStockAmount(Objects.nonNull(rs.getObject("stock_amount")) ? rs.getInt("stock_amount") : null);
        return entity;
    }
}