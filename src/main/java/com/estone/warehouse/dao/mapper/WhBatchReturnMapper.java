package com.estone.warehouse.dao.mapper;

import com.estone.warehouse.bean.WhBatchReturn;
import java.sql.ResultSet;
import java.sql.SQLException;
import org.springframework.jdbc.core.RowMapper;

public class WhBatchReturnMapper implements <PERSON><PERSON>apper<WhBatchReturn> {

    public WhBatchReturn mapRow(ResultSet rs, int rowNum) throws SQLException {
        WhBatchReturn entity = new WhBatchReturn();
        entity.setId(rs.getObject(WhBatchReturnDBField.ID) == null ? null : rs.getInt(WhBatchReturnDBField.ID));
        entity.setOrderNo(rs.getString(WhBatchReturnDBField.ORDER_NO));
        entity.setType(rs.getString(WhBatchReturnDBField.TYPE));
        entity.setWarehouseId(rs.getObject( WhBatchReturnDBField.WAREHOUSE_ID) == null ? null : rs.getInt( WhBatchReturnDBField.WAREHOUSE_ID));
        entity.setStatus(rs.getObject(WhBatchReturnDBField.STATUS) == null ? null : rs.getInt(WhBatchReturnDBField.STATUS));
        entity.setGridBy(rs.getObject(WhBatchReturnDBField.GRID_BY) == null ? null : rs.getInt(WhBatchReturnDBField.GRID_BY));
        entity.setReturnBy(rs.getObject(WhBatchReturnDBField.RETURN_BY) == null ? null : rs.getInt(WhBatchReturnDBField.RETURN_BY));
        entity.setUpBy(rs.getObject(WhBatchReturnDBField.UP_BY) == null ? null : rs.getInt(WhBatchReturnDBField.UP_BY));
        entity.setCreatedBy(rs.getObject(WhBatchReturnDBField.CREATED_BY) == null ? null : rs.getInt(WhBatchReturnDBField.CREATED_BY));
        entity.setCreationDate(rs.getTimestamp(WhBatchReturnDBField.CREATION_DATE));
        entity.setLastUpdatedBy(rs.getObject(WhBatchReturnDBField.LAST_UPDATED_BY) == null ? null : rs.getInt(WhBatchReturnDBField.LAST_UPDATED_BY));
        entity.setLastUpdateDate(rs.getTimestamp(WhBatchReturnDBField.LAST_UPDATE_DATE));
        return entity;
    }
}