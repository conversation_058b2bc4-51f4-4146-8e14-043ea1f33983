package com.estone.warehouse.dao.mapper;

import com.estone.warehouse.bean.PrestorageStockTransferOrderItem;
import org.springframework.jdbc.core.RowMapper;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2023-03-16 17:36
 */
public class PrestorageStockTransferOrderItemMapper implements RowMapper<PrestorageStockTransferOrderItem> {
    @Override
    public PrestorageStockTransferOrderItem mapRow(ResultSet rs, int rowNum) throws SQLException {
        PrestorageStockTransferOrderItem entity = new PrestorageStockTransferOrderItem();

        entity.setOrderId(Objects.nonNull(rs.getObject(PrestorageStockTransferDetailDBField.ORDER_ID))
                ? rs.getInt(PrestorageStockTransferDetailDBField.ORDER_ID) : null);
        entity.setId(Objects.nonNull(rs.getObject(PrestorageStockTransferDetailDBField.ID))
                ? rs.getInt(PrestorageStockTransferDetailDBField.ID) : null);
        entity.setSku(rs.getString(PrestorageStockTransferDetailDBField.SKU));
        entity.setSkuAmount(Objects.nonNull(rs.getObject(PrestorageStockTransferDetailDBField.SKU_AMOUNT))
                ? rs.getInt(PrestorageStockTransferDetailDBField.SKU_AMOUNT) : null);
        entity.setImmigrationLocationNumber(rs.getString(PrestorageStockTransferDetailDBField.IMMIGRATION_LOCATION_NUMBER));
        entity.setEmigrationLocationNumber(rs.getString(PrestorageStockTransferDetailDBField.EMIGRATION_LOCATION_NUMBER));
        entity.setPickingSkuAmount(Objects.nonNull(rs.getObject(PrestorageStockTransferDetailDBField.PICKING_SKU_AMOUNT))
                ? rs.getInt(PrestorageStockTransferDetailDBField.PICKING_SKU_AMOUNT) : null);
        entity.setUploadSkuAmount(Objects.nonNull(rs.getObject(PrestorageStockTransferDetailDBField.UPLOAD_SKU_AMOUNT))
                ? rs.getInt(PrestorageStockTransferDetailDBField.UPLOAD_SKU_AMOUNT) : null);

        return entity;
    }
}
