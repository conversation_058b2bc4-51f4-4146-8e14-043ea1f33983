package com.estone.warehouse.dao.mapper;

import com.estone.warehouse.bean.WhAllocateRecord;
import java.sql.ResultSet;
import java.sql.SQLException;
import org.springframework.jdbc.core.RowMapper;

public class WhAllocateRecordMapper implements <PERSON><PERSON>apper<WhAllocateRecord> {

    public WhAllocateRecord mapRow(ResultSet rs, int rowNum) throws SQLException {
        WhAllocateRecord entity = new WhAllocateRecord();
        entity.setId(rs.getObject(WhAllocateRecordDBField.ID) == null ? null : rs.getInt(WhAllocateRecordDBField.ID));
        entity.setAllocateNumber(rs.getString(WhAllocateRecordDBField.ALLOCATE_NUMBER));
        entity.setOutWhType(rs.getObject(WhAllocateRecordDBField.OUT_WH_TYPE) == null ? null : rs.getInt(WhAllocateRecordDBField.OUT_WH_TYPE));
        entity.setInWhType(rs.getObject(WhAllocateRecordDBField.IN_WH_TYPE) == null ? null : rs.getInt(WhAllocateRecordDBField.IN_WH_TYPE));
        entity.setOutOwnerUserId(rs.getString(WhAllocateRecordDBField.OUT_OWNER_USER_ID));
        entity.setInOwnerUserId(rs.getString(WhAllocateRecordDBField.IN_OWNER_USER_ID));
        entity.setSkuNum(rs.getObject(WhAllocateRecordDBField.SKU_NUM) == null ? null : rs.getInt(WhAllocateRecordDBField.SKU_NUM));
        entity.setAllotNum(rs.getObject(WhAllocateRecordDBField.ALLOT_NUM) == null ? null : rs.getInt(WhAllocateRecordDBField.ALLOT_NUM));
        entity.setCreateBy(rs.getObject(WhAllocateRecordDBField.CREATE_BY) == null ? null : rs.getInt(WhAllocateRecordDBField.CREATE_BY));
        entity.setCreationDate(rs.getTimestamp(WhAllocateRecordDBField.CREATION_DATE));
        entity.setLastUpdatedBy(rs.getObject(WhAllocateRecordDBField.LAST_UPDATED_BY) == null ? null : rs.getInt(WhAllocateRecordDBField.LAST_UPDATED_BY));
        entity.setLastUpdateDate(rs.getTimestamp(WhAllocateRecordDBField.LAST_UPDATE_DATE));
        return entity;
    }
}