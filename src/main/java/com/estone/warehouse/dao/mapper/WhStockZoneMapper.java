package com.estone.warehouse.dao.mapper;

import java.sql.ResultSet;
import java.sql.SQLException;

import org.springframework.jdbc.core.RowMapper;

import com.estone.warehouse.bean.WhStockZone;

public class WhStockZoneMapper implements Row<PERSON>apper<WhStockZone> {

    /**
     * This method corresponds to the database table wh_stock_zone
     *
     * @mbggenerated Fri Aug 24 14:15:44 CST 2018
     */
    public WhStockZone mapRow(ResultSet rs, int rowNum) throws SQLException {
        WhStockZone entity = new WhStockZone();
        entity.setId(rs.getInt(WhStockZoneDBField.ID));
        entity.setWarehouseId(rs.getObject(WhStockZoneDBField.WAREHOUSE_ID) == null ? null
                : rs.getInt(WhStockZoneDBField.WAREHOUSE_ID));
        entity.setCode(rs.getString(WhStockZoneDBField.CODE));
        entity.setName(rs.getString(WhStockZoneDBField.NAME));
        entity.setStockLocationPrefixes(rs.getString(WhStockZoneDBField.STOCK_LOCATION_PREFIXES));
        return entity;
    }
}