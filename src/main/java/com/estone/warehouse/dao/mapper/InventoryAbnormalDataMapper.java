package com.estone.warehouse.dao.mapper;

import com.estone.warehouse.bean.InventoryAbnormalData;
import java.sql.ResultSet;
import java.sql.SQLException;
import org.springframework.jdbc.core.RowMapper;

public class InventoryAbnormalDataMapper implements RowMapper<InventoryAbnormalData> {

    public InventoryAbnormalData mapRow(ResultSet rs, int rowNum) throws SQLException {
        InventoryAbnormalData entity = new InventoryAbnormalData();
        entity.setId(rs.getObject(InventoryAbnormalDataDBField.ID) == null ? null : rs.getInt(InventoryAbnormalDataDBField.ID));
        entity.setTaskNumber(rs.getString(InventoryAbnormalDataDBField.TASK_NUMBER));
        entity.setAbnormalTurnoverBasket(rs.getString(InventoryAbnormalDataDBField.ABNORMAL_TURNOVER_BASKET));
        entity.setChecker(rs.getObject(InventoryAbnormalDataDBField.CHECKER) == null ? null : rs.getInt(InventoryAbnormalDataDBField.CHECKER));
        entity.setConfirmDate(rs.getTimestamp(InventoryAbnormalDataDBField.CONFIRM_DATE));
        entity.setSku(rs.getString(InventoryAbnormalDataDBField.SKU));
        entity.setShelfPosition(rs.getString(InventoryAbnormalDataDBField.SHELF_POSITION));
        entity.setNormalShelfPosition(rs.getString(InventoryAbnormalDataDBField.NORMAL_SHELF_POSITION));
        entity.setOperationType(rs.getObject(InventoryAbnormalDataDBField.OPERATION_TYPE) == null ? null : rs.getInt(InventoryAbnormalDataDBField.OPERATION_TYPE));
        entity.setStatus(rs.getObject(InventoryAbnormalDataDBField.STATUS) == null ? null : rs.getInt(InventoryAbnormalDataDBField.STATUS));
        entity.setPhysicalTransferPosition(rs.getString(InventoryAbnormalDataDBField.PHYSICAL_TRANSFER_POSITION));
        entity.setUpQuantity(rs.getObject(InventoryAbnormalDataDBField.UP_QUANTITY) == null ? null : rs.getInt(InventoryAbnormalDataDBField.UP_QUANTITY));
        entity.setNextStepTaskId(rs.getObject(InventoryAbnormalDataDBField.NEXT_STEP_TASK_ID) == null ? null : rs.getInt(InventoryAbnormalDataDBField.NEXT_STEP_TASK_ID));
        entity.setPrefixTaskNumber(rs.getString(InventoryAbnormalDataDBField.PREFIX_TASK_NUMBER));
        entity.setStockId(rs.getObject(InventoryAbnormalDataDBField.STOCK_ID) == null ? null : rs.getInt(InventoryAbnormalDataDBField.STOCK_ID));
        return entity;
    }
}