package com.estone.warehouse.dao.mapper;

import com.estone.warehouse.bean.WhBackupSecurityApv;
import java.sql.ResultSet;
import java.sql.SQLException;
import org.springframework.jdbc.core.RowMapper;

public class WhBackupSecurityApvMapper implements <PERSON><PERSON>apper<WhBackupSecurityApv> {

    /**
     * This method corresponds to the database table wh_backup_security_apv
     *
     * @mbggenerated Thu Jun 13 15:12:44 CST 2019
     */
    public WhBackupSecurityApv mapRow(ResultSet rs, int rowNum) throws SQLException {
        WhBackupSecurityApv entity = new WhBackupSecurityApv();
        entity.setId(rs.getObject(WhBackupSecurityApvDBField.ID) == null ? null : rs.getInt(WhBackupSecurityApvDBField.ID));
        entity.setApvNo(rs.getString(WhBackupSecurityApvDBField.APV_NO));
        entity.setPlatformOrderId(rs.getString(WhBackupSecurityApvDBField.PLATFORM_ORDER_ID));
        entity.setSalesRecordNumber(rs.getString(WhBackupSecurityApvDBField.SALES_RECORD_NUMBER));
        entity.setTrackingNumber(rs.getString(WhBackupSecurityApvDBField.TRACKING_NUMBER));
        entity.setServiceProviderNo(rs.getString(WhBackupSecurityApvDBField.SERVICE_PROVIDER_NO));
        entity.setShipService(rs.getString(WhBackupSecurityApvDBField.SHIP_SERVICE));
        entity.setCreationDate(rs.getTimestamp(WhBackupSecurityApvDBField.CREATION_DATE));
        entity.setApvDataJson(rs.getString(WhBackupSecurityApvDBField.APV_DATA_JSON));
        return entity;
    }
}