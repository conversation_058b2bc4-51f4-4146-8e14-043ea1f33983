package com.estone.warehouse.dao.mapper;

public interface WhSkuSaleQuantityInfoDBField {
    String ID = "id";

    String SKU = "sku";

    String ATTRIBUTE1 = "attribute1";

    String ATTRIBUTE2 = "attribute2";

    String ATTRIBUTE3 = "attribute3";

    String ATTRIBUTE4 = "attribute4";

    String ATTRIBUTE5 = "attribute5";

    String ATTRIBUTE6 = "attribute6";

    String ATTRIBUTE7 = "attribute7";

    String ATTRIBUTE8 = "attribute8";

    String ATTRIBUTE9 = "attribute9";

    String CREATION_DATE = "creation_date";

    String CREATE_BY = "create_by";
}