package com.estone.warehouse.dao.mapper;

import com.estone.warehouse.bean.WhStockFullDTO;
import com.estone.warehouse.bean.WhStockFullGroupDTO;
import org.springframework.jdbc.core.RowMapper;

import java.sql.ResultSet;
import java.sql.SQLException;

public class WhStockFullMapper implements RowMapper<WhStockFullDTO> {

    private boolean isGroup = false;

    public WhStockFullMapper(){}

    public WhStockFullMapper(boolean isGroup){
        this.isGroup = isGroup;
    }

    public WhStockFullDTO mapRow(ResultSet rs, int rowNum) throws SQLException {
        WhStockFullDTO entity = new WhStockFullDTO();
        if(isGroup){
            entity.setOnWayQuantityGroup(rs.getObject("onWayQuantity_group") == null ? 0 : rs.getInt("onWayQuantity_group"));
            entity.setQuantityGroup(rs.getObject("quantity_group") == null ? 0 : rs.getInt("quantity_group"));
            entity.setQcQuantityGroup(rs.getObject("qc_quantity_group") == null ? 0 : rs.getInt("qc_quantity_group"));
            entity.setWaitingUpQuantityGroup(rs.getObject("waiting_up_quantity_group") == null ? 0 : rs.getInt("waiting_up_quantity_group"));
            entity.setUpQuantityGroup(rs.getObject("up_quantity_group") == null ? 0 : rs.getInt("up_quantity_group"));
            entity.setSurplusQuantityGroup(rs.getObject("surplus_quantity_group") == null ? 0 : rs.getInt("surplus_quantity_group"));
            entity.setAllotQuantityGroup(rs.getObject("allot_quantity_group") == null ? 0 : rs.getInt("allot_quantity_group"));
            entity.setPickQuantityGroup(rs.getObject("pick_quantity_group") == null ? 0 : rs.getInt("pick_quantity_group"));
            entity.setPickNotQuantityGroup(rs.getObject("pick_not_quantity_group") == null ? 0 : rs.getInt("pick_not_quantity_group"));
            entity.setPickReturnQuantityGroup(rs.getObject("pick_return_quantity_group") == null ? 0 : rs.getInt("pick_return_quantity_group"));
            entity.setCancelQuantityGroup(rs.getObject("cancel_quantity_group") == null ? 0 : rs.getInt("cancel_quantity_group"));
            entity.setOrderAllocationQuantityGroup(rs.getObject("order_allocation_quantity_group") == null ? 0 : rs.getInt("order_allocation_quantity_group"));
            entity.setAllocationQuantityGroup(rs.getObject("allocation_quantity_group") == null ? 0 : rs.getInt("allocation_quantity_group"));
            entity.setFrozenQuantityGroup(rs.getObject("frozen_quantity_group") == null ? 0 :rs.getInt("frozen_quantity_group"));
            entity.setBatchReturnQuantityGroup(rs.getInt("batch_return_quantity_group"));
            entity.setLendOnwayQuantityGroup(rs.getInt("lend_onway_quantity_group"));
            entity.setLocationQuantityGroup(entity.getSurplusQuantityGroup() + entity.getAllotQuantityGroup());
            entity.setQuantityGroup(entity.getSurplusQuantityGroup() + entity.getAllotQuantityGroup()
                    + entity.getFrozenQuantityGroup()
                    + entity.getPickQuantityGroup()
                    + entity.getPickNotQuantityGroup() + entity.getCancelQuantityGroup()
                    + entity.getPickReturnQuantityGroup() + entity.getAllocationQuantityGroup()
                    + entity.getOrderAllocationQuantityGroup());
        }else {
            entity.setId(rs.getInt("id"));
            entity.setSku(rs.getString("sku"));
            entity.setName(rs.getString("name"));
            entity.setStatus(rs.getInt("status"));
            entity.setWarehouseId(rs.getString("warehouse_id"));
            entity.setLocationNumber(rs.getString("location_number"));
            entity.setSaleAttributeSettingStr(rs.getString("sale_attribute_setting_str"));
            entity.setThirtyDaysSalesOrders(rs.getObject("thirtyDaysSalesOrders") == null ? 0 : rs.getInt("thirtyDaysSalesOrders"));
            entity.setThirtyDaysSalesDays(rs.getObject("thirtyDaysSalesDays") == null ? 0 : rs.getInt("thirtyDaysSalesDays"));
            entity.setOnWayQuantity(rs.getObject("onWayQuantity") == null ? 0 : rs.getInt("onWayQuantity"));
            entity.setQuantity(rs.getObject("quantity") == null ? 0 : rs.getInt("quantity"));
            entity.setQcQuantity(rs.getObject("qc_quantity") == null ? 0 : rs.getInt("qc_quantity"));
            entity.setWaitingUpQuantity(rs.getObject("waiting_up_quantity") == null ? 0 : rs.getInt("waiting_up_quantity"));
            entity.setUpQuantity(rs.getObject("up_quantity") == null ? 0 : rs.getInt("up_quantity"));
            entity.setSurplusQuantity(rs.getObject("surplus_quantity") == null ? 0 : rs.getInt("surplus_quantity"));
            entity.setAllotQuantity(rs.getObject("allot_quantity") == null ? 0 : rs.getInt("allot_quantity"));
            entity.setPickQuantity(rs.getObject("pick_quantity") == null ? 0 : rs.getInt("pick_quantity"));
            entity.setPickNotQuantity(rs.getObject("pick_not_quantity") == null ? 0 : rs.getInt("pick_not_quantity"));
            entity.setPickReturnQuantity(rs.getObject("pick_return_quantity") == null ? 0 : rs.getInt("pick_return_quantity"));
            entity.setCancelQuantity(rs.getObject("cancel_quantity") == null ? 0 : rs.getInt("cancel_quantity"));
            entity.setOrderAllocationQuantity(rs.getObject("order_allocation_quantity") == null ? 0 : rs.getInt("order_allocation_quantity"));
            entity.setAllocationQuantity(rs.getObject("allocation_quantity") == null ? 0 : rs.getInt("allocation_quantity"));
            entity.setFrozenQuantity(rs.getObject("frozen_quantity") == null ? 0 : rs.getInt("frozen_quantity"));
            entity.setBatchReturnQuantity(rs.getInt("batch_return_quantity"));
            entity.setLendOnwayQuantity(rs.getInt("lend_onway_quantity"));
            entity.setLocationQuantity(entity.getSurplusQuantity() + entity.getAllotQuantity());
            entity.setQuantity(entity.getSurplusQuantity() + entity.getAllotQuantity()
                    + entity.getFrozenQuantity()
                    + entity.getPickQuantity()
                    + entity.getPickNotQuantity() + entity.getCancelQuantity()
                    + entity.getPickReturnQuantity() + entity.getAllocationQuantity()
                    + entity.getOrderAllocationQuantity());
        }
        return entity;
    }

}