package com.estone.warehouse.dao.mapper;

public interface WhStockMoveDBField {
    String MOVE_ID = "move_id";

    String QUANTITY = "quantity";

    String OUT_LOCATION = "out_location";

    String IN_LOCATION = "in_location";

    String REMARK = "remark";

    String CREATE_BY = "create_by";

    String CREATE_DATE = "create_date";

    String LAST_UPDATE_DATE = "last_update_date";

    String STOCK_TYPE = "stock_type";
}