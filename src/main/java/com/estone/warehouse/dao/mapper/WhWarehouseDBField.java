package com.estone.warehouse.dao.mapper;

public interface WhWarehouseDBField {
    String ID = "id";

    String COMPANY = "company";

    String NAME = "name";

    String CODE = "code";

    String LOCATION = "location";

    String LEVEL = "level";

    String ADDRESS = "address";

    String CONTACTS = "contacts";

    String PHONE = "phone";

    String FAX = "fax";

    String IS_DEFAULT = "is_default";

    String SEVEN_RATE = "seven_rate";

    String FIFTEEN_RATE = "fifteen_rate";

    String THIRTY_RATE = "thirty_rate";

    String PLAN_SEVEN_RATE = "plan_seven_rate";

    String PLAN_FIFTEEN_RATE = "plan_fifteen_rate";

    String PLAN_THIRTY_RATE = "plan_thirty_rate";

    String SAFETY_DAY = "safety_day";

    String CREATE_DATE = "create_date";

    String CREATE_USER = "create_user";

    String UPDATE_DATE = "update_date";

    String UPDATE_USER = "update_user";

    String COMMENT = "comment";

    String INVENTORY_CYCLE = "inventory_cycle";

    String BLANK_CYCLE = "blank_cycle";

    String IS_OVERSEA = "is_oversea";
}