package com.estone.warehouse.dao.mapper;

public interface FrozenStockDBField {
    String ID = "id";

    String SKU = "sku";

    String QUANTITY = "quantity";

    String FROZEN_QUANTITY = "frozen_quantity";

    String SCRAP_QUANTITY = "scrap_quantity";

    String LEND_ONWAY_QUANTITY = "lend_onway_quantity";

    String LEND_QUANTITY = "lend_quantity";

    String BAD_PRODUCT_QUANTITY = "bad_product_quantity";

    String REMARK = "remark";

    String CREATION_DATE = "creation_date";

    String CREATE_BY = "create_by";

    String LAST_UPDATE_DATE = "last_update_date";

    String LAST_UPDATED_BY = "last_updated_by";

    String LAST_UP_TIME = "last_up_time";

    String LAST_UP_USER = "last_up_user";

    String LAST_MOVE_TIME = "last_move_time";

    String LAST_MOVE_USER = "last_move_user";

    String STOCK_ID = "stock_id";

    String CHECK_IN_UP_TIME = "check_in_up_time";

    String CHECK_IN_UP_USER = "check_in_up_user";
    String ALLOCATION_IN_TIME = "allocation_in_time";
    String ALLOCATION_OUT_TIME = "allocation_out_time";
    String MIN_EXP_DATE = "min_exp_date";
}