package com.estone.warehouse.dao.mapper;

import java.sql.ResultSet;
import java.sql.SQLException;

import org.springframework.jdbc.core.RowMapper;

import com.estone.warehouse.bean.WhLocation;
import com.estone.warehouse.bean.WhLocationStorage;

public class WhLocationAndStorageMapper implements RowMapper<WhLocation> {

    private boolean pageQuery;

    public WhLocationAndStorageMapper() {

    }

    public WhLocationAndStorageMapper(boolean pageQuery) {
        this.pageQuery = pageQuery;
    }

    /**
     * This method corresponds to the database table wh_location
     *
     * @mbggenerated Mon Mar 04 15:14:07 CST 2019
     */
    public WhLocation mapRow(ResultSet rs, int rowNum) throws SQLException {
        WhLocation entity = new WhLocation();
        entity.setId(rs.getObject("wl.id") == null ? null : rs.getInt("wl.id"));
        entity.setWarehouseType(rs.getObject("wl.warehouse_type") == null ? null : rs.getInt("wl.warehouse_type"));
        entity.setLocationRegion(rs.getString("wl.location_region"));
        entity.setLocationAisle(rs.getString("wl.location_aisle"));
        entity.setLocation(rs.getString("wl.location"));
        entity.setLocationType(rs.getObject("wl.location_type") == null ? null : rs.getInt("wl.location_type"));
        entity.setLocationAttribute(
                rs.getObject("wl.location_attribute") == null ? null : rs.getInt("wl.location_attribute"));
        entity.setCategoryLimit(rs.getObject("wl.category_limit") == null ? null : rs.getInt("wl.category_limit"));
        entity.setStorageAttribute(
                rs.getObject("wl.storage_attribute") == null ? null : rs.getInt("wl.storage_attribute"));
        entity.setLocationStatus(rs.getObject("wl.location_status") == null ? null : rs.getInt("wl.location_status"));
        entity.setLocationLength(rs.getDouble("wl.location_length"));
        entity.setLocationWidth(rs.getDouble("wl.location_width"));
        entity.setLocationHeight(rs.getDouble("wl.location_height"));
        entity.setLocationVolume(rs.getDouble("wl.location_volume"));
        entity.setCreateBy(rs.getObject("wl.create_by") == null ? null : rs.getInt("wl.create_by"));
        entity.setCreatedDate(rs.getTimestamp("wl.created_date"));
        entity.setLastUpdateBy(rs.getObject("wl.last_update_by") == null ? null : rs.getInt("wl.last_update_by"));
        entity.setLastUpdateDate(rs.getTimestamp("wl.last_update_date"));
        entity.setCategoryActual(rs.getObject(WhLocationDBField.CATEGORY_ACTUAL) == null ? null
                : rs.getInt(WhLocationDBField.CATEGORY_ACTUAL));

        if (pageQuery) {
            entity.setCategoryInStock(rs.getInt("categoryInStock"));
        }

        WhLocationStorage whLocationStorage = new WhLocationStorage();
        whLocationStorage.setId(rs.getObject("wls.id") == null ? null : rs.getInt("wls.id"));
        whLocationStorage.setName(rs.getString("wls.name"));
        entity.setWhLocationStorage(whLocationStorage);
        return entity;
    }
}