package com.estone.warehouse.dao.mapper;

import com.estone.warehouse.bean.WhInventoryItem;
import java.sql.ResultSet;
import java.sql.SQLException;
import org.springframework.jdbc.core.RowMapper;

public class WhInventoryItemMapper implements RowMapper<WhInventoryItem> {

    /**
     * This method corresponds to the database table wh_inventory_item
     *
     * @mbggenerated Fri Aug 17 15:45:23 CST 2018
     */
    public WhInventoryItem mapRow(ResultSet rs, int rowNum) throws SQLException {
        WhInventoryItem entity = new WhInventoryItem();
        entity.setId(rs.getObject(WhInventoryItemDBField.ID) == null ? null : rs.getInt(WhInventoryItemDBField.ID));
        entity.setInventoryId(rs.getObject(WhInventoryItemDBField.INVENTORY_ID) == null ? null : rs.getInt(WhInventoryItemDBField.INVENTORY_ID));
        entity.setSku(rs.getString(WhInventoryItemDBField.SKU));
        entity.setSkuLocation(rs.getString(WhInventoryItemDBField.SKU_LOCATION));
        entity.setSkuName(rs.getString(WhInventoryItemDBField.SKU_NAME));
        entity.setSkuNum(rs.getObject(WhInventoryItemDBField.SKU_NUM) == null ? null : rs.getInt(WhInventoryItemDBField.SKU_NUM));
        entity.setInventoryNum(rs.getObject(WhInventoryItemDBField.INVENTORY_NUM) == null ? null : rs.getInt(WhInventoryItemDBField.INVENTORY_NUM));
        entity.setDifferencesNum(rs.getObject(WhInventoryItemDBField.DIFFERENCES_NUM) == null ? null : rs.getInt(WhInventoryItemDBField.DIFFERENCES_NUM));
        entity.setPrice(rs.getObject(WhInventoryItemDBField.PRICE) == null ? null : rs.getDouble(WhInventoryItemDBField.PRICE));
        entity.setStatus(rs.getObject(WhInventoryItemDBField.STATUS) == null ? null : rs.getInt(WhInventoryItemDBField.STATUS));
        entity.setRemark(rs.getString(WhInventoryItemDBField.REMARK));
        entity.setCreatedBy(rs.getObject(WhInventoryItemDBField.CREATED_BY) == null ? null : rs.getInt(WhInventoryItemDBField.CREATED_BY));
        entity.setCreatedDate(rs.getTimestamp(WhInventoryItemDBField.CREATED_DATE));
        entity.setLastUpdateDate(rs.getTimestamp(WhInventoryItemDBField.LAST_UPDATE_DATE));
        entity.setLastUpdatedBy(rs.getObject(WhInventoryItemDBField.LAST_UPDATED_BY) == null ? null : rs.getInt(WhInventoryItemDBField.LAST_UPDATED_BY));
        return entity;
    }
}