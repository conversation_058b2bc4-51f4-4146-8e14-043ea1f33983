package com.estone.warehouse.dao;

import com.estone.warehouse.bean.WhOrderCancelItem;
import com.estone.warehouse.bean.WhOrderCancelItemQueryCondition;
import com.whq.tool.component.Pager;

import java.util.List;
import java.util.Map;

public interface WhOrderCancelItemDao {
    int queryWhOrderCancelItemCount(WhOrderCancelItemQueryCondition query);

    List<WhOrderCancelItem> queryWhOrderCancelItemList();

    List<WhOrderCancelItem> queryWhOrderCancelItemList(WhOrderCancelItemQueryCondition query, Pager pager);

    WhOrderCancelItem queryWhOrderCancelItem(Integer primaryKey);

    WhOrderCancelItem queryWhOrderCancelItem(WhOrderCancelItemQueryCondition query);

    void createWhOrderCancelItem(WhOrderCancelItem entity);

    void batchCreateWhOrderCancelItem(List<WhOrderCancelItem> entityList);

    void batchUpdateWhOrderCancelItem(List<WhOrderCancelItem> entityList);

    void deleteWhOrderCancelItem(Integer primaryKey);

    void updateWhOrderCancelItem(WhOrderCancelItem entity);

    List<Map<String, Object>> queryWhOrderCancelList(WhOrderCancelItemQueryCondition query);
}