package com.estone.warehouse.dao;

import com.estone.warehouse.bean.PrestorageStockMigrationRule;
import com.estone.warehouse.bean.PrestorageStockMigrationRuleQueryCondition;
import com.estone.warehouse.bean.PrestorageStockMigrationRuleVO;
import com.whq.tool.component.Pager;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-03-21 09:36
 */
public interface PrestorageStockMigrationRuleDao {

    /**
     * 用于查找出所有规则
     *
     * @return 所有规则的数据集合
     */
    List<PrestorageStockMigrationRuleVO> queryAllRule();

    /**
     * 用于查询符合条件的规则对象
     *
     * @param queryCondition 查询条件对象
     * @param pager          分页对象
     * @return 符合条件的规则对象
     */
    List<PrestorageStockMigrationRuleVO> query(PrestorageStockMigrationRuleQueryCondition queryCondition, Pager pager);

    /**
     * 通过id查找对应的规则明细
     *
     * @param id 规则Id
     * @return 规则明细
     */
    PrestorageStockMigrationRuleVO queryById(Integer id);

    /**
     * 用于插入一条规则
     *
     * @param prestorageStockMigrationRule 要进行插入的规则对象
     * @return 插入后规则的ID
     */
    Integer insert(PrestorageStockMigrationRule prestorageStockMigrationRule);


    /**
     * 用于根据Id更新一条规则
     *
     * @param prestorageStockMigrationRule 更新的规则内容
     */
    void updateById(PrestorageStockMigrationRule prestorageStockMigrationRule);

    /**
     * 用于批量更新规则内容
     *
     * @param prestorageStockMigrationRules
     */
    void batchUpdateById(List<PrestorageStockMigrationRule> prestorageStockMigrationRules);
}
