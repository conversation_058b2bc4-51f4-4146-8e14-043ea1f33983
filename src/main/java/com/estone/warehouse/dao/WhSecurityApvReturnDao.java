package com.estone.warehouse.dao;

import com.estone.warehouse.bean.WhSecurityApvReturn;
import com.estone.warehouse.bean.WhSecurityApvReturnQueryCondition;
import com.whq.tool.component.Pager;
import java.util.List;

public interface WhSecurityApvReturnDao {
    /**
     * This method corresponds to the database table wh_security_apv_return
     *
     * @mbggenerated Wed Mar 13 14:55:15 CST 2019
     */
    int queryWhSecurityApvReturnCount(WhSecurityApvReturnQueryCondition query);

    /**
     * This method corresponds to the database table wh_security_apv_return
     *
     * @mbggenerated Wed Mar 13 14:55:15 CST 2019
     */
    List<WhSecurityApvReturn> queryWhSecurityApvReturnList();

    /**
     * This method corresponds to the database table wh_security_apv_return
     *
     * @mbggenerated Wed Mar 13 14:55:15 CST 2019
     */
    List<WhSecurityApvReturn> queryWhSecurityApvReturnList(WhSecurityApvReturnQueryCondition query, Pager pager);

    /**
     * This method corresponds to the database table wh_security_apv_return
     *
     * @mbggenerated Wed Mar 13 14:55:15 CST 2019
     */
    WhSecurityApvReturn queryWhSecurityApvReturn(Integer primaryKey);

    /**
     * This method corresponds to the database table wh_security_apv_return
     *
     * @mbggenerated Wed Mar 13 14:55:15 CST 2019
     */
    WhSecurityApvReturn queryWhSecurityApvReturn(WhSecurityApvReturnQueryCondition query);

    /**
     * This method corresponds to the database table wh_security_apv_return
     *
     * @mbggenerated Wed Mar 13 14:55:15 CST 2019
     */
    void createWhSecurityApvReturn(WhSecurityApvReturn entity);

    /**
     * This method corresponds to the database table wh_security_apv_return
     *
     * @mbggenerated Wed Mar 13 14:55:15 CST 2019
     */
    void batchCreateWhSecurityApvReturn(List<WhSecurityApvReturn> entityList);

    /**
     * This method corresponds to the database table wh_security_apv_return
     *
     * @mbggenerated Wed Mar 13 14:55:15 CST 2019
     */
    void batchUpdateWhSecurityApvReturn(List<WhSecurityApvReturn> entityList);

    /**
     * This method corresponds to the database table wh_security_apv_return
     *
     * @mbggenerated Wed Mar 13 14:55:15 CST 2019
     */
    void deleteWhSecurityApvReturn(Integer primaryKey);

    /**
     * This method corresponds to the database table wh_security_apv_return
     *
     * @mbggenerated Wed Mar 13 14:55:15 CST 2019
     */
    void updateWhSecurityApvReturn(WhSecurityApvReturn entity);
}