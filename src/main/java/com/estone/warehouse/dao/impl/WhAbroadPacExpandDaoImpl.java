package com.estone.warehouse.dao.impl;

import com.estone.warehouse.bean.WhAbroadPacExpand;
import com.estone.warehouse.bean.WhAbroadPacExpandQueryCondition;
import com.estone.warehouse.dao.WhAbroadPacExpandDao;
import com.estone.warehouse.dao.mapper.WhAbroadPacExpandDBField;
import com.estone.warehouse.dao.mapper.WhAbroadPacExpandMapper;
import com.whq.tool.component.Pager;
import com.whq.tool.context.DataContextHolder;
import com.whq.tool.sqler.SqlerRequest;
import com.whq.tool.sqler.analyzer.DataType;
import com.estone.common.util.SqlerTemplate;
import java.sql.Timestamp;
import java.util.List;
import org.springframework.stereotype.Repository;
import org.springframework.util.Assert;

@Repository("whAbroadPacExpandDao")
public class WhAbroadPacExpandDaoImpl implements WhAbroadPacExpandDao {

    private void setQueryCondition(SqlerRequest request, WhAbroadPacExpandQueryCondition query) {
        if (query == null) {
            return;
        }
        // 添加查询条件
        // 搜索条件不允许出现空字符
        request.setAllowBlank(false);
        
        request.addDataParam(WhAbroadPacExpandDBField.ID, DataType.INT, query.getId());
        request.addDataParam(WhAbroadPacExpandDBField.RETURN_NO, DataType.STRING, query.getReturnNo());
        request.addDataParam(WhAbroadPacExpandDBField.APV_NO, DataType.STRING, query.getApvNo());
        request.addDataParam(WhAbroadPacExpandDBField.UUID_SKU, DataType.STRING, query.getUuidSku());
    }

    @Override
    public int queryWhAbroadPacExpandCount(WhAbroadPacExpandQueryCondition query) {
        SqlerRequest request = new SqlerRequest("queryWhAbroadPacExpandCount");
        setQueryCondition(request, query);
        return SqlerTemplate.queryForInt(request);
    }

    @Override
    public List<WhAbroadPacExpand> queryWhAbroadPacExpandList() {
        SqlerRequest request = new SqlerRequest("queryWhAbroadPacExpandList");
        return SqlerTemplate.query(request, new WhAbroadPacExpandMapper());
    }

    @Override
    public List<WhAbroadPacExpand> queryWhAbroadPacExpandList(WhAbroadPacExpandQueryCondition query, Pager pager) {
        SqlerRequest request = new SqlerRequest("queryWhAbroadPacExpandList");
        setQueryCondition(request, query);
        if(pager != null) {
            request.addFetch(pager.getPageNo(), pager.getPageSize());
        }
        return SqlerTemplate.query(request, new WhAbroadPacExpandMapper());
    }

    @Override
    public WhAbroadPacExpand queryWhAbroadPacExpand(Integer primaryKey) {
        Assert.notNull(primaryKey, "primaryKey is null!");
        SqlerRequest request = new SqlerRequest("queryWhAbroadPacExpandByPrimaryKey");
        request.addDataParam(WhAbroadPacExpandDBField.ID, DataType.INT, primaryKey);
        return SqlerTemplate.queryForObject(request, new WhAbroadPacExpandMapper());
    }

    @Override
    public WhAbroadPacExpand queryWhAbroadPacExpand(WhAbroadPacExpandQueryCondition query) {
        SqlerRequest request = new SqlerRequest("queryWhAbroadPacExpand");
        setQueryCondition(request, query);
        return SqlerTemplate.queryForObject(request, new WhAbroadPacExpandMapper());
    }

    @Override
    public void createWhAbroadPacExpand(WhAbroadPacExpand entity) {
        SqlerRequest request = new SqlerRequest("createWhAbroadPacExpand");
        request.addDataParam(WhAbroadPacExpandDBField.RETURN_NO, DataType.STRING, entity.getReturnNo());
        request.addDataParam(WhAbroadPacExpandDBField.APV_NO, DataType.STRING, entity.getApvNo());
        request.addDataParam(WhAbroadPacExpandDBField.UUID_SKU, DataType.STRING, entity.getUuidSku());
        request.addDataParam(WhAbroadPacExpandDBField.CREATION_DATE, DataType.TIMESTAMP, new Timestamp(System.currentTimeMillis()));
        request.addDataParam(WhAbroadPacExpandDBField.LAST_UPDATE_DATE, DataType.TIMESTAMP, new Timestamp(System.currentTimeMillis()));
        Integer autoIncrementId = SqlerTemplate.executeAndReturn(request);
        entity.setId(autoIncrementId);
    }

    @Override
    public void updateWhAbroadPacExpand(WhAbroadPacExpand entity) {
        SqlerRequest request = new SqlerRequest("updateWhAbroadPacExpandByPrimaryKey");
        request.addDataParam(WhAbroadPacExpandDBField.ID, DataType.INT, entity.getId());
        
        request.addDataParam(WhAbroadPacExpandDBField.RETURN_NO, DataType.STRING, entity.getReturnNo());
        request.addDataParam(WhAbroadPacExpandDBField.APV_NO, DataType.STRING, entity.getApvNo());
        request.addDataParam(WhAbroadPacExpandDBField.UUID_SKU, DataType.STRING, entity.getUuidSku());
        
        request.addDataParam(WhAbroadPacExpandDBField.LAST_UPDATE_DATE, DataType.TIMESTAMP, new Timestamp(System.currentTimeMillis()));
        SqlerTemplate.execute(request);
    }

    @Override
    public void batchCreateWhAbroadPacExpand(List<WhAbroadPacExpand> entityList) {
        if (entityList != null && !entityList.isEmpty()) {
            SqlerRequest request = new SqlerRequest("createWhAbroadPacExpand");
            for (WhAbroadPacExpand entity : entityList) {
                request.addBatchDataParam(WhAbroadPacExpandDBField.RETURN_NO, DataType.STRING, entity.getReturnNo());
                request.addBatchDataParam(WhAbroadPacExpandDBField.APV_NO, DataType.STRING, entity.getApvNo());
                request.addBatchDataParam(WhAbroadPacExpandDBField.UUID_SKU, DataType.STRING, entity.getUuidSku());
                request.addBatchDataParam(WhAbroadPacExpandDBField.CREATION_DATE, DataType.TIMESTAMP, new Timestamp(System.currentTimeMillis()));
                request.addBatchDataParam(WhAbroadPacExpandDBField.LAST_UPDATE_DATE, DataType.TIMESTAMP, new Timestamp(System.currentTimeMillis()));
                request.addBatch();
            }
            SqlerTemplate.batchUpdate(request);
        }
    }

    @Override
    public void batchUpdateWhAbroadPacExpand(List<WhAbroadPacExpand> entityList) {
        if (entityList != null && !entityList.isEmpty()) {
            SqlerRequest request = new SqlerRequest("updateWhAbroadPacExpandByPrimaryKey");
            for (WhAbroadPacExpand entity : entityList) {
                request.addBatchDataParam(WhAbroadPacExpandDBField.ID, DataType.INT, entity.getId());
                
                request.addBatchDataParam(WhAbroadPacExpandDBField.RETURN_NO, DataType.STRING, entity.getReturnNo());
                request.addBatchDataParam(WhAbroadPacExpandDBField.APV_NO, DataType.STRING, entity.getApvNo());
                request.addBatchDataParam(WhAbroadPacExpandDBField.UUID_SKU, DataType.STRING, entity.getUuidSku());
                
                request.addBatchDataParam(WhAbroadPacExpandDBField.LAST_UPDATE_DATE, DataType.TIMESTAMP, new Timestamp(System.currentTimeMillis()));
                request.addBatch();
            }
            SqlerTemplate.batchUpdate(request);
        }
    }

    @Override
    public void deleteWhAbroadPacExpand(Integer primaryKey) {
        SqlerRequest request = new SqlerRequest("deleteWhAbroadPacExpandByPrimaryKey");
        request.addDataParam(WhAbroadPacExpandDBField.ID, DataType.INT, primaryKey);
        SqlerTemplate.execute(request);
    }
}