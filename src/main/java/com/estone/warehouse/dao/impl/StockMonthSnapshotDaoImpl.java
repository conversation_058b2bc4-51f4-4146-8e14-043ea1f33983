package com.estone.warehouse.dao.impl;

import java.util.List;

import org.springframework.stereotype.Repository;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import com.estone.common.util.SqlerTemplate;
import com.estone.warehouse.bean.StockMonthSnapshot;
import com.estone.warehouse.bean.StockMonthSnapshotQueryCondition;
import com.estone.warehouse.dao.StockMonthSnapshotDao;
import com.estone.warehouse.dao.mapper.StockMonthSnapshotDBField;
import com.estone.warehouse.dao.mapper.StockMonthSnapshotMapper;
import com.whq.tool.component.Pager;
import com.whq.tool.sqler.SqlerRequest;
import com.whq.tool.sqler.analyzer.DataType;

@Repository("stockMonthSnapshotDao")
public class StockMonthSnapshotDaoImpl implements StockMonthSnapshotDao {

    private void setQueryCondition(SqlerRequest request, StockMonthSnapshotQueryCondition query) {
        if (query == null) {
            return;
        }
        // 添加只读权限
        if (query.getReadOnly() != null && query.getReadOnly())
        {
            request.setReadOnly(true);
        }
        // 添加查询条件
        // 搜索条件不允许出现空字符
        request.setAllowBlank(false);
        
        request.addDataParam(StockMonthSnapshotDBField.ID, DataType.INT, query.getId());
        request.addDataParam(StockMonthSnapshotDBField.COUNT_MONTH, DataType.STRING, query.getCountMonth());
        request.addDataParam(StockMonthSnapshotDBField.SKU, DataType.STRING, query.getSku());
        request.addDataParam("beforeMonth", DataType.STRING, query.getBeforeMonth());
    }

    @Override
    public int queryStockMonthSnapshotCount(StockMonthSnapshotQueryCondition query) {
        SqlerRequest request = new SqlerRequest("queryStockMonthSnapshotCount");
        setQueryCondition(request, query);
        return SqlerTemplate.queryForInt(request);
    }

    @Override
    public List<StockMonthSnapshot> queryStockMonthSnapshotList() {
        SqlerRequest request = new SqlerRequest("queryStockMonthSnapshotList");
        return SqlerTemplate.query(request, new StockMonthSnapshotMapper());
    }

    @Override
    public List<StockMonthSnapshot> queryStockMonthSnapshotList(StockMonthSnapshotQueryCondition query, Pager pager) {
        SqlerRequest request = new SqlerRequest("queryStockMonthSnapshotList");
        setQueryCondition(request, query);
        if(pager != null) {
            request.addFetch(pager.getPageNo(), pager.getPageSize());
        }
        return SqlerTemplate.query(request, new StockMonthSnapshotMapper());
    }

    @Override
    public StockMonthSnapshot queryStockMonthSnapshot(Integer primaryKey) {
        Assert.notNull(primaryKey, "primaryKey is null!");
        SqlerRequest request = new SqlerRequest("queryStockMonthSnapshotByPrimaryKey");
        request.addDataParam(StockMonthSnapshotDBField.ID, DataType.INT, primaryKey);
        return SqlerTemplate.queryForObject(request, new StockMonthSnapshotMapper());
    }

    @Override
    public StockMonthSnapshot queryStockMonthSnapshot(StockMonthSnapshotQueryCondition query) {
        SqlerRequest request = new SqlerRequest("queryStockMonthSnapshot");
        setQueryCondition(request, query);
        return SqlerTemplate.queryForObject(request, new StockMonthSnapshotMapper());
    }

    @Override
    public void createStockMonthSnapshot(StockMonthSnapshot entity) {
        SqlerRequest request = new SqlerRequest("createStockMonthSnapshot");
        request.addDataParam(StockMonthSnapshotDBField.SKU, DataType.STRING, entity.getSku());
        request.addDataParam(StockMonthSnapshotDBField.QUANTITY, DataType.INT, entity.getQuantity());
        request.addDataParam(StockMonthSnapshotDBField.TRANSFER_QTY, DataType.INT, entity.getTransferQty());
        request.addDataParam(StockMonthSnapshotDBField.NN_QTY, DataType.INT, entity.getNnQty());
        request.addDataParam(StockMonthSnapshotDBField.COUNT_MONTH, DataType.STRING, entity.getCountMonth());
        Integer autoIncrementId = SqlerTemplate.executeAndReturn(request);
        entity.setId(autoIncrementId);
    }

    @Override
    public void updateStockMonthSnapshot(StockMonthSnapshot entity) {
        SqlerRequest request = new SqlerRequest("updateStockMonthSnapshotByPrimaryKey");
        request.addDataParam(StockMonthSnapshotDBField.ID, DataType.INT, entity.getId());
        
        request.addDataParam(StockMonthSnapshotDBField.SKU, DataType.STRING, entity.getSku());
        request.addDataParam(StockMonthSnapshotDBField.QUANTITY, DataType.INT, entity.getQuantity());
        request.addDataParam(StockMonthSnapshotDBField.TRANSFER_QTY, DataType.INT, entity.getTransferQty());
        request.addDataParam(StockMonthSnapshotDBField.NN_QTY, DataType.INT, entity.getNnQty());
        request.addDataParam(StockMonthSnapshotDBField.COUNT_MONTH, DataType.STRING, entity.getCountMonth());
        SqlerTemplate.execute(request);
    }

    @Override
    public void batchCreateStockMonthSnapshot(List<StockMonthSnapshot> entityList) {
        if (entityList != null && !entityList.isEmpty()) {
            SqlerRequest request = new SqlerRequest("createStockMonthSnapshot");
            for (StockMonthSnapshot entity : entityList) {
                request.addBatchDataParam(StockMonthSnapshotDBField.SKU, DataType.STRING, entity.getSku());
                request.addBatchDataParam(StockMonthSnapshotDBField.QUANTITY, DataType.INT, entity.getQuantity());
                request.addBatchDataParam(StockMonthSnapshotDBField.TRANSFER_QTY, DataType.INT, entity.getTransferQty());
                request.addBatchDataParam(StockMonthSnapshotDBField.NN_QTY, DataType.INT, entity.getNnQty());
                request.addBatchDataParam(StockMonthSnapshotDBField.COUNT_MONTH, DataType.STRING, entity.getCountMonth());
                request.addBatch();
            }
            SqlerTemplate.batchUpdate(request);
        }
    }

    @Override
    public void batchUpdateStockMonthSnapshot(List<StockMonthSnapshot> entityList) {
        if (entityList != null && !entityList.isEmpty()) {
            SqlerRequest request = new SqlerRequest("updateStockMonthSnapshotByPrimaryKey");
            for (StockMonthSnapshot entity : entityList) {
                request.addBatchDataParam(StockMonthSnapshotDBField.ID, DataType.INT, entity.getId());
                
                request.addBatchDataParam(StockMonthSnapshotDBField.SKU, DataType.STRING, entity.getSku());
                request.addBatchDataParam(StockMonthSnapshotDBField.QUANTITY, DataType.INT, entity.getQuantity());
                request.addBatchDataParam(StockMonthSnapshotDBField.TRANSFER_QTY, DataType.INT, entity.getTransferQty());
                request.addBatchDataParam(StockMonthSnapshotDBField.NN_QTY, DataType.INT, entity.getNnQty());
                request.addBatchDataParam(StockMonthSnapshotDBField.COUNT_MONTH, DataType.STRING, entity.getCountMonth());
                request.addBatch();
            }
            SqlerTemplate.batchUpdate(request);
        }
    }

    @Override
    public void deleteStockMonthSnapshot(Integer primaryKey) {
        SqlerRequest request = new SqlerRequest("deleteStockMonthSnapshotByPrimaryKey");
        request.addDataParam(StockMonthSnapshotDBField.ID, DataType.INT, primaryKey);
        SqlerTemplate.execute(request);
    }

    @Override
    public void batchDeleteById(List<Integer> id) {
        if (CollectionUtils.isEmpty(id))return;
        SqlerRequest request = new SqlerRequest("batchDeleteById");
        request.addDataParam("id_list", DataType.INT, id);
        SqlerTemplate.execute(request);
    }
}