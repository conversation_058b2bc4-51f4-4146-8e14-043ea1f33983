package com.estone.warehouse.dao.impl;

import java.sql.Timestamp;
import java.util.*;
import java.util.stream.Collectors;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;
import org.springframework.util.Assert;

import com.estone.warehouse.bean.WhExLocation;
import com.estone.warehouse.bean.WhExLocationQueryCondition;
import com.estone.warehouse.dao.WhExLocationDao;
import com.estone.warehouse.dao.mapper.WhExLocationDBField;
import com.estone.warehouse.dao.mapper.WhExLocationMapper;
import com.estone.warehouse.dao.mapper.WhLocationDBField;
import com.whq.tool.component.Pager;
import com.whq.tool.sqler.SqlerRequest;
import com.whq.tool.sqler.analyzer.DataType;
import com.estone.common.util.SqlerTemplate;

@Repository("whExLocationDao")
public class WhExLocationDaoImpl implements WhExLocationDao {

    private void setQueryCondition(SqlerRequest request, WhExLocationQueryCondition query) {
        if (query == null) {
            return;
        }
        // 添加查询条件
        // 搜索条件不允许出现空字符
        request.setAllowBlank(false);
        
        request.addDataParam(WhExLocationDBField.ID, DataType.INT, query.getId());
        request.addDataParam(WhExLocationDBField.LOCATION, DataType.STRING, query.getLocation());
        request.addDataParam(WhExLocationDBField.WAREHOUSE_TYPE, DataType.INT, query.getWarehouseType());
        request.addDataParam(WhExLocationDBField.LOCATION_REGION, DataType.STRING, query.getLocationRegion());
        request.addDataParam(WhExLocationDBField.LOCATION_ATTRIBUTE, DataType.INT, query.getLocationAttribute());
        request.addDataParam(WhExLocationDBField.LOCATION_STATUS, DataType.INT, query.getLocationStatus());
        request.addDataParam(WhExLocationDBField.LOCATION_TYPE, DataType.INT, query.getLocationType());

        if (StringUtils.isNotBlank(query.getLocationAisle()) && query.getLocationAisle().indexOf(",") > -1) {
            request.addDataParam("location_aisle_list", DataType.STRING, Arrays.asList(query.getLocationAisle().split(",")));
        } else {
            request.addDataParam(WhLocationDBField.LOCATION_AISLE, DataType.STRING, query.getLocationAisle());
        }

        if (StringUtils.isNotBlank(query.getLikeLocation())) {
            request.addDataParam("like_location", DataType.STRING, "%" + query.getLikeLocation() + "%");
        }

        if (CollectionUtils.isNotEmpty(query.getLocationIds())) {
            request.addDataParam("location_id_list", DataType.INT, query.getLocationIds());
        }

        if (CollectionUtils.isNotEmpty(query.getLocationList())) {
            request.addDataParam("location_list", DataType.STRING, query.getLocationList());
        }

        // 品类上限
        request.addDataParam(WhLocationDBField.CATEGORY_LIMIT, DataType.INT, query.getCategoryLimit());

        if (null != query.getCategoryActual()) {
            request.addSqlDataParam(WhLocationDBField.CATEGORY_ACTUAL,
                    "AND (SELECT COUNT(*) FROM wh_check_in_exception wcie WHERE wcie.location_number=location) = "
                            + query.getCategoryActual());

        }

    }

    @Override
    public int queryWhExLocationCount(WhExLocationQueryCondition query) {
        SqlerRequest request = new SqlerRequest("queryWhExLocationCount");
        setQueryCondition(request, query);
        return SqlerTemplate.queryForInt(request);
    }

    @Override
    public List<WhExLocation> queryWhExLocationList() {
        SqlerRequest request = new SqlerRequest("queryWhExLocationList");
        return SqlerTemplate.query(request, new WhExLocationMapper());
    }

    @Override
    public List<WhExLocation> queryWhExLocationList(WhExLocationQueryCondition query, Pager pager) {
        SqlerRequest request = new SqlerRequest("queryWhExLocationList");
        setQueryCondition(request, query);
        if(pager != null) {
            request.addFetch(pager.getPageNo(), pager.getPageSize());
        }
        return SqlerTemplate.query(request, new WhExLocationMapper());
    }

    @Override
    public WhExLocation queryWhExLocation(Integer primaryKey) {
        Assert.notNull(primaryKey, "primaryKey is null!");
        SqlerRequest request = new SqlerRequest("queryWhExLocationByPrimaryKey");
        request.addDataParam(WhExLocationDBField.ID, DataType.INT, primaryKey);
        return SqlerTemplate.queryForObject(request, new WhExLocationMapper());
    }

    @Override
    public WhExLocation queryWhExLocation(WhExLocationQueryCondition query) {
        SqlerRequest request = new SqlerRequest("queryWhExLocation");
        setQueryCondition(request, query);
        return SqlerTemplate.queryForObject(request, new WhExLocationMapper());
    }

    @Override
    public void createWhExLocation(WhExLocation entity) {
        SqlerRequest request = new SqlerRequest("createWhExLocation");
        request.addDataParam(WhExLocationDBField.WAREHOUSE_TYPE, DataType.INT, entity.getWarehouseType());
        request.addDataParam(WhExLocationDBField.LOCATION_REGION, DataType.STRING, entity.getLocationRegion());
        request.addDataParam(WhExLocationDBField.LOCATION_AISLE, DataType.STRING, entity.getLocationAisle());
        request.addDataParam(WhExLocationDBField.LOCATION, DataType.STRING, entity.getLocation());
        request.addDataParam(WhExLocationDBField.LOCATION_TYPE, DataType.INT, entity.getLocationType());
        request.addDataParam(WhExLocationDBField.LOCATION_ATTRIBUTE, DataType.INT, entity.getLocationAttribute());
        request.addDataParam(WhExLocationDBField.CATEGORY_LIMIT, DataType.INT, entity.getCategoryLimit());
        request.addDataParam(WhExLocationDBField.STORAGE_ATTRIBUTE, DataType.INT, entity.getStorageAttribute());
        request.addDataParam(WhExLocationDBField.LOCATION_STATUS, DataType.INT, entity.getLocationStatus());
        request.addDataParam(WhExLocationDBField.LOCATION_LENGTH, DataType.DOUBLE, entity.getLocationLength());
        request.addDataParam(WhExLocationDBField.LOCATION_WIDTH, DataType.DOUBLE, entity.getLocationWidth());
        request.addDataParam(WhExLocationDBField.LOCATION_HEIGHT, DataType.DOUBLE, entity.getLocationHeight());
        request.addDataParam(WhExLocationDBField.LOCATION_VOLUME, DataType.DOUBLE, entity.getLocationVolume());
        request.addDataParam(WhExLocationDBField.CREATE_BY, DataType.INT, entity.getCreateBy());
        request.addDataParam(WhExLocationDBField.CREATED_DATE, DataType.TIMESTAMP, entity.getCreatedDate());
        request.addDataParam(WhExLocationDBField.LAST_UPDATE_BY, DataType.INT, entity.getLastUpdateBy());
        request.addDataParam(WhExLocationDBField.LAST_UPDATE_DATE, DataType.TIMESTAMP, new Timestamp(System.currentTimeMillis()));
        Integer autoIncrementId = SqlerTemplate.executeAndReturn(request);
        entity.setId(autoIncrementId);
    }

    @Override
    public void updateWhExLocation(WhExLocation entity) {
        SqlerRequest request = new SqlerRequest("updateWhExLocationByPrimaryKey");
        request.addDataParam(WhExLocationDBField.ID, DataType.INT, entity.getId());
        
        request.addDataParam(WhExLocationDBField.WAREHOUSE_TYPE, DataType.INT, entity.getWarehouseType());
        request.addDataParam(WhExLocationDBField.LOCATION_REGION, DataType.STRING, entity.getLocationRegion());
        request.addDataParam(WhExLocationDBField.LOCATION_AISLE, DataType.STRING, entity.getLocationAisle());
        request.addDataParam(WhExLocationDBField.LOCATION, DataType.STRING, entity.getLocation());
        request.addDataParam(WhExLocationDBField.LOCATION_TYPE, DataType.INT, entity.getLocationType());
        request.addDataParam(WhExLocationDBField.LOCATION_ATTRIBUTE, DataType.INT, entity.getLocationAttribute());
        request.addDataParam(WhExLocationDBField.CATEGORY_LIMIT, DataType.INT, entity.getCategoryLimit());
        request.addDataParam(WhExLocationDBField.STORAGE_ATTRIBUTE, DataType.INT, entity.getStorageAttribute());
        request.addDataParam(WhExLocationDBField.LOCATION_STATUS, DataType.INT, entity.getLocationStatus());
        request.addDataParam(WhExLocationDBField.LOCATION_LENGTH, DataType.DOUBLE, entity.getLocationLength());
        request.addDataParam(WhExLocationDBField.LOCATION_WIDTH, DataType.DOUBLE, entity.getLocationWidth());
        request.addDataParam(WhExLocationDBField.LOCATION_HEIGHT, DataType.DOUBLE, entity.getLocationHeight());
        request.addDataParam(WhExLocationDBField.LOCATION_VOLUME, DataType.DOUBLE, entity.getLocationVolume());
        request.addDataParam(WhExLocationDBField.CREATE_BY, DataType.INT, entity.getCreateBy());
        request.addDataParam(WhExLocationDBField.CREATED_DATE, DataType.TIMESTAMP, entity.getCreatedDate());
        request.addDataParam(WhExLocationDBField.LAST_UPDATE_BY, DataType.INT, entity.getLastUpdateBy());
        request.addDataParam(WhExLocationDBField.LAST_UPDATE_DATE, DataType.TIMESTAMP, new Timestamp(System.currentTimeMillis()));
        SqlerTemplate.execute(request);
    }

    @Override
    public void batchCreateWhExLocation(List<WhExLocation> entityList) {
        if (entityList != null && !entityList.isEmpty()) {
            SqlerRequest request = new SqlerRequest("createWhExLocation");
            for (WhExLocation entity : entityList) {
                request.addBatchDataParam(WhExLocationDBField.WAREHOUSE_TYPE, DataType.INT, entity.getWarehouseType());
                request.addBatchDataParam(WhExLocationDBField.LOCATION_REGION, DataType.STRING, entity.getLocationRegion());
                request.addBatchDataParam(WhExLocationDBField.LOCATION_AISLE, DataType.STRING, entity.getLocationAisle());
                request.addBatchDataParam(WhExLocationDBField.LOCATION, DataType.STRING, entity.getLocation());
                request.addBatchDataParam(WhExLocationDBField.LOCATION_TYPE, DataType.INT, entity.getLocationType());
                request.addBatchDataParam(WhExLocationDBField.LOCATION_ATTRIBUTE, DataType.INT, entity.getLocationAttribute());
                request.addBatchDataParam(WhExLocationDBField.CATEGORY_LIMIT, DataType.INT, entity.getCategoryLimit());
                request.addBatchDataParam(WhExLocationDBField.STORAGE_ATTRIBUTE, DataType.INT, entity.getStorageAttribute());
                request.addBatchDataParam(WhExLocationDBField.LOCATION_STATUS, DataType.INT, entity.getLocationStatus());
                request.addBatchDataParam(WhExLocationDBField.LOCATION_LENGTH, DataType.DOUBLE, entity.getLocationLength());
                request.addBatchDataParam(WhExLocationDBField.LOCATION_WIDTH, DataType.DOUBLE, entity.getLocationWidth());
                request.addBatchDataParam(WhExLocationDBField.LOCATION_HEIGHT, DataType.DOUBLE, entity.getLocationHeight());
                request.addBatchDataParam(WhExLocationDBField.LOCATION_VOLUME, DataType.DOUBLE, entity.getLocationVolume());
                request.addBatchDataParam(WhExLocationDBField.CREATE_BY, DataType.INT, entity.getCreateBy());
                request.addBatchDataParam(WhExLocationDBField.CREATED_DATE, DataType.TIMESTAMP, entity.getCreatedDate());
                request.addBatchDataParam(WhExLocationDBField.LAST_UPDATE_BY, DataType.INT, entity.getLastUpdateBy());
                request.addBatchDataParam(WhExLocationDBField.LAST_UPDATE_DATE, DataType.TIMESTAMP, new Timestamp(System.currentTimeMillis()));
                request.addBatch();
            }
            SqlerTemplate.batchUpdate(request);
        }
    }

    @Override
    public void batchUpdateWhExLocation(List<WhExLocation> entityList) {
        if (entityList != null && !entityList.isEmpty()) {
            SqlerRequest request = new SqlerRequest("updateWhExLocationByPrimaryKey");
            for (WhExLocation entity : entityList) {
                request.addBatchDataParam(WhExLocationDBField.ID, DataType.INT, entity.getId());
                
                request.addBatchDataParam(WhExLocationDBField.WAREHOUSE_TYPE, DataType.INT, entity.getWarehouseType());
                request.addBatchDataParam(WhExLocationDBField.LOCATION_REGION, DataType.STRING, entity.getLocationRegion());
                request.addBatchDataParam(WhExLocationDBField.LOCATION_AISLE, DataType.STRING, entity.getLocationAisle());
                request.addBatchDataParam(WhExLocationDBField.LOCATION, DataType.STRING, entity.getLocation());
                request.addBatchDataParam(WhExLocationDBField.LOCATION_TYPE, DataType.INT, entity.getLocationType());
                request.addBatchDataParam(WhExLocationDBField.LOCATION_ATTRIBUTE, DataType.INT, entity.getLocationAttribute());
                request.addBatchDataParam(WhExLocationDBField.CATEGORY_LIMIT, DataType.INT, entity.getCategoryLimit());
                request.addBatchDataParam(WhExLocationDBField.STORAGE_ATTRIBUTE, DataType.INT, entity.getStorageAttribute());
                request.addBatchDataParam(WhExLocationDBField.LOCATION_STATUS, DataType.INT, entity.getLocationStatus());
                request.addBatchDataParam(WhExLocationDBField.LOCATION_LENGTH, DataType.DOUBLE, entity.getLocationLength());
                request.addBatchDataParam(WhExLocationDBField.LOCATION_WIDTH, DataType.DOUBLE, entity.getLocationWidth());
                request.addBatchDataParam(WhExLocationDBField.LOCATION_HEIGHT, DataType.DOUBLE, entity.getLocationHeight());
                request.addBatchDataParam(WhExLocationDBField.LOCATION_VOLUME, DataType.DOUBLE, entity.getLocationVolume());
                request.addBatchDataParam(WhExLocationDBField.CREATE_BY, DataType.INT, entity.getCreateBy());
                request.addBatchDataParam(WhExLocationDBField.CREATED_DATE, DataType.TIMESTAMP, entity.getCreatedDate());
                request.addBatchDataParam(WhExLocationDBField.LAST_UPDATE_BY, DataType.INT, entity.getLastUpdateBy());
                request.addBatchDataParam(WhExLocationDBField.LAST_UPDATE_DATE, DataType.TIMESTAMP, new Timestamp(System.currentTimeMillis()));
                request.addBatch();
            }
            SqlerTemplate.batchUpdate(request);
        }
    }

    @Override
    public void deleteWhExLocation(Integer primaryKey) {
        SqlerRequest request = new SqlerRequest("deleteWhExLocationByPrimaryKey");
        request.addDataParam(WhExLocationDBField.ID, DataType.INT, primaryKey);
        SqlerTemplate.execute(request);
    }

    @Override
    public Map<String, List<String>> queryRegionAndAisle() {
        SqlerRequest request = new SqlerRequest("queryRegions");
        List<Map<String, Object>> regionsList = SqlerTemplate.queryForList(request);

        SqlerRequest aisleRequest = new SqlerRequest("queryAisles");
        List<Map<String, Object>> aisleList = SqlerTemplate.queryForList(aisleRequest);

        List<String> region = Optional.ofNullable(regionsList).orElse(new ArrayList<>()).stream()
                .filter(m -> m.get("location_region") != null).map(m -> m.get("location_region").toString())
                .collect(Collectors.toList());
        List<String> aisle = Optional.ofNullable(aisleList).orElse(new ArrayList<>()).stream()
                .filter(m -> m.get("location_aisle") != null).map(m -> m.get("location_aisle").toString())
                .collect(Collectors.toList());
        Map<String, List<String>> resultMap = new HashMap<>();
        resultMap.put("region", region);
        resultMap.put("aisle", aisle);
        return resultMap;
    }
}