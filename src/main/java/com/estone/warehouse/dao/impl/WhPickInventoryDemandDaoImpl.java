package com.estone.warehouse.dao.impl;

import java.sql.Timestamp;
import java.util.List;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Repository;
import org.springframework.util.Assert;

import com.estone.common.util.CommonUtils;
import com.estone.warehouse.bean.WhPickInventoryDemand;
import com.estone.warehouse.bean.WhPickInventoryDemandQueryCondition;
import com.estone.warehouse.dao.WhPickInventoryDemandDao;
import com.estone.warehouse.dao.mapper.WhPickInventoryDemandAndInventoryTaskMapper;
import com.estone.warehouse.dao.mapper.WhPickInventoryDemandDBField;
import com.estone.warehouse.dao.mapper.WhPickInventoryDemandMapper;
import com.whq.tool.component.Pager;
import com.whq.tool.context.DataContextHolder;
import com.whq.tool.sqler.SqlerRequest;
import com.whq.tool.sqler.analyzer.DataType;
import com.whq.tool.sqler.dialect.DialectFactory;
import com.whq.tool.sqler.dialect.SQLDialect;
import com.estone.common.util.SqlerTemplate;

@Repository("whPickInventoryDemandDao")
public class WhPickInventoryDemandDaoImpl implements WhPickInventoryDemandDao {

    private void setQueryCondition(SqlerRequest request, WhPickInventoryDemandQueryCondition query) {
        if (query == null) {
            return;
        }
        // 添加查询条件
        // 搜索条件不允许出现空字符
        request.setAllowBlank(false);

        request.addDataParam(WhPickInventoryDemandDBField.ID, DataType.INT, query.getId());
        request.addDataParam(WhPickInventoryDemandDBField.PICK_TASK_NO, DataType.STRING, query.getPickTaskNo());
        request.addDataParam(WhPickInventoryDemandDBField.PICK_TASK_TYPE, DataType.INT, query.getPickTaskType());
        request.addDataParam(WhPickInventoryDemandDBField.STATUS, DataType.INT, query.getStatus());
        request.addDataParam(WhPickInventoryDemandDBField.TASK_LEVEL, DataType.INT, query.getTaskLevel());
        request.addDataParam(WhPickInventoryDemandDBField.DEMAND_TYPE, DataType.INT, query.getDemandType());
        request.addDataParam(WhPickInventoryDemandDBField.CREATION_USER, DataType.INT, query.getCreationUser());
        request.addDataParam(WhPickInventoryDemandDBField.TASK_NO_PREFIX, DataType.STRING, query.getTaskNoPrefix());
        request.addDataParam(WhPickInventoryDemandDBField.STOCK_ID, DataType.INT, query.getStockId());
        if (StringUtils.isNotBlank(query.getSku())) {
            if (StringUtils.contains(query.getSku(), ",")) {
                List<String> skus = CommonUtils.splitList(query.getSku(), ",");
                request.addDataParam("sku_list", DataType.STRING, skus);
            } else {
                request.addDataParam(WhPickInventoryDemandDBField.SKU, DataType.STRING, query.getSku());
            }
        }
        if (StringUtils.isNotBlank(query.getStatusStr())) {
            if (StringUtils.indexOf(query.getStatusStr(), ",") != -1) {
                request.addDataParam("status_list", DataType.INT, CommonUtils.splitIntList(query.getStatusStr(), ","));
            } else {
                request.addDataParam(WhPickInventoryDemandDBField.STATUS, DataType.INT,
                        Integer.valueOf(query.getStatusStr()));
            }
        }
        if (StringUtils.isNotBlank(query.getTaskLevelStr())) {
            if (StringUtils.indexOf(query.getTaskLevelStr(), ",") != -1) {
                request.addDataParam("task_level_list", DataType.INT,
                        CommonUtils.splitIntList(query.getTaskLevelStr(), ","));
            } else {
                request.addDataParam(WhPickInventoryDemandDBField.TASK_LEVEL, DataType.INT,
                        Integer.valueOf(query.getTaskLevelStr()));
            }
        }
        // 需求类型
        if (StringUtils.isNotBlank(query.getDemandTypeStr())) {
            if (StringUtils.indexOf(query.getDemandTypeStr(), ",") != -1) {
                request.addDataParam("demand_type_list", DataType.INT,
                        CommonUtils.splitIntList(query.getDemandTypeStr(), ","));
            } else {
                request.addDataParam("demand_type", DataType.INT,
                        Integer.valueOf(query.getDemandTypeStr()));
            }
        }
        if (StringUtils.isNotBlank(query.getPickTaskTypeStr())) {
            if (StringUtils.indexOf(query.getPickTaskTypeStr(), ",") != -1) {
                request.addDataParam("pick_task_type_list", DataType.INT,
                        CommonUtils.splitIntList(query.getPickTaskTypeStr(), ","));
            } else {
                request.addDataParam(WhPickInventoryDemandDBField.PICK_TASK_TYPE, DataType.INT,
                        Integer.valueOf(query.getPickTaskTypeStr()));
            }
        }
        if (StringUtils.isNotBlank(query.getInventoryTaskNo())) {
            request.addDataParam("inventoryTaskNo", DataType.STRING, query.getInventoryTaskNo());
        }
        if (CollectionUtils.isNotEmpty(query.getNotInStatus())) {
            request.addDataParam("notInStatus", DataType.INT, query.getNotInStatus());
        }
        if (CollectionUtils.isNotEmpty(query.getIds())) {
            request.addDataParam("ids", DataType.INT, query.getIds());
        }
        if (CollectionUtils.isNotEmpty(query.getLocationRegions())) {
            request.addDataParam("regions", DataType.STRING, query.getLocationRegions());
        }
        // 创建时间查询
        request.addDataParam("from_creation_date", DataType.STRING, query.getFromCreationDate());
        request.addDataParam("end_creation_date", DataType.STRING, query.getEndCreationDate());
        if (StringUtils.isNotBlank(query.getOrderBy()) && query.getOrderBy().equals("LOCATION_ASC")) {
            request.addSqlDataParam("ORDER_BY", " ORDER BY stock.location_number ASC");
        } else {
            request.addSqlDataParam("ORDER_BY", " ORDER BY d.id DESC");
        }

        if (CollectionUtils.isNotEmpty(query.getStockIds())) {
            request.addDataParam("stock_id_list", DataType.INT, query.getStockIds());
        }

        if (StringUtils.isNotBlank(query.getStockIdStr())) {
            if (StringUtils.indexOf(query.getStockIdStr(), ",") != -1) {
                request.addDataParam("stock_id_list", DataType.INT, CommonUtils.splitIntList(query.getStockIdStr(), ","));
            } else {
                request.addDataParam(WhPickInventoryDemandDBField.STOCK_ID, DataType.INT,
                        Integer.valueOf(query.getStockIdStr()));
            }
        }

    }

    /**
     * This method corresponds to the database table wh_pick_inventory_demand
     *
     * @mbggenerated Tue Jul 02 16:57:09 CST 2019
     */
    public int queryWhPickInventoryDemandCount(WhPickInventoryDemandQueryCondition query) {
        SqlerRequest request = new SqlerRequest("queryWhPickInventoryDemandCount");
        setQueryCondition(request, query);
        return SqlerTemplate.queryForInt(request);
    }

    /**
     * This method corresponds to the database table wh_pick_inventory_demand
     *
     * @mbggenerated Tue Jul 02 16:57:09 CST 2019
     */
    public List<WhPickInventoryDemand> queryWhPickInventoryDemandList() {
        SqlerRequest request = new SqlerRequest("queryWhPickInventoryDemandList");
        return SqlerTemplate.query(request, new WhPickInventoryDemandMapper());
    }

    /**
     * This method corresponds to the database table wh_pick_inventory_demand
     *
     * @mbggenerated Tue Jul 02 16:57:09 CST 2019
     */
    public List<WhPickInventoryDemand> queryWhPickInventoryDemandList(WhPickInventoryDemandQueryCondition query,
                                                                      Pager pager) {
        SqlerRequest request = new SqlerRequest("queryWhPickInventoryDemandList");
        setQueryCondition(request, query);
        if (pager != null) {
            request.addFetch(pager.getPageNo(), pager.getPageSize());
        }
        return SqlerTemplate.query(request, new WhPickInventoryDemandMapper(true));
    }

    /**
     * This method corresponds to the database table wh_pick_inventory_demand
     *
     * @mbggenerated Tue Jul 02 16:57:09 CST 2019
     */
    public WhPickInventoryDemand queryWhPickInventoryDemand(Integer primaryKey) {
        Assert.notNull(primaryKey);
        SqlerRequest request = new SqlerRequest("queryWhPickInventoryDemandByPrimaryKey");
        request.addDataParam(WhPickInventoryDemandDBField.ID, DataType.INT, primaryKey);
        return SqlerTemplate.queryForObject(request, new WhPickInventoryDemandMapper());
    }

    /**
     * This method corresponds to the database table wh_pick_inventory_demand
     *
     * @mbggenerated Tue Jul 02 16:57:09 CST 2019
     */
    public WhPickInventoryDemand queryWhPickInventoryDemand(WhPickInventoryDemandQueryCondition query) {
        SqlerRequest request = new SqlerRequest("queryWhPickInventoryDemand");
        setQueryCondition(request, query);
        return SqlerTemplate.queryForObject(request, new WhPickInventoryDemandMapper());
    }

    /**
     * This method corresponds to the database table wh_pick_inventory_demand
     *
     * @mbggenerated Tue Jul 02 16:57:09 CST 2019
     */
    public void createWhPickInventoryDemand(WhPickInventoryDemand entity) {
        SqlerRequest request = new SqlerRequest("createWhPickInventoryDemand");
        request.addDataParam(WhPickInventoryDemandDBField.PICK_TASK_NO, DataType.STRING, entity.getPickTaskNo());
        request.addDataParam(WhPickInventoryDemandDBField.PICK_TASK_TYPE, DataType.INT, entity.getPickTaskType());
        request.addDataParam(WhPickInventoryDemandDBField.SKU, DataType.STRING, entity.getSku());
        request.addDataParam(WhPickInventoryDemandDBField.STOCK_ID, DataType.INT, entity.getStockId());
        request.addDataParam(WhPickInventoryDemandDBField.STATUS, DataType.INT, entity.getStatus());
        request.addDataParam(WhPickInventoryDemandDBField.NEED_QUANTITY, DataType.INT, entity.getNeedQuantity());
        request.addDataParam(WhPickInventoryDemandDBField.PICK_QUANTITY, DataType.INT, entity.getPickQuantity());
        request.addDataParam(WhPickInventoryDemandDBField.DIFF_QUANTITY, DataType.INT, entity.getDiffQuantity());
        request.addDataParam(WhPickInventoryDemandDBField.QUANTITY, DataType.INT, entity.getQuantity());
        request.addDataParam(WhPickInventoryDemandDBField.TASK_LEVEL, DataType.INT, entity.getTaskLevel());
        request.addDataParam(WhPickInventoryDemandDBField.DEMAND_TYPE, DataType.INT, entity.getDemandType());
        request.addDataParam(WhPickInventoryDemandDBField.CREATION_USER, DataType.INT,
                entity.getCreationUser() == null ? DataContextHolder.getUserId() : entity.getCreationUser());
        request.addDataParam(WhPickInventoryDemandDBField.CREATION_DATE, DataType.TIMESTAMP,
                new Timestamp(System.currentTimeMillis()));
        request.addDataParam(WhPickInventoryDemandDBField.TASK_NO_PREFIX, DataType.STRING, entity.getTaskNoPrefix());
        Integer autoIncrementId = SqlerTemplate.executeAndReturn(request);
        entity.setId(autoIncrementId);
    }

    /**
     * This method corresponds to the database table wh_pick_inventory_demand
     *
     * @mbggenerated Tue Jul 02 16:57:09 CST 2019
     */
    public int updateWhPickInventoryDemand(WhPickInventoryDemand entity) {
        SqlerRequest request = new SqlerRequest("updateWhPickInventoryDemandByPrimaryKey");
        request.addDataParam(WhPickInventoryDemandDBField.ID, DataType.INT, entity.getId());

        request.addDataParam(WhPickInventoryDemandDBField.PICK_TASK_NO, DataType.STRING, entity.getPickTaskNo());
        request.addDataParam(WhPickInventoryDemandDBField.PICK_TASK_TYPE, DataType.INT, entity.getPickTaskType());
        request.addDataParam(WhPickInventoryDemandDBField.SKU, DataType.STRING, entity.getSku());
        request.addDataParam(WhPickInventoryDemandDBField.STOCK_ID, DataType.INT, entity.getStockId());
        request.addDataParam(WhPickInventoryDemandDBField.STATUS, DataType.INT, entity.getStatus());
        request.addDataParam(WhPickInventoryDemandDBField.NEED_QUANTITY, DataType.INT, entity.getNeedQuantity());
        request.addDataParam(WhPickInventoryDemandDBField.PICK_QUANTITY, DataType.INT, entity.getPickQuantity());
        request.addDataParam(WhPickInventoryDemandDBField.DIFF_QUANTITY, DataType.INT, entity.getDiffQuantity());
        request.addDataParam(WhPickInventoryDemandDBField.QUANTITY, DataType.INT, entity.getQuantity());
        request.addDataParam(WhPickInventoryDemandDBField.TASK_LEVEL, DataType.INT, entity.getTaskLevel());
        request.addDataParam(WhPickInventoryDemandDBField.DEMAND_TYPE, DataType.INT, entity.getDemandType());
        request.addDataParam(WhPickInventoryDemandDBField.CREATION_USER, DataType.INT, entity.getCreationUser());
        request.addDataParam(WhPickInventoryDemandDBField.TASK_NO_PREFIX, DataType.STRING, entity.getTaskNoPrefix());

        request.addDataParam("lastStatus", DataType.INT, entity.getLastStatus());
        return SqlerTemplate.execute(request);
    }

    /**
     * This method corresponds to the database table wh_pick_inventory_demand
     *
     * @mbggenerated Tue Jul 02 16:57:09 CST 2019
     */
    public void batchCreateWhPickInventoryDemand(List<WhPickInventoryDemand> entityList) {
        if (entityList != null && !entityList.isEmpty()) {
            SqlerRequest request = new SqlerRequest("createWhPickInventoryDemand");
            for (WhPickInventoryDemand entity : entityList) {
                request.addBatchDataParam(WhPickInventoryDemandDBField.PICK_TASK_NO, DataType.STRING,
                        entity.getPickTaskNo());
                request.addBatchDataParam(WhPickInventoryDemandDBField.PICK_TASK_TYPE, DataType.INT,
                        entity.getPickTaskType());
                request.addBatchDataParam(WhPickInventoryDemandDBField.SKU, DataType.STRING, entity.getSku());
                request.addBatchDataParam(WhPickInventoryDemandDBField.STOCK_ID, DataType.INT, entity.getStockId());
                request.addBatchDataParam(WhPickInventoryDemandDBField.STATUS, DataType.INT, entity.getStatus());
                request.addBatchDataParam(WhPickInventoryDemandDBField.NEED_QUANTITY, DataType.INT,
                        entity.getNeedQuantity());
                request.addBatchDataParam(WhPickInventoryDemandDBField.PICK_QUANTITY, DataType.INT,
                        entity.getPickQuantity());
                request.addBatchDataParam(WhPickInventoryDemandDBField.DIFF_QUANTITY, DataType.INT,
                        entity.getDiffQuantity());
                request.addBatchDataParam(WhPickInventoryDemandDBField.QUANTITY, DataType.INT, entity.getQuantity());
                request.addBatchDataParam(WhPickInventoryDemandDBField.TASK_LEVEL, DataType.INT, entity.getTaskLevel());
                request.addBatchDataParam(WhPickInventoryDemandDBField.DEMAND_TYPE, DataType.INT, entity.getDemandType());
                request.addBatchDataParam(WhPickInventoryDemandDBField.CREATION_USER, DataType.INT,
                        entity.getCreationUser() == null ? DataContextHolder.getUserId() : entity.getCreationUser());
                request.addBatchDataParam(WhPickInventoryDemandDBField.CREATION_DATE, DataType.TIMESTAMP,
                        new Timestamp(System.currentTimeMillis()));
                request.addBatchDataParam(WhPickInventoryDemandDBField.TASK_NO_PREFIX, DataType.STRING,
                        entity.getTaskNoPrefix());
                request.addBatch();
            }
            SqlerTemplate.batchUpdate(request);
        }
    }

    /**
     * This method corresponds to the database table wh_pick_inventory_demand
     *
     * @mbggenerated Tue Jul 02 16:57:09 CST 2019
     */
    public int[] batchUpdateWhPickInventoryDemand(List<WhPickInventoryDemand> entityList) {
        if (entityList != null && !entityList.isEmpty()) {
            SqlerRequest request = new SqlerRequest("updateWhPickInventoryDemandByPrimaryKey");
            for (WhPickInventoryDemand entity : entityList) {
                request.addBatchDataParam(WhPickInventoryDemandDBField.ID, DataType.INT, entity.getId());

                request.addBatchDataParam(WhPickInventoryDemandDBField.PICK_TASK_NO, DataType.STRING,
                        entity.getPickTaskNo());
                request.addBatchDataParam(WhPickInventoryDemandDBField.PICK_TASK_TYPE, DataType.INT,
                        entity.getPickTaskType());
                request.addBatchDataParam(WhPickInventoryDemandDBField.SKU, DataType.STRING, entity.getSku());
                request.addBatchDataParam(WhPickInventoryDemandDBField.STOCK_ID, DataType.INT, entity.getStockId());
                request.addBatchDataParam(WhPickInventoryDemandDBField.STATUS, DataType.INT, entity.getStatus());
                request.addBatchDataParam(WhPickInventoryDemandDBField.NEED_QUANTITY, DataType.INT,
                        entity.getNeedQuantity());
                request.addBatchDataParam(WhPickInventoryDemandDBField.PICK_QUANTITY, DataType.INT,
                        entity.getPickQuantity());
                request.addBatchDataParam(WhPickInventoryDemandDBField.DIFF_QUANTITY, DataType.INT,
                        entity.getDiffQuantity());
                request.addBatchDataParam(WhPickInventoryDemandDBField.QUANTITY, DataType.INT, entity.getQuantity());
                request.addBatchDataParam(WhPickInventoryDemandDBField.TASK_LEVEL, DataType.INT, entity.getTaskLevel());
                request.addBatchDataParam(WhPickInventoryDemandDBField.DEMAND_TYPE, DataType.INT, entity.getDemandType());
                request.addBatchDataParam(WhPickInventoryDemandDBField.CREATION_USER, DataType.INT,
                        entity.getCreationUser());
                request.addBatchDataParam(WhPickInventoryDemandDBField.TASK_NO_PREFIX, DataType.STRING,
                        entity.getTaskNoPrefix());

                request.addBatch();
            }
            return SqlerTemplate.batchUpdate(request);
        }
        return new int[]{0};
    }

    /**
     * This method corresponds to the database table wh_pick_inventory_demand
     *
     * @mbggenerated Tue Jul 02 16:57:09 CST 2019
     */
    public void deleteWhPickInventoryDemand(Integer primaryKey) {
        SqlerRequest request = new SqlerRequest("deleteWhPickInventoryDemandByPrimaryKey");
        request.addDataParam(WhPickInventoryDemandDBField.ID, DataType.INT, primaryKey);
        SqlerTemplate.execute(request);
    }

    @Override
    public int queryWhPickInventoryDemandAndInventoryTaskCount(WhPickInventoryDemandQueryCondition query) {
        SqlerRequest request = new SqlerRequest("queryWhPickInventoryDemandAndInventoryTaskCount");
        setQueryCondition(request, query);
        return SqlerTemplate.queryForInt(request);
    }

    @Override
    public List<WhPickInventoryDemand> queryWhPickInventoryDemandAndInventoryTaskList(
            WhPickInventoryDemandQueryCondition query, Pager pager) {
        SqlerRequest request = new SqlerRequest("queryWhPickInventoryDemandAndInventoryTaskList");
        setQueryCondition(request, query);
        if (pager != null) {
            // request.addFetch(pager.getPageNo(), pager.getPageSize());
            SQLDialect dial = DialectFactory.createDialect(null);

            long start = (long) (pager.getPageNo() - 1) * pager.getPageSize();

            long end = start + pager.getPageSize() - 1L;

            request.addSqlDataParam("LIMIT", dial.queryFirst2Last("", (int) start, (int) end));
        }
        return SqlerTemplate.query(request, new WhPickInventoryDemandAndInventoryTaskMapper(true));
    }
}