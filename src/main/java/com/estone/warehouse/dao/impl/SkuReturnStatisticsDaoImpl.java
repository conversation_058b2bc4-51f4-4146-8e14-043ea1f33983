package com.estone.warehouse.dao.impl;

import com.estone.common.util.SqlerTemplate;
import com.estone.warehouse.bean.SkuReturnStatistics;
import com.estone.warehouse.bean.SkuReturnStatisticsQueryCondition;
import com.estone.warehouse.dao.SkuReturnStatisticsDao;
import com.estone.warehouse.dao.mapper.SkuReturnStatisticsDBField;
import com.estone.warehouse.dao.mapper.SkuReturnStatisticsMapper;
import com.whq.tool.component.Pager;
import com.whq.tool.sqler.SqlerRequest;
import com.whq.tool.sqler.analyzer.DataType;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;
import org.springframework.util.Assert;

import java.util.Arrays;
import java.util.List;

@Repository("skuReturnStatisticsDao")
public class SkuReturnStatisticsDaoImpl implements SkuReturnStatisticsDao {

    private void setQueryCondition(SqlerRequest request, SkuReturnStatisticsQueryCondition query) {
        if (query == null) {
            return;
        }
        // 添加只读权限
        if (query.getReadOnly() != null && query.getReadOnly())
        {
            request.setReadOnly(true);
        }
        // 添加查询条件
        // 搜索条件不允许出现空字符
        request.setAllowBlank(false);
        
        request.addDataParam(SkuReturnStatisticsDBField.ID, DataType.INT, query.getId());
        request.addDataParam(SkuReturnStatisticsDBField.SKU_TAG, DataType.STRING, query.getSkuTag());
        request.addDataParam(SkuReturnStatisticsDBField.PLATFORM, DataType.INT, query.getPlatform());
        request.addDataParam(SkuReturnStatisticsDBField.RETURN_TIME, DataType.TIMESTAMP, query.getReturnTime());
        request.addDataParam(SkuReturnStatisticsDBField.RETURN_BY, DataType.INT, query.getReturnBy());

        if (StringUtils.isNotBlank(query.getStartReturnTime())) {
            request.addDataParam("startReturnTime", DataType.STRING, query.getStartReturnTime());
        }
        if (StringUtils.isNotBlank(query.getEndReturnTime())) {
            request.addDataParam("endReturnTime", DataType.STRING, query.getEndReturnTime());
        }


        if (StringUtils.isNotBlank(query.getApvNo())){
            if (!query.getApvNo().contains(",")){
                request.addDataParam(SkuReturnStatisticsDBField.APV_NO, DataType.STRING, query.getApvNo());
            }else{
                List<String> apvNoList = Arrays.asList(query.getApvNo().split(","));
                request.addDataParam("apvNoList",  DataType.STRING,apvNoList);
            }
        }
        if (StringUtils.isNotBlank(query.getSku())){
            if (!query.getSku().contains(",")){
                request.addDataParam(SkuReturnStatisticsDBField.SKU, DataType.STRING, query.getSku());
            }else{
                List<String> skuList = Arrays.asList(query.getSku().split(","));
                request.addDataParam("skuList",  DataType.STRING,skuList);
            }
        }

    }

    @Override
    public int querySkuReturnStatisticsCount(SkuReturnStatisticsQueryCondition query) {
        SqlerRequest request = new SqlerRequest("querySkuReturnStatisticsCount");
        setQueryCondition(request, query);
        return SqlerTemplate.queryForInt(request);
    }

    @Override
    public List<SkuReturnStatistics> querySkuReturnStatisticsList() {
        SqlerRequest request = new SqlerRequest("querySkuReturnStatisticsList");
        return SqlerTemplate.query(request, new SkuReturnStatisticsMapper());
    }

    @Override
    public List<SkuReturnStatistics> querySkuReturnStatisticsList(SkuReturnStatisticsQueryCondition query, Pager pager) {
        SqlerRequest request = new SqlerRequest("querySkuReturnStatisticsList");
        setQueryCondition(request, query);
        if(pager != null) {
            request.addFetch(pager.getPageNo(), pager.getPageSize());
        }
        return SqlerTemplate.query(request, new SkuReturnStatisticsMapper());
    }

    @Override
    public SkuReturnStatistics querySkuReturnStatistics(Integer primaryKey) {
        Assert.notNull(primaryKey, "primaryKey is null!");
        SqlerRequest request = new SqlerRequest("querySkuReturnStatisticsByPrimaryKey");
        request.addDataParam(SkuReturnStatisticsDBField.ID, DataType.INT, primaryKey);
        return SqlerTemplate.queryForObject(request, new SkuReturnStatisticsMapper());
    }

    @Override
    public SkuReturnStatistics querySkuReturnStatistics(SkuReturnStatisticsQueryCondition query) {
        SqlerRequest request = new SqlerRequest("querySkuReturnStatistics");
        setQueryCondition(request, query);
        return SqlerTemplate.queryForObject(request, new SkuReturnStatisticsMapper());
    }

    @Override
    public void createSkuReturnStatistics(SkuReturnStatistics entity) {
        SqlerRequest request = new SqlerRequest("createSkuReturnStatistics");
        request.addDataParam(SkuReturnStatisticsDBField.APV_NO, DataType.STRING, entity.getApvNo());
        request.addDataParam(SkuReturnStatisticsDBField.SKU, DataType.STRING, entity.getSku());
        request.addDataParam(SkuReturnStatisticsDBField.SKU_TAG, DataType.STRING, entity.getSkuTag());
        request.addDataParam(SkuReturnStatisticsDBField.PLATFORM, DataType.INT, entity.getPlatform());
        request.addDataParam(SkuReturnStatisticsDBField.RETURN_TIME, DataType.TIMESTAMP, entity.getReturnTime());
        request.addDataParam(SkuReturnStatisticsDBField.RETURN_BY, DataType.INT, entity.getReturnBy());
        Integer autoIncrementId = SqlerTemplate.executeAndReturn(request);
        entity.setId(autoIncrementId);
    }

    @Override
    public void updateSkuReturnStatistics(SkuReturnStatistics entity) {
        SqlerRequest request = new SqlerRequest("updateSkuReturnStatisticsByPrimaryKey");
        request.addDataParam(SkuReturnStatisticsDBField.ID, DataType.INT, entity.getId());
        
        request.addDataParam(SkuReturnStatisticsDBField.APV_NO, DataType.STRING, entity.getApvNo());
        request.addDataParam(SkuReturnStatisticsDBField.SKU, DataType.STRING, entity.getSku());
        request.addDataParam(SkuReturnStatisticsDBField.SKU_TAG, DataType.STRING, entity.getSkuTag());
        request.addDataParam(SkuReturnStatisticsDBField.PLATFORM, DataType.INT, entity.getPlatform());
        request.addDataParam(SkuReturnStatisticsDBField.RETURN_TIME, DataType.TIMESTAMP, entity.getReturnTime());
        request.addDataParam(SkuReturnStatisticsDBField.RETURN_BY, DataType.INT, entity.getReturnBy());
        SqlerTemplate.execute(request);
    }

    @Override
    public void batchCreateSkuReturnStatistics(List<SkuReturnStatistics> entityList) {
        if (entityList != null && !entityList.isEmpty()) {
            SqlerRequest request = new SqlerRequest("createSkuReturnStatistics");
            for (SkuReturnStatistics entity : entityList) {
                request.addBatchDataParam(SkuReturnStatisticsDBField.APV_NO, DataType.STRING, entity.getApvNo());
                request.addBatchDataParam(SkuReturnStatisticsDBField.SKU, DataType.STRING, entity.getSku());
                request.addBatchDataParam(SkuReturnStatisticsDBField.SKU_TAG, DataType.STRING, entity.getSkuTag());
                request.addBatchDataParam(SkuReturnStatisticsDBField.PLATFORM, DataType.INT, entity.getPlatform());
                request.addBatchDataParam(SkuReturnStatisticsDBField.RETURN_TIME, DataType.TIMESTAMP, entity.getReturnTime());
                request.addBatchDataParam(SkuReturnStatisticsDBField.RETURN_BY, DataType.INT, entity.getReturnBy());
                request.addBatch();
            }
            SqlerTemplate.batchUpdate(request);
        }
    }

    @Override
    public void batchUpdateSkuReturnStatistics(List<SkuReturnStatistics> entityList) {
        if (entityList != null && !entityList.isEmpty()) {
            SqlerRequest request = new SqlerRequest("updateSkuReturnStatisticsByPrimaryKey");
            for (SkuReturnStatistics entity : entityList) {
                request.addBatchDataParam(SkuReturnStatisticsDBField.ID, DataType.INT, entity.getId());
                
                request.addBatchDataParam(SkuReturnStatisticsDBField.APV_NO, DataType.STRING, entity.getApvNo());
                request.addBatchDataParam(SkuReturnStatisticsDBField.SKU, DataType.STRING, entity.getSku());
                request.addBatchDataParam(SkuReturnStatisticsDBField.SKU_TAG, DataType.STRING, entity.getSkuTag());
                request.addBatchDataParam(SkuReturnStatisticsDBField.PLATFORM, DataType.INT, entity.getPlatform());
                request.addBatchDataParam(SkuReturnStatisticsDBField.RETURN_TIME, DataType.TIMESTAMP, entity.getReturnTime());
                request.addBatchDataParam(SkuReturnStatisticsDBField.RETURN_BY, DataType.INT, entity.getReturnBy());
                request.addBatch();
            }
            SqlerTemplate.batchUpdate(request);
        }
    }

    @Override
    public void deleteSkuReturnStatistics(Integer primaryKey) {
        SqlerRequest request = new SqlerRequest("deleteSkuReturnStatisticsByPrimaryKey");
        request.addDataParam(SkuReturnStatisticsDBField.ID, DataType.INT, primaryKey);
        SqlerTemplate.execute(request);
    }
}