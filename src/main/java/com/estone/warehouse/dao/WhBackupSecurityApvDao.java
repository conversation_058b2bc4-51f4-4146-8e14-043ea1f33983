package com.estone.warehouse.dao;

import com.estone.warehouse.bean.WhBackupSecurityApv;
import com.estone.warehouse.bean.WhBackupSecurityApvQueryCondition;
import com.whq.tool.component.Pager;
import java.util.List;

public interface WhBackupSecurityApvDao {
    /**
     * This method corresponds to the database table wh_backup_security_apv
     *
     * @mbggenerated Thu Jun 13 15:12:44 CST 2019
     */
    int queryWhBackupSecurityApvCount(WhBackupSecurityApvQueryCondition query);

    /**
     * This method corresponds to the database table wh_backup_security_apv
     *
     * @mbggenerated Thu Jun 13 15:12:44 CST 2019
     */
    List<WhBackupSecurityApv> queryWhBackupSecurityApvList();

    /**
     * This method corresponds to the database table wh_backup_security_apv
     *
     * @mbggenerated Thu Jun 13 15:12:44 CST 2019
     */
    List<WhBackupSecurityApv> queryWhBackupSecurityApvList(WhBackupSecurityApvQueryCondition query, Pager pager);

    /**
     * This method corresponds to the database table wh_backup_security_apv
     *
     * @mbggenerated Thu Jun 13 15:12:44 CST 2019
     */
    WhBackupSecurityApv queryWhBackupSecurityApv(Integer primaryKey);

    /**
     * This method corresponds to the database table wh_backup_security_apv
     *
     * @mbggenerated Thu Jun 13 15:12:44 CST 2019
     */
    WhBackupSecurityApv queryWhBackupSecurityApv(WhBackupSecurityApvQueryCondition query);

    /**
     * This method corresponds to the database table wh_backup_security_apv
     *
     * @mbggenerated Thu Jun 13 15:12:44 CST 2019
     */
    void createWhBackupSecurityApv(WhBackupSecurityApv entity);

    /**
     * This method corresponds to the database table wh_backup_security_apv
     *
     * @mbggenerated Thu Jun 13 15:12:44 CST 2019
     */
    void batchCreateWhBackupSecurityApv(List<WhBackupSecurityApv> entityList);

    /**
     * This method corresponds to the database table wh_backup_security_apv
     *
     * @mbggenerated Thu Jun 13 15:12:44 CST 2019
     */
    void batchUpdateWhBackupSecurityApv(List<WhBackupSecurityApv> entityList);

    /**
     * This method corresponds to the database table wh_backup_security_apv
     *
     * @mbggenerated Thu Jun 13 15:12:44 CST 2019
     */
    void deleteWhBackupSecurityApv(Integer primaryKey);

    /**
     * This method corresponds to the database table wh_backup_security_apv
     *
     * @mbggenerated Thu Jun 13 15:12:44 CST 2019
     */
    void updateWhBackupSecurityApv(WhBackupSecurityApv entity);
}