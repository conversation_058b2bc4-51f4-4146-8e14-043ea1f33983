package com.estone.warehouse.aspect;

import org.springframework.core.annotation.Order;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * @Description: 中转仓库存锁注解
 * @Author: Yimeil
 * @Date: 2021/6/21 11:07
 * @Version: 1.0.0
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
@Documented
@Order(99)
public @interface TransitStockLock {
}
