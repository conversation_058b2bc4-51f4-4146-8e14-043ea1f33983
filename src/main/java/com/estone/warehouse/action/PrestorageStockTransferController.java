package com.estone.warehouse.action;

import java.util.*;
import java.util.stream.Collectors;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;

import com.estone.common.SelectJson;
import com.estone.common.enums.ExportType;
import com.estone.system.downloadcenter.enums.WhDownloadContentEnum;
import com.estone.system.downloadcenter.service.WhDownloadCenterService;
import com.estone.warehouse.bean.PrestorageStockTransfer;
import com.estone.warehouse.bean.PrestorageStockTransferDetail;
import com.estone.warehouse.bean.PrestorageStockTransferOrder;
import com.estone.warehouse.bean.PrestorageStockTransferQueryCondition;
import com.estone.warehouse.domain.DownloadModelDo;
import com.estone.warehouse.domain.PrestorageStockTransferDO;
import com.estone.warehouse.enums.PrestorageStockTransferSourceEnum;
import com.estone.warehouse.enums.PrestorageStockTransferStatusEnum;
import com.estone.warehouse.service.PrestorageStockTransferService;
import com.whq.tool.component.Pager;
import com.whq.tool.component.StatusCode;
import com.whq.tool.json.ResponseJson;

/**
 * <AUTHOR>
 * @date 2023-03-08 14:33
 */
@Controller
@RequestMapping("prestorageStockTransfer")
public class PrestorageStockTransferController {

    @Autowired
    private PrestorageStockTransferService prestorageStockTransferService;
    @Resource
    private WhDownloadCenterService whDownloadCenterService;

    private final static String[] headers = {"编号", "仓库", "任务号", "类型", "SKU", "名称",
            "迁出库位", "迁入库位", "迁移数量", "拣货数量", "上架数量"};

    @GetMapping("/init")
    public String init(@ModelAttribute("domain") PrestorageStockTransferDO prestorageStockTransferDO) {
        initFormData(prestorageStockTransferDO);
        return "warehouse/prestorageStockTransferList";
    }


    @PostMapping("/search")
    public String search(@ModelAttribute("domain") PrestorageStockTransferDO prestorageStockTransferDO) {
        initFormData(prestorageStockTransferDO);
        PrestorageStockTransferQueryCondition queryCondition = prestorageStockTransferDO.getQuery();
        Pager pager = prestorageStockTransferDO.getPage();
        if (Objects.isNull(queryCondition)) {
            queryCondition = new PrestorageStockTransferQueryCondition();
        }
        List<PrestorageStockTransfer> prestorageStockTransfers = prestorageStockTransferService.queryOrders(queryCondition, pager);
        prestorageStockTransferDO.setOrders(prestorageStockTransfers);
        return "warehouse/prestorageStockTransferList";
    }


    @GetMapping("/orderDetail")
    public String detail(@ModelAttribute("domain") PrestorageStockTransferDO prestorageStockTransferDO,
                         @RequestParam("orderId") Integer orderId) {
        List<PrestorageStockTransferDetail> prestorageStockTransferDetails = prestorageStockTransferService.queryOrderDetailsByOrderId(orderId);
        prestorageStockTransferDO.setDetails(prestorageStockTransferDetails);
        return "warehouse/prestorageStockTransferDetail";
    }


    @PostMapping("/batchAbort")
    @ResponseBody
    public ResponseJson abort(@RequestParam("ids") List<Integer> ids) {
        ResponseJson result = new ResponseJson(StatusCode.SUCCESS);
        List<PrestorageStockTransferOrder> unsuccessfulOrders = prestorageStockTransferService.batchAbortOrder(ids);
        if (CollectionUtils.isNotEmpty(unsuccessfulOrders)) {
            result.setStatus(StatusCode.FAIL);
            List<String> orderNos = unsuccessfulOrders.stream()
                    .map(PrestorageStockTransferOrder::getOrderNo)
                    .collect(Collectors.toList());
            String message = StringUtils.join(orderNos, ",");
            result.setMessage("订单废弃失败:" + message);
        }
        return result;
    }

    @PostMapping("/import")
    @ResponseBody
    public ResponseJson importFile(HttpServletRequest request) {
        MultipartHttpServletRequest multiRequest = (MultipartHttpServletRequest) request;
        Map<String, MultipartFile> fileMap = multiRequest.getFileMap();
        return prestorageStockTransferService.handleFiles(fileMap);
    }

    @GetMapping("/downloadModel")
    public String downloadModel(@ModelAttribute("domain") DownloadModelDo domain) {
        domain.setUrl("prestorageStockTransfer/download");
        return "common/download_mode";
    }

    @PostMapping("/download")
    @ResponseBody
    public ResponseJson download(@ModelAttribute("domain") PrestorageStockTransferDO domain,
                         @RequestParam(value = "ids", required = false) List<Integer> ids,
                         @RequestParam("exportType") String exportType,
                         @RequestParam("uuid") String uuid) {

        ResponseJson response = new ResponseJson();
        ExportType exportTypeEnum = ExportType.build(exportType);
        PrestorageStockTransferQueryCondition query = domain.getQuery();
        if (query == null) {
            query = new PrestorageStockTransferQueryCondition();
            domain.setQuery(query);
        }
        query.setIsDownload(true);

        boolean isAll = false;
        // 查询导出的数据
        switch (exportTypeEnum) {
            //全量导出需要循环分sheet处理
            case ALL: {
                isAll = whDownloadCenterService.checkFieldAllNull(query);
                domain.getPage().setPageNo(-1);
                break;
            }
            case PAGE: {
                isAll = false;
                List<PrestorageStockTransferOrder> transferOrders = prestorageStockTransferService
                        .queryTransferOrders(query, domain.getPage());
                List<Integer> idList = Optional.ofNullable(transferOrders).orElse(Collections.emptyList()).stream()
                        .map(PrestorageStockTransferOrder::getId).collect(Collectors.toList());
                query.setOrderIds(idList);
                break;
            }
            case CHECKED: {
                isAll = false;
                query = new PrestorageStockTransferQueryCondition();
                query.setOrderIds(ids);
                domain.getPage().setPageNo(-1);
                break;
            }
        }
        String fileName = "存货迁移明细" + System.currentTimeMillis() + ".xlsx";
        PrestorageStockTransferQueryCondition finalQuery = query;
        whDownloadCenterService.downloading(fileName,headers, WhDownloadContentEnum.PRE_STORAGE_STOCK_TRANSFER,isAll,domain.getPage(),(page) -> {
            List<PrestorageStockTransferDetail> stockTransferDetails = prestorageStockTransferService.queryOrderDetails(finalQuery, page);
            return prestorageStockTransferService.getExportList(stockTransferDetails, headers);
        });
        response.setStatus(StatusCode.SUCCESS);
        response.setMessage("导出任务已经创建，请到下载中心查看结果");
        return response;
    }

    @GetMapping("/printInit")
    public String printInit(@ModelAttribute("domain") PrestorageStockTransferDO prestorageStockTransferDO,
                        @RequestParam("ids") List<Integer> ids) {
        PrestorageStockTransferQueryCondition queryCondition = prestorageStockTransferDO.getQuery();
        if (Objects.isNull(queryCondition)) {
            queryCondition = new PrestorageStockTransferQueryCondition();
        }
        queryCondition.setOrderIds(ids);
        List<PrestorageStockTransfer> orderNos = prestorageStockTransferService.queryOrders(queryCondition, null);
        prestorageStockTransferDO.setOrderNos(orderNos);
        return "warehouse/prestorageStockTransferPrint";
    }

    @GetMapping("/print")
    @ResponseBody
    public void print(@ModelAttribute("domain") PrestorageStockTransferDO prestorageStockTransferDO,
                      @RequestParam("ids") List<Integer> ids) {
        if (CollectionUtils.isEmpty(ids)){
            return;
        }
        prestorageStockTransferService.printOrder(ids);
    }

    /**
     * 用于初始化数据表格
     *
     * @param prestorageStockTransferDO
     */
    private void initFormData(PrestorageStockTransferDO prestorageStockTransferDO) {
        prestorageStockTransferDO.setStatusSelectData(SelectJson.getList(PrestorageStockTransferStatusEnum.values()));
        prestorageStockTransferDO.setSourceSelectData(SelectJson.getList(PrestorageStockTransferSourceEnum.values()));
    }


}
