package com.estone.warehouse.action;

import com.estone.common.CacheName;
import com.estone.common.SelectJson;
import com.estone.common.util.CacheUtils;
import com.estone.common.util.SystemLogUtils;
import com.estone.system.user.bean.SaleUser;
import com.estone.warehouse.bean.WhBox;
import com.estone.warehouse.bean.WhBoxQueryCondition;
import com.estone.warehouse.domain.WhBoxDo;
import com.estone.warehouse.enums.BoxStatus;
import com.estone.warehouse.enums.BoxType;
import com.estone.warehouse.service.WhBoxService;
import com.whq.tool.action.BaseController;
import com.whq.tool.component.Pager;
import com.whq.tool.component.StatusCode;
import com.whq.tool.json.ResponseJson;
import jodd.util.StringUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpSession;
import java.util.*;

@Controller
@RequestMapping(value = "warehouse/boxs")
public class WhBoxController extends BaseController {
    private static Logger logger = LoggerFactory.getLogger(WhBoxController.class);
    @Resource
    private WhBoxService whBoxService;
    private final static String BOX_FROM_OUT = "OUT";
    @RequestMapping(method = { RequestMethod.GET })
    public String init(@ModelAttribute("domain") WhBoxDo domain) {
        initFormData(domain);
        if (StringUtils.equalsAnyIgnoreCase(domain.getBoxFrom(),BOX_FROM_OUT)){
            return "warehouse/boxOutList";
        } else {
            return "warehouse/boxList";
        }
    }

    private void initFormData(@ModelAttribute("domain") WhBoxDo domain) {
        if (StringUtils.equalsAnyIgnoreCase(domain.getBoxFrom(),BOX_FROM_OUT)){
            domain.setBoxTypes(SelectJson.getList(BoxType.valuesOutBoxType()));
        } else {
            domain.setBoxTypes(SelectJson.getList(BoxType.valuesInBoxType()));
        }
    }

    private void queryWhBoxs(@ModelAttribute("domain") WhBoxDo domain) {
        WhBoxQueryCondition query = domain.getQuery();
        Pager page = domain.getPage();
        if (query == null) {
            query = new WhBoxQueryCondition();
            domain.setQuery(query);
        }
        if (query.getType() == null) {
            if (StringUtils.equalsAnyIgnoreCase(domain.getBoxFrom(),BOX_FROM_OUT)){
                query.setTypeList(BoxType.getOutBoxType());
            } else {
                query.setTypeList(BoxType.getInBoxType());
            }
        }
        query.setWhId(CacheUtils.getLocalWarehouseId());
        List<WhBox> whBoxs = whBoxService.queryWhBoxs(query, page);
        domain.setWhBoxs(whBoxs);
    }

    @RequestMapping(value = "search", method = { RequestMethod.POST })
    public String search(@ModelAttribute("domain") WhBoxDo domain) {
        initFormData(domain);
        queryWhBoxs(domain);
        if (StringUtils.equalsAnyIgnoreCase(domain.getBoxFrom(),BOX_FROM_OUT)){
            return "warehouse/boxOutList";
        } else {
            return "warehouse/boxList";
        }
    }

    @RequestMapping(value = "create", method = { RequestMethod.GET })
    public String toCreateWhBox(@ModelAttribute("domain") WhBoxDo domain) {
        return "warehouse/boxAdd";
    }

    @RequestMapping(value = "create", method = { RequestMethod.POST })
    @ResponseBody
    public ResponseJson createWhBox(@ModelAttribute("domain") WhBoxDo domain, HttpSession session) {
        ResponseJson responseJson = new ResponseJson(StatusCode.FAIL);
        WhBox whBox = domain.getWhBox();
        List<Integer> ids = new ArrayList<>();
        for (int i = 0; i < domain.getNum(); i++) {
            try {
                whBox.setWhId(CacheUtils.getLocalWarehouseId());
                whBoxService.createWhBox(whBox);
                if (whBox.getId() != null) {
                    ids.add(whBox.getId());
                }
            }
            catch (Exception e) {
                logger.info(e.getMessage());
                continue;
            }
        }
        if (CollectionUtils.isNotEmpty(ids)) {
            responseJson.setStatus(StatusCode.SUCCESS);
            responseJson.setMessage("成功添加：" + ids.size() + "个周转框！");
        }
        return responseJson;
    }

    @RequestMapping(value = "print", method = { RequestMethod.GET })
    public String print(@ModelAttribute("domain") WhBoxDo domain, @RequestParam("ids") List<Integer> whBoxIds,
            @RequestParam(value = "remark", required = false) String remark) {
        WhBoxQueryCondition query = new WhBoxQueryCondition();
        query.setWhBoxIds(whBoxIds);
        List<WhBox> whBoxs = whBoxService.queryWhBoxs(query, null);
        domain.setWhBoxs(whBoxs);
        domain.setTitle(remark);
        return "warehouse/boxPrint";
    }

    @RequestMapping(value = "unbind", method = { RequestMethod.GET })
    @ResponseBody
    public ResponseJson unbind(@RequestParam("ids") Integer id) {
        ResponseJson responseJson = new ResponseJson();
        responseJson.setStatus(StatusCode.SUCCESS);
        // 解锁
        int i = whBoxService.updateWhBoxOfUnbindingForPage(id, new String[][] { { "周转框解绑！" } });
        if (i == 0) {
            responseJson.setStatus(StatusCode.FAIL);
        }
        return responseJson;
    }

    @RequestMapping(value = "batchUnbind", method = { RequestMethod.GET })
    @ResponseBody
    public ResponseJson batchUnbind(@RequestParam("ids") List<Integer> ids) {
        ResponseJson responseJson = new ResponseJson();
        responseJson.setStatus(StatusCode.SUCCESS);
        // 解锁
        whBoxService.batchUpdateWhBoxOfUnbindingForPage(ids, new String[][] { { "周转框解绑！" } });

        return responseJson;
    }

    /**
     * 查询是否可用
     * 
     * @Description: 周转框是否可用
     *
     * @param boxNo
     * @Author: Administrator
     * @Date: 2018/10/12
     * @Version: 0.0.1
     */
    @RequestMapping(value = "boxNo", method = { RequestMethod.GET })
    @ResponseBody
    public ResponseJson boxNo(@RequestParam("boxNo") String boxNo, @RequestParam("type") String type) {
        ResponseJson responseJson = new ResponseJson();
        responseJson.setStatus(StatusCode.FAIL);
        WhBox whBox = whBoxService.queryWhBoxByBoxNo(boxNo);
        if (whBox != null && whBox.getStatus().equals(BoxStatus.NOT_USED.intCode())
                && whBox.getType().toString().equals(type)) {
            responseJson.setStatus(StatusCode.SUCCESS);
        }
        return responseJson;
    }

    @RequestMapping(value = "success", method = { RequestMethod.GET })
    @ResponseBody
    public void success(@ModelAttribute("domain") WhBoxDo domain) {
        WhBoxQueryCondition query = domain.getQuery();
        whBoxService.updateWhBoxPrint(query.getWhBoxIds());
    }

    @RequestMapping(value = "update", method = { RequestMethod.GET })
    public String toUpdateWhBox(@ModelAttribute("domain") WhBoxDo domain, @RequestParam("whBoxId") Integer whBoxId) {
        WhBox whBox = whBoxService.getWhBox(whBoxId);
        domain.setWhBox(whBox);
        return "{模块}/{页面}";
    }

    @RequestMapping(value = "update", method = { RequestMethod.POST })
    public String updateWhBox(@ModelAttribute("domain") WhBoxDo domain, HttpSession session) {
        WhBox whBox = domain.getWhBox();
        whBoxService.updateWhBox(whBox);
        return "redirect:/{查询列表}";
    }

    @RequestMapping(value = "delete", method = { RequestMethod.GET })
    @ResponseBody
    public ResponseJson deleteWhBox(@ModelAttribute("domain") WhBoxDo domain,
            @RequestParam("whBoxId") Integer whBoxId) {
        ResponseJson response = new ResponseJson();
        whBoxService.deleteWhBox(whBoxId);
        return response;
    }

    @RequestMapping(value = "binding", method = { RequestMethod.POST })
    @ResponseBody
    public ResponseJson bindingWhBox(@ModelAttribute("domain") WhBoxDo domain) {
        ResponseJson responseJson = new ResponseJson();
        responseJson.setStatus(StatusCode.FAIL);

        int i = whBoxService.updateWhBoxOfBinding(domain.getQuery().getBoxNo(), domain.getQuery().getRelationNo(),
                new String[][] { { "绑定拣货任务号", domain.getQuery().getRelationNo() } });

        if (i > 0) {
            responseJson.setStatus(StatusCode.SUCCESS);
        }

        return responseJson;
    }

    @RequestMapping(value = "bindingPre/{boxNo}", method = { RequestMethod.GET })
    @ResponseBody
    public ResponseJson bindingPre(@PathVariable String boxNo){
        ResponseJson responseJson = new ResponseJson(StatusCode.FAIL);
        WhBox whBox = whBoxService.queryWhBoxByBoxNo(boxNo);
        if(whBox != null && BoxStatus.NOT_USED.intCode().equals(whBox.getStatus())){
            // 只将状态改为已使用作预占用，relation_no等后续实际绑定时再赋值
            whBox.setStatus(BoxStatus.ALREADY_USED.intCode());
            whBoxService.updateWhBox(whBox);
            SystemLogUtils.BOXLOG.log(whBox.getId(), "绑定周转框", new String[][] { { "周转筐已被预占用"}});
            responseJson.setStatus(StatusCode.SUCCESS);
        }
        return responseJson;
    }

    @RequestMapping(value = "batchUnbindPage", method = { RequestMethod.GET })
    public String createWhReturn(@ModelAttribute("domain") WhBoxDo domain) {
        return "warehouse/boxBatchUnbind";
    }

    @RequestMapping(value = "boxDetail", method = { RequestMethod.POST })
    @ResponseBody
    public ResponseJson getBoxDetail(@RequestParam("boxNo") String boxNo) {
        ResponseJson responseJson = new ResponseJson();
        responseJson.setStatus(StatusCode.SUCCESS);
        WhBoxQueryCondition query = new WhBoxQueryCondition();
        query.setBoxNo(boxNo);
        WhBox whBox = whBoxService.queryWhBox(query);
        if (null == whBox) {
            responseJson.setStatus(StatusCode.FAIL);
            responseJson.setMessage("当前周转框不存在!");
            return responseJson;
        }
        if (BoxType.getShBoxIntCode().contains(whBox.getType())) {
            responseJson.setStatus(StatusCode.FAIL);
            responseJson.setMessage("不支持收货周转框!");
            return responseJson;
        }
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("whBox", whBox);
        map.put("boxType", BoxType.getNameByCode(whBox.getType().toString()));
        String presentUser = "";
        if (null != whBox.getPresentUser()) {
            SaleUser cacheBuyer = CacheUtils.get(CacheName.USER_CACHE, String.valueOf(whBox.getPresentUser()),
                    SaleUser.class);
            presentUser = cacheBuyer.getUsername() + " - " + cacheBuyer.getName();
        }
        map.put("presentUser", presentUser);
        responseJson.setBody(map);
        return responseJson;
    }
}