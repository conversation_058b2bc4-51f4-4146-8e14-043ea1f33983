package com.estone.warehouse.action;

import com.whq.tool.action.BaseController;
import com.estone.warehouse.bean.WhSkuSaleQuantityInfo;
import com.estone.warehouse.bean.WhSkuSaleQuantityInfoQueryCondition;
import com.estone.warehouse.domain.WhSkuSaleQuantityInfoDo;
import com.estone.warehouse.service.WhSkuSaleQuantityInfoService;
import com.whq.tool.component.Pager;
import java.util.List;
import javax.annotation.Resource;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping(value = "sale/skuSaleQuantityInfos")
public class WhSkuSaleQuantityInfoController extends BaseController {
    @Resource
    private WhSkuSaleQuantityInfoService whSkuSaleQuantityInfoService;

    @RequestMapping(method = {RequestMethod.GET})
    public String init(@ModelAttribute("domain") WhSkuSaleQuantityInfoDo domain) {
        return "{模块}/{页面}";
    }

    private void initFormData(@ModelAttribute("domain") WhSkuSaleQuantityInfoDo domain) {
         
    }

    private void queryWhSkuSaleQuantityInfos(@ModelAttribute("domain") WhSkuSaleQuantityInfoDo domain) {
        WhSkuSaleQuantityInfoQueryCondition query = domain.getQuery();
        Pager page = domain.getPage();
        if (query == null)
        {
            query = new WhSkuSaleQuantityInfoQueryCondition();
            domain.setQuery(query);
        }
        List<WhSkuSaleQuantityInfo> whSkuSaleQuantityInfos = whSkuSaleQuantityInfoService.queryWhSkuSaleQuantityInfos(query, page);
        domain.setWhSkuSaleQuantityInfos(whSkuSaleQuantityInfos);
    }

    @RequestMapping(value="search", method = {RequestMethod.POST})
    public String search(@ModelAttribute("domain") WhSkuSaleQuantityInfoDo domain) {
        initFormData(domain);
        queryWhSkuSaleQuantityInfos(domain);
        return "{模块}/{页面}";
    }
}