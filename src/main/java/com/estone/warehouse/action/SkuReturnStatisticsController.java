package com.estone.warehouse.action;

import com.estone.allocation.bean.WhAllocationDemandAuto;
import com.estone.allocation.bean.WhAllocationDemandAutoQueryCondition;
import com.estone.common.util.POIUtils;
import com.estone.common.util.model.ApiResult;
import com.estone.common.util.model.CQuery;
import com.estone.common.util.model.CQueryResult;
import com.estone.statistics.bean.AllStatistics;
import com.estone.system.downloadcenter.enums.WhDownloadContentEnum;
import com.estone.system.downloadcenter.service.WhDownloadCenterService;
import com.estone.warehouse.bean.SkuReturnStatistics;
import com.estone.warehouse.bean.SkuReturnStatisticsQueryCondition;
import com.estone.warehouse.service.SkuReturnStatisticsService;
import com.estone.warehouse.service.impl.SkuReturnStatisticsServiceImpl;
import com.whq.tool.action.BaseController;
import com.whq.tool.component.Pager;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;

@Slf4j
@RestController
@RequestMapping(value = "skuReturnStatistics")
public class SkuReturnStatisticsController extends BaseController {
    @Resource
    private SkuReturnStatisticsService skuReturnStatisticsService;

    @Resource
    private WhDownloadCenterService whDownloadCenterService;

    @PostMapping(value = "/list")
    @ResponseBody
    public ApiResult<?> list(@RequestBody CQuery<SkuReturnStatisticsQueryCondition> query){
        CQueryResult<SkuReturnStatistics> result = null;
        try {
            if (query == null) return ApiResult.newError("param error");
            SkuReturnStatisticsQueryCondition search = query.getSearch();
            if (search == null) {
                search = new SkuReturnStatisticsQueryCondition();
                query.setSearch(search);
            }
            result = ((SkuReturnStatisticsServiceImpl) skuReturnStatisticsService).list(query);
        } catch (Exception e) {
            log.error(e.getMessage(),e);
            return ApiResult.newError(e.getMessage());
        }
        return ApiResult.newSuccess(result);
    }


    String[] HEADERS = { "平台", "发货单号", "SKU", "SKU标签", "标记退仓时间"};

    @RequestMapping(value = "download", method = { RequestMethod.POST })
    @ResponseBody
    public ApiResult<?> download(@RequestBody SkuReturnStatisticsQueryCondition query) {
        if (query.getId() != null) {
            query = new SkuReturnStatisticsQueryCondition();
            query.setId(query.getId());
        }
        query.setReadOnly(true);
        String fileName = WhDownloadContentEnum.SKU_LABEL_RETURN_STATISTICS_DETAIL.getName()+"导出" + System.currentTimeMillis() + ".xlsx";
        query.setReadOnly(true);
        boolean isAll = whDownloadCenterService.checkFieldAllNull(query);
        Pager pager = new Pager();
        pager.setPageNo(-1);
        SkuReturnStatisticsQueryCondition finalQuery = query;
        whDownloadCenterService.downloading(fileName, HEADERS, WhDownloadContentEnum.SKU_LABEL_RETURN_STATISTICS_DETAIL, isAll, pager,
                (page) -> {
                    List<SkuReturnStatistics> skuReturnStatistics = skuReturnStatisticsService.querySkuReturnStatisticss(finalQuery, page);
                    return this.getExportList(skuReturnStatistics);

                });
        return ApiResult.newSuccess("导出任务已经创建，请到下载中心查看结果");
    }
    private List<List<String>> getExportList( List<SkuReturnStatistics> skuReturnStatistics) {
        List<List<String>> data = new ArrayList<>();
        if (CollectionUtils.isEmpty(skuReturnStatistics)) {
            return data;
        }
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        for (SkuReturnStatistics returnStatistics : skuReturnStatistics) {
            List<String> batchReturnList = new ArrayList<>(HEADERS.length);
            batchReturnList.add(POIUtils.transferObj2Str(returnStatistics.getPlatformName()));
            batchReturnList.add(POIUtils.transferObj2Str(returnStatistics.getApvNo()));
            batchReturnList.add(POIUtils.transferObj2Str(returnStatistics.getSku()));
            batchReturnList.add(POIUtils.transferObj2Str(returnStatistics.getSkuTag()));
            batchReturnList.add(POIUtils.transferObj2Str(returnStatistics.getReturnTime()==null?"":sdf.format(returnStatistics.getReturnTime())));
            data.add(batchReturnList);
        }
        return data;
    }




}