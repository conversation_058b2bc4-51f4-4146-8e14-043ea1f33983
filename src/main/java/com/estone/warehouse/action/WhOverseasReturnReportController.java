package com.estone.warehouse.action;

import com.estone.common.util.DateUtils;
import com.estone.common.util.POIUtils;
import com.estone.common.util.model.ApiResult;
import com.estone.system.downloadcenter.enums.WhDownloadContentEnum;
import com.estone.system.downloadcenter.service.WhDownloadCenterService;
import com.estone.warehouse.bean.WhOverseasReturnReport;
import com.estone.warehouse.bean.WhOverseasReturnReportQueryCondition;
import com.estone.warehouse.bean.WhPlatformOrderCount;
import com.estone.warehouse.domain.WhOverseasReturnReportDo;
import com.estone.warehouse.service.WhOverseasReturnReportService;
import com.whq.tool.action.BaseController;
import com.whq.tool.component.Pager;
import com.whq.tool.json.ResponseJson;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

@Controller
@RequestMapping(value = "return/report")
public class WhOverseasReturnReportController extends BaseController {
    @Resource
    private WhOverseasReturnReportService whOverseasReturnReportService;

    @Resource
    private WhDownloadCenterService whDownloadCenterService;

    @RequestMapping(method = {RequestMethod.GET})
    public String init(@ModelAttribute("domain") WhOverseasReturnReportDo domain) {
        return "{模块}/{页面}";
    }


    @RequestMapping(value="search", method = {RequestMethod.POST})
    @ResponseBody
    public ApiResult search(@RequestBody WhOverseasReturnReportQueryCondition query) {
        ApiResult apiResult=new ApiResult();
        apiResult.setSuccess(false);
        if (query == null) {
            apiResult.setErrorMsg("参数异常!");
            return apiResult;
        }
        String startTime0 = query.getStartStatisticsDate();
        String endTime0 = query.getEndStatisticsDate();

        if(query.getDateType() == null || (StringUtils.isBlank(startTime0) && StringUtils.isBlank(endTime0))){
            apiResult.setErrorMsg("时间维度或时间范围为空!");
            return apiResult;
        }
        if (query.getQueryType()==null){
            apiResult.setErrorMsg("查询类型为空!");
            return apiResult;
        }
        query.setReadOnly(true);
        apiResult.setSuccess(true);
        if (query.getQueryType()==1){
            List<WhOverseasReturnReport> whOverseasReturnReportList=whOverseasReturnReportService.queryOverseasReturnReportList(query);

            Map<String, List<WhOverseasReturnReport>> dateStrMap = whOverseasReturnReportList.stream().collect(Collectors.groupingBy(w -> w.getDateStr()));

            Map<String, List<WhOverseasReturnReport>> reportMap = whOverseasReturnReportList.stream().collect(Collectors.groupingBy(w -> w.getSaleAttribute()));

            reportMap=new LinkedHashMap<>(reportMap);

            List<WhOverseasReturnReport> overseasReturnReportList=new ArrayList<>();
            dateStrMap.forEach((dateStr,whOverseasReturnReports)->{
                WhOverseasReturnReport whOverseasReturnReport=new WhOverseasReturnReport();
                whOverseasReturnReport.setDateStr(dateStr);
                whOverseasReturnReport.setSaleAttribute("汇总");
                whOverseasReturnReport.setSkuQuantitySum(whOverseasReturnReports.stream().mapToLong(a -> Optional.ofNullable(a.getSkuQuantitySum()).orElse(0L)).sum());
                whOverseasReturnReport.setPcsQuantitySum(whOverseasReturnReports.stream().mapToLong(a -> Optional.ofNullable(a.getPcsQuantitySum()).orElse(0L)).sum());
                whOverseasReturnReport.setStatisticsDate(whOverseasReturnReports.get(0).getStatisticsDate());
                List<String> saleAttributeList = Arrays.asList("滞销", "短呆滞", "长呆滞");
                long unsalablePcsSum = whOverseasReturnReports.stream().filter(r -> saleAttributeList.contains(r.getSaleAttribute())).mapToLong(a -> Optional.ofNullable(a.getPcsQuantitySum()).orElse(0L)).sum();
                whOverseasReturnReport.setUnsalablePcsSum(unsalablePcsSum);
                if (whOverseasReturnReport.getPcsQuantitySum()==0 || unsalablePcsSum==0) {
                    overseasReturnReportList.add(whOverseasReturnReport);
                    return;
                }
                String unsalablePcsDuty = String.format("%.2f",(double) unsalablePcsSum / whOverseasReturnReport.getPcsQuantitySum() * 100);
                whOverseasReturnReport.setUnsalablePcsDuty(unsalablePcsDuty+'%');
                overseasReturnReportList.add(whOverseasReturnReport);
            });
            reportMap.put("汇总", overseasReturnReportList.stream().sorted(Comparator.comparing(WhOverseasReturnReport::getStatisticsDate)).collect(Collectors.toList()));
            apiResult.setResult(reportMap);
        }
        if (query.getQueryType()==2){
            List<WhPlatformOrderCount> whPlatformOrderCounts=whOverseasReturnReportService.queryPlatformOrderCountList(query);
            apiResult.setResult(whPlatformOrderCounts);
        }

        return apiResult;
    }

    @RequestMapping(value="syncOverseasReturnSku", method = {RequestMethod.POST})
    @ResponseBody
    public ApiResult syncOverseasReturnSku(@RequestBody WhOverseasReturnReportQueryCondition query) {
        ApiResult apiResult=new ApiResult();
        apiResult.setSuccess(false);

        if (StringUtils.isBlank(query.getStartStatisticsDate()) ||  StringUtils.isBlank(query.getEndStatisticsDate())) {
            apiResult.setErrorMsg("请输入需要统计的时间段");
            return apiResult;
        }
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        List<LocalDate> localDates = DateUtils.splitDateRange(query.getStartStatisticsDate(), query.getEndStatisticsDate(), 1,1);
        for (LocalDate localDate : localDates) {
            LocalDateTime startOfDay = LocalDateTime.of(localDate, LocalTime.MIN);
            LocalDateTime endOfDay = LocalDateTime.of(localDate, LocalTime.MAX);
            whOverseasReturnReportService.statisticsOverseasReturn(startOfDay.format(formatter),endOfDay.format(formatter));
        }
        apiResult.setSuccess(true);
        return apiResult;

    }


    @RequestMapping(value="create", method = {RequestMethod.GET})
    public String toCreateWhOverseasReturnReport(@ModelAttribute("domain") WhOverseasReturnReportDo domain) {
        return "{模块}/{页面}";
    }

    @RequestMapping(value="create", method = {RequestMethod.POST})
    public String createWhOverseasReturnReport(@ModelAttribute("domain") WhOverseasReturnReportDo domain) {
        WhOverseasReturnReport whOverseasReturnReport = domain.getWhOverseasReturnReport();
        whOverseasReturnReportService.createWhOverseasReturnReport(whOverseasReturnReport);
        return "redirect:/{查询列表}";
    }

    @RequestMapping(value="update", method = {RequestMethod.GET})
    public String toUpdateWhOverseasReturnReport(@ModelAttribute("domain") WhOverseasReturnReportDo domain, @RequestParam("whOverseasReturnReportId") Integer whOverseasReturnReportId) {
        WhOverseasReturnReport whOverseasReturnReport = whOverseasReturnReportService.getWhOverseasReturnReport(whOverseasReturnReportId);
        domain.setWhOverseasReturnReport(whOverseasReturnReport);
        return "{模块}/{页面}";
    }

    @RequestMapping(value="update", method = {RequestMethod.POST})
    public String updateWhOverseasReturnReport(@ModelAttribute("domain") WhOverseasReturnReportDo domain) {
        WhOverseasReturnReport whOverseasReturnReport = domain.getWhOverseasReturnReport();
        whOverseasReturnReportService.updateWhOverseasReturnReport(whOverseasReturnReport);
        return "redirect:/{查询列表}";
    }

    @RequestMapping(value="delete", method = {RequestMethod.GET})
    @ResponseBody
    public ResponseJson deleteWhOverseasReturnReport(@ModelAttribute("domain") WhOverseasReturnReportDo domain, @RequestParam("whOverseasReturnReportId") Integer whOverseasReturnReportId) {
        ResponseJson response = new ResponseJson();
        whOverseasReturnReportService.deleteWhOverseasReturnReport(whOverseasReturnReportId);
        return response;
    }



    private static String[] SKU_HEADERS = { "日期", "销售属性", "SKU", "PCS","滞销类PCS","滞销类PCS占比"};

    private static String[] ORDER_HEADERS = { "日期", "Shopee订单数","Shopee占比", "SMT订单数", "SMT占比","Amazon订单数", "Amazon占比","Ebay订单数", "Ebay占比","Lazada订单数",
            "Lazada占比","Wish订单数", "Wish占比", "JOOM订单数", "JOOM占比","Walmart订单数", "Walmart占比","其他平台订单数", "其他平台占比", "合计订单数","合计占比"};


    /**
     * 导出
     */
    @RequestMapping(value = "download", method = { RequestMethod.POST })
    @ResponseBody
    public ApiResult download(@RequestBody WhOverseasReturnReportQueryCondition query) {
        ApiResult apiResult=new ApiResult();
        if (query == null) {
            apiResult.setErrorMsg("参数异常!");
            return apiResult;
        }
        String startTime0 = query.getStartStatisticsDate();
        String endTime0 = query.getEndStatisticsDate();

        if(query.getDateType() == null || (StringUtils.isBlank(startTime0) && StringUtils.isBlank(endTime0))){
            apiResult.setErrorMsg("时间维度或时间范围为空!");
            return apiResult;
        }
        if (query.getQueryType()==null){
            apiResult.setErrorMsg("查询类型为空!");
            return apiResult;
        }
        query.setReadOnly(true);

        String fileName = "海外退件上架SKU统计报表" + System.currentTimeMillis() + ".xls";

        boolean isAll = whDownloadCenterService.checkFieldAllNull(query);
        Pager pager=new Pager();
        pager.setPageNo(-1);
        WhOverseasReturnReportQueryCondition finalQuery = query;
        if (query.getQueryType()==1){
            whDownloadCenterService.downloading(fileName, SKU_HEADERS, WhDownloadContentEnum.OVERSEAS_RETURN_SKU_COUNT, isAll,pager, (page) -> {
                List<WhOverseasReturnReport> whOverseasReturnReportList=whOverseasReturnReportService.queryOverseasReturnReportList(finalQuery);
                return this.getExportList(whOverseasReturnReportList);
            });
        }

        if (query.getQueryType()==2){
            fileName = "海外退件上架平台订单统计报表" + System.currentTimeMillis() + ".xls";
                whDownloadCenterService.downloading(fileName, ORDER_HEADERS, WhDownloadContentEnum.OVERSEAS_RETURN_ORDER_COUNT, isAll,pager, (page) -> {
                    List<WhPlatformOrderCount> whPlatformOrderCounts=whOverseasReturnReportService.queryPlatformOrderCountList(finalQuery);
                    return this.getOrderExportList(whPlatformOrderCounts);
                });
        }

        apiResult.setSuccess(true);
        apiResult.setResult("导出任务已经创建，请到下载中心查看结果");
        return apiResult;
    }

    private List<List<String>> getExportList(List<WhOverseasReturnReport> whOverseasReturnReports) {
        List<List<String>> data = new ArrayList<List<String>>();
        if (CollectionUtils.isEmpty(whOverseasReturnReports)) {
            return data;
        }
        Map<String, List<WhOverseasReturnReport>> dateStrMap = whOverseasReturnReports.stream().collect(Collectors.groupingBy(w -> w.getDateStr()));

        dateStrMap.forEach((dateStr,whOverseasReturnReportList)->{
            if (CollectionUtils.isEmpty(whOverseasReturnReportList)) {
                return;
            }
            for (WhOverseasReturnReport whOverseasReturnReport : whOverseasReturnReportList) {
                List<String> apvlist = new ArrayList<String>(SKU_HEADERS.length);
                apvlist.add(POIUtils.transferObj2Str(whOverseasReturnReport.getDateStr()));
                apvlist.add(POIUtils.transferObj2Str(whOverseasReturnReport.getSaleAttribute()));
                apvlist.add(POIUtils.transferObj2Str(whOverseasReturnReport.getSkuQuantitySum()));
                apvlist.add(POIUtils.transferObj2Str(whOverseasReturnReport.getPcsQuantitySum()));
                apvlist.add(POIUtils.transferObj2Str(0));
                apvlist.add(POIUtils.transferObj2Str(0));
                data.add(apvlist);
            }
            List<String> apvlist = new ArrayList<String>(SKU_HEADERS.length);
            apvlist.add(POIUtils.transferObj2Str(dateStr));
            apvlist.add(POIUtils.transferObj2Str("汇总"));
            apvlist.add(POIUtils.transferObj2Str(whOverseasReturnReportList.stream().mapToLong(a -> Optional.ofNullable(a.getSkuQuantitySum()).orElse(0L)).sum()));

            long pcsQuantitySum = whOverseasReturnReportList.stream().mapToLong(a -> Optional.ofNullable(a.getPcsQuantitySum()).orElse(0L)).sum();
            apvlist.add(POIUtils.transferObj2Str(pcsQuantitySum));
            List<String> saleAttributeList = Arrays.asList("滞销", "短呆滞", "长呆滞");
            long unsalablePcsSum = whOverseasReturnReportList.stream().filter(r -> saleAttributeList.contains(r.getSaleAttribute())).mapToLong(a -> Optional.ofNullable(a.getPcsQuantitySum()).orElse(0L)).sum();
            apvlist.add(POIUtils.transferObj2Str(unsalablePcsSum));
            if (pcsQuantitySum==0 || unsalablePcsSum==0) {
                apvlist.add(POIUtils.transferObj2Str("0%"));
            }else{
                apvlist.add(POIUtils.transferObj2Str(String.format("%.2f",(double) unsalablePcsSum / pcsQuantitySum * 100))+"%");
            }
            data.add(apvlist);

        });
        return data;
    }

    private List<List<String>> getOrderExportList(List<WhPlatformOrderCount> whPlatformOrderCounts) {
        List<List<String>> data = new ArrayList<List<String>>();
        if (CollectionUtils.isEmpty(whPlatformOrderCounts)) {
            return data;
        }
        for (WhPlatformOrderCount whPlatformOrderCount : whPlatformOrderCounts) {
            List<String> apvlist = new ArrayList<String>(ORDER_HEADERS.length);
            Long orderQuantitySum = whPlatformOrderCount.getOrderQuantitySum();
            apvlist.add(POIUtils.transferObj2Str(whPlatformOrderCount.getDateStr()));
            apvlist.add(POIUtils.transferObj2Str(whPlatformOrderCount.getShopeeQuantitySum()));
            apvlist.add(POIUtils.transferObj2Str(String.format("%.2f",(double) whPlatformOrderCount.getShopeeQuantitySum() /orderQuantitySum)));

            apvlist.add(POIUtils.transferObj2Str(whPlatformOrderCount.getSmtQuantitySum()));
            apvlist.add(POIUtils.transferObj2Str(String.format("%.2f",(double) whPlatformOrderCount.getSmtQuantitySum() /orderQuantitySum)));

            apvlist.add(POIUtils.transferObj2Str(whPlatformOrderCount.getAmazonQuantitySum()));
            apvlist.add(POIUtils.transferObj2Str(String.format("%.2f",(double) whPlatformOrderCount.getAmazonQuantitySum() /orderQuantitySum)));

            apvlist.add(POIUtils.transferObj2Str(whPlatformOrderCount.getEbayQuantitySum()));
            apvlist.add(POIUtils.transferObj2Str(String.format("%.2f",(double) whPlatformOrderCount.getEbayQuantitySum() /orderQuantitySum)));

            apvlist.add(POIUtils.transferObj2Str(whPlatformOrderCount.getLazadaQuantitySum()));
            apvlist.add(POIUtils.transferObj2Str(String.format("%.2f",(double) whPlatformOrderCount.getLazadaQuantitySum() /orderQuantitySum)));

            apvlist.add(POIUtils.transferObj2Str(whPlatformOrderCount.getWishQuantitySum()));
            apvlist.add(POIUtils.transferObj2Str(String.format("%.2f",(double) whPlatformOrderCount.getWishQuantitySum() /orderQuantitySum)));

            apvlist.add(POIUtils.transferObj2Str(whPlatformOrderCount.getJoomQuantitySum()));
            apvlist.add(POIUtils.transferObj2Str(String.format("%.2f",(double) whPlatformOrderCount.getJoomQuantitySum() /orderQuantitySum)));

            apvlist.add(POIUtils.transferObj2Str(whPlatformOrderCount.getWalmartQuantitySum()));
            apvlist.add(POIUtils.transferObj2Str(String.format("%.2f",(double) whPlatformOrderCount.getWalmartQuantitySum() /orderQuantitySum)));

            apvlist.add(POIUtils.transferObj2Str(whPlatformOrderCount.getOtherQuantitySum()));
            apvlist.add(POIUtils.transferObj2Str(String.format("%.2f",(double) whPlatformOrderCount.getOtherQuantitySum() /orderQuantitySum)));

            apvlist.add(POIUtils.transferObj2Str(orderQuantitySum));
            apvlist.add(POIUtils.transferObj2Str(1));

            data.add(apvlist);
        }
        return data;
    }

}