package com.estone.warehouse.action;

import com.estone.common.util.POIUtils;
import com.estone.common.util.model.ApiResult;
import com.estone.common.util.model.CQueryResult;
import com.estone.sku.service.WhSkuExtendService;
import com.estone.system.downloadcenter.enums.WhDownloadContentEnum;
import com.estone.system.downloadcenter.service.WhDownloadCenterService;
import com.estone.warehouse.bean.SkuLabelReturnQueryCondition;
import com.estone.warehouse.bean.SkuLabelReturnVO;
import com.estone.warehouse.bean.WhOrderCancel;
import com.estone.warehouse.bean.WhOrderCancelItemQueryCondition;
import com.whq.tool.action.BaseController;
import com.whq.tool.component.Pager;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Description:  订单取消报表
 */
@Slf4j
@Controller
@RequestMapping(value = "warehouse/skuLabelReturn")
public class SkuLabelReturnController extends BaseController {
    @Resource
    private WhSkuExtendService whSkuExtendService;

    @Resource
    private WhDownloadCenterService whDownloadCenterService;

    @RequestMapping(value = "search", method = { RequestMethod.POST })
    @ResponseBody
    public ApiResult<?> search(@RequestBody SkuLabelReturnQueryCondition query) {
        if (query == null) {
            query=new SkuLabelReturnQueryCondition();
        }
        String startTime0 = query.getStartTime();
        String endTime0 = query.getEndTime();
        if(query.getDateType() == null || (StringUtils.isBlank(startTime0) && StringUtils.isBlank(endTime0))){
            return ApiResult.newError("时间维度或时间范围为空!");
        }
        Pager page = new Pager();
        page.setPageNo(query.getPage());
        page.setPageSize(query.getSize());
        query.setReadOnly(true);
        List<SkuLabelReturnVO> queryResult=whSkuExtendService.querySkuLabelReturnList(query);

        // 计算分页结果
        int totalRecords = (int) queryResult.stream().map(SkuLabelReturnVO::getDateStr).distinct().count();
        int startIndex = (page.getPageNo() - 1) * page.getPageSize()*query.getTagCount();
        int endIndex = Math.min(startIndex + page.getPageSize()*query.getTagCount(), queryResult.size());
        List<SkuLabelReturnVO> pagedResult = queryResult.subList(startIndex, endIndex);
        // 封装分页结果
        CQueryResult<SkuLabelReturnVO> cQueryResult = new CQueryResult<>();
        cQueryResult.setTotal(totalRecords);
        cQueryResult.setRows(pagedResult);
        return ApiResult.newSuccess(cQueryResult);
    }

    private static String[] HEADERS = { "日期", "SKU标签", "SMT", "虾皮", "Amazon","Lazada","Ebay","Wish","Joom","沃尔玛","其他平台","汇总"};
    /**
     * 导出
     */
    @RequestMapping(value = "download", method = { RequestMethod.POST })
    @ResponseBody
    public ApiResult<?> download(@RequestBody SkuLabelReturnQueryCondition query) {
        if(query==null || query.getDateType() == null ||StringUtils.isBlank(query.getStartTime()) || StringUtils.isBlank(query.getEndTime())){
            return ApiResult.newError("参数异常 或 时间维度不能为空！");
        }
        query.setReadOnly(true);
        String fileName = "SKU标签退货统计" + System.currentTimeMillis() + ".xls";

        boolean isAll = whDownloadCenterService.checkFieldAllNull(query);
        Pager pager=new Pager();
        pager.setPageNo(-1);
        SkuLabelReturnQueryCondition finalQuery = query;
        whDownloadCenterService.downloading(fileName, HEADERS, WhDownloadContentEnum.SKU_LABEL_RETURN_STATISTICS, isAll,pager, (page) -> {
            List<SkuLabelReturnVO> whOrderCancels = whSkuExtendService.querySkuLabelReturnList(finalQuery);
            return this.getExportList(whOrderCancels);
        });
        return ApiResult.newSuccess("导出任务已经创建，请到下载中心查看结果");
    }

    private List<List<String>> getExportList(List<SkuLabelReturnVO> returnVOList) {
        List<List<String>> data = new ArrayList<List<String>>();
        if (CollectionUtils.isEmpty(returnVOList)) {
            return data;
        }
        for (SkuLabelReturnVO skuLabelReturnVO : returnVOList) {
            List<String> apvlist = new ArrayList<String>(HEADERS.length);
            apvlist.add(POIUtils.transferObj2Str(skuLabelReturnVO.getDateStr()));
            apvlist.add(POIUtils.transferObj2Str(skuLabelReturnVO.getSkuLabel()));
            apvlist.add(POIUtils.transferObj2Str(skuLabelReturnVO.getSmtQuantity()));
            apvlist.add(POIUtils.transferObj2Str(skuLabelReturnVO.getShopeeQuantity()));
            apvlist.add(POIUtils.transferObj2Str(skuLabelReturnVO.getAmazonQuantity()));
            apvlist.add(POIUtils.transferObj2Str(skuLabelReturnVO.getLazadaQuantity()));
            apvlist.add(POIUtils.transferObj2Str(skuLabelReturnVO.getEbayQuantity()));
            apvlist.add(POIUtils.transferObj2Str(skuLabelReturnVO.getWishQuantity()));
            apvlist.add(POIUtils.transferObj2Str(skuLabelReturnVO.getJoomQuantity()));
            apvlist.add(POIUtils.transferObj2Str(skuLabelReturnVO.getWalmartQuantity()));
            apvlist.add(POIUtils.transferObj2Str(skuLabelReturnVO.getOtherPlatformQuantity()));
            apvlist.add(POIUtils.transferObj2Str(skuLabelReturnVO.getTotalQuantity()));
            data.add(apvlist);
        }
        return data;
    }

}
