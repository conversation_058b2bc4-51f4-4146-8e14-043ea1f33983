package com.estone.warehouse.action;

import java.io.OutputStream;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import com.alibaba.fastjson.JSON;
import com.estone.common.SelectJson;
import com.estone.common.enums.ExportType;
import com.estone.common.util.CacheUtils;
import com.estone.common.util.POIUtils;
import com.estone.common.util.SystemLogUtils;
import com.estone.rule.common.HandleTypeModal;
import com.estone.warehouse.bean.WhExLocation;
import com.estone.warehouse.bean.WhExLocationQueryCondition;
import com.estone.warehouse.domain.WhExLocationDo;
import com.estone.warehouse.enums.LocationStatus;
import com.estone.warehouse.enums.LocationType;
import com.estone.warehouse.service.WhExLocationService;
import com.whq.tool.action.BaseController;
import com.whq.tool.component.Pager;
import com.whq.tool.component.StatusCode;
import com.whq.tool.json.ResponseJson;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Controller
@RequestMapping(value = "whExLocation")
public class WhExLocationController extends BaseController {
    @Resource
    private WhExLocationService whExLocationService;

    @RequestMapping(method = {RequestMethod.GET})
    public String init(@ModelAttribute("domain") WhExLocationDo domain) {
        initFormData(domain);
//        queryWhExLocations(domain);
        return "warehouse/exLocationList";
    }

    private void initFormData(@ModelAttribute("domain") WhExLocationDo domain) {
        domain.setLocationWarehouseType(CacheUtils.getLocalWarehouseId());// 当前仓库
        domain.setLocationStatusList(LocationStatus.values());// 库位状态
        Map<String, List<String>> listMap = whExLocationService.queryRegionAndAisle();
        // 库位区域
        domain.setLocationRegionList(listMap.get("region"));
        List<String> aisleList = listMap.get("aisle");
        // 库位通道
        if (CollectionUtils.isNotEmpty(aisleList)) {
            List<SelectJson> sjs = new ArrayList<>();
            aisleList.forEach(item -> sjs.add(new SelectJson(item, item)));
            domain.setLocationAisleSelectJson(JSON.toJSONString(sjs));
        } else {
            domain.setLocationAisleSelectJson("[]");
        }
    }

    private void queryWhExLocations(@ModelAttribute("domain") WhExLocationDo domain) {
        WhExLocationQueryCondition query = domain.getQuery();
        Pager page = domain.getPage();
        if (query == null)
        {
            query = new WhExLocationQueryCondition();
            domain.setQuery(query);
        }
        List<WhExLocation> whExLocations = whExLocationService.queryWhExLocations(query, page);
        domain.setWhExLocations(whExLocations);
    }

    @RequestMapping(value="search", method = {RequestMethod.POST})
    public String search(@ModelAttribute("domain") WhExLocationDo domain) {
        initFormData(domain);
        queryWhExLocations(domain);
        return "warehouse/exLocationList";
    }

    @RequestMapping(value = "editLocation", method = { RequestMethod.POST })
    @ResponseBody
    public ResponseJson createWhExLocation(@ModelAttribute("domain") WhExLocationDo domain,
            @RequestParam(value = "type", required = false) String type) {
        ResponseJson response = new ResponseJson();

        try {

            WhExLocation whExLocation = domain.getWhExLocation();
            response = whExLocationService.doEditLocation(whExLocation, type);
        }
        catch (Exception e) {
            String str = StringUtils.equals(HandleTypeModal.ADD, type) ? "添加" : "编辑";
            response.setStatus(StatusCode.FAIL);
            response.setMessage("库位" + str + "失败。请联系管理员！");
        }
        return response;
    }

    @RequestMapping(value = "batchModifyLocation", method = { RequestMethod.POST })
    @ResponseBody
    public ResponseJson batchModifyLocation(@RequestParam(value = "locationIds") List<Integer> locationIds,
            @RequestParam(value = "type") Integer type) {
        ResponseJson response = new ResponseJson();
        response.setStatus(StatusCode.FAIL);

        if (LocationStatus.STARTUP.intCode().equals(type)) {// 开启
            List<WhExLocation> entityList = new ArrayList<WhExLocation>();
            for (Integer locationId : locationIds) {
                WhExLocation entity = new WhExLocation();
                entity.setId(locationId);
                entity.setLocationStatus(LocationStatus.STARTUP.intCode());
                entityList.add(entity);
                SystemLogUtils.WH_EX_LOCATION.log(locationId, "开启库位");
            }
            whExLocationService.batchUpdateWhExLocation(entityList);
        }
        else if (LocationStatus.CLOSEUP.intCode().equals(type)) {// 关闭
            WhExLocationQueryCondition query = new WhExLocationQueryCondition();
            query.setLocationIds(locationIds);
            query.setCategoryActual(0);
            List<WhExLocation> entityList = whExLocationService.queryWhExLocations(query, null);
            if (CollectionUtils.isEmpty(entityList)) {
                response.setMessage("请先将库位内SKU移至其他库位再执行关闭操作");
                return response;
            }
            entityList.forEach(entity -> {
                entity.setLocationStatus(LocationStatus.CLOSEUP.intCode());
                locationIds.remove(entity.getId());
                SystemLogUtils.WH_EX_LOCATION.log(entity.getId(), "关闭库位");
            });
            whExLocationService.batchUpdateWhExLocation(entityList);

            if (CollectionUtils.isNotEmpty(locationIds)) {
                query.setLocationIds(locationIds);
                query.setCategoryActual(null);
                List<WhExLocation> existList = whExLocationService.queryWhExLocations(query, null);
                List<String> locationList = Optional.ofNullable(existList).orElse(new ArrayList<>()).stream()
                        .map(WhExLocation::getLocation).collect(Collectors.toList());
                response.setMessage("部分关闭成功，关闭失败的库位：" + JSON.toJSONString(locationList) + "，请将关闭失败的库位品类移库后在操作");
                response.setStatus(StatusCode.SUCCESS);
                return response;
            }
        }
        else if (3 == type) {// 删除
            WhExLocationQueryCondition query = new WhExLocationQueryCondition();
            query.setLocationIds(locationIds);
            query.setLocationStatus(LocationStatus.CLOSEUP.intCode());
            List<WhExLocation> entityList = whExLocationService.queryWhExLocations(query, null);
            if (CollectionUtils.isEmpty(entityList)) {
                response.setMessage("勾选的库位不为关闭状态，请重新选择");
                return response;
            }

            entityList.forEach(entity -> {
                whExLocationService.deleteWhExLocation(entity.getId());
                locationIds.remove(entity.getId());
            });

            if (CollectionUtils.isNotEmpty(locationIds)) {
                query.setLocationIds(locationIds);
                query.setLocationStatus(null);
                List<WhExLocation> existList = whExLocationService.queryWhExLocations(query, null);
                List<String> locationList = Optional.ofNullable(existList).orElse(new ArrayList<>()).stream()
                        .map(WhExLocation::getLocation).collect(Collectors.toList());
                response.setMessage("部分删除成功，删除失败的库位：" + JSON.toJSONString(locationList) + "，请检查删除失败的库位是否已关闭");
                response.setStatus(StatusCode.SUCCESS);
                return response;
            }
        }
        else {
            response.setMessage("操作类型错误");
            return response;
        }

        response.setStatus(StatusCode.SUCCESS);
        response.setMessage("操作成功");
        return response;
    }


    String[] headers = { "区域","货架","库位","品类上限","实际品类","长(cm)","宽(cm)","高(cm)","体积(m³)","库位类型","状态" };

    /**
     * 导出
     *
     * @param domain
     * @param exportType
     * @param response
     */
    @SuppressWarnings("incomplete-switch")
    @RequestMapping(value = "download", method = { RequestMethod.POST })
    @ResponseBody
    public void download(@ModelAttribute("domain") WhExLocationDo domain, @RequestParam("exportType") String exportType,
            HttpServletResponse response) {

        WhExLocationQueryCondition query = domain.getQuery();

        List<WhExLocation> whExLocations = null;

        ExportType exportTypeEnum = ExportType.build(exportType);

        if (query == null) {
            query = new WhExLocationQueryCondition();
        }

        // 查询导出的数据
        switch (exportTypeEnum) {
            case ALL: {
                whExLocations = whExLocationService.queryWhExLocations(query, null);
                break;
            }
            case PAGE: {
                whExLocations = whExLocationService.queryWhExLocations(query, domain.getPage());
                break;
            }
            case CHECKED: {
                if (StringUtils.isNotEmpty(query.getLocationIdStr())) {
                    List<Integer> locationIds = new ArrayList<Integer>();
                    String[] split = query.getLocationIdStr().split(",");
                    for (String str : split) {
                        locationIds.add(Integer.valueOf(str));
                    }
                    if (CollectionUtils.isNotEmpty(locationIds)) {
                        query = new WhExLocationQueryCondition();
                        query.setLocationIds(locationIds);
                        whExLocations = whExLocationService.queryWhExLocations(query, null);
                    }
                }
                break;
            }
        }

        OutputStream os = null;
        try {
            String fileName = "入库异常库位列表" + System.currentTimeMillis() + ".xls";
            response.setContentType("application/ms-excel");
            response.setHeader("Content-Disposition",
                    "attachment; filename=" + java.net.URLEncoder.encode(fileName, "UTF-8"));
            os = response.getOutputStream();
            log.warn("download file name: " + fileName);
            final List<List<String>> locationData = new ArrayList<List<String>>();
            POIUtils.createExcel(headers, whExLocations, item -> {

                locationData.clear();

                List<String> lists = new ArrayList<String>(headers.length);

                lists.add(POIUtils.transferObj2Str(item.getLocationRegion()));

                lists.add(POIUtils.transferObj2Str(item.getLocationAisle()));

                lists.add(POIUtils.transferObj2Str(item.getLocation()));

                lists.add(POIUtils.transferObj2Str(item.getCategoryLimit()));

                lists.add(POIUtils.transferObj2Str(item.getCategoryActual()));

                lists.add(POIUtils.transferObj2Str(item.getLocationLength()));

                lists.add(POIUtils.transferObj2Str(item.getLocationWidth()));

                lists.add(POIUtils.transferObj2Str(item.getLocationHeight()));

                lists.add(POIUtils.transferObj2Str(item.getLocationVolume()));

                lists.add(LocationType.getNameByCode(POIUtils.transferObj2Str(item.getLocationType())));

                lists.add(LocationStatus.getNameByCode(POIUtils.transferObj2Str(item.getLocationStatus())));

                locationData.add(lists);

                return locationData;

            }, true, os);
            log.warn("---task execute end---");
        }
        catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        finally {
            IOUtils.closeQuietly(os);
        }
    }
}