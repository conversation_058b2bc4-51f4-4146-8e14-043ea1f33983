package com.estone.android.action;

import com.alibaba.fastjson.JSON;
import com.estone.android.domain.AndroidProductDo;
import com.estone.common.util.SystemLogUtils;
import com.estone.picking.bean.LocationCheckTask;
import com.estone.picking.bean.LocationCheckTaskItem;
import com.estone.picking.bean.LocationCheckTaskQueryCondition;
import com.estone.picking.enums.LocationCheckStatus;
import com.estone.picking.service.LocationCheckTaskItemService;
import com.estone.picking.service.LocationCheckTaskService;
import com.whq.tool.component.StatusCode;
import com.whq.tool.json.ResponseJson;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @create 2023/8/16 15:37
 */
@Slf4j
@RestController
@RequestMapping(value = "android/locationCheck")
public class AndroidLocationCheckController {

    @Resource
    private LocationCheckTaskService locationCheckTaskService;

    @Resource
    private LocationCheckTaskItemService locationCheckTaskItemService;

    @PostMapping(value = "receive")
    public ResponseJson receiveTask(@ModelAttribute("domain") AndroidProductDo domain) {

        ResponseJson responseJson = new ResponseJson();
        responseJson.setStatus(StatusCode.FAIL);

        if (domain.getReceivePerson() == null || domain.getReceivePerson() == 0) {
            responseJson.setMessage("参数receivePerson为空或者为0！");
            return responseJson;
        }

        LocationCheckTaskQueryCondition query = new LocationCheckTaskQueryCondition();
        // 先领取核对中的任务，如果没有再领取待领取状态的任务，如果没有再随机从待领取的任务中领取一个
        query.setTaskStatus(LocationCheckStatus.PICKING.intCode());
        query.setReceiveBy(domain.getReceivePerson());
        List<LocationCheckTask> taskList = locationCheckTaskService.queryLocationCheckTasks(query, null);
        if (CollectionUtils.isNotEmpty(taskList)) {
            LocationCheckTask checkTask = taskList.get(0);
            locationCheckTaskService.sortByLocation(checkTask);
            responseJson.setMessage(JSON.toJSONString(checkTask));
            responseJson.setStatus(StatusCode.SUCCESS);
            return responseJson;
        }
        query.setReceiveBy(domain.getReceivePerson());
        query.setTaskStatus(LocationCheckStatus.UNRECEIVED.intCode());
        taskList = locationCheckTaskService.queryLocationCheckTasks(query, null);
        if (CollectionUtils.isEmpty(taskList)){
            query.setReceiveBy(null);
            query.setReceiverIsNull(true);
            query.setTaskStatus(LocationCheckStatus.UNRECEIVED.intCode());
            taskList = locationCheckTaskService.queryLocationCheckTasks(query, null);
        }
        if (taskList.size() > 0) {
            try {
                LocationCheckTask checkTask = taskList.get(0);
                LocationCheckTask updateTask = new LocationCheckTask();
                updateTask.setId(checkTask.getId());
                updateTask.setReceiveBy(domain.getReceivePerson());
                updateTask.setReceiveDate(new Timestamp(System.currentTimeMillis()));
                updateTask.setTaskStatus(LocationCheckStatus.PICKING.intCode());
                locationCheckTaskService.updateLocationCheckTask(updateTask);
                SystemLogUtils.LOCATION_CHECK_TASK.log(checkTask.getId(),
                        "领取任务-修改状态为" + LocationCheckStatus.getNameByCode(String.valueOf(updateTask.getTaskStatus())),
                        new String[][] {{ "原状态", LocationCheckStatus.getNameByCode(String.valueOf(checkTask.getTaskStatus()))}});
                locationCheckTaskService.sortByLocation(checkTask);
                responseJson.setMessage(JSON.toJSONString(checkTask));
                responseJson.setStatus(StatusCode.SUCCESS);
                return responseJson;
            } catch (Exception e) {
                log.error(e.getMessage(), e);
                responseJson.setMessage("领取任务失败，请重新领取！" + e.getMessage());
            }
        } else {
            responseJson.setMessage("目前可领取的任务列表数为0");
        }
        return responseJson;
    }

    /**
     * 扫描库位
     *
     * @param domain
     * @return
     */
    @PostMapping(value = "scanLocationNumber")
    public ResponseJson scanExpLocationNumber(@ModelAttribute("domain") AndroidProductDo domain) {
        ResponseJson responseJson = new ResponseJson();
        responseJson.setStatus(StatusCode.FAIL);
        if (domain.getTaskId() == null || domain.getTaskId() == 0) {
            responseJson.setMessage("参数taskId为空或者为0！");
            return responseJson;
        }
        if (StringUtils.isBlank(domain.getLocation())) {
            responseJson.setMessage("参数库位为空！");
            return responseJson;
        }
        log.info("android scanLocationNumber taskNo=" + domain.getTaskNo() + ";库位=" + domain.getLocation());

        LocationCheckTaskQueryCondition query = new LocationCheckTaskQueryCondition();
        query.setId(domain.getTaskId());
        List<LocationCheckTask> taskList = locationCheckTaskService.queryLocationCheckTasks(query, null);
        if (CollectionUtils.isEmpty(taskList) || CollectionUtils.isEmpty(taskList.get(0).getItemList())) {
            responseJson.setMessage(String.format("任务号%s,查无此单!", domain.getTaskNo()));
            return responseJson;
        }
        LocationCheckTask checkTask = taskList.get(0);

        if (LocationCheckStatus.COMPLETED.intCode().equals(checkTask.getTaskStatus())
                || LocationCheckStatus.EXCEPTION_COMPLETE.intCode().equals(checkTask.getTaskStatus())) {
            responseJson.setMessage(String.format("拣货任务号%s,已完成!", domain.getTaskNo()));
            return responseJson;
        }
        if (LocationCheckStatus.DISCARDED.intCode().equals(checkTask.getTaskStatus())) {
            responseJson.setMessage(String.format("任务号%s,已废除!", domain.getTaskNo()));
            return responseJson;
        }

        List<LocationCheckTaskItem> locationItems = checkTask.getItemList().stream()
                .filter(i -> i.getLocationNumber().equals(domain.getLocation())).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(locationItems)) {
            responseJson.setMessage(String.format("当前库位%s,不属于：%s核对单号!", domain.getLocation(), checkTask.getTaskNo()));
            return responseJson;
        }
        responseJson.setStatus(StatusCode.SUCCESS);
        responseJson.setMessage(JSON.toJSONString(locationItems));
        return responseJson;
    }

    @PostMapping(value = "scanSku")
    public ResponseJson scanSku(@ModelAttribute("domain") AndroidProductDo domain) {
        ResponseJson responseJson = new ResponseJson(StatusCode.FAIL);
        if (domain.getTaskId() == null || domain.getTaskId() == 0) {
            responseJson.setMessage("参数taskId为空或者为0！");
            return responseJson;
        }
        if (StringUtils.isBlank(domain.getLocation())) {
            responseJson.setMessage("参数库位为空！");
            return responseJson;
        }
        if (StringUtils.isBlank(domain.getSku())) {
            responseJson.setMessage("参数sku为空！");
            return responseJson;
        }
        try {
            return locationCheckTaskItemService.checkLocationSku(domain.getTaskId(), domain.getLocation(),
                    domain.getSku());
        }
        catch (Exception e) {
            log.error("scanSku error" + e.getMessage(), e);
            responseJson.setMessage("扫描sku失败");
            return responseJson;
        }
    }

    @PostMapping(value = "commitLocation")
    public ResponseJson commitLocation(@ModelAttribute("domain") AndroidProductDo domain) {
        ResponseJson responseJson = new ResponseJson(StatusCode.FAIL);
        if (domain.getTaskId() == null || domain.getTaskId() == 0) {
            responseJson.setMessage("参数taskId为空或者为0！");
            return responseJson;
        }
        if (StringUtils.isBlank(domain.getLocation())) {
            responseJson.setMessage("参数库位为空！");
            return responseJson;
        }
        try {
            return locationCheckTaskItemService.commitLocation(domain.getTaskId(), domain.getLocation(),
                    domain.getCommit());
        }
        catch (Exception e) {
            log.error("commitLocation error" + e.getMessage(), e);
            responseJson.setMessage("提交失败");
            return responseJson;
        }
    }
}
