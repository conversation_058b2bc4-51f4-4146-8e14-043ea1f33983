package com.estone.android.action;

import com.alibaba.fastjson.JSON;
import com.estone.allocation.call.ApvAllocationRequestCall;
import com.estone.android.domain.AndroidProductDo;
import com.estone.apv.bean.WhApv;
import com.estone.apv.bean.WhApvItem;
import com.estone.apv.bean.WhApvItemQueryCondition;
import com.estone.apv.common.ApvStatus;
import com.estone.apv.service.SmPackingPickingTaskService;
import com.estone.apv.service.WhApvItemService;
import com.estone.apv.service.WhApvService;
import com.estone.common.enums.LogModule;
import com.estone.common.enums.WarehousePropertyEnum;
import com.estone.common.util.CacheUtils;
import com.estone.common.util.RedisKeys;
import com.estone.common.util.RedissonLockUtil;
import com.estone.common.util.SystemLogUtils;
import com.estone.picking.bean.*;
import com.estone.picking.enums.*;
import com.estone.picking.service.WhReplenishmentPickingItemService;
import com.estone.picking.service.WhReplenishmentPickingSkuService;
import com.estone.picking.service.WhReplenishmentPickingTaskService;
import com.estone.sku.bean.WhSku;
import com.estone.sku.bean.WhSkuQueryCondition;
import com.estone.sku.service.WhSkuService;
import com.estone.sowstockout.service.SowStockoutService;
import com.estone.transfer.service.WhFbaAllocationHandleService;
import com.estone.transfer.service.WhFbaAllocationService;
import com.estone.warehouse.bean.WhWarehouse;
import com.whq.tool.component.StatusCode;
import com.whq.tool.json.ResponseJson;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.io.IOException;
import java.sql.Timestamp;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 挂单补货拣货
 */
@Slf4j
@RestController
@RequestMapping(value = "android/replenishment/pickings")
public class AndroidReplenishmentPickingController {

    @Resource
    private WhReplenishmentPickingTaskService whReplenishmentPickingTaskService;

    @Resource
    private WhReplenishmentPickingSkuService whReplenishmentPickingSkuService;

    @Resource
    private WhReplenishmentPickingItemService whReplenishmentPickingItemService;

    @Resource
    private WhApvService whApvService;

    @Resource
    private WhApvItemService whApvItemService;

    @Resource
    private WhSkuService whSkuService;

    /**
     * 扫描挂单拣货任务号
     */
    @RequestMapping(value = "scanningPickingTaskNo", method = { RequestMethod.POST })
    public ResponseJson scanningPicking(@ModelAttribute("domain") AndroidProductDo domain) {
        log.info("android scanningPickingTaskNo ");
        ResponseJson responseJson = new ResponseJson(StatusCode.FAIL);
        if (StringUtils.isBlank(domain.getTaskNo())) {
            responseJson.setMessage("参数taskNo为空！");
            return responseJson;
        }
        if (domain.getReceivePerson() == null || domain.getReceivePerson() == 0) {
            responseJson.setMessage("参数receivePerson为空或者为0！");
            return responseJson;
        }
        if (StringUtils.isBlank(domain.getReceivePersonType())) {
            responseJson.setMessage("参数receivePersonType为空！");
            return responseJson;
        }

        boolean isTaskNo = true;
        if (checkSubTaskNo(domain.getTaskNo()).isSuccess()) {
            // 扫描调拨拣货子任务号
            String tempStr = domain.getTaskNo();
            domain.setTaskNo(tempStr.substring(1));
            isTaskNo = false;
            if (!ApvAllocationRequestCall.allocationIsCheckCompleted(tempStr)) {
                responseJson.setMessage("调拨仓库未装车, 请让调拨仓库完成装车后再进行拣货！");
                return responseJson;
            }
        }

        WhReplenishmentPickingTaskQueryCondition taskQueryCondition = new WhReplenishmentPickingTaskQueryCondition();
        taskQueryCondition.setTaskNo(domain.getTaskNo());
        WhReplenishmentPickingTask pickingTask = whReplenishmentPickingTaskService.queryWhReplenishmentPickingTask(taskQueryCondition);

        if (pickingTask == null) {
            responseJson.setMessage("任务不存在！");
            return responseJson;
        }

        // 扫描拣货任务号领取拣货任务必须是已打印的
        if (!(pickingTask.getIsPrinting().equals(PickingTaskIsPrinting.PRINTING.intCode())
                || pickingTask.getIsPrinting().equals(PickingTaskIsPrinting.PAPER_PRINTING.intCode())) && isTaskNo) {
            responseJson.setMessage("任务必须为已打印才能扫描领取！");
            return responseJson;
        }

        if (!PickingTaskWarehouseType.getNameByCode(String.valueOf(pickingTask.getWarehouseType()))
                .equals(domain.getReceivePersonType()) && isTaskNo) {
            responseJson.setMessage("任务必须本仓的任务！");
            return responseJson;
        }

        if (pickingTask.getWarehouseType() == PickingTaskWarehouseType.STRIDE.intCode() && isTaskNo) {
            responseJson.setMessage("任务不能是跨仓任务！");
            return responseJson;
        }

        if (pickingTask.getTaskStatus()!=null && ReplenishmentPickingTaskStatus.DISCARDED.intCode().equals(pickingTask.getTaskStatus())) {
            responseJson.setMessage("任务不能是废除任务！");
            return responseJson;
        }

        // 判断拣货人是否存在：
        List<WhReplenishmentPickingTask> pickingTasks = null;
        // 非多品多件 2021/05/29
        // 1.如果任务拣货人不存在，查询本账号是否有已领取的任务列表
        if (pickingTask.getTaskType() != null) {
            WhReplenishmentPickingTaskQueryCondition query = new WhReplenishmentPickingTaskQueryCondition();
            List<Integer> intList = new ArrayList<Integer>();
            intList.add(ReplenishmentPickingTaskStatus.UNRECEIVED.intCode());
            intList.add(ReplenishmentPickingTaskStatus.RECEIVED.intCode());
            query.setStatusList(intList);
            query.setReceivePerson(domain.getReceivePerson());
            pickingTasks = whReplenishmentPickingTaskService.queryWhReplenishmentPickingTasks(query, null);
        }
        //当前用户不存在已分配的任务号并且当前扫描的任务号没有领取人
        if (CollectionUtils.isEmpty(pickingTasks) && (pickingTask.getReceivePerson() == null || pickingTask.getReceivePerson() == 0)) {
            // 热销单领取先退冻结库存
            /*if ((pickingTask.getTaskType().equals(PickingTaskType.RXSINGLESINGLE.intCode())
                    || pickingTask.getTaskType().equals(PickingTaskType.RXSINGLEMULTIPLE.intCode()))
                    && pickingTask.getIsPrinting().equals(PickingTaskIsPrinting.PAPER_PRINTING.intCode())){
                return receiveReXiaoPickingTask(pickingTask, domain);
            }else {*/
                // 1.1如果账号没有领取过任务存在，则将本任务改为已领取，并且把拣货SKU列表信息返回
                pickingTask.setReceivePerson(domain.getReceivePerson());
                int returnInt = whReplenishmentPickingTaskService.receivePickingTask(pickingTask);
                if (returnInt > 0) {
                    responseJson = getWhPickingTaskAndSkus(pickingTask.getId(), domain.getReceivePersonType());
                    return responseJson;
                }
                else {
                    responseJson.setMessage("扫任务号领取拣货任务失败:请联系管理员检查任务数据！");
                    return responseJson;
                }
//            }
        }

        //当前用户存在已分配的任务号
        if(CollectionUtils.isNotEmpty(pickingTasks) && ((pickingTask.getReceivePerson() == null || pickingTask.getReceivePerson() == 0 || pickingTask.getReceivePerson().equals(domain.getReceivePerson())))){
            //存在已领取任务号
            List<WhReplenishmentPickingTask> whPickingTasksList = pickingTasks.stream().filter(s -> ReplenishmentPickingTaskStatus.RECEIVED.intCode().equals(s.getTaskStatus())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(whPickingTasksList)) {
//                responseJson = getWhPickingTaskAndSkus(whPickingTasksList.get(0).getId(), domain.getReceivePersonType());
                List<WhReplenishmentPickingTask> currentScanTask = whPickingTasksList.stream().filter(s -> domain.getTaskNo().equals(s.getTaskNo())).collect(Collectors.toList());
                if(CollectionUtils.isNotEmpty(currentScanTask)){
                    return getWhPickingTaskAndSkus(currentScanTask.get(0).getId(), domain.getReceivePersonType());
                }
                responseJson.setMessage("当前账号存在未完成挂单补货拣货任务：" + whPickingTasksList.get(0).getTaskNo());
                return responseJson;
            }

            //当前扫描的任务号是当前领取人
            List<WhReplenishmentPickingTask> currentTasks = pickingTasks.stream().filter(s -> pickingTask.getId().equals(s.getId())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(currentTasks) && (pickingTask.getReceivePerson() != null || pickingTask.getReceivePerson() != 0 )) {
                pickingTasks = currentTasks;
            }
            int returnInt = whReplenishmentPickingTaskService.receivePickingTask(pickingTasks.get(0));
            if (returnInt > 0) {
                // 1.2如果账号有已领取的任务存在，则把拣货SKU列表信息返回
                responseJson = getWhPickingTaskAndSkus(pickingTasks.get(0).getId(), domain.getReceivePersonType());
            }
            else {
                responseJson.setMessage("扫任务号领取拣货任务失败:请联系管理员检查任务数据！");
            }
            return responseJson;
        }

        //当前用户没有已分配的任务并且当前扫描任务号已存在其他领取人
        if(pickingTask.getReceivePerson() != null && pickingTask.getReceivePerson() != 0){
            if (pickingTask.getReceivePerson().equals(domain.getReceivePerson())) {
                // 2.1.3 拣货状态等于已完成或者已废除
                responseJson.setMessage("扫任务号领取拣货任务失败:请检查任务信息并确定是否可以被领取！");
            }
            else{
                responseJson.setMessage("扫任务号领取拣货任务失败:任务已被其它账号领取！");
            }
        }

        return responseJson;
    }

    // 校验订单调拨子任务号
    private ResponseJson checkSubTaskNo(String subTaskNo) {
        ResponseJson responseJson = new ResponseJson();
        responseJson.setStatus(StatusCode.FAIL);

        if (StringUtils.isBlank(subTaskNo)) {
            responseJson.setMessage("订单调拨子任务号为空！");
            return responseJson;
        }

        Pattern pattern = Pattern.compile("([B-B]{1})([J-J]{1})([H-H]{1})([0-9]{0,})");
        Matcher matcher = pattern.matcher(subTaskNo);
        if (matcher.matches()) {
            responseJson.setStatus(StatusCode.SUCCESS);
            return responseJson;
        }
        return responseJson;
    }

    public ResponseJson receiveReXiaoPickingTask(WhReplenishmentPickingTask task, AndroidProductDo domain){
        ResponseJson responseJson = new ResponseJson();
        responseJson.setStatus(StatusCode.FAIL);
        try {
            WhReplenishmentPickingSkuQueryCondition taskSkuQuery = new WhReplenishmentPickingSkuQueryCondition();
            taskSkuQuery.setTaskId(task.getId());
            List<WhReplenishmentPickingSku> taskSkus = whReplenishmentPickingSkuService.queryWhReplenishmentPickingTaskAndSkus(taskSkuQuery, null);
            if (CollectionUtils.isEmpty(taskSkus)){
                responseJson.setMessage("领取任务失败, 拣货明细异常：" +task.getTaskNo());
                return responseJson;
            }
            task.setPickingSkuList(taskSkus);
            task.setReceivePerson(domain.getReceivePerson());
//            responseJson = whPickingTaskService.updateReceiveReXiaoTask(Arrays.asList(taskSkus.get(0).getSku()), task);
            if (responseJson.getStatus().equals(StatusCode.FAIL)){
                return responseJson;
            }
            responseJson = getWhPickingTaskAndSkus(task.getId(), domain.getReceivePersonType());
        }catch (Exception e){
            log.error(e.getMessage(), e);
            responseJson.setMessage("领取任务失败：" + e.getMessage());
            return responseJson;
        }
        return responseJson;
    }

    public ResponseJson getWhPickingTaskAndSkus(Integer id, String receivePersonType) {
        ResponseJson responseJson = new ResponseJson();
        responseJson.setStatus(StatusCode.FAIL);

        WhReplenishmentPickingSkuQueryCondition skuQueryCondition = new WhReplenishmentPickingSkuQueryCondition();
        skuQueryCondition.setTaskId(id);

        // （仓库ID：1-老仓(汉海达仓)，2-新仓(美景仓)）（拣货人类型：38-老仓(汉海达仓)的人，39-新仓(美景仓)的人）
        if (receivePersonType.equals(PickingTaskWarehouseType.OLD.getName())) {
            // 去除掉拣货任务列表仓库校验：库存调拨要求留货，需求要求这么干的
            // whPickingTaskSkuQueryCondition.setWarehouseId(PickingTaskWarehouseType.OLD.intCode());
        }
        else if (receivePersonType.equals(PickingTaskWarehouseType.NEW.getName())) {
            // whPickingTaskSkuQueryCondition.setWarehouseId(PickingTaskWarehouseType.NEW.intCode());
        }
        else {
            log.info("参数receivePersonType不正确:传参:" + receivePersonType + "---OLD:"
                    + PickingTaskWarehouseType.OLD.getName() + "---NEW:" + PickingTaskWarehouseType.NEW.getName());
            log.info("OLD:" + receivePersonType.equals(PickingTaskWarehouseType.OLD.getName()) + "");
            log.info("NEW:" + receivePersonType.equals(PickingTaskWarehouseType.NEW.getName()) + "");
            responseJson.setMessage("参数receivePersonType不正确！");
            return responseJson;
        }

        List<WhReplenishmentPickingSku> taskSkus = whReplenishmentPickingSkuService
                .queryWhReplenishmentPickingTaskAndSkus(skuQueryCondition, null);

        if (CollectionUtils.isNotEmpty(taskSkus)) {
            // SKU库位排序[拣货路径]
            Collections.sort(taskSkus);

            //TODO 新增的 优选仓集包类型，pda领取的时候先转为优选仓，查优选仓库存
            taskSkus.stream().forEach(t -> {
                if (t.getFlagType() != null && PickingTaskType.getOptimalIntCode().contains(t.getFlagType()))
                    t.setFlagType(PickingTaskType.OPTIMAL.intCode());
            });
            responseJson.setMessage(JSON.toJSONString(taskSkus));
            responseJson.setStatus(StatusCode.SUCCESS);
        }
        else {
            responseJson.setMessage("任务详情为空，请检查账号的角色信息！");
        }

        return responseJson;
    }

    // TODO 校验订单是否已经取消或者移动到待分配已交运，如果取消，sku退回原货位，该条目走以下拣货缺货的逻辑
    public List<WhApvItem> getPickCancleQuantity(AndroidProductDo domain, WhReplenishmentPickingSku whPickingTaskSku) {
        WhReplenishmentPickingItemQueryCondition cancleTaskQuery = new WhReplenishmentPickingItemQueryCondition();
        cancleTaskQuery.setTaskId(domain.getTaskId());
        cancleTaskQuery.setSku(domain.getSku());
        List<WhReplenishmentPickingItem> cancleTaskItems = whReplenishmentPickingItemService.queryWhReplenishmentPickingItemsAndSkus(cancleTaskQuery,null);

        if (CollectionUtils.isNotEmpty(cancleTaskItems)) {
            // 正常状态的发货单
            List<Integer> ycIds = new ArrayList<>();
                cancleTaskItems.forEach(pickItem -> {
                    // 排除异常状态和非本批次任务的发货单
                    if (pickItem.getApvStatus() == null || pickItem.getApvStatus() != ApvStatus.SINGLETON_TOUCHING.intCode()){
                        ycIds.add(pickItem.getApvId());
                    }
                });

            if (CollectionUtils.isNotEmpty(ycIds)){
                WhApvItemQueryCondition apvItemQuery = new WhApvItemQueryCondition();
                apvItemQuery.setApvIdList(ycIds);
                apvItemQuery.setSku(domain.getSku());
                return whApvItemService.queryWhApvItems(apvItemQuery, null);
            }
        }
        return null;
    }

    /**
     *
     * @Description: 点击下一步
     *
     * @param domain
     * @return
     * @return: ResponseJson
     * @Author: qinyangkai
     * @Date: 2018/08/22
     * @Version: 0.0.1
     */
    @RequestMapping(value = "picking", method = { RequestMethod.POST })
    public ResponseJson picking(@ModelAttribute("domain") AndroidProductDo domain) {
        ResponseJson responseJson = new ResponseJson(StatusCode.FAIL);
        if (domain.getTaskSkuId() == null || domain.getTaskSkuId() == 0) {
            responseJson.setMessage("参数taskSkuId为空或者为0！");
            return responseJson;
        }
        Integer taskId = domain.getTaskId();
        if (taskId == null || taskId == 0) {
            responseJson.setMessage("参数taskId为空或者为0！");
            return responseJson;
        }
        if (StringUtils.isBlank(domain.getSku())) {
            responseJson.setMessage("参数sku为空！");
            return responseJson;
        }

        log.info("android picking taskNo=" + domain.getTaskNo() + ";sku=" + domain.getSku() + ";pickQuantity=" + domain.getPickQuantity());

        // 检测库位移库 即时刷新拣货列表
        if (StringUtils.isNotBlank(domain.getNextSku()) && StringUtils.isNotBlank(domain.getNextLocation())) {
            WhSkuQueryCondition query = new WhSkuQueryCondition();
            query.setSku(domain.getNextSku());
            query.setLocationNumber(domain.getNextLocation());
            WhSku whSku = whSkuService.queryWhSku(query);
            if (whSku == null) {
                responseJson.setMessage("1");
            }
        }

        WhReplenishmentPickingTaskQueryCondition taskQueryCondition = new WhReplenishmentPickingTaskQueryCondition();
        taskQueryCondition.setId(taskId);
        WhReplenishmentPickingTask whPickingTask = whReplenishmentPickingTaskService.queryWhReplenishmentPickingTask(taskQueryCondition);

        if (whPickingTask.getTaskStatus().equals(ReplenishmentPickingTaskStatus.DISCARDED.intCode())) {
            responseJson.setMessage("该任务已废除！");
            return responseJson;
        }

        domain.setTaskType(whPickingTask.getTaskType());

        try {
            if (RedissonLockUtil.tryLock(RedisKeys.getPickingNextKey(taskId.toString()), TimeUnit.SECONDS, 5, 300)) {
                log.warn("Picking: taskNo=" + whPickingTask.getTaskNo() + ";sku=" + domain.getSku() + ";pickQuantity=" + domain.getPickQuantity());
                WhReplenishmentPickingSkuQueryCondition pickingSkuQueryCondition = new WhReplenishmentPickingSkuQueryCondition();
                pickingSkuQueryCondition.setId(domain.getTaskSkuId());
                WhReplenishmentPickingSku whPickingTaskSku = whReplenishmentPickingSkuService
                        .queryWhReplenishmentPickingSku(pickingSkuQueryCondition);

                if (!whPickingTaskSku.getStatus().equals(ReplenishmentPickingSkuStatus.UNCOMPLETED.intCode())) {
                    responseJson.setMessage("该sku已拣！");
                    return responseJson;
                }
                domain.setNeedQuantity(whPickingTaskSku.getQuantity());
                domain.setTaskNo(whPickingTask.getTaskNo());
                // 判断是否为单品单件或者单品多件 (快递 FBA 热销)

                /*if (domain.getTaskType().equals(PickingTaskType.SINGLESINGLE.intCode())
                        || domain.getTaskType().equals(PickingTaskType.SINGLEMULTIPLE.intCode())
                        || domain.getTaskType().equals(PickingTaskType.OPTIMAL_RU_SS.intCode())
                        || domain.getTaskType().equals(PickingTaskType.OPTIMAL_RU_SM.intCode())
                        || domain.getTaskType().equals(PickingTaskType.OPTIMAL_RU_SV.intCode())
                        || domain.getTaskType().equals(PickingTaskType.EXPRESSSINGLESINGLE.intCode())
                        || domain.getTaskType().equals(PickingTaskType.EXPRESSSINGLEMULTIPLE.intCode())
                        || domain.getTaskType().equals(PickingTaskType.FBASINGLESINGLE.intCode())
                        || domain.getTaskType().equals(PickingTaskType.FBASINGLEMULTIPLE.intCode())
                        || domain.getTaskType().equals(PickingTaskType.RXSINGLESINGLE.intCode())
                        || domain.getTaskType().equals(PickingTaskType.RXSINGLEMULTIPLE.intCode())
                        || domain.getTaskType().equals(PickingTaskType.AISLESINGLESINGLE.intCode())
                        || domain.getTaskType().equals(PickingTaskType.AISLESINGLEMULTIPLE.intCode())
                        || domain.getTaskType().equals(PickingTaskType.SINGLEVERIETY.intCode())) {*/

                    /**
                     * 单品拣货任务
                     */
                    return singlePick(domain, whPickingTaskSku, whPickingTask);
                /*}
                else if (PickingTaskType.getTransferIntCode().contains(domain.getTaskType())
                        || (whPickingTask.getIsAsn() != null
                                && PickingTaskType.getTransferIntCode().contains(whPickingTask.getIsAsn()))) {
                    *//**
                     * 海外仓备货
                     *//*
                    return whFbaAllocationHandleService.multiplePickReplenishment(domain,whPickingTask);
                }
                else {
                    *//**
                     * 多品多件记录拣货数量
                     *//*
                    return multiplePick(domain, whPickingTaskSku, whPickingTask);

                }*/
            }else {
                responseJson.setMessage("请勿重复提交！");
                return responseJson;
            }
        }
        catch (Exception e) {
            log.error("拣货下一步失败！", e);
            responseJson.setMessage("拣货下一步失败：" + e.getMessage());
            return responseJson;
        }
        finally {
            RedissonLockUtil.unlock(RedisKeys.getPickingNextKey(taskId.toString()));
        }
    }

    /**
     * 单品拣货下一步
     * @param domain
     * @param whPickingTaskSku
     * @param whPickingTask
     * @return
     */
    public ResponseJson singlePick(AndroidProductDo domain, WhReplenishmentPickingSku whPickingTaskSku, WhReplenishmentPickingTask whPickingTask){
        ResponseJson response = new ResponseJson();
        response.setStatus(StatusCode.FAIL);
        // 非单件合单状态条目
        List<WhApvItem> cancleApvItems = getPickCancleQuantity(domain, whPickingTaskSku);
        Integer cancleSkuQuantity = 0;
        List<Integer> cancleApvIds = new ArrayList<Integer>();
        if (CollectionUtils.isNotEmpty(cancleApvItems)) {
            for (WhApvItem apvItem : cancleApvItems) {
                cancleSkuQuantity += apvItem.getSaleQuantity();
                cancleApvIds.add(apvItem.getApvId());
            }
            log.info(JSON.toJSONString(whPickingTaskSku.getSku() + "非单件合单状态的订单id：" + cancleApvIds));
            log.info(whPickingTaskSku.getSku() + "取消数量：" + cancleSkuQuantity);
        }
        // 需要退回的sku数量 = 拣货数量 - (订单需拣数量 - 取消订单sku的总数量)
        Integer retutnQuantity = domain.getPickQuantity() - (whPickingTaskSku.getQuantity() - cancleSkuQuantity);
        if (retutnQuantity > 0) {
            Map<String, Object> map = new HashMap(8);
            map.put("skuQuantity", whPickingTaskSku.getQuantity() - cancleSkuQuantity);
            map.put("msg", "有订单已不在单件合单状态，请把该SKU退" + retutnQuantity + "个回到原货位！");
            response.setMessage(JSON.toJSONString(map));
            return response;
        }
        List<String> skus = new ArrayList<>();
        skus.add(domain.getSku());
        // 修改拣货任务Sku的状态-->>已完成
        WhReplenishmentPickingSku pickingSku = new WhReplenishmentPickingSku();
        pickingSku.setId(domain.getTaskSkuId());
        pickingSku.setStatus(ReplenishmentPickingSkuStatus.COMPLETED.intCode());
        pickingSku.setPickQuantity(domain.getPickQuantity());
        whReplenishmentPickingSkuService.updateWhReplenishmentPickingSku(pickingSku);
        response.setStatus(StatusCode.SUCCESS);
        return response;
    }

    /**
     * 多品多件拣货下一步
     * @param domain
     * @param whPickingTaskSku
     * @param whPickingTask
     * @return
     */
    public ResponseJson multiplePick(AndroidProductDo domain, WhReplenishmentPickingSku whPickingTaskSku, WhReplenishmentPickingTask whPickingTask){
        ResponseJson response = new ResponseJson();
        response.setStatus(StatusCode.FAIL);

        // 拣货缺货的APV_ID
        List<Integer> failedApvIds = new ArrayList<>();
        List<WhApv> updateApvs = new ArrayList<>();
        List<WhApvItem> updateApvItems = new ArrayList<>();
        /**
         * 仓库标记
         */
        boolean accross = false;
        List<String> itemSkuList = new ArrayList<>();

        List<WhApv> whapvs = whApvService.queryWhApvAndItemListForPickingByTaskId(domain.getTaskId());
        if (CollectionUtils.isNotEmpty(whapvs)) {

            // 正常状态的发货单(还在该任务中)
            //List<Integer> apvIds = whApvService.queryCurrentTaskNormal(whPickingTask.getTaskNo());

            String sku = domain.getSku();
            // 拣货数量待扣除参数
            Integer tempQuantity = domain.getPickQuantity();
            WhApvItem lastApvItem = null;
            for (WhApv whApv : whapvs) {
                if (CollectionUtils.isEmpty(itemSkuList)){
                    for (WhApvItem whApvItem : whApv.getWhApvItems()) {
                        itemSkuList.add(whApvItem.getSku());
                    }
                }
                if (whApv.getStatus().equals(ApvStatus.CANCEL.intCode())
                        || whApv.getStatus().equals(ApvStatus.PICKING_STOCKOUT_NOT.intCode())
                        || whApv.getStatus().equals(ApvStatus.DELIVER.intCode())
                        || whApv.getStatus().equals(ApvStatus.WAITING_ALLOT.intCode())) {
                    continue;
                }
                // 排除移动状态后又重新合单的订单
                /*if (CollectionUtils.isEmpty(apvIds)){
                    continue;
                }else if (!apvIds.contains(whApv.getId())){
                    continue;
                }*/
                boolean isFull = true;
                List<WhApvItem> whApvItems = whApv.getWhApvItems();
                for (WhApvItem whApvItem : whApvItems) {
                    if (sku.equals(whApvItem.getSku())) {
                        lastApvItem = whApvItem;
                        int saleQuantity = whApvItem.getSaleQuantity();
                        int pickQuantity = whApvItem.getPickQuantity() == null ? 0 : whApvItem.getPickQuantity();
                        int needQuantity = saleQuantity - pickQuantity;
                        if (tempQuantity >= 0) {
                            log.warn("多品多件扣除已匹配数量 " + tempQuantity + " - " + needQuantity);
                            if (tempQuantity >= needQuantity) {
                                whApvItem.setPickQuantity(saleQuantity);
                                lastApvItem.setPickQuantity(saleQuantity);
                                tempQuantity -= needQuantity;
                            }
                            else {
                                whApvItem.setPickQuantity(pickQuantity + tempQuantity);
                                tempQuantity = 0;
                                failedApvIds.add(whApv.getId());
                            }
                        }
                        else {
                            whApvItem.setPickQuantity(0);
                            failedApvIds.add(whApv.getId());
                        }
                        WhApvItem updateApvItem = new WhApvItem();
                        updateApvItem.setId(whApvItem.getId());
                        updateApvItem.setSku(whApvItem.getSku());
                        updateApvItem.setApvLineItemId(whApvItem.getApvLineItemId());
                        updateApvItem.setPickQuantity(whApvItem.getPickQuantity());

                        updateApvItems.add(updateApvItem);
                    }
                    if (whApvItem.getPickQuantity() == null
                            || whApvItem.getPickQuantity() < whApvItem.getSaleQuantity()) {
                        isFull = false;
                    }
                }
                if (isFull) {
                    // 设置为已拣货
                    whApv.setSignDistributionGoods(true);
                    WhApv updateApv = new WhApv();
                    updateApv.setId(whApv.getId());
                    updateApv.setSignDistributionGoods(true);
                    updateApvs.add(updateApv);
                }
            }
            // TODO 应对播种时发现丢货,生成了二次拣货任务,实际已拣大于销售数量
            // 返回给PDA一个是否需要退SKU的信息
            /*if (!domain.getTaskType().equals(PickingTaskType.BZYC.intCode())) {
                if (tempQuantity > 0) {
                    ResponseJson responseJson = new ResponseJson();
                    responseJson.setStatus(StatusCode.FAIL);
                    responseJson.setMessage("请把该SKU退" + tempQuantity + "个回到原货位！");
                    return responseJson;
                }
            }else {
                // TODO 排除取消的订单
            }*/

            if (tempQuantity > 0) {
                ResponseJson responseJson = new ResponseJson();
                responseJson.setStatus(StatusCode.FAIL);
                responseJson.setMessage("请把该SKU退" + tempQuantity + "个回到原货位！");
                return responseJson;
            }

        }

        // 查询一个APV中SKU的仓库属性
        if (CollectionUtils.isNotEmpty(itemSkuList)){
            int warehouseId = CacheUtils.getLocalWarehouseId();
            // 老仓才判断是否跨仓
            if (warehouseId == WarehousePropertyEnum.HHD.intCode()){
                WhSkuQueryCondition queryCondition = new WhSkuQueryCondition();
                queryCondition.setSkus(itemSkuList);
                List<WhSku> whSkus = whSkuService.queryWhSkus(queryCondition, null);
                if (CollectionUtils.isNotEmpty(whSkus)){
                    for (WhSku whSku: whSkus){
                        if (!Integer.valueOf(warehouseId).equals(whSku.getWarehouseId())){
                            accross = true;
                        }
                    }
                }
            }
        }

        List<String> skus = new ArrayList<>();
        skus.add(domain.getSku());
        try {
            // TODO 生成盘点任务
//            response = whPickingTaskService.updateMultiplePicking(skus, domain, whPickingTask, updateApvs, updateApvItems, accross);
        }catch (Exception e){
            log.error(e.getMessage(), e);
            response.setMessage("拣货下一步失败：" + e.getMessage());
        }
        return response;
    }

    /**
     *
     * @Description: 点击完成拣货
     *
     * @param domain
     * @return
     * @return: ResponseJson
     * @throws IOException
     * @Author: qinyangkai
     * @Date: 2018/08/22
     * @Version: 0.0.1
     */
    @RequestMapping(value = "finishPicking", method = { RequestMethod.POST })
    public ResponseJson finishPicking(@ModelAttribute("domain") AndroidProductDo domain) {
        log.info("android finishPicking ");

        ResponseJson responseJson = new ResponseJson();
        responseJson.setStatus(StatusCode.FAIL);

        if (domain.getTaskId() == null || domain.getTaskId() == 0) {
            responseJson.setMessage("参数taskId为空或者为0！");
            return responseJson;
        }

        Integer pickQuantity = 0;
        Integer quantity = 0;
        WhReplenishmentPickingSkuQueryCondition skuQueryCondition = new WhReplenishmentPickingSkuQueryCondition();
        skuQueryCondition.setTaskId(domain.getTaskId());
        List<WhReplenishmentPickingSku> pickingSkuList = whReplenishmentPickingSkuService.queryWhReplenishmentPickingSkus(skuQueryCondition, null);
        if (CollectionUtils.isNotEmpty(pickingSkuList)) {
            for (WhReplenishmentPickingSku pickingSku : pickingSkuList) {
                int pickQty = pickingSku.getPickQuantity() == null ? 0 : pickingSku.getPickQuantity();
                pickQuantity += pickQty;
                int qty = pickingSku.getQuantity() == null ? 0 : pickingSku.getQuantity();
                quantity += qty;
            }
        }

        WhReplenishmentPickingTask whPickingTask = new WhReplenishmentPickingTask();
        whPickingTask.setId(domain.getTaskId());
        whPickingTask.setTaskStatus(ReplenishmentPickingTaskStatus.COMPLETED.intCode());
        whPickingTask.setPickingEndDate(new Timestamp(System.currentTimeMillis()));
        whPickingTask.setPickingQuantity(pickQuantity);
        whPickingTask.setPickingDifferQuantity(quantity - pickQuantity);
        int returnInt = whReplenishmentPickingTaskService.completePickingTask(whPickingTask,
                Collections.singletonList(ReplenishmentPickingTaskStatus.RECEIVED.intCode()));
        if (returnInt <= 0) {
            responseJson.setMessage("修改拣货状态为已完成-失败！");
            return responseJson;
        }
//        PICKINGLOG.log(domain.getTaskId(), "完成拣货-修改状态为已完成", new String[][] {
//                { "原状态", PickingTaskStatus.getNameByCode(String.valueOf(domain.getPickStatus())) } });

        responseJson.setStatus(StatusCode.SUCCESS);
        return responseJson;
    }

    @RequestMapping(value = "refreshPickingTaskList", method = { RequestMethod.POST })
    public ResponseJson refreshPickingTaskList(@ModelAttribute("domain") AndroidProductDo domain) {
        log.info("android refreshPickingTaskList ");

        ResponseJson responseJson = new ResponseJson();
        responseJson.setStatus(StatusCode.FAIL);

        if (domain.getTaskId() == null || domain.getTaskId() == 0) {
            responseJson.setMessage("参数taskId为空或者为0！");
            responseJson.setExceptionCode("1");// 代表PDA需要退出当前页面
            return responseJson;
        }

        if (domain.getReceivePerson() == null || domain.getReceivePerson() == 0) {
            responseJson.setMessage("参数receivePerson为空或者为0！");
            responseJson.setExceptionCode("1");
            return responseJson;
        }

        // 汉字 汉海达仓 美景仓
        if (StringUtils.isBlank(domain.getReceivePersonType())) {
            responseJson.setMessage("参数receivePersonType为空！");
            responseJson.setExceptionCode("1");
            return responseJson;
        }

        WhReplenishmentPickingTaskQueryCondition taskQueryCondition = new WhReplenishmentPickingTaskQueryCondition();
        taskQueryCondition.setId(domain.getTaskId());
        WhReplenishmentPickingTask pickingTask = whReplenishmentPickingTaskService.queryWhReplenishmentPickingTask(taskQueryCondition);

        if (pickingTask == null) {
            responseJson.setMessage("任务不存在！");
            responseJson.setExceptionCode("1");
            return responseJson;
        }

        if (!pickingTask.getReceivePerson().equals(domain.getReceivePerson())) {
            responseJson.setMessage("任务领取人不一致！");
            responseJson.setExceptionCode("1");
            return responseJson;
        }

        if (!pickingTask.getTaskStatus().equals(PickingTaskStatus.RECEIVED.intCode())) {
            responseJson.setMessage("任务必须为已领取！");
            responseJson.setExceptionCode("1");
            return responseJson;
        }

        responseJson = getWhPickingTaskAndSkus(domain.getTaskId(), domain.getReceivePersonType());
        return responseJson;
    }

}
