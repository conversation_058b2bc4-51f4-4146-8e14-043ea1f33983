package com.estone.android.action;

import com.alibaba.fastjson.JSON;
import com.estone.android.domain.AndroidPrestorageStockPickVO;
import com.estone.checkin.enums.CheckInWhType;
import com.estone.system.user.bean.SaleUser;
import com.estone.system.user.service.SaleUserService;
import com.estone.transfer.bean.TransferStock;
import com.estone.transfer.bean.TransferStockQueryCondition;
import com.estone.transfer.service.TransferStockService;
import com.estone.warehouse.bean.*;
import com.estone.warehouse.enums.BoxStatus;
import com.estone.warehouse.enums.BoxType;
import com.estone.warehouse.enums.PrestorageStockTransferStatusEnum;
import com.estone.warehouse.enums.PrintStatusEnum;
import com.estone.warehouse.service.PrestorageStockTransferService;
import com.estone.warehouse.service.WhBoxService;
import com.whq.tool.component.Pager;
import com.whq.tool.component.StatusCode;
import com.whq.tool.context.DataContextHolder;
import com.whq.tool.json.ResponseJson;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023-03-15 18:04
 */
@Slf4j
@RestController
@RequestMapping("android/prestorageStockTransfer")
public class AndroidPrestorageStockTransferOrderController {

    @Autowired
    private PrestorageStockTransferService prestorageStockTransferService;
    @Autowired
    private SaleUserService saleUserService;
    @Autowired
    private WhBoxService whBoxService;
    @Autowired
    private TransferStockService transferStockService;


    /**
     * 通过扫描任务号领取任务
     *
     * @param orderNo 任务号
     * @return 任务详细信息或其他提示内容
     */
    @GetMapping("acceptOrder")
    @ResponseBody
    public ResponseJson acceptOrder(@RequestParam("orderNo") String orderNo) {
        ResponseJson responseJson = new ResponseJson(StatusCode.FAIL);

        List<PrestorageStockTransfer> uncompletedOrders = this.getOtherUncompletedPickOrder(orderNo);

        if (CollectionUtils.isNotEmpty(uncompletedOrders)) {
            List<String> uncompletedOrderNos = uncompletedOrders.stream()
                    .map(PrestorageStockTransfer::getOrderNo)
                    .collect(Collectors.toList());
            responseJson.setMessage("当前本人存在未拣货完成的任务:" + JSON.toJSONString(uncompletedOrderNos));
            return responseJson;
        }

        PrestorageStockTransferQueryCondition queryCondition = new PrestorageStockTransferQueryCondition();
        queryCondition.setOrderNoStrs(orderNo);

        List<PrestorageStockTransferOrder> prestorageStockTransferOrders = prestorageStockTransferService.queryTransferOrders(queryCondition, null);
        if (CollectionUtils.isEmpty(prestorageStockTransferOrders) || prestorageStockTransferOrders.size() > 1) {
            responseJson.setMessage("任务:" + orderNo + "查找失败!");
            return responseJson;
        }

        PrestorageStockTransferOrder prestorageStockTransferOrder = prestorageStockTransferOrders.get(0);
        if (!Objects.equals(PrestorageStockTransferStatusEnum.WAITTING_ACCEPTED.getCode(), prestorageStockTransferOrder.getStatus())
                && !Objects.equals(PrestorageStockTransferStatusEnum.PICKING.getCode(), prestorageStockTransferOrder.getStatus())) {
            responseJson.setMessage("任务：" + orderNo + "不为拣货中或待领取状态");
            return responseJson;
        }

        if (Objects.equals(PrintStatusEnum.UNCOMPLETED_PRINT.getCode(), prestorageStockTransferOrder.getPrintStatus())) {
            responseJson.setMessage("任务：" + orderNo + "为未打印状态，不可领取!");
            return responseJson;
        }

        // 任务曾被人领取过并且不是当前这个人
        if (Objects.nonNull(prestorageStockTransferOrder.getAcceptPersonId())
                && !Objects.equals(DataContextHolder.getUserId(), prestorageStockTransferOrder.getAcceptPersonId())) {
            SaleUser user = saleUserService.getSaleUser(prestorageStockTransferOrder.getAcceptPersonId());
            responseJson.setMessage("任务：" + orderNo + "是" + user.getName() + "进行拣取的!");
            return responseJson;
        }
        return this.getPickingDetailByOrder(prestorageStockTransferOrder);
    }

    /**
     * 随机领取一个任务，但是会优先安排当前用户还未拣货完成的任务去执行
     *
     * @return 任务的详细信息或错误提示
     */
    @GetMapping("randomAcceptOrder")
    @ResponseBody
    public ResponseJson acceptOrder() {
        ResponseJson responseJson = new ResponseJson(StatusCode.FAIL);

        // 优先获取当前人未执行完成的任务
        List<PrestorageStockTransfer> orders = this.getOtherUncompletedPickOrder(null);

        if (CollectionUtils.isEmpty(orders)) {
            PrestorageStockTransferQueryCondition queryCondition = new PrestorageStockTransferQueryCondition();
            queryCondition.setStatus(PrestorageStockTransferStatusEnum.WAITTING_ACCEPTED.getCode());
            Pager pager = new Pager(1, 1);
            orders = prestorageStockTransferService.queryOrders(queryCondition, pager);
        }

        if (CollectionUtils.isEmpty(orders)) {
            responseJson.setMessage("暂无可执行拣货操作的待领取任务!");
            return responseJson;
        }

        PrestorageStockTransfer prestorageStockTransfer = orders.get(0);
        String orderNo = prestorageStockTransfer.getOrderNo();
        PrestorageStockTransferQueryCondition queryCondition = new PrestorageStockTransferQueryCondition();
        queryCondition.setOrderNoStrs(orderNo);
        List<PrestorageStockTransferOrder> prestorageStockTransferOrders = prestorageStockTransferService.queryTransferOrders(queryCondition, null);
        if (CollectionUtils.isEmpty(prestorageStockTransferOrders)) {
            responseJson.setMessage("暂无可执行拣货操作的待领取任务");
            return responseJson;
        }

        return this.getPickingDetailByOrder(prestorageStockTransferOrders.get(0));
    }


    /**
     * 通过订单得到需要进行捡取的任务明细
     *
     * @param order 订单
     * @return 响应的结果，如果需要拣货则为success，否则为fail且给出提示信息
     */
    private ResponseJson getPickingDetailByOrder(PrestorageStockTransferOrder order) {
        ResponseJson responseJson = new ResponseJson(StatusCode.FAIL);
        if (Objects.isNull(order)) {
            responseJson.setMessage("订单不存在!");
            return responseJson;
        }
        String orderNo = order.getOrderNo();
        PrestorageStockTransferQueryCondition queryCondition = new PrestorageStockTransferQueryCondition();
        queryCondition.setOrderNoStrs(orderNo);
        List<PrestorageStockTransferDetail> details = prestorageStockTransferService.queryOrderDetails(queryCondition, null);

        // 查询出中转仓的库存对象
        Map<String, TransferStock> transferStockMap = new HashMap<>();
        List<String> transferIds = Optional.ofNullable(details)
                .orElse(new ArrayList<>())
                .stream()
                .filter(v -> Objects.equals(CheckInWhType.FBA.intCode(), v.getWarehouseType()))
                .map(PrestorageStockTransferDetail::getEmigrationLocationNumber)
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(transferIds)) {
            TransferStockQueryCondition transferStockQueryCondition = new TransferStockQueryCondition();
            transferStockQueryCondition.setIdStr(StringUtils.join(transferIds, ","));
            List<TransferStock> transferStocks = transferStockService.queryTransferStocks(transferStockQueryCondition, null);
            transferStockMap = transferStocks
                    .stream()
                    .collect(Collectors.toMap(v -> String.valueOf(v.getId()), Function.identity()));
        }

        Map<String, TransferStock> finalTransferStockMap = transferStockMap;
        List<AndroidPrestorageStockPickVO> pickVOS = Optional.ofNullable(details)
                .orElse(new ArrayList<>())
                .stream()
                // 过滤掉拣货完成的sku
                .filter(v -> Objects.isNull(v.getPickingSkuAmount()))
                .map(v -> {
                    AndroidPrestorageStockPickVO vo = new AndroidPrestorageStockPickVO();
                    vo.setOrderNo(orderNo);
                    vo.setBoxNo(order.getBindBoxNo());
                    vo.setOrderItemId(v.getId());
                    vo.setSku(v.getSku());
                    vo.setSkuName(v.getSkuName());
                    String pickRegion = v.getEmigrationLocationNumber();
                    if (Objects.equals(CheckInWhType.FBA.intCode(), v.getWarehouseType())) {
                        TransferStock transferStock = finalTransferStockMap.get(pickRegion);
                        pickRegion = transferStock.getLocationNumber();
                    }
                    vo.setPickLocationNumber(pickRegion);
                    vo.setNeedPick(v.getMigrationSkuAmount());
                    vo.setAlreadyPick(0);
                    return vo;
                })
                .collect(Collectors.toList());
        // 完成该任务的拣货并给出提示
        if (CollectionUtils.isEmpty(pickVOS)) {
            order.setStatus(PrestorageStockTransferStatusEnum.PICKING_COMPLETED.getCode());
            prestorageStockTransferService.updateOrder(order);
            responseJson.setStatus(StatusCode.FAIL);
            responseJson.setMessage("任务:" + orderNo + "已拣货完成,无需拣货，请去执行上架！");
        }

        responseJson.setStatus(StatusCode.SUCCESS);
        responseJson.setMessage(JSON.toJSONString(pickVOS));
        return responseJson;
    }


    /**
     * 用于获取当前人除当前指定任务之外其余未完成拣货的任务
     *
     * @param orderNo 指定任务号
     * @return 其余未完成拣货工作的任务
     */
    private List<PrestorageStockTransfer> getOtherUncompletedPickOrder(String orderNo) {
        PrestorageStockTransferQueryCondition queryCondition = new PrestorageStockTransferQueryCondition();
        queryCondition.setAcceptPersonId(DataContextHolder.getUserId());
        queryCondition.setStatus(PrestorageStockTransferStatusEnum.PICKING.getCode());
        List<PrestorageStockTransfer> uncompletedOrders = prestorageStockTransferService.queryOrders(queryCondition, null);

        // 过滤掉当前要执行的任务
        uncompletedOrders = Optional.ofNullable(uncompletedOrders).orElse(new ArrayList<>())
                .stream()
                .filter(v -> !Objects.equals(v.getOrderNo(), orderNo))
                .collect(Collectors.toList());

        return uncompletedOrders;
    }

    /**
     * 周转框绑定
     *
     * @param boxNo      周转筐号
     * @param relationNo 存货迁移任务号
     * @return 绑定结果
     */
    private ResponseJson bindingWhBox(String boxNo, String relationNo) {
        ResponseJson responseJson = new ResponseJson();
        responseJson.setStatus(StatusCode.FAIL);

        int i = whBoxService.updateWhBoxOfBinding(boxNo, relationNo,
                new String[][]{{"绑定存货迁移任务号", relationNo}});

        if (i > 0) {
            responseJson.setStatus(StatusCode.SUCCESS);
        } else {
            responseJson.setMessage("周转筐绑定失败!");
        }

        return responseJson;
    }

    /**
     * 扫描周转筐用于确认执行任务的拣选工作
     *
     * @param boxNo   周转筐编号
     * @param orderNo 订单号
     * @return 返回相关的响应信息
     */
    @GetMapping("pick/scanBox")
    @ResponseBody
    public ResponseJson scanPickBox(@RequestParam("boxNo") String boxNo, @RequestParam("orderNo") String orderNo) {
        ResponseJson responseJson = new ResponseJson(StatusCode.FAIL);
        WhBox whBox = whBoxService.queryWhBoxByBoxNo(boxNo);
        if (Objects.isNull(whBox)) {
            responseJson.setMessage("当前周转框不存在!");
            return responseJson;
        }

        if (!Objects.equals(whBox.getType(), BoxType.YK.intCode())) {
            responseJson.setMessage("当前周转筐类型为" + BoxType.getNameByCode(String.valueOf(whBox.getType())) + "，请找一个移库周转筐进行拣货操作!");
            return responseJson;
        }

        if (Objects.equals(whBox.getStatus(), BoxStatus.ALREADY_USED.intCode())) {
            responseJson.setMessage("当前周转筐已使用，请找一个未使用的移库周转筐进行拣货操作!");
            return responseJson;
        }

        PrestorageStockTransferQueryCondition queryCondition = new PrestorageStockTransferQueryCondition();
        queryCondition.setOrderNoStrs(orderNo);
        List<PrestorageStockTransferOrder> prestorageStockTransferOrders = prestorageStockTransferService.queryTransferOrders(queryCondition, null);
        if (CollectionUtils.isEmpty(prestorageStockTransferOrders)) {
            responseJson.setMessage("当前" + orderNo + "任务不存在!");
            return responseJson;
        }
        if (prestorageStockTransferOrders.size() > 1) {
            responseJson.setMessage("当前" + orderNo + "任务存在异常，一个任务号检出多个任务!");
            return responseJson;
        }
        PrestorageStockTransferOrder order = prestorageStockTransferOrders.get(0);
        if (StringUtils.isNotBlank(order.getBindBoxNo()) && !Objects.equals(order.getBindBoxNo(), boxNo)) {
            responseJson.setMessage("订单" + orderNo + "已绑定周转筐" + order.getBindBoxNo());
            return responseJson;
        }

        if (Objects.nonNull(order.getAcceptPersonId())) {
            SaleUser saleUser = saleUserService.getSaleUser(order.getAcceptPersonId());
            responseJson.setMessage("订单已被" + saleUser.getName() + "执行,请重新领取");
            return responseJson;
        }

        responseJson = this.bindingWhBox(boxNo, orderNo);
        if (Objects.equals(StatusCode.FAIL, responseJson.getStatus())) {
            return responseJson;
        }

        responseJson = this.getPickingDetailByOrder(order);
        if (Objects.equals(StatusCode.FAIL, responseJson.getStatus())) {
            return responseJson;
        }
        order.setBindBoxNo(boxNo);
        order.setStatus(PrestorageStockTransferStatusEnum.PICKING.getCode());
        prestorageStockTransferService.updateOrder(order);
        return responseJson;
    }

    /**
     * 用于记录每次的拣货情况
     *
     * @param orderNo    拣货订单号
     * @param itemId     拣货明细ID
     * @param pickNumber 拣货数量
     * @return
     */
    @GetMapping("pick/pickSku")
    @ResponseBody
    public ResponseJson pickSku(@RequestParam("orderNo") String orderNo,
                                @RequestParam("orderItemId") Integer itemId,
                                @RequestParam("pickNumber") Integer pickNumber) {

        return prestorageStockTransferService.pickSku(orderNo, itemId, pickNumber);
    }

    /**
     * 用于扫描周转筐查出要进行上架的相关任务明细
     *
     * @param boxNo 周转筐号
     * @return 响应要进行上架的任务明细
     */
    @GetMapping("upload/scanBox")
    @ResponseBody
    public ResponseJson scanUploadBox(@RequestParam("boxNo") String boxNo) {
        ResponseJson responseJson = new ResponseJson(StatusCode.FAIL);
        WhBox whBox = whBoxService.queryWhBoxByBoxNo(boxNo);
        if (Objects.isNull(whBox)) {
            responseJson.setMessage("周转筐不存在");
            return responseJson;
        }

        if (!Objects.equals(BoxType.YK.intCode(), whBox.getType())) {
            responseJson.setMessage("周转筐不为移库类型，不符合要进行上架的周转框类型要求，请重新确认!");
            return responseJson;
        }

        PrestorageStockTransferQueryCondition queryCondition = new PrestorageStockTransferQueryCondition();
        queryCondition.setBoxNo(boxNo);
        queryCondition.setStatusList(Arrays.asList(PrestorageStockTransferStatusEnum.PICKING_COMPLETED.getCode(),
                PrestorageStockTransferStatusEnum.UPLODING.getCode()));
        List<PrestorageStockTransferDetail> details = prestorageStockTransferService.queryOrderDetails(queryCondition, null);
        if (CollectionUtils.isEmpty(details)) {
            responseJson.setMessage("周转筐无需上架明细!");
            return responseJson;
        }
        //过滤掉已上架过的
        details = details.stream()
                .filter(v -> Objects.isNull(v.getUploadSkuAmount()))
                .collect(Collectors.toList());

        // 表明该料箱中的已全部上架过但是任务状态没有变更
        if (CollectionUtils.isEmpty(details)) {
            List<String> orderNos = details.stream().map(PrestorageStockTransferDetail::getOrderNo).collect(Collectors.toList());
            queryCondition = new PrestorageStockTransferQueryCondition();
            queryCondition.setOrderNoStrs(StringUtils.join(orderNos, ","));
            List<PrestorageStockTransferOrder> orders = prestorageStockTransferService.queryTransferOrders(queryCondition, null);
            if (CollectionUtils.isEmpty(orders)) {
                responseJson.setMessage("周转筐管理料箱明细的订单不存在!");
                return responseJson;
            }
            for (PrestorageStockTransferOrder order : orders) {
                order.setStatus(PrestorageStockTransferStatusEnum.COMPLETED.getCode());
            }
            prestorageStockTransferService.batchUpdateOrder(orders);
            responseJson.setMessage("料箱中相关订单已全部上架过，无需再上架!");
            return responseJson;
        }
        List<AndroidPrestorageStockPickVO> vos = details.stream()
                .map(v -> {
                    AndroidPrestorageStockPickVO vo = new AndroidPrestorageStockPickVO();
                    vo.setOrderItemId(v.getId());
                    vo.setOrderNo(v.getOrderNo());
                    vo.setSku(v.getSku());
                    vo.setUploadLocationNumber(v.getImmigrationLocationNumber());
                    vo.setAlreadyPick(v.getPickingSkuAmount());
                    vo.setBoxNo(boxNo);
                    return vo;
                })
                .collect(Collectors.toList());

        responseJson.setStatus(StatusCode.SUCCESS);
        responseJson.setMessage(JSON.toJSONString(vos));
        return responseJson;
    }

    /**
     * 校验上架库位是否符合条件
     *
     * @param orderNo              存货迁移单编号
     * @param sku                  上架sku的唯一码或者sku
     * @param uploadLocationNumber 上架库位
     * @return
     */
    @GetMapping("upload/checkUploadLocationNumber")
    @ResponseBody
    public ResponseJson checkLocation(@RequestParam("orderNo") String orderNo,
                                      @RequestParam("sku") String sku,
                                      @RequestParam("uploadLocationNumber") String uploadLocationNumber) {
        return prestorageStockTransferService.checkUploadLocationNumber(orderNo, sku, uploadLocationNumber);
    }

    /**
     * 执行任务的上架操作
     *
     * @param orderNo
     * @param itemId
     * @param uploadNumber
     * @return
     */
    @GetMapping("upload/uploadSku")
    @ResponseBody
    public ResponseJson uploadSku(@RequestParam("orderNo") String orderNo,
                                  @RequestParam("orderItemId") Integer itemId,
                                  @RequestParam("uploadLocationNumber") String uploadLocationNumber,
                                  @RequestParam("uploadNumber") Integer uploadNumber) {

        return prestorageStockTransferService.uploadSku(orderNo, itemId, uploadLocationNumber, uploadNumber);
    }
}
