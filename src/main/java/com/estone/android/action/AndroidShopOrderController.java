package com.estone.android.action;

import com.alibaba.fastjson.JSON;
import com.estone.android.domain.AndroidProductDo;
import com.estone.checkin.enums.CheckInLogType;
import com.estone.combineSku.bean.WhCombineSkuTask;
import com.estone.combineSku.bean.WhCombineSkuTaskQueryCondition;
import com.estone.combineSku.enums.WhCombineSkuTaskStatus;
import com.estone.combineSku.service.WhCombineSkuTaskService;
import com.estone.common.util.RedisKeys;
import com.estone.common.util.RedissonLockUtil;
import com.estone.common.util.SystemLogUtils;
import com.estone.common.util.model.ApiResult;
import com.estone.shop.bean.ShopOrder;
import com.estone.shop.bean.ShopOrderQueryCondition;
import com.estone.shop.domain.EditParam;
import com.estone.shop.enums.DeliveryMethod;
import com.estone.shop.enums.ShopOrderStatus;
import com.estone.shop.service.ShopOrderHandleService;
import com.estone.shop.service.ShopOrderService;
import com.estone.sku.bean.WhUniqueSku;
import com.estone.sku.bean.WhUniqueSkuLog;
import com.estone.sku.bean.WhUniqueSkuQueryCondition;
import com.estone.sku.enums.UniqueSkuStep;
import com.estone.sku.service.LocationMoveInfoService;
import com.estone.sku.service.WhUniqueSkuLogService;
import com.estone.sku.service.WhUniqueSkuService;
import com.estone.warehouse.bean.WhLocation;
import com.estone.warehouse.bean.WhLocationQueryCondition;
import com.estone.warehouse.bean.WhStock;
import com.estone.warehouse.bean.WhStockQueryCondition;
import com.estone.warehouse.enums.LocationStatus;
import com.estone.warehouse.enums.LocationType;
import com.estone.warehouse.service.WhLocationService;
import com.estone.warehouse.service.WhStockService;
import com.whq.tool.component.StatusCode;
import com.whq.tool.json.ResponseJson;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * 内购单
 */
@Slf4j
@RestController
@RequestMapping(value = "android/shopOrder")
public class AndroidShopOrderController {

    @Resource
    private ShopOrderService shopOrderService;

    @Resource
    private ShopOrderHandleService shopOrderHandleService;

    @RequestMapping(value = "scanCheckOut", method = { RequestMethod.POST })
    public ResponseJson scanSku(@RequestParam String orderNo) {
        ResponseJson responseJson = new ResponseJson(StatusCode.FAIL);
        if (StringUtils.isBlank(orderNo)) {
            responseJson.setMessage("内购单号不能为空");
            return responseJson;
        }
        ShopOrderQueryCondition query = new ShopOrderQueryCondition();
        query.setOrderNo(orderNo);
        List<ShopOrder> orderList = shopOrderService.queryShopOrders(query, null);
        if (CollectionUtils.isEmpty(orderList)) {
            responseJson.setMessage("为找到内购单信息");
            return responseJson;
        }
        ShopOrder order = orderList.get(0);
        if (!StringUtils.equals(DeliveryMethod.BY_SELF_COLLECTION.getName(), order.getDeliveryMethod())) {
            responseJson.setMessage("内购单配送方式不是自提");
            return responseJson;
        }
        if (!ShopOrderStatus.WAITING_DELIVER.intCode().equals(order.getStatus())) {
            responseJson.setMessage("内购单不是待发货状态");
            return responseJson;
        }

        try {
            EditParam param = new EditParam();
            param.setId(order.getId());
            param.setShippingCompany(DeliveryMethod.BY_SELF_COLLECTION.getName());
            param.setShippingMethod(DeliveryMethod.BY_SELF_COLLECTION.getName());
            ApiResult<?> apiResult = shopOrderHandleService.editShipMsg(param);
            if (!apiResult.isSuccess()) {
                responseJson.setMessage(apiResult.getErrorMsg());
                return responseJson;
            }
        } catch (Exception e) {
            log.error("内购单自提扫描异常：" + e.getMessage(), e);
            responseJson.setMessage(e.getMessage());
            return responseJson;
        }
        Map<String, String> map = new HashMap();
        map.put("shippingCompany", DeliveryMethod.BY_SELF_COLLECTION.getName());
        map.put("shippingMethod", DeliveryMethod.BY_SELF_COLLECTION.getName());
        map.put("shippingOrderNo", "");
        responseJson.setMessage(JSON.toJSONString(map));
        responseJson.setStatus(StatusCode.SUCCESS);
        return responseJson;
    }

}
