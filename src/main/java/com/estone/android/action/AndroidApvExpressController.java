package com.estone.android.action;

import com.alibaba.fastjson.JSON;
import com.estone.apv.bean.ApvExpress;
import com.estone.apv.bean.ApvExpressQueryCondition;
import com.estone.apv.bean.WhApv;
import com.estone.apv.bean.WhApvQueryCondition;
import com.estone.apv.common.ApvOrderType;
import com.estone.apv.common.ApvStatus;
import com.estone.apv.service.ApvExpressService;
import com.estone.apv.service.WhApvService;
import com.estone.common.util.DateUtils;
import com.estone.common.util.SpringUtils;
import com.estone.scan.deliver.action.DeliverOrderController;
import com.estone.scan.deliver.bean.*;
import com.estone.scan.deliver.service.WhPackCarRecordService;
import com.estone.scan.deliver.service.WhScanShipmentToApvService;
import com.estone.scan.deliver.service.WhShippingMethodService;
import com.whq.tool.component.StatusCode;
import com.whq.tool.json.ResponseJson;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.Semaphore;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @Description 快递类型发货单交运装车
 * @date 2020/7/22 10:11
 */
@RestController
@RequestMapping(value = "android/apvExpress")
public class AndroidApvExpressController {

    private static Logger logger = LoggerFactory.getLogger(AndroidApvExpressController.class);

    @Resource
    private WhPackCarRecordService whPackCarRecordService;

    @Resource
    private WhShippingMethodService whShippingMethodService;

    @Resource
    private WhScanShipmentToApvService whScanShipmentToApvService;

    @Resource
    private WhApvService whApvService;

    @Resource
    private ApvExpressService apvExpressService;

    /**
     * @Description 快递交运
     * <AUTHOR>
     * @date 2020/7/22 14:18
     * @version 1.0
     */
    @RequestMapping(value = "deliver", method = {RequestMethod.POST})
    @ResponseBody
    public ResponseJson deliver(@RequestParam(value = "orderNo") String orderNo,
                                @RequestParam(value = "apvNo") String apvNo) {
        ResponseJson response = new ResponseJson();
        response.setStatus(StatusCode.FAIL);
        if (StringUtils.isBlank(orderNo)) {
            response.setMessage("单号不能为空！");
            return response;
        }
        if (StringUtils.isBlank(apvNo)) {
            response.setMessage("发货单号不能为空！");
            return response;
        }
        DeliverOrderController controller = SpringUtils.getBean(DeliverOrderController.class);
        WhApv whApv = controller.getApvByApvIdOrShippingNo(orderNo);
        if (whApv == null) {
            response.setMessage("单号有误，查无此发货单！");
            return response;
        }
        ApvExpressQueryCondition query = new ApvExpressQueryCondition();
        query.setApvNo(whApv.getApvNo());
        ApvExpress apvExpress = apvExpressService.queryApvExpress(query);
        return apvExpressService.deliverExpress(whApv,apvExpress);
    }

    /**
     * @Description 快递装车
     * <AUTHOR>
     * @date 2020/7/22 14:20
     * @version 1.0
     */
    @RequestMapping(value = "load", method = {RequestMethod.POST})
    @ResponseBody
    public ResponseJson load(@RequestParam(value = "orderNo") String orderNo, @RequestParam(value = "apvNo") String apvNo,
                             @RequestParam(value = "collectCompanyCode", required = false) String collectCompanyCode) {
        ResponseJson response = new ResponseJson();
        response.setStatus(StatusCode.FAIL);
        if (StringUtils.isBlank(orderNo)) {
            response.setMessage("单号不能为空！");
            return response;
        }
        if (StringUtils.isBlank(apvNo)) {
            response.setMessage("发货单号不能为空！");
            return response;
        }
        DeliverOrderController controller = SpringUtils.getBean(DeliverOrderController.class);
        WhApv whApv = controller.getApvByApvIdOrShippingNo(orderNo);
        if (whApv == null) {
            response.setMessage("单号有误，查无此发货单！");
            return response;
        }
        if (!ApvOrderType.getExpressIntCode().contains(whApv.getShipStatus())) {
            response.setMessage("不是快递类型的发货单！");
            return response;
        }
        if (!ApvStatus.DELIVER.equals(whApv.getStatus())) {
            response.setMessage("发货单不是已交运状态！");
            return response;
        }
        // yst不匹配
        if (!StringUtils.equalsIgnoreCase(apvNo, whApv.getApvNo())){
            response.setMessage("扫描追踪号与发货单号不匹配，不能交运!");
            return response;
        }
        ApvExpressQueryCondition query = new ApvExpressQueryCondition();
        query.setApvNo(whApv.getApvNo());
        ApvExpress apvExpress = apvExpressService.queryApvExpress(query);
        if (apvExpress == null) {
            response.setMessage("快递发货单数据未初始化！");
            return response;
        }
        ResponseJson check = apvExpressService.checkMethod(whApv);
        if (check.getStatus().equals(StatusCode.FAIL)) {
            return check;
        }else {
            String collectCode = check.getMessage();
            if (StringUtils.isNotBlank(collectCompanyCode)){
                if (!collectCompanyCode.equals(collectCode)){
                    response.setMessage("不是当前揽收公司！");
                    return response;
                }
            }
            collectCompanyCode = collectCode;
        }
        try {
            ResponseJson result = apvExpressService.updateToLoad(whApv);
            if (result.getStatus().equals(StatusCode.SUCCESS)) {
                response.setStatus(StatusCode.SUCCESS);
                Map<String, String> map = new HashMap<>();
                map.put("apvNo", whApv.getApvNo());
                map.put("trackingNumber", whApv.getTrackingNumber());
                map.put("loadDate", DateUtils.dateToString(new Date(), "yyyy-MM-dd HH:mm:ss"));
                map.put("collectCompanyCode", collectCompanyCode);
                response.setMessage(JSON.toJSONString(map));
            } else {
                response.setMessage(result.getMessage());
            }
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            response.setMessage(e.getMessage());
        }
        response.setStatus(StatusCode.SUCCESS);
        return response;
    }

    private static Semaphore semaphore = new Semaphore(1);

    /**
     * @Description 快递装车汇总
     * <AUTHOR>
     * @date 2020/7/22 14:54
     * @version 1.0
     */
    @RequestMapping(value = "loadToCar", method = {RequestMethod.POST})
    @ResponseBody
    public ResponseJson loadToCar(@RequestBody Set<String> apvNos) {
        ResponseJson response = new ResponseJson();
        response.setStatus(StatusCode.FAIL);
        if (CollectionUtils.isEmpty(apvNos)) {
            response.setMessage("单号不能为空！");
            return response;
        }
        WhScanShipmentToApvQueryCondition query0 = new WhScanShipmentToApvQueryCondition();
        query0.setApvNoList(new ArrayList<>(apvNos));
        List<String> exist = whScanShipmentToApvService.queryApvNoList(query0);
        if (CollectionUtils.isNotEmpty(exist)) {
            apvNos = apvNos.stream().filter(item -> !exist.contains(item)).collect(Collectors.toSet());
            if (CollectionUtils.isEmpty(apvNos)) {
                response.setStatus(StatusCode.SUCCESS);
                response.setMessage("请勿重复提交！");
                return response;
            }
        }
        WhApvQueryCondition query = new WhApvQueryCondition();
        query.setApvNo(StringUtils.join(apvNos, ","));
        query.setExpress(true);
        List<WhApv> apvList = whApvService.queryWhApvAndItemList(query, null);
        if (CollectionUtils.isEmpty(apvList)) {
            response.setMessage("YST号有误，查无此发货单！");
            return response;
        }
        Map<String, WhApv> apvMap = apvList.stream().collect(Collectors.toMap(WhApv::getApvNo, item -> item));
        Map<String, List<WhApv>> map = new HashMap<>();
        for (String apvNo : apvNos) {
            WhApv apv = apvMap.get(apvNo);
            if (apv == null) {
                response.setMessage("YST号有误，查无此发货单: " + apvNo);
                return response;
            }
            if (!ApvOrderType.getExpressIntCode().contains(apv.getShipStatus())) {
                response.setMessage("不是快递类型的发货单: " + apvNo);
                return response;
            }
            if (!ApvStatus.LOADED.equals(apv.getStatus())) {
                response.setStatus(StatusCode.SUCCESS);
                response.setMessage("发货单不是已装车状态: " + apvNo);
                return response;
            }
            String logisticsCompany = apv.getLogisticsCompany();
            List<WhApv> apvs = map.get(logisticsCompany);
            if (CollectionUtils.isEmpty(apvs)) {
                apvs = new ArrayList<>();
            }
            apvs.add(apv);
            map.put(logisticsCompany, apvs);
        }
        WhShippingMethodQueryCondition methodQuery = new WhShippingMethodQueryCondition();
        methodQuery.setCodeList(new ArrayList<>(map.keySet()));
        List<WhShippingMethod> shippingMethods = whShippingMethodService.queryWhShippingMethodDetailsList(methodQuery, null);
        Map<String, WhShippingMethod> methodMap = shippingMethods.stream().collect(Collectors.toMap(WhShippingMethod::getCode, item -> item));
        Map<String, List<WhApv>> deliverMap = new HashMap<>();
        WhCollectCompany collectCompany = null;
        for (String code : map.keySet()) {
            WhShippingMethod method = methodMap.get(code);
            if (method == null) {
                response.setMessage(code + ": 找不到物流方式");
            }
            WhDeliveryCompany deliveryCompany = method.getWhDeliveryCompany();
            if (deliveryCompany == null) {
                response.setMessage(method.getCode() + ": 未配置交运方式");
                return response;
            }
            WhCollectCompany whCollectCompany = method.getWhCollectCompany();
            if (whCollectCompany == null){
                response.setMessage(method.getCode() + ": 未配置揽收公司");
                return response;
            }
            if (collectCompany == null){
                collectCompany = whCollectCompany;
            }else if (!collectCompany.getCode().equals(whCollectCompany.getCode())){
                response.setMessage(method.getCode() + ": 揽收公司不一致");
                return response;
            }
            List<WhApv> deliverApvList = deliverMap.get(deliveryCompany.getCode());
            if (CollectionUtils.isEmpty(deliverApvList)) {
                deliverApvList = new ArrayList<>();
            }
            deliverApvList.addAll(map.get(code));
            deliverMap.put(deliveryCompany.getCode(), deliverApvList);
        }
        try {
            if (!semaphore.tryAcquire()) {
                response.setMessage("还有任务待处理，请稍后提交");
                return response;
            }
            ResponseJson result = apvExpressService.updateAndLoadToCar(deliverMap, collectCompany);
            if (result.getStatus().equals(StatusCode.SUCCESS)) {
                response.setMessage("操作成功");
                response.setStatus(StatusCode.SUCCESS);
                Map<String, String> resultMap = JSON.parseObject(result.getMessage(), HashMap.class);
                WhPackCarRecord packCarRecord = JSON.parseObject(resultMap.get("packCarRecord"), WhPackCarRecord.class);
                List<WhScanShipment> scanShipmentList = JSON.parseArray(resultMap.get("scanShipmentList"), WhScanShipment.class);
                for (WhScanShipment scanShipment : scanShipmentList) {
                    scanShipment.setLocalTruck(true);
                    whPackCarRecordService.pushScanDataToTms(packCarRecord, scanShipment, false);
                }
            } else {
                response.setMessage(result.getMessage());
            }
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            response.setMessage(e.getMessage());
        } finally {
            semaphore.release();
        }
        return response;
    }
}
