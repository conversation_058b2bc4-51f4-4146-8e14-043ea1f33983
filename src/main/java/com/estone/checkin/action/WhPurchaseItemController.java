package com.estone.checkin.action;

import java.util.List;

import javax.annotation.Resource;
import javax.servlet.http.HttpSession;

import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import com.estone.checkin.bean.WhPurchaseItem;
import com.estone.checkin.bean.WhPurchaseItemQueryCondition;
import com.estone.checkin.domain.WhPurchaseItemDo;
import com.estone.checkin.service.WhPurchaseItemService;
import com.whq.tool.action.BaseController;
import com.whq.tool.component.Pager;
import com.whq.tool.json.ResponseJson;

@Controller
@RequestMapping(value = "checkin/whPurchaseItem")
public class WhPurchaseItemController extends BaseController {
    @Resource
    private WhPurchaseItemService whPurchaseItemService;

    @RequestMapping(method = {RequestMethod.GET})
    public String init(@ModelAttribute("domain") WhPurchaseItemDo domain) {
        return "{模块}/{页面}";
    }

    private void initFormData(@ModelAttribute("domain") WhPurchaseItemDo domain) {
         
    }

    private void queryWhPurchaseItems(@ModelAttribute("domain") WhPurchaseItemDo domain) {
        WhPurchaseItemQueryCondition query = domain.getQuery();
        Pager page = domain.getPage();
        if (query == null)
        {
            query = new WhPurchaseItemQueryCondition();
            domain.setQuery(query);
        }
        List<WhPurchaseItem> whPurchaseItems = whPurchaseItemService.queryWhPurchaseItems(query, page);
        domain.setWhPurchaseItems(whPurchaseItems);
    }

    @RequestMapping(value="search", method = {RequestMethod.POST})
    public String search(@ModelAttribute("domain") WhPurchaseItemDo domain) {
        initFormData(domain);
        queryWhPurchaseItems(domain);
        return "{模块}/{页面}";
    }

    @RequestMapping(value="create", method = {RequestMethod.GET})
    public String toCreateWhPurchaseItem(@ModelAttribute("domain") WhPurchaseItemDo domain) {
        return "{模块}/{页面}";
    }

    @RequestMapping(value="create", method = {RequestMethod.POST})
    public String createWhPurchaseItem(@ModelAttribute("domain") WhPurchaseItemDo domain, HttpSession session) {
        WhPurchaseItem whPurchaseItem = domain.getWhPurchaseItem();
        whPurchaseItemService.createWhPurchaseItem(whPurchaseItem);
        return "redirect:/{查询列表}";
    }

    @RequestMapping(value="update", method = {RequestMethod.GET})
    public String toUpdateWhPurchaseItem(@ModelAttribute("domain") WhPurchaseItemDo domain, @RequestParam("whPurchaseItemId") Integer whPurchaseItemId) {
        WhPurchaseItem whPurchaseItem = whPurchaseItemService.getWhPurchaseItem(whPurchaseItemId);
        domain.setWhPurchaseItem(whPurchaseItem);
        return "{模块}/{页面}";
    }

    @RequestMapping(value="update", method = {RequestMethod.POST})
    public String updateWhPurchaseItem(@ModelAttribute("domain") WhPurchaseItemDo domain, HttpSession session) {
        WhPurchaseItem whPurchaseItem = domain.getWhPurchaseItem();
        whPurchaseItemService.updateWhPurchaseItem(whPurchaseItem);
        return "redirect:/{查询列表}";
    }

    @RequestMapping(value="delete", method = {RequestMethod.GET})
    @ResponseBody
    public ResponseJson deleteWhPurchaseItem(@ModelAttribute("domain") WhPurchaseItemDo domain, @RequestParam("whPurchaseItemId") Integer whPurchaseItemId) {
        ResponseJson response = new ResponseJson();
        whPurchaseItemService.deleteWhPurchaseItem(whPurchaseItemId);
        return response;
    }
}