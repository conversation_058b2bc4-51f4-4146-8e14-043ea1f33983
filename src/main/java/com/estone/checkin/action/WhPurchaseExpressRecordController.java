package com.estone.checkin.action;

import java.util.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpSession;

import com.estone.warehouse.enums.LocationWarehouseType;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import com.alibaba.fastjson.JSON;
import com.estone.checkin.bean.*;
import com.estone.checkin.domain.WhPurchaseExpressRecordDo;
import com.estone.checkin.enums.*;
import com.estone.checkin.service.*;
import com.estone.checkin.utils.GetUserNameOrEmployeeNameUtil;
import com.estone.common.enums.LogModule;
import com.estone.common.util.*;
import com.estone.system.downloadcenter.enums.WhDownloadContentEnum;
import com.estone.system.downloadcenter.service.WhDownloadCenterService;
import com.estone.system.rabbitmq.model.PushCheckInException;
import com.estone.system.rabbitmq.producer.RabbitmqProducerService;
import com.estone.warehouse.bean.WhBox;
import com.estone.warehouse.bean.WhBoxQueryCondition;
import com.estone.warehouse.enums.BoxStatus;
import com.estone.warehouse.enums.BoxType;
import com.estone.warehouse.service.WhBoxItemService;
import com.estone.warehouse.service.WhBoxService;
import com.whq.tool.action.BaseController;
import com.whq.tool.component.Pager;
import com.whq.tool.component.StatusCode;
import com.whq.tool.context.DataContextHolder;
import com.whq.tool.json.ResponseJson;

@Controller
@RequestMapping(value = "expressRecords")
public class WhPurchaseExpressRecordController extends BaseController {

    private static Logger logger = LoggerFactory.getLogger(WhPurchaseExpressRecordController.class);
    final static SystemLogUtils EXPRESSRECORDLOG = SystemLogUtils.create(LogModule.EXPRESSRECORD.getCode());
    String[] headers = {"快递单号", "采购单号", "快递公司", "采购员", "仓库", "件数", "实重(kg)", "理论总重(kg)", "签收人", "签收时间", "扫描人", "扫描时间", "拆分人", "拆分时间",
            "原因","标签", "备注"};

    @Resource
    private WhPurchaseExpressRecordService whPurchaseExpressRecordService;

    @Resource
    private WhPurchaseOrderService whPurchaseOrderService;

    @Resource
    private PmsCheckInService pmsCheckInService;

    @Resource
    private WhCheckInExceptionService whCheckInExceptionService;
    @Resource
    private WhCheckInExceptionHandleService whCheckInExceptionHandleService;

    @Resource
    private RabbitmqProducerService rabbitmqProducerService;

    @Resource
    private WhBoxService whBoxService;

    @Resource
    private WhBoxItemService whBoxItemService;

    @Resource
    private OutStockMatchHandelService outStockMatchHandelService;
    
    @Resource
    private WhDownloadCenterService whDownloadCenterService;

    @RequestMapping(method = {RequestMethod.GET})
    public String init(@ModelAttribute("domain") WhPurchaseExpressRecordDo domain) {
        initFormData(domain);
        queryWhPurchaseExpressRecords(domain);
        return "checkin/purchaseExpressRecordList";
    }

    private void initFormData(@ModelAttribute("domain") WhPurchaseExpressRecordDo domain) {
        List<String[]> isSplitList = new ArrayList<>();// 是否拆分
        isSplitList.add(new String[]{"yes", "已拆分"});
        isSplitList.add(new String[]{"no", "未拆分"});

        List<String[]> hasPurchaseOrderNoList = new ArrayList<>();// 是否绑定采购单
        hasPurchaseOrderNoList.add(new String[]{"yes", "已绑定"});
        hasPurchaseOrderNoList.add(new String[]{"no", "未绑定"});

        List<String[]> hasCommentList = new ArrayList<>();// 是否备注
        hasCommentList.add(new String[]{"yes", "有备注"});
        hasCommentList.add(new String[]{"no", "无备注"});

        List<String[]> purchaseTypeList = new ArrayList<>();// 采购类型
        purchaseTypeList.add(new String[]{"FBA", "FBA"});
        purchaseTypeList.add(new String[]{"TJ", "特急"});
        purchaseTypeList.add(new String[]{"VW", "虚拟仓"});
        purchaseTypeList.add(new String[]{"ORDINARY", "普通采购单"});
        purchaseTypeList.add(new String[]{"URGENCY", "紧急采购单"});
        purchaseTypeList.add(new String[]{"JERRY", "Jerry"});
        purchaseTypeList.add(new String[]{"WL", "物流"});

        domain.setPurchaseUsersList(GetUserNameOrEmployeeNameUtil.getPurchaseUser());// 采购员
        domain.setIsSplitList(isSplitList);// 是否拆分
        domain.setHasPurchaseOrderNoList(hasPurchaseOrderNoList);// 是否绑定采购单
        domain.setHasCommentList(hasCommentList);// 是否备注
        domain.setPurchaseTypeList(purchaseTypeList);
        //快递公司
        String shippingCpnStr = "中通快递,极兔快递,申通快递,圆通快递,韵达快递,邮政快递,优速快递,加运美快递,德邦快递,联昊通快递,平安达快递,京东快递,顺丰快递,信丰快递,速腾快递,中通快运,百世快运,安能物流,壹米滴答,顺心物流,韵达快运,德邦物流,京东物流,圆通-委派";
        domain.setShippingMethodList(Arrays.asList(StringUtils.split(shippingCpnStr, ",")));
    }

    private void queryWhPurchaseExpressRecords(@ModelAttribute("domain") WhPurchaseExpressRecordDo domain) {
        WhPurchaseExpressRecordQueryCondition query = domain.getQuery();
        Pager page = domain.getPage();
        if (query == null) {
            query = new WhPurchaseExpressRecordQueryCondition();
            domain.setQuery(query);
        }
        query.setWarehouseId(CacheUtils.getLocalWarehouseId());

        List<WhPurchaseExpressRecord> whPurchaseExpressRecords = whPurchaseExpressRecordService
                .queryWhPurchaseExpressRecords(query, page);
        if (CollectionUtils.isNotEmpty(whPurchaseExpressRecords)){
            List<String> expressNoList = new ArrayList<>();
            for (WhPurchaseExpressRecord record: whPurchaseExpressRecords){
                expressNoList.add(record.getTrackingNumber());
            }

            whPurchaseExpressRecordService.setWhPurchaseExpressRecordFlags(whPurchaseExpressRecords);

            WhCheckInExceptionQueryCondition exceptionQuery = new WhCheckInExceptionQueryCondition();
            exceptionQuery.setTrackingNos(expressNoList);
            List<WhCheckInException> exceptions = whCheckInExceptionService.queryWhCheckInExceptions(exceptionQuery, null);
            if (CollectionUtils.isNotEmpty(exceptions)){
                Map<String, List<WhCheckInException>> map = new HashMap<>();
                for (WhCheckInException checkInException: exceptions){
                    List<WhCheckInException> list = map.get(checkInException.getTrackingNumber());
                    if (list == null){
                        list = new ArrayList<>();
                    }
                    list.add(checkInException);
                    map.put(checkInException.getTrackingNumber(), list);
                }
                for (WhPurchaseExpressRecord record: whPurchaseExpressRecords){
                    record.setCheckinExceptions(map.get(record.getTrackingNumber()));
                }
            }
        }
        domain.setWhPurchaseExpressRecords(whPurchaseExpressRecords);
    }

    @RequestMapping(value = "search", method = {RequestMethod.POST})
    public String search(@ModelAttribute("domain") WhPurchaseExpressRecordDo domain) {
        initFormData(domain);
        queryWhPurchaseExpressRecords(domain);
        return "checkin/purchaseExpressRecordList";
    }

    @RequestMapping(value = "create", method = {RequestMethod.GET})
    public String toCreateWhPurchaseExpressRecord(@ModelAttribute("domain") WhPurchaseExpressRecordDo domain) {
        return "checkin/purchaseExpressRecordAdd";
    }

    @RequestMapping(value = "create", method = {RequestMethod.POST})
    public String createWhPurchaseExpressRecord(@ModelAttribute("domain") WhPurchaseExpressRecordDo domain,
                                                HttpSession session) {
        WhPurchaseExpressRecord whPurchaseExpressRecord = domain.getWhPurchaseExpressRecord();
        whPurchaseExpressRecordService.createWhPurchaseExpressRecord(whPurchaseExpressRecord);
        return "redirect:/expressRecords";
    }

    @RequestMapping(value = "update", method = {RequestMethod.GET})
    public String toUpdateWhPurchaseExpressRecord(@ModelAttribute("domain") WhPurchaseExpressRecordDo domain,
                                                  @RequestParam("whPurchaseExpressRecordId") Integer whPurchaseExpressRecordId) {
        WhPurchaseExpressRecord whPurchaseExpressRecord = whPurchaseExpressRecordService
                .getWhPurchaseExpressRecord(whPurchaseExpressRecordId);
        domain.setWhPurchaseExpressRecord(whPurchaseExpressRecord);
        return "checkin/purchaseExpressRecordEdit";
    }

    @RequestMapping(value = "update", method = {RequestMethod.POST})
    @ResponseBody
    public ResponseJson updateWhPurchaseExpressRecord(@ModelAttribute("domain") WhPurchaseExpressRecordDo domain,
                                                HttpSession session) {
        ResponseJson response = new ResponseJson();
        response.setStatus(StatusCode.FAIL);
        WhPurchaseExpressRecord whPurchaseExpressRecord = domain.getWhPurchaseExpressRecord();
        if (StringUtils.isBlank(whPurchaseExpressRecord.getPurchaseOrderNo())) {
            whPurchaseExpressRecord.setPurchaseOrderNo(null);
        }else{
            // 校验采购单存在
            WhPurchaseOrderQueryCondition condition = new WhPurchaseOrderQueryCondition();
            condition.setPurchaseOrderNo(whPurchaseExpressRecord.getPurchaseOrderNo());
            List<WhPurchaseOrder> whPurchaseOrders = whPurchaseOrderService.queryWhPurchaseOrders(condition, null);
            if (CollectionUtils.isEmpty(whPurchaseOrders)){
                response.setMessage("该采购单号没查到采购信息!");
                return response;
            }
        }
        if (StringUtils.isBlank(whPurchaseExpressRecord.getBoxNo())) {
            whPurchaseExpressRecord.setBoxNo(null);
        }
        if (StringUtils.isBlank(whPurchaseExpressRecord.getComment())) {
            whPurchaseExpressRecord.setComment(null);
        }
        if (StringUtils.isBlank(whPurchaseExpressRecord.getReason())) {
            whPurchaseExpressRecord.setReason(null);
        }
        try {
            whPurchaseExpressRecordService.updateWhPurchaseExpressRecord(whPurchaseExpressRecord);
            if (whPurchaseExpressRecord.getId() != null) {
                WhPurchaseExpressRecord dbOrder = whPurchaseExpressRecordService
                        .getWhPurchaseExpressRecord(whPurchaseExpressRecord.getId());
                if (StringUtils.isNotBlank(dbOrder.getPurchaseOrderNo())) {
                    String[] purchaseOrderNoArr = StringUtils.split(dbOrder.getPurchaseOrderNo(), ",");
                    for (int i = 0; i < purchaseOrderNoArr.length; i++) {
                        int expressStatus = PurchaseExpressStatus.TO_BE_PROCESSED.intCode();
                        if (StringUtils.isNotBlank(whPurchaseExpressRecord.getComment())) {
                            expressStatus = PurchaseExpressStatus.PROCESSED.intCode();
                        }
                        // 修改快递单状态,已处理
                        whPurchaseExpressRecordService.updatePurchaseExpressStatus(purchaseOrderNoArr[i],
                                whPurchaseExpressRecord.getTrackingNumber(), expressStatus);

                        // 修改采购单状态,待入库
                        whPurchaseExpressRecordService.updatePurchaseOrderPurchaseStatus(purchaseOrderNoArr[i],
                                WmsPurchaseOrderStatus.WAIT_STOCK_IN.intCode());

                    }
                }
    /*            if (StringUtils.isNotBlank(whPurchaseExpressRecord.getPurchaseOrderNo())) {
                    List<String> purchaseOrderNos = Arrays.asList(whPurchaseExpressRecord.getPurchaseOrderNo());
                    if (CollectionUtils.isNotEmpty(purchaseOrderNos)) {
                        purchaseOrderNos.forEach(purchaseOrderNo -> whPurchaseExpressRecordService.createPurchaseExpressAmqMessage(dbOrder, purchaseOrderNo));
                    }
                }*/
            }
        } catch (Exception e) {
            logger.error(e.getMessage(),e);
            response.setMessage(e.getMessage());
            return response;
        }
        String[][] log ;
        if (StringUtils.isNotBlank(whPurchaseExpressRecord.getPurchaseOrderNo())
                && !StringUtils.equals(whPurchaseExpressRecord.getPurchaseOrderNo(),whPurchaseExpressRecord.getOldPurchaseOrderNo())) {
            log = new String[3][2];
            log[0] = new String[]{"原采购单号", whPurchaseExpressRecord.getOldPurchaseOrderNo()};
            log[1] = new String[]{"现采购单号", whPurchaseExpressRecord.getPurchaseOrderNo()};
            // 匹配缺货订单
            outStockMatchHandelService.matchApv(null, whPurchaseExpressRecord.getPurchaseOrderNo());
        }else {
            log = new String[1][2];
        }
        log[log.length-1] = new String[]{"原因", whPurchaseExpressRecord.getReason()};
        EXPRESSRECORDLOG.log(whPurchaseExpressRecord.getId(), "编辑采购单",
                log);
        response.setStatus(StatusCode.SUCCESS);
        return response;
    }

    @RequestMapping(value = "delete", method = {RequestMethod.GET})
    @ResponseBody
    public ResponseJson deleteWhPurchaseExpressRecord(@ModelAttribute("domain") WhPurchaseExpressRecordDo domain,
                                                      @RequestParam("whPurchaseExpressRecordId") Integer whPurchaseExpressRecordId) {
        ResponseJson response = new ResponseJson();
        whPurchaseExpressRecordService.deleteWhPurchaseExpressRecord(whPurchaseExpressRecordId);
        logger.info("delete whPurchaseExpressRecord: id[" + whPurchaseExpressRecordId + "]");
        return response;
    }

    /**
     * @param domain
     * @param trackingNumber
     * @return
     * @Title: checkExist
     * @Description: 校验快递是否已签收
     */
    @RequestMapping(value = "checkExist", method = {RequestMethod.GET})
    @ResponseBody
    public ResponseJson checkExist(@ModelAttribute("domain") WhPurchaseExpressRecordDo domain,
                                   @RequestParam("trackingNumber") String trackingNumber) {
        ResponseJson response = new ResponseJson();
        response.setStatus(StatusCode.FAIL);
        WhPurchaseExpressRecordQueryCondition query = new WhPurchaseExpressRecordQueryCondition();
        query.setTrackingNumber(trackingNumber);
        List<WhPurchaseExpressRecord> records = whPurchaseExpressRecordService.queryWhPurchaseExpressRecords(query,
                null);
        if (CollectionUtils.isNotEmpty(records)) {
            logger.info("expressRecordExist 已经有收件记录: trackingNumber[" + trackingNumber + "]");
            response.setMessage(trackingNumber + ":已经有收件记录");
        } else {
            response.setStatus(StatusCode.SUCCESS);
        }
        return response;
    }

    // 批量更新采购单号
    @RequestMapping(value = "batchUpdatePurchaseOrderNo", method = {RequestMethod.GET})
    public String batchUpdatePurchaseOrderNo(@ModelAttribute("domain") WhPurchaseExpressRecordDo domain,
                                             @RequestParam("ids") List<Integer> ids) {
        for (Integer id : ids) {
            WhPurchaseExpressRecord record = whPurchaseExpressRecordService.getWhPurchaseExpressRecord(id);
            String trackingNumber = record.getTrackingNumber();
            if (StringUtils.isNotBlank(trackingNumber) && StringUtils.isBlank(record.getPurchaseOrderNo())) {
                if (trackingNumber.indexOf("=") > 0) {
                    trackingNumber = trackingNumber.split("=")[0]; // 截取前半段
                }

                if (LocationWarehouseType.NANNING.intCode().equals(CacheUtils.getLocalWarehouseId())) {
                    WhPurchaseOrderQueryCondition query = new WhPurchaseOrderQueryCondition();
                    query.setExpressId(trackingNumber);
                    List<WhPurchaseOrder> records = whPurchaseOrderService.queryWhPurchaseExpressOrderAndItemList(query, null);
                    if (CollectionUtils.isEmpty(records)) {
                        continue;
                    }
                }

                List<PurchaseOrder> purchaseOrders = pmsCheckInService
                        .queryPurchaseOrdersByExpressIdOrPurchaseOrderNo(trackingNumber);
                if (CollectionUtils.isNotEmpty(purchaseOrders)) {
                    whPurchaseExpressRecordService.updatePurchaseOrderNo(purchaseOrders, trackingNumber);
                    purchaseOrders.forEach(purchaseOrder -> whPurchaseExpressRecordService.createPurchaseExpressAmqMessage(record, purchaseOrder.getPurchaseOrderNo()));
                }
            }
        }
        return "redirect:/expressRecords";
    }

    /**
     * @param domain
     * @param expressId
     * @param quantity
     * @return
     * @Title: printItemExpressIds
     * @Description: 打印快递单子单号
     */
    @RequestMapping(value = "printItemExpressIds", method = {RequestMethod.GET})
    public String printItemExpressIds(@ModelAttribute("domain") WhPurchaseExpressRecordDo domain,
                                      @RequestParam(value = "expressId") String expressId,
                                      @RequestParam("quantity") Integer quantity,
                                      @RequestParam(value = "isLog", required = false) String isLog) {
        logger.info("打印快递单子单号: expressId[" + expressId + "], quantity[" + quantity + "]");
        if (StringUtils.isNotBlank(isLog) && quantity > 1) {
            String data = StringRedisUtils.get(StringRedisUtils.CHECK_SCAN_LOG_KEY + DataContextHolder.getOperationId());
            if (StringUtils.isNotBlank(data) && CollectionUtils.isNotEmpty(JSON.parseArray(data, String.class))) {
                // 有未完成的物流收货
                Map<String, Object> validateMap = new HashMap<>();
                validateMap.put("isError", true);
                validateMap.put("errorMsg", "还有物流子单号未完成称重!");
                domain.setValidateMap(validateMap);
                return "checkin/receive_express_scan";
            }
            List<String> itemExpressIds = new ArrayList<>(quantity);
            itemExpressIds.add(expressId);
            for (int i = 1; i < quantity; i++) {
                itemExpressIds.add(expressId + "=" + i);
            }
            // 先校验是否有签收记录
            WhPurchaseExpressRecordQueryCondition query = new WhPurchaseExpressRecordQueryCondition();
            query.setTrackingNumber(expressId);
            List<WhPurchaseExpressRecord> records = whPurchaseExpressRecordService.queryWhPurchaseExpressRecords(query, null);
            if (CollectionUtils.isNotEmpty(records)) {
                Map<String, Object> validateMap = new HashMap<>();
                validateMap.put("isError", true);
                validateMap.put("errorMsg", "单号已签收请重新核对!");
                domain.setValidateMap(validateMap);
                return "checkin/receive_express_scan";
            }
            StringRedisUtils.set(StringRedisUtils.CHECK_SCAN_LOG_KEY
                    + DataContextHolder.getOperationId(), JSON.toJSONString(itemExpressIds));
            domain.setItemExpressIds(itemExpressIds);
        } else if (quantity > 1) {
            List<String> itemExpressIds = new ArrayList<>(quantity);
            for (int i = 0; i < quantity; i++) {
                itemExpressIds.add(expressId + "-" + String.format("%03d", i + 1));
            }
            domain.setItemExpressIds(itemExpressIds);
        }
        return "checkin/printItemExpressIds";
    }

    @PostMapping(value = "download")
    @ResponseBody
    public ResponseJson download(@ModelAttribute("domain") WhPurchaseExpressRecordDo domain,
            @RequestParam(value = "ids", required = false) List<Integer> ids) {
        WhPurchaseExpressRecordQueryCondition query = domain.getQuery();
        if (query == null) {
            query = new WhPurchaseExpressRecordQueryCondition();
            domain.setQuery(query);
        }
        if (CollectionUtils.isNotEmpty(ids)) {
            query.setIds(ids);
        }
        query.setWarehouseId(CacheUtils.getLocalWarehouseId());

        domain.getPage().setPageNo(-1);
        boolean isAll = whDownloadCenterService.checkFieldAllNull(query);

        String fileName = "签收记录" + System.currentTimeMillis() + ".xlsx";
        WhPurchaseExpressRecordQueryCondition finalQuery = query;
        whDownloadCenterService.downloading(fileName, headers, WhDownloadContentEnum.EXPRESS_RECORD, isAll,
                domain.getPage(), (page) -> {
                    List<WhPurchaseExpressRecord> records = whPurchaseExpressRecordService
                            .queryWhPurchaseExpressRecords(finalQuery, page);
                    return getExportList(records);
                });
        ResponseJson response = new ResponseJson();
        response.setStatus(StatusCode.SUCCESS);
        response.setMessage("导出任务已经创建，请到下载中心查看结果");
        return response;
    }

    private List<List<String>> getExportList(List<WhPurchaseExpressRecord> records) {
        List<List<String>> recordData = new ArrayList<List<String>>();
        if (CollectionUtils.isEmpty(records)) {
            return recordData;
        }
        for (WhPurchaseExpressRecord record : records) {
            List<String> recordList = new ArrayList<String>(headers.length);
            recordList.add(POIUtils.transferObj2Str(record.getTrackingNumber()));// 快递单号
            recordList.add(POIUtils.transferObj2Str(record.getPurchaseOrderNo()));// 采购单号
            recordList.add(POIUtils.transferObj2Str(record.getShippingCpn()));// 快递公司
            recordList.add(POIUtils.transferObj2Str(record.getPurchaseUserName()));// 采购员
            if (record.getWarehouseId() != null) {
                recordList.add(POIUtils.transferObj2Str(TaglibUtils.getWarehouseById(record.getWarehouseId())));// 仓库
            }
            else {
                recordList.add(POIUtils.transferObj2Str(""));
            }
            recordList.add(POIUtils.transferObj2Str(record.getQuantity()));// 件数
            recordList.add(POIUtils.transferObj2Str(record.getWeight()));// 实重(kg)
            recordList.add(POIUtils.transferObj2Str(record.getTotalWeight()));// 理论总重(kg)
            recordList.add(POIUtils.transferObj2Str(TaglibUtils.getEmployeeNameByUserId(record.getCreatedBy())));// 签收人
            recordList.add(POIUtils.transferObj2Str(record.getCreationDate()));// 签收时间
            recordList.add(POIUtils.transferObj2Str(TaglibUtils.getEmployeeNameByUserId(record.getCheckInScanner())));// 扫描人
            recordList.add(POIUtils.transferObj2Str(record.getCheckInScanTime()));// 扫描时间
            recordList.add(POIUtils.transferObj2Str(TaglibUtils.getEmployeeNameByUserId(record.getSplitUser())));// 拆分人
            recordList.add(POIUtils.transferObj2Str(record.getSplitDate()));// 拆分时间
            recordList.add(POIUtils.transferObj2Str(record.getReason()));// 原因
            recordList.add(POIUtils
                    .transferObj2Str(StringUtils.equals(record.getLogisticsMark(), "R_PARTS") ? "该包裹有补发配件" : ""));// 标签
            recordList.add(POIUtils.transferObj2Str(record.getComment()));// 备注

            recordData.add(recordList);
        }
        return recordData;
    }

    /**
     * 创建未匹配采购单的异常单
     *
     * @param trackingNumber
     * @param boxNo
     * @param id
     * @param purchaseUser
     * @return
     */
    @RequestMapping(value = "createExceptionOrder", method = {RequestMethod.POST})
    @ResponseBody
    public ResponseJson createExceptionOrder(@RequestParam(value = "trackingNumber") String trackingNumber,
                                             @RequestParam(value = "exceptionBoxNo") String boxNo, @RequestParam(value = "id") Integer id,
                                             @RequestParam(value = "purchaseUser") Integer purchaseUser) {
        ResponseJson responseJson = new ResponseJson();
        String sku = null;
        if (id == null || StringUtils.isBlank(trackingNumber)) {
            responseJson.setMessage("参数不能为空！");
            responseJson.setStatus(StatusCode.FAIL);
            return responseJson;
        }
        if (StringUtils.isBlank(boxNo) || purchaseUser == null) {
            responseJson.setMessage("周转筐为空或者采购员为空！");
            responseJson.setStatus(StatusCode.FAIL);
            return responseJson;
        }
        // 先刷新采购单
        List<PurchaseOrder> purchaseOrders = pmsCheckInService
                .queryPurchaseOrdersByExpressIdOrPurchaseOrderNo(trackingNumber);
        if (CollectionUtils.isNotEmpty(purchaseOrders)) {
            whPurchaseExpressRecordService.updatePurchaseOrderNo(purchaseOrders, trackingNumber);
        }

        WhPurchaseExpressRecord dbPurchaseExpressRecord = whPurchaseExpressRecordService.getWhPurchaseExpressRecord(id);
        // 如果采购单刷新成功
        if (dbPurchaseExpressRecord != null && StringUtils.isNotBlank(dbPurchaseExpressRecord.getPurchaseOrderNo())) {
            responseJson.setStatus(StatusCode.FAIL);
            responseJson.setMessage("已匹配到采购单：" + dbPurchaseExpressRecord.getPurchaseOrderNo() + ",不再生成异常单！");
            return responseJson;
        }
        // 查询该收货单是否已经生成异常单
        WhCheckInExceptionQueryCondition query = new WhCheckInExceptionQueryCondition();
        query.setTrackingNumber(trackingNumber);
        query.setExceptionFrom(7);
        query.setExceptionType("23");
        WhCheckInException exist = whCheckInExceptionService.queryWhCheckInException(query);
        if (exist != null) {
            responseJson.setStatus(StatusCode.FAIL);
            responseJson.setMessage("该收货单已生成过异常单,生成异常单失败！");
            return responseJson;
        }
        // 没有匹配到相关采购单，生成异常数据
        if (dbPurchaseExpressRecord != null && StringUtils.isBlank(dbPurchaseExpressRecord.getPurchaseOrderNo())) {
            WhCheckInException whCheckInException = new WhCheckInException();
            whCheckInException.setWarehouseId(dbPurchaseExpressRecord.getWarehouseId());// 仓库编号
            whCheckInException.setCreatedBy(DataContextHolder.getUserId());// 创建人
            whCheckInException.setTrackingNumber(dbPurchaseExpressRecord.getTrackingNumber());// 快递单号
            whCheckInException.setStatus(ExceptionStatus.UNCONFIRM.intCode());// 设置初始状态，“草稿”
            whCheckInException.setBoxNo(boxNo.toUpperCase());// 防止周转码小写
            whCheckInException.setExceptionFrom(ExceptionFrom.SCAN_RECEIPT_EXCEPTION.intCode());// 设置异常来源
            whCheckInException.setExceptionType(ExceptionType.NO_PURCHASE_ORDER_MATCH.intCode().toString());
            whCheckInException.setPurchaseUser(purchaseUser);
            createWhCheckInExceptionAndHandleAndSendMsgToPms(whCheckInException, "purchaseExpressRecordException");
            if (whCheckInException.getId() != null) {
                responseJson.setStatus(StatusCode.SUCCESS);
                responseJson.setMessage("生成异常单成功！");

                if (StringUtils.isNotBlank(whCheckInException.getTrackingNumber())) {
                    WhPurchaseOrderQueryCondition orderQuery = new WhPurchaseOrderQueryCondition();
                    orderQuery.setTrackingNumber(whCheckInException.getTrackingNumber());
                    orderQuery.setQueryExpress(true);
                    List<WhPurchaseOrder> whPurchaseOrderList = whPurchaseOrderService
                            .queryWhPurchaseExpressOrderAndItemList(orderQuery, null);
                    if (CollectionUtils.isNotEmpty(whPurchaseOrderList)) {
                        for (WhPurchaseOrder purchaseOrder : whPurchaseOrderList) {
                            // 修改采购单状态,待入库
                            whPurchaseExpressRecordService.updatePurchaseOrderPurchaseStatus(
                                    purchaseOrder.getPurchaseOrderNo(), WmsPurchaseOrderStatus.WAIT_STOCK_IN.intCode());
                            // 修改采购快递单状态,已处理
                            whPurchaseExpressRecordService.updatePurchaseExpressStatus(
                                    purchaseOrder.getPurchaseOrderNo(), whCheckInException.getTrackingNumber(),
                                    PurchaseExpressStatus.PROCESSED.intCode());
                        }

                    }
                }
            } else {
                responseJson.setStatus(StatusCode.FAIL);
                responseJson.setMessage("生成异常单失败！");
            }

        }
        return responseJson;
    }

    /**
     * 创建异常单和异常记录并发送消息给pms
     *
     * @param whCheckInException
     * @param msgType
     */
    public void createWhCheckInExceptionAndHandleAndSendMsgToPms(WhCheckInException whCheckInException,
                                                                 String msgType) {
        // 创建入库异常单
        whCheckInExceptionService.createWhCheckInException(whCheckInException,null);
        // 获取创建成功的异常订单
        WhCheckInExceptionQueryCondition queryCondition = new WhCheckInExceptionQueryCondition();
        queryCondition.setId(whCheckInException.getId());
        WhCheckInException dbWhCheckInException = whCheckInExceptionService.queryWhCheckInException(queryCondition);
        // 组装入库异常单处理详情数据
        WhCheckInExceptionHandle whCheckInExceptionHandle = new WhCheckInExceptionHandle();
        whCheckInExceptionHandle.setCreatedBy(DataContextHolder.getUserId());
        whCheckInExceptionHandle.setExceptionId(dbWhCheckInException.getId());
        whCheckInExceptionHandle.setQuantity(dbWhCheckInException.getQuantity());
        whCheckInExceptionHandle.setStatus(dbWhCheckInException.getStatus());
        whCheckInExceptionHandle.setHandleComment(dbWhCheckInException.getExceptionComment());
        // 创建入库异常单处理详情
        whCheckInExceptionHandleService.createWhCheckInExceptionHandle(whCheckInExceptionHandle);

        whCheckInException.setPurchaseUserNameToPms(whCheckInException.getPurchaseUserNameToPms());
        // 推送点数入库异常信息的消息到采购系统
        logger.info("start to send " + msgType + " message to pms ====exception:" + dbWhCheckInException.getId()
                + "===exceptionHandle:" + whCheckInExceptionHandle.getId());
        rabbitmqProducerService.pushCheckInExceptionMsgToPms(dbWhCheckInException,
                whCheckInExceptionHandleService.getWhCheckInExceptionHandle(whCheckInExceptionHandle.getId()),
                new PushCheckInException());
        logger.info("send " + msgType + " message to pms end ");
    }

    @RequestMapping(value = "checkBox", method = {RequestMethod.GET})
    @ResponseBody
    public ResponseJson receiveExpressCheckBox(@ModelAttribute("domain") WhPurchaseExpressRecordDo domain,
                                               @RequestParam("boxNo") String boxNo, @RequestParam("type") String type,
                                               @RequestParam("warehouseId") Integer warehouseId) {
        logger.info("expressRecords/checkBox: boxNo[" + boxNo + "]");
        ResponseJson response = new ResponseJson();
        response.setStatus(StatusCode.FAIL);
        WhBoxQueryCondition query = new WhBoxQueryCondition();
        query.setBoxNo(boxNo);
        WhBox whBox = whBoxService.queryWhSHBox(query);
        if (whBox != null) {
            if (StringUtils.isNotBlank(type) && type.equals("receive")
                    && !BoxType.getShBoxIntCode().contains(whBox.getType())) {
                response.setMessage("此周转筐不是收货专用,请重新输入");
                return response;
            }
            if (warehouseId != null && !warehouseId.equals(whBox.getWhId())) {
                response.setMessage("此周转筐所在仓库与所选仓库不匹配,请重新输入");
                return response;
            }
            if (BoxStatus.ALREADY_USED.intCode().equals(whBox.getStatus()) && whBox.getPresentUser() != null) {
                response.setMessage("此周转筐已被" + whBox.getPresentUserStr() + "领取,请重新输入");
                return response;
            }
            if (BoxStatus.ALREADY_USED.intCode().equals(whBox.getStatus()) && whBox.getConsignee() != null
                    && !whBox.getConsignee().equals(DataContextHolder.getUserId())) {
                response.setMessage("此周转筐正在被" + whBox.getConsigneeStr() + "使用,请重新输入");
                return response;
            }


            whBoxService.updateStatus(boxNo, true);
            WhBox whBox1 = whBoxService.viewItems(boxNo);
            if (whBox1 != null && CollectionUtils.isNotEmpty(whBox1.getWhBoxItems()))
            whBox.setWhBoxItems(whBox1.getWhBoxItems());
            response.setMessage(JSON.toJSONString(whBox));
            response.setStatus(StatusCode.SUCCESS);

        } else {
            response.setMessage("无此周转筐,请重新输入");
        }
        logger.info("response: " + response);
        return response;
    }


    @RequestMapping(value = "refreshReceiveItems", method = {RequestMethod.GET})
    @ResponseBody
    public ResponseJson refreshReceiveItems(@RequestParam("boxNo") String boxNo) {
        logger.info("expressRecords/refreshReceiveItems: boxNo[" + boxNo + "]");
        ResponseJson response = new ResponseJson();
        WhBoxQueryCondition query = new WhBoxQueryCondition();
        query.setBoxNo(boxNo);
        WhBox whBox = whBoxService.viewItems(boxNo);
        if (whBox != null) {
            response.setMessage(JSON.toJSONString(whBox));
            return response;
        }
        return response;
    }


    /**
     * 修改周转框使用状态
     *
     * @param boxNo
     * @param oldBoxNo
     * @return
     */
    @RequestMapping(value = "trackingNoBindBoxNoAndChangeStatus", method = {RequestMethod.POST})
    @ResponseBody
    public ResponseJson trackingNoBindBoxNoAndChangeStatus(@RequestParam("boxNo") String boxNo,
                                                           @RequestParam(value = "oldBoxNo") String oldBoxNo) {
        ResponseJson responseJson = new ResponseJson();
        responseJson.setStatus(StatusCode.FAIL);
        try {
            if (StringUtils.isBlank(boxNo) || StringUtils.isBlank(oldBoxNo)) {
                responseJson.setMessage("周转框参数为空！");
                return responseJson;
            }
            responseJson = whBoxItemService.updateStatusAndCreateLog(boxNo, oldBoxNo);
        } catch (Exception e) {
            responseJson.setMessage("异常，更改周转框使用状态失败！");
            return responseJson;
        }
        return responseJson;
    }

    /**
     * 批量拒收
     *
     * @param ids
     * @param rejectComment
     * @return
     */
    @RequestMapping(value = "batchRejectExpressRecord", method = {RequestMethod.POST})
    @ResponseBody
    public ResponseJson batchRejectExpressRecord(@RequestParam("ids") List<Integer> ids,
                                                 @RequestParam("rejectComment") String rejectComment) {
        ResponseJson response = new ResponseJson();
        response.setStatus(StatusCode.FAIL);
        if (CollectionUtils.isEmpty(ids) || StringUtils.isBlank(rejectComment)) {
            response.setMessage("参数为空！");
            return response;
        }

        return whPurchaseExpressRecordService.batchRejectExpressRecord(ids, rejectComment);

    }

    /**
     * 解绑
     *
     * @param id
     * @return
     */
    @RequestMapping(value = "unbindExpressRecord", method = {RequestMethod.GET})
    @ResponseBody
    public ResponseJson unbindExpressRecord(@RequestParam("ids")String id){
        ResponseJson responseJson = new ResponseJson();
        responseJson.setStatus(StatusCode.FAIL);

        try {
            whPurchaseExpressRecordService.unbindExpressRecord(Integer.parseInt(id));
        } catch (Exception e) {
            responseJson.setMessage(e.getMessage());
            return responseJson;
        }
        responseJson.setMessage("解绑成功！");
        responseJson.setStatus(StatusCode.SUCCESS);
        return responseJson;
    }


    @RequestMapping(value = "batchComment", method = {RequestMethod.GET})
    public String skipBatchComment(@ModelAttribute("domain") WhPurchaseExpressRecordDo domain) {
        return "checkin/batch_comment";
    }

    @ResponseBody
    @RequestMapping(value = "batchComment", method = {RequestMethod.POST})
    public ResponseJson batchComment(@RequestParam("ids") String ids, @RequestParam("comment") String comment) {

        ResponseJson responseJson = new ResponseJson();
        responseJson.setStatus(StatusCode.FAIL);

        if (StringUtils.isBlank(comment)) {
            responseJson.setStatus(StatusCode.SUCCESS);
            return responseJson;
        }

        try {
            /*
            1、修改备注
            2、修改关联的快递单状态为已处理
            3、修改关联的采购单为待入库
            4、记录日志 快递单在方法里面已经记录了，单独记录采购日志
         */
            whPurchaseExpressRecordService.doBatchUpdateCommentByIds(ids, comment);
        } catch (Exception e) {
            responseJson.setMessage(e.getMessage());
            return responseJson;
        }

        responseJson.setMessage("备注成功！");
        responseJson.setStatus(StatusCode.SUCCESS);
        return responseJson;
    }

    @ResponseBody
    @RequestMapping(value = "parcelUnbind", method = {RequestMethod.POST})
    public ResponseJson parcelUnbind(@RequestParam("unbindUser") Integer unbindUser) {
        ResponseJson responseJson = new ResponseJson();
        responseJson.setStatus(StatusCode.FAIL);

        if (unbindUser==null) {
            responseJson.setMessage("解绑人不能为空！");
            return responseJson;
        }
        try {
            StringRedisUtils.del(StringRedisUtils.CHECK_SCAN_LOG_KEY + unbindUser);
        } catch (Exception e) {
            responseJson.setMessage(e.getMessage());
            return responseJson;
        }
        responseJson.setMessage("解绑成功！");
        responseJson.setStatus(StatusCode.SUCCESS);
        return responseJson;
    }
}