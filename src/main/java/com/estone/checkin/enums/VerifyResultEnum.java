package com.estone.checkin.enums;

public enum VerifyResultEnum {

    /**
     * 合格
     */
    PASS(1, "合格"),

    /**
     * 不合格
     */
    UNPASS(2, "不合格"),
    ;

    private Integer code;

    private String name;

    private VerifyResultEnum(Integer code, String name) {
        this.name = name;
        this.code = code;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public static String getNameByCode(Integer code) {
        VerifyResultEnum[] values = values();
        for (VerifyResultEnum type : values) {
            if (type.code.equals(code)) {
                return type.getName();
            }
        }
        return null;
    }

    public static VerifyResultEnum build(Integer code) {
        VerifyResultEnum[] values = values();
        for (VerifyResultEnum type : values) {
            if (type.code.equals(code)) {
                return type;
            }
        }

        return null;
    }
}
