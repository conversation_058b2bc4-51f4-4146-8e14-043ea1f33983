package com.estone.checkin.enums;

/**
 * ---------------------------------------------------------------------------
 * Copyright (c) 2018, estone- All Rights Reserved.
 * Project Name:wms
 * Package Name:com.estone.checkin.enums
 * File Name:WmsPurchaseOrderStatus.java
 * Description:仓库采购单状态
 * Author:Yimeil
 * Date:2019-10-10 15:09
 * ---------------------------------------------------------------------------
 */
public enum  WmsPurchaseOrderStatus {
    UNBIND("未绑定", "1"),

    WAIT_RECEIVE("待收货", "3"),

    WAIT_STOCK_IN("待入库", "5"),

    STOCK_IN_ING("入库中", "7"),

    PART_STOCK_IN("部分入库","9"),

    ALL_STOCK_IN("全部入库", "11"),

    CANCELED("已取消", "13"),

    ABANDON("已废弃", "20");

    private String code;

    private String name;

    private WmsPurchaseOrderStatus(String name, String code) {
        this.name = name;
        this.code = code;
    }

    public String getCode() {
        return code;
    }

    public Integer intCode() {
        return Integer.valueOf(code);
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public static String getNameByCode(String code) {
        WmsPurchaseOrderStatus[] values = values();
        for (WmsPurchaseOrderStatus type : values) {
            if (type.code.equals(code)) {
                return type.getName();
            }
        }
        return null;
    }
}
