package com.estone.checkin.enums;

import java.io.Serializable;
import java.util.*;

public enum ClothingItemAttr  implements Serializable {


    YC("衣长", "YC", "Length"),

    LK("领宽", "LK", "NeckWidth"),

    JK("肩宽", "J<PERSON>", "Shoulder"),

    XW("胸围", "XW", "Bust"),

    BW("臂围", "BW", "Arm"),

    XC("袖长", "XC", "Sleeve"),

    XK("袖口", "XK", "SleeveOpening"),

    XBW("下摆围", "XBW", "Hem"),

    TW("臀围", "TW", "Hip"),

    YW("腰围", "YW", "Waist"),

    ZDC("直档长", "ZDC", "Rise"),

    KC("裤长", "KC", "Leg"),

    DTW("大腿围", "DTW", "ThighWidth"),

    KJW("裤脚围", "KJ<PERSON>", "LegOpening"),

    QC("裙长", "QC", "DressesLength"),

    SXW("上胸围", "SXW", "UpperBust"),

    XXW("下胸围", "XXW", "UnderBust"),

    BX("杯型", "BX", "CupType"),

    DDC("吊带长", "DDC", "StrapLength"),

    JKS("脚口", "JKS", "Thigh");

    private String code;

    private String name;

    private String enName;

    ClothingItemAttr(String name, String code,String enName) {
        this.name = name;
        this.code = code;
        this.enName = enName;
    }

    public String getCode() {
        return code;
    }

    public Integer intCode() {
        return Integer.valueOf(code);
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getEnName() {
        return enName;
    }

    public void setEnName(String enName) {
        this.enName = enName;
    }

    public static String getNameByCode(String code) {
        ClothingItemAttr[] values = values();
        for (ClothingItemAttr type : values) {
            if (type.code.equalsIgnoreCase(code)) {
                return type.getName();
            }
        }
        return null;
    }

    public static String getCodeByName(String name) {
        ClothingItemAttr[] values = values();
        for (ClothingItemAttr type : values) {
            if (type.name.equals(name)) {
                return type.getCode();
            }
        }
        return null;
    }

    public static String getEnNameByName(String name) {
        ClothingItemAttr[] values = values();
        for (ClothingItemAttr type : values) {
            if (type.name.equals(name)) {
                return type.getEnName();
            }
        }
        return null;
    }

    public static Map toMap(){
        Map<String, String> map = new HashMap<>();
        ClothingItemAttr[] values = values();
        for (ClothingItemAttr type : values) {
            map.put(type.getCode(), type.getName());
        }
        return map;
    }
    public static Map toEnMap(){
        Map<String, String> map = new HashMap<>();
        ClothingItemAttr[] values = values();
        for (ClothingItemAttr type : values) {
            map.put(type.getCode(), type.getEnName());
        }
        return map;
    }

    public static List<String> getCodeList(){
        List<String> codeList = new ArrayList<>();
        ClothingItemAttr[] values = values();
        for (ClothingItemAttr type : values) {
            codeList.add(type.getCode());
        }
        return codeList;
    }
    
    public static List<ClothingItemAttr> getSyList() {
        return Arrays.asList(ClothingItemAttr.YC, ClothingItemAttr.LK, ClothingItemAttr.JK, ClothingItemAttr.XW,
                ClothingItemAttr.BW, ClothingItemAttr.XC, ClothingItemAttr.XK, ClothingItemAttr.XBW);
    }

    public static List<ClothingItemAttr> getKzList() {
        return Arrays.asList(ClothingItemAttr.TW, ClothingItemAttr.YW, ClothingItemAttr.ZDC, ClothingItemAttr.KC,
                ClothingItemAttr.DTW, ClothingItemAttr.KJW);
    }

    public static List<ClothingItemAttr> getTzList() {
        return Arrays.asList(ClothingItemAttr.YC, ClothingItemAttr.LK, ClothingItemAttr.JK, ClothingItemAttr.XW,
                ClothingItemAttr.BW, ClothingItemAttr.XC, ClothingItemAttr.XK, ClothingItemAttr.XBW,
                ClothingItemAttr.TW, ClothingItemAttr.YW, ClothingItemAttr.ZDC, ClothingItemAttr.KC,
                ClothingItemAttr.DTW, ClothingItemAttr.KJW);
    }

    public static List<ClothingItemAttr> getLyqList() {
        return Arrays.asList(ClothingItemAttr.LK, ClothingItemAttr.JK, ClothingItemAttr.XW, ClothingItemAttr.BW,
                ClothingItemAttr.XC, ClothingItemAttr.XK, ClothingItemAttr.TW, ClothingItemAttr.YW, ClothingItemAttr.QC,
                ClothingItemAttr.XBW);
    }

    public static List<ClothingItemAttr> getDqList() {
        return Arrays.asList(ClothingItemAttr.TW, ClothingItemAttr.YW, ClothingItemAttr.QC, ClothingItemAttr.XBW);
    }

    public static List<ClothingItemAttr> getNyList() {
        return Arrays.asList(ClothingItemAttr.SXW, ClothingItemAttr.XXW, ClothingItemAttr.BX, ClothingItemAttr.YW,
                ClothingItemAttr.DDC);
    }

    public static List<ClothingItemAttr> getNkList() {
        return Arrays.asList(ClothingItemAttr.TW, ClothingItemAttr.YW, ClothingItemAttr.ZDC, ClothingItemAttr.JKS);
    }
}
