package com.estone.checkin.enums;

/**
 * 采购单完成状态
 */
public enum PurchaseFinishStatus {

    NOT_FINISH("未完结", "1"),

    FINISHED("已核销", "3"),;

    private String code;

    private String name;

    private PurchaseFinishStatus(String name, String code) {
        this.name = name;
        this.code = code;
    }

    public String getCode() {
        return code;
    }

    public Integer intCode() {
        return Integer.valueOf(code);
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public static String getNameByCode(String code) {
        PurchaseFinishStatus[] values = values();
        for (PurchaseFinishStatus type : values) {
            if (type.code.equals(code)) {
                return type.getName();
            }
        }
        return null;
    }
}
