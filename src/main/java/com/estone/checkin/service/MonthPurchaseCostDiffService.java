package com.estone.checkin.service;

import com.estone.checkin.bean.MonthPurchaseCostDiff;
import com.estone.checkin.bean.MonthPurchaseCostDiffQueryCondition;
import com.whq.tool.component.Pager;
import java.util.List;
import java.util.Map;

public interface MonthPurchaseCostDiffService {
    List<MonthPurchaseCostDiff> queryAllMonthPurchaseCostDiffs();

    List<MonthPurchaseCostDiff> queryMonthPurchaseCostDiffs(MonthPurchaseCostDiffQueryCondition query, Pager pager);

    MonthPurchaseCostDiff getMonthPurchaseCostDiff(Integer id);

    MonthPurchaseCostDiff getMonthPurchaseCostDiffDetail(Integer id);

    MonthPurchaseCostDiff queryMonthPurchaseCostDiff(MonthPurchaseCostDiffQueryCondition query);

    void createMonthPurchaseCostDiff(MonthPurchaseCostDiff monthPurchaseCostDiff);

    void batchCreateMonthPurchaseCostDiff(List<MonthPurchaseCostDiff> entityList);

    void deleteMonthPurchaseCostDiff(Integer id);

    void updateMonthPurchaseCostDiff(MonthPurchaseCostDiff monthPurchaseCostDiff);

    void batchUpdateMonthPurchaseCostDiff(List<MonthPurchaseCostDiff> entityList);

    /**
     * 更新采购单核销状态并计算每月采购运费差异
     */
    void updateStatusAndCountMonthPurchaseCostDiff();

    /**
     * 查询汇总
     * 
     * @param query
     * @return
     */
    Map<String, Object> queryMonthPurchaseCostDiffTotal(MonthPurchaseCostDiffQueryCondition query);
}