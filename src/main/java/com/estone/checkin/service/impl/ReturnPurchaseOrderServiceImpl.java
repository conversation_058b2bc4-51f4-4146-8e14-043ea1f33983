package com.estone.checkin.service.impl;

import com.estone.checkin.bean.*;
import com.estone.checkin.dao.ReturnPurchaseOrderDao;
import com.estone.checkin.domain.ReturnPurchaseOrderDo;
import com.estone.checkin.enums.ExceptionHandleWay;
import com.estone.checkin.enums.ExceptionStatus;
import com.estone.checkin.enums.ShippingCompanyEnum;
import com.estone.checkin.service.*;
import com.estone.common.util.TaglibUtils;
import com.estone.system.rabbitmq.model.PushCheckInException;
import com.estone.system.rabbitmq.model.PushReturnPurchaseOrderInfoToPmsMessage;
import com.estone.system.rabbitmq.producer.RabbitmqProducerService;
import com.estone.warehouse.service.WhBoxService;
import com.whq.tool.component.Pager;
import com.whq.tool.component.StatusCode;
import com.whq.tool.context.DataContextHolder;
import com.whq.tool.exception.BusinessException;
import com.whq.tool.exception.DBErrorCode;
import com.whq.tool.json.ResponseJson;
import com.whq.tool.sqler.SqlerException;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.util.*;
import java.util.stream.Collectors;

@Service("returnPurchaseOrderService")
public class ReturnPurchaseOrderServiceImpl implements ReturnPurchaseOrderService {
    private static final Logger logger = LoggerFactory.getLogger(ReturnPurchaseOrderServiceImpl.class);

    @Resource
    private ReturnPurchaseOrderDao returnPurchaseOrderDao;
    @Resource
    private WhCheckInExceptionService whCheckInExceptionService;

    @Resource
    private ReturnPurchaseOrderLogService returnPurchaseOrderLogService;
    @Resource
    private ReturnPurchaseOrderItemService returnPurchaseOrderItemService;

    @Resource
    private WhCheckInExceptionHandleService whCheckInExceptionHandleService;

    @Resource
    private RabbitmqProducerService rabbitmqProducerService;

    @Resource
    private WhBoxService whBoxService;

    /**
     * This method corresponds to the database table return_purchase_order
     *
     * @mbggenerated Mon Jul 15 18:01:34 CST 2019
     */
    public ReturnPurchaseOrder getReturnPurchaseOrder(Integer id) {
        ReturnPurchaseOrder returnPurchaseOrder = returnPurchaseOrderDao.queryReturnPurchaseOrder(id);
        return returnPurchaseOrder;
    }

    /**
     * This method corresponds to the database table return_purchase_order
     *
     * @mbggenerated Mon Jul 15 18:01:34 CST 2019
     */
    public ReturnPurchaseOrder getReturnPurchaseOrderDetail(Integer id) {
        ReturnPurchaseOrder returnPurchaseOrder = returnPurchaseOrderDao.queryReturnPurchaseOrder(id);
        // 关联查询
        return returnPurchaseOrder;
    }

    /**
     * This method corresponds to the database table return_purchase_order
     *
     * @mbggenerated Mon Jul 15 18:01:34 CST 2019
     */
    public ReturnPurchaseOrder queryReturnPurchaseOrder(ReturnPurchaseOrderQueryCondition query) {
        Assert.notNull(query);
        ReturnPurchaseOrder returnPurchaseOrder = returnPurchaseOrderDao.queryReturnPurchaseOrder(query);
        return returnPurchaseOrder;
    }

    /**
     * This method corresponds to the database table return_purchase_order
     *
     * @mbggenerated Mon Jul 15 18:01:34 CST 2019
     */
    public List<ReturnPurchaseOrder> queryAllReturnPurchaseOrders() {
        return returnPurchaseOrderDao.queryReturnPurchaseOrderList();
    }

    /**
     * This method corresponds to the database table return_purchase_order
     *
     * @mbggenerated Mon Jul 15 18:01:34 CST 2019
     */
    public List<ReturnPurchaseOrder> queryReturnPurchaseOrders(ReturnPurchaseOrderQueryCondition query, Pager pager) {
        Assert.notNull(query);
        if (pager != null && pager.isQueryCount()) {
            int count = returnPurchaseOrderDao.queryReturnPurchaseOrderCount(query);
            pager.setTotalCount(count);
            if (count == 0) {
                return new ArrayList<ReturnPurchaseOrder>();
            }
        }
        List<ReturnPurchaseOrder> returnPurchaseOrders = returnPurchaseOrderDao.queryReturnPurchaseOrderList(query,
                pager);
        return returnPurchaseOrders;
    }

    /**
     * This method corresponds to the database table return_purchase_order
     *
     * @mbggenerated Mon Jul 15 18:01:34 CST 2019
     */
    public void createReturnPurchaseOrder(ReturnPurchaseOrder returnPurchaseOrder) {
        try {
            returnPurchaseOrderDao.createReturnPurchaseOrder(returnPurchaseOrder);
        }
        catch (SqlerException e) {
            logger.error(e.getMessage(), e);
            throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
        }
    }

    /**
     * This method corresponds to the database table return_purchase_order
     *
     * @mbggenerated Mon Jul 15 18:01:34 CST 2019
     */
    public void batchCreateReturnPurchaseOrder(List<ReturnPurchaseOrder> entityList) {
        if (CollectionUtils.isNotEmpty(entityList)) {
            try {
                returnPurchaseOrderDao.batchCreateReturnPurchaseOrder(entityList);
            }
            catch (SqlerException e) {
                logger.error(e.getMessage(), e);
                throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
            }
        }
    }

    /**
     * This method corresponds to the database table return_purchase_order
     *
     * @mbggenerated Mon Jul 15 18:01:34 CST 2019
     */
    public void deleteReturnPurchaseOrder(Integer id) {
        try {
            returnPurchaseOrderDao.deleteReturnPurchaseOrder(id);
        }
        catch (SqlerException e) {
            logger.error(e.getMessage(), e);
            throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
        }
    }

    @Override
    public void batchDeleteReturnPurchaseOrder(List<Integer> ids){
        try{
            returnPurchaseOrderDao.batchDeleteReturnPurchaseOrder(ids);
        }catch (SqlerException e){
            logger.error(e.getMessage(), e);
            throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
        }
    }


    @Override
    public void deleteReturnPurchaseOrderByReturnNo(String returnNo) {
        try {
            returnPurchaseOrderDao.deleteReturnPurchaseOrderByReturnNo(returnNo);
        }
        catch (SqlerException e) {
            logger.error(e.getMessage(), e);
            throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
        }
    }

    /**
     * This method corresponds to the database table return_purchase_order
     *
     * @mbggenerated Mon Jul 15 18:01:34 CST 2019
     */
    public void updateReturnPurchaseOrder(ReturnPurchaseOrder returnPurchaseOrder) {
        try {
            returnPurchaseOrderDao.updateReturnPurchaseOrder(returnPurchaseOrder);
        }
        catch (SqlerException e) {
            logger.error(e.getMessage(), e);
            throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
        }
    }

    /**
     * This method corresponds to the database table return_purchase_order
     *
     * @mbggenerated Mon Jul 15 18:01:34 CST 2019
     */
    public void batchUpdateReturnPurchaseOrder(List<ReturnPurchaseOrder> entityList) {
        if (CollectionUtils.isNotEmpty(entityList)) {
            try {
                //通过Id更新数据
                List<ReturnPurchaseOrder> idList = entityList.stream()
                        .filter(v-> Objects.nonNull(v.getId()))
                        .collect(Collectors.toList());
                returnPurchaseOrderDao.batchUpdateReturnPurchaseOrderByPrimaryKey(idList);

                //通过退货单号+退货物流单号更新数据
                List<ReturnPurchaseOrder> noList = entityList.stream()
                        .filter(v -> !idList.contains(v))
                        .filter(v -> Objects.nonNull(v.getReturnOrderNo()) && Objects.nonNull(v.getReturnShippingOrderNo()))
                        .collect(Collectors.toList());
                returnPurchaseOrderDao.batchUpdateReturnPurchaseOrderByNos(noList);
            }
            catch (SqlerException e) {
                logger.error(e.getMessage(), e);
                throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
            }
        }
    }

    @Override
    public void batchOverrulePurchaseOrder(List<String> returnNos, String remark, String status) {
        if (CollectionUtils.isNotEmpty(returnNos) && StringUtils.isNotBlank(status)) {
            try {
                for (String returnNo : returnNos) {
                    returnPurchaseOrderDao.overrulePurchaseOrderByReturnNo(returnNo, remark, status);
                }
            }
            catch (SqlerException e) {
                logger.error(e.getMessage(), e);
                throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
            }
        }
    }

    @Override
    public void changeExceptionOrderStatusAndSendMsgToPms(List<ReturnPurchaseOrderItem> items, Integer status) {
        if (CollectionUtils.isNotEmpty(items)) {
            List<String> exceptionIds = new ArrayList<String>();
            for (ReturnPurchaseOrderItem item : items) {
                if (StringUtils.isNotBlank(item.getExceptionId())) {
                    exceptionIds.addAll(Arrays.asList(item.getExceptionId().split(",")));
                }
            }
            if (CollectionUtils.isNotEmpty(exceptionIds)) {
                for (String exceptionId : exceptionIds) {
                    WhCheckInExceptionQueryCondition query = new WhCheckInExceptionQueryCondition();
                    query.setId(Integer.parseInt(exceptionId));
                    WhCheckInException whCheckInException = whCheckInExceptionService.queryWhCheckInException(query);
                    if (whCheckInException != null
                            && ExceptionHandleWay.RETURN.intCode().equals(whCheckInException.getHandleWay())) {
                        WhCheckInException updateException = new WhCheckInException();
                        updateException.setId(whCheckInException.getId());
                        updateException.setStatus(status);
                        if (ExceptionStatus.RETURNED.intCode().equals(status))
                            updateException.setLocationNumber("");
                        whCheckInExceptionService.updateWhCheckInException(updateException);
                        if (ExceptionStatus.RETURNED.intCode().equals(status)) {
                            whCheckInExceptionService.updateLocationAuto(updateException,
                                    whCheckInException.getLocationNumber());
                        }

                        WhCheckInExceptionHandle handle = new WhCheckInExceptionHandle();
                        handle.setExceptionId(whCheckInException.getId());
                        handle.setCreatedBy(DataContextHolder.getUserId());
                        handle.setCreationDate(new Timestamp(System.currentTimeMillis()));
                        handle.setStatus(status);
                        if (ExceptionStatus.WAREHOUSE_PENDING.intCode().equals(whCheckInException.getStatus())) {
                            handle.setHandleComment("仓库已处理过退货单");
                        }
                        else if (ExceptionStatus.RETURNED.intCode().equals(whCheckInException.getStatus())) {
                            handle.setHandleComment("退货单已完成");
                        }
                        whCheckInExceptionHandleService.createWhCheckInExceptionHandle(handle);

                        // 解绑周转筐
                        String boxNo = whCheckInException.getBoxNo();
                        if (StringUtils.isNotBlank(boxNo) && ExceptionStatus.RETURNED.intCode().equals(status)) {
                            String[][] logs = new String[][] { { "仓库录入退货快递信息完成", "" },
                                    { "relationNo", whCheckInException.getId().toString() } };
                            int updated = whBoxService.updateWhBoxOfUnbinding(boxNo, logs);
                            if (updated >= 1) {
                                logger.info("仓库录入退货快递信息后释放周转码: boxNo[" + boxNo + "],  boxUpdated[" + updated + "]");
                            }
                        }

                        // 推送状态消息到采购系统
                        whCheckInException.setStatus(status);
                        logger.info(
                                "start to send exception message to pms ====exceptionId:" + whCheckInException.getId());
                        rabbitmqProducerService.pushCheckInExceptionMsgToPms(whCheckInException, handle,
                                new PushCheckInException());
                        logger.info("send exception message to pms end ");
                    }
                }
            }
        }
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResponseJson sendCheckMsgToPms(List<ReturnPurchaseOrder> returnPurchaseOrders) {
        ResponseJson responseJson = new ResponseJson(StatusCode.FAIL);
        if (CollectionUtils.isEmpty(returnPurchaseOrders)){
            responseJson.setMessage("要推送至采购系统的订单信息为空!");
            return responseJson;
        }
        String orderNo = "";
        for (ReturnPurchaseOrder returnPurchaseOrder : returnPurchaseOrders) {
            String returnPurchaseOrderNo = returnPurchaseOrder.getReturnOrderNo();
            if (StringUtils.isBlank(orderNo)) {
                orderNo = returnPurchaseOrderNo;
                continue;
            }
            if (!Objects.equals(orderNo, returnPurchaseOrderNo)) {
                responseJson.setMessage("包裹中退货单号不同");
                return responseJson;
            }
        }
        // 发送消息到采购
        List<PushReturnPurchaseOrderInfoToPmsMessage> pushReturnPurchaseOrderInfoToPmsMessageList = new ArrayList<>();
        for (ReturnPurchaseOrder returnPurchaseOrder : returnPurchaseOrders) {
            PushReturnPurchaseOrderInfoToPmsMessage pushReturnPurchaseOrderInfoToPmsMessage = new PushReturnPurchaseOrderInfoToPmsMessage();
            String remark = null;
            if (returnPurchaseOrder.getVolumeWeight() == null) {
                remark = "物流公司：" + ShippingCompanyEnum.getNameByCode(returnPurchaseOrder.getShippingCompany())
                        + ", 退货物流单号：" + returnPurchaseOrder.getReturnShippingOrderNo() + ", 重量："
                        + Float.parseFloat(returnPurchaseOrder.getWeight().toString()) + ", 运费："
                        + returnPurchaseOrder.getReturnShippingCost();
            } else {
                StringBuffer stringBuffer = new StringBuffer("物流公司：");
                stringBuffer.append(ShippingCompanyEnum.getNameByCode(returnPurchaseOrder.getShippingCompany()))
                        .append(", 退货物流单号：").append(returnPurchaseOrder.getReturnShippingOrderNo())
                        .append(", 长：").append(returnPurchaseOrder.getLength())
                        .append(", 宽：").append(returnPurchaseOrder.getWidth())
                        .append(", 高：").append(returnPurchaseOrder.getHeight())
                        .append(", 体积重：").append(returnPurchaseOrder.getVolumeWeight())
                        .append(", 运费：").append(returnPurchaseOrder.getReturnShippingCost());
                remark = stringBuffer.toString();
            }
            ReturnPurchaseOrderLog returnPurchaseOrderLog =returnPurchaseOrderLogService.saveHandleLog(
                    returnPurchaseOrder.getReturnOrderNo(), remark, "仓库编辑", returnPurchaseOrder.getReturnStatus());
            pushReturnPurchaseOrderInfoToPmsMessage.setReturnCheckRecord(returnPurchaseOrderLog);
            pushReturnPurchaseOrderInfoToPmsMessage.setReturnOrderNo(returnPurchaseOrder.getReturnOrderNo());
            pushReturnPurchaseOrderInfoToPmsMessage.setIsBack(false);
            pushReturnPurchaseOrderInfoToPmsMessage.setReturnShippingCost(returnPurchaseOrder.getReturnShippingCost());
            pushReturnPurchaseOrderInfoToPmsMessage.setReturnShippingOrderNo(returnPurchaseOrder.getReturnShippingOrderNo());
            pushReturnPurchaseOrderInfoToPmsMessage.setShippingCompany(
                    ShippingCompanyEnum.getNameByCode(returnPurchaseOrder.getShippingCompany()));
            pushReturnPurchaseOrderInfoToPmsMessage.setWeight(Float.parseFloat(returnPurchaseOrder.getWeight().toString()));
            pushReturnPurchaseOrderInfoToPmsMessageList.add(pushReturnPurchaseOrderInfoToPmsMessage);
        }

        rabbitmqProducerService.pushReturnPurchaseOrderMsgToPms(pushReturnPurchaseOrderInfoToPmsMessageList);
        // 编辑退货单保存后修改异常单状态为已退货，并且发送消息到采购
        ReturnPurchaseOrderItemQueryCondition queryCondition = new ReturnPurchaseOrderItemQueryCondition();
        queryCondition.setReturnOrderNo(orderNo);
        List<ReturnPurchaseOrderItem> returnPurchaseOrderItems = returnPurchaseOrderItemService.queryReturnPurchaseOrderItems(queryCondition, null);
        this.changeExceptionOrderStatusAndSendMsgToPms(returnPurchaseOrderItems, ExceptionStatus.RETURNED.intCode());
        responseJson.setStatus(StatusCode.SUCCESS);
        return responseJson;
    }

    @Override
    public void saveReturnPurchaseOrder(ReturnPurchaseOrderDo domain, ResponseJson responseJson) {
        List<ReturnPurchaseOrder> returnPurchaseOrders = domain.getReturnPurchaseOrders();
        if (CollectionUtils.isEmpty(returnPurchaseOrders)) {
            responseJson.setMessage("退货快递信息中包裹信息不能为空");
            return;
        }
        String orderNo = "";
        for (ReturnPurchaseOrder returnPurchaseOrder : returnPurchaseOrders) {
            if (StringUtils.isBlank(returnPurchaseOrder.getShippingCompany())
                    || StringUtils.isBlank(returnPurchaseOrder.getReturnShippingOrderNo())) {
                responseJson.setMessage("物流公司或物流单号不能为空");
                return;
            }
            String returnShippingOrderNo = returnPurchaseOrder.getReturnShippingOrderNo().replaceAll(" ", "");
            returnPurchaseOrder.setReturnShippingOrderNo(returnShippingOrderNo.trim());

            if (returnPurchaseOrder.getWidth() == null || returnPurchaseOrder.getLength() == null
                    || returnPurchaseOrder.getHeight() == null) {
                responseJson.setMessage(String.format("物流单号：%s,长宽高不能为空", returnShippingOrderNo));
                return;
            }

            String returnPurchaseOrderNo = returnPurchaseOrder.getReturnOrderNo();
            if (StringUtils.isBlank(returnPurchaseOrderNo)) {
                responseJson.setMessage("退货单号不能为空");
                return;
            }
            if (StringUtils.isBlank(orderNo)) {
                orderNo = returnPurchaseOrderNo;
                continue;
            }
            if (!Objects.equals(orderNo, returnPurchaseOrderNo)) {
                responseJson.setMessage("包裹中退货单号不同");
                return;
            }
        }
        logger.info("returnPurchaseOrder/updateReturnPurchaseOrder param:[" + orderNo + "]");

        for (ReturnPurchaseOrder returnPurchaseOrder : returnPurchaseOrders) {
            returnPurchaseOrder.setWmsHandleUser(TaglibUtils.getEmployeeNameByUserId(DataContextHolder.getUserId()));
            returnPurchaseOrder.setWmsHandleDate(new Timestamp(System.currentTimeMillis()));
            returnPurchaseOrder.setReturnStatus(ExceptionStatus.WAIT_WMS_CHECK.toString());
        }

        /**
         * 用于得到新增，删除，更新的包裹信息
         * 输入的数据对象中包含更新和新增的包裹信息
         * 数据库中的数据对象在排除了更新和新增的包裹信息后，就是需要进行删除的数据
         */
        ReturnPurchaseOrderQueryCondition query = new ReturnPurchaseOrderQueryCondition();
        query.setReturnOrderNo(orderNo);
        query.setDeleteFlag(false);
        List<ReturnPurchaseOrder> dbReturnPurchaseOrders = this.queryReturnPurchaseOrders(query, null);

        Set<String> dbReturnShippingOrderNos = dbReturnPurchaseOrders.stream()
                .map(ReturnPurchaseOrder::getReturnShippingOrderNo)
                .collect(Collectors.toSet());
        Set<Integer> dbReturnShippingIds = dbReturnPurchaseOrders.stream()
                .map(ReturnPurchaseOrder::getId)
                .collect(Collectors.toSet());

        // Id在数据库中或者shippingOrderNo在数据库中的数据即为更新的数据
        List<ReturnPurchaseOrder> updateReturnPurchaseOrders = returnPurchaseOrders.stream()
                .filter(returnPurchaseOrder -> dbReturnShippingIds.contains(returnPurchaseOrder.getId())
                        || dbReturnShippingOrderNos.contains(returnPurchaseOrder.getReturnShippingOrderNo()))
                .collect(Collectors.toList());
        // 排除进行更新的数据，剩下的就是新增的数据
        List<ReturnPurchaseOrder> insertReturnPurchaseOrders = returnPurchaseOrders.stream()
                .filter(returnPurchaseOrder -> {
                    if (updateReturnPurchaseOrders.contains(returnPurchaseOrder)) {
                        return false;
                    }
                    return true;
                })
                .collect(Collectors.toList());

        Set<String> returnShippingOrderNos = returnPurchaseOrders.stream()
                .map(ReturnPurchaseOrder::getReturnShippingOrderNo)
                .collect(Collectors.toSet());
        Set<Integer> returnShippingIds = returnPurchaseOrders.stream()
                .map(ReturnPurchaseOrder::getId)
                .collect(Collectors.toSet());
        // 排除了插入和修改的数据，数据库中剩下的即为删除的数据
        List<Integer> deleteReturnPurchaseOrderIds = dbReturnPurchaseOrders.stream()
                .filter(returnPurchaseOrder -> !returnShippingIds.contains(returnPurchaseOrder.getId())
                        && !returnShippingOrderNos.contains(returnPurchaseOrder.getReturnShippingOrderNo()))
                .map(ReturnPurchaseOrder::getId)
                .collect(Collectors.toList());

        this.batchUpdateReturnPurchaseOrder(updateReturnPurchaseOrders);
        this.batchDeleteReturnPurchaseOrder(deleteReturnPurchaseOrderIds);

        // 按照数据库中之前的数据填充要插入数据库的默认数据
        ReturnPurchaseOrder dbReturnPurchaseOrder = dbReturnPurchaseOrders.get(0);
        for (ReturnPurchaseOrder insertReturnPurchaseOrder : insertReturnPurchaseOrders) {
            insertReturnPurchaseOrder.setCreater(dbReturnPurchaseOrder.getCreater());
            insertReturnPurchaseOrder.setIsVendorBlame(dbReturnPurchaseOrder.getIsVendorBlame());
            insertReturnPurchaseOrder.setReturnReason(dbReturnPurchaseOrder.getReturnReason());
            insertReturnPurchaseOrder.setShippingCostBearer(dbReturnPurchaseOrder.getShippingCostBearer());
            insertReturnPurchaseOrder.setShippingCostType(dbReturnPurchaseOrder.getShippingCostType());
            insertReturnPurchaseOrder.setState(dbReturnPurchaseOrder.getState());
            insertReturnPurchaseOrder.setReturnAddress(dbReturnPurchaseOrder.getReturnAddress());
            insertReturnPurchaseOrder.setReceiver(dbReturnPurchaseOrder.getReceiver());
            insertReturnPurchaseOrder.setReceiverPhone(dbReturnPurchaseOrder.getReceiverPhone());
            insertReturnPurchaseOrder.setCreateTime(new Timestamp(System.currentTimeMillis()));
            insertReturnPurchaseOrder.setBackRemark(dbReturnPurchaseOrder.getBackRemark());
            insertReturnPurchaseOrder.setAccountantChecked(false);
            insertReturnPurchaseOrder.setDeleteFlag(false);
        }
        this.batchCreateReturnPurchaseOrder(insertReturnPurchaseOrders);

        String remark = "";
        for (ReturnPurchaseOrder returnPurchaseOrder : returnPurchaseOrders) {
            if (returnPurchaseOrder.getVolumeWeight() == null) {
                remark += " 物流公司：" + ShippingCompanyEnum.getNameByCode(returnPurchaseOrder.getShippingCompany())
                        + ", 退货物流单号：" + returnPurchaseOrder.getReturnShippingOrderNo() + ", 重量："
                        + Float.parseFloat(returnPurchaseOrder.getWeight().toString()) + ", 运费："
                        + returnPurchaseOrder.getReturnShippingCost();
            } else {
                StringBuffer stringBuffer = new StringBuffer(" 物流公司：");
                stringBuffer.append(ShippingCompanyEnum.getNameByCode(returnPurchaseOrder.getShippingCompany()))
                        .append(", 退货物流单号：").append(returnPurchaseOrder.getReturnShippingOrderNo())
                        .append(", 长：").append(returnPurchaseOrder.getLength())
                        .append(", 宽：").append(returnPurchaseOrder.getWidth())
                        .append(", 高：").append(returnPurchaseOrder.getHeight())
                        .append(", 体积重：").append(returnPurchaseOrder.getVolumeWeight())
                        .append(", 运费：").append(returnPurchaseOrder.getReturnShippingCost());
                remark += stringBuffer.toString();
            }
        }
        returnPurchaseOrderLogService.saveHandleLog(orderNo, remark, "仓库编辑", ExceptionStatus.WAIT_WMS_CHECK.toString());
        responseJson.setStatus(StatusCode.SUCCESS);
    }
}