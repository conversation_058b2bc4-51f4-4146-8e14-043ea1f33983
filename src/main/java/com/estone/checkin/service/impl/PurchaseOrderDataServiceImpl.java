package com.estone.checkin.service.impl;

import java.util.ArrayList;
import java.util.List;

import javax.annotation.Resource;

import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import com.estone.checkin.bean.PurchaseOrderData;
import com.estone.checkin.bean.PurchaseOrderDataQueryCondition;
import com.estone.checkin.dao.PurchaseOrderDataDao;
import com.estone.checkin.service.PurchaseOrderDataService;
import com.whq.tool.component.Pager;
import com.whq.tool.exception.BusinessException;
import com.whq.tool.exception.DBErrorCode;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import com.whq.tool.sqler.SqlerException;

@Service("purchaseOrderDataService")
public class PurchaseOrderDataServiceImpl implements PurchaseOrderDataService {
    private static final Logger logger = LoggerFactory.getLogger(PurchaseOrderDataServiceImpl.class);

    @Resource
    private PurchaseOrderDataDao purchaseOrderDataDao;

    /**
     * This method corresponds to the database table purchase_order_data
     *
     * @mbggenerated Wed Sep 26 16:13:16 CST 2018
     */
    public PurchaseOrderData getPurchaseOrderData(Integer id) {
        PurchaseOrderData purchaseOrderData = purchaseOrderDataDao.queryPurchaseOrderData(id);
        return purchaseOrderData;
    }

    /**
     * This method corresponds to the database table purchase_order_data
     *
     * @mbggenerated Wed Sep 26 16:13:16 CST 2018
     */
    public PurchaseOrderData getPurchaseOrderDataDetail(Integer id) {
        PurchaseOrderData purchaseOrderData = purchaseOrderDataDao.queryPurchaseOrderData(id);
        // 关联查询
        return purchaseOrderData;
    }

    /**
     * This method corresponds to the database table purchase_order_data
     *
     * @mbggenerated Wed Sep 26 16:13:16 CST 2018
     */
    public PurchaseOrderData queryPurchaseOrderData(PurchaseOrderDataQueryCondition query) {
        Assert.notNull(query);
        PurchaseOrderData purchaseOrderData = purchaseOrderDataDao.queryPurchaseOrderData(query);
        return purchaseOrderData;
    }

    /**
     * This method corresponds to the database table purchase_order_data
     *
     * @mbggenerated Wed Sep 26 16:13:16 CST 2018
     */
    public List<PurchaseOrderData> queryAllPurchaseOrderDatas() {
        return purchaseOrderDataDao.queryPurchaseOrderDataList();
    }

    /**
     * This method corresponds to the database table purchase_order_data
     *
     * @mbggenerated Wed Sep 26 16:13:16 CST 2018
     */
    public List<PurchaseOrderData> queryPurchaseOrderDatas(PurchaseOrderDataQueryCondition query, Pager pager) {
        Assert.notNull(query);
        if (pager != null && pager.isQueryCount())
        {
            int count = purchaseOrderDataDao.queryPurchaseOrderDataCount(query);
            pager.setTotalCount(count);
            if ( count == 0)
            {
                return new ArrayList<PurchaseOrderData>();
            }
        }
        List<PurchaseOrderData> purchaseOrderDatas = purchaseOrderDataDao.queryPurchaseOrderDataList(query, pager);
        return purchaseOrderDatas;
    }

    /**
     * This method corresponds to the database table purchase_order_data
     *
     * @mbggenerated Wed Sep 26 16:13:16 CST 2018
     */
    public void createPurchaseOrderData(PurchaseOrderData purchaseOrderData) {
        try
        {
            purchaseOrderDataDao.createPurchaseOrderData(purchaseOrderData);
        }
        catch (SqlerException e)
        {
            logger.error(e.getMessage(), e);
            throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
        }
    }

    /**
     * This method corresponds to the database table purchase_order_data
     *
     * @mbggenerated Wed Sep 26 16:13:16 CST 2018
     */
    public void batchCreatePurchaseOrderData(List<PurchaseOrderData> entityList) {
        if (CollectionUtils.isNotEmpty(entityList))
        {
            try
            {
                purchaseOrderDataDao.batchCreatePurchaseOrderData(entityList);
            }
            catch (SqlerException e)
            {
                logger.error(e.getMessage(), e);
                throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
            }
        }
    }

    /**
     * This method corresponds to the database table purchase_order_data
     *
     * @mbggenerated Wed Sep 26 16:13:16 CST 2018
     */
    public void deletePurchaseOrderData(Integer id) {
        try
        {
            purchaseOrderDataDao.deletePurchaseOrderData(id);
        }
        catch (SqlerException e)
        {
            logger.error(e.getMessage(), e);
            throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
        }
    }

    /**
     * This method corresponds to the database table purchase_order_data
     *
     * @mbggenerated Wed Sep 26 16:13:16 CST 2018
     */
    public void updatePurchaseOrderData(PurchaseOrderData purchaseOrderData) {
        try
        {
            purchaseOrderDataDao.updatePurchaseOrderData(purchaseOrderData);
        }
        catch (SqlerException e)
        {
            logger.error(e.getMessage(), e);
            throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
        }
    }

    /**
     * This method corresponds to the database table purchase_order_data
     *
     * @mbggenerated Wed Sep 26 16:13:16 CST 2018
     */
    public void batchUpdatePurchaseOrderData(List<PurchaseOrderData> entityList) {
        if (CollectionUtils.isNotEmpty(entityList))
        {
            try
            {
                purchaseOrderDataDao.batchUpdatePurchaseOrderData(entityList);
            }
            catch (SqlerException e)
            {
                logger.error(e.getMessage(), e);
                throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
            }
        }
    }
}