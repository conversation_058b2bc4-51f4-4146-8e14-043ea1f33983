package com.estone.checkin.service.impl;

import java.sql.Timestamp;
import java.util.*;
import java.util.concurrent.*;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import com.estone.apv.common.ApvLockType;
import com.estone.apv.common.ApvPlatform;
import com.estone.scan.deliver.bean.WhShippingMethod;
import com.estone.scan.deliver.bean.WhShippingMethodQueryCondition;
import com.estone.scan.deliver.service.WhShippingMethodService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.alibaba.fastjson.JSON;
import com.estone.apv.bean.WhApv;
import com.estone.apv.bean.WhApvItem;
import com.estone.apv.bean.WhApvOutStockChain;
import com.estone.apv.bean.WhApvQueryCondition;
import com.estone.apv.common.ApvOrderType;
import com.estone.apv.common.ApvStatus;
import com.estone.apv.enums.ApvTypeEnum;
import com.estone.apv.enums.WhApvOutStockChainStatusEnum;
import com.estone.apv.service.*;
import com.estone.checkin.bean.*;
import com.estone.checkin.enums.CheckInFlags;
import com.estone.checkin.enums.OutStockMatchStatus;
import com.estone.checkin.service.OutStockMatchHandelService;
import com.estone.checkin.service.PurchaseApvOutStockMatchService;
import com.estone.checkin.service.WhCheckInService;
import com.estone.checkin.service.WhPurchaseOrderService;
import com.estone.common.executors.ExecutorUtils;
import com.estone.common.util.*;
import com.estone.sku.service.WhSkuService;
import com.estone.sku.service.WhUniqueSkuService;
import com.estone.system.rabbitmq.bean.AmqMessage;
import com.estone.system.rabbitmq.model.ProductSkuMessage;
import com.estone.system.rabbitmq.producer.RabbitmqProducerService;
import com.estone.system.rabbitmq.service.AmqMessageService;
import com.estone.system.rabbitmq.utils.AssembleMessageDataUtils;
import com.estone.system.skuverifyweightdiff.util.DirectTimeSettingUtils;
import com.estone.transfer.bean.WhFbaPurchaseData;
import com.estone.transfer.bean.WhFbaPurchaseDataQueryCondition;
import com.estone.transfer.service.WhFbaPurchaseDataService;
import com.estone.warehouse.aspect.StockServicelock;
import com.estone.warehouse.bean.WhStock;
import com.estone.warehouse.bean.WhStockLog;
import com.estone.warehouse.bean.WhStockQueryCondition;
import com.estone.warehouse.enums.LocationTagEnum;
import com.estone.warehouse.enums.LocationType;
import com.estone.warehouse.enums.StockLogStep;
import com.estone.warehouse.enums.StockLogType;
import com.estone.warehouse.service.WhAllocateLocationRuleService;
import com.estone.warehouse.service.WhStockLogService;
import com.estone.warehouse.service.WhStockService;
import com.whq.tool.component.StatusCode;
import com.whq.tool.exception.BusinessException;
import com.whq.tool.json.ResponseJson;

import lombok.extern.slf4j.Slf4j;

/**
 * @Description:缺货订单预匹配采购单
 * @Author: Yimeil
 * @Date: 2024/4/2 17:34
 * @Version: 1.0.0
 */
@Slf4j
@Service
public class OutStockMatchHandelServiceImpl implements OutStockMatchHandelService {

    @Resource
    private PurchaseApvOutStockMatchService outStockMatchService;
    @Resource
    private WhPurchaseOrderService whPurchaseOrderService;

    @Resource
    private WhApvService whApvService;

    @Resource
    private WhAllocateLocationRuleService whAllocateLocationRuleService;

    @Resource
    private WhStockService stockService;
    @Resource
    private WhStockLogService whStockLogService;
    @Resource
    private WhApvOutStockChainService whApvOutStockChainService;
    @Resource
    private WhApvOutStockChainCancelService whApvOutStockChainCancelService;
    
    @Resource
    private WhApvItemService whApvItemService;
    @Resource
    private RabbitmqProducerService rabbitmqProducerService;

    @Resource
    private WhFbaPurchaseDataService whFbaPurchaseDataService;
    @Resource
    private WhApvLockService whApvLockService;
    @Resource
    private WhUniqueSkuService whUniqueSkuService;
    @Resource
    private AmqMessageService amqMessageService;
    @Resource
    private WhSkuService whSkuService;
    private final static ExecutorService executors = ExecutorUtils.newFixedThreadPool(2);

    private final static ExecutorService pushExecutors = ExecutorUtils.newFixedThreadPool(2);
    @Resource
    private WhCheckInService checkInService;
    @Resource
    private WhShippingMethodService whShippingMethodService;

    @Resource
    private WhApvStatusService whApvStatusService;

    @Override
    public Future<Boolean> matchApv(List<WhPurchaseOrder> purchaseOrders, String purchaseOrderNo) {
        if (CollectionUtils.isEmpty(purchaseOrders) && StringUtils.isBlank(purchaseOrderNo))
            return CompletableFuture.completedFuture(false);
        // 不入库直发，在非配置的时间段内不进行匹配操作
        if (!DirectTimeSettingUtils.checkTime()){
            return  CompletableFuture.completedFuture(false);
        }
        return executors.submit(() -> {
                Set<String> skuSet = new HashSet<>();
                List<WhPurchaseOrder> finalPurchaseOrders = purchaseOrders;
                if (StringUtils.isNotBlank(purchaseOrderNo)) {
                    WhPurchaseOrderQueryCondition purchaseQuery = new WhPurchaseOrderQueryCondition();
                    purchaseQuery.setPurchaseOrderNo(purchaseOrderNo);
                    finalPurchaseOrders = whPurchaseOrderService.queryWhPurchaseOrderAndItems(purchaseQuery, null);
                }
                finalPurchaseOrders.forEach(p -> {
                    if (CollectionUtils.isNotEmpty(p.getItems())) {
                        Set<String> skus = p.getItems().stream().map(WhPurchaseItem::getSku)
                                .collect(Collectors.toSet());
                        skuSet.addAll(skus);
                    }

                });
                matchOrder(skuSet, finalPurchaseOrders);
                return true;
        });
    }

    private void matchOrder(Set<String> skuSet, List<WhPurchaseOrder> purchaseOrders) {
        if (CollectionUtils.isEmpty(skuSet) || CollectionUtils.isEmpty(purchaseOrders))
            return;
        boolean existSku = skuSet.stream().anyMatch(
                sku -> StringUtils.isNotBlank(StringRedisUtils.get(RedisConstant.MATCH_OUT_STOCK_APV_KEY + sku)));
        if (existSku) {
            try {
                TimeUnit.SECONDS.sleep(3); // 休眠
            }
            catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                log.error(e.getMessage(), e);
            }
            matchOrder(skuSet, purchaseOrders);
        }
        else {
            handelMatchOrder(new ArrayList<>(skuSet), purchaseOrders);
        }
        skuSet.forEach(sku -> StringRedisUtils.del(RedisConstant.MATCH_OUT_STOCK_APV_KEY + sku));
    }

    private void handelMatchOrder(List<String> skuList, List<WhPurchaseOrder> purchaseOrders) {
        if (CollectionUtils.isEmpty(skuList) || CollectionUtils.isEmpty(purchaseOrders))
            return;
        PurchaseApvOutStockMatchQueryCondition query = new PurchaseApvOutStockMatchQueryCondition();
        query.setSkuList(skuList);
        query.setStatus(OutStockMatchStatus.WAIT_MATCH.intCode());
        List<PurchaseApvOutStockMatch> orderList = outStockMatchService.queryPurchaseApvOutStockMatchs(query, null);
        //根据物流方式 及 追踪号超时时间,判断追踪号是否超时
        orderList=matchOrderLogisticsInfoByDdNo(orderList);
        if (CollectionUtils.isEmpty(orderList))
            return;
        log.info("set redis :{}", Thread.currentThread().getName());
        skuList.forEach(sku -> StringRedisUtils.set(RedisConstant.MATCH_OUT_STOCK_APV_KEY + sku, sku, 10l));
        
        // 查询不备货SKU
//        WhSkuQueryCondition skuQuery = new WhSkuQueryCondition();
//        skuQuery.setSkus(skuList);
//        skuQuery.setNoStockUp(1);
//        List<WhSku> whSkus = whSkuService.queryWhSkus(skuQuery, null);
//        List<String> noStockUpSkuList = Optional.ofNullable(whSkus).orElse(new ArrayList<>()).stream()
//                .map(WhSku::getSku).collect(Collectors.toList());

        // 捞取已匹配的采购单对应的发货单
        PurchaseApvOutStockMatchQueryCondition matchedQuery = new PurchaseApvOutStockMatchQueryCondition();
        matchedQuery.setSkuList(skuList);
        matchedQuery.setStatus(OutStockMatchStatus.MATCHED.intCode());
        matchedQuery.setPurchaseOrderNoList(
                purchaseOrders.stream().map(WhPurchaseOrder::getPurchaseOrderNo).collect(Collectors.toList()));
        List<PurchaseApvOutStockMatch> matchedOrderList = outStockMatchService
                .queryPurchaseApvOutStockMatchs(matchedQuery, null);

        Map<String, Integer> matchedMap = Optional.ofNullable(matchedOrderList).orElse(new ArrayList<>()).stream()
                .collect(Collectors.groupingBy(o -> o.getPurchaseOrderNo() + o.getSku(),
                        Collectors.summingInt(
                                o -> o.getCheckInQty() == null ? o.getQuantity() == null ? 0 : o.getQuantity()
                                        : o.getCheckInQty())));

        Map<String, List<PurchaseApvOutStockMatch>> skuOrderMap = orderList.stream()
                .sorted(Comparator.comparing(o -> Optional.ofNullable(o.getQuantity()).orElse(0)))
                .collect(Collectors.groupingBy(PurchaseApvOutStockMatch::getSku));


        WhFbaPurchaseDataQueryCondition queryCondition = new WhFbaPurchaseDataQueryCondition();
        queryCondition.setPurchaseOrderList(
                purchaseOrders.stream().map(WhPurchaseOrder::getPurchaseOrderNo).collect(Collectors.toList()));
        List<WhFbaPurchaseData> whFbaPurchaseDataList = whFbaPurchaseDataService.queryWhFbaPurchaseDatas(queryCondition,
                null);

        Map<String, Integer> transferSkuMap = Optional.ofNullable(whFbaPurchaseDataList).orElse(new ArrayList<>()).stream()
                .filter(c -> c.getOrderQuantity() != null && !StringUtils.equalsIgnoreCase("local", c.getShipmentId()))
                .collect(Collectors.groupingBy(f -> f.getPurchaseorderno() + f.getSku(), Collectors.summingInt(WhFbaPurchaseData::getOrderQuantity)));

        List<PurchaseApvOutStockMatch> pushMatchList = new ArrayList<>();
        List<PurchaseApvOutStockMatch> updateMatchList = new ArrayList<>();
        for (WhPurchaseOrder order : purchaseOrders) {
            if (CollectionUtils.isEmpty(order.getItems()))
                continue;
            for (WhPurchaseItem item : order.getItems()) {
                // 不备货SKU，匹配缺货订单
//                if (CollectionUtils.isEmpty(noStockUpSkuList) || !noStockUpSkuList.contains(item.getSku()))
//                    continue;
                Integer pQty = item.getQuantity();
                if (pQty == null || pQty == 0)
                    continue;
                Integer fbaQty = transferSkuMap.get(order.getPurchaseOrderNo() + item.getSku());

                //排除中转仓采购数量
                if (fbaQty != null && fbaQty > 0) {
                    pQty = pQty - fbaQty;
                }

                if (pQty <= 0)
                    continue;

                Integer matchedQty = matchedMap.get(order.getPurchaseOrderNo() + item.getSku());
                if (matchedQty != null && matchedQty >= pQty)
                    continue;
                List<PurchaseApvOutStockMatch> stockMatches = skuOrderMap.get(item.getSku());
                if (CollectionUtils.isEmpty(stockMatches))
                    continue;
                List<Integer> moveIds = new ArrayList<>();
                for (PurchaseApvOutStockMatch apv : stockMatches) {
                    if (apv.getQuantity() == null || apv.getQuantity() == 0 || pQty < apv.getQuantity())
                        continue;
                    Integer needQty = apv.getQuantity();

                    PurchaseApvOutStockMatch matchOrder = new PurchaseApvOutStockMatch();
                    matchOrder.setId(apv.getId());
                    matchOrder.setStatus(OutStockMatchStatus.MATCHED.intCode());
                    matchOrder.setPurchaseOrderNo(order.getPurchaseOrderNo());
                    updateMatchList.add(matchOrder);
                    moveIds.add(apv.getId());
                    pushMatchList.add(apv);
                    pQty = pQty - needQty;
                }
                if (CollectionUtils.isNotEmpty(moveIds)) {
                    stockMatches.removeIf(p -> moveIds.contains(p.getId()));
                    skuOrderMap.put(item.getSku(), stockMatches);
                }

            }
        }
        outStockMatchService.batchUpdatePurchaseApvOutStockMatch(updateMatchList);
        if (CollectionUtils.isNotEmpty(pushMatchList)){
            pushExecutors.execute(() -> {
                ExecutorUtils.setThreadSuffix("push_sales_record_number");
                outStockMatchService.pushSalesRecordNumberToOMS(pushMatchList);
            });
        }
    }
    public List<PurchaseApvOutStockMatch> matchOrderLogisticsInfoByDdNo(List<PurchaseApvOutStockMatch> orderList) {
        if (CollectionUtils.isEmpty(orderList)) {
            return orderList;
        }
        List<String> deliverMethodList =orderList
                .stream()
                .map(PurchaseApvOutStockMatch::getDeliverMethod)
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(deliverMethodList)) {
            return orderList;
        }
        WhShippingMethodQueryCondition methodQuery = new WhShippingMethodQueryCondition();
        methodQuery.setCodeList(deliverMethodList);
        List<WhShippingMethod> shippingMethods = whShippingMethodService.queryWhShippingMethodDetailsList(methodQuery, null);
        if (CollectionUtils.isEmpty(shippingMethods)) {
            return orderList;
        }
        Map<String, WhShippingMethod> shippingMethodMap = shippingMethods.stream()
                .collect(Collectors.toMap(WhShippingMethod::getCode,m->m, (v1, v2) -> v1));

        List<PurchaseApvOutStockMatch> orderLogisticsInfoList = new ArrayList<>();
        List<PurchaseApvOutStockMatch> updateOrderList = new ArrayList<>();
        for (PurchaseApvOutStockMatch outStockMatch : orderList) {
            String deliverMethod = outStockMatch.getDeliverMethod();
            Timestamp syncLogisticsTime = outStockMatch.getSyncLogisticsTime();
            if (StringUtils.isBlank(deliverMethod) || syncLogisticsTime == null){
                orderLogisticsInfoList.add(outStockMatch);
                continue;
            }
            WhShippingMethod shippingMethod = shippingMethodMap.get(deliverMethod);
            if (shippingMethod == null || shippingMethod.getOverduedays() == null || shippingMethod.getOverduedays()<=0){
                orderLogisticsInfoList.add(outStockMatch);
                continue;
            }
            Double timeOutDay = shippingMethod.getOverduedays();// 超时时间(天)
            int overHour = DateUtils.getHoursBetween(syncLogisticsTime, new Date());
            if (overHour > timeOutDay * 24 - 2 * 24) {
                PurchaseApvOutStockMatch apvOutStockMatch = new PurchaseApvOutStockMatch();
                apvOutStockMatch.setId(outStockMatch.getId());
                apvOutStockMatch.setStatus(OutStockMatchStatus.NOT_STOCK_OUT.intCode());
                updateOrderList.add(apvOutStockMatch);
                continue;
            }
            orderLogisticsInfoList.add(outStockMatch);

        }
        if (CollectionUtils.isNotEmpty(updateOrderList)) {
            outStockMatchService.batchUpdatePurchaseApvOutStockMatch(updateOrderList);
        }
        return orderLogisticsInfoList;
    }

    /**
     * 特殊库位可用->已分配
     * @param skuList
     * @param checkInItem
     */
    @Override
    @StockServicelock
    public void allotStockApv(List<String> skuList, WhCheckInItem checkInItem) {
        if (CollectionUtils.isEmpty(skuList) || checkInItem == null || checkInItem.getItemId() == null
                || checkInItem.getQcQuantity() == null || checkInItem.getQcQuantity() == 0
                || checkInItem.getSkuId() == null)
            throw new RuntimeException("入库单上架匹配发货单分配发货单库存失败，参数错误！");
        Integer quantity = checkInItem.getQcQuantity();
        Integer zfStockId = checkInItem.getSkuId();
        // 按照上架库存分配缺货订单，优先分配发货量多的订单
        Integer pickStockId = NumberUtils.isNumber(checkInItem.getComment()) ? Integer.valueOf(checkInItem.getComment())
                : null;
        String sku = skuList.get(0);
        WhApvQueryCondition apvQuery = new WhApvQueryCondition();
        apvQuery.setSku(sku);
        apvQuery.setStatus(ApvStatus.WAITING_ALLOT.intCode());
        apvQuery.setShipStatus(ApvOrderType.LACK_MERCHANDISE.intCode());
        apvQuery.setApvType(ApvTypeEnum.SS.getCode() + "," + ApvTypeEnum.SM.getCode());
        apvQuery.setMoveStatus(true);
        List<WhApv> apvList = whApvService.queryWhApvAndItemList(apvQuery, null);
        if (CollectionUtils.isEmpty(apvList)) {
            log.error("没有获取到待分配的缺货订单：sku" + sku);
            Object existStr = StringRedisUtils.hGet(RedisConstant.ZF_NOT_ALLOT_CHECK_IN_LIST, String.valueOf(checkInItem.getItemId()));
            if (existStr == null) {
                StringRedisUtils.hSet(RedisConstant.ZF_NOT_ALLOT_CHECK_IN_LIST, String.valueOf(checkInItem.getItemId()), checkInItem);
            }
            return;
        }
        //缺货订单判断追踪号是否过期
        List<Integer> apvIds = new ArrayList<>();
        String errMsg =null;
        for (WhApv whApv : apvList) {
            String logisticsCompany = whApv.getLogisticsCompany();
            Timestamp shippingOrderNoSyncTime = whApv.getShipmentTime();
            if (StringUtils.isBlank(logisticsCompany) || shippingOrderNoSyncTime == null) {
                continue;
            }
            WhShippingMethod shippingMethod = whShippingMethodService.getWhShippingMethodDetailByShippingMethodCode(logisticsCompany);
            if (shippingMethod != null && shippingMethod.getOverduedays() != null) {
                log.warn("----检查追踪号是否过期-----sfmCode[" + shippingMethod.getCode() + "]追踪号时间["
                        + shippingOrderNoSyncTime + "]");
                Double timeOutDay = shippingMethod.getOverduedays();// 超时时间(天)
                if (timeOutDay <= 0) {
                    continue;
                }
                int overHour = DateUtils.getHoursBetween(shippingOrderNoSyncTime, new Date());
                if (overHour > timeOutDay * 24 - 2 * 24) {
                    apvIds.add(whApv.getId());
                    errMsg=" ——该订单追踪号还有两天过期自动取消!";
                }
            }
        }
        whApvStatusService.moveCancel(apvIds, new ResponseJson());
        apvList=apvList.stream().filter(i->!apvIds.contains(i.getId())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(apvList)) {
            log.error("入库单ID:"+checkInItem.getInId()+errMsg);
            Object existStr = StringRedisUtils.hGet(RedisConstant.ZF_NOT_ALLOT_CHECK_IN_LIST, String.valueOf(checkInItem.getItemId()));
            if (existStr == null) {
                StringRedisUtils.hSet(RedisConstant.ZF_NOT_ALLOT_CHECK_IN_LIST, String.valueOf(checkInItem.getItemId()), checkInItem);
            }
            return;
        }
        WhStockQueryCondition stockQuery = new WhStockQueryCondition();
        stockQuery.setSku(sku);
        stockQuery.setId(zfStockId);
        WhStock zfStock = stockService.queryWhStock(stockQuery);
        if (zfStock == null)
            throw new RuntimeException("入库单上架匹配发货单分配发货单库存失败，直发库位库存不存在！");
        if (zfStock.getSurplusQuantity() == null || zfStock.getSurplusQuantity() == 0)
            throw new RuntimeException("入库单上架匹配发货单分配发货单库存失败，直发库位没有可用库存！");
        // 发货单按数量排序
        List<WhApvItem> whApvItems = apvList.stream().map(apv -> {
            WhApvItem apvItem = apv.getWhApvItems().get(0);
            apvItem.setApvLineItemId(apv.getApvNo());
            apvItem.setSalesRecordNumber(apv.getSalesRecordNumber());
            apvItem.setSaleQuantity(apvItem.getSaleQuantity() == null ? 0 : apvItem.getSaleQuantity());
            return apvItem;
        }).sorted(Comparator.comparing(WhApvItem::getSaleQuantity).reversed()).collect(Collectors.toList());

        List<WhApv> updateApvList = new ArrayList<>();
        List<WhStockLog> whStockLogList = new ArrayList<>();
        List<WhApvOutStockChain> whApvOutStockChains = new ArrayList<>();
        WhStock updateZfStock = WhStock.buildUpdateStock(zfStock);

        Integer orzSurplusQty = zfStock.getSurplusQuantity();
        Integer orzAllotQty = zfStock.getAllotQuantity() == null ? 0 : zfStock.getAllotQuantity();
        Integer qty = quantity >= orzSurplusQty ? orzSurplusQty : quantity;
        for (WhApvItem item : whApvItems) {
            if (item.getSaleQuantity() == null || item.getSaleQuantity() == 0 || item.getSaleQuantity() > qty)
                continue;
            Integer needQty = item.getSaleQuantity();

            if (qty < needQty)
                continue;

            updateZfStock.setSurplusQuantity(orzSurplusQty - needQty);
            updateZfStock.setAllotQuantity(orzAllotQty + needQty);
            updateZfStock.setLastSurplusDate(new Timestamp(System.currentTimeMillis()));

            whStockLogList.add(new WhStockLog(updateZfStock.getSku(), StockLogType.USABLE_STOCK, updateZfStock.getId(),
                    updateZfStock.getLocationNumber(), StockLogStep.PURCHASE_PICK_UP, -needQty, orzSurplusQty,
                    item.getApvLineItemId()));
            whStockLogList.add(new WhStockLog(updateZfStock.getSku(), StockLogType.ALLOCATED, updateZfStock.getId(),
                    updateZfStock.getLocationNumber(), StockLogStep.PURCHASE_PICK_UP, needQty, orzAllotQty,
                    item.getApvLineItemId()));
            // 库存分配明细
            WhApvOutStockChain stockChain = new WhApvOutStockChain(item.getSku(), item.getApvLineItemId(),
                    WhApvOutStockChainStatusEnum.ALLOT.intCode(), zfStock.getId(), needQty, 0, 0);
            stockChain.setPickStockId(pickStockId);
            whApvOutStockChains.add(stockChain);

            WhApv whApv = new WhApv();
            whApv.setId(item.getApvId());
            whApv.setApvNo(item.getApvLineItemId());
            whApv.setSignPayment(false);
            whApv.setStatus(ApvStatus.ALLOT.intCode());// 已分配
            whApv.setDeliverDate(new Timestamp(System.currentTimeMillis())); // 分配库存时间
            updateApvList.add(whApv);
            qty = qty - needQty;
            orzSurplusQty = orzSurplusQty - needQty;
            orzAllotQty = orzAllotQty + needQty;
            if (qty <= 0 || orzSurplusQty <= 0)
                break;
        }

        if (CollectionUtils.isEmpty(updateApvList))
            return;

        // 保存出库明细 退换货存在多次分配
        List<WhApvOutStockChain> whApvOutStockChainList = whApvOutStockChainService.queryWhApvOutStockChains(
                updateApvList.stream().map(WhApv::getApvNo).collect(Collectors.toList()), null);
        if (CollectionUtils.isNotEmpty(whApvOutStockChainList))
            whApvOutStockChainService.deleteWhApvOutStockChainByIds(
                    whApvOutStockChainList.stream().map(WhApvOutStockChain::getId).collect(Collectors.toList()));
        whApvOutStockChainService.batchCreateWhApvOutStockChain(whApvOutStockChains);
        // 删除 移会待分配记录
        if (CollectionUtils.isNotEmpty(whApvOutStockChains))
            whApvOutStockChainCancelService.deleteWhApvOutStockChainCancelByRelevantNos(
                    whApvOutStockChains.stream().map(WhApvOutStockChain::getRelevantNo).collect(Collectors.toList()));

        stockService.updateWhStock(updateZfStock);
        whStockLogService.batchAddWhStockLog(whStockLogList);

        whApvService.batchUpdateWhApv(updateApvList); // 修改状态
        updateApvList.forEach(whApv -> {
            // 修改不缺货SKU whApvItem
            whApvItemService.batchUpdateWhApvItemByAllot(whApv.getId(), skuList, ApvStatus.WAITING_ALLOT.getCode());
            SystemLogUtils.APVLOG.log(whApv.getId(), "匹配直发库存发货单状态变更", new String[][] {
                    { "历史状态", ApvStatus.WAITING_ALLOT.getName() }, { "更改状态", ApvStatus.ALLOT.getName() } });
            // 发送MQ消息修改发货单状态
            rabbitmqProducerService.pushApvStatusToOms(whApv);
        });

        ProductSkuMessage productSkuMessage = new ProductSkuMessage();
        productSkuMessage.setSku(sku);
        productSkuMessage.setSurplusQuantity(quantity);
        AmqMessage amqMessage = AssembleMessageDataUtils.assembleStockDataDiff(productSkuMessage);
        //实时同步wms修改后的可用库存到pms
        amqMessageService.batchCreateAmqMessage(Collections.singletonList(amqMessage));
        // 缺货订单分配库存，推送大数据和Redis
        rabbitmqProducerService.lackOrderAllotStockToPush(whStockLogList);
    }


    @Override
    @StockServicelock
    @Transactional(propagation = Propagation.REQUIRED)
    public void allotStockApv(WhApv whApv) {
        if (null == whApv)
            return;
        log.warn("allot whApv: {}", JSON.toJSONString(whApv));
        whApv.setUpdateStatus(whApv.getStatus()); // 修改之前的状态
        List<String> skus = whApv.getWhApvItems().stream().map(WhApvItem::getSku).collect(Collectors.toList()); // 获取APV的SKU集合
        if (CollectionUtils.isEmpty(skus))
            return;
        Map<String, Integer> itemQtyMap = whApv.getWhApvItems().stream()
                .collect(Collectors.toMap(WhApvItem::getSku, WhApvItem::getSaleQuantity));

        String logisticsCompany = whApv.getLogisticsCompany();
        Timestamp shippingOrderNoSyncTime = whApv.getShipmentTime();
        WhShippingMethod shippingMethod =null;
        if (StringUtils.isNotBlank(logisticsCompany)) {
            shippingMethod = whShippingMethodService.getWhShippingMethodDetailByShippingMethodCode(logisticsCompany);
        }
        if (shippingMethod != null && shippingMethod.getOverduedays() != null && shippingOrderNoSyncTime != null) {
            log.warn("----检查追踪号是否过期-----sfmCode[" + shippingMethod.getCode() + "]追踪号时间["
                    + shippingOrderNoSyncTime + "]");
            Double timeOutDay = shippingMethod.getOverduedays();// 超时时间(天)
            int overHour = DateUtils.getHoursBetween(shippingOrderNoSyncTime, new Date());
            if (timeOutDay > 0 && overHour > timeOutDay * 24 - 2 * 24) {
                whApvStatusService.moveCancel(Collections.singletonList(whApv.getId()), new ResponseJson());
                return;
            }
        }
        WhStockQueryCondition query = new WhStockQueryCondition();
        query.setSkus(skus);
        query.setQueryLocationType(true);
        List<WhStock> whStocks = stockService.queryWhStocks(query, null);
        if (CollectionUtils.isEmpty(whStocks) || whStocks.stream()
                .noneMatch(s -> LocationType.ZHIFA.intCode().equals(s.getLocationType()))) {
            log.error("queryWhStocks result is null: " + skus);
            return;
        }
        Map<String, List<WhStock>> stockMap = whStocks.stream()
                .filter(s -> LocationType.ZHIFA.intCode().equals(s.getLocationType()))
                .collect(Collectors.groupingBy(WhStock::getSku));

        Map<String, List<WhStock>> pickMap = whStocks.stream()
                .filter(s -> LocationType.PICKING.intCode().equals(s.getLocationType()))
                .collect(Collectors.groupingBy(WhStock::getSku));

        // 查询已上架的入库单
        WhCheckInQueryCondition checkInQuery = new WhCheckInQueryCondition();
        checkInQuery.setSkus(skus);
        checkInQuery.setCheckInFlag(CheckInFlags.SHELF_STRAIGHT_HAIR.intCode());
        checkInQuery.setFromUpTime(DateUtils.dateToString(new Date(), DateUtils.DEFAULT_FORMAT + " 00:00:00"));
        checkInQuery.setToUpTime(DateUtils.dateToString(new Date(), DateUtils.DEFAULT_FORMAT + " 23:59:59"));
        List<WhCheckIn> checkInList = checkInService.queryWhCheckIns(checkInQuery, null);
        Map<Integer, List<WhCheckIn>> checkInMap = Optional.ofNullable(checkInList).orElse(new ArrayList<>()).stream()
                .collect(Collectors.groupingBy(c -> c.getWhCheckInItem().getSkuId()));

        // 库存变更记录
        List<WhStock> updateList = new ArrayList<>();
        // 库存日志变更记录
        List<WhStockLog> stockLogs = new ArrayList<>();
        // 库存分配明细
        List<WhApvOutStockChain> whApvOutStockChains = new ArrayList<>();
        
        for (Map.Entry<String, Integer> entry : itemQtyMap.entrySet()) {
            String sku = entry.getKey();
            Integer waitQty = entry.getValue();

            List<WhStock> stockList = stockMap.get(sku);
            List<WhStock> pickList = pickMap.get(sku);
            if (CollectionUtils.isEmpty(stockList))
                continue;

            for (WhStock whStock : stockList) {
                if (waitQty <= 0 || whStock.getSurplusQuantity() == null || whStock.getSurplusQuantity() == 0)
                    continue;

                List<WhCheckIn> whCheckIns = checkInMap.get(whStock.getId());
                Integer pickStockId = CollectionUtils.isEmpty(pickList) ? null : pickList.get(0).getId();
                if (CollectionUtils.isNotEmpty(whCheckIns) && whCheckIns.get(0).getWhCheckInItem() != null
                        && StringUtils.isNotBlank(whCheckIns.get(0).getWhCheckInItem().getComment()))
                    pickStockId = Integer.valueOf(whCheckIns.get(0).getWhCheckInItem().getComment());
                
                Integer surplusQuantity = whStock.getSurplusQuantity();
                WhStock updateStock = WhStock.buildUpdateStock(whStock);
                Integer upQty = waitQty - surplusQuantity > 0 ? surplusQuantity : waitQty;
                waitQty -= upQty;
                // 记录库存变更
                updateStock.setSurplusQuantity(surplusQuantity - upQty);
                int allotQty = whStock.getAllotQuantity() == null ? 0 : whStock.getAllotQuantity();
                updateStock.setAllotQuantity(allotQty + upQty);
                updateList.add(updateStock);
                // 记录库存日志变更
                stockLogs.add(new WhStockLog(whStock.getSku(), StockLogType.USABLE_STOCK, whStock.getId(),
                        whStock.getLocationNumber(), StockLogStep.APV_ALLOT, -upQty, surplusQuantity, whApv.getApvNo()));
                stockLogs.add(new WhStockLog(whStock.getSku(), StockLogType.ALLOCATED, whStock.getId(),
                        whStock.getLocationNumber(), StockLogStep.APV_ALLOT, upQty, allotQty, whApv.getApvNo()));
                // 库存分配明细
                WhApvOutStockChain stockChain = new WhApvOutStockChain(whStock.getSku(), whApv.getApvNo(),
                        WhApvOutStockChainStatusEnum.ALLOT.intCode(), whStock.getId(), upQty, 0, 0);
                stockChain.setPickStockId(pickStockId);
                whApvOutStockChains.add(stockChain);
            }
        }
        if (CollectionUtils.isEmpty(stockLogs))
            return;
        // 保存出库明细 退换货存在多次分配
        List<WhApvOutStockChain> whApvOutStockChainList = whApvOutStockChainService
                .queryWhApvOutStockChains(Collections.singletonList(whApv.getApvNo()), null);
        if (CollectionUtils.isNotEmpty(whApvOutStockChainList))
            whApvOutStockChainService.deleteWhApvOutStockChainByIds(
                    whApvOutStockChainList.stream().map(WhApvOutStockChain::getId).collect(Collectors.toList()));
        whApvOutStockChainService.batchCreateWhApvOutStockChain(whApvOutStockChains);
        // 删除 移会待分配记录
        if (CollectionUtils.isNotEmpty(whApvOutStockChains))
            whApvOutStockChainCancelService.deleteWhApvOutStockChainCancelByRelevantNos(
                    whApvOutStockChains.stream().map(WhApvOutStockChain::getRelevantNo).collect(Collectors.toList()));
        // 保存库存记录
        stockService.batchUpdateWhStock(updateList);
        // 保存库存日志
        whStockLogService.batchAddWhStockLog(stockLogs);
        Integer status = whApv.getStatus();
        whApv.setUpdateStatus(status);
        whApv.setStatus(ApvStatus.ALLOT.intCode());
        whApv.setDeliverDate(new Timestamp(System.currentTimeMillis())); // 分配库存时间
        int[] results = whApvService.updateApvStatusByPrimaryKey(Arrays.asList(whApv)); // 修改状态
        if (null == results || results[0] == 0) {
            // 修改apv状态失败回滚库存
            throw new RuntimeException("匹配直发库存扣库存更改apv发货单状态失败！apvNo: " + whApv.getApvNo());
        }
        // 修改不缺货SKU whApvItem
        whApvItemService.batchUpdateWhApvItemByAllot(whApv.getId(), skus, ApvStatus.WAITING_ALLOT.getCode());
        SystemLogUtils.APVLOG.log(whApv.getId(), "匹配直发库存发货单状态变更",
                new String[][] { { "历史状态", ApvStatus.getNameByCode(whApv.getUpdateStatus().toString()) },
                        { "更改状态", ApvStatus.ALLOT.getName() } });
        // 发送MQ消息修改发货单状态
        rabbitmqProducerService.pushApvStatusToOms(whApv);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED)
    public ResponseJson passPack(Integer apvId) {
        ResponseJson rsp = new ResponseJson(StatusCode.FAIL);
        if (apvId == null){
            rsp.setMessage("参数为空！");
            return rsp;
        }
        WhApvQueryCondition query = new WhApvQueryCondition();
        query.setId(apvId);
        query.setMoveStatus(true);
        query.setStatus(ApvStatus.ALLOT.intCode());
        List<WhApv> apvList = whApvService.queryWhApvAndItemList(query, null);
        if (CollectionUtils.isEmpty(apvList) || CollectionUtils.isEmpty(apvList.get(0).getWhApvItems())) {
            rsp.setMessage("没有找到对应已分配的的发货单！");
            return rsp;
        }
        //扣库存
        boolean updateStock = updateAllotToPick(apvList.get(0));
        if (!updateStock){
            throw new RuntimeException("包装分配库存失败！");
        }
        boolean passApv = whApvService.passZfProductScan(apvId);

        if (!passApv){
            throw new RuntimeException("修改发货单状态失败！");
        }

        rsp.setStatus(StatusCode.SUCCESS);
        return rsp;

    }

    /**
     * 发货单变更等待发货 减特殊库位已分配，加正常拣货库位已拣
     * 
     * @param whApv
     * @return
     */
    @StockServicelock
    private boolean updateAllotToPick(WhApv whApv) {
        if (null == whApv)
            return false;
        List<String> skus = whApv.getWhApvItems().stream().map(WhApvItem::getSku).collect(Collectors.toList()); // 获取APV的SKU集合
        if (CollectionUtils.isEmpty(skus)) {
            return false;
        }
        // 获取库存分配记录
        List<WhApvOutStockChain> whApvOutStockChains = whApvOutStockChainService
                .queryWhApvOutStockChains(CommonUtils.splitList(whApv.getApvNo(), ","), null);
        if (CollectionUtils.isEmpty(whApvOutStockChains)) {
            SystemLogUtils.APVLOG.log(whApv.getId(), "包装失败: [" + whApv.getApvNo() + "]无库存匹配记录!");
            throw new RuntimeException("包装失败: [" + whApv.getApvNo() + "]无库存匹配记录!");
        }
        Map<String, List<WhApvOutStockChain>> skuLocationMap = whApvOutStockChains.stream()
                .collect(Collectors.groupingBy(w -> w.getSku()));
        List<Integer> existZfIds = whApvOutStockChains.stream().map(WhApvOutStockChain::getStockId)
                .collect(Collectors.toList());
        WhStockQueryCondition stockQuery = new WhStockQueryCondition();
        stockQuery.setSkus(skus);
        stockQuery.setQueryLocationType(true);
        List<WhStock> whStocks = stockService.queryWhStocks(stockQuery, null);
        List<WhStock> existZf = Optional.ofNullable(whStocks).orElse(new ArrayList<>()).stream()
                .filter(s -> existZfIds.contains(s.getId())).collect(Collectors.toList());
        List<WhStock> existPick = Optional.ofNullable(whStocks).orElse(new ArrayList<>()).stream()
                .filter(s -> LocationType.PICKING.intCode().equals(s.getLocationType()))
                .collect(Collectors.toList());
        List<Integer> existPickIds = whApvOutStockChains.stream().filter(w -> w.getPickStockId() != null).map(WhApvOutStockChain::getPickStockId)
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(existPickIds)) {
            existPick = Optional.ofNullable(whStocks).orElse(new ArrayList<>()).stream()
                    .filter(s -> existPickIds.contains(s.getId())).collect(Collectors.toList());
        }
        if (CollectionUtils.isEmpty(existZf) || CollectionUtils.isEmpty(existPick))
            return false;
        Map<String, List<WhStock>> pickStockMap = existPick.stream().collect(Collectors.groupingBy(WhStock::getSku));
        Map<Integer, WhStock> zfStockMap = existZf.stream().collect(Collectors.toMap(WhStock::getId, s -> s));
        Map<String, WhStock> zfSkuStockMap = existZf.stream().collect(Collectors.toMap(WhStock::getSku, s -> s));

        List<WhStock> updateList = new ArrayList<>();
        List<WhStockLog> logList = new ArrayList<>();
        List<WhApvOutStockChain> updateChainList = new ArrayList<>();
        for (String sku : skus) {
            List<WhApvOutStockChain> stockChains = skuLocationMap.get(sku);
            if (CollectionUtils.isEmpty(whApvOutStockChains)) {
                throw new BusinessException("sku: " + sku + "库存分配记录为空");
            }
            WhStock zfAllotStock = zfSkuStockMap.get(sku);
            if (zfAllotStock == null) {
                throw new BusinessException("sku: " + sku + "直发分配库存记录为空！");
            }
            WhStock pickStock = CollectionUtils.isEmpty(pickStockMap.get(sku)) ? null : pickStockMap.get(sku).get(0);
            if (pickStock == null || StringUtils.isBlank(pickStock.getLocationNumber())) {
                boolean expSku = zfAllotStock.existLocationTag(LocationTagEnum.SHELF_LIFE);
                boolean afterSale = zfAllotStock.existLocationTag(LocationTagEnum.SALED_BALANCE);
                WhStock whStock = whAllocateLocationRuleService.matchSkuLocation(sku, null, expSku, afterSale);
                if (Objects.isNull(whStock)) {
                    whStock = whAllocateLocationRuleService.matchSpecialZfNewStock(sku, null);
                }
                if (Objects.isNull(whStock)) {
                    throw new BusinessException("sku: " + sku + "无正常拣货条目，请先创建库存记录");
                }
                if (StringUtils.isBlank(whStock.getLocationNumber()))
                    whStock = whAllocateLocationRuleService.matchSpecialZfNewStock(sku, whStock);
                pickStock = whStock;
            }

            int orgPickQty = pickStock.getPickQuantity() == null ? 0 : pickStock.getPickQuantity();

            WhStock updatePickStock = WhStock.buildUpdateStock(pickStock);

            for (WhApvOutStockChain stockChain : stockChains) {
                WhStock zfStock = zfStockMap.get(stockChain.getStockId());
                if (zfStock == null)
                    throw new BusinessException("sku: " + sku + "直发分配库存记录为空！");

                if (!LocationType.ZHIFA.intCode().equals(zfStock.getLocationType()))
                    throw new BusinessException("sku: " + sku + "已分配库存记录不是直发库位！");

                int orgAllotQty = zfStock.getAllotQuantity() == null ? 0 : zfStock.getAllotQuantity();

                int allot = stockChain.getQuantity() == null ? 0 : stockChain.getQuantity();

                if (allot == 0)
                    continue;

                if (orgAllotQty < allot)
                    throw new RuntimeException(String.format("SKU%s,直发分配%s,包装数量%s,直发分配库存不足！", sku, orgAllotQty, allot));

                WhStock updateZfStock = WhStock.buildUpdateStock(zfStock);
                updateZfStock.setAllotQuantity(orgAllotQty - allot);

                updatePickStock.setPickQuantity(orgPickQty + allot);

                logList.add(new WhStockLog(updatePickStock.getSku(), StockLogType.PICKED_STOCK, updatePickStock.getId(),
                        updatePickStock.getLocationNumber(), StockLogStep.APV_PACK, allot, orgPickQty,
                        whApv.getApvNo()));
                logList.add(new WhStockLog(updateZfStock.getSku(), StockLogType.ALLOCATED, updateZfStock.getId(),
                        updateZfStock.getLocationNumber(), StockLogStep.APV_PACK, -allot, orgAllotQty,
                        whApv.getApvNo()));
                updateList.add(updateZfStock);
                orgPickQty = orgPickQty + allot;

                WhApvOutStockChain updateChain = new WhApvOutStockChain();
                updateChain.setId(stockChain.getId());
                updateChain.setPickQuantity(allot);
                updateChain.setStockId(updatePickStock.getId());
                updateChainList.add(updateChain);

            }
            updateList.add(updatePickStock);
        }
        stockService.batchUpdateWhStock(updateList);
        whApvOutStockChainService.batchUpdateWhApvOutStockChain(updateChainList);
        whStockLogService.batchAddWhStockLog(logList);

        return true;
    }

    /**
     * 包装撤回
     * 
     * @param apvId
     * @return
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRED)
    public ResponseJson revokeApv(Integer apvId) {
        ResponseJson rsp = new ResponseJson(StatusCode.FAIL);
        if (apvId == null) {
            rsp.setStatus(StatusCode.SUCCESS);
            return rsp;
        }
        WhApvQueryCondition query = new WhApvQueryCondition();
        query.setId(apvId);
        List<WhApv> apvList = whApvService.queryWhApvAndItemList(query, null);
        if (CollectionUtils.isEmpty(apvList) || CollectionUtils.isEmpty(apvList.get(0).getWhApvItems())) {
            rsp.setStatus(StatusCode.SUCCESS);
            return rsp;
        }
        WhApv apv = apvList.get(0);

        if (apv.getStatus().equals(ApvStatus.CANCEL.intCode())) {
            rsp.setMessage("发货单已取消！");
            return rsp;
        }
        if (ApvTypeEnum.SM.getCode().equals(apv.getApvType())
                && apv.getStatus() >= ApvStatus.WAITING_DELIVER.intCode()) {
            rsp.setMessage("单品多件已包装不允许撤回！");
            return rsp;
        }

        if (apv.getStatus().equals(ApvStatus.ALLOT.intCode())) {
            whUniqueSkuService.revokePacking(apv.getApvNo());
            return rsp;
        }
        // 扣库存
        boolean updateStock = updatePickToAllot(apv);
        if (updateStock) {
            // 撤销回按货打单状态
            whApvLockService.revokeScanedWhApvByStatus(apvId, ApvStatus.ALLOT.intCode());
            whUniqueSkuService.revokePacking(apv.getApvNo());
            SystemLogUtils.APVLOG.log(apvId, "发货单状态变更  撤回扫描",
                    new String[][] { { "历史状态", ApvStatus.getNameByCode(ApvStatus.WAITING_DELIVER.getCode()) },
                            { "更改状态", ApvStatus.getNameByCode(ApvStatus.ALLOT.getCode()) } });
        }
        rsp.setStatus(StatusCode.SUCCESS);
        return rsp;
    }

    /**
     * 包装撤回退库存
     * @param whApv
     * @return
     */
    @StockServicelock
    private boolean updatePickToAllot(WhApv whApv) {
        if (null == whApv)
            return false;
        List<String> skus = whApv.getWhApvItems().stream().map(WhApvItem::getSku).collect(Collectors.toList()); // 获取APV的SKU集合
        if (CollectionUtils.isEmpty(skus)) {
            return false;
        }
        // 获取库存分配记录
        List<WhApvOutStockChain> whApvOutStockChains = whApvOutStockChainService
                .queryWhApvOutStockChains(CommonUtils.splitList(whApv.getApvNo(), ","), null);
        if (CollectionUtils.isEmpty(whApvOutStockChains)) {
            SystemLogUtils.APVLOG.log(whApv.getId(), "包装失败: [" + whApv.getApvNo() + "]无库存匹配记录!");
            throw new RuntimeException("包装失败: [" + whApv.getApvNo() + "]无库存匹配记录!");
        }
        Map<String, List<WhApvOutStockChain>> skuLocationMap = whApvOutStockChains.stream()
                .collect(Collectors.groupingBy(w -> w.getSku()));
        List<Integer> existPickIds = whApvOutStockChains.stream().map(WhApvOutStockChain::getStockId)
                .collect(Collectors.toList());

        WhStockQueryCondition stockQuery = new WhStockQueryCondition();
        stockQuery.setSkus(skus);
        stockQuery.setQueryLocationType(true);
        List<WhStock> whStocks = stockService.queryWhStocks(stockQuery, null);

        List<WhStock> existPick = Optional.ofNullable(whStocks).orElse(new ArrayList<>()).stream()
                .filter(s -> existPickIds.contains(s.getId())).collect(Collectors.toList());
        List<WhStock> existZf = Optional.ofNullable(whStocks).orElse(new ArrayList<>()).stream()
                .filter(s -> LocationType.ZHIFA.intCode().equals(s.getLocationType())).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(existZf) || CollectionUtils.isEmpty(existPick))
            return false;
        Map<Integer, WhStock> pickStockMap = existPick.stream().collect(Collectors.toMap(WhStock::getId, s -> s));
        Map<String, WhStock> zfStockMap = existZf.stream()
                .collect(Collectors.toMap(WhStock::getSku, s -> s, (s1, s2) -> s1));

        List<WhStock> updateList = new ArrayList<>();
        List<WhStockLog> logList = new ArrayList<>();
        List<WhApvOutStockChain> updateChainList = new ArrayList<>();
        for (String sku : skus) {
            List<WhApvOutStockChain> stockChains = skuLocationMap.get(sku);
            if (CollectionUtils.isEmpty(whApvOutStockChains)) {
                throw new BusinessException("sku: " + sku + "已拣库存记录为空");
            }
            for (WhApvOutStockChain stockChain : stockChains) {
                WhStock pickStock = pickStockMap.get(stockChain.getStockId());
                if (pickStock == null)
                    throw new BusinessException("sku: " + sku + "已拣库存记录为空！");
                WhStock zfStock = zfStockMap.get(stockChain.getSku());
                if (zfStock == null)
                    throw new BusinessException("sku: " + sku + "直发分配库存记录为空！");

                int orgPickQty = pickStock.getPickQuantity() == null ? 0 : pickStock.getPickQuantity();
                int pick = stockChain.getPickQuantity() == null ? 0 : stockChain.getPickQuantity();
                if (pick == 0)
                    continue;

                if (orgPickQty < pick)
                    throw new RuntimeException(String.format("SKU%s,需扣%s,已拣数量%s,已拣库存不足！", sku, pick, orgPickQty));

                int orgAllotQty = zfStock.getAllotQuantity() == null ? 0 : zfStock.getAllotQuantity();
                WhStock updatePickStock = WhStock.buildUpdateStock(pickStock);
                WhStock updateZfStock = WhStock.buildUpdateStock(zfStock);
                updateZfStock.setAllotQuantity(orgAllotQty + pick);
                updatePickStock.setPickQuantity(orgPickQty - pick);

                logList.add(new WhStockLog(updatePickStock.getSku(), StockLogType.PICKED_STOCK, updatePickStock.getId(),
                        updatePickStock.getLocationNumber(), StockLogStep.APV_PACK, -pick, orgPickQty,
                        whApv.getApvNo()));
                logList.add(new WhStockLog(updateZfStock.getSku(), StockLogType.ALLOCATED, updateZfStock.getId(),
                        updateZfStock.getLocationNumber(), StockLogStep.APV_PACK, pick, orgAllotQty, whApv.getApvNo()));
                updateList.add(updateZfStock);
                updateList.add(updatePickStock);

                WhApvOutStockChain updateChain = new WhApvOutStockChain();
                updateChain.setId(stockChain.getId());
                updateChain.setPickQuantity(0);
                updateChain.setStockId(updateZfStock.getId());
                updateChainList.add(updateChain);

            }
        }

        stockService.batchUpdateWhStock(updateList);
        whApvOutStockChainService.batchUpdateWhApvOutStockChain(updateChainList);
        whStockLogService.batchAddWhStockLog(logList);
        return true;
    }
}
