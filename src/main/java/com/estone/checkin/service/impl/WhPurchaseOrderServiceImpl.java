package com.estone.checkin.service.impl;

import static java.util.stream.Collectors.toList;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import com.estone.checkin.dao.WhCheckInExceptionDao;
import com.estone.common.util.HttpUtils;
import com.estone.common.util.model.ApiRequestParam;
import com.estone.common.util.model.HttpParams;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.estone.checkin.bean.*;
import com.estone.checkin.dao.WhPurchaseOrderDao;
import com.estone.checkin.enums.*;
import com.estone.checkin.service.*;
import com.estone.checkin.utils.GetUserNameOrEmployeeNameUtil;
import com.estone.checkin.utils.PurchaseOrderPercentageUtils;
import com.estone.checkin.utils.TranslateWhPurchaseOrderToPurchaseOrderUtils;
import com.estone.common.util.CacheUtils;
import com.estone.common.util.CommonUtils;
import com.estone.exquisite.bean.BoutiquePurchaseItem;
import com.estone.exquisite.bean.BoutiquePurchaseItemQueryCondition;
import com.estone.exquisite.bean.BoutiqueStock;
import com.estone.exquisite.bean.BoutiqueStockQueryCondition;
import com.estone.exquisite.service.BoutiquePurchaseItemService;
import com.estone.exquisite.service.BoutiqueStockService;
import com.estone.sku.bean.WhSkuExtend;
import com.estone.sku.bean.WhSkuExtendQueryCondition;
import com.estone.sku.bean.WhSkuSpecialGoods;
import com.estone.sku.bean.WhSkuSpecialGoodsQueryCondition;
import com.estone.sku.service.WhSkuExtendService;
import com.estone.sku.service.WhSkuSpecialGoodsService;
import com.estone.system.param.bean.SystemParam;
import com.estone.system.param.service.SystemParamService;
import com.estone.system.uniquenosegment.bean.WhSkuUniqueNoSegment;
import com.estone.system.uniquenosegment.service.WhSkuUniqueNoSegmentService;
import com.estone.transfer.bean.WhFbaPurchaseData;
import com.estone.transfer.bean.WhFbaPurchaseDataQueryCondition;
import com.estone.transfer.service.WhFbaPurchaseDataService;
import com.estone.warehouse.bean.WhStock;
import com.estone.warehouse.bean.WhStockQueryCondition;
import com.estone.warehouse.service.WhStockService;
import com.whq.tool.component.Pager;
import com.whq.tool.component.StatusCode;
import com.whq.tool.exception.BusinessException;
import com.whq.tool.exception.DBErrorCode;
import com.whq.tool.json.ResponseJson;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import com.whq.tool.sqler.SqlerException;

@Slf4j
@Service("whPurchaseOrderService")
public class WhPurchaseOrderServiceImpl implements WhPurchaseOrderService {
    private static final Logger logger = LoggerFactory.getLogger(WhPurchaseOrderServiceImpl.class);

    @Resource
    private WhPurchaseOrderDao whPurchaseOrderDao;

    @Resource
    private WhPurchaseItemService whPurchaseItemService;

    @Resource
    private WhPurchaseToExpressService whPurchaseToExpressService;

    @Resource
    private WhPurchaseExpressRecordService whPurchaseExpressRecordService;

    @Resource
    private WhCheckInExceptionService whCheckInExceptionService;

    @Resource
    private WhCheckInService whCheckInService;

    @Resource
    private WhStockService whStockService;

    @Resource
    private WhSkuSpecialGoodsService whSkuSpecialGoodsService;

    @Resource
    private SystemParamService systemParamService;

    @Resource
    private WhFbaPurchaseDataService whFbaPurchaseDataService;

    @Resource
    private PmsCheckInService pmsCheckInService;

    @Resource
    private WhSkuUniqueNoSegmentService whSkuUniqueNoSegmentService;

    @Resource
    private MaterialCheckInService materialCheckInService;

    @Resource
    private BoutiquePurchaseItemService boutiquePurchaseItemService;

    @Resource
    private BoutiqueStockService boutiqueStockService;

    @Resource
    private WhSkuExtendService whSkuExtendService;

    @Resource
    private WhCheckInExceptionDao whCheckInExceptionDao;

    /**
     * This method corresponds to the database table wh_purchase_order
     *
     * @mbggenerated Mon May 27 14:24:34 CST 2019
     */
    public WhPurchaseOrder getWhPurchaseOrder(Integer id) {
        WhPurchaseOrder whPurchaseOrder = whPurchaseOrderDao.queryWhPurchaseOrder(id);
        return whPurchaseOrder;
    }

    /**
     * This method corresponds to the database table wh_purchase_order
     *
     * @mbggenerated Mon May 27 14:24:34 CST 2019
     */
    public WhPurchaseOrder getWhPurchaseOrderDetail(Integer id) {
        WhPurchaseOrder whPurchaseOrder = whPurchaseOrderDao.queryWhPurchaseOrder(id);
        // 关联查询
        return whPurchaseOrder;
    }

    /**
     * This method corresponds to the database table wh_purchase_order
     *
     * @mbggenerated Mon May 27 14:24:34 CST 2019
     */
    public WhPurchaseOrder queryWhPurchaseOrder(WhPurchaseOrderQueryCondition query) {
        Assert.notNull(query);
        WhPurchaseOrder whPurchaseOrder = whPurchaseOrderDao.queryWhPurchaseOrder(query);
        return whPurchaseOrder;
    }

    /**
     * This method corresponds to the database table wh_purchase_order
     *
     * @mbggenerated Mon May 27 14:24:34 CST 2019
     */
    public List<WhPurchaseOrder> queryAllWhPurchaseOrders() {
        return whPurchaseOrderDao.queryWhPurchaseOrderList();
    }

    /**
     * This method corresponds to the database table wh_purchase_order
     *
     * @mbggenerated Mon May 27 14:24:34 CST 2019
     */
    public List<WhPurchaseOrder> queryWhPurchaseOrders(WhPurchaseOrderQueryCondition query, Pager pager) {
        Assert.notNull(query);
        if (pager != null && pager.isQueryCount()) {
            int count = whPurchaseOrderDao.queryWhPurchaseOrderCount(query);
            pager.setTotalCount(count);
            if (count == 0) {
                return new ArrayList<WhPurchaseOrder>();
            }
        }
        List<WhPurchaseOrder> whPurchaseOrders = whPurchaseOrderDao.queryWhPurchaseOrderList(query, pager);
        return whPurchaseOrders;
    }

    /**
     * This method corresponds to the database table wh_purchase_order
     *
     * @mbggenerated Mon May 27 14:24:34 CST 2019
     */
    public void createWhPurchaseOrder(WhPurchaseOrder whPurchaseOrder) {
        try {
            whPurchaseOrderDao.createWhPurchaseOrder(whPurchaseOrder);
        }
        catch (SqlerException e) {
            logger.error(e.getMessage(), e);
            throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
        }
    }

    /**
     * This method corresponds to the database table wh_purchase_order
     *
     * @mbggenerated Mon May 27 14:24:34 CST 2019
     */
    public void batchCreateWhPurchaseOrder(List<WhPurchaseOrder> entityList) {
        if (CollectionUtils.isNotEmpty(entityList)) {
            try {
                whPurchaseOrderDao.batchCreateWhPurchaseOrder(entityList);
            }
            catch (SqlerException e) {
                logger.error(e.getMessage(), e);
                throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
            }
        }
    }

    /**
     * This method corresponds to the database table wh_purchase_order
     *
     * @mbggenerated Mon May 27 14:24:34 CST 2019
     */
    public void deleteWhPurchaseOrder(Integer id) {
        try {
            whPurchaseOrderDao.deleteWhPurchaseOrder(id);
        }
        catch (SqlerException e) {
            logger.error(e.getMessage(), e);
            throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
        }
    }

    /**
     * This method corresponds to the database table wh_purchase_order
     *
     * @mbggenerated Mon May 27 14:24:34 CST 2019
     */
    public void updateWhPurchaseOrder(WhPurchaseOrder whPurchaseOrder) {
        try {
            whPurchaseOrderDao.updateWhPurchaseOrder(whPurchaseOrder);
        }
        catch (SqlerException e) {
            logger.error(e.getMessage(), e);
            throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
        }
    }

    /**
     * This method corresponds to the database table wh_purchase_order
     *
     * @mbggenerated Mon May 27 14:24:34 CST 2019
     */
    public void batchUpdateWhPurchaseOrder(List<WhPurchaseOrder> entityList) {
        if (CollectionUtils.isNotEmpty(entityList)) {
            try {
                whPurchaseOrderDao.batchUpdateWhPurchaseOrder(entityList);
            }
            catch (SqlerException e) {
                logger.error(e.getMessage(), e);
                throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
            }
        }
    }

    @Override
    public List<WhPurchaseOrder> queryWhPurchaseOrderAndItems(WhPurchaseOrderQueryCondition query, Pager pager) {
        Assert.notNull(query);
        if (pager != null && pager.isQueryCount()) {
            int count = whPurchaseOrderDao.queryWhPurchaseOrderAndItemCount(query);
            pager.setTotalCount(count);
            if (count == 0) {
                return new ArrayList<WhPurchaseOrder>();
            }
        }
        List<WhPurchaseOrder> whPurchaseOrders = whPurchaseOrderDao.queryWhPurchaseOrderAndItemList(query, pager);
        return whPurchaseOrders;
    }

    @Override
    public List<WhPurchaseOrder> queryWhPurchaseExpressOrderAndItemList(WhPurchaseOrderQueryCondition query,
            Pager pager) {
        Assert.notNull(query);
        if (pager != null && pager.isQueryCount()) {
            int count = whPurchaseOrderDao.queryWhPurchaseOrderAndItemCount(query);
            pager.setTotalCount(count);
            if (count == 0) {
                return new ArrayList<WhPurchaseOrder>();
            }
        }
        return whPurchaseOrderDao.queryWhPurchaseExpressOrderAndItemList(query, pager);
    }

    public boolean updateWhPurchaseOrderAndItemByPurchaseOrderNo(WhPurchaseOrder order) {
        Map<String, String> recordExpressIdMap = new HashMap<String, String>();
        List<String> recordPurchaseOrderNos = new ArrayList<String>();
        if (StringUtils.isBlank(order.getExpressId())) {
            order.setPurchaseStatus(WmsPurchaseOrderStatus.UNBIND.intCode());
        }
        else if ("Wait_Stock_In".equals(order.getChekinStatus())) {
            order.setPurchaseStatus(WmsPurchaseOrderStatus.WAIT_RECEIVE.intCode());
            // 查询相关收货单
            WhPurchaseExpressRecordQueryCondition recordQuery = new WhPurchaseExpressRecordQueryCondition();
            List<String> expressIds = new ArrayList<String>();
            CollectionUtils.addAll(expressIds, StringUtils.split(order.getExpressId(), ","));
            recordQuery.setTrackingNos(expressIds);
            List<WhPurchaseExpressRecord> expressRecords = whPurchaseExpressRecordService
                    .queryWhPurchaseExpressRecords(recordQuery, null);
            
            Optional.ofNullable(expressRecords).orElse(new ArrayList<>()).forEach(record -> {
                recordExpressIdMap.put(record.getTrackingNumber(), record.getComment());
                if (StringUtils.isNotBlank(record.getPurchaseOrderNo())) {
                    CollectionUtils.addAll(recordPurchaseOrderNos, StringUtils.split(record.getPurchaseOrderNo(), ","));
                }
            });
        }

        if ("Abandon".equals(order.getStatus())) {
            order.setPurchaseStatus(WmsPurchaseOrderStatus.ABANDON.intCode());
        }

        WhPurchaseOrderQueryCondition query = new WhPurchaseOrderQueryCondition();
        query.setPurchaseOrderNo(order.getPurchaseOrderNo());
        WhPurchaseOrder dbWhPurchaseOrder = whPurchaseOrderDao.queryWhPurchaseOrder(query);

        if (dbWhPurchaseOrder == null) {
            // 有新的采购人员时，刷新到redis和数据库
            String empName = order.getPurchaseUserName();
            if (StringUtils.isBlank(empName)){
                try {
                    SystemParam systemParam = CacheUtils.SystemParamGet("PURCHASE_USERS.PURCHASE_USERS");
                    SystemParam updateSystemParam = new SystemParam();
                    updateSystemParam.setParamId(systemParam.getParamId());
                    updateSystemParam.setParamKey("PURCHASE_USERS");
                    updateSystemParam.setParamValue("");
                    systemParamService.updateSystemParam(updateSystemParam);
                    GetUserNameOrEmployeeNameUtil.getPurchaseUser();
                } catch (Exception e) {
                    logger.warn("刷新采购人员失败!"+e.getMessage());
                }
            }

            whPurchaseOrderDao.createWhPurchaseOrder(order);
            //if (!StringUtils.startsWith(order.getPurchaseOrderNo(), PurchaseOrderType.NCGHC.getCode()))
                // 检验库存记录
                //doPurchaseSkuStock(order.getItems());
        }
        else {
            if (dbWhPurchaseOrder.getPurchaseStatus() != null
                    && dbWhPurchaseOrder.getPurchaseStatus() >= WmsPurchaseOrderStatus.WAIT_STOCK_IN.intCode()) {
                order.setPurchaseStatus(dbWhPurchaseOrder.getPurchaseStatus());
            }
            order.setId(dbWhPurchaseOrder.getId());
            whPurchaseOrderDao.updateWhPurchaseOrder(order);
        }

        /** 计算采购单重量占比,只计算已审核，且入库状态为待收货的采购单 **/
        if (StringUtils.isNotBlank(order.getJsonData())) {
            WhPurchaseOrder init = order.init();
            if (init.getIsdepconfirmed() != null && init.getIsdepconfirmed()
                    && "Wait_Stock_In".equals(order.getChekinStatus())) {
                PurchaseOrderPercentageUtils.calculatePercentage(order,null);
            }
        }

        if (dbWhPurchaseOrder != null || order.getId() != null) {
            /** 操作SKU **/
            doPurchaseSku(order, dbWhPurchaseOrder);

            /** 操作快递单 **/
            doPurchaseExpressId(order, dbWhPurchaseOrder);

            /** 收货管理页面有相关采购单,改采购单状态为“待入库”，快递单状态为“已签收” **/
            doExistRecordExpressOrder(order, recordExpressIdMap, recordPurchaseOrderNos);
            return true;
        }
        return false;
    }

    //更新入库异常单采购员
    private void updatePurchaseUser(WhPurchaseOrder order) {
        String purchaseUser = order.getPurchaseUser();
        if (StringUtils.isBlank(purchaseUser)) {
            return;
        }
        //更新采购单采购员
        WhPurchaseOrderQueryCondition query = new WhPurchaseOrderQueryCondition();
        query.setPurchaseOrderNo(order.getPurchaseOrderNo());
        List<WhPurchaseOrder> whPurchaseOrders = whPurchaseOrderDao.queryWhPurchaseOrderList(query, null);
        if (CollectionUtils.isNotEmpty(whPurchaseOrders)) {
            for (WhPurchaseOrder whPurchaseOrder : whPurchaseOrders) {
                whPurchaseOrder.setPurchaseUser(purchaseUser);
                whPurchaseOrderDao.updateWhPurchaseOrder(whPurchaseOrder);
            }
        }
        //更新入库单采购员
        WhCheckInExceptionQueryCondition condition = new WhCheckInExceptionQueryCondition();
        condition.setPurchaseOrderNo(order.getPurchaseOrderNo());
        List<WhCheckInException> checkInExceptions = whCheckInExceptionDao.queryWhCheckInExceptionList(condition, null);
        if (CollectionUtils.isNotEmpty(checkInExceptions)) {
            Integer purchaseUserId = GetUserNameOrEmployeeNameUtil.getUserId(purchaseUser);
            for (WhCheckInException checkInException : checkInExceptions) {
                checkInException.setPurchaseUser(purchaseUserId);
                whCheckInExceptionDao.updateWhCheckInException(checkInException);
            }
            //推送给采购系统
            // url = "192.168.8.223:8086/external/wms/inboundExceptionOrder/modifyPurchaseUser";
            try {
                String url = CacheUtils.SystemParamGet("PMS_PARAM.UPDATE_PURCHASE_USER").getParamValue();
                HttpParams<String> params = new HttpParams<String>();
                HttpHeaders httpHeaders = new HttpHeaders();
                httpHeaders.set("Authorization", HttpUtils.ACCESS_TOKEN);
                httpHeaders.set("Content-Type", "application/json");
                params.setHttpHeaders(httpHeaders);
                ApiRequestParam<String> param = new ApiRequestParam<>();
                List<Map<String,String>> list = new ArrayList<>();
                Map<String,String> map = new HashMap<>();
                map.put("purchaseOrderNo", order.getPurchaseOrderNo());
                map.put("purchaseUser", purchaseUser);
                map.put("handlerNoAndName", "system");
                list.add(map);
                param.setArgs(JSON.toJSONString(list));
                params.setHttpMethod(HttpMethod.POST);
                params.setUrl(url);
                params.setBody(JSON.toJSONString(param));
                log.info("推送采购参数：" + JSON.toJSONString(param));
                HttpUtils.exchange2ApiResult(params, String.class);
            } catch (Exception e) {
                log.error("采购系统更新采购员接口异常,{},{}", order.getPurchaseOrderNo(), e.getMessage());
            }
        }
    }

    /**
     * 检验库存记录
     * @param itemList
     */
    public void doPurchaseSkuStock(List<WhPurchaseItem> itemList) {
        if (CollectionUtils.isEmpty(itemList))
            return;
        List<String> skus = itemList.stream().filter(i -> i.getBoutique() == null || i.getBoutique() == 0)
                .map(item -> item.getSku()).collect(toList());
        if (CollectionUtils.isEmpty(skus))
            return;
        WhStockQueryCondition query = new WhStockQueryCondition();
        query.setSkus(skus);
        List<WhStock> stocks = whStockService.queryWhStocks(query, null);
        Map<String, List<WhStock>> skuMap = Optional.ofNullable(stocks).orElse(new ArrayList<>()).stream()
                .collect(Collectors.groupingBy(s -> s.getSku()));
        for (String sku : skus) {
            List<WhStock> whStockList = skuMap.get(sku);
            if (CollectionUtils.isNotEmpty(whStockList))
                continue;
            // 不纯在SKU库存记录就直接创建
            WhStock stock = new WhStock();
            stock.setSku(sku);
            stock.setLastUpdatedBy(1);
            whStockService.createWhStock(stock);

        }
    }

    /**
     * 操作SKU
     * 
     * @param order
     * @param dbWhPurchaseOrder
     */
    public void doPurchaseSku(WhPurchaseOrder order, WhPurchaseOrder dbWhPurchaseOrder) {
        String allRevoked = "";
        Map<String,Integer> checkInQuantityMap = new HashMap<>();
        if (StringUtils.startsWith(order.getPurchaseOrderNo(), PurchaseOrderType.NCGHC.getCode())) {
            MaterialCheckInQueryCondition mQuery = new MaterialCheckInQueryCondition();
            mQuery.setStatusList(Arrays.asList(MaterialCheckInStatus.WAITING_CHECK.intCode(),
                    MaterialCheckInStatus.WAITING_QC.intCode(), MaterialCheckInStatus.REJECT.intCode()));
            mQuery.setPurchaseOrderNo(order.getPurchaseOrderNo());
            checkInQuantityMap = materialCheckInService.querySkuCheckInQuantityCount(mQuery);
        }
        else {
            // 查询采购单对应的入库单（待QC,待上架，上架中，上架失败）sku入库数量
            WhCheckInQueryCondition query = new WhCheckInQueryCondition();
            String statusStr = CheckInStatus.WAITING_QC.intCode() + "," + CheckInStatus.WAITING_UP.intCode() + ","
                    + CheckInStatus.UPING.intCode() + "," + CheckInStatus.UPERROR.intCode();
            List<Integer> statuses = new ArrayList<>();
            CollectionUtils.addAll(statuses, StringUtils.split(statusStr, ","));
            query.setStatuses(statuses);
            query.setPurchaseOrderNo(order.getPurchaseOrderNo());
            checkInQuantityMap = whCheckInService.querySkuCheckInQuantityCount(query);
        }
        for (WhPurchaseItem item : order.getItems()) {
            //如果采购单价≥150元并且SKU属于新品，自动标记贵重物品
            Double price = Optional.ofNullable(item.getPrice()).orElse(0d);

            if (item.getFirstOrderType()!=null  && 1==item.getFirstOrderType() && price>=150){
                estimateValuables(item.getSku());
            }
            item.setpId(order.getId() == null ? dbWhPurchaseOrder.getId() : order.getId());
            item.setStatus(order.getStatus());
            item.setChekinStatus(order.getChekinStatus());
            Integer quantity = item.getQuantity() == null ? 0 : item.getQuantity();
            Integer upQuantity = item.getUpQuantity() == null ? 0 : item.getUpQuantity();
            Integer checkInQuantity = checkInQuantityMap.get(item.getSku()) == null ? 0 : checkInQuantityMap.get(item.getSku());
            Integer revokedQuantity = item.getRevokedQuantity() == null ? 0 : item.getRevokedQuantity();
            // 在途数量 = 采购数量-取消在途-上架数量-入库数量（待QC,待上架，上架中，上架失败）
            Integer onWayQuantity = quantity - revokedQuantity - upQuantity - checkInQuantity;
            if (item.getBoutique() != null && item.getBoutique() == 1) {
                onWayQuantity = quantity - revokedQuantity - upQuantity;
                onWayQuantity = onWayQuantity < 0 ? 0 : onWayQuantity;
            }
            item.setOnWayQuantity(onWayQuantity);
            item.setQuantity(quantity);
            item.setUpQuantity(upQuantity);
            item.setCheckinQuantity(checkInQuantity);
            item.setRevokedQuantity(revokedQuantity);

            WhPurchaseItemQueryCondition itemQuery = new WhPurchaseItemQueryCondition();
            itemQuery.setpId(order.getId() == null ? dbWhPurchaseOrder.getId() : order.getId());
            itemQuery.setSku(item.getSku());
            itemQuery.setBoutique(item.getBoutique());
            WhPurchaseItem dbItem = whPurchaseItemService.queryWhPurchaseItem(itemQuery);
            if (null == dbItem) {
                // 获取采购sku的特殊标签
                try {
                    WhSkuSpecialGoodsQueryCondition queryCondition = new WhSkuSpecialGoodsQueryCondition();
                    queryCondition.setSpecialSku(item.getSku());
                    List<WhSkuSpecialGoods> whSkuSpecialGoods = this.whSkuSpecialGoodsService.queryWhSkuSpecialGoodss(queryCondition, null);
                    if (CollectionUtils.isNotEmpty(whSkuSpecialGoods)){
                        item.setPurchaseSkuSpecialGoods( StringUtils.join(whSkuSpecialGoods.stream().map(i -> i.getSpecialType()).
                                collect(Collectors.toList()),","));
                    }
                } catch (Exception e) {
                    logger.error("消费采购消息时，获取采购sku标签失败",e);
                }
                // 采购SKU初始状态“待入库”
                item.setSkuStatus(PurchaseSkuStatus.WAIT_STOCK_IN.intCode());
                whPurchaseItemService.createWhPurchaseItem(item);
            }
            else {

                int itemRevokedQuantity = item.getRevokedQuantity() == null ? 0 : item.getRevokedQuantity();

                if (itemRevokedQuantity > 0 && dbItem.getQuantity().equals(itemRevokedQuantity)) {
                    allRevoked += ",true";
                    // 采购全部取消在途，变为已取消
                    item.setSkuStatus(PurchaseSkuStatus.CANCELED.intCode());
                }
                else if (itemRevokedQuantity == 0 || !dbItem.getQuantity().equals(itemRevokedQuantity)) {
                    allRevoked += ",false";
                    if (PurchaseSkuStatus.CANCELED.intCode().equals(dbItem.getSkuStatus())) {
                        // 采购全部取消在途后又重新修改取消在途，SKU状态回到“待入库”
                        item.setSkuStatus(PurchaseSkuStatus.WAIT_STOCK_IN.intCode());
                    }
                }
                item.setId(dbItem.getId());
                if (item.getBoutique() != null && item.getBoutique() == 1) {
                    BoutiquePurchaseItem boutiquePurchaseItem = new BoutiquePurchaseItem();
                    BeanUtils.copyProperties(item, boutiquePurchaseItem);
                    boutiquePurchaseItem.setCheckinQuantity(null);
                    boutiquePurchaseItemService.updateBoutiquePurchaseItem(boutiquePurchaseItem);
                }
                else {
                    whPurchaseItemService.updateWhPurchaseItem(item);
                }
            }
        }
        // 采购全部取消在途，变为已取消
        if (dbWhPurchaseOrder != null && !allRevoked.contains("false")) {
            order.setPurchaseStatus(WmsPurchaseOrderStatus.CANCELED.intCode());
            whPurchaseOrderDao.updateWhPurchaseOrder(order);
        }
        else if (dbWhPurchaseOrder != null
                && WmsPurchaseOrderStatus.CANCELED.intCode().equals(dbWhPurchaseOrder.getPurchaseStatus())) {
            if (StringUtils.isBlank(order.getExpressId())) {
                order.setPurchaseStatus(WmsPurchaseOrderStatus.UNBIND.intCode());
            }
            else if ("Wait_Stock_In".equals(order.getChekinStatus())) {
                order.setPurchaseStatus(WmsPurchaseOrderStatus.WAIT_RECEIVE.intCode());
            }
            whPurchaseOrderDao.updateWhPurchaseOrder(order);
        }

        WhPurchaseItemQueryCondition dbItemQuery = new WhPurchaseItemQueryCondition();
        dbItemQuery.setpId(order.getId() == null ? dbWhPurchaseOrder.getId() : order.getId());
        List<WhPurchaseItem> dbItemList = whPurchaseItemService.queryWhPurchaseItems(dbItemQuery, null);
        if (CollectionUtils.isNotEmpty((dbItemList))) {
            // 移除的SKU
            List<WhPurchaseItem> removeItems = dbItemList.stream().filter(item -> !order.getItems().stream()
                    .map(e -> e.getSku()).collect(Collectors.toList()).contains(item.getSku()))
                    .collect(Collectors.toList());

            removeItems.parallelStream()
                    .forEach(removeItem -> whPurchaseItemService.deleteWhPurchaseItem(removeItem.getId()));
        }

        BoutiquePurchaseItemQueryCondition itemQuery = new BoutiquePurchaseItemQueryCondition();
        BeanUtils.copyProperties(dbItemQuery, itemQuery);
        List<BoutiquePurchaseItem> dbJpItemList = boutiquePurchaseItemService.queryBoutiquePurchaseItems(itemQuery, null);
        if (CollectionUtils.isNotEmpty((dbJpItemList))){
            List<BoutiquePurchaseItem> removeJpItems = dbJpItemList.stream().filter(item -> !order.getItems()
                            .stream().map(e -> e.getSku()).collect(Collectors.toList()).contains(item.getSku()))
                    .collect(Collectors.toList());
            removeJpItems.parallelStream()
                    .forEach(removeItem -> boutiquePurchaseItemService.deleteBoutiquePurchaseItem(removeItem.getId()));
        }
    }

    private void estimateValuables(String sku){
        if (StringUtils.isBlank(sku))return;
        WhSkuExtendQueryCondition queryCondition = new WhSkuExtendQueryCondition();
        queryCondition.setSku(sku);
        List<WhSkuExtend> whSkuExtends = whSkuExtendService.queryWhSkuExtends(queryCondition, null);
        if (CollectionUtils.isEmpty(whSkuExtends)){
            WhSkuExtend whSkuExtend=new WhSkuExtend();
            whSkuExtend.setSku(sku);
            whSkuExtend.setTags("贵重物品(wms)");
            whSkuExtendService.createWhSkuExtend(whSkuExtend);
            return;
        }
        WhSkuExtend whSkuExtend = whSkuExtends.get(0);
        List<String> tagList = whSkuExtend.getTagList();
        if (tagList.contains("贵重物品(wms)")) {
            return;
        }
        tagList.add("贵重物品(wms)");
        WhSkuExtend update=new WhSkuExtend();
        update.setId(whSkuExtend.getId());
        update.setTags(StringUtils.join(tagList, ","));
        whSkuExtendService.updateWhSkuExtend(update);
    }

    /**
     * 操作快递单
     * 
     * @param order
     * @param dbWhPurchaseOrder
     */
    public void doPurchaseExpressId(WhPurchaseOrder order, WhPurchaseOrder dbWhPurchaseOrder) {
        if (StringUtils.isNotBlank(order.getExpressId())) {
            List<String> expressIdList = new ArrayList<>();
            CollectionUtils.addAll(expressIdList, StringUtils.split(order.getExpressId(), ","));

            expressIdList.forEach(expressId -> {
                WhPurchaseToExpress express = new WhPurchaseToExpress();
                express.setPurchaseOrderNo(order.getPurchaseOrderNo());
                express.setExpressId(expressId);
                int exceptionCount = 0;
                if (!StringUtils.startsWith(order.getPurchaseOrderNo(),PurchaseOrderType.NCGHC.getCode())){
                    WhCheckInExceptionQueryCondition exceptionQuery = new WhCheckInExceptionQueryCondition();
                    exceptionQuery.setTrackingNumber(expressId);
                    exceptionCount = whCheckInExceptionService.getExceptionCount(exceptionQuery);
                }

                WhPurchaseToExpressQueryCondition expressQuery = new WhPurchaseToExpressQueryCondition();
                expressQuery.setPurchaseOrderNo(order.getPurchaseOrderNo());
                expressQuery.setExpressId(expressId);
                WhPurchaseToExpress dbExpress = whPurchaseToExpressService.queryWhPurchaseToExpress(expressQuery);
                if (dbExpress == null) {
                    // 采购快递单初始状态“待签收”
                    express.setExpressStatus(PurchaseExpressStatus.WAIT_RECEIVE.intCode());
                    whPurchaseToExpressService.createWhPurchaseToExpress(express);
                }
                else {
                    if (exceptionCount > 0) {
                        express.setExpressStatus(PurchaseExpressStatus.PROCESSED.intCode());
                    }
                    express.setId(dbExpress.getId());
                    whPurchaseToExpressService.updateWhPurchaseToExpress(express);
                }
            });

            if (dbWhPurchaseOrder != null) {
                List<String> dbExpressIdList = new ArrayList<>();
                if (StringUtils.isNotBlank(dbWhPurchaseOrder.getExpressId())) {
                    CollectionUtils.addAll(dbExpressIdList, StringUtils.split(dbWhPurchaseOrder.getExpressId(), ","));
                    // 移除的SKU
                    List<String> removeExpressIds = dbExpressIdList.stream()
                            .filter(oldExpressId -> !expressIdList.contains(oldExpressId)).collect(toList());
                    removeExpressIds.parallelStream().forEach(removeExpressId -> {
                        WhPurchaseToExpressQueryCondition expressQuery = new WhPurchaseToExpressQueryCondition();
                        expressQuery.setPurchaseOrderNo(order.getPurchaseOrderNo());
                        expressQuery.setExpressId(removeExpressId);
                        WhPurchaseToExpress dbExpress = whPurchaseToExpressService
                                .queryWhPurchaseToExpress(expressQuery);
                        if (dbExpress != null) {
                            whPurchaseToExpressService.deleteWhPurchaseToExpress(dbExpress.getId());
                        }
                    });
                }
            }
        }
    }

    /**
     * 收货管理页面有相关采购单,改采购单状态为“待入库”，快递单状态为“已签收”
     * 
     * @param order
     * @param recordExpressIdMap
     * @param recordPurchaseOrderNos
     */
    public void doExistRecordExpressOrder(WhPurchaseOrder order, Map<String, String> recordExpressIdMap,
            List<String> recordPurchaseOrderNos) {
        if (CollectionUtils.isNotEmpty(recordPurchaseOrderNos)) {

            for (String recordPurchaseOrderNo : recordPurchaseOrderNos) {
                WhPurchaseOrderQueryCondition queryCondition = new WhPurchaseOrderQueryCondition();
                queryCondition.setPurchaseOrderNo(recordPurchaseOrderNo);
                WhPurchaseOrder sysOrder = whPurchaseOrderDao.queryWhPurchaseOrder(queryCondition);
                if (sysOrder != null && (sysOrder.getPurchaseStatus() != null
                        && sysOrder.getPurchaseStatus() < WmsPurchaseOrderStatus.WAIT_STOCK_IN.intCode())) {
                    WhPurchaseOrder updateOrder = new WhPurchaseOrder();
                    updateOrder.setId(sysOrder.getId());
                    updateOrder.setPurchaseStatus(WmsPurchaseOrderStatus.WAIT_STOCK_IN.intCode());
                    whPurchaseOrderDao.updateWhPurchaseOrder(order);
                }
            }
        }

        if (recordExpressIdMap != null) {
            for (String recordExpressId : recordExpressIdMap.keySet()) {
                WhPurchaseToExpressQueryCondition expressQuery = new WhPurchaseToExpressQueryCondition();
                expressQuery.setPurchaseOrderNo(order.getPurchaseOrderNo());
                expressQuery.setExpressId(recordExpressId);
                WhPurchaseToExpress dbExpress = whPurchaseToExpressService.queryWhPurchaseToExpress(expressQuery);
                if (dbExpress != null) {
                    int exceptionCount = 0;
                    int checkInCount = 0;
                    if (StringUtils.startsWith(order.getPurchaseOrderNo(), PurchaseOrderType.NCGHC.getCode())) {
                        MaterialCheckInQueryCondition mQuery = new MaterialCheckInQueryCondition();
                        mQuery.setTrackingNumber(dbExpress.getExpressId());
                        checkInCount = materialCheckInService.queryMaterialCheckInCount(mQuery);
                    }
                    else {
                        WhCheckInExceptionQueryCondition exceptionQuery = new WhCheckInExceptionQueryCondition();
                        exceptionQuery.setTrackingNumber(dbExpress.getExpressId());
                        exceptionCount = whCheckInExceptionService.getExceptionCount(exceptionQuery);

                        WhCheckInQueryCondition checkInQuery = new WhCheckInQueryCondition();
                        checkInQuery.setTrackingNumber(dbExpress.getExpressId());
                        checkInCount = whCheckInService.queryWhCheckInCount(checkInQuery);
                    }

                    WhPurchaseToExpress express = new WhPurchaseToExpress();
                    express.setId(dbExpress.getId());
                    if (exceptionCount > 0 || checkInCount > 0
                            || StringUtils.isNotBlank(recordExpressIdMap.get(recordExpressId))) {
                        // 收货单有备注，有异常数据，有入库单，采购快递单变为“已处理”
                        express.setExpressStatus(PurchaseExpressStatus.PROCESSED.intCode());
                    }
                    else {
                        // 收货单没有备注，采购单变为“待处理”
                        express.setExpressStatus(PurchaseExpressStatus.TO_BE_PROCESSED.intCode());
                    }
                    if (dbExpress.getExpressStatus() == null
                            || dbExpress.getExpressStatus() < express.getExpressStatus()) {
                        whPurchaseToExpressService.updateWhPurchaseToExpress(express);
                    }
                }
            }
        }
    }

    @Override
    public ResponseJson updatePushWhPurchaseOrders(List<WhPurchaseOrder> orders) {
        ResponseJson response = new ResponseJson();
        response.setStatus(StatusCode.FAIL);
        List<String> failedNos = new ArrayList<>();
        for (WhPurchaseOrder order : orders) {
            if (StringUtils.isBlank(order.getPurchaseOrderNo())) {
                continue;
            }
            boolean result = false;

            if (CollectionUtils.isNotEmpty(order.getItems())) {

                result = updateWhPurchaseOrderAndItemByPurchaseOrderNo(order);
                //海外仓 生成货件采购数据
                if (result) {
                    WhFbaPurchaseDataQueryCondition queryCondition = new WhFbaPurchaseDataQueryCondition();
                    queryCondition.setPurchaseorderno(order.getPurchaseOrderNo());
                    List<WhFbaPurchaseData> whFbaPurchaseDataList = whFbaPurchaseDataService
                            .queryWhFbaPurchaseDatas(queryCondition, null);
                    if (CollectionUtils.isEmpty(whFbaPurchaseDataList)) {
                        Map<String, WhFbaPurchaseData> whFbaPurchaseDataMap = new HashMap<>();
                        List<WhPurchaseItem> items = order.getItems();
                        for (WhPurchaseItem item : items) {
                            if (item.getBoutique()!=null && item.getBoutique()==1 &&  CollectionUtils.isEmpty(item.getParcelInfoList()) && PurchaseOrderType.NCGYP.getCode().equalsIgnoreCase(order.getPurchaseOrderType())){
                                createSampleInfo(whFbaPurchaseDataMap,item,order);
                                continue;
                            }
                            if (CollectionUtils.isEmpty(item.getParcelInfoList()))
                                break;
                            item.getParcelInfoList().stream().forEach(p -> {
                                p.setAccountNumber(p.getUnpreixedAccountNumber());

                                WhFbaPurchaseData whFbaPurchaseData = whFbaPurchaseDataMap
                                        .get(item.getSku() + p.getAccountNumber());
                                Integer pQty = p.getParcelQuantity() == null ? 0 : p.getParcelQuantity();
                                if (whFbaPurchaseData == null) {
                                    whFbaPurchaseData = new WhFbaPurchaseData();
                                    whFbaPurchaseData.setPurchaseorderno(order.getPurchaseOrderNo());
                                    whFbaPurchaseData.setSku(item.getSku());
                                    if (StringUtils.isNotBlank(p.getParcelNo())) {
                                        String shipmentId = CommonUtils.splitList(p.getParcelNo(), ",").stream()
                                                .distinct().collect(Collectors.joining(","));
                                        whFbaPurchaseData.setShipmentId(shipmentId);
                                    }
                                    whFbaPurchaseData.setOrderQuantity(pQty);
                                    // 添加店铺账号字段
                                    whFbaPurchaseData.setAccountNumber(p.getAccountNumber());
                                    whFbaPurchaseDataMap.put(item.getSku() + p.getAccountNumber(), whFbaPurchaseData);
                                }
                                else {
                                    whFbaPurchaseData.setOrderQuantity(whFbaPurchaseData.getOrderQuantity() + pQty);
                                    List<String> shipmentIds = CommonUtils
                                            .splitList(whFbaPurchaseData.getShipmentId() + "," + p.getParcelNo(), ",");
                                    whFbaPurchaseData.setShipmentId(
                                            shipmentIds.stream().distinct().collect(Collectors.joining(",")));
                                }
                            });

                        }
                        if (MapUtils.isNotEmpty(whFbaPurchaseDataMap))
                            whFbaPurchaseDataService
                                    .batchCreateWhFbaPurchaseData(new ArrayList<>(whFbaPurchaseDataMap.values()));
                    }
                    else if (!PurchaseOrderType.NCGHW.getCode().equalsIgnoreCase(order.getPurchaseOrderType())) {
                        Map<String, WhFbaPurchaseData> whFbaPurchaseDataMap = whFbaPurchaseDataList.stream()
                                .collect(Collectors.toMap(
                                        item -> (item.getSku() + "-" + item.getShipmentId() + item.getAccountNumber())
                                                .toUpperCase(),
                                        item2 -> item2, (i1, i2) -> i1));
                        Map<String, WhFbaPurchaseData> createWhFbaPurchaseDatas = new HashMap<>();
                        Map<String, WhFbaPurchaseData> updateWhFbaPurchaseDatas = new HashMap<>();
                        List<WhPurchaseItem> items = order.getItems();
                        for(WhPurchaseItem item:items){
                            if (item.getBoutique()!=null && item.getBoutique()==1 &&  CollectionUtils.isEmpty(item.getParcelInfoList()) && PurchaseOrderType.NCGYP.getCode().equalsIgnoreCase(order.getPurchaseOrderType())){
                                Map<String, WhFbaPurchaseData> purchaseDataMap = whFbaPurchaseDataList.stream().collect(Collectors.
                                        toMap(i -> i.getSku(), Function.identity()));
                                WhFbaPurchaseData whFbaPurchaseData = purchaseDataMap.get(item.getSku());
                                if (whFbaPurchaseData==null){
                                    createSampleInfo(createWhFbaPurchaseDatas,item,order);
                                }else{
                                    createSampleInfo(updateWhFbaPurchaseDatas,item,order);
                                }
                                continue;
                            }

                            if (CollectionUtils.isEmpty(item.getParcelInfoList())) break;
                            // 存在更新数量
                            item.getParcelInfoList().stream().forEach(p -> {
                                p.setAccountNumber(p.getUnpreixedAccountNumber());
                                String mapKey = (item.getSku() + "-" + p.getParcelNo() + p.getAccountNumber()).toUpperCase();
                                WhFbaPurchaseData exist = whFbaPurchaseDataMap.get(mapKey);
                                if (exist == null) {
                                    WhFbaPurchaseData whFbaPurchaseData = createWhFbaPurchaseDatas.get(mapKey);
                                    int pQty = p.getParcelQuantity() == null ? 0 : p.getParcelQuantity();
                                    if (whFbaPurchaseData == null) {
                                        whFbaPurchaseData = new WhFbaPurchaseData();
                                        whFbaPurchaseData.setPurchaseorderno(order.getPurchaseOrderNo());
                                        whFbaPurchaseData.setSku(item.getSku());
                                        whFbaPurchaseData.setShipmentId(p.getParcelNo());
                                        whFbaPurchaseData.setOrderQuantity(pQty);
                                        // 添加店铺账号字段
                                        whFbaPurchaseData.setAccountNumber(p.getAccountNumber());
                                        createWhFbaPurchaseDatas.put(mapKey, whFbaPurchaseData);
                                    }
                                    else {
                                        whFbaPurchaseData.setOrderQuantity(whFbaPurchaseData.getOrderQuantity() + pQty);
                                    }
                                }
                                else {
                                    WhFbaPurchaseData updatePurchaseData = updateWhFbaPurchaseDatas.get(mapKey);
                                    int pQty = p.getParcelQuantity() == null ? 0 : p.getParcelQuantity();
                                    if (updatePurchaseData == null) {
                                        updatePurchaseData = new WhFbaPurchaseData();
                                        updatePurchaseData.setId(exist.getId());
                                        updatePurchaseData.setOrderQuantity(pQty);
                                        updateWhFbaPurchaseDatas.put(mapKey, updatePurchaseData);
                                    }
                                    else {
                                        updatePurchaseData
                                                .setOrderQuantity(updatePurchaseData.getOrderQuantity() + pQty);
                                    }
                                }
                            });

                        }
                        if (MapUtils.isNotEmpty(createWhFbaPurchaseDatas))
                            whFbaPurchaseDataService.batchCreateWhFbaPurchaseData(new ArrayList<>(createWhFbaPurchaseDatas.values()));
                        if (MapUtils.isNotEmpty(updateWhFbaPurchaseDatas))
                            whFbaPurchaseDataService.batchUpdateWhFbaPurchaseData(new ArrayList<>(updateWhFbaPurchaseDatas.values()));
                    }
                }
            } else if (BooleanUtils.isTrue(order.getIsModifyPurchaseUser())) {
                //更新采购单和异常单的采购员
                try {
                    updatePurchaseUser(order);
                    result = true;
                } catch (Exception e) {
                    log.error("采购系统更新采购员异常：{}", e.getMessage());
                }
            }
            if (!result) {
                failedNos.add(order.getPurchaseOrderNo());
            }
        }
        if (CollectionUtils.isNotEmpty(failedNos)) {
            response.setMessage("failedNos: " + JSON.toJSONString(failedNos));
            throw new RuntimeException(response.getMessage());
        }
        else {
            response.setStatus(StatusCode.SUCCESS);
        }
        return response;
    }

    //精品sku 样品订单 没有店铺信息 店铺统一为sample
    private void createSampleInfo(Map<String, WhFbaPurchaseData> whFbaPurchaseDataMap,WhPurchaseItem item,WhPurchaseOrder order){
        WhFbaPurchaseData whFbaPurchaseData = whFbaPurchaseDataMap.get(item.getSku());
        if (whFbaPurchaseData == null) {
            whFbaPurchaseData = new WhFbaPurchaseData();
            whFbaPurchaseData.setPurchaseorderno(order.getPurchaseOrderNo());
            whFbaPurchaseData.setSku(item.getSku());
            whFbaPurchaseData.setOrderQuantity(item.getQuantity());
            // 添加店铺账号字段
            whFbaPurchaseData.setAccountNumber("sample");
            whFbaPurchaseDataMap.put(item.getSku() , whFbaPurchaseData);
        }
        else {
            whFbaPurchaseData.setOrderQuantity(whFbaPurchaseData.getOrderQuantity() + Optional.ofNullable(item.getQuantity()).orElse(0));
        }


        BoutiqueStockQueryCondition query = new BoutiqueStockQueryCondition();
        query.setSku(item.getSku());
        query.setStore("sample");
        List<BoutiqueStock> existStockList = boutiqueStockService.queryBoutiqueStocks(query,null);
        if (CollectionUtils.isEmpty(existStockList)) {
            BoutiqueStock BoutiqueStock=new BoutiqueStock();
            BoutiqueStock.setSku(item.getSku());
            BoutiqueStock.setStore("sample");
            boutiqueStockService.createBoutiqueStock(BoutiqueStock);
        }

    }

    @Override
    public Map<String, Integer> queryWhPurchaseExpressOrderAndItemListCount(WhPurchaseOrderQueryCondition query,
            Pager pager) {
        return whPurchaseOrderDao.queryWhPurchaseExpressOrderAndItemListCount(query, pager);
    }

    @Override
    public int queryPurchaseQuantityByPurchaseOrderNo(String purchaseOrderNo) {
        if(StringUtils.isBlank(purchaseOrderNo)){
            return 0;
        }
        return whPurchaseOrderDao.queryPurchaseQuantityByPurchaseOrderNo(purchaseOrderNo);
    }

    /**
     * @Description 在途SKU统计
     * <AUTHOR>
     * @date 2020/6/15 11:58
     * @param: query
     * @return java.util.Map<java.lang.String,java.lang.Object>
     */
    @Override
    public Map<String, Object> querySkuOnWayCount(WhPurchaseOrderQueryCondition query){
        Map<String, Object> resultMap = new HashMap<>();
        JSONArray legendData = new JSONArray();
        JSONArray seriesData = new JSONArray();
        Map<String, Object> skuOnWayMap = whPurchaseOrderDao.querySkuOnWayCount(query);

       /**1、在途SKU：库存表中在途库存>0的SKU数量
        2、在途PCS：在途SKU的在途库存之和
        3、待签收快递：采购查询列表中待签收快递单数量
        4、待签收SKU：采购单查询中，有待签收快递单的采购单中待入库的SKU数量
        5、待签收PCS：待签收SKU的采购数量*/
        legendData.add("在途SKU");
        legendData.add("在途PCS");
        legendData.add("待签收快递");
        legendData.add("待签收SKU");
        legendData.add("待签收PCS");

        String skuCount = skuOnWayMap.get("skuCount").toString();
        String quantity = skuOnWayMap.get("quantity").toString();
        seriesData.add(JSON.parse("{name: '在途SKU', value: '" + skuCount + "'}"));
        seriesData.add(JSON.parse("{name: '在途PCS', value: '" + quantity + "'}"));

        Map<String, Object> skuWaitRecieveMap = whPurchaseOrderDao.querySkuWaitRecieveCount(query);
        Map<String, Object> orderWaitRecieveMap = whPurchaseOrderDao.queryOrderWaitRecieveCount(query);
        String orderWaitRecieveCount = orderWaitRecieveMap.get("orderWaitRecieveCount").toString();
        String skuWaitRecieveCount = skuWaitRecieveMap.get("skuWaitRecieveCount").toString();
        String waitRecieveQuantity = skuWaitRecieveMap.get("waitRecieveQuantity").toString();
        seriesData.add(JSON.parse("{name: '待签收快递', value: '" + orderWaitRecieveCount + "'}"));
        seriesData.add(JSON.parse("{name: '待签收SKU', value: '" + skuWaitRecieveCount + "'}"));
        seriesData.add(JSON.parse("{name: '待签收PCS', value: '" + waitRecieveQuantity + "'}"));

        resultMap.put("legendData", legendData);
        resultMap.put("seriesData", seriesData);
        return resultMap;
    }

    /**
     * 获取采购单接口信息
     * @param expressId
     * @return
     */
    @Override
    public List<PurchaseOrder> getPurchaseOrders(String expressId) {
        if (StringUtils.isNotBlank(expressId)) {
            List<PurchaseOrder> purchaseOrders = null;
            SystemParam systemParam = CacheUtils.SystemParamGet("SWITCH.QUERY_PURCHASE_ORDER");
            if (systemParam != null && StringUtils.isNotBlank(systemParam.getParamValue())){
                // 从本地查
                WhPurchaseOrderQueryCondition query = new WhPurchaseOrderQueryCondition();
                if(expressId.contains("CG")) {
                    query.setPurchaseOrderNo(expressId);
                }else {
                    query.setTrackingNumber(expressId);
                }
                purchaseOrders = TranslateWhPurchaseOrderToPurchaseOrderUtils.translateWhPurchaseOrderToPurchaseOrder(
                        this.queryWhPurchaseOrderAndItems(query, null));
            }else {
                purchaseOrders = pmsCheckInService
                        .queryPurchaseOrdersByExpressIdOrPurchaseOrderNo(expressId);
            }
            WhPurchaseExpressRecord record = null;
            // 快递单查不到采购数据，再用手动编辑的采购单号重试
            if (!StringUtils.startsWith(expressId, "CG")
                    && !StringUtils.startsWith(expressId, "HWCG") && !StringUtils.startsWith(expressId, "NCG")) {
                WhPurchaseExpressRecordQueryCondition query = new WhPurchaseExpressRecordQueryCondition();
                query.setTrackingNumber(expressId);

                List<WhPurchaseExpressRecord> records = whPurchaseExpressRecordService
                        .queryWhPurchaseExpressRecords(query, null);
                record = Optional.ofNullable(records).orElse(Collections.emptyList()).stream().findFirst().orElse(null);
                if (CollectionUtils.isEmpty(purchaseOrders) && record != null) {
                    String purchaseOrderNo = record.getPurchaseOrderNo();
                    // 只适用于一个快递对应一个采购单的情况
                    if (StringUtils.isNotBlank(purchaseOrderNo) && !purchaseOrderNo.contains(",")) {
                        if (systemParam != null && StringUtils.isNotBlank(systemParam.getParamValue())){
                            // 从本地查
                            WhPurchaseOrderQueryCondition queryCondition = new WhPurchaseOrderQueryCondition();
                            queryCondition.setPurchaseOrderNo(purchaseOrderNo);
                            List<WhPurchaseOrder> orders = this.queryWhPurchaseOrderAndItems(queryCondition, null);
                            purchaseOrders = TranslateWhPurchaseOrderToPurchaseOrderUtils.translateWhPurchaseOrderToPurchaseOrder(orders);
                        }else {
                            purchaseOrders = pmsCheckInService
                                    .queryPurchaseOrdersByExpressIdOrPurchaseOrderNo(purchaseOrderNo);
                        }
                    }
                }
            }
            // 判断sku是否有配置唯一码号段
            if(CollectionUtils.isNotEmpty(purchaseOrders)){
                List<String> skus = new ArrayList<>();
                for (PurchaseOrder purchaseOrder : purchaseOrders) {
                    String purchaseOrderType = purchaseOrder.getPurchaseOrderType();
                    for (PurchaseOrderItem item : purchaseOrder.getPurchaseOrderItems()) {
                        skus.add(item.getSku());
                        item.setVendorName(purchaseOrder.getVendorName());
                        List<String> unAfterSaleOrderTypes = Arrays.asList(PurchaseOrderType.NCGZS.getCode(),PurchaseOrderType.NCGCW.getCode(),PurchaseOrderType.NCGHH.getCode());
                        if (StringUtils.isNotBlank(purchaseOrder.getPayMethod())
                                && StringUtils.contains(purchaseOrder.getPayMethod(), "售后")
                                && !unAfterSaleOrderTypes.contains(purchaseOrderType)) {
                            item.setAfterSaleQty(item.getQuantity());
                            item.setAfterSale(true);
                        }
                    }
                    if (record != null && StringUtils.isNotBlank(record.getLogisticsMark())) {
                        purchaseOrder.setLogisticsMark(record.getLogisticsMark());
                    }
                }
                if(CollectionUtils.isNotEmpty(skus)){
                    skus = skus.stream().distinct().collect(toList());
                    List<WhSkuUniqueNoSegment> segments = whSkuUniqueNoSegmentService.queryWhSkuUniqueNoSegmentList(skus);
                    if(CollectionUtils.isNotEmpty(segments)){
                        Set<String> skuSet = segments.stream().map(WhSkuUniqueNoSegment::getSku).collect(Collectors.toSet());
                        for (PurchaseOrder purchaseOrder : purchaseOrders) {
                            for (PurchaseOrderItem item : purchaseOrder.getPurchaseOrderItems()) {
                                if(StringUtils.isNotBlank(item.getSku()) && skuSet.contains(item.getSku())){
                                    item.setExistSkuUniqueSegment(true);
                                }
                            }
                        }
                    }
                }
            }
            return purchaseOrders;
        }
        else {
            return null;
        }
    }
}