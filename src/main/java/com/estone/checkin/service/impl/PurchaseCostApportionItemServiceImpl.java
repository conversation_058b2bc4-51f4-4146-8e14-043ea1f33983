package com.estone.checkin.service.impl;

import com.estone.checkin.bean.MonthPurchaseFreightUnitPrice;
import com.estone.checkin.bean.PurchaseCostApportionItem;
import com.estone.checkin.bean.PurchaseCostApportionItemQueryCondition;
import com.estone.checkin.dao.PurchaseCostApportionItemDao;
import com.estone.checkin.service.PurchaseCostApportionItemService;
import com.whq.tool.component.Pager;
import com.whq.tool.exception.BusinessException;
import com.whq.tool.exception.DBErrorCode;
import com.whq.tool.sqler.SqlerException;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

@Service("purchaseCostApportionItemService")
@Slf4j
public class PurchaseCostApportionItemServiceImpl implements PurchaseCostApportionItemService {
    @Resource
    private PurchaseCostApportionItemDao purchaseCostApportionItemDao;

    @Override
    public PurchaseCostApportionItem getPurchaseCostApportionItem(Integer id) {
        PurchaseCostApportionItem purchaseCostApportionItem = purchaseCostApportionItemDao.queryPurchaseCostApportionItem(id);
        return purchaseCostApportionItem;
    }

    @Override
    public PurchaseCostApportionItem getPurchaseCostApportionItemDetail(Integer id) {
        PurchaseCostApportionItem purchaseCostApportionItem = purchaseCostApportionItemDao.queryPurchaseCostApportionItem(id);
        // 关联查询
        return purchaseCostApportionItem;
    }

    @Override
    public PurchaseCostApportionItem queryPurchaseCostApportionItem(PurchaseCostApportionItemQueryCondition query) {
        Assert.notNull(query, "query is null!");
        PurchaseCostApportionItem purchaseCostApportionItem = purchaseCostApportionItemDao.queryPurchaseCostApportionItem(query);
        return purchaseCostApportionItem;
    }

    @Override
    public List<PurchaseCostApportionItem> queryAllPurchaseCostApportionItems() {
        return purchaseCostApportionItemDao.queryPurchaseCostApportionItemList();
    }

    @Override
    public List<PurchaseCostApportionItem> queryPurchaseCostApportionItems(PurchaseCostApportionItemQueryCondition query, Pager pager) {
        Assert.notNull(query, "query is null!");
        if (pager != null && pager.isQueryCount()) {
            int count = purchaseCostApportionItemDao.queryPurchaseCostApportionItemCount(query);
            pager.setTotalCount(count);
            if ( count == 0) {
                return new ArrayList<PurchaseCostApportionItem>();
            }
        }
        List<PurchaseCostApportionItem> purchaseCostApportionItems = purchaseCostApportionItemDao.queryPurchaseCostApportionItemList(query, pager);
        return purchaseCostApportionItems;
    }

    @Override
    public void createPurchaseCostApportionItem(PurchaseCostApportionItem purchaseCostApportionItem) {
        try {
            purchaseCostApportionItemDao.createPurchaseCostApportionItem(purchaseCostApportionItem);
        }
        catch (SqlerException e) {
            log.error(e.getMessage(), e);
            throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
        }
    }

    @Override
    public void batchCreatePurchaseCostApportionItem(List<PurchaseCostApportionItem> entityList) {
        if (CollectionUtils.isNotEmpty(entityList)) {
            try {
                purchaseCostApportionItemDao.batchCreatePurchaseCostApportionItem(entityList);
            }
            catch (SqlerException e) {
                log.error(e.getMessage(), e);
                throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
            }
        }
    }

    @Override
    public void deletePurchaseCostApportionItem(Integer id) {
        try {
            purchaseCostApportionItemDao.deletePurchaseCostApportionItem(id);
        }
        catch (SqlerException e) {
            log.error(e.getMessage(), e);
            throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
        }
    }

    @Override
    public void updatePurchaseCostApportionItem(PurchaseCostApportionItem purchaseCostApportionItem) {
        try {
            purchaseCostApportionItemDao.updatePurchaseCostApportionItem(purchaseCostApportionItem);
        }
        catch (SqlerException e) {
            log.error(e.getMessage(), e);
            throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
        }
    }

    @Override
    public void batchUpdatePurchaseCostApportionItem(List<PurchaseCostApportionItem> entityList) {
        if (CollectionUtils.isNotEmpty(entityList)) {
            try {
                purchaseCostApportionItemDao.batchUpdatePurchaseCostApportionItem(entityList);
            }
            catch (SqlerException e) {
                log.error(e.getMessage(), e);
                throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
            }
        }
    }

    @Override
    public List<MonthPurchaseFreightUnitPrice> countMonthUpSku(PurchaseCostApportionItemQueryCondition query) {
        return purchaseCostApportionItemDao.countMonthUpSku(query);
    }
}