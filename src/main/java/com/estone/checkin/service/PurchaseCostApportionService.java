package com.estone.checkin.service;

import com.estone.checkin.bean.PurchaseCostApportion;
import com.estone.checkin.bean.PurchaseCostApportionQueryCondition;
import com.estone.checkin.bean.PurchaseOrderStatusDTO;
import com.whq.tool.component.Pager;

import java.util.Date;
import java.util.List;
import java.util.Map;

public interface PurchaseCostApportionService {
    List<PurchaseCostApportion> queryAllPurchaseCostApportions();

    /**
     * 采购单运费分摊主页面分页查询
     * 
     * @param query
     * @param pager
     * @return
     */
    List<PurchaseCostApportion> queryPurchaseCostApportions(PurchaseCostApportionQueryCondition query, Pager pager);

    PurchaseCostApportion getPurchaseCostApportion(Integer id);

    PurchaseCostApportion getPurchaseCostApportionDetail(Integer id);

    PurchaseCostApportion queryPurchaseCostApportion(PurchaseCostApportionQueryCondition query);

    void createPurchaseCostApportion(PurchaseCostApportion purchaseCostApportion);

    void batchCreatePurchaseCostApportion(List<PurchaseCostApportion> entityList);

    void deletePurchaseCostApportion(Integer id);

    void updatePurchaseCostApportion(PurchaseCostApportion purchaseCostApportion);

    void batchUpdatePurchaseCostApportion(List<PurchaseCostApportion> entityList);

    /**
     * 查询采购单分摊运费和明细
     * 
     * @param query
     * @param pager
     * @return
     */
    List<PurchaseCostApportion> queryPurchaseCostApportionAndItems(PurchaseCostApportionQueryCondition query,
            Pager pager);

    /**
     * 更新核销状态
     */
    Map<String, Date> updatePurchaseCostApportionFinishStatus();
}