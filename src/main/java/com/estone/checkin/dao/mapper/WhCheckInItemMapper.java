package com.estone.checkin.dao.mapper;

import com.estone.checkin.bean.WhCheckInItem;
import org.springframework.jdbc.core.RowMapper;

import java.sql.ResultSet;
import java.sql.SQLException;

public class WhCheckInItemMapper implements Row<PERSON>apper<WhCheckInItem> {

    /**
     * This method corresponds to the database table wh_check_in_item
     *
     * @mbggenerated Tue Aug 14 11:25:58 CST 2018
     */
    public WhCheckInItem mapRow(ResultSet rs, int rowNum) throws SQLException {
        WhCheckInItem entity = new WhCheckInItem();
        entity.setItemId(
                rs.getObject(WhCheckInItemDBField.ITEM_ID) == null ? null : rs.getInt(WhCheckInItemDBField.ITEM_ID));
        entity.setInId(rs.getObject(WhCheckInItemDBField.IN_ID) == null ? null : rs.getInt(WhCheckInItemDBField.IN_ID));
        entity.setSkuId(
                rs.getObject(WhCheckInItemDBField.SKU_ID) == null ? null : rs.getInt(WhCheckInItemDBField.SKU_ID));
        entity.setSku(rs.getString(WhCheckInItemDBField.SKU));
        entity.setQuantity(
                rs.getObject(WhCheckInItemDBField.QUANTITY) == null ? null : rs.getInt(WhCheckInItemDBField.QUANTITY));
        entity.setUpQuantity(rs.getObject(WhCheckInItemDBField.UP_QUANTITY) == null ? null
                : rs.getInt(WhCheckInItemDBField.UP_QUANTITY));
        entity.setPacUpNum(rs.getObject(WhCheckInItemDBField.PAC_UP_NUM) == null ? null
                : rs.getInt(WhCheckInItemDBField.PAC_UP_NUM));
        entity.setQcQuantity(rs.getObject(WhCheckInItemDBField.QC_QUANTITY) == null ? null
                : rs.getInt(WhCheckInItemDBField.QC_QUANTITY));
        entity.setPurchasePrice(rs.getObject(WhCheckInItemDBField.PURCHASE_PRICE) == null ? null
                : rs.getDouble(WhCheckInItemDBField.PURCHASE_PRICE));
        entity.setComment(rs.getString(WhCheckInItemDBField.COMMENT));
        entity.setLocation(rs.getString(WhCheckInItemDBField.LOCATION));
        entity.setPurchaseQuantity(rs.getObject(WhCheckInItemDBField.PURCHASE_QUANTITY) == null ? null
                : rs.getInt(WhCheckInItemDBField.PURCHASE_QUANTITY));
        entity.setQcNum(
                rs.getObject(WhCheckInItemDBField.QC_NUM) == null ? null : rs.getInt(WhCheckInItemDBField.QC_NUM));
        entity.setPickQty(
                rs.getObject(WhCheckInItemDBField.PICK_QTY) == null ? null : rs.getInt(WhCheckInItemDBField.PICK_QTY));
        entity.setCheckInSkuFlags(rs.getString(WhCheckInItemDBField.CHECK_IN_SKU_FLAGS));
        entity.setFirstOrderType(rs.getString(WhCheckInItemDBField.FIRST_ORDER_TYPE));
        entity.setCheckInPackageAttr(rs.getObject(WhCheckInItemDBField.CHECK_IN_PACKAGE_ATTR) == null ? null
                : rs.getInt(WhCheckInItemDBField.CHECK_IN_PACKAGE_ATTR));
        return entity;
    }
}