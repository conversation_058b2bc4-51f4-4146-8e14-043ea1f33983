package com.estone.checkin.dao.mapper;

import com.estone.checkin.bean.WhCheckInExceBatch;
import java.sql.ResultSet;
import java.sql.SQLException;
import org.springframework.jdbc.core.RowMapper;

public class WhCheckInExceBatchMapper implements <PERSON><PERSON>apper<WhCheckInExceBatch> {

    public WhCheckInExceBatch mapRow(ResultSet rs, int rowNum) throws SQLException {
        WhCheckInExceBatch entity = new WhCheckInExceBatch();
        entity.setId(rs.getObject(WhCheckInExceBatchDBField.ID) == null ? null : rs.getInt(WhCheckInExceBatchDBField.ID));
        entity.setInId(rs.getObject(WhCheckInExceBatchDBField.IN_ID) == null ? null : rs.getInt(WhCheckInExceBatchDBField.IN_ID));
        entity.setExceId(rs.getObject(WhCheckInExceBatchDBField.EXCE_ID) == null ? null : rs.getInt(WhCheckInExceBatchDBField.EXCE_ID));
        entity.setPurchaseOrderNo(rs.getString(WhCheckInExceBatchDBField.PURCHASE_ORDER_NO));
        entity.setTrackingNumber(rs.getString(WhCheckInExceBatchDBField.TRACKING_NUMBER));
        entity.setBatchNo(rs.getString(WhCheckInExceBatchDBField.BATCH_NO));
        entity.setCreatedBy(rs.getObject(WhCheckInExceBatchDBField.CREATED_BY) == null ? null : rs.getInt(WhCheckInExceBatchDBField.CREATED_BY));
        entity.setCreationDate(rs.getTimestamp(WhCheckInExceBatchDBField.CREATION_DATE));
        return entity;
    }
}