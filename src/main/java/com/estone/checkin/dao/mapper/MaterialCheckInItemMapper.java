package com.estone.checkin.dao.mapper;

import com.estone.checkin.bean.MaterialCheckInItem;
import java.sql.ResultSet;
import java.sql.SQLException;
import org.springframework.jdbc.core.RowMapper;

public class MaterialCheckInItemMapper implements RowMapper<MaterialCheckInItem> {

    public MaterialCheckInItem mapRow(ResultSet rs, int rowNum) throws SQLException {
        MaterialCheckInItem entity = new MaterialCheckInItem();
        entity.setItemId(rs.getObject(MaterialCheckInItemDBField.ITEM_ID) == null ? null : rs.getInt(MaterialCheckInItemDBField.ITEM_ID));
        entity.setInId(rs.getObject(MaterialCheckInItemDBField.IN_ID) == null ? null : rs.getInt(MaterialCheckInItemDBField.IN_ID));
        entity.setArticleNumber(rs.getString(MaterialCheckInItemDBField.ARTICLE_NUMBER));
        entity.setQuantity(rs.getObject(MaterialCheckInItemDBField.QUANTITY) == null ? null : rs.getInt(MaterialCheckInItemDBField.QUANTITY));
        entity.setPurchaseQuantity(rs.getObject(MaterialCheckInItemDBField.PURCHASE_QUANTITY) == null ? null : rs.getInt(MaterialCheckInItemDBField.PURCHASE_QUANTITY));
        entity.setPurchasePrice(rs.getObject(MaterialCheckInItemDBField.PURCHASE_PRICE) == null ? null : rs.getDouble(MaterialCheckInItemDBField.PURCHASE_PRICE));
        return entity;
    }
}