package com.estone.checkin.dao.mapper;

import com.estone.checkin.bean.WhAllocationCheckIn;
import com.estone.checkin.bean.WhAllocationCheckInItem;
import org.springframework.jdbc.core.RowMapper;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.HashMap;
import java.util.Map;

public class WhAllocationCheckInMapper implements RowMapper<WhAllocationCheckIn> {

    /**
     * This method corresponds to the database table wh_allocation_check_in
     *
     * @mbggenerated Tue Mar 12 19:16:13 CST 2019
     */
/*    public WhAllocationCheckIn mapRow(ResultSet rs, int rowNum) throws SQLException {
        WhAllocationCheckIn entity = new WhAllocationCheckIn();
        entity.setInId(rs.getObject(WhAllocationCheckInDBField.IN_ID) == null ? null : rs.getInt(WhAllocationCheckInDBField.IN_ID));
        entity.setCheckInType(rs.getObject(WhAllocationCheckInDBField.CHECK_IN_TYPE) == null ? null : rs.getInt(WhAllocationCheckInDBField.CHECK_IN_TYPE));
        entity.setBagNo(rs.getString(WhAllocationCheckInDBField.BAG_NO));
        entity.setAllocationOrderNo(rs.getString(WhAllocationCheckInDBField.ALLOCATION_ORDER_NO));
        entity.setBoxNo(rs.getString(WhAllocationCheckInDBField.BOX_NO));
        entity.setPurchaseDate(rs.getTimestamp(WhAllocationCheckInDBField.PURCHASE_DATE));
        entity.setCreateDate(rs.getTimestamp(WhAllocationCheckInDBField.CREATE_DATE));
        entity.setCreateUser(rs.getObject(WhAllocationCheckInDBField.CREATE_USER) == null ? null : rs.getInt(WhAllocationCheckInDBField.CREATE_USER));
        entity.setComment(rs.getString(WhAllocationCheckInDBField.COMMENT));
        entity.setWarehouseId(rs.getObject(WhAllocationCheckInDBField.WAREHOUSE_ID) == null ? null : rs.getInt(WhAllocationCheckInDBField.WAREHOUSE_ID));
        entity.setToWarehouseId(rs.getObject(WhAllocationCheckInDBField.TO_WAREHOUSE_ID) == null ? null : rs.getInt(WhAllocationCheckInDBField.TO_WAREHOUSE_ID));
        entity.setStatus(rs.getObject(WhAllocationCheckInDBField.STATUS) == null ? null : rs.getInt(WhAllocationCheckInDBField.STATUS));
        entity.setConfirmDate(rs.getTimestamp(WhAllocationCheckInDBField.CONFIRM_DATE));
        entity.setUpdateUser(rs.getObject(WhAllocationCheckInDBField.UPDATE_USER) == null ? null : rs.getInt(WhAllocationCheckInDBField.UPDATE_USER));
        entity.setUpdateDate(rs.getTimestamp(WhAllocationCheckInDBField.UPDATE_DATE));
        entity.setUpUser(rs.getObject(WhAllocationCheckInDBField.UP_USER) == null ? null : rs.getInt(WhAllocationCheckInDBField.UP_USER));
        entity.setUpTime(rs.getTimestamp(WhAllocationCheckInDBField.UP_TIME));
        entity.setQcUser(rs.getObject(WhAllocationCheckInDBField.QC_USER) == null ? null : rs.getInt(WhAllocationCheckInDBField.QC_USER));
        entity.setQcTime(rs.getTimestamp(WhAllocationCheckInDBField.QC_TIME));
        entity.setObtainUser(rs.getObject(WhAllocationCheckInDBField.OBTAIN_USER) == null ? null : rs.getInt(WhAllocationCheckInDBField.OBTAIN_USER));
        entity.setObtainTime(rs.getTimestamp(WhAllocationCheckInDBField.OBTAIN_TIME));
        entity.setIsQcPacking(rs.getObject(WhAllocationCheckInDBField.IS_QC_PACKING) == null ? null : rs.getBoolean(WhAllocationCheckInDBField.IS_QC_PACKING));
        entity.setRecievedTime(rs.getTimestamp(WhAllocationCheckInDBField.RECIEVED_TIME));
        return entity;
    }*/
    
    
    
    private Map<String, WhAllocationCheckIn> exist = new HashMap<String, WhAllocationCheckIn>();
    
    private boolean hasItem = false;
    
    
    public WhAllocationCheckInMapper(){
        
    }
    
    public WhAllocationCheckInMapper(boolean hasItem){
        this.hasItem = hasItem;
    }
    
    public WhAllocationCheckIn mapRow(ResultSet rs, int rowNum) throws SQLException {
        WhAllocationCheckIn entity = null;
        if (!hasItem) {
            entity = getWhAllocationCheckIn(rs, "");
            return entity;
        }

        String id = rs.getString("ch." + WhAllocationCheckInDBField.IN_ID);
        entity = exist.get(id);
        if (entity == null) {
            entity = getWhAllocationCheckIn(rs, "ch.");
            exist.put(id, entity);
            if (hasItem) {
                entity.setWhAllocationCheckInItem(getWhAllocationCheckInItem(rs, "i."));// 封装明细
            }
            return entity;
        }
        else {
            if (hasItem) {
                entity.setWhAllocationCheckInItem(getWhAllocationCheckInItem(rs, "i."));// 封装明细
            }
            return null;
        }
    }
    
    public WhAllocationCheckIn getWhAllocationCheckIn(ResultSet rs, String prefix) throws SQLException {
        WhAllocationCheckIn entity = new WhAllocationCheckIn();
        entity.setInId(rs.getObject(prefix+WhAllocationCheckInDBField.IN_ID) == null ? null : rs.getInt(prefix+WhAllocationCheckInDBField.IN_ID));
        entity.setCheckInType(rs.getObject(prefix+WhAllocationCheckInDBField.CHECK_IN_TYPE) == null ? null : rs.getInt(prefix+WhAllocationCheckInDBField.CHECK_IN_TYPE));
        entity.setBagNo(rs.getString(prefix+WhAllocationCheckInDBField.BAG_NO));
        entity.setAllocationOrderNo(rs.getString(prefix+WhAllocationCheckInDBField.ALLOCATION_ORDER_NO));
        entity.setBoxNo(rs.getString(prefix+WhAllocationCheckInDBField.BOX_NO));
        entity.setPurchaseDate(rs.getTimestamp(prefix+WhAllocationCheckInDBField.PURCHASE_DATE));
        entity.setCreateDate(rs.getTimestamp(prefix+WhAllocationCheckInDBField.CREATE_DATE));
        entity.setCreateUser(rs.getObject(prefix+WhAllocationCheckInDBField.CREATE_USER) == null ? null : rs.getInt(prefix+WhAllocationCheckInDBField.CREATE_USER));
        entity.setComment(rs.getString(prefix+WhAllocationCheckInDBField.COMMENT));
        entity.setWarehouseId(rs.getObject(prefix+WhAllocationCheckInDBField.WAREHOUSE_ID) == null ? null : rs.getInt(prefix+WhAllocationCheckInDBField.WAREHOUSE_ID));
        entity.setToWarehouseId(rs.getObject(prefix+WhAllocationCheckInDBField.TO_WAREHOUSE_ID) == null ? null : rs.getInt(prefix+WhAllocationCheckInDBField.TO_WAREHOUSE_ID));
        entity.setStatus(rs.getObject(prefix+WhAllocationCheckInDBField.STATUS) == null ? null : rs.getInt(prefix+WhAllocationCheckInDBField.STATUS));
        entity.setConfirmDate(rs.getTimestamp(prefix+WhAllocationCheckInDBField.CONFIRM_DATE));
        entity.setUpdateUser(rs.getObject(prefix+WhAllocationCheckInDBField.UPDATE_USER) == null ? null : rs.getInt(prefix+WhAllocationCheckInDBField.UPDATE_USER));
        entity.setUpdateDate(rs.getTimestamp(prefix+WhAllocationCheckInDBField.UPDATE_DATE));
        entity.setUpUser(rs.getObject(prefix+WhAllocationCheckInDBField.UP_USER) == null ? null : rs.getInt(prefix+WhAllocationCheckInDBField.UP_USER));
        entity.setUpTime(rs.getTimestamp(prefix+WhAllocationCheckInDBField.UP_TIME));
        entity.setQcUser(rs.getObject(prefix+WhAllocationCheckInDBField.QC_USER) == null ? null : rs.getInt(prefix+WhAllocationCheckInDBField.QC_USER));
        entity.setQcTime(rs.getTimestamp(prefix+WhAllocationCheckInDBField.QC_TIME));
        entity.setObtainUser(rs.getObject(prefix+WhAllocationCheckInDBField.OBTAIN_USER) == null ? null : rs.getInt(prefix+WhAllocationCheckInDBField.OBTAIN_USER));
        entity.setObtainTime(rs.getTimestamp(prefix+WhAllocationCheckInDBField.OBTAIN_TIME));
        entity.setQcPacking(rs.getObject(prefix+WhAllocationCheckInDBField.IS_QC_PACKING) == null ? null : rs.getInt(prefix+WhAllocationCheckInDBField.IS_QC_PACKING));
        entity.setRecievedTime(rs.getTimestamp(prefix+WhAllocationCheckInDBField.RECIEVED_TIME));
        entity.setCheckInFlag(rs.getString(prefix+WhAllocationCheckInDBField.CHECK_IN_FLAG));
        return entity;
    }
    
    public WhAllocationCheckInItem getWhAllocationCheckInItem(ResultSet rs, String prefix) throws SQLException {
        WhAllocationCheckInItem entity = new WhAllocationCheckInItem();
        entity.setItemId(rs.getObject(prefix+WhAllocationCheckInItemDBField.ITEM_ID) == null ? null : rs.getInt(prefix+WhAllocationCheckInItemDBField.ITEM_ID));
        entity.setInId(rs.getObject(prefix+WhAllocationCheckInItemDBField.IN_ID) == null ? null : rs.getInt(prefix+WhAllocationCheckInItemDBField.IN_ID));
        entity.setSkuId(rs.getObject(prefix+WhAllocationCheckInItemDBField.SKU_ID) == null ? null : rs.getInt(prefix+WhAllocationCheckInItemDBField.SKU_ID));
        entity.setSku(rs.getString(prefix+WhAllocationCheckInItemDBField.SKU));
        entity.setBoxQuantity(rs.getObject(prefix+WhAllocationCheckInItemDBField.BOX_QUANTITY) == null ? null : rs.getInt(prefix+WhAllocationCheckInItemDBField.BOX_QUANTITY));
        entity.setQuantity(rs.getObject(prefix+WhAllocationCheckInItemDBField.QUANTITY) == null ? null : rs.getInt(prefix+WhAllocationCheckInItemDBField.QUANTITY));
        entity.setExceptionQuantity(rs.getObject(prefix+WhAllocationCheckInItemDBField.EXCEPTION_QUANTITY) == null ? null : rs.getInt(prefix+WhAllocationCheckInItemDBField.EXCEPTION_QUANTITY));
        entity.setQcQuantity(rs.getObject(prefix+WhAllocationCheckInItemDBField.QC_QUANTITY) == null ? null : rs.getInt(prefix+WhAllocationCheckInItemDBField.QC_QUANTITY));
        entity.setQcExceptionQuantity(rs.getObject(prefix+WhAllocationCheckInItemDBField.QC_EXCEPTION_QUANTITY) == null ? null : rs.getInt(prefix+WhAllocationCheckInItemDBField.QC_EXCEPTION_QUANTITY));
        entity.setUpQuantity(rs.getObject(prefix+WhAllocationCheckInItemDBField.UP_QUANTITY) == null ? null : rs.getInt(prefix+WhAllocationCheckInItemDBField.UP_QUANTITY));
        entity.setPurchasePrice(rs.getObject(prefix+WhAllocationCheckInItemDBField.PURCHASE_PRICE) == null ? null : rs.getDouble(prefix+WhAllocationCheckInItemDBField.PURCHASE_PRICE));
        entity.setComment(rs.getString(prefix+WhAllocationCheckInItemDBField.COMMENT));
        entity.setExceptionType(rs.getObject(prefix+WhAllocationCheckInItemDBField.EXCEPTION_TYPE) == null ? null : rs.getInt(prefix+WhAllocationCheckInItemDBField.EXCEPTION_TYPE));
        entity.setQcExceptionType(rs.getObject(prefix+WhAllocationCheckInItemDBField.QC_EXCEPTION_TYPE) == null ? null : rs.getInt(prefix+WhAllocationCheckInItemDBField.QC_EXCEPTION_TYPE));
        //库位不用前缀
        entity.setLocation(rs.getString(WhAllocationCheckInItemDBField.LOCATION));
        entity.setStockId(rs.getObject(prefix+WhAllocationCheckInItemDBField.STOCK_ID) == null ? null : rs.getInt(prefix+WhAllocationCheckInItemDBField.STOCK_ID));
        if (entity.getItemId()==null) {
            return null;
        }
        return entity;
    }

}