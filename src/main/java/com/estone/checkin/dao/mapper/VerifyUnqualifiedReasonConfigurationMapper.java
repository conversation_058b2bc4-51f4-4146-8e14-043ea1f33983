package com.estone.checkin.dao.mapper;

import com.estone.checkin.bean.VerifyUnqualifiedReasonConfiguration;
import java.sql.ResultSet;
import java.sql.SQLException;
import org.springframework.jdbc.core.RowMapper;

public class VerifyUnqualifiedReasonConfigurationMapper implements RowMapper<VerifyUnqualifiedReasonConfiguration> {

    public VerifyUnqualifiedReasonConfiguration mapRow(ResultSet rs, int rowNum) throws SQLException {
        VerifyUnqualifiedReasonConfiguration entity = new VerifyUnqualifiedReasonConfiguration();
        entity.setId(rs.getObject(VerifyUnqualifiedReasonConfigurationDBField.ID) == null ? null : rs.getInt(VerifyUnqualifiedReasonConfigurationDBField.ID));
        entity.setReason(rs.getString(VerifyUnqualifiedReasonConfigurationDBField.REASON));
        entity.setCreationBy(rs.getObject(VerifyUnqualifiedReasonConfigurationDBField.CREATION_BY) == null ? null : rs.getInt(VerifyUnqualifiedReasonConfigurationDBField.CREATION_BY));
        entity.setCreationDate(rs.getTimestamp(VerifyUnqualifiedReasonConfigurationDBField.CREATION_DATE));
        return entity;
    }
}