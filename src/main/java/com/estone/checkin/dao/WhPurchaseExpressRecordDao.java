package com.estone.checkin.dao;

import com.estone.checkin.bean.WhPurchaseExpressRecord;
import com.estone.checkin.bean.WhPurchaseExpressRecordQueryCondition;
import com.whq.tool.component.Pager;
import java.util.List;

public interface WhPurchaseExpressRecordDao {
        /**
         * This method corresponds to the database table wh_purchase_express_record
         *
         * @mbggenerated Fri Aug 17 10:33:25 CST 2018
         */
        int queryWhPurchaseExpressRecordCount(WhPurchaseExpressRecordQueryCondition query);

        /**
         * This method corresponds to the database table wh_purchase_express_record
         *
         * @mbggenerated Fri Aug 17 10:33:25 CST 2018
         */
        List<WhPurchaseExpressRecord> queryWhPurchaseExpressRecordList();

        /**
         * This method corresponds to the database table wh_purchase_express_record
         *
         * @mbggenerated Fri Aug 17 10:33:25 CST 2018
         */
        List<WhPurchaseExpressRecord> queryWhPurchaseExpressRecordList(WhPurchaseExpressRecordQueryCondition query, Pager pager);

        /**
         * This method corresponds to the database table wh_purchase_express_record
         *
         * @mbggenerated Fri Aug 17 10:33:25 CST 2018
         */
        WhPurchaseExpressRecord queryWhPurchaseExpressRecord(Integer primaryKey);

        /**
         * This method corresponds to the database table wh_purchase_express_record
         *
         * @mbggenerated Fri Aug 17 10:33:25 CST 2018
         */
        WhPurchaseExpressRecord queryWhPurchaseExpressRecord(WhPurchaseExpressRecordQueryCondition query);

        /**
         * This method corresponds to the database table wh_purchase_express_record
         *
         * @mbggenerated Fri Aug 17 10:33:25 CST 2018
         */
        void createWhPurchaseExpressRecord(WhPurchaseExpressRecord entity);

        /**
         * This method corresponds to the database table wh_purchase_express_record
         *
         * @mbggenerated Fri Aug 17 10:33:25 CST 2018
         */
        void batchCreateWhPurchaseExpressRecord(List<WhPurchaseExpressRecord> entityList);

        /**
         * This method corresponds to the database table wh_purchase_express_record
         *
         * @mbggenerated Fri Aug 17 10:33:25 CST 2018
         */
        void batchUpdateWhPurchaseExpressRecord(List<WhPurchaseExpressRecord> entityList);

        /**
         * This method corresponds to the database table wh_purchase_express_record
         *
         * @mbggenerated Fri Aug 17 10:33:25 CST 2018
         */
        void deleteWhPurchaseExpressRecord(Integer primaryKey);

        /**
         * This method corresponds to the database table wh_purchase_express_record
         *
         * @mbggenerated Fri Aug 17 10:33:25 CST 2018
         */
        void updateWhPurchaseExpressRecord(WhPurchaseExpressRecord entity);

        List<WhPurchaseExpressRecord> getNotFinishExpressRecord(Integer userId);

        /**
         * 根据id批量修改备注
         * @param ids
         * @param comment
         * @return void
         * <AUTHOR>
         * @date 2021/12/26 11:51
         */
        void batchUpdateCommentByIds(String ids, String comment);
}