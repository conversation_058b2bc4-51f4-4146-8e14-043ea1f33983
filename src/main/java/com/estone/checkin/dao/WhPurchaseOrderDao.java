package com.estone.checkin.dao;

import com.estone.checkin.bean.WhPurchaseOrder;
import com.estone.checkin.bean.WhPurchaseOrderQueryCondition;
import com.whq.tool.component.Pager;

import java.util.List;
import java.util.Map;

public interface WhPurchaseOrderDao {
    /**
     * This method corresponds to the database table wh_purchase_order
     *
     * @mbggenerated Mon May 27 14:24:34 CST 2019
     */
    int queryWhPurchaseOrderCount(WhPurchaseOrderQueryCondition query);

    /**
     * This method corresponds to the database table wh_purchase_order
     *
     * @mbggenerated Mon May 27 14:24:34 CST 2019
     */
    List<WhPurchaseOrder> queryWhPurchaseOrderList();

    /**
     * This method corresponds to the database table wh_purchase_order
     *
     * @mbggenerated Mon May 27 14:24:34 CST 2019
     */
    List<WhPurchaseOrder> queryWhPurchaseOrderList(WhPurchaseOrderQueryCondition query, Pager pager);

    /**
     * This method corresponds to the database table wh_purchase_order
     *
     * @mbggenerated Mon May 27 14:24:34 CST 2019
     */
    WhPurchaseOrder queryWhPurchaseOrder(Integer primaryKey);

    /**
     * This method corresponds to the database table wh_purchase_order
     *
     * @mbggenerated Mon May 27 14:24:34 CST 2019
     */
    WhPurchaseOrder queryWhPurchaseOrder(WhPurchaseOrderQueryCondition query);

    /**
     * This method corresponds to the database table wh_purchase_order
     *
     * @mbggenerated Mon May 27 14:24:34 CST 2019
     */
    void createWhPurchaseOrder(WhPurchaseOrder entity);

    /**
     * This method corresponds to the database table wh_purchase_order
     *
     * @mbggenerated Mon May 27 14:24:34 CST 2019
     */
    void batchCreateWhPurchaseOrder(List<WhPurchaseOrder> entityList);

    /**
     * This method corresponds to the database table wh_purchase_order
     *
     * @mbggenerated Mon May 27 14:24:34 CST 2019
     */
    void batchUpdateWhPurchaseOrder(List<WhPurchaseOrder> entityList);

    /**
     * This method corresponds to the database table wh_purchase_order
     *
     * @mbggenerated Mon May 27 14:24:34 CST 2019
     */
    void deleteWhPurchaseOrder(Integer primaryKey);

    /**
     * This method corresponds to the database table wh_purchase_order
     *
     * @mbggenerated Mon May 27 14:24:34 CST 2019
     */
    void updateWhPurchaseOrder(WhPurchaseOrder entity);
    
    int queryWhPurchaseOrderAndItemCount(WhPurchaseOrderQueryCondition query);

    List<WhPurchaseOrder> queryWhPurchaseOrderAndItemList(WhPurchaseOrderQueryCondition query, Pager pager);

    List<WhPurchaseOrder> queryWhPurchaseExpressOrderAndItemList(WhPurchaseOrderQueryCondition query, Pager pager);

    Map<String, Integer> queryWhPurchaseExpressOrderAndItemListCount(WhPurchaseOrderQueryCondition query, Pager pager);

    int deleteWhPurchaseOrderAndItemByPurchaseOrderNo(String purchaseOrderNo);

    /**
     * 根据采购单号获取采购数量
     *
     * @param purchaseOrderNo
     * @return
     */
    int queryPurchaseQuantityByPurchaseOrderNo(String purchaseOrderNo);

    /**
     * @Description 在途统计
     * <AUTHOR>
     * @date 2020/6/15 14:22
     * @param: query
     * @return java.util.Map<java.lang.String,java.lang.Object>
     */
    Map<String, Object> querySkuOnWayCount(WhPurchaseOrderQueryCondition query);

    /**
     * @Description 待签SKU收统计
     * <AUTHOR>
     * @date 2020/6/15 14:44
     * @param: query
     * @return java.util.Map<java.lang.String,java.lang.Object>
     */
    Map<String, Object> querySkuWaitRecieveCount(WhPurchaseOrderQueryCondition query);

    /**
     * @Description 待签收快递统计
     * <AUTHOR>
     * @date 2020/6/18 11:16
     * @param: query
     * @return java.util.Map<java.lang.String,java.lang.Object>
     */
    Map<String, Object> queryOrderWaitRecieveCount(WhPurchaseOrderQueryCondition query);
}