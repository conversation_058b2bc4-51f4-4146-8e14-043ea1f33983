package com.estone.checkin.dao;

import com.estone.checkin.bean.RfoCheckIn;
import com.estone.checkin.bean.RfoCheckInQueryCondition;
import com.whq.tool.component.Pager;
import java.util.List;

public interface RfoCheckInDao {
    int queryRfoCheckInCount(RfoCheckInQueryCondition query);

    List<RfoCheckIn> queryRfoCheckInList();

    List<RfoCheckIn> queryRfoCheckInList(RfoCheckInQueryCondition query, Pager pager);

    RfoCheckIn queryRfoCheckIn(Integer primaryKey);

    RfoCheckIn queryRfoCheckIn(RfoCheckInQueryCondition query);

    void createRfoCheckIn(RfoCheckIn entity);

    void batchCreateRfoCheckIn(List<RfoCheckIn> entityList);

    void batchUpdateRfoCheckIn(List<RfoCheckIn> entityList);

    void deleteRfoCheckIn(Integer primaryKey);

    int updateRfoCheckIn(RfoCheckIn entity);

    List<RfoCheckIn> queryAllRfoCheckInByRelevantNo(String badProductCode);
}