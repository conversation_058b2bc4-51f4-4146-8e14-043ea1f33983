package com.estone.checkin.dao;

import com.estone.checkin.bean.WhAllocationCheckInException;
import com.estone.checkin.bean.WhAllocationCheckInExceptionQueryCondition;
import com.whq.tool.component.Pager;
import java.util.List;

public interface WhAllocationCheckInExceptionDao {
    int queryWhAllocationCheckInExceptionCount(WhAllocationCheckInExceptionQueryCondition query);

    List<WhAllocationCheckInException> queryWhAllocationCheckInExceptionList();

    List<WhAllocationCheckInException> queryWhAllocationCheckInExceptionList(WhAllocationCheckInExceptionQueryCondition query, Pager pager);

    WhAllocationCheckInException queryWhAllocationCheckInException(Integer primaryKey);

    WhAllocationCheckInException queryWhAllocationCheckInException(WhAllocationCheckInExceptionQueryCondition query);

    void createWhAllocationCheckInException(WhAllocationCheckInException entity);

    void batchCreateWhAllocationCheckInException(List<WhAllocationCheckInException> entityList);

    void batchUpdateWhAllocationCheckInException(List<WhAllocationCheckInException> entityList);

    void deleteWhAllocationCheckInException(Integer primaryKey);

    void updateWhAllocationCheckInException(WhAllocationCheckInException entity);
}