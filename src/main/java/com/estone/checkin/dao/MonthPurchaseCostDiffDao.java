package com.estone.checkin.dao;

import com.estone.checkin.bean.MonthPurchaseCostDiff;
import com.estone.checkin.bean.MonthPurchaseCostDiffQueryCondition;
import com.whq.tool.component.Pager;
import java.util.List;
import java.util.Map;

public interface MonthPurchaseCostDiffDao {
    int queryMonthPurchaseCostDiffCount(MonthPurchaseCostDiffQueryCondition query);

    List<MonthPurchaseCostDiff> queryMonthPurchaseCostDiffList();

    List<MonthPurchaseCostDiff> queryMonthPurchaseCostDiffList(MonthPurchaseCostDiffQueryCondition query, Pager pager);

    MonthPurchaseCostDiff queryMonthPurchaseCostDiff(Integer primaryKey);

    MonthPurchaseCostDiff queryMonthPurchaseCostDiff(MonthPurchaseCostDiffQueryCondition query);

    void createMonthPurchaseCostDiff(MonthPurchaseCostDiff entity);

    void batchCreateMonthPurchaseCostDiff(List<MonthPurchaseCostDiff> entityList);

    void batchUpdateMonthPurchaseCostDiff(List<MonthPurchaseCostDiff> entityList);

    void deleteMonthPurchaseCostDiff(Integer primaryKey);

    void updateMonthPurchaseCostDiff(MonthPurchaseCostDiff entity);

    Map<String,Object> queryMonthPurchaseCostDiffTotal(MonthPurchaseCostDiffQueryCondition query);
}