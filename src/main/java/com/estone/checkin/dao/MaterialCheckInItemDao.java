package com.estone.checkin.dao;

import com.estone.checkin.bean.MaterialCheckInItem;
import com.estone.checkin.bean.MaterialCheckInItemQueryCondition;
import com.whq.tool.component.Pager;
import java.util.List;

public interface MaterialCheckInItemDao {
    int queryMaterialCheckInItemCount(MaterialCheckInItemQueryCondition query);

    List<MaterialCheckInItem> queryMaterialCheckInItemList();

    List<MaterialCheckInItem> queryMaterialCheckInItemList(MaterialCheckInItemQueryCondition query, Pager pager);

    MaterialCheckInItem queryMaterialCheckInItem(Integer primaryKey);

    MaterialCheckInItem queryMaterialCheckInItem(MaterialCheckInItemQueryCondition query);

    void createMaterialCheckInItem(MaterialCheckInItem entity);

    void batchCreateMaterialCheckInItem(List<MaterialCheckInItem> entityList);

    void batchUpdateMaterialCheckInItem(List<MaterialCheckInItem> entityList);

    void deleteMaterialCheckInItem(Integer primaryKey);

    void updateMaterialCheckInItem(MaterialCheckInItem entity);
}