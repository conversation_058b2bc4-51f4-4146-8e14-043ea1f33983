package com.estone.checkin.dao.impl;

import java.util.List;

import org.springframework.stereotype.Repository;
import org.springframework.util.Assert;

import com.estone.checkin.bean.HandleExceptionDate;
import com.estone.checkin.bean.HandleExceptionDateQueryCondition;
import com.estone.checkin.dao.HandleExceptionDateDao;
import com.estone.checkin.dao.mapper.HandleExceptionDateDBField;
import com.estone.checkin.dao.mapper.HandleExceptionDateMapper;
import com.estone.common.util.SqlerTemplate;
import com.whq.tool.component.Pager;
import com.whq.tool.sqler.SqlerRequest;
import com.whq.tool.sqler.analyzer.DataType;

@Repository("handleExceptionDateDao")
public class HandleExceptionDateDaoImpl implements HandleExceptionDateDao {

    private void setQueryCondition(SqlerRequest request, HandleExceptionDateQueryCondition query) {
        if (query == null) {
            return;
        }
        // 添加只读权限
        if (query.getReadOnly() != null && query.getReadOnly())
        {
            request.setReadOnly(true);
        }
        // 添加查询条件
        // 搜索条件不允许出现空字符
        request.setAllowBlank(false);
        
        request.addDataParam(HandleExceptionDateDBField.ID, DataType.INT, query.getId());
        request.addDataParam(HandleExceptionDateDBField.EXCEPTION_ID, DataType.INT, query.getExceptionId());
    }

    @Override
    public int queryHandleExceptionDateCount(HandleExceptionDateQueryCondition query) {
        SqlerRequest request = new SqlerRequest("queryHandleExceptionDateCount");
        setQueryCondition(request, query);
        return SqlerTemplate.queryForInt(request);
    }

    @Override
    public List<HandleExceptionDate> queryHandleExceptionDateList() {
        SqlerRequest request = new SqlerRequest("queryHandleExceptionDateList");
        return SqlerTemplate.query(request, new HandleExceptionDateMapper());
    }

    @Override
    public List<HandleExceptionDate> queryHandleExceptionDateList(HandleExceptionDateQueryCondition query, Pager pager) {
        SqlerRequest request = new SqlerRequest("queryHandleExceptionDateList");
        setQueryCondition(request, query);
        if(pager != null) {
            request.addFetch(pager.getPageNo(), pager.getPageSize());
        }
        return SqlerTemplate.query(request, new HandleExceptionDateMapper());
    }

    @Override
    public HandleExceptionDate queryHandleExceptionDate(Integer primaryKey) {
        Assert.notNull(primaryKey, "primaryKey is null!");
        SqlerRequest request = new SqlerRequest("queryHandleExceptionDateByPrimaryKey");
        request.addDataParam(HandleExceptionDateDBField.ID, DataType.INT, primaryKey);
        return SqlerTemplate.queryForObject(request, new HandleExceptionDateMapper());
    }

    @Override
    public HandleExceptionDate queryHandleExceptionDate(HandleExceptionDateQueryCondition query) {
        SqlerRequest request = new SqlerRequest("queryHandleExceptionDate");
        setQueryCondition(request, query);
        return SqlerTemplate.queryForObject(request, new HandleExceptionDateMapper());
    }

    @Override
    public void createHandleExceptionDate(HandleExceptionDate entity) {
        SqlerRequest request = new SqlerRequest("createHandleExceptionDate");
        request.addDataParam(HandleExceptionDateDBField.EXCEPTION_ID, DataType.INT, entity.getExceptionId());
        request.addDataParam(HandleExceptionDateDBField.HANDLE_DRAFT_BY, DataType.INT, entity.getHandleDraftBy());
        request.addDataParam(HandleExceptionDateDBField.HANDLE_DRAFT_DATE, DataType.TIMESTAMP, entity.getHandleDraftDate());
        request.addDataParam(HandleExceptionDateDBField.FINISH_BY, DataType.INT, entity.getFinishBy());
        request.addDataParam(HandleExceptionDateDBField.FINISH_DATE, DataType.TIMESTAMP, entity.getFinishDate());
        Integer autoIncrementId = SqlerTemplate.executeAndReturn(request);
        entity.setId(autoIncrementId);
    }

    @Override
    public void updateHandleExceptionDate(HandleExceptionDate entity) {
        SqlerRequest request = new SqlerRequest("updateHandleExceptionDateByPrimaryKey");
        request.addDataParam(HandleExceptionDateDBField.ID, DataType.INT, entity.getId());
        
        request.addDataParam(HandleExceptionDateDBField.EXCEPTION_ID, DataType.INT, entity.getExceptionId());
        request.addDataParam(HandleExceptionDateDBField.HANDLE_DRAFT_BY, DataType.INT, entity.getHandleDraftBy());
        request.addDataParam(HandleExceptionDateDBField.HANDLE_DRAFT_DATE, DataType.TIMESTAMP, entity.getHandleDraftDate());
        request.addDataParam(HandleExceptionDateDBField.FINISH_BY, DataType.INT, entity.getFinishBy());
        request.addDataParam(HandleExceptionDateDBField.FINISH_DATE, DataType.TIMESTAMP, entity.getFinishDate());
        SqlerTemplate.execute(request);
    }

    @Override
    public void batchCreateHandleExceptionDate(List<HandleExceptionDate> entityList) {
        if (entityList != null && !entityList.isEmpty()) {
            SqlerRequest request = new SqlerRequest("createHandleExceptionDate");
            for (HandleExceptionDate entity : entityList) {
                request.addBatchDataParam(HandleExceptionDateDBField.EXCEPTION_ID, DataType.INT, entity.getExceptionId());
                request.addBatchDataParam(HandleExceptionDateDBField.HANDLE_DRAFT_BY, DataType.INT, entity.getHandleDraftBy());
                request.addBatchDataParam(HandleExceptionDateDBField.HANDLE_DRAFT_DATE, DataType.TIMESTAMP, entity.getHandleDraftDate());
                request.addBatchDataParam(HandleExceptionDateDBField.FINISH_BY, DataType.INT, entity.getFinishBy());
                request.addBatchDataParam(HandleExceptionDateDBField.FINISH_DATE, DataType.TIMESTAMP, entity.getFinishDate());
                request.addBatch();
            }
            SqlerTemplate.batchUpdate(request);
        }
    }

    @Override
    public void batchUpdateHandleExceptionDate(List<HandleExceptionDate> entityList) {
        if (entityList != null && !entityList.isEmpty()) {
            SqlerRequest request = new SqlerRequest("updateHandleExceptionDateByPrimaryKey");
            for (HandleExceptionDate entity : entityList) {
                request.addBatchDataParam(HandleExceptionDateDBField.ID, DataType.INT, entity.getId());
                
                request.addBatchDataParam(HandleExceptionDateDBField.EXCEPTION_ID, DataType.INT, entity.getExceptionId());
                request.addBatchDataParam(HandleExceptionDateDBField.HANDLE_DRAFT_BY, DataType.INT, entity.getHandleDraftBy());
                request.addBatchDataParam(HandleExceptionDateDBField.HANDLE_DRAFT_DATE, DataType.TIMESTAMP, entity.getHandleDraftDate());
                request.addBatchDataParam(HandleExceptionDateDBField.FINISH_BY, DataType.INT, entity.getFinishBy());
                request.addBatchDataParam(HandleExceptionDateDBField.FINISH_DATE, DataType.TIMESTAMP, entity.getFinishDate());
                request.addBatch();
            }
            SqlerTemplate.batchUpdate(request);
        }
    }

    @Override
    public void deleteHandleExceptionDate(Integer primaryKey) {
        SqlerRequest request = new SqlerRequest("deleteHandleExceptionDateByPrimaryKey");
        request.addDataParam(HandleExceptionDateDBField.ID, DataType.INT, primaryKey);
        SqlerTemplate.execute(request);
    }
}