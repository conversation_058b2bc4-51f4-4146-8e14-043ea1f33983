package com.estone.checkin.dao.impl;

import java.sql.Timestamp;
import java.util.List;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Repository;
import org.springframework.util.Assert;

import com.estone.checkin.bean.MaterialCheckIn;
import com.estone.checkin.bean.MaterialCheckInQueryCondition;
import com.estone.checkin.dao.MaterialCheckInDao;
import com.estone.checkin.dao.mapper.MaterialCheckInDBField;
import com.estone.checkin.dao.mapper.MaterialCheckInItemDBField;
import com.estone.checkin.dao.mapper.MaterialCheckInMapper;
import com.estone.common.util.CommonUtils;
import com.estone.common.util.SqlerTemplate;
import com.whq.tool.component.Pager;
import com.whq.tool.context.DataContextHolder;
import com.whq.tool.sqler.SqlerRequest;
import com.whq.tool.sqler.analyzer.DataType;

@Repository("materialCheckInDao")
public class MaterialCheckInDaoImpl implements MaterialCheckInDao {

    private void setQueryCondition(SqlerRequest request, MaterialCheckInQueryCondition query) {
        if (query == null) {
            return;
        }
        // 添加只读权限
        if (query.getReadOnly() != null && query.getReadOnly()) {
            request.setReadOnly(true);
        }
        // 添加查询条件
        // 搜索条件不允许出现空字符
        request.setAllowBlank(false);

        request.addDataParam(MaterialCheckInDBField.IN_ID, DataType.INT, query.getInId());
        request.addDataParam(MaterialCheckInDBField.STATUS, DataType.INT, query.getStatus());
        request.addDataParam(MaterialCheckInDBField.CREATE_USER, DataType.INT, query.getCreateUser());
        request.addDataParam(MaterialCheckInDBField.CHECK_MANAGER, DataType.INT, query.getCheckManager());
        request.addDataParam(MaterialCheckInDBField.QC_USER, DataType.INT, query.getQcUser());
        request.addDataParam("from_create_date", DataType.STRING, query.getFromCreateDate());
        request.addDataParam("to_create_date", DataType.STRING, query.getToCreateDate());
        request.addDataParam("from_package_acceptance_date", DataType.STRING, query.getFromPackageAcceptanceDate());
        request.addDataParam("to_package_acceptance_date", DataType.STRING, query.getToPackageAcceptanceDate());

        if (StringUtils.isNotBlank(query.getSupplier())) {
            if (StringUtils.contains(query.getSupplier(), ",")) {
                request.addDataParam("supplierList", DataType.STRING, CommonUtils.splitList(query.getSupplier(), ","));
            } else {
                request.addDataParam("likeSupplier", DataType.STRING, "%"+query.getSupplier()+"%");
            }
        }

        if (StringUtils.isNotEmpty(query.getInIdStr())) {
            if (StringUtils.contains(query.getInIdStr(), ",")) {
                request.addDataParam("inIdList", DataType.INT, CommonUtils.splitList(query.getInIdStr(), ","));
            } else {
                request.addDataParam(MaterialCheckInDBField.IN_ID, DataType.INT, query.getInIdStr());
            }
        }

        if (CollectionUtils.isNotEmpty(query.getInIdList()))
            request.addDataParam("inIdList", DataType.INT, query.getInIdList());

        if (StringUtils.isNotEmpty(query.getTrackingNumber())) {
            if (StringUtils.contains(query.getTrackingNumber(), ",")) {
                request.addDataParam("trackingNumberList", DataType.STRING,
                        CommonUtils.splitList(query.getTrackingNumber(), ","));
            } else {
                request.addDataParam(MaterialCheckInDBField.TRACKING_NUMBER, DataType.STRING,
                        query.getTrackingNumber());
            }
        }
        if (StringUtils.isNotEmpty(query.getPurchaseOrderNo())) {
            if (StringUtils.contains(query.getPurchaseOrderNo(), ",")) {
                request.addDataParam("purchaseOrderNoList", DataType.STRING,
                        CommonUtils.splitList(query.getPurchaseOrderNo(), ","));
            } else {
                request.addDataParam(MaterialCheckInDBField.PURCHASE_ORDER_NO, DataType.STRING,
                        query.getPurchaseOrderNo());
            }
        }
        if (StringUtils.isNotEmpty(query.getArticleNumber())) {
            if (StringUtils.contains(query.getArticleNumber(), ",")) {
                request.addDataParam("articleNumberList", DataType.STRING,
                        CommonUtils.splitList(query.getArticleNumber(), ","));
            } else {
                request.addDataParam(MaterialCheckInItemDBField.ARTICLE_NUMBER, DataType.STRING,
                        query.getArticleNumber());
            }
        }
        if (CollectionUtils.isNotEmpty(query.getStatusList())) {
            request.addDataParam("statusList", DataType.STRING, query.getStatusList());
        }
    }

    @Override
    public int queryMaterialCheckInCount(MaterialCheckInQueryCondition query) {
        SqlerRequest request = new SqlerRequest("queryMaterialCheckInCount");
        setQueryCondition(request, query);
        return SqlerTemplate.queryForInt(request);
    }

    @Override
    public List<MaterialCheckIn> queryMaterialCheckInList() {
        SqlerRequest request = new SqlerRequest("queryMaterialCheckInList");
        return SqlerTemplate.query(request, new MaterialCheckInMapper());
    }

    @Override
    public List<MaterialCheckIn> queryMaterialCheckInList(MaterialCheckInQueryCondition query, Pager pager) {
        SqlerRequest request = new SqlerRequest("queryMaterialCheckInList");
        setQueryCondition(request, query);
        if (pager != null) {
            request.addFetch(pager.getPageNo(), pager.getPageSize());
        }
        return SqlerTemplate.query(request, new MaterialCheckInMapper(true));
    }

    @Override
    public MaterialCheckIn queryMaterialCheckIn(Integer primaryKey) {
        Assert.notNull(primaryKey, "primaryKey is null!");
        SqlerRequest request = new SqlerRequest("queryMaterialCheckInByPrimaryKey");
        request.addDataParam(MaterialCheckInDBField.IN_ID, DataType.INT, primaryKey);
        return SqlerTemplate.queryForObject(request, new MaterialCheckInMapper());
    }

    @Override
    public MaterialCheckIn queryMaterialCheckIn(MaterialCheckInQueryCondition query) {
        SqlerRequest request = new SqlerRequest("queryMaterialCheckIn");
        setQueryCondition(request, query);
        return SqlerTemplate.queryForObject(request, new MaterialCheckInMapper());
    }

    @Override
    public void createMaterialCheckIn(MaterialCheckIn entity) {
        SqlerRequest request = new SqlerRequest("createMaterialCheckIn");
        request.addDataParam(MaterialCheckInDBField.PURCHASE_ORDER_NO, DataType.STRING, entity.getPurchaseOrderNo());
        request.addDataParam(MaterialCheckInDBField.TRACKING_NUMBER, DataType.STRING, entity.getTrackingNumber());
        request.addDataParam(MaterialCheckInDBField.CREATE_DATE, DataType.TIMESTAMP,
                entity.getCreateDate() == null ? new Timestamp(System.currentTimeMillis()) : entity.getCreateDate());
        request.addDataParam(MaterialCheckInDBField.CREATE_USER, DataType.INT,
                entity.getCreateUser() == null ? DataContextHolder.getUserId() : entity.getCreateUser());
        request.addDataParam(MaterialCheckInDBField.UPDATE_USER, DataType.INT, entity.getUpdateUser());
        request.addDataParam(MaterialCheckInDBField.UPDATE_DATE, DataType.TIMESTAMP, entity.getUpdateDate());
        request.addDataParam(MaterialCheckInDBField.CHECK_MANAGER, DataType.INT, entity.getCheckManager());
        request.addDataParam(MaterialCheckInDBField.CHECK_TIME, DataType.TIMESTAMP, entity.getCheckTime());
        request.addDataParam(MaterialCheckInDBField.QC_USER, DataType.INT, entity.getQcUser());
        request.addDataParam(MaterialCheckInDBField.QC_TIME, DataType.TIMESTAMP, entity.getQcTime());
        request.addDataParam(MaterialCheckInDBField.COMMENT, DataType.STRING, entity.getComment());
        request.addDataParam(MaterialCheckInDBField.STATUS, DataType.INT, entity.getStatus());
        request.addDataParam(MaterialCheckInDBField.SHIPPING_COST, DataType.DOUBLE, entity.getShippingCost());
        request.addDataParam(MaterialCheckInDBField.TOTAL_WEIGHT, DataType.DOUBLE, entity.getTotalWeight());
        Integer autoIncrementId = SqlerTemplate.executeAndReturn(request);
        entity.setInId(autoIncrementId);
    }

    @Override
    public void updateMaterialCheckIn(MaterialCheckIn entity) {
        SqlerRequest request = new SqlerRequest("updateMaterialCheckInByPrimaryKey");
        request.addDataParam(MaterialCheckInDBField.IN_ID, DataType.INT, entity.getInId());

        request.addDataParam(MaterialCheckInDBField.PURCHASE_ORDER_NO, DataType.STRING, entity.getPurchaseOrderNo());
        request.addDataParam(MaterialCheckInDBField.TRACKING_NUMBER, DataType.STRING, entity.getTrackingNumber());
        request.addDataParam(MaterialCheckInDBField.CREATE_DATE, DataType.TIMESTAMP, entity.getCreateDate());
        request.addDataParam(MaterialCheckInDBField.CREATE_USER, DataType.INT, entity.getCreateUser());
        request.addDataParam(MaterialCheckInDBField.UPDATE_USER, DataType.INT, entity.getUpdateUser());
        request.addDataParam(MaterialCheckInDBField.UPDATE_DATE, DataType.TIMESTAMP, entity.getUpdateDate());
        request.addDataParam(MaterialCheckInDBField.CHECK_MANAGER, DataType.INT, entity.getCheckManager());
        request.addDataParam(MaterialCheckInDBField.CHECK_TIME, DataType.TIMESTAMP, entity.getCheckTime());
        request.addDataParam(MaterialCheckInDBField.QC_USER, DataType.INT, entity.getQcUser());
        request.addDataParam(MaterialCheckInDBField.QC_TIME, DataType.TIMESTAMP, entity.getQcTime());
        request.addDataParam(MaterialCheckInDBField.COMMENT, DataType.STRING, entity.getComment());
        request.addDataParam(MaterialCheckInDBField.STATUS, DataType.INT, entity.getStatus());
        request.addDataParam(MaterialCheckInDBField.SHIPPING_COST, DataType.DOUBLE, entity.getShippingCost());
        request.addDataParam(MaterialCheckInDBField.TOTAL_WEIGHT, DataType.DOUBLE, entity.getTotalWeight());
        SqlerTemplate.execute(request);
    }

    @Override
    public void batchCreateMaterialCheckIn(List<MaterialCheckIn> entityList) {
        if (entityList != null && !entityList.isEmpty()) {
            SqlerRequest request = new SqlerRequest("createMaterialCheckIn");
            for (MaterialCheckIn entity : entityList) {
                request.addBatchDataParam(MaterialCheckInDBField.PURCHASE_ORDER_NO, DataType.STRING,
                        entity.getPurchaseOrderNo());
                request.addBatchDataParam(MaterialCheckInDBField.TRACKING_NUMBER, DataType.STRING,
                        entity.getTrackingNumber());
                request.addBatchDataParam(MaterialCheckInDBField.CREATE_DATE, DataType.TIMESTAMP,
                        entity.getCreateDate() == null ? new Timestamp(System.currentTimeMillis())
                                : entity.getCreateDate());
                request.addBatchDataParam(MaterialCheckInDBField.CREATE_USER, DataType.INT,
                        entity.getCreateUser() == null ? DataContextHolder.getUserId() : entity.getCreateUser());

                request.addBatchDataParam(MaterialCheckInDBField.UPDATE_USER, DataType.INT, entity.getUpdateUser());
                request.addBatchDataParam(MaterialCheckInDBField.UPDATE_DATE, DataType.TIMESTAMP,
                        entity.getUpdateDate());
                request.addBatchDataParam(MaterialCheckInDBField.CHECK_MANAGER, DataType.INT, entity.getCheckManager());
                request.addBatchDataParam(MaterialCheckInDBField.CHECK_TIME, DataType.TIMESTAMP, entity.getCheckTime());
                request.addBatchDataParam(MaterialCheckInDBField.QC_USER, DataType.INT, entity.getQcUser());
                request.addBatchDataParam(MaterialCheckInDBField.QC_TIME, DataType.TIMESTAMP, entity.getQcTime());
                request.addBatchDataParam(MaterialCheckInDBField.COMMENT, DataType.STRING, entity.getComment());
                request.addBatchDataParam(MaterialCheckInDBField.STATUS, DataType.INT, entity.getStatus());
                request.addBatchDataParam(MaterialCheckInDBField.SHIPPING_COST, DataType.DOUBLE,
                        entity.getShippingCost());
                request.addBatchDataParam(MaterialCheckInDBField.TOTAL_WEIGHT, DataType.DOUBLE,
                        entity.getTotalWeight());
                request.addBatch();
            }
            SqlerTemplate.batchUpdate(request);
        }
    }

    @Override
    public void batchUpdateMaterialCheckIn(List<MaterialCheckIn> entityList) {
        if (entityList != null && !entityList.isEmpty()) {
            SqlerRequest request = new SqlerRequest("updateMaterialCheckInByPrimaryKey");
            for (MaterialCheckIn entity : entityList) {
                request.addBatchDataParam(MaterialCheckInDBField.IN_ID, DataType.INT, entity.getInId());

                request.addBatchDataParam(MaterialCheckInDBField.PURCHASE_ORDER_NO, DataType.STRING,
                        entity.getPurchaseOrderNo());
                request.addBatchDataParam(MaterialCheckInDBField.TRACKING_NUMBER, DataType.STRING,
                        entity.getTrackingNumber());
                request.addBatchDataParam(MaterialCheckInDBField.CREATE_DATE, DataType.TIMESTAMP,
                        entity.getCreateDate());
                request.addBatchDataParam(MaterialCheckInDBField.CREATE_USER, DataType.INT, entity.getCreateUser());
                request.addBatchDataParam(MaterialCheckInDBField.UPDATE_USER, DataType.INT, entity.getUpdateUser());
                request.addBatchDataParam(MaterialCheckInDBField.UPDATE_DATE, DataType.TIMESTAMP,
                        entity.getUpdateDate());
                request.addBatchDataParam(MaterialCheckInDBField.CHECK_MANAGER, DataType.INT, entity.getCheckManager());
                request.addBatchDataParam(MaterialCheckInDBField.CHECK_TIME, DataType.TIMESTAMP, entity.getCheckTime());
                request.addBatchDataParam(MaterialCheckInDBField.QC_USER, DataType.INT, entity.getQcUser());
                request.addBatchDataParam(MaterialCheckInDBField.QC_TIME, DataType.TIMESTAMP, entity.getQcTime());
                request.addBatchDataParam(MaterialCheckInDBField.COMMENT, DataType.STRING, entity.getComment());
                request.addBatchDataParam(MaterialCheckInDBField.STATUS, DataType.INT, entity.getStatus());
                request.addBatchDataParam(MaterialCheckInDBField.SHIPPING_COST, DataType.DOUBLE,
                        entity.getShippingCost());
                request.addBatchDataParam(MaterialCheckInDBField.TOTAL_WEIGHT, DataType.DOUBLE,
                        entity.getTotalWeight());
                request.addBatch();
            }
            SqlerTemplate.batchUpdate(request);
        }
    }

    @Override
    public void deleteMaterialCheckIn(Integer primaryKey) {
        SqlerRequest request = new SqlerRequest("deleteMaterialCheckInByPrimaryKey");
        request.addDataParam(MaterialCheckInDBField.IN_ID, DataType.INT, primaryKey);
        SqlerTemplate.execute(request);
    }
}