package com.estone.checkin.dao.impl;

import java.sql.Timestamp;
import java.util.List;

import jodd.util.StringUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Repository;
import org.springframework.util.Assert;

import com.estone.checkin.bean.WhCheckInExceptionHandle;
import com.estone.checkin.bean.WhCheckInExceptionHandleQueryCondition;
import com.estone.checkin.dao.WhCheckInExceptionHandleDao;
import com.estone.checkin.dao.mapper.WhCheckInExceptionHandleDBField;
import com.estone.checkin.dao.mapper.WhCheckInExceptionHandleMapper;
import com.whq.tool.component.Pager;
import com.whq.tool.context.DataContextHolder;
import com.whq.tool.sqler.SqlerRequest;
import com.whq.tool.sqler.analyzer.DataType;
import com.estone.common.util.SqlerTemplate;

@Repository("whCheckInExceptionHandleDao")
public class WhCheckInExceptionHandleDaoImpl implements WhCheckInExceptionHandleDao {

    private void setQueryCondition(SqlerRequest request, WhCheckInExceptionHandleQueryCondition query) {
        if (query == null) {
            return;
        }
        // 添加查询条件
        request.addDataParam(WhCheckInExceptionHandleDBField.ID, DataType.INT, query.getId());
        request.addDataParam(WhCheckInExceptionHandleDBField.EXCEPTION_ID, DataType.INT, query.getExceptionId());
        request.addDataParam(WhCheckInExceptionHandleDBField.HANDLE_COMMENT, DataType.STRING, query.getHandleComment());
        request.addDataParam(WhCheckInExceptionHandleDBField.HANDLE_WAY, DataType.INT, query.getHandleWay());
        request.addDataParam(WhCheckInExceptionHandleDBField.STATUS, DataType.INT, query.getStatus());
        request.addDataParam(WhCheckInExceptionHandleDBField.QUANTITY, DataType.INT, query.getQuantity());
        if (CollectionUtils.isNotEmpty(query.getExceptionIds())) {
            request.addDataParam("exceptionIds", DataType.INT, query.getExceptionIds());
        }
        if (CollectionUtils.isNotEmpty(query.getStatusList())) {
            request.addDataParam("statusList", DataType.INT, query.getStatusList());
        }

        if (StringUtils.isNotBlank(query.getOrderByStr())) {
            request.addSqlDataParam("ORDER_BY", query.getOrderByStr());
        }
        // 搜索条件不允许出现空字符
        request.setAllowBlank(false);

    }

    /**
     * This method corresponds to the database table wh_check_in_exception_handle
     *
     * @mbggenerated Mon Nov 19 15:25:28 CST 2018
     */
    public int queryWhCheckInExceptionHandleCount(WhCheckInExceptionHandleQueryCondition query) {
        SqlerRequest request = new SqlerRequest("queryWhCheckInExceptionHandleCount");
        setQueryCondition(request, query);
        return SqlerTemplate.queryForInt(request);
    }

    /**
     * This method corresponds to the database table wh_check_in_exception_handle
     *
     * @mbggenerated Mon Nov 19 15:25:28 CST 2018
     */
    public List<WhCheckInExceptionHandle> queryWhCheckInExceptionHandleList() {
        SqlerRequest request = new SqlerRequest("queryWhCheckInExceptionHandleList");
        return SqlerTemplate.query(request, new WhCheckInExceptionHandleMapper());
    }

    /**
     * This method corresponds to the database table wh_check_in_exception_handle
     *
     * @mbggenerated Mon Nov 19 15:25:28 CST 2018
     */
    public List<WhCheckInExceptionHandle> queryWhCheckInExceptionHandleList(WhCheckInExceptionHandleQueryCondition query, Pager pager) {
        SqlerRequest request = new SqlerRequest("queryWhCheckInExceptionHandleList");
        setQueryCondition(request, query);
        if (pager != null) {
            request.addFetch(pager.getPageNo(), pager.getPageSize());
        }
        return SqlerTemplate.query(request, new WhCheckInExceptionHandleMapper());
    }

    /**
     * This method corresponds to the database table wh_check_in_exception_handle
     *
     * @mbggenerated Mon Nov 19 15:25:28 CST 2018
     */
    public WhCheckInExceptionHandle queryWhCheckInExceptionHandle(Integer primaryKey) {
        Assert.notNull(primaryKey);
        SqlerRequest request = new SqlerRequest("queryWhCheckInExceptionHandleByPrimaryKey");
        request.addDataParam(WhCheckInExceptionHandleDBField.ID, DataType.INT, primaryKey);
        return SqlerTemplate.queryForObject(request, new WhCheckInExceptionHandleMapper());
    }

    /**
     * This method corresponds to the database table wh_check_in_exception_handle
     *
     * @mbggenerated Mon Nov 19 15:25:28 CST 2018
     */
    public WhCheckInExceptionHandle queryWhCheckInExceptionHandle(WhCheckInExceptionHandleQueryCondition query) {
        SqlerRequest request = new SqlerRequest("queryWhCheckInExceptionHandle");
        setQueryCondition(request, query);
        return SqlerTemplate.queryForObject(request, new WhCheckInExceptionHandleMapper());
    }

    /**
     * This method corresponds to the database table wh_check_in_exception_handle
     *
     * @mbggenerated Mon Nov 19 15:25:28 CST 2018
     */
    public void createWhCheckInExceptionHandle(WhCheckInExceptionHandle entity) {
        SqlerRequest request = new SqlerRequest("createWhCheckInExceptionHandle");
        request.addDataParam(WhCheckInExceptionHandleDBField.EXCEPTION_ID, DataType.INT, entity.getExceptionId());
        request.addDataParam(WhCheckInExceptionHandleDBField.HANDLE_COMMENT, DataType.STRING, entity.getHandleComment());
        request.addDataParam(WhCheckInExceptionHandleDBField.HANDLE_WAY, DataType.INT, entity.getHandleWay());
        request.addDataParam(WhCheckInExceptionHandleDBField.STATUS, DataType.INT, entity.getStatus());
        request.addDataParam(WhCheckInExceptionHandleDBField.CREATED_BY, DataType.INT, entity.getCreatedBy() == null ? DataContextHolder.getUserId() : entity.getCreatedBy());
        request.addDataParam(WhCheckInExceptionHandleDBField.CREATION_DATE, DataType.TIMESTAMP, new Timestamp(System.currentTimeMillis()));
        request.addDataParam(WhCheckInExceptionHandleDBField.QUANTITY, DataType.INT, entity.getQuantity());
        request.addDataParam(WhCheckInExceptionHandleDBField.CREATE_USER_NAME, DataType.STRING, entity.getCreateUserName());
        Integer autoIncrementId = SqlerTemplate.executeAndReturn(request);
        entity.setId(autoIncrementId);
    }

    /**
     * This method corresponds to the database table wh_check_in_exception_handle
     *
     * @mbggenerated Mon Nov 19 15:25:28 CST 2018
     */
    public void updateWhCheckInExceptionHandle(WhCheckInExceptionHandle entity) {
        SqlerRequest request = new SqlerRequest("updateWhCheckInExceptionHandleByPrimaryKey");
        request.addDataParam(WhCheckInExceptionHandleDBField.ID, DataType.INT, entity.getId());

        request.addDataParam(WhCheckInExceptionHandleDBField.EXCEPTION_ID, DataType.INT, entity.getExceptionId());
        request.addDataParam(WhCheckInExceptionHandleDBField.HANDLE_COMMENT, DataType.STRING, entity.getHandleComment());
        request.addDataParam(WhCheckInExceptionHandleDBField.HANDLE_WAY, DataType.INT, entity.getHandleWay());
        request.addDataParam(WhCheckInExceptionHandleDBField.STATUS, DataType.INT, entity.getStatus());


        request.addDataParam(WhCheckInExceptionHandleDBField.QUANTITY, DataType.INT, entity.getQuantity());
        SqlerTemplate.execute(request);
    }

    /**
     * This method corresponds to the database table wh_check_in_exception_handle
     *
     * @mbggenerated Mon Nov 19 15:25:28 CST 2018
     */
    public void batchCreateWhCheckInExceptionHandle(List<WhCheckInExceptionHandle> entityList) {
        if (entityList != null && !entityList.isEmpty()) {
            SqlerRequest request = new SqlerRequest("createWhCheckInExceptionHandle");
            for (WhCheckInExceptionHandle entity : entityList) {
                request.addBatchDataParam(WhCheckInExceptionHandleDBField.EXCEPTION_ID, DataType.INT, entity.getExceptionId());
                request.addBatchDataParam(WhCheckInExceptionHandleDBField.HANDLE_COMMENT, DataType.STRING, entity.getHandleComment());
                request.addBatchDataParam(WhCheckInExceptionHandleDBField.HANDLE_WAY, DataType.INT, entity.getHandleWay());
                request.addBatchDataParam(WhCheckInExceptionHandleDBField.STATUS, DataType.INT, entity.getStatus());
                request.addBatchDataParam(WhCheckInExceptionHandleDBField.CREATED_BY, DataType.INT, entity.getCreatedBy() == null ? DataContextHolder.getUserId() : entity.getCreatedBy());
                request.addBatchDataParam(WhCheckInExceptionHandleDBField.CREATION_DATE, DataType.TIMESTAMP, new Timestamp(System.currentTimeMillis()));
                request.addBatchDataParam(WhCheckInExceptionHandleDBField.QUANTITY, DataType.INT, entity.getQuantity());
                request.addBatchDataParam(WhCheckInExceptionHandleDBField.CREATE_USER_NAME, DataType.STRING, entity.getCreateUserName());
                request.addBatch();
            }
            SqlerTemplate.batchUpdate(request);
        }
    }

    /**
     * This method corresponds to the database table wh_check_in_exception_handle
     *
     * @mbggenerated Mon Nov 19 15:25:28 CST 2018
     */
    public void batchUpdateWhCheckInExceptionHandle(List<WhCheckInExceptionHandle> entityList) {
        if (entityList != null && !entityList.isEmpty()) {
            SqlerRequest request = new SqlerRequest("updateWhCheckInExceptionHandleByPrimaryKey");
            for (WhCheckInExceptionHandle entity : entityList) {
                request.addBatchDataParam(WhCheckInExceptionHandleDBField.ID, DataType.INT, entity.getId());

                request.addBatchDataParam(WhCheckInExceptionHandleDBField.EXCEPTION_ID, DataType.INT, entity.getExceptionId());
                request.addBatchDataParam(WhCheckInExceptionHandleDBField.HANDLE_COMMENT, DataType.STRING, entity.getHandleComment());
                request.addBatchDataParam(WhCheckInExceptionHandleDBField.HANDLE_WAY, DataType.INT, entity.getHandleWay());
                request.addBatchDataParam(WhCheckInExceptionHandleDBField.STATUS, DataType.INT, entity.getStatus());


                request.addBatchDataParam(WhCheckInExceptionHandleDBField.QUANTITY, DataType.INT, entity.getQuantity());
                request.addBatch();
            }
            SqlerTemplate.batchUpdate(request);
        }
    }

    /**
     * This method corresponds to the database table wh_check_in_exception_handle
     *
     * @mbggenerated Mon Nov 19 15:25:28 CST 2018
     */
    public void deleteWhCheckInExceptionHandle(Integer primaryKey) {
        SqlerRequest request = new SqlerRequest("deleteWhCheckInExceptionHandleByPrimaryKey");
        request.addDataParam(WhCheckInExceptionHandleDBField.ID, DataType.INT, primaryKey);
        SqlerTemplate.execute(request);
    }
}