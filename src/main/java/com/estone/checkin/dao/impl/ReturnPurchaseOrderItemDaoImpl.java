package com.estone.checkin.dao.impl;

import com.estone.checkin.bean.ReturnPurchaseOrderItem;
import com.estone.checkin.bean.ReturnPurchaseOrderItemQueryCondition;
import com.estone.checkin.dao.ReturnPurchaseOrderItemDao;
import com.estone.checkin.dao.mapper.ReturnPurchaseOrderItemDBField;
import com.estone.checkin.dao.mapper.ReturnPurchaseOrderItemMapper;
import com.whq.tool.component.Pager;
import com.whq.tool.sqler.SqlerRequest;
import com.whq.tool.sqler.analyzer.DataType;
import com.estone.common.util.SqlerTemplate;
import org.springframework.stereotype.Repository;
import org.springframework.util.Assert;

import java.util.List;

@Repository("returnPurchaseOrderItemDao")
public class ReturnPurchaseOrderItemDaoImpl implements ReturnPurchaseOrderItemDao {

    private void setQueryCondition(SqlerRequest request, ReturnPurchaseOrderItemQueryCondition query) {
        if (query == null)
        {
            return;
        }
        // 添加查询条件
        // 搜索条件不允许出现空字符
        request.setAllowBlank(false);
        
        request.addDataParam(ReturnPurchaseOrderItemDBField.ID, DataType.INT, query.getId());
        request.addDataParam(ReturnPurchaseOrderItemDBField.RETURN_ORDER_NO, DataType.STRING, query.getReturnOrderNo());
    }

    /**
     * This method corresponds to the database table return_purchase_order_item
     *
     * @mbggenerated Mon Jul 15 18:00:38 CST 2019
     */
    public int queryReturnPurchaseOrderItemCount(ReturnPurchaseOrderItemQueryCondition query) {
        SqlerRequest request = new SqlerRequest("queryReturnPurchaseOrderItemCount");
        setQueryCondition(request, query);
        return SqlerTemplate.queryForInt(request);
    }

    /**
     * This method corresponds to the database table return_purchase_order_item
     *
     * @mbggenerated Mon Jul 15 18:00:38 CST 2019
     */
    public List<ReturnPurchaseOrderItem> queryReturnPurchaseOrderItemList() {
        SqlerRequest request = new SqlerRequest("queryReturnPurchaseOrderItemList");
        return SqlerTemplate.query(request, new ReturnPurchaseOrderItemMapper());
    }

    /**
     * This method corresponds to the database table return_purchase_order_item
     *
     * @mbggenerated Mon Jul 15 18:00:38 CST 2019
     */
    public List<ReturnPurchaseOrderItem> queryReturnPurchaseOrderItemList(ReturnPurchaseOrderItemQueryCondition query, Pager pager) {
        SqlerRequest request = new SqlerRequest("queryReturnPurchaseOrderItemList");
        setQueryCondition(request, query);
        if(pager != null)
        {
            request.addFetch(pager.getPageNo(), pager.getPageSize());
        }
        return SqlerTemplate.query(request, new ReturnPurchaseOrderItemMapper());
    }

    /**
     * This method corresponds to the database table return_purchase_order_item
     *
     * @mbggenerated Mon Jul 15 18:00:38 CST 2019
     */
    public ReturnPurchaseOrderItem queryReturnPurchaseOrderItem(Integer primaryKey) {
        Assert.notNull(primaryKey);
        SqlerRequest request = new SqlerRequest("queryReturnPurchaseOrderItemByPrimaryKey");
        request.addDataParam(ReturnPurchaseOrderItemDBField.ID, DataType.INT, primaryKey);
        return SqlerTemplate.queryForObject(request, new ReturnPurchaseOrderItemMapper());
    }

    /**
     * This method corresponds to the database table return_purchase_order_item
     *
     * @mbggenerated Mon Jul 15 18:00:38 CST 2019
     */
    public ReturnPurchaseOrderItem queryReturnPurchaseOrderItem(ReturnPurchaseOrderItemQueryCondition query) {
        SqlerRequest request = new SqlerRequest("queryReturnPurchaseOrderItem");
        setQueryCondition(request, query);
        return SqlerTemplate.queryForObject(request, new ReturnPurchaseOrderItemMapper());
    }

    /**
     * This method corresponds to the database table return_purchase_order_item
     *
     * @mbggenerated Mon Jul 15 18:00:38 CST 2019
     */
    public void createReturnPurchaseOrderItem(ReturnPurchaseOrderItem entity) {
        SqlerRequest request = new SqlerRequest("createReturnPurchaseOrderItem");
        request.addDataParam(ReturnPurchaseOrderItemDBField.ID, DataType.INT, entity.getId());
        request.addDataParam(ReturnPurchaseOrderItemDBField.RETURN_ORDER_NO, DataType.STRING, entity.getReturnOrderNo());
        request.addDataParam(ReturnPurchaseOrderItemDBField.PURCHASE_ORDER_NO, DataType.STRING, entity.getPurchaseOrderNo());
        request.addDataParam(ReturnPurchaseOrderItemDBField.ARTICLE_NUMBER, DataType.STRING, entity.getArticleNumber());
        request.addDataParam(ReturnPurchaseOrderItemDBField.QUANTITY, DataType.INT, entity.getQuantity());
        request.addDataParam(ReturnPurchaseOrderItemDBField.RETURN_QUANTITY, DataType.INT, entity.getReturnQuantity());
        request.addDataParam(ReturnPurchaseOrderItemDBField.PRODUCT_MANAGER, DataType.STRING, entity.getProductManager());
        request.addDataParam(ReturnPurchaseOrderItemDBField.PUCHASE_ASSISTANTER, DataType.STRING, entity.getPuchaseAssistanter());
        request.addDataParam(ReturnPurchaseOrderItemDBField.WAREHOUSE_ID, DataType.STRING, entity.getWarehouseId());
        request.addDataParam(ReturnPurchaseOrderItemDBField.PRODUCT_IMG, DataType.STRING, entity.getProductImage());
        request.addDataParam(ReturnPurchaseOrderItemDBField.SKU_LIFE_CYCLE_PHASE, DataType.STRING, entity.getSkuLifeCyclePhase());
        request.addDataParam(ReturnPurchaseOrderItemDBField.RECEIVE_SHIPPING_ORDER_NO, DataType.STRING, entity.getReceiveShippingOrderNo());
        request.addDataParam(ReturnPurchaseOrderItemDBField.EXCEPTION_ID, DataType.STRING, entity.getExceptionId());
        Integer autoIncrementId = SqlerTemplate.executeAndReturn(request);
        entity.setId(autoIncrementId);
    }

    /**
     * This method corresponds to the database table return_purchase_order_item
     *
     * @mbggenerated Mon Jul 15 18:00:38 CST 2019
     */
    public void updateReturnPurchaseOrderItem(ReturnPurchaseOrderItem entity) {
        SqlerRequest request = new SqlerRequest("updateReturnPurchaseOrderItemByPrimaryKey");
        request.addDataParam(ReturnPurchaseOrderItemDBField.ID, DataType.INT, entity.getId());
        
        request.addDataParam(ReturnPurchaseOrderItemDBField.RETURN_ORDER_NO, DataType.STRING, entity.getReturnOrderNo());
        request.addDataParam(ReturnPurchaseOrderItemDBField.PURCHASE_ORDER_NO, DataType.STRING, entity.getPurchaseOrderNo());
        request.addDataParam(ReturnPurchaseOrderItemDBField.ARTICLE_NUMBER, DataType.STRING, entity.getArticleNumber());
        request.addDataParam(ReturnPurchaseOrderItemDBField.QUANTITY, DataType.INT, entity.getQuantity());
        request.addDataParam(ReturnPurchaseOrderItemDBField.RETURN_QUANTITY, DataType.INT, entity.getReturnQuantity());
        request.addDataParam(ReturnPurchaseOrderItemDBField.PRODUCT_MANAGER, DataType.STRING, entity.getProductManager());
        request.addDataParam(ReturnPurchaseOrderItemDBField.PUCHASE_ASSISTANTER, DataType.STRING, entity.getPuchaseAssistanter());
        request.addDataParam(ReturnPurchaseOrderItemDBField.WAREHOUSE_ID, DataType.STRING, entity.getWarehouseId());
        request.addDataParam(ReturnPurchaseOrderItemDBField.PRODUCT_IMG, DataType.STRING, entity.getProductImage());
        request.addDataParam(ReturnPurchaseOrderItemDBField.SKU_LIFE_CYCLE_PHASE, DataType.STRING, entity.getSkuLifeCyclePhase());
        request.addDataParam(ReturnPurchaseOrderItemDBField.RECEIVE_SHIPPING_ORDER_NO, DataType.STRING, entity.getReceiveShippingOrderNo());
        request.addDataParam(ReturnPurchaseOrderItemDBField.EXCEPTION_ID, DataType.STRING, entity.getExceptionId());
        SqlerTemplate.execute(request);
    }

    /**
     * This method corresponds to the database table return_purchase_order_item
     *
     * @mbggenerated Mon Jul 15 18:00:38 CST 2019
     */
    public void batchCreateReturnPurchaseOrderItem(List<ReturnPurchaseOrderItem> entityList) {
        if (entityList != null && !entityList.isEmpty())
        {
            SqlerRequest request = new SqlerRequest("createReturnPurchaseOrderItem");
            for (ReturnPurchaseOrderItem entity : entityList)
            {
                request.addBatchDataParam(ReturnPurchaseOrderItemDBField.ID, DataType.INT, entity.getId());

                request.addBatchDataParam(ReturnPurchaseOrderItemDBField.RETURN_ORDER_NO, DataType.STRING, entity.getReturnOrderNo());
                request.addBatchDataParam(ReturnPurchaseOrderItemDBField.PURCHASE_ORDER_NO, DataType.STRING, entity.getPurchaseOrderNo());
                request.addBatchDataParam(ReturnPurchaseOrderItemDBField.ARTICLE_NUMBER, DataType.STRING, entity.getArticleNumber());
                request.addBatchDataParam(ReturnPurchaseOrderItemDBField.QUANTITY, DataType.INT, entity.getQuantity());
                request.addBatchDataParam(ReturnPurchaseOrderItemDBField.RETURN_QUANTITY, DataType.INT, entity.getReturnQuantity());
                request.addBatchDataParam(ReturnPurchaseOrderItemDBField.PRODUCT_MANAGER, DataType.STRING, entity.getProductManager());
                request.addBatchDataParam(ReturnPurchaseOrderItemDBField.PUCHASE_ASSISTANTER, DataType.STRING, entity.getPuchaseAssistanter());
                request.addBatchDataParam(ReturnPurchaseOrderItemDBField.WAREHOUSE_ID, DataType.STRING, entity.getWarehouseId());
                request.addBatchDataParam(ReturnPurchaseOrderItemDBField.PRODUCT_IMG, DataType.STRING, entity.getProductImage());
                request.addBatchDataParam(ReturnPurchaseOrderItemDBField.SKU_LIFE_CYCLE_PHASE, DataType.STRING, entity.getSkuLifeCyclePhase());
                request.addBatchDataParam(ReturnPurchaseOrderItemDBField.RECEIVE_SHIPPING_ORDER_NO, DataType.STRING, entity.getReceiveShippingOrderNo());
                request.addBatchDataParam(ReturnPurchaseOrderItemDBField.EXCEPTION_ID, DataType.STRING, entity.getExceptionId());
                request.addBatch();
            }
            SqlerTemplate.batchUpdate(request);
        }
    }

    /**
     * This method corresponds to the database table return_purchase_order_item
     *
     * @mbggenerated Mon Jul 15 18:00:38 CST 2019
     */
    public void batchUpdateReturnPurchaseOrderItem(List<ReturnPurchaseOrderItem> entityList) {
        if (entityList != null && !entityList.isEmpty())
        {
            SqlerRequest request = new SqlerRequest("updateReturnPurchaseOrderItemByPrimaryKey");
            for (ReturnPurchaseOrderItem entity : entityList)
            {
                request.addBatchDataParam(ReturnPurchaseOrderItemDBField.ID, DataType.INT, entity.getId());
                
                request.addBatchDataParam(ReturnPurchaseOrderItemDBField.RETURN_ORDER_NO, DataType.STRING, entity.getReturnOrderNo());
                request.addBatchDataParam(ReturnPurchaseOrderItemDBField.PURCHASE_ORDER_NO, DataType.STRING, entity.getPurchaseOrderNo());
                request.addBatchDataParam(ReturnPurchaseOrderItemDBField.ARTICLE_NUMBER, DataType.STRING, entity.getArticleNumber());
                request.addBatchDataParam(ReturnPurchaseOrderItemDBField.QUANTITY, DataType.INT, entity.getQuantity());
                request.addBatchDataParam(ReturnPurchaseOrderItemDBField.RETURN_QUANTITY, DataType.INT, entity.getReturnQuantity());
                request.addBatchDataParam(ReturnPurchaseOrderItemDBField.PRODUCT_MANAGER, DataType.STRING, entity.getProductManager());
                request.addBatchDataParam(ReturnPurchaseOrderItemDBField.PUCHASE_ASSISTANTER, DataType.STRING, entity.getPuchaseAssistanter());
                request.addBatchDataParam(ReturnPurchaseOrderItemDBField.WAREHOUSE_ID, DataType.STRING, entity.getWarehouseId());
                request.addBatchDataParam(ReturnPurchaseOrderItemDBField.PRODUCT_IMG, DataType.STRING, entity.getProductImage());
                request.addBatchDataParam(ReturnPurchaseOrderItemDBField.SKU_LIFE_CYCLE_PHASE, DataType.STRING, entity.getSkuLifeCyclePhase());
                request.addBatchDataParam(ReturnPurchaseOrderItemDBField.RECEIVE_SHIPPING_ORDER_NO, DataType.STRING, entity.getReceiveShippingOrderNo());
                request.addBatchDataParam(ReturnPurchaseOrderItemDBField.EXCEPTION_ID, DataType.STRING, entity.getExceptionId());
                request.addBatch();
            }
            SqlerTemplate.batchUpdate(request);
        }
    }

    /**
     * This method corresponds to the database table return_purchase_order_item
     *
     * @mbggenerated Mon Jul 15 18:00:38 CST 2019
     */
    public void deleteReturnPurchaseOrderItem(Integer primaryKey) {
        SqlerRequest request = new SqlerRequest("deleteReturnPurchaseOrderItemByPrimaryKey");
        request.addDataParam(ReturnPurchaseOrderItemDBField.ID, DataType.INT, primaryKey);
        SqlerTemplate.execute(request);
    }

    @Override
    public void deleteReturnPurchaseOrderItemBySku(String sku) {
        SqlerRequest request = new SqlerRequest("deleteReturnPurchaseOrderItemBySku");
        request.addDataParam(ReturnPurchaseOrderItemDBField.ARTICLE_NUMBER, DataType.STRING, sku);
        SqlerTemplate.execute(request);
    }
}