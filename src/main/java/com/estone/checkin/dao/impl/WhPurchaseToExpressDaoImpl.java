package com.estone.checkin.dao.impl;

import com.estone.checkin.bean.WhPurchaseToExpress;
import com.estone.checkin.bean.WhPurchaseToExpressQueryCondition;
import com.estone.checkin.dao.WhPurchaseToExpressDao;
import com.estone.checkin.dao.mapper.WhPurchaseToExpressDBField;
import com.estone.checkin.dao.mapper.WhPurchaseToExpressMapper;
import com.whq.tool.component.Pager;
import com.whq.tool.sqler.SqlerRequest;
import com.whq.tool.sqler.analyzer.DataType;
import com.estone.common.util.SqlerTemplate;
import org.springframework.stereotype.Repository;
import org.springframework.util.Assert;

import java.sql.Timestamp;
import java.util.List;

@Repository("whPurchaseToExpressDao")
public class WhPurchaseToExpressDaoImpl implements WhPurchaseToExpressDao {

    private void setQueryCondition(SqlerRequest request, WhPurchaseToExpressQueryCondition query) {
        if (query == null)
        {
            return;
        }
        // 添加查询条件
        // 搜索条件不允许出现空字符
        request.setAllowBlank(false);
        
        request.addDataParam(WhPurchaseToExpressDBField.ID, DataType.INT, query.getId());
        request.addDataParam(WhPurchaseToExpressDBField.PURCHASE_ORDER_NO, DataType.STRING, query.getPurchaseOrderNo());
        request.addDataParam(WhPurchaseToExpressDBField.EXPRESS_ID, DataType.STRING, query.getExpressId());
        request.addDataParam(WhPurchaseToExpressDBField.EXPRESS_STATUS, DataType.INT, query.getExpressStatus());
    }

    /**
     * This method corresponds to the database table wh_purchase_to_express
     *
     * @mbggenerated Mon May 27 14:33:48 CST 2019
     */
    public int queryWhPurchaseToExpressCount(WhPurchaseToExpressQueryCondition query) {
        SqlerRequest request = new SqlerRequest("queryWhPurchaseToExpressCount");
        setQueryCondition(request, query);
        return SqlerTemplate.queryForInt(request);
    }

    /**
     * This method corresponds to the database table wh_purchase_to_express
     *
     * @mbggenerated Mon May 27 14:33:48 CST 2019
     */
    public List<WhPurchaseToExpress> queryWhPurchaseToExpressList() {
        SqlerRequest request = new SqlerRequest("queryWhPurchaseToExpressList");
        return SqlerTemplate.query(request, new WhPurchaseToExpressMapper());
    }

    /**
     * This method corresponds to the database table wh_purchase_to_express
     *
     * @mbggenerated Mon May 27 14:33:48 CST 2019
     */
    public List<WhPurchaseToExpress> queryWhPurchaseToExpressList(WhPurchaseToExpressQueryCondition query, Pager pager) {
        SqlerRequest request = new SqlerRequest("queryWhPurchaseToExpressList");
        setQueryCondition(request, query);
        if(pager != null)
        {
            request.addFetch(pager.getPageNo(), pager.getPageSize());
        }
        return SqlerTemplate.query(request, new WhPurchaseToExpressMapper());
    }

    /**
     * This method corresponds to the database table wh_purchase_to_express
     *
     * @mbggenerated Mon May 27 14:33:48 CST 2019
     */
    public WhPurchaseToExpress queryWhPurchaseToExpress(Integer primaryKey) {
        Assert.notNull(primaryKey);
        SqlerRequest request = new SqlerRequest("queryWhPurchaseToExpressByPrimaryKey");
        request.addDataParam(WhPurchaseToExpressDBField.ID, DataType.INT, primaryKey);
        return SqlerTemplate.queryForObject(request, new WhPurchaseToExpressMapper());
    }

    /**
     * This method corresponds to the database table wh_purchase_to_express
     *
     * @mbggenerated Mon May 27 14:33:48 CST 2019
     */
    public WhPurchaseToExpress queryWhPurchaseToExpress(WhPurchaseToExpressQueryCondition query) {
        SqlerRequest request = new SqlerRequest("queryWhPurchaseToExpress");
        setQueryCondition(request, query);
        return SqlerTemplate.queryForObject(request, new WhPurchaseToExpressMapper());
    }

    /**
     * This method corresponds to the database table wh_purchase_to_express
     *
     * @mbggenerated Mon May 27 14:33:48 CST 2019
     */
    public void createWhPurchaseToExpress(WhPurchaseToExpress entity) {
        SqlerRequest request = new SqlerRequest("createWhPurchaseToExpress");
        request.addDataParam(WhPurchaseToExpressDBField.PURCHASE_ORDER_NO, DataType.STRING, entity.getPurchaseOrderNo());
        request.addDataParam(WhPurchaseToExpressDBField.EXPRESS_ID, DataType.STRING, entity.getExpressId());
        request.addDataParam(WhPurchaseToExpressDBField.EXPRESS_STATUS, DataType.INT, entity.getExpressStatus());
        request.addDataParam(WhPurchaseToExpressDBField.CREATION_DATE, DataType.TIMESTAMP, new Timestamp(System.currentTimeMillis()));
        Integer autoIncrementId = SqlerTemplate.executeAndReturn(request);
        entity.setId(autoIncrementId);
    }

    /**
     * This method corresponds to the database table wh_purchase_to_express
     *
     * @mbggenerated Mon May 27 14:33:48 CST 2019
     */
    public void updateWhPurchaseToExpress(WhPurchaseToExpress entity) {
        SqlerRequest request = new SqlerRequest("updateWhPurchaseToExpressByPrimaryKey");
        request.addDataParam(WhPurchaseToExpressDBField.ID, DataType.INT, entity.getId());
        request.addDataParam(WhPurchaseToExpressDBField.EXPRESS_STATUS, DataType.INT, entity.getExpressStatus());
        request.addDataParam(WhPurchaseToExpressDBField.PURCHASE_ORDER_NO, DataType.STRING, entity.getPurchaseOrderNo());
        request.addDataParam(WhPurchaseToExpressDBField.EXPRESS_ID, DataType.STRING, entity.getExpressId());
        
        SqlerTemplate.execute(request);
    }

    /**
     * This method corresponds to the database table wh_purchase_to_express
     *
     * @mbggenerated Mon May 27 14:33:48 CST 2019
     */
    public void batchCreateWhPurchaseToExpress(List<WhPurchaseToExpress> entityList) {
        if (entityList != null && !entityList.isEmpty())
        {
            SqlerRequest request = new SqlerRequest("createWhPurchaseToExpress");
            for (WhPurchaseToExpress entity : entityList)
            {
                request.addBatchDataParam(WhPurchaseToExpressDBField.PURCHASE_ORDER_NO, DataType.STRING, entity.getPurchaseOrderNo());
                request.addBatchDataParam(WhPurchaseToExpressDBField.EXPRESS_ID, DataType.STRING, entity.getExpressId());
                request.addBatchDataParam(WhPurchaseToExpressDBField.EXPRESS_STATUS, DataType.INT, entity.getExpressStatus());
                request.addBatchDataParam(WhPurchaseToExpressDBField.CREATION_DATE, DataType.TIMESTAMP, new Timestamp(System.currentTimeMillis()));
                request.addBatch();
            }
            SqlerTemplate.batchUpdate(request);
        }
    }

    /**
     * This method corresponds to the database table wh_purchase_to_express
     *
     * @mbggenerated Mon May 27 14:33:48 CST 2019
     */
    public void batchUpdateWhPurchaseToExpress(List<WhPurchaseToExpress> entityList) {
        if (entityList != null && !entityList.isEmpty())
        {
            SqlerRequest request = new SqlerRequest("updateWhPurchaseToExpressByPrimaryKey");
            for (WhPurchaseToExpress entity : entityList)
            {
                request.addBatchDataParam(WhPurchaseToExpressDBField.ID, DataType.INT, entity.getId());
                request.addBatchDataParam(WhPurchaseToExpressDBField.EXPRESS_STATUS, DataType.INT, entity.getExpressStatus());
                request.addBatchDataParam(WhPurchaseToExpressDBField.PURCHASE_ORDER_NO, DataType.STRING, entity.getPurchaseOrderNo());
                request.addBatchDataParam(WhPurchaseToExpressDBField.EXPRESS_ID, DataType.STRING, entity.getExpressId());
                
                request.addBatch();
            }
            SqlerTemplate.batchUpdate(request);
        }
    }

    /**
     * This method corresponds to the database table wh_purchase_to_express
     *
     * @mbggenerated Mon May 27 14:33:48 CST 2019
     */
    public void deleteWhPurchaseToExpress(Integer primaryKey) {
        SqlerRequest request = new SqlerRequest("deleteWhPurchaseToExpressByPrimaryKey");
        request.addDataParam(WhPurchaseToExpressDBField.ID, DataType.INT, primaryKey);
        SqlerTemplate.execute(request);
    }
}