package com.estone.checkin.dao.impl;

import java.sql.Timestamp;
import java.util.List;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Repository;
import org.springframework.util.Assert;

import com.estone.checkin.bean.ExpWaitShipments;
import com.estone.checkin.bean.ExpWaitShipmentsQueryCondition;
import com.estone.checkin.dao.ExpWaitShipmentsDao;
import com.estone.checkin.dao.mapper.ExpWaitShipmentsDBField;
import com.estone.checkin.dao.mapper.ExpWaitShipmentsMapper;
import com.estone.common.util.CommonUtils;
import com.estone.common.util.SqlerTemplate;
import com.whq.tool.component.Pager;
import com.whq.tool.context.DataContextHolder;
import com.whq.tool.sqler.SqlerRequest;
import com.whq.tool.sqler.analyzer.DataType;

@Repository("expWaitShipmentsDao")
public class ExpWaitShipmentsDaoImpl implements ExpWaitShipmentsDao {

    private void setQueryCondition(SqlerRequest request, ExpWaitShipmentsQueryCondition query) {
        if (query == null) {
            return;
        }
        // 添加只读权限
        if (query.getReadOnly() != null && query.getReadOnly())
        {
            request.setReadOnly(true);
        }
        // 添加查询条件
        // 搜索条件不允许出现空字符
        request.setAllowBlank(false);

        if(StringUtils.isNotBlank(query.getSku())){
            if (StringUtils.contains(query.getSku(), ",")) {
                request.addDataParam("sku_list", DataType.STRING, CommonUtils.splitList(query.getSku(), ","));
            } else {
                request.addDataParam(ExpWaitShipmentsDBField.SKU, DataType.STRING, query.getSku());
            }
        }

        request.addDataParam("fromCreationDate", DataType.STRING, query.getFromCreationDate());
        request.addDataParam("toCreationDate", DataType.STRING, query.getToCreationDate());

        if(StringUtils.isNotBlank(query.getRelationIdLike())){
            request.addSqlDataParam("RELATION_ID_LIKE_CONDITION", " AND relation_id like '%" + query.getRelationIdLike().trim() + "%' ");
        }

        request.addDataParam(ExpWaitShipmentsDBField.RELATION_ID, DataType.STRING, query.getRelationId());
        request.addDataParam(ExpWaitShipmentsDBField.CREATE_BY, DataType.INT, query.getCreateBy());

        request.addDataParam(ExpWaitShipmentsDBField.ID, DataType.INT, query.getId());

        if (CollectionUtils.isNotEmpty(query.getIds())) {
            request.addDataParam("ids", DataType.INT, query.getIds());
        }
        if(query.getQueryUndone()) {
            request.addSqlDataParam("QUERY_UNDONE_CONDITION", " AND ifnull(offset_quantity,0) < quantity ");
        }

    }

    @Override
    public int queryExpWaitShipmentsCount(ExpWaitShipmentsQueryCondition query) {
        SqlerRequest request = new SqlerRequest("queryExpWaitShipmentsCount");
        setQueryCondition(request, query);
        return SqlerTemplate.queryForInt(request);
    }

    @Override
    public List<ExpWaitShipments> queryExpWaitShipmentsList() {
        SqlerRequest request = new SqlerRequest("queryExpWaitShipmentsList");
        return SqlerTemplate.query(request, new ExpWaitShipmentsMapper());
    }

    @Override
    public List<ExpWaitShipments> queryExpWaitShipmentsList(ExpWaitShipmentsQueryCondition query, Pager pager) {
        SqlerRequest request = new SqlerRequest("queryExpWaitShipmentsList");
        setQueryCondition(request, query);
        if(pager != null) {
            request.addFetch(pager.getPageNo(), pager.getPageSize());
        }
        return SqlerTemplate.query(request, new ExpWaitShipmentsMapper());
    }

    @Override
    public ExpWaitShipments queryExpWaitShipments(Integer primaryKey) {
        Assert.notNull(primaryKey, "primaryKey is null!");
        SqlerRequest request = new SqlerRequest("queryExpWaitShipmentsByPrimaryKey");
        request.addDataParam(ExpWaitShipmentsDBField.ID, DataType.INT, primaryKey);
        return SqlerTemplate.queryForObject(request, new ExpWaitShipmentsMapper());
    }

    @Override
    public ExpWaitShipments queryExpWaitShipments(ExpWaitShipmentsQueryCondition query) {
        SqlerRequest request = new SqlerRequest("queryExpWaitShipments");
        setQueryCondition(request, query);
        return SqlerTemplate.queryForObject(request, new ExpWaitShipmentsMapper());
    }

    @Override
    public void createExpWaitShipments(ExpWaitShipments entity) {
        SqlerRequest request = new SqlerRequest("createExpWaitShipments");
        request.addDataParam(ExpWaitShipmentsDBField.SKU, DataType.STRING, entity.getSku());
        request.addDataParam(ExpWaitShipmentsDBField.WAREHOUSE_TYPE, DataType.STRING, entity.getWarehouseType());
        request.addDataParam(ExpWaitShipmentsDBField.QUANTITY, DataType.INT, entity.getQuantity());
        request.addDataParam(ExpWaitShipmentsDBField.OFFSET_QUANTITY, DataType.INT, entity.getOffsetQuantity());
        request.addDataParam(ExpWaitShipmentsDBField.RELATION_ID, DataType.STRING, entity.getRelationId());
        request.addDataParam(ExpWaitShipmentsDBField.CREATE_BY, DataType.INT,
                entity.getCreateBy() == null ? DataContextHolder.getUserId() : entity.getCreateBy());
        request.addDataParam(ExpWaitShipmentsDBField.CREATION_DATE, DataType.TIMESTAMP, new Timestamp(System.currentTimeMillis()));
        request.addDataParam(ExpWaitShipmentsDBField.LAST_UPDATE_BY, DataType.INT,
                entity.getLastUpdateBy() == null ? DataContextHolder.getUserId() : entity.getLastUpdateBy());
        request.addDataParam(ExpWaitShipmentsDBField.LAST_UPDATE_DATE, DataType.TIMESTAMP, new Timestamp(System.currentTimeMillis()));
        request.addDataParam(ExpWaitShipmentsDBField.RELATION_DETAIL_JSON, DataType.STRING, entity.getRelationDetailJson());
        Integer autoIncrementId = SqlerTemplate.executeAndReturn(request);
        entity.setId(autoIncrementId);
    }

    @Override
    public void updateExpWaitShipments(ExpWaitShipments entity) {
        SqlerRequest request = new SqlerRequest("updateExpWaitShipmentsByPrimaryKey");
        request.addDataParam(ExpWaitShipmentsDBField.ID, DataType.INT, entity.getId());

        request.addDataParam(ExpWaitShipmentsDBField.SKU, DataType.STRING, entity.getSku());
        request.addDataParam(ExpWaitShipmentsDBField.WAREHOUSE_TYPE, DataType.STRING, entity.getWarehouseType());
        request.addDataParam(ExpWaitShipmentsDBField.QUANTITY, DataType.INT, entity.getQuantity());
        request.addDataParam(ExpWaitShipmentsDBField.OFFSET_QUANTITY, DataType.INT, entity.getOffsetQuantity());
        request.addDataParam(ExpWaitShipmentsDBField.RELATION_ID, DataType.STRING, entity.getRelationId());

        request.addDataParam(ExpWaitShipmentsDBField.LAST_UPDATE_BY, DataType.INT,
                entity.getLastUpdateBy() == null ? DataContextHolder.getUserId() : entity.getLastUpdateBy());
        request.addDataParam(ExpWaitShipmentsDBField.LAST_UPDATE_DATE, DataType.TIMESTAMP, new Timestamp(System.currentTimeMillis()));
        request.addDataParam(ExpWaitShipmentsDBField.RELATION_DETAIL_JSON, DataType.STRING, entity.getRelationDetailJson());
        SqlerTemplate.execute(request);
    }

    @Override
    public void batchCreateExpWaitShipments(List<ExpWaitShipments> entityList) {
        if (entityList != null && !entityList.isEmpty()) {
            SqlerRequest request = new SqlerRequest("createExpWaitShipments");
            for (ExpWaitShipments entity : entityList) {
                request.addBatchDataParam(ExpWaitShipmentsDBField.SKU, DataType.STRING, entity.getSku());
                request.addBatchDataParam(ExpWaitShipmentsDBField.WAREHOUSE_TYPE, DataType.STRING, entity.getWarehouseType());
                request.addBatchDataParam(ExpWaitShipmentsDBField.QUANTITY, DataType.INT, entity.getQuantity());
                request.addBatchDataParam(ExpWaitShipmentsDBField.OFFSET_QUANTITY, DataType.INT, entity.getOffsetQuantity());
                request.addBatchDataParam(ExpWaitShipmentsDBField.RELATION_ID, DataType.STRING, entity.getRelationId());
                request.addBatchDataParam(ExpWaitShipmentsDBField.CREATE_BY, DataType.INT,
                        entity.getCreateBy() == null ? DataContextHolder.getUserId() : entity.getCreateBy());
                request.addBatchDataParam(ExpWaitShipmentsDBField.CREATION_DATE, DataType.TIMESTAMP, new Timestamp(System.currentTimeMillis()));
                request.addBatchDataParam(ExpWaitShipmentsDBField.LAST_UPDATE_BY, DataType.INT,
                        entity.getLastUpdateBy() == null ? DataContextHolder.getUserId() : entity.getLastUpdateBy());
                request.addBatchDataParam(ExpWaitShipmentsDBField.LAST_UPDATE_DATE, DataType.TIMESTAMP, new Timestamp(System.currentTimeMillis()));
                request.addBatchDataParam(ExpWaitShipmentsDBField.RELATION_DETAIL_JSON, DataType.STRING, entity.getRelationDetailJson());
                request.addBatch();
            }
            SqlerTemplate.batchUpdate(request);
        }
    }

    @Override
    public void batchUpdateExpWaitShipments(List<ExpWaitShipments> entityList) {
        if (entityList != null && !entityList.isEmpty()) {
            SqlerRequest request = new SqlerRequest("updateExpWaitShipmentsByPrimaryKey");
            for (ExpWaitShipments entity : entityList) {
                request.addBatchDataParam(ExpWaitShipmentsDBField.ID, DataType.INT, entity.getId());

                request.addBatchDataParam(ExpWaitShipmentsDBField.SKU, DataType.STRING, entity.getSku());
                request.addBatchDataParam(ExpWaitShipmentsDBField.WAREHOUSE_TYPE, DataType.STRING, entity.getWarehouseType());
                request.addBatchDataParam(ExpWaitShipmentsDBField.QUANTITY, DataType.INT, entity.getQuantity());
                request.addBatchDataParam(ExpWaitShipmentsDBField.OFFSET_QUANTITY, DataType.INT, entity.getOffsetQuantity());
                request.addBatchDataParam(ExpWaitShipmentsDBField.RELATION_ID, DataType.STRING, entity.getRelationId());

                request.addBatchDataParam(ExpWaitShipmentsDBField.LAST_UPDATE_BY, DataType.INT,
                        entity.getLastUpdateBy() == null ? DataContextHolder.getUserId() : entity.getLastUpdateBy());
                request.addBatchDataParam(ExpWaitShipmentsDBField.LAST_UPDATE_DATE, DataType.TIMESTAMP, new Timestamp(System.currentTimeMillis()));
                request.addBatchDataParam(ExpWaitShipmentsDBField.RELATION_DETAIL_JSON, DataType.STRING, entity.getRelationDetailJson());
                request.addBatch();
            }
            SqlerTemplate.batchUpdate(request);
        }
    }

    @Override
    public void deleteExpWaitShipments(Integer primaryKey) {
        SqlerRequest request = new SqlerRequest("deleteExpWaitShipmentsByPrimaryKey");
        request.addDataParam(ExpWaitShipmentsDBField.ID, DataType.INT, primaryKey);
        SqlerTemplate.execute(request);
    }
}