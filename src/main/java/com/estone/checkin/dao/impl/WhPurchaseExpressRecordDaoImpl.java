package com.estone.checkin.dao.impl;

import com.estone.checkin.bean.WhPurchaseExpressRecord;
import com.estone.checkin.bean.WhPurchaseExpressRecordQueryCondition;
import com.estone.checkin.dao.WhPurchaseExpressRecordDao;
import com.estone.checkin.dao.mapper.WhPurchaseExpressRecordDBField;
import com.estone.checkin.dao.mapper.WhPurchaseExpressRecordMapper;
import com.estone.common.util.CommonUtils;
import com.whq.tool.component.Pager;
import com.whq.tool.context.DataContextHolder;
import com.whq.tool.sqler.SqlerRequest;
import com.whq.tool.sqler.analyzer.DataType;
import com.estone.common.util.SqlerTemplate;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Repository;
import org.springframework.util.Assert;

import java.sql.Timestamp;
import java.util.Arrays;
import java.util.List;

@Repository("whPurchaseExpressRecordDao")
public class WhPurchaseExpressRecordDaoImpl implements WhPurchaseExpressRecordDao {

    private void setQueryCondition(SqlerRequest request, WhPurchaseExpressRecordQueryCondition query) {
        if (query == null) {
            return;
        }
        // 添加查询条件
        // 搜索条件不允许出现空字符
        request.setAllowBlank(false);

        request.addDataParam(WhPurchaseExpressRecordDBField.ID, DataType.INT, query.getId());
        request.addDataParam(WhPurchaseExpressRecordDBField.SPLIT_USER, DataType.INT, query.getSplitUser());// 拆分人
        request.addDataParam(WhPurchaseExpressRecordDBField.CREATED_BY, DataType.INT, query.getCreatedBy());// 签收人
        request.addDataParam(WhPurchaseExpressRecordDBField.RECEIVE_USER, DataType.INT, query.getReceiveUser());// 领取人
        request.addDataParam(WhPurchaseExpressRecordDBField.SHIPPING_CPN, DataType.STRING, query.getShippingCpn());// 物流公司

//        request.addDataParam(WhPurchaseExpressRecordDBField.BOX_NO, DataType.STRING, query.getBoxNo());// 周转框
        if (StringUtils.isNotBlank(query.getBoxNo())) {
            request.addDataParam(WhPurchaseExpressRecordDBField.BOX_NO,
                    DataType.STRING,
                    CommonUtils.splitList(query.getBoxNo(), ","));
        }
        if (query.getRejectRecord()) {
            request.addDataParam(WhPurchaseExpressRecordDBField.STATUS, DataType.INT, query.getStatus());// 状态
        } else {
            request.addSqlDataParam("STATUS_QUERY", " AND (status != 1 OR status IS NULL)");
        }

        // request.addDataParam(WhPurchaseExpressRecordDBField.PURCHASE_ORDER_NO,
        // DataType.STRING, query.getPurchaseOrderNo());// 采购单号
        if (StringUtils.isNotBlank(query.getPurchaseOrderNo())) {
            request.addFuzzyDataParam("purchase_order_no_like", query.getPurchaseOrderNo().trim());// 采购单号
           /* request.addDataParam("purchase_order_no_like",
                    DataType.STRING,
                    query.getPurchaseOrderNo().replace(",", "|"));*/
        }
        if (query.getCheckInScanFindRecord() && StringUtils.isNotBlank(query.getPurchaseOrderNo())) {
            request.addDataParam("purchase_order_no_query", DataType.STRING, query.getPurchaseOrderNo().trim());// 优化查询用
        }

        if (false == query.getCheckInScanFindRecord()) {
//            request.addDataParam(WhPurchaseExpressRecordDBField.TRACKING_NUMBER, DataType.STRING,
//                    query.getTrackingNumber());// 快递单号

            if (StringUtils.isNotBlank(query.getTrackingNumber())) {
                request.addDataParam(WhPurchaseExpressRecordDBField.TRACKING_NUMBER,
                        DataType.STRING,
                        CommonUtils.splitList(query.getTrackingNumber(), ","));
            }
        }
        request.addDataParam(WhPurchaseExpressRecordDBField.WAREHOUSE_ID, DataType.INT, query.getWarehouseId());// 仓库
        request.addDataParam(WhPurchaseExpressRecordDBField.QUANTITY, DataType.INT, query.getQuantity());// 件数

        // 签收时间查询
        request.addDataParam("from_creation_date", DataType.STRING, query.getFromCreationDate());
        request.addDataParam("to_creation_date", DataType.STRING, query.getToCreationDate());
        // 拆分时间查询
        request.addDataParam("from_split_date", DataType.STRING, query.getFromSplitDate());
        request.addDataParam("to_split_date", DataType.STRING, query.getToSplitDate());

        if (StringUtils.isNotBlank(query.getFromReceiveDate()) && StringUtils.isNotBlank(query.getToReceiveDate())) {
            // 领取时间
            request.addDataParam("from_receive_date", DataType.STRING, query.getFromReceiveDate());
            request.addDataParam("to_receive_date", DataType.STRING, query.getToReceiveDate());
        }

        if (StringUtils.isNotBlank(query.getIsSplitStr())) {// 是否拆分
            if ("yes".equals(query.getIsSplitStr())) {
                request.addSqlDataParam("IsSplitStr", " AND split_date is NOT NULL ");
            } else if ("no".equals(query.getIsSplitStr())) {
                request.addSqlDataParam("IsSplitStr", " AND split_date is NULL ");
            }
        }
        if (StringUtils.isNotBlank(query.getHasPurchaseOrderNoStr())) {// 是否绑定追踪号
            if ("yes".equals(query.getHasPurchaseOrderNoStr())) {
                request.addSqlDataParam("HasPurchaseOrderNoStr",
                        " AND purchase_order_no is NOT NULL AND purchase_order_no != ''");
            } else if ("no".equals(query.getHasPurchaseOrderNoStr())) {
                request.addSqlDataParam("HasPurchaseOrderNoStr",
                        " AND (purchase_order_no is NULL OR purchase_order_no = '')");
            }
        }

        if (StringUtils.isNotBlank(query.getHasCommentStr())) {// 是否备注
            if ("yes".equals(query.getHasCommentStr())) {
                request.addSqlDataParam("HasCommentStr",
                        " AND comment is NOT NULL AND comment != ''");
            } else if ("no".equals(query.getHasCommentStr())) {
                request.addSqlDataParam("HasCommentStr",
                        " AND (comment is NULL OR comment = '')");
            }
        }

        // 采购单类型
        if (StringUtils.isNotBlank(query.getPurchaseType())) {
            String purchaseType = query.getPurchaseType();
            if (purchaseType.contains("FBA") || purchaseType.contains("TJ") || purchaseType.contains("VW")) {
                /*request.addSqlDataParam("PURCHASE_TYPE",
                        " AND tracking_number IN ( select e.express_id from wh_purchase_to_express e LEFT JOIN wh_purchase_order p" +
                                " on e.purchase_order_no=p.purchase_order_no where p.flags='"+ purchaseType +"')");*/
                request.addSqlDataParam("PURCHASE_TYPE",
                        " AND tracking_number IN ( select e.express_id from wh_purchase_to_express e LEFT JOIN wh_purchase_order p" +
                                " on e.purchase_order_no=p.purchase_order_no where FIND_IN_SET('" + purchaseType + "',p.flags))");
            } else if (purchaseType.contains("ORDINARY") || purchaseType.contains("URGENCY")) {
                request.addSqlDataParam("PURCHASE_TYPE",
                        " AND tracking_number IN ( select e.express_id from wh_purchase_to_express e LEFT JOIN wh_purchase_order p" +
                                " on e.purchase_order_no=p.purchase_order_no where p.purchase_level='" + purchaseType + "')");
            } else if (purchaseType.contains("JERRY")) {
                request.addSqlDataParam("PURCHASE_TYPE",
                        " AND tracking_number IN ( select e.express_id from wh_purchase_to_express e LEFT JOIN wh_purchase_order p" +
                                " on e.purchase_order_no=p.purchase_order_no LEFT JOIN wh_purchase_item i on i.p_id=p.id where i.sku like 'JR%')");
            } else if (purchaseType.contains("WL")) {
                request.addDataParam(WhPurchaseExpressRecordDBField.SERIAL_NUMBER, DataType.INT, 1);
            }
        }

        // 排序
        if (StringUtils.isNotBlank(query.getOrderBy())) {
            request.addSqlDataParam("order_by", "ORDER BY " + query.getOrderBy());
        }

        if (CollectionUtils.isNotEmpty(query.getIds())) {
            request.addDataParam("ids", DataType.INT, query.getIds());
        }
        if (CollectionUtils.isNotEmpty(query.getTrackingNos())) {
            request.addDataParam("trackingNos", DataType.STRING, query.getTrackingNos());
        }
        // 入库扫描一个采购单对应多个快递单的查询
        if (true == query.getCheckInScanFindRecord() && StringUtils.isNotBlank(query.getTrackingNumber())) {
            request.addSqlDataParam("CHECK_IN_SCAN_FIND_RECORD",
                    "AND DATE(creation_date) = (SELECT DATE(creation_date) FROM wh_purchase_express_record WHERE tracking_number = '"
                            + query.getTrackingNumber() + "')");
        }

        if (query.getCheckInScanStatus() != null) {
            request.addDataParam("check_in_scan_status", DataType.INT, query.getCheckInScanStatus());
        }

        if (query.getLocalWarehouseBag() != null) {
            if (query.getLocalWarehouseBag()) {
                // 是本仓包裹
                request.addSqlDataParam("HasPurchaseOrderNoStr",
                        " AND (scan_mark IS NULL or scan_mark != 1) ");
            } else {
                // 非本仓包裹
                request.addSqlDataParam("HasPurchaseOrderNoStr",
                        " AND scan_mark = 1 ");
                request.addDataParam(WhPurchaseExpressRecordDBField.SCAN_MARK, DataType.INT, 1);
            }
        }
    }

    /**
     * This method corresponds to the database table wh_purchase_express_record
     *
     * @mbggenerated Fri Aug 17 10:33:25 CST 2018
     */
    public int queryWhPurchaseExpressRecordCount(WhPurchaseExpressRecordQueryCondition query) {
        SqlerRequest request = new SqlerRequest("queryWhPurchaseExpressRecordCount");
        setQueryCondition(request, query);
        return SqlerTemplate.queryForInt(request);
    }

    /**
     * This method corresponds to the database table wh_purchase_express_record
     *
     * @mbggenerated Fri Aug 17 10:33:25 CST 2018
     */
    public List<WhPurchaseExpressRecord> queryWhPurchaseExpressRecordList() {
        SqlerRequest request = new SqlerRequest("queryWhPurchaseExpressRecordList");
        return SqlerTemplate.query(request, new WhPurchaseExpressRecordMapper());
    }

    /**
     * This method corresponds to the database table wh_purchase_express_record
     *
     * @mbggenerated Fri Aug 17 10:33:25 CST 2018
     */
    public List<WhPurchaseExpressRecord> queryWhPurchaseExpressRecordList(WhPurchaseExpressRecordQueryCondition query,
                                                                          Pager pager) {
        SqlerRequest request = new SqlerRequest("queryWhPurchaseExpressRecordList");
        setQueryCondition(request, query);
        if (pager != null) {
            request.addFetch(pager.getPageNo(), pager.getPageSize());
        }
        return SqlerTemplate.query(request, new WhPurchaseExpressRecordMapper(true));
    }

    /**
     * This method corresponds to the database table wh_purchase_express_record
     *
     * @mbggenerated Fri Aug 17 10:33:25 CST 2018
     */
    public WhPurchaseExpressRecord queryWhPurchaseExpressRecord(Integer primaryKey) {
        Assert.notNull(primaryKey);
        SqlerRequest request = new SqlerRequest("queryWhPurchaseExpressRecordByPrimaryKey");
        request.addDataParam(WhPurchaseExpressRecordDBField.ID, DataType.INT, primaryKey);
        return SqlerTemplate.queryForObject(request, new WhPurchaseExpressRecordMapper());
    }

    /**
     * This method corresponds to the database table wh_purchase_express_record
     *
     * @mbggenerated Fri Aug 17 10:33:25 CST 2018
     */
    public WhPurchaseExpressRecord queryWhPurchaseExpressRecord(WhPurchaseExpressRecordQueryCondition query) {
        SqlerRequest request = new SqlerRequest("queryWhPurchaseExpressRecord");
        setQueryCondition(request, query);
        return SqlerTemplate.queryForObject(request, new WhPurchaseExpressRecordMapper());
    }

    /**
     * This method corresponds to the database table wh_purchase_express_record
     *
     * @mbggenerated Fri Aug 17 10:33:25 CST 2018
     */
    public void createWhPurchaseExpressRecord(WhPurchaseExpressRecord entity) {
        SqlerRequest request = new SqlerRequest("createWhPurchaseExpressRecord");
        request.addDataParam(WhPurchaseExpressRecordDBField.TRACKING_NUMBER, DataType.STRING,
                entity.getTrackingNumber());
        request.addDataParam(WhPurchaseExpressRecordDBField.PURCHASE_ORDER_NO, DataType.STRING,
                entity.getPurchaseOrderNo());
        request.addDataParam(WhPurchaseExpressRecordDBField.SERIAL_NUMBER, DataType.INT, entity.getSerialNumber());
        request.addDataParam(WhPurchaseExpressRecordDBField.CREATED_BY, DataType.INT,
                entity.getCreatedBy() == null ? DataContextHolder.getUserId() : entity.getCreatedBy());
        request.addDataParam(WhPurchaseExpressRecordDBField.CREATION_DATE, DataType.TIMESTAMP,
                new Timestamp(System.currentTimeMillis()));
        request.addDataParam(WhPurchaseExpressRecordDBField.SPLIT_USER, DataType.INT, entity.getSplitUser());
        request.addDataParam(WhPurchaseExpressRecordDBField.SPLIT_DATE, DataType.TIMESTAMP, entity.getSplitDate());
        request.addDataParam(WhPurchaseExpressRecordDBField.LAST_UPDATED_BY, DataType.INT,
                entity.getLastUpdatedBy() == null ? DataContextHolder.getUserId() : entity.getLastUpdatedBy());
        request.addDataParam(WhPurchaseExpressRecordDBField.LAST_UPDATE_DATE, DataType.TIMESTAMP,
                new Timestamp(System.currentTimeMillis()));
        request.addDataParam(WhPurchaseExpressRecordDBField.COMMENT, DataType.STRING, entity.getComment());
        request.addDataParam(WhPurchaseExpressRecordDBField.REASON, DataType.STRING, entity.getReason());
        request.addDataParam(WhPurchaseExpressRecordDBField.WAREHOUSE_ID, DataType.INT, entity.getWarehouseId());
        request.addDataParam(WhPurchaseExpressRecordDBField.WEIGHT, DataType.DOUBLE, entity.getWeight());
        request.addDataParam(WhPurchaseExpressRecordDBField.TOTALWEIGHT, DataType.DOUBLE, entity.getTotalWeight());
        request.addDataParam(WhPurchaseExpressRecordDBField.QUANTITY, DataType.INT, entity.getQuantity());
        request.addDataParam(WhPurchaseExpressRecordDBField.BOX_NO, DataType.STRING, entity.getBoxNo());
        request.addDataParam(WhPurchaseExpressRecordDBField.SHIPPING_CPN, DataType.STRING, entity.getShippingCpn());
        request.addDataParam(WhPurchaseExpressRecordDBField.RECEIVE_USER, DataType.INT, entity.getReceiveUser());
        request.addDataParam(WhPurchaseExpressRecordDBField.RECEIVE_DATE, DataType.TIMESTAMP, entity.getReceiveDate());
        request.addDataParam(WhPurchaseExpressRecordDBField.STATUS, DataType.INT, entity.getStatus());
        request.addDataParam(WhPurchaseExpressRecordDBField.LENGTH, DataType.DOUBLE, entity.getLength());
        request.addDataParam(WhPurchaseExpressRecordDBField.WIDTH, DataType.DOUBLE, entity.getWidth());
        request.addDataParam(WhPurchaseExpressRecordDBField.HEIGHT, DataType.DOUBLE, entity.getHeight());
        request.addDataParam(WhPurchaseExpressRecordDBField.IMAGE_URL, DataType.STRING, entity.getImageUrl());
        request.addDataParam(WhPurchaseExpressRecordDBField.SCAN_MARK, DataType.INT, entity.getScanMark());
        Integer autoIncrementId = SqlerTemplate.executeAndReturn(request);
        entity.setId(autoIncrementId);
    }

    /**
     * This method corresponds to the database table wh_purchase_express_record
     *
     * @mbggenerated Fri Aug 17 10:33:25 CST 2018
     */
    public void updateWhPurchaseExpressRecord(WhPurchaseExpressRecord entity) {
        SqlerRequest request = new SqlerRequest("updateWhPurchaseExpressRecordByPrimaryKey");
        request.addDataParam(WhPurchaseExpressRecordDBField.ID, DataType.INT, entity.getId());

        request.addDataParam(WhPurchaseExpressRecordDBField.TRACKING_NUMBER, DataType.STRING,
                entity.getTrackingNumber());
        request.addDataParam(WhPurchaseExpressRecordDBField.PURCHASE_ORDER_NO, DataType.STRING,
                StringUtils.isBlank(entity.getPurchaseOrderNo()) ? null : entity.getPurchaseOrderNo());
        request.addDataParam(WhPurchaseExpressRecordDBField.SERIAL_NUMBER, DataType.INT, entity.getSerialNumber());

        request.addDataParam(WhPurchaseExpressRecordDBField.SPLIT_USER, DataType.INT, entity.getSplitUser());
        request.addDataParam(WhPurchaseExpressRecordDBField.SPLIT_DATE, DataType.TIMESTAMP, entity.getSplitDate());
        request.addDataParam(WhPurchaseExpressRecordDBField.LAST_UPDATED_BY, DataType.INT,
                entity.getLastUpdatedBy() == null ? DataContextHolder.getUserId() : entity.getLastUpdatedBy());
        request.addDataParam(WhPurchaseExpressRecordDBField.LAST_UPDATE_DATE, DataType.TIMESTAMP,
                new Timestamp(System.currentTimeMillis()));
        request.addDataParam(WhPurchaseExpressRecordDBField.COMMENT, DataType.STRING, entity.getComment());
        request.addDataParam(WhPurchaseExpressRecordDBField.SHIPPING_CPN, DataType.STRING, entity.getShippingCpn());
        request.addDataParam(WhPurchaseExpressRecordDBField.REASON, DataType.STRING, entity.getReason());
        request.addDataParam(WhPurchaseExpressRecordDBField.WAREHOUSE_ID, DataType.INT, entity.getWarehouseId());
        request.addDataParam(WhPurchaseExpressRecordDBField.WEIGHT, DataType.DOUBLE, entity.getWeight());
        request.addDataParam(WhPurchaseExpressRecordDBField.TOTALWEIGHT, DataType.DOUBLE, entity.getTotalWeight());
        request.addDataParam(WhPurchaseExpressRecordDBField.QUANTITY, DataType.INT, entity.getQuantity());
        request.addDataParam(WhPurchaseExpressRecordDBField.BOX_NO, DataType.STRING, entity.getBoxNo());
        request.addDataParam("check_in_scan_status", DataType.BOOLEAN, entity.getCheckInScanStatus());
        request.addDataParam("check_in_scanner", DataType.INT, entity.getCheckInScanner());
        request.addDataParam("check_in_scan_time", DataType.TIMESTAMP, entity.getCheckInScanTime());
        request.addDataParam(WhPurchaseExpressRecordDBField.BOX_NO, DataType.STRING, entity.getBoxNo());
        request.addDataParam(WhPurchaseExpressRecordDBField.RECEIVE_USER, DataType.INT, entity.getReceiveUser());
        request.addDataParam(WhPurchaseExpressRecordDBField.RECEIVE_DATE, DataType.TIMESTAMP, entity.getReceiveDate());
        request.addDataParam(WhPurchaseExpressRecordDBField.STATUS, DataType.INT, entity.getStatus());
        request.addDataParam(WhPurchaseExpressRecordDBField.LENGTH, DataType.DOUBLE, entity.getLength());
        request.addDataParam(WhPurchaseExpressRecordDBField.WIDTH, DataType.DOUBLE, entity.getWidth());
        request.addDataParam(WhPurchaseExpressRecordDBField.HEIGHT, DataType.DOUBLE, entity.getHeight());
        request.addDataParam(WhPurchaseExpressRecordDBField.IMAGE_URL, DataType.STRING, entity.getImageUrl());
        request.addDataParam(WhPurchaseExpressRecordDBField.SCAN_MARK, DataType.INT, entity.getScanMark());
        SqlerTemplate.execute(request);
    }

    @Override
    public List<WhPurchaseExpressRecord> getNotFinishExpressRecord(Integer userId) {
        SqlerRequest request = new SqlerRequest("getNotFinishExpressRecord");
        request.addDataParam("check_in_scanner", DataType.INT, userId);
        return SqlerTemplate.query(request, new WhPurchaseExpressRecordMapper());
    }

    /**
     * This method corresponds to the database table wh_purchase_express_record
     *
     * @mbggenerated Fri Aug 17 10:33:25 CST 2018
     */
    public void batchCreateWhPurchaseExpressRecord(List<WhPurchaseExpressRecord> entityList) {
        if (entityList != null && !entityList.isEmpty()) {
            SqlerRequest request = new SqlerRequest("createWhPurchaseExpressRecord");
            for (WhPurchaseExpressRecord entity : entityList) {
                request.addBatchDataParam(WhPurchaseExpressRecordDBField.TRACKING_NUMBER, DataType.STRING,
                        entity.getTrackingNumber());
                request.addBatchDataParam(WhPurchaseExpressRecordDBField.PURCHASE_ORDER_NO, DataType.STRING,
                        entity.getPurchaseOrderNo());
                request.addBatchDataParam(WhPurchaseExpressRecordDBField.SERIAL_NUMBER, DataType.INT,
                        entity.getSerialNumber());
                request.addBatchDataParam(WhPurchaseExpressRecordDBField.CREATED_BY, DataType.INT,
                        entity.getCreatedBy() == null ? DataContextHolder.getUserId() : entity.getCreatedBy());
                request.addBatchDataParam(WhPurchaseExpressRecordDBField.CREATION_DATE, DataType.TIMESTAMP,
                        new Timestamp(System.currentTimeMillis()));
                request.addBatchDataParam(WhPurchaseExpressRecordDBField.SPLIT_USER, DataType.INT,
                        entity.getSplitUser());
                request.addBatchDataParam(WhPurchaseExpressRecordDBField.SPLIT_DATE, DataType.TIMESTAMP,
                        entity.getSplitDate());
                request.addBatchDataParam(WhPurchaseExpressRecordDBField.LAST_UPDATED_BY, DataType.INT,
                        entity.getLastUpdatedBy() == null ? DataContextHolder.getUserId() : entity.getLastUpdatedBy());
                request.addBatchDataParam(WhPurchaseExpressRecordDBField.LAST_UPDATE_DATE, DataType.TIMESTAMP,
                        new Timestamp(System.currentTimeMillis()));
                request.addBatchDataParam(WhPurchaseExpressRecordDBField.COMMENT, DataType.STRING, entity.getComment());
                request.addBatchDataParam(WhPurchaseExpressRecordDBField.SHIPPING_CPN, DataType.STRING, entity.getShippingCpn());
                request.addBatchDataParam(WhPurchaseExpressRecordDBField.REASON, DataType.STRING, entity.getReason());
                request.addBatchDataParam(WhPurchaseExpressRecordDBField.WAREHOUSE_ID, DataType.INT,
                        entity.getWarehouseId());
                request.addBatchDataParam(WhPurchaseExpressRecordDBField.WEIGHT, DataType.DOUBLE, entity.getWeight());
                request.addBatchDataParam(WhPurchaseExpressRecordDBField.TOTALWEIGHT, DataType.DOUBLE,
                        entity.getTotalWeight());
                request.addBatchDataParam(WhPurchaseExpressRecordDBField.QUANTITY, DataType.INT, entity.getQuantity());
                request.addBatchDataParam(WhPurchaseExpressRecordDBField.BOX_NO, DataType.STRING, entity.getBoxNo());
                request.addBatchDataParam(WhPurchaseExpressRecordDBField.RECEIVE_USER, DataType.INT,
                        entity.getReceiveUser());
                request.addBatchDataParam(WhPurchaseExpressRecordDBField.RECEIVE_DATE, DataType.TIMESTAMP,
                        entity.getReceiveDate());
                request.addBatchDataParam(WhPurchaseExpressRecordDBField.STATUS, DataType.INT, entity.getStatus());
                request.addBatchDataParam(WhPurchaseExpressRecordDBField.LENGTH, DataType.DOUBLE, entity.getLength());
                request.addBatchDataParam(WhPurchaseExpressRecordDBField.WIDTH, DataType.DOUBLE, entity.getWidth());
                request.addBatchDataParam(WhPurchaseExpressRecordDBField.HEIGHT, DataType.DOUBLE, entity.getHeight());
                request.addBatchDataParam(WhPurchaseExpressRecordDBField.IMAGE_URL, DataType.STRING, entity.getImageUrl());
                request.addBatchDataParam(WhPurchaseExpressRecordDBField.SCAN_MARK, DataType.INT, entity.getScanMark());
                request.addBatch();
            }
            SqlerTemplate.batchUpdate(request);
        }
    }

    /**
     * This method corresponds to the database table wh_purchase_express_record
     *
     * @mbggenerated Fri Aug 17 10:33:25 CST 2018
     */
    public void batchUpdateWhPurchaseExpressRecord(List<WhPurchaseExpressRecord> entityList) {
        if (entityList != null && !entityList.isEmpty()) {
            SqlerRequest request = new SqlerRequest("updateWhPurchaseExpressRecordByPrimaryKey");
            for (WhPurchaseExpressRecord entity : entityList) {
                request.addBatchDataParam(WhPurchaseExpressRecordDBField.ID, DataType.INT, entity.getId());

                request.addBatchDataParam(WhPurchaseExpressRecordDBField.TRACKING_NUMBER, DataType.STRING,
                        entity.getTrackingNumber());
                request.addBatchDataParam(WhPurchaseExpressRecordDBField.PURCHASE_ORDER_NO, DataType.STRING,
                        StringUtils.isBlank(entity.getPurchaseOrderNo()) ? null : entity.getPurchaseOrderNo());
                request.addBatchDataParam(WhPurchaseExpressRecordDBField.SERIAL_NUMBER, DataType.INT,
                        entity.getSerialNumber());

                request.addBatchDataParam(WhPurchaseExpressRecordDBField.SPLIT_USER, DataType.INT,
                        entity.getSplitUser());
                request.addBatchDataParam(WhPurchaseExpressRecordDBField.SPLIT_DATE, DataType.TIMESTAMP,
                        entity.getSplitDate());
                request.addBatchDataParam(WhPurchaseExpressRecordDBField.LAST_UPDATED_BY, DataType.INT,
                        entity.getLastUpdatedBy() == null ? DataContextHolder.getUserId() : entity.getLastUpdatedBy());
                request.addBatchDataParam(WhPurchaseExpressRecordDBField.LAST_UPDATE_DATE, DataType.TIMESTAMP,
                        new Timestamp(System.currentTimeMillis()));
                request.addBatchDataParam(WhPurchaseExpressRecordDBField.COMMENT, DataType.STRING, entity.getComment());
                request.addBatchDataParam(WhPurchaseExpressRecordDBField.SHIPPING_CPN, DataType.STRING, entity.getShippingCpn());
                request.addBatchDataParam(WhPurchaseExpressRecordDBField.REASON, DataType.STRING, entity.getReason());
                request.addBatchDataParam(WhPurchaseExpressRecordDBField.WAREHOUSE_ID, DataType.INT,
                        entity.getWarehouseId());
                request.addBatchDataParam(WhPurchaseExpressRecordDBField.WEIGHT, DataType.DOUBLE, entity.getWeight());
                request.addBatchDataParam(WhPurchaseExpressRecordDBField.TOTALWEIGHT, DataType.DOUBLE,
                        entity.getTotalWeight());
                request.addBatchDataParam(WhPurchaseExpressRecordDBField.QUANTITY, DataType.INT, entity.getQuantity());
                request.addBatchDataParam(WhPurchaseExpressRecordDBField.BOX_NO, DataType.STRING, entity.getBoxNo());
                request.addBatchDataParam(WhPurchaseExpressRecordDBField.RECEIVE_USER, DataType.INT,
                        entity.getReceiveUser());
                request.addBatchDataParam(WhPurchaseExpressRecordDBField.RECEIVE_DATE, DataType.TIMESTAMP,
                        entity.getReceiveDate());
                request.addBatchDataParam(WhPurchaseExpressRecordDBField.STATUS, DataType.INT, entity.getStatus());

                request.addBatchDataParam("check_in_scan_status", DataType.BOOLEAN, entity.getCheckInScanStatus());
                request.addBatchDataParam(WhPurchaseExpressRecordDBField.LENGTH, DataType.DOUBLE, entity.getLength());
                request.addBatchDataParam(WhPurchaseExpressRecordDBField.WIDTH, DataType.DOUBLE, entity.getWidth());
                request.addBatchDataParam(WhPurchaseExpressRecordDBField.HEIGHT, DataType.DOUBLE, entity.getHeight());
                request.addBatchDataParam(WhPurchaseExpressRecordDBField.IMAGE_URL, DataType.STRING, entity.getImageUrl());
                request.addBatchDataParam(WhPurchaseExpressRecordDBField.SCAN_MARK, DataType.INT, entity.getScanMark());
                request.addBatch();
            }
            SqlerTemplate.batchUpdate(request);
        }
    }

    /**
     * This method corresponds to the database table wh_purchase_express_record
     *
     * @mbggenerated Fri Aug 17 10:33:25 CST 2018
     */
    public void deleteWhPurchaseExpressRecord(Integer primaryKey) {
        SqlerRequest request = new SqlerRequest("deleteWhPurchaseExpressRecordByPrimaryKey");
        request.addDataParam(WhPurchaseExpressRecordDBField.ID, DataType.INT, primaryKey);
        SqlerTemplate.execute(request);
    }


    @Override
    public void batchUpdateCommentByIds(String ids, String comment) {

        //设置执行sql
        SqlerRequest request = new SqlerRequest("batchUpdateCommentByIds");

        //设置sql参数，这里的标准重量可以用null覆盖原值
        request.addDataParam(WhPurchaseExpressRecordDBField.COMMENT, DataType.STRING, comment);

        if (StringUtils.isNotBlank(ids)) {
            request.addDataParam("ids",
                    DataType.STRING,
                    Arrays.asList(org.apache.commons.lang3.StringUtils.split(ids, ",")));
        }

        //执行sql
        SqlerTemplate.execute(request);
    }
}