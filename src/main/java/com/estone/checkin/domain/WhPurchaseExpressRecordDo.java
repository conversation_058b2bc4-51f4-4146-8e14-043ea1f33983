package com.estone.checkin.domain;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.estone.checkin.bean.PmsPurchaseUsers;
import com.estone.checkin.bean.PurchaseOrder;
import com.estone.checkin.bean.WhPurchaseExpressRecord;
import com.estone.checkin.bean.WhPurchaseExpressRecordQueryCondition;
import com.whq.tool.component.Pager;

import lombok.Data;

@Data
public class WhPurchaseExpressRecordDo {
    private WhPurchaseExpressRecord whPurchaseExpressRecord;

    private WhPurchaseExpressRecordQueryCondition query;

    private List<WhPurchaseExpressRecord> whPurchaseExpressRecords = new ArrayList<WhPurchaseExpressRecord>();
    
    private Integer warehouseId;// 仓库
    
    private List<String[]> splitUserList = new ArrayList<>();// 拆分人
    
    private List<String[]> createUserList = new ArrayList<>();// 签收人
    
    private List<String[]> isSplitList = new ArrayList<>();// 是否拆分
    
    private List<String[]> hasPurchaseOrderNoList = new ArrayList<>();// 是否绑定采购单

    private List<String[]> hasCommentList = new ArrayList<>();// 是否备注

    private List<String[]> purchaseTypeList = new ArrayList<>();// 采购类型

    private List<PurchaseOrder> purchaseOrders = new ArrayList<>();// 采购单
    
    private List<String> itemExpressIds = new ArrayList<>();// 子单号

    private List<PmsPurchaseUsers> purchaseUsersList;//采购员
    
    //TJ：特急,FBA,VW：虚拟仓
    private String flagsName;// 标签
    
    private boolean hasExpressRecord = false;

    //包含Shopify加急sku
    private boolean hasShopifyTJSku = false;

    private Pager page = new Pager();

    private List<String> ShippingMethodList;//物流公司
    
    private Map<String, Object> validateMap = new HashMap<>();

    public boolean isHasShopifyTJSku() {
        return hasShopifyTJSku;
    }

    public void setHasShopifyTJSku(boolean hasShopifyTJSku) {
        this.hasShopifyTJSku = hasShopifyTJSku;
    }

    public Map<String, Object> getValidateMap() {
        return validateMap;
    }

    public void setValidateMap(Map<String, Object> validateMap) {
        this.validateMap = validateMap;
    }

    public WhPurchaseExpressRecord getWhPurchaseExpressRecord() {
        return whPurchaseExpressRecord;
    }

    public void setWhPurchaseExpressRecord(WhPurchaseExpressRecord whPurchaseExpressRecord) {
        this.whPurchaseExpressRecord = whPurchaseExpressRecord;
    }

    public WhPurchaseExpressRecordQueryCondition getQuery() {
        return query;
    }

    public void setQuery(WhPurchaseExpressRecordQueryCondition query) {
        this.query = query;
    }

    public List<WhPurchaseExpressRecord> getWhPurchaseExpressRecords() {
        return whPurchaseExpressRecords;
    }

    public void setWhPurchaseExpressRecords(List<WhPurchaseExpressRecord> whPurchaseExpressRecords) {
        this.whPurchaseExpressRecords = whPurchaseExpressRecords;
    }

    public Pager getPage() {
        return page;
    }

    public void setPage(Pager page) {
        this.page = page;
    }

    public Integer getWarehouseId() {
        return warehouseId;
    }

    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    public List<String[]> getSplitUserList() {
        return splitUserList;
    }

    public void setSplitUserList(List<String[]> splitUserList) {
        this.splitUserList = splitUserList;
    }

    public List<String[]> getCreateUserList() {
        return createUserList;
    }

    public void setCreateUserList(List<String[]> createUserList) {
        this.createUserList = createUserList;
    }

    public List<String[]> getIsSplitList() {
        return isSplitList;
    }

    public void setIsSplitList(List<String[]> isSplitList) {
        this.isSplitList = isSplitList;
    }

    public List<String[]> getHasPurchaseOrderNoList() {
        return hasPurchaseOrderNoList;
    }

    public void setHasPurchaseOrderNoList(List<String[]> hasPurchaseOrderNoList) {
        this.hasPurchaseOrderNoList = hasPurchaseOrderNoList;
    }

    public List<PurchaseOrder> getPurchaseOrders() {
        return purchaseOrders;
    }

    public void setPurchaseOrders(List<PurchaseOrder> purchaseOrders) {
        this.purchaseOrders = purchaseOrders;
    }

    public boolean isHasExpressRecord() {
        return hasExpressRecord;
    }

    public void setHasExpressRecord(boolean hasExpressRecord) {
        this.hasExpressRecord = hasExpressRecord;
    }

    public String getFlagsName() {
        return flagsName;
    }

    public void setFlagsName(String flagsName) {
        this.flagsName = flagsName;
    }

    public List<String> getItemExpressIds() {
        return itemExpressIds;
    }

    public void setItemExpressIds(List<String> itemExpressIds) {
        this.itemExpressIds = itemExpressIds;
    }

    public List<PmsPurchaseUsers> getPurchaseUsersList() {
        return purchaseUsersList;
    }

    public void setPurchaseUsersList(List<PmsPurchaseUsers> purchaseUsersList) {
        this.purchaseUsersList = purchaseUsersList;
    }

    public List<String[]> getHasCommentList() {
        return hasCommentList;
    }

    public void setHasCommentList(List<String[]> hasCommentList) {
        this.hasCommentList = hasCommentList;
    }

    public List<String[]> getPurchaseTypeList() {
        return purchaseTypeList;
    }

    public void setPurchaseTypeList(List<String[]> purchaseTypeList) {
        this.purchaseTypeList = purchaseTypeList;
    }
}