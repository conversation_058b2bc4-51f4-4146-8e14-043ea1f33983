package com.estone.checkin.domain;

import com.estone.checkin.bean.ExemptionQcConfiguration;
import com.estone.checkin.bean.ExemptionQcConfigurationQueryCondition;
import com.estone.checkin.enums.CheckInFlags;
import com.estone.checkin.enums.ExceptionType;
import com.whq.tool.component.Pager;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class ExemptionQcConfigurationDo {
    private ExemptionQcConfiguration exemptionQcConfiguration;

    private ExemptionQcConfigurationQueryCondition query;

    private List<ExemptionQcConfiguration> exemptionQcConfigurations = new ArrayList<ExemptionQcConfiguration>();

    private Pager page = new Pager();

    private List<ExceptionType> exceptionTypes;

    private List<CheckInFlags> exclusiveSkuTypes;
}