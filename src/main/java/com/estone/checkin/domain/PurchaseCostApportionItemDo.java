package com.estone.checkin.domain;

import com.estone.checkin.bean.PurchaseCostApportionItem;
import com.estone.checkin.bean.PurchaseCostApportionItemQueryCondition;
import com.whq.tool.component.Pager;
import java.util.ArrayList;
import java.util.List;

public class PurchaseCostApportionItemDo {
    private PurchaseCostApportionItem purchaseCostApportionItem;

    private PurchaseCostApportionItemQueryCondition query;

    private List<PurchaseCostApportionItem> purchaseCostApportionItems = new ArrayList<PurchaseCostApportionItem>();

    private Pager page = new Pager();

    public PurchaseCostApportionItem getPurchaseCostApportionItem() {
        return purchaseCostApportionItem;
    }

    public void setPurchaseCostApportionItem(PurchaseCostApportionItem purchaseCostApportionItem) {
        this.purchaseCostApportionItem = purchaseCostApportionItem;
    }

    public PurchaseCostApportionItemQueryCondition getQuery() {
        return query;
    }

    public void setQuery(PurchaseCostApportionItemQueryCondition query) {
        this.query = query;
    }

    public List<PurchaseCostApportionItem> getPurchaseCostApportionItems() {
        return purchaseCostApportionItems;
    }

    public void setPurchaseCostApportionItems(List<PurchaseCostApportionItem> purchaseCostApportionItems) {
        this.purchaseCostApportionItems = purchaseCostApportionItems;
    }

    public Pager getPage() {
        return page;
    }

    public void setPage(Pager page) {
        this.page = page;
    }
}