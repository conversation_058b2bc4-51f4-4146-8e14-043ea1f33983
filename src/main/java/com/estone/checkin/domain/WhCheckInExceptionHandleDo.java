package com.estone.checkin.domain;

import com.estone.checkin.bean.WhCheckInExceptionHandle;
import com.estone.checkin.bean.WhCheckInExceptionHandleQueryCondition;
import com.whq.tool.component.Pager;
import java.util.ArrayList;
import java.util.List;

public class WhCheckInExceptionHandleDo {
    private WhCheckInExceptionHandle whCheckInExceptionHandle;

    private WhCheckInExceptionHandleQueryCondition query;

    private List<WhCheckInExceptionHandle> whCheckInExceptionHandles = new ArrayList<WhCheckInExceptionHandle>();

    private Pager page = new Pager();

    public WhCheckInExceptionHandle getWhCheckInExceptionHandle() {
        return whCheckInExceptionHandle;
    }

    public void setWhCheckInExceptionHandle(WhCheckInExceptionHandle whCheckInExceptionHandle) {
        this.whCheckInExceptionHandle = whCheckInExceptionHandle;
    }

    public WhCheckInExceptionHandleQueryCondition getQuery() {
        return query;
    }

    public void setQuery(WhCheckInExceptionHandleQueryCondition query) {
        this.query = query;
    }

    public List<WhCheckInExceptionHandle> getWhCheckInExceptionHandles() {
        return whCheckInExceptionHandles;
    }

    public void setWhCheckInExceptionHandles(List<WhCheckInExceptionHandle> whCheckInExceptionHandles) {
        this.whCheckInExceptionHandles = whCheckInExceptionHandles;
    }

    public Pager getPage() {
        return page;
    }

    public void setPage(Pager page) {
        this.page = page;
    }
}