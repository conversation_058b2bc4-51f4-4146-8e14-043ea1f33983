package com.estone.checkin.bean;

import java.io.Serializable;
import java.sql.Timestamp;
import lombok.Data;

@Data
public class WhExceptionDate implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键 database column wh_exception_date.id
     */
    private Integer id;

    /**
     * 入库异常单ID database column wh_exception_date.exception_id
     */
    private Integer exceptionId;

    /**
     * 处理结果状态 database column wh_exception_date.status
     */
    private Integer status;

    /**
     * 创建人 database column wh_exception_date.created_by
     */
    private Integer createdBy;

    /**
     * 创建时间 database column wh_exception_date.creation_date
     */
    private Timestamp creationDate;

    /**
     * 数量 database column wh_exception_date.quantity
     */
    private Integer quantity;
}