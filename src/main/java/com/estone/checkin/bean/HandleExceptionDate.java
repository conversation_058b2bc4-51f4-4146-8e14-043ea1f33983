package com.estone.checkin.bean;

import java.io.Serializable;
import java.sql.Timestamp;
import lombok.Data;

@Data
public class HandleExceptionDate implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键 database column handle_exception_date.id
     */
    private Integer id;

    /**
     * 入库异常单ID database column handle_exception_date.exception_id
     */
    private Integer exceptionId;

    /**
     * 第一次处理草稿人 database column handle_exception_date.handle_draft_by
     */
    private Integer handleDraftBy;

    /**
     * 第一次处理草稿时间 database column handle_exception_date.handle_draft_date
     */
    private Timestamp handleDraftDate;

    /**
     * 完成人 database column handle_exception_date.finish_by
     */
    private Integer finishBy;

    /**
     * 完成时间 database column handle_exception_date.finish_date
     */
    private Timestamp finishDate;
}