package com.estone.checkin.bean;

import lombok.Data;

import java.util.List;

@Data
public class MaterialCheckInQueryCondition extends MaterialCheckIn {
    private static final long serialVersionUID = 1L;

    private Boolean readOnly = false;

    private String articleNumber;

    private String inIdStr;

    private String fromCreateDate;

    private String toCreateDate;

    private String supplier;

    private String fromPackageAcceptanceDate;

    private String toPackageAcceptanceDate;

    private List<Integer> statusList;

    private List<Integer> inIdList;
}