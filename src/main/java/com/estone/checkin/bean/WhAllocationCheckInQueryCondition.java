package com.estone.checkin.bean;

import lombok.Data;

import java.util.List;

@Data
public class WhAllocationCheckInQueryCondition extends WhAllocationCheckIn {
    private static final long serialVersionUID = 1L;

    private String fromCreateDate;// 入库起始时间
    private String toCreateDate;// 入库终止时间

    private String fromQcTime;// QC起始时间
    private String toQcTime;// QC终止时间

    private String fromObtainTime;// 提货起始时间
    private String toObtainTime;// 提货终止时间

    private String fromUpTime;// 上架起始时间
    private String toUpTime;// 上架终止时间

    private String sku;// SKU
    private String orderBy = "ch.in_id desc";

    private List<Integer> inIds;// IDs

    private List<Integer> statuses;// statuses

    private String statusStr;// statusStr

    private List<String> allocationOrderNos;

    private List<String> skus;

    private List<String> bagNos;

    private Boolean download = false;

    private String location;
    private Integer stockId;
}