package com.estone.checkin.bean;

import lombok.Data;

import java.util.Date;

/**
 * @Description: 采购单核销状态DTO
 * @Author: Yimeil
 * @Date: 2021/12/18 17:10
 * @Version: 1.0.0
 */
@Data
public class PurchaseOrderStatusDTO {

    private String purchaseOrderNo;

    private String paymentStatus;

    private String stockInStatus;

    /**
     * 入库完成时间
     */
    private Date completedTime;

    /**
     * 付款完成时间
     */
    private Date finishPayTime;
}
