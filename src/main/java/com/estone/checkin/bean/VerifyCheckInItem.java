package com.estone.checkin.bean;

import java.io.Serializable;
import java.sql.Timestamp;

import com.estone.checkin.enums.VerifyResultEnum;
import com.estone.sku.bean.WhSku;
import com.estone.warehouse.bean.WhStock;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

@Data
public class VerifyCheckInItem implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键 database column verify_check_in_item.id
     */
    private Integer id;

    /**
     * 入库单ID database column verify_check_in_item.in_id
     */
    private Integer inId;

    /**
     * sku database column verify_check_in_item.sku
     */
    private String sku;

    /**
     * 相关的sku对象信息，包括质检备注和仓库标签等信息
     */
    private WhSku whSku;

    /**
     * 上架数量
     */
    private Integer upQuantity;

    /**
     * 上架库位
     */
    private String locationNumber;

    /**
     * 核查任务ID database column verify_check_in_item.verify_task_id
     */
    private Integer verifyTaskId;

    /**
     * 核查任务号
     */
    private String verifyTaskNo;

    /**
     * 核查结果 database column verify_check_in_item.verify_result
     * @see com.estone.checkin.enums.VerifyResultEnum
     */
    private Integer verifyResult;

    /**
     * 核查备注 database column verify_check_in_item.verify_reason
     */
    private String verifyReason;

    /**
     * 创建时间 database column verify_check_in_item.creation_date
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Timestamp creationDate;

    /**
     * 创建任务时间 database column verify_check_in_item.task_date
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Timestamp taskDate;

    /**
     * 核查时间 database column verify_check_in_item.verify_date
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Timestamp verifyDate;

    /**
     * 用于更新操作的时候，解绑任务
     */
    private Boolean releaseTask;

    /**
     * 库存数据对象
     */
    private WhStock whStock;

    public String getVerifyResultName() {
        if (verifyResult == null) {
            return null;
        }
        return VerifyResultEnum.getNameByCode(verifyResult);
    }
}