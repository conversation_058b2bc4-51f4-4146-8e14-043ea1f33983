package com.estone.checkin.bean;

import java.io.Serializable;
import java.sql.Timestamp;

import com.estone.checkin.enums.CheckInStatus;
import lombok.Data;

@Data
public class RfoCheckIn implements Serializable {
    private static final long serialVersionUID = 1L;

    public static final String RETURN_FORM_REMARK = "滞销退换货单号-退回不良品";

    /**
     * 入库单号 database column rfo_check_in.in_id
     */
    private Integer inId;

    /**
     * 退换货单号 database column rfo_check_in.return_form_no
     */
    private String returnFormNo;

    /**
     * 退回单号 database column rfo_check_in.back_order_no
     */
    private String backOrderNo;

    /**
     * sku database column rfo_check_in.sku
     */
    private String sku;

    /**
     * 数量 database column rfo_check_in.quantity
     */
    private Integer quantity;

    /**
     * 不良出库 database column frozen_stock.bad_product_quantity
     */
    private Integer badProductQuantity;

    /**
     * 库位 database column rfo_check_in.location_number
     *
     *  存储stock_id
     */
    private String locationNumber;

    /**
     * 创建时间 database column rfo_check_in.create_date
     */
    private Timestamp createDate;

    /**
     * 创建人 database column rfo_check_in.create_user
     */
    private Integer createUser;

    /**
     * 状态 11:上架中 13:已上架 20:已废弃 database column rfo_check_in.status
     */
    private Integer status;

    /**
     * 上架人 database column rfo_check_in.up_user
     */
    private Integer upUser;

    /**
     * 上架时间 database column rfo_check_in.up_time
     */
    private Timestamp upTime;

    /**
     * 采购上架是否成功 database column rfo_check_in.is_purchase_stock_in
     */
    private Boolean isPurchaseStockIn;

    /**
     * 加库存是否成功 database column rfo_check_in.is_in_stock
     */
    private Boolean isInStock;

    /**
     * 废弃原因 database column rfo_check_in.discard_reason
     */
    private String discardReason;

    /**
     * 关联单号
     */
    private String relevantNo;

    public String getStatusName() {
        if (status == null)
            return null;
        return CheckInStatus.getNameByCode(status.toString());
    }
}