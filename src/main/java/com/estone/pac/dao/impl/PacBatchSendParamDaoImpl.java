package com.estone.pac.dao.impl;

import com.estone.pac.bean.PacBatchSendParam;
import com.estone.pac.bean.PacBatchSendParamQueryCondition;
import com.estone.pac.dao.PacBatchSendParamDao;
import com.estone.pac.dao.mapper.PacBatchSendParamDBField;
import com.estone.pac.dao.mapper.PacBatchSendParamMapper;
import com.whq.tool.component.Pager;
import com.whq.tool.context.DataContextHolder;
import com.whq.tool.sqler.SqlerRequest;
import com.whq.tool.sqler.analyzer.DataType;
import com.estone.common.util.SqlerTemplate;
import java.sql.Timestamp;
import java.util.List;
import org.springframework.stereotype.Repository;
import org.springframework.util.Assert;

@Repository("pacBatchSendParamDao")
public class PacBatchSendParamDaoImpl implements PacBatchSendParamDao {

    private void setQueryCondition(SqlerRequest request, PacBatchSendParamQueryCondition query) {
        if (query == null) {
            return;
        }
        // 添加查询条件
        // 搜索条件不允许出现空字符
        request.setAllowBlank(false);
        
        request.addDataParam(PacBatchSendParamDBField.ID, DataType.INT, query.getId());

        request.addDataParam(PacBatchSendParamDBField.ORDER_CODE, DataType.STRING, query.getOrderCode());
    }

    @Override
    public int queryPacBatchSendParamCount(PacBatchSendParamQueryCondition query) {
        SqlerRequest request = new SqlerRequest("queryPacBatchSendParamCount");
        setQueryCondition(request, query);
        return SqlerTemplate.queryForInt(request);
    }

    @Override
    public List<PacBatchSendParam> queryPacBatchSendParamList() {
        SqlerRequest request = new SqlerRequest("queryPacBatchSendParamList");
        return SqlerTemplate.query(request, new PacBatchSendParamMapper());
    }

    @Override
    public List<PacBatchSendParam> queryPacBatchSendParamList(PacBatchSendParamQueryCondition query, Pager pager) {
        SqlerRequest request = new SqlerRequest("queryPacBatchSendParamList");
        setQueryCondition(request, query);
        if(pager != null) {
            request.addFetch(pager.getPageNo(), pager.getPageSize());
        }
        return SqlerTemplate.query(request, new PacBatchSendParamMapper());
    }

    @Override
    public PacBatchSendParam queryPacBatchSendParam(Integer primaryKey) {
        Assert.notNull(primaryKey, "primaryKey is null!");
        SqlerRequest request = new SqlerRequest("queryPacBatchSendParamByPrimaryKey");
        request.addDataParam(PacBatchSendParamDBField.ID, DataType.INT, primaryKey);
        return SqlerTemplate.queryForObject(request, new PacBatchSendParamMapper());
    }

    @Override
    public PacBatchSendParam queryPacBatchSendParam(PacBatchSendParamQueryCondition query) {
        SqlerRequest request = new SqlerRequest("queryPacBatchSendParam");
        setQueryCondition(request, query);
        return SqlerTemplate.queryForObject(request, new PacBatchSendParamMapper());
    }

    @Override
    public void createPacBatchSendParam(PacBatchSendParam entity) {
        SqlerRequest request = new SqlerRequest("createPacBatchSendParam");
        request.addDataParam(PacBatchSendParamDBField.ORDER_CODE, DataType.STRING, entity.getOrderCode());
        request.addDataParam(PacBatchSendParamDBField.UNIQUE_CODE, DataType.STRING, entity.getUniqueCode());
        request.addDataParam(PacBatchSendParamDBField.CREATE_TIME, DataType.TIMESTAMP, new Timestamp(System.currentTimeMillis()));
        request.addDataParam(PacBatchSendParamDBField.UPDATE_TIME, DataType.TIMESTAMP, new Timestamp(System.currentTimeMillis()));
        request.addDataParam(PacBatchSendParamDBField.TOTAL_ORDER_ITEM_COUNT, DataType.INT, entity.getTotalOrderItemCount());
        request.addDataParam(PacBatchSendParamDBField.SUM_ORDER_ITEM_COUNT, DataType.INT, entity.getSumOrderItemCount());
        request.addDataParam(PacBatchSendParamDBField.DISTRIBUTE_TYPE, DataType.INT, entity.getDistributeType());
        Integer autoIncrementId = SqlerTemplate.executeAndReturn(request);
        entity.setId(autoIncrementId);
    }

    @Override
    public void updatePacBatchSendParam(PacBatchSendParam entity) {
        SqlerRequest request = new SqlerRequest("updatePacBatchSendParamByPrimaryKey");
        request.addDataParam(PacBatchSendParamDBField.ID, DataType.INT, entity.getId());
        
        request.addDataParam(PacBatchSendParamDBField.ORDER_CODE, DataType.STRING, entity.getOrderCode());
        request.addDataParam(PacBatchSendParamDBField.UNIQUE_CODE, DataType.STRING, entity.getUniqueCode());
        request.addDataParam(PacBatchSendParamDBField.CREATE_TIME, DataType.TIMESTAMP, entity.getCreateTime());
        request.addDataParam(PacBatchSendParamDBField.UPDATE_TIME, DataType.TIMESTAMP, entity.getUpdateTime());
        request.addDataParam(PacBatchSendParamDBField.TOTAL_ORDER_ITEM_COUNT, DataType.INT, entity.getTotalOrderItemCount());
        request.addDataParam(PacBatchSendParamDBField.SUM_ORDER_ITEM_COUNT, DataType.INT, entity.getSumOrderItemCount());
        request.addDataParam(PacBatchSendParamDBField.DISTRIBUTE_TYPE, DataType.INT, entity.getDistributeType());
        SqlerTemplate.execute(request);
    }

    @Override
    public void batchCreatePacBatchSendParam(List<PacBatchSendParam> entityList) {
        if (entityList != null && !entityList.isEmpty()) {
            SqlerRequest request = new SqlerRequest("createPacBatchSendParam");
            for (PacBatchSendParam entity : entityList) {
                request.addBatchDataParam(PacBatchSendParamDBField.ORDER_CODE, DataType.STRING, entity.getOrderCode());
                request.addBatchDataParam(PacBatchSendParamDBField.UNIQUE_CODE, DataType.STRING, entity.getUniqueCode());
                request.addBatchDataParam(PacBatchSendParamDBField.CREATE_TIME, DataType.TIMESTAMP, entity.getCreateTime());
                request.addBatchDataParam(PacBatchSendParamDBField.UPDATE_TIME, DataType.TIMESTAMP, entity.getUpdateTime());
                request.addBatchDataParam(PacBatchSendParamDBField.TOTAL_ORDER_ITEM_COUNT, DataType.INT, entity.getTotalOrderItemCount());
                request.addBatchDataParam(PacBatchSendParamDBField.SUM_ORDER_ITEM_COUNT, DataType.INT, entity.getSumOrderItemCount());
                request.addBatchDataParam(PacBatchSendParamDBField.DISTRIBUTE_TYPE, DataType.INT, entity.getDistributeType());
                request.addBatch();
            }
            SqlerTemplate.batchUpdate(request);
        }
    }

    @Override
    public void batchUpdatePacBatchSendParam(List<PacBatchSendParam> entityList) {
        if (entityList != null && !entityList.isEmpty()) {
            SqlerRequest request = new SqlerRequest("updatePacBatchSendParamByPrimaryKey");
            for (PacBatchSendParam entity : entityList) {
                request.addBatchDataParam(PacBatchSendParamDBField.ID, DataType.INT, entity.getId());
                
                request.addBatchDataParam(PacBatchSendParamDBField.ORDER_CODE, DataType.STRING, entity.getOrderCode());
                request.addBatchDataParam(PacBatchSendParamDBField.UNIQUE_CODE, DataType.STRING, entity.getUniqueCode());
                request.addBatchDataParam(PacBatchSendParamDBField.CREATE_TIME, DataType.TIMESTAMP, entity.getCreateTime());
                request.addBatchDataParam(PacBatchSendParamDBField.UPDATE_TIME, DataType.TIMESTAMP, entity.getUpdateTime());
                request.addBatchDataParam(PacBatchSendParamDBField.TOTAL_ORDER_ITEM_COUNT, DataType.INT, entity.getTotalOrderItemCount());
                request.addBatchDataParam(PacBatchSendParamDBField.SUM_ORDER_ITEM_COUNT, DataType.INT, entity.getSumOrderItemCount());
                request.addBatchDataParam(PacBatchSendParamDBField.DISTRIBUTE_TYPE, DataType.INT, entity.getDistributeType());
                request.addBatch();
            }
            SqlerTemplate.batchUpdate(request);
        }
    }

    @Override
    public void deletePacBatchSendParam(Integer primaryKey) {
        SqlerRequest request = new SqlerRequest("deletePacBatchSendParamByPrimaryKey");
        request.addDataParam(PacBatchSendParamDBField.ID, DataType.INT, primaryKey);
        SqlerTemplate.execute(request);
    }
}