package com.estone.pac.dao.impl;

import com.estone.pac.bean.PacStockInOrderItem;
import com.estone.pac.bean.PacStockInOrderItemQueryCondition;
import com.estone.pac.dao.PacStockInOrderItemDao;
import com.estone.pac.dao.mapper.PacStockInOrderItemDBField;
import com.estone.pac.dao.mapper.PacStockInOrderItemMapper;
import com.whq.tool.component.Pager;
import com.whq.tool.sqler.SqlerRequest;
import com.whq.tool.sqler.analyzer.DataType;
import com.estone.common.util.SqlerTemplate;
import java.sql.Timestamp;
import java.util.List;

import org.springframework.stereotype.Repository;
import org.springframework.util.Assert;

@Repository("pacStockInOrderItemDao")
public class PacStockInOrderItemDaoImpl implements PacStockInOrderItemDao {

    private void setQueryCondition(SqlerRequest request, PacStockInOrderItemQueryCondition query) {
        if (query == null) {
            return;
        }
        // 添加查询条件
        // 搜索条件不允许出现空字符
        request.setAllowBlank(false);
        
        request.addDataParam(PacStockInOrderItemDBField.ID, DataType.INT, query.getId());
        request.addDataParam(PacStockInOrderItemDBField.OWNER_USER_ID, DataType.STRING, query.getOwnerUserId());
        request.addDataParam("itemCodeList", DataType.STRING, query.getItemCodeList());
    }

    @Override
    public int queryPacStockInOrderItemCount(PacStockInOrderItemQueryCondition query) {
        SqlerRequest request = new SqlerRequest("queryPacStockInOrderItemCount");
        setQueryCondition(request, query);
        return SqlerTemplate.queryForInt(request);
    }

    @Override
    public List<PacStockInOrderItem> queryPacStockInOrderItemList() {
        SqlerRequest request = new SqlerRequest("queryPacStockInOrderItemList");
        return SqlerTemplate.query(request, new PacStockInOrderItemMapper());
    }

    @Override
    public List<PacStockInOrderItem> queryPacStockInOrderItemList(PacStockInOrderItemQueryCondition query, Pager pager) {
        SqlerRequest request = new SqlerRequest("queryPacStockInOrderItemList");
        setQueryCondition(request, query);
        if(pager != null) {
            request.addFetch(pager.getPageNo(), pager.getPageSize());
        }
        return SqlerTemplate.query(request, new PacStockInOrderItemMapper());
    }

    @Override
    public PacStockInOrderItem queryPacStockInOrderItem(Integer primaryKey) {
        Assert.notNull(primaryKey, "primaryKey is null!");
        SqlerRequest request = new SqlerRequest("queryPacStockInOrderItemByPrimaryKey");
        request.addDataParam(PacStockInOrderItemDBField.ID, DataType.INT, primaryKey);
        return SqlerTemplate.queryForObject(request, new PacStockInOrderItemMapper());
    }

    @Override
    public PacStockInOrderItem queryPacStockInOrderItem(PacStockInOrderItemQueryCondition query) {
        SqlerRequest request = new SqlerRequest("queryPacStockInOrderItem");
        setQueryCondition(request, query);
        return SqlerTemplate.queryForObject(request, new PacStockInOrderItemMapper());
    }

    @Override
    public void createPacStockInOrderItem(PacStockInOrderItem entity) {
        SqlerRequest request = new SqlerRequest("createPacStockInOrderItem");
        request.addDataParam(PacStockInOrderItemDBField.PAC_STOCK_IN_ORDER_ID, DataType.INT, entity.getPacStockInOrderId());
        request.addDataParam(PacStockInOrderItemDBField.OWNER_USER_ID, DataType.STRING, entity.getOwnerUserId());
        request.addDataParam(PacStockInOrderItemDBField.OWNER_USER_NAME, DataType.STRING, entity.getOwnerUserName());
        request.addDataParam(PacStockInOrderItemDBField.ORDER_ITEM_ID, DataType.STRING, entity.getOrderItemId());
        request.addDataParam(PacStockInOrderItemDBField.ITEM_ID, DataType.STRING, entity.getItemId());
        request.addDataParam(PacStockInOrderItemDBField.ITEM_NAME, DataType.STRING, entity.getItemName());
        request.addDataParam(PacStockInOrderItemDBField.BAR_CODE, DataType.STRING, entity.getBarCode());
        request.addDataParam(PacStockInOrderItemDBField.ITEM_CODE, DataType.STRING, entity.getItemCode());
        request.addDataParam(PacStockInOrderItemDBField.ITEM_QUANTITY, DataType.INT, entity.getItemQuantity());
        request.addDataParam(PacStockInOrderItemDBField.ITEM_UP_QUANTITY, DataType.INT, entity.getItemUpQuantity());
        request.addDataParam(PacStockInOrderItemDBField.CREATE_TIME, DataType.TIMESTAMP, new Timestamp(System.currentTimeMillis()));
        request.addDataParam(PacStockInOrderItemDBField.UPDATE_TIME, DataType.TIMESTAMP, new Timestamp(System.currentTimeMillis()));
        Integer autoIncrementId = SqlerTemplate.executeAndReturn(request);
        entity.setId(autoIncrementId);
    }

    @Override
    public void updatePacStockInOrderItem(PacStockInOrderItem entity) {
        SqlerRequest request = new SqlerRequest("updatePacStockInOrderItemByPrimaryKey");
        request.addDataParam(PacStockInOrderItemDBField.ID, DataType.INT, entity.getId());
        
        request.addDataParam(PacStockInOrderItemDBField.PAC_STOCK_IN_ORDER_ID, DataType.INT, entity.getPacStockInOrderId());
        request.addDataParam(PacStockInOrderItemDBField.OWNER_USER_ID, DataType.STRING, entity.getOwnerUserId());
        request.addDataParam(PacStockInOrderItemDBField.OWNER_USER_NAME, DataType.STRING, entity.getOwnerUserName());
        request.addDataParam(PacStockInOrderItemDBField.ITEM_ID, DataType.STRING, entity.getItemId());
        request.addDataParam(PacStockInOrderItemDBField.ITEM_NAME, DataType.STRING, entity.getItemName());
        request.addDataParam(PacStockInOrderItemDBField.BAR_CODE, DataType.STRING, entity.getBarCode());
        request.addDataParam(PacStockInOrderItemDBField.ITEM_CODE, DataType.STRING, entity.getItemCode());
        request.addDataParam(PacStockInOrderItemDBField.ITEM_QUANTITY, DataType.INT, entity.getItemQuantity());
        request.addDataParam(PacStockInOrderItemDBField.ITEM_UP_QUANTITY, DataType.INT, entity.getItemUpQuantity());
        request.addDataParam(PacStockInOrderItemDBField.UPDATE_TIME, DataType.TIMESTAMP, new Timestamp(System.currentTimeMillis()));
        SqlerTemplate.execute(request);
    }

    @Override
    public int queryPacInOrderItemSkuCount(PacStockInOrderItemQueryCondition query) {
        SqlerRequest request = new SqlerRequest("queryPacInOrderItemSkuCount");
        setQueryCondition(request, query);
        return SqlerTemplate.queryForInt(request);
    }

    @Override
    public List<PacStockInOrderItem> queryPacInOrderItemSkuList(PacStockInOrderItemQueryCondition query,Pager pager) {
        SqlerRequest request = new SqlerRequest("queryPacInOrderItemSkuList");
        setQueryCondition(request, query);
        if(pager != null) {
            request.addFetch(pager.getPageNo(), pager.getPageSize());
        }
        return SqlerTemplate.query(request, new PacStockInOrderItemMapper());
    }

    @Override
    public void batchCreatePacStockInOrderItem(List<PacStockInOrderItem> entityList) {
        if (entityList != null && !entityList.isEmpty()) {
            SqlerRequest request = new SqlerRequest("createPacStockInOrderItem");
            for (PacStockInOrderItem entity : entityList) {
                request.addBatchDataParam(PacStockInOrderItemDBField.PAC_STOCK_IN_ORDER_ID, DataType.INT, entity.getPacStockInOrderId());
                request.addBatchDataParam(PacStockInOrderItemDBField.OWNER_USER_ID, DataType.STRING, entity.getOwnerUserId());
                request.addBatchDataParam(PacStockInOrderItemDBField.OWNER_USER_NAME, DataType.STRING, entity.getOwnerUserName());
                request.addBatchDataParam(PacStockInOrderItemDBField.ORDER_ITEM_ID, DataType.STRING, entity.getOrderItemId());
                request.addBatchDataParam(PacStockInOrderItemDBField.ITEM_ID, DataType.STRING, entity.getItemId());
                request.addBatchDataParam(PacStockInOrderItemDBField.ITEM_NAME, DataType.STRING, entity.getItemName());
                request.addBatchDataParam(PacStockInOrderItemDBField.BAR_CODE, DataType.STRING, entity.getBarCode());
                request.addBatchDataParam(PacStockInOrderItemDBField.ITEM_CODE, DataType.STRING, entity.getItemCode());
                request.addBatchDataParam(PacStockInOrderItemDBField.ITEM_QUANTITY, DataType.INT, entity.getItemQuantity());
                request.addBatchDataParam(PacStockInOrderItemDBField.ITEM_UP_QUANTITY, DataType.INT, entity.getItemUpQuantity());
                request.addBatchDataParam(PacStockInOrderItemDBField.CREATE_TIME, DataType.TIMESTAMP, new Timestamp(System.currentTimeMillis()));
                request.addBatchDataParam(PacStockInOrderItemDBField.UPDATE_TIME, DataType.TIMESTAMP, new Timestamp(System.currentTimeMillis()));
                request.addBatch();
            }
            SqlerTemplate.batchUpdate(request);
        }
    }

    @Override
    public void batchUpdatePacStockInOrderItem(List<PacStockInOrderItem> entityList) {
        if (entityList != null && !entityList.isEmpty()) {
            SqlerRequest request = new SqlerRequest("updatePacStockInOrderItemByPrimaryKey");
            for (PacStockInOrderItem entity : entityList) {
                request.addBatchDataParam(PacStockInOrderItemDBField.ID, DataType.INT, entity.getId());
                
                request.addBatchDataParam(PacStockInOrderItemDBField.PAC_STOCK_IN_ORDER_ID, DataType.INT, entity.getPacStockInOrderId());
                request.addBatchDataParam(PacStockInOrderItemDBField.OWNER_USER_ID, DataType.STRING, entity.getOwnerUserId());
                request.addBatchDataParam(PacStockInOrderItemDBField.OWNER_USER_NAME, DataType.STRING, entity.getOwnerUserName());
                request.addBatchDataParam(PacStockInOrderItemDBField.ITEM_ID, DataType.STRING, entity.getItemId());
                request.addBatchDataParam(PacStockInOrderItemDBField.ITEM_NAME, DataType.STRING, entity.getItemName());
                request.addBatchDataParam(PacStockInOrderItemDBField.BAR_CODE, DataType.STRING, entity.getBarCode());
                request.addBatchDataParam(PacStockInOrderItemDBField.ITEM_CODE, DataType.STRING, entity.getItemCode());
                request.addBatchDataParam(PacStockInOrderItemDBField.ITEM_QUANTITY, DataType.INT, entity.getItemQuantity());
                request.addBatchDataParam(PacStockInOrderItemDBField.ITEM_UP_QUANTITY, DataType.INT, entity.getItemUpQuantity());
                request.addBatchDataParam(PacStockInOrderItemDBField.UPDATE_TIME, DataType.TIMESTAMP, new Timestamp(System.currentTimeMillis()));
                request.addBatch();
            }
            SqlerTemplate.batchUpdate(request);
        }
    }

    @Override
    public void deletePacStockInOrderItem(Integer primaryKey) {
        SqlerRequest request = new SqlerRequest("deletePacStockInOrderItemByPrimaryKey");
        request.addDataParam(PacStockInOrderItemDBField.ID, DataType.INT, primaryKey);
        SqlerTemplate.execute(request);
    }
}