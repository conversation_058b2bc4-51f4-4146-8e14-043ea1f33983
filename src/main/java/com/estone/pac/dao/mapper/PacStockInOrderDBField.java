package com.estone.pac.dao.mapper;

public interface PacStockInOrderDBField {
    String ID = "id";

    String OWNER_USER_ID = "owner_user_id";

    String OWNER_USER_NAME = "owner_user_name";

    String ORIGINAL_APV_NO = "original_apv_no";

    String STORE_CODE = "store_code";

    String ORDER_CODE = "order_code";

    String ERP_ORDER_CODE = "erp_order_code";

    String STATUS = "status";

    String ORDER_TYPE = "order_type";

    String CONFIRM_FLAG = "confirm_flag";

    String ORDER_CREATE_TIME = "order_create_time";

    String CREATE_TIME = "create_time";

    String UPDATE_TIME = "update_time";

    String TOTAL_ORDER_ITEM_COUNT = "total_order_item_count";

    String DISTRIBUTE_TYPE = "distribute_type";

    String UP_DATE="up_date";

}