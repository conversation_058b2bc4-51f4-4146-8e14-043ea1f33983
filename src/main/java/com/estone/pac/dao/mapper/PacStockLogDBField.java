package com.estone.pac.dao.mapper;

public interface PacStockLogDBField {
    String ID = "id";

    String TYPE = "type";

    String STEP = "step";

    String SKU = "sku";

    String ITEM_ID = "item_id";

    String QUANTITY = "quantity";

    String ORIGINAL_QUANTITY = "original_quantity";

    String CONTENT = "content";

    String CONTINUITY = "continuity";

    String CREATION_DATE = "creation_date";

    String CREATE_BY = "create_by";

    String OWNER_USER_ID = "owner_user_id";

    String CHECK = "check";
}