package com.estone.pac.dao.mapper;

public interface PacStockOutOrderItemDBField {
    String ID = "id";

    String PAC_STOCK_OUT_ORDER_ID = "pac_stock_out_order_id";

    String OWNER_USER_ID = "owner_user_id";

    String OWNER_USER_NAME = "owner_user_name";

    String ORDER_ITEM_ID = "order_item_id";

    String ITEM_ID = "item_id";

    String ITEM_NAME = "item_name";

    String BAR_CODE = "bar_code";

    String ITEM_CODE = "item_code";

    String ITEM_QUANTITY = "item_quantity";

    String ITEM_UP_QUANTITY = "item_up_quantity";

    String CREATE_TIME = "create_time";

    String UPDATE_TIME = "update_time";

    String STATUS = "status";
}