package com.estone.pac.dao.mapper;

import com.estone.pac.bean.PacNewStock;
import java.sql.ResultSet;
import java.sql.SQLException;
import org.springframework.jdbc.core.RowMapper;

public class PacNewStockMapper implements RowMapper<PacNewStock> {
    private boolean isGroupQuery = false;

    public PacNewStockMapper() {

    }

    public PacNewStockMapper(boolean isGroupQuery) {
        this.isGroupQuery = isGroupQuery;
    }

    public PacNewStock mapRow(ResultSet rs, int rowNum) throws SQLException {
        PacNewStock entity = new PacNewStock();
        if (isGroupQuery) {
            entity.setLocalSurplusQtySum(rs.getObject(PacNewStockDBField.LOCAL_SURPLUS_QUANTITY) == null ? 0L : rs.getLong(PacNewStockDBField.LOCAL_SURPLUS_QUANTITY));
            entity.setWaitingAllotQtySum(rs.getObject(PacNewStockDBField.WAITING_ALLOT_QUANTITY) == null ? 0L : rs.getLong(PacNewStockDBField.WAITING_ALLOT_QUANTITY));
            entity.setOmsPredistributionQtySum(rs.getObject(PacNewStockDBField.OMS_PREDISTRIBUTION_QUANTITY) == null ? 0L : rs.getLong(PacNewStockDBField.OMS_PREDISTRIBUTION_QUANTITY));
            entity.setWaitingCheckQtySum(rs.getObject(PacNewStockDBField.WAITING_CHECK_QUANTITY) == null ? 0L : rs.getLong(PacNewStockDBField.WAITING_CHECK_QUANTITY));
            entity.setQtySum(rs.getObject(PacNewStockDBField.QUANTITY) == null ? 0L : rs.getLong(PacNewStockDBField.QUANTITY));
            entity.setLockQtySum(rs.getObject(PacNewStockDBField.LOCK_QUANTITY) == null ? 0L : rs.getLong(PacNewStockDBField.LOCK_QUANTITY));
            entity.setDifferenceQtySum(rs.getObject(PacNewStockDBField.DIFFERENCE_QUANTITY) == null ? 0L : rs.getLong(PacNewStockDBField.DIFFERENCE_QUANTITY));
        }
        else {
            entity.setId(rs.getObject(PacNewStockDBField.ID) == null ? null : rs.getInt(PacNewStockDBField.ID));
            entity.setSku(rs.getString(PacNewStockDBField.SKU));
            entity.setImg(rs.getString(PacNewStockDBField.IMG));
            entity.setSkuName(rs.getString(PacNewStockDBField.SKU_NAME));
            entity.setLocation(rs.getString(PacNewStockDBField.LOCATION));
            entity.setItemId(rs.getString(PacNewStockDBField.ITEM_ID));
            entity.setBarCode(rs.getString(PacNewStockDBField.BAR_CODE));
            entity.setLocalSurplusQuantity(rs.getObject(PacNewStockDBField.LOCAL_SURPLUS_QUANTITY) == null ? null : rs.getInt(PacNewStockDBField.LOCAL_SURPLUS_QUANTITY));
            entity.setWaitingAllotQuantity(rs.getObject(PacNewStockDBField.WAITING_ALLOT_QUANTITY) == null ? null : rs.getInt(PacNewStockDBField.WAITING_ALLOT_QUANTITY));
            entity.setOmsPredistributionQuantity(rs.getObject(PacNewStockDBField.OMS_PREDISTRIBUTION_QUANTITY) == null ? null : rs.getInt(PacNewStockDBField.OMS_PREDISTRIBUTION_QUANTITY));
            entity.setWaitingCheckQuantity(rs.getObject(PacNewStockDBField.WAITING_CHECK_QUANTITY) == null ? null : rs.getInt(PacNewStockDBField.WAITING_CHECK_QUANTITY));
            entity.setQuantity(rs.getObject(PacNewStockDBField.QUANTITY) == null ? null : rs.getInt(PacNewStockDBField.QUANTITY));
            entity.setLockQuantity(rs.getObject(PacNewStockDBField.LOCK_QUANTITY) == null ? null : rs.getInt(PacNewStockDBField.LOCK_QUANTITY));
            entity.setDifferenceQuantity(rs.getObject(PacNewStockDBField.DIFFERENCE_QUANTITY) == null ? null : rs.getInt(PacNewStockDBField.DIFFERENCE_QUANTITY));
            entity.setCreationDate(rs.getTimestamp(PacNewStockDBField.CREATION_DATE));
            entity.setLastUpdateDate(rs.getTimestamp(PacNewStockDBField.LAST_UPDATE_DATE));
            entity.setSynchronizationTime(rs.getTimestamp(PacNewStockDBField.SYNCHRONIZATION_TIME));
            entity.setLastUpdatedBy(rs.getObject(PacNewStockDBField.LAST_UPDATED_BY) == null ? null : rs.getInt(PacNewStockDBField.LAST_UPDATED_BY));
            entity.setOwnerUserId(rs.getString(PacNewStockDBField.OWNER_USER_ID));
        }
        return entity;
    }
}