package com.estone.pac.action;

import com.estone.common.SelectJson;
import com.estone.common.enums.ExportType;
import com.estone.common.util.*;
import com.estone.pac.bean.*;
import com.estone.pac.configer.PacCommonConfiger;
import com.estone.pac.domain.PacStockOutOrderDo;
import com.estone.pac.domain.PacStockOutOrderItemDo;
import com.estone.pac.enums.PacStockInOrderStatus;
import com.estone.pac.enums.PacStockOutOrderStatus;
import com.estone.pac.service.PacConsignOrderService;
import com.estone.pac.service.PacStockOutOrderService;

import com.estone.picking.domain.WhPickingTaskDo;
import com.estone.system.rabbitmq.bean.AmqMessage;
import com.estone.system.rabbitmq.bean.AmqMessageQueryCondition;
import com.estone.system.rabbitmq.service.AmqMessageService;
import com.estone.transfer.bean.WhTransitReturn;
import com.estone.transfer.bean.WhTransitReturnItem;
import com.estone.transfer.bean.WhTransitReturnQueryCondition;
import com.estone.transfer.domain.WhTransitReturnDo;
import com.whq.tool.action.BaseController;
import com.whq.tool.component.Pager;

import java.io.OutputStream;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;

import com.whq.tool.json.ResponseJson;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

@Controller
@RequestMapping(value = "pacStockOutOrder")
@Slf4j
public class PacStockOutOrderController extends BaseController {
    @Resource
    private PacStockOutOrderService pacStockOutOrderService;

    private static String[] HEADERS = { "编号", "退仓单物流单号", "货主ID", "ITEM数量","SKU种类", "总退仓数量", "实际退仓数量", "接收时间", "退仓单状态", "完结时间"};


    @RequestMapping(method = {RequestMethod.GET})
    public String init(@ModelAttribute("domain") PacStockOutOrderDo domain) {
        initFormData(domain);
        return "pac/pacStockOutOrderList";
    }

    private void initFormData(@ModelAttribute("domain") PacStockOutOrderDo domain) {
        if (domain.getQuery() == null) {
            domain.setQuery(new PacStockOutOrderQueryCondition());
        }
        domain.setStatusJson(SelectJson.getList(PacStockOutOrderStatus.values()));
    }

    private void queryPacStockOutOrders(@ModelAttribute("domain") PacStockOutOrderDo domain) {
        PacStockOutOrderQueryCondition query = domain.getQuery();
        Pager page = domain.getPage();
        if (query == null)
        {
            query = new PacStockOutOrderQueryCondition();
            domain.setQuery(query);
        }
        if (StringUtils.isNotEmpty(query.getSkuSplit())) {
            query.setSkuList( CommonUtils.splitList(query.getSkuSplit(), ","));
        }
        if (StringUtils.isNotEmpty(query.getItemIdSplit())) {
            query.setItemIdList( CommonUtils.splitList(query.getItemIdSplit(), ","));
        }
        List<PacStockOutOrder> pacStockOutOrders = pacStockOutOrderService.queryPacStockOutOrderAndItems(query, page);
        domain.setPacStockOutOrders(pacStockOutOrders);
    }

    @RequestMapping(value="search", method = {RequestMethod.POST})
    public String search(@ModelAttribute("domain") PacStockOutOrderDo domain) {
        initFormData(domain);
        queryPacStockOutOrders(domain);
        return "pac/pacStockOutOrderList";
    }

    @RequestMapping(value="create", method = {RequestMethod.GET})
    public String toCreatePacStockOutOrder(@ModelAttribute("domain") PacStockOutOrderDo domain) {
        initFormData(domain);
        queryPacStockOutOrders(domain);
        return  "pac/pacStockOutOrderList";
    }

    @RequestMapping(value="create", method = {RequestMethod.POST})
    public String createPacStockOutOrder(@ModelAttribute("domain") PacStockOutOrderDo domain) {
        PacStockOutOrder pacStockOutOrder = domain.getPacStockOutOrder();
        pacStockOutOrderService.createPacStockOutOrder(pacStockOutOrder);
        return "redirect:/{查询列表}";
    }

    @RequestMapping(value="update", method = {RequestMethod.GET})
    public String toUpdatePacStockOutOrder(@ModelAttribute("domain") PacStockOutOrderDo domain, @RequestParam("pacStockOutOrderId") Integer pacStockOutOrderId) {
        PacStockOutOrder pacStockOutOrder = pacStockOutOrderService.getPacStockOutOrder(pacStockOutOrderId);
        domain.setPacStockOutOrder(pacStockOutOrder);
        return "{模块}/{页面}";
    }

    @RequestMapping(value="update", method = {RequestMethod.POST})
    public String updatePacStockOutOrder(@ModelAttribute("domain") PacStockOutOrderDo domain) {
        PacStockOutOrder pacStockOutOrder = domain.getPacStockOutOrder();
        pacStockOutOrderService.updatePacStockOutOrder(pacStockOutOrder);
        return "redirect:/{查询列表}";
    }

    @RequestMapping(value="delete", method = {RequestMethod.GET})
    @ResponseBody
    public ResponseJson deletePacStockOutOrder(@ModelAttribute("domain") PacStockOutOrderDo domain, @RequestParam("pacStockOutOrderId") Integer pacStockOutOrderId) {
        ResponseJson response = new ResponseJson();
        pacStockOutOrderService.deletePacStockOutOrder(pacStockOutOrderId);
        return response;
    }

    @RequestMapping(value="doPacStockOutOrder", method = {RequestMethod.GET})
    @ResponseBody
    public ResponseJson doPacStockInOrder() {
        ResponseJson response = new ResponseJson();
        AmqMessageService amqMessageService = SpringUtils.getBean(AmqMessageService.class);
        AmqMessageQueryCondition query = new AmqMessageQueryCondition();
        query.setSendStatus(false);
        query.setModuleNames(PacCommonConfiger.WMS_STOCK_OUT_ORDER_NOTIFY);
        Pager pager = new Pager();
        pager.setPageSize(10);
        List<AmqMessage> messageList = amqMessageService.queryAmqMessages(query, pager);
        if (CollectionUtils.isNotEmpty(messageList)) {
            pacStockOutOrderService.doPacStockOutOrder(messageList.get(0));
        }
        return response;
    }

    @RequestMapping(value = "outOrderItems", method = { RequestMethod.GET })
    public String returnItems(@ModelAttribute("domain") PacStockOutOrderDo domain, @RequestParam("id") Integer id) {
        PacStockOutOrderQueryCondition query = new PacStockOutOrderQueryCondition();
        query.setId(id);
        List<PacStockOutOrder> pacStockOutOrders = pacStockOutOrderService.queryPacStockOutOrderAndItems(query, null);
        if (CollectionUtils.isNotEmpty(pacStockOutOrders)) {
            domain.setPacStockOutOrder(pacStockOutOrders.get(0));
        }
        initFormData(domain);
        return "pac/pacStockOutOrderItemList";
    }

    @RequestMapping(value="searchItem", method = {RequestMethod.POST})
    public String searchItem(@ModelAttribute("domain") PacStockOutOrderDo domain) {
        initFormData(domain);
        PacStockOutOrderQueryCondition query = domain.getQuery();
        List<PacStockOutOrder> pacStockOutOrders = pacStockOutOrderService.queryPacStockOutOrderItems(query, null);
        if (CollectionUtils.isEmpty(pacStockOutOrders)) {
            PacStockOutOrder pacStockOutOrder=new PacStockOutOrder();
            pacStockOutOrder.setOrderCode(query.getOrderCode());
            pacStockOutOrder.setId(query.getId());
            pacStockOutOrders.add(pacStockOutOrder);
        }
        domain.setPacStockOutOrder(pacStockOutOrders.get(0));
        return "pac/pacStockOutOrderItemList";
    }


    /**
     * 导出
     *
     * @param domain
     * @param ids
     * @param response
     */
    @SuppressWarnings("incomplete-switch")
    @RequestMapping(value = "download", method = { RequestMethod.GET })
    @ResponseBody
    public void download(@ModelAttribute("domain") PacStockOutOrderDo domain,
                         @RequestParam(value = "ids", required = false) List<Integer> ids, HttpServletResponse response) {
        PacStockOutOrderQueryCondition query = domain.getQuery();
        if (query == null || CollectionUtils.isNotEmpty(ids)) {
            query = new PacStockOutOrderQueryCondition();
            query.setIds(ids);
        }
        List<PacStockOutOrder> pacStockOutOrders =pacStockOutOrderService.queryPacStockOutOrderAndItems(query, null);

        // 大于100W不能导出
        if (CollectionUtils.isEmpty(pacStockOutOrders) || pacStockOutOrders.size() > 1000000) {
            return;
        }
        OutputStream os = null;
        try {
            String fileName = "优选仓库存" + System.currentTimeMillis() + ".xls";
            response.setContentType("application/ms-excel");
            response.setHeader("Content-Disposition",
                    "attachment; filename=" + java.net.URLEncoder.encode(fileName, "UTF-8"));
            os = response.getOutputStream();
            log.warn("download file name: " + fileName);
            final List<List<String>> batchReturnData = new ArrayList<>();
            POIUtils.createExcel(HEADERS, pacStockOutOrders, stockOutOrder -> {

                batchReturnData.clear();

                List<String> arrayList = new ArrayList<String>(HEADERS.length);
                arrayList.add(POIUtils.transferObj2Str(stockOutOrder.getId()));
                arrayList.add(POIUtils.transferObj2Str(stockOutOrder.getOrderCode()));
                arrayList.add(POIUtils.transferObj2Str(stockOutOrder.getOwnerUserId()));
                arrayList.add(POIUtils.transferObj2Str(stockOutOrder.getTotalOrderItemCount()));
                arrayList.add(POIUtils.transferObj2Str(stockOutOrder.getSkuQuantity()));
                arrayList.add(POIUtils.transferObj2Str(stockOutOrder.getOutQuantitySum()));
                arrayList.add(POIUtils.transferObj2Str(stockOutOrder.getActualOutQuantity()));
                arrayList.add(POIUtils.transferObj2Str(stockOutOrder.getCreateTime()));

                arrayList.add(POIUtils.transferObj2Str(PacStockOutOrderStatus.getNameByCode(stockOutOrder.getStatus())));

                arrayList.add(POIUtils.transferObj2Str(stockOutOrder.getUpDate()));


                batchReturnData.add(arrayList);
                return batchReturnData;

            }, true, os);
            log.warn("---task execute end---");
        } catch (Exception e) {
            log.warn(e.getMessage(), e);
        } finally {
            org.apache.poi.util.IOUtils.closeQuietly(os);
        }
    }

}