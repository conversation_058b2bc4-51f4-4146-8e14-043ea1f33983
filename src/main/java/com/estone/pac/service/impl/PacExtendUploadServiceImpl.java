package com.estone.pac.service.impl;


import com.alibaba.fastjson.JSONObject;
import com.estone.common.util.DateUtils;
import com.estone.common.util.StringRedisUtils;
import com.estone.pac.service.PacExtendUploadService;
import com.taobao.pac.sdk.cp.PacClient;
import com.taobao.pac.sdk.cp.SendSysParams;
import com.taobao.pac.sdk.cp.dataobject.request.WMS_EXTEND_UPLOAD.ExtendUploadDetailRequest;
import com.taobao.pac.sdk.cp.dataobject.request.WMS_EXTEND_UPLOAD.WmsExtendUploadRequest;
import com.taobao.pac.sdk.cp.dataobject.response.WMS_EXTEND_UPLOAD.WmsExtendUploadResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022-04-27 14:42
 */
@Service("pacExtendUploadService")
@Slf4j
public class PacExtendUploadServiceImpl implements PacExtendUploadService {
    @Resource
    private PacClient pacClient ;
    @Resource
    private SendSysParams sendSysParams;

    @Override
    public String wmsExtendUpload(List<String> lps) {
        String result = null;
        String bizOrderCode = null;
        try {
            if (CollectionUtils.isEmpty(lps))  return null;
            WmsExtendUploadRequest pacRequest = new WmsExtendUploadRequest();
            bizOrderCode = StringRedisUtils.getPacBizOrderCode("SZU902" + DateUtils.dateToString(new Date(), "yyMM"));
            pacRequest.setBizOrderCode(bizOrderCode);
          //  pacRequest.setOrderCode(orderCode);
            pacRequest.setStoreCode("SZU902");
            pacRequest.setType("bigBag");
            pacRequest.setOperator("SZU902");
            pacRequest.setOperateTime(new Date());
            pacRequest.setExtendUploadDetailList(lps.stream().map(lp -> {
                ExtendUploadDetailRequest detailRequest = new ExtendUploadDetailRequest();
                detailRequest.setBizDetailCode(lp);
                return detailRequest;
            }).collect(Collectors.toList()));
            WmsExtendUploadResponse rsp = pacClient.send(pacRequest, sendSysParams);
            log.info("bizOrderCode["+bizOrderCode+"] send rep:"+ JSONObject.toJSONString(rsp));
            if (!rsp.isSuccess()) {
                //重试
                for(int i = 0;i<3;i++){
                    WmsExtendUploadResponse rspRetry = pacClient.send(pacRequest, sendSysParams);
                    log.info("retry["+(i+1)+"]bizOrderCode["+bizOrderCode+"] send rep:"+ JSONObject.toJSONString(rsp));
                    if(rspRetry.isSuccess()){
                        result = bizOrderCode;
                        break;
                    }
                }
            }else{
                result = bizOrderCode;
            }
        } catch (Exception e) {
            log.info("bizOrderCode["+bizOrderCode+"] send failed:"+e.getMessage(),e);
        }
        return result;
    }
}
