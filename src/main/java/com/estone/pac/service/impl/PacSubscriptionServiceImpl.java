package com.estone.pac.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.estone.pac.bean.*;
import com.estone.pac.configer.PacCommonConfiger;
import com.estone.pac.dao.PacSubscriptionDao;
import com.estone.pac.service.PacSubscriptionService;
import com.estone.pac.utils.PacAmqMessageUtil;
import com.estone.system.rabbitmq.bean.AmqMessage;
import com.taobao.pac.sdk.cp.dataobject.request.WMS_SELLER_ENTER_MESSAGE.ShopDO;
import com.taobao.pac.sdk.cp.dataobject.request.WMS_SELLER_ENTER_MESSAGE.WmsSellerEnterMessageRequest;
import com.taobao.pac.sdk.cp.dataobject.request.WMS_SKU_INFO_NOTIFY.WmsSkuInfoNotifyRequest;
import com.taobao.pac.sdk.cp.dataobject.request.WMS_SUBSCRIPTION_NOTIFY.WmsSubscriptionNotifyRequest;
import com.taobao.pac.sdk.cp.dataobject.response.WMS_SELLER_ENTER_MESSAGE.WmsSellerEnterMessageResponse;
import com.taobao.pac.sdk.cp.dataobject.response.WMS_SUBSCRIPTION_NOTIFY.WmsSubscriptionNotifyResponse;
import com.whq.tool.component.Pager;
import com.whq.tool.component.StatusCode;
import com.whq.tool.exception.BusinessException;
import com.whq.tool.exception.DBErrorCode;
import com.whq.tool.json.ResponseJson;
import com.whq.tool.sqler.SqlerException;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.interceptor.TransactionAspectSupport;
import org.springframework.util.Assert;

@Service("pacSubscriptionService")
@Slf4j
public class PacSubscriptionServiceImpl implements PacSubscriptionService {
    @Resource
    private PacSubscriptionDao pacSubscriptionDao;

    @Override
    public PacSubscription getPacSubscription(Integer id) {
        PacSubscription pacSubscription = pacSubscriptionDao.queryPacSubscription(id);
        return pacSubscription;
    }

    @Override
    public PacSubscription getPacSubscriptionDetail(Integer id) {
        PacSubscription pacSubscription = pacSubscriptionDao.queryPacSubscription(id);
        // 关联查询
        return pacSubscription;
    }

    @Override
    public PacSubscription queryPacSubscription(PacSubscriptionQueryCondition query) {
        Assert.notNull(query, "query is null!");
        PacSubscription pacSubscription = pacSubscriptionDao.queryPacSubscription(query);
        return pacSubscription;
    }

    @Override
    public List<PacSubscription> queryAllPacSubscriptions() {
        return pacSubscriptionDao.queryPacSubscriptionList();
    }

    @Override
    public List<PacSubscription> queryPacSubscriptions(PacSubscriptionQueryCondition query, Pager pager) {
        Assert.notNull(query, "query is null!");
        if (pager != null && pager.isQueryCount()) {
            int count = pacSubscriptionDao.queryPacSubscriptionCount(query);
            pager.setTotalCount(count);
            if ( count == 0) {
                return new ArrayList<PacSubscription>();
            }
        }
        List<PacSubscription> pacSubscriptions = pacSubscriptionDao.queryPacSubscriptionList(query, pager);
        return pacSubscriptions;
    }

    @Override
    public void createPacSubscription(PacSubscription pacSubscription) {
        try {
            pacSubscriptionDao.createPacSubscription(pacSubscription);
        }
        catch (SqlerException e) {
            log.error(e.getMessage(), e);
            throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
        }
    }

    @Override
    public void batchCreatePacSubscription(List<PacSubscription> entityList) {
        if (CollectionUtils.isNotEmpty(entityList)) {
            try {
                pacSubscriptionDao.batchCreatePacSubscription(entityList);
            }
            catch (SqlerException e) {
                log.error(e.getMessage(), e);
                throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
            }
        }
    }

    @Override
    public void deletePacSubscription(Integer id) {
        try {
            pacSubscriptionDao.deletePacSubscription(id);
        }
        catch (SqlerException e) {
            log.error(e.getMessage(), e);
            throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
        }
    }

    @Override
    public void updatePacSubscription(PacSubscription pacSubscription) {
        try {
            pacSubscriptionDao.updatePacSubscription(pacSubscription);
        }
        catch (SqlerException e) {
            log.error(e.getMessage(), e);
            throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
        }
    }

    @Override
    public void batchUpdatePacSubscription(List<PacSubscription> entityList) {
        if (CollectionUtils.isNotEmpty(entityList)) {
            try {
                pacSubscriptionDao.batchUpdatePacSubscription(entityList);
            }
            catch (SqlerException e) {
                log.error(e.getMessage(), e);
                throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
            }
        }
    }

    @Override
    public ResponseJson doPacSellerEnter(AmqMessage amqMessage) {
        ResponseJson response = new ResponseJson();
        response.setStatus(StatusCode.FAIL);
        if (null!=amqMessage) {
            String messageBody = amqMessage.getMessageBody();
            log.info("messageId：" + amqMessage.getMessageId());
            try {
                if (PacCommonConfiger.WMS_SELLER_ENTER_MESSAGE.equals(amqMessage.getModuleName())){
                    WmsSellerEnterMessageRequest request = JSONObject.parseObject(messageBody, WmsSellerEnterMessageRequest.class);
                    PacSubscription pacSubscription = new PacSubscription();
                    pacSubscription.setServiceCode(request.getStoreCode());
                    pacSubscription.setActionType(request.getActionType());
                    pacSubscription.setUserId(request.getOwnerUserId());
                    List<ShopDO> shopList = request.getShopList();
                    if (CollectionUtils.isNotEmpty(shopList)){
                        ShopDO shopDO = shopList.get(0);
                        pacSubscription.setSubscriberMobile(shopDO.getServiceTel());
                        pacSubscription.setSubscriberPhone(shopDO.getSenderPhone());
                        pacSubscription.setSubscriberName(shopDO.getSenderName());
                    }
                    pacSubscription.setRemark("WMS_SELLER_ENTER_MESSAGE");
                    pacSubscriptionDao.createPacSubscription(pacSubscription);

                    //更新阿里云优选仓消息状态
                    PacAmqMessageUtil.updatePacAmqMessage(amqMessage.getMessageId());
                    response.setStatus(StatusCode.SUCCESS);
                    return response;
                }
            } catch (Exception e) {
                throw new RuntimeException("系统异常，优选仓操作失败:" + e.getMessage());
            }

        }
        response.setMessage("消息体为空");
        return response;
    }


    @Override
    public ResponseJson doPacSubscriptionNotify(AmqMessage amqMessage) {
        ResponseJson response = new ResponseJson();
        response.setStatus(StatusCode.FAIL);
        if (null!=amqMessage) {
            String messageBody = amqMessage.getMessageBody();
            log.info("messageId：" + amqMessage.getMessageId());
            try {
                if (PacCommonConfiger.WMS_SUBSCRIPTION_NOTIFY.equals(amqMessage.getModuleName())){
                    WmsSubscriptionNotifyRequest request = JSONObject.parseObject(messageBody, WmsSubscriptionNotifyRequest.class);
                    PacSubscription pacSubscription = new PacSubscription();
                    BeanUtils.copyProperties(request,pacSubscription);
                    pacSubscriptionDao.createPacSubscription(pacSubscription);

                    //更新阿里云优选仓消息状态
                    PacAmqMessageUtil.updatePacAmqMessage(amqMessage.getMessageId());
                    response.setStatus(StatusCode.SUCCESS);
                    return response;
                }
            } catch (Exception e) {
                throw new RuntimeException("系统异常，优选仓操作失败:" + e.getMessage());
            }

        }
        response.setMessage("消息体为空");
        return response;
    }
}