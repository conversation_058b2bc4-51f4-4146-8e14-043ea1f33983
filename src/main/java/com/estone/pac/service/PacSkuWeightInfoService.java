package com.estone.pac.service;

import com.estone.pac.bean.PacSkuWeightInfo;
import com.estone.pac.bean.PacSkuWeightInfoQueryCondition;
import com.whq.tool.component.Pager;
import java.util.List;

public interface PacSkuWeightInfoService {
    List<PacSkuWeightInfo> queryAllPacSkuWeightInfos();

    List<PacSkuWeightInfo> queryPacSkuWeightInfos(PacSkuWeightInfoQueryCondition query, Pager pager);

    PacSkuWeightInfo getPacSkuWeightInfo(Integer id);

    PacSkuWeightInfo getPacSkuWeightInfoDetail(Integer id);

    PacSkuWeightInfo queryPacSkuWeightInfo(PacSkuWeightInfoQueryCondition query);

    void createPacSkuWeightInfo(PacSkuWeightInfo pacSkuWeightInfo);

    void batchCreatePacSkuWeightInfo(List<PacSkuWeightInfo> entityList);

    void deletePacSkuWeightInfo(Integer id);

    void updatePacSkuWeightInfo(PacSkuWeightInfo pacSkuWeightInfo);

    void batchUpdatePacSkuWeightInfo(List<PacSkuWeightInfo> entityList);

    List<Integer> getIdByQuery(PacSkuWeightInfoQueryCondition infoQueryCondition);
}