package com.estone.pac.service.cp;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.estone.apv.bean.WhApv;
import com.estone.apv.bean.WhApvQueryCondition;
import com.estone.apv.common.ApvStatus;
import com.estone.apv.service.ApvStatusUpdateService;
import com.estone.apv.service.WhApvService;
import com.estone.common.util.SpringUtils;
import com.estone.common.util.SystemLogUtils;
import com.estone.pac.configer.PacCommonConfiger;
import com.estone.pac.enums.PacStockInOrderType;
import com.estone.system.rabbitmq.bean.AmqMessage;
import com.estone.system.rabbitmq.service.AmqMessageService;
import com.estone.system.rabbitmq.utils.AssembleMessageDataUtils;
import com.taobao.pac.sdk.cp.ReceiveListener;
import com.taobao.pac.sdk.cp.ReceiveSysParams;
import com.taobao.pac.sdk.cp.dataobject.request.WMS_ORDER_CANCEL_NOTIFY.WmsOrderCancelNotifyRequest;
import com.taobao.pac.sdk.cp.dataobject.response.WMS_ORDER_CANCEL_NOTIFY.WmsOrderCancelNotifyResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import java.sql.Timestamp;
import java.util.List;


/**
 * 出库取消通知
 * <AUTHOR>
 *
 */
@Slf4j
@Service
public class WmsOrderCancelNotifyCpImpl implements ReceiveListener<WmsOrderCancelNotifyRequest, WmsOrderCancelNotifyResponse> {

    @Override
    public WmsOrderCancelNotifyResponse execute(ReceiveSysParams arg0,
                                                WmsOrderCancelNotifyRequest request) {
       // log.info("WmsOrderCancelNotifyCpImpl ReceiveSysParams："+ JSONObject.toJSONString(arg0));
        log.info("WmsOrderCancelNotifyCpImpl WmsOrderCancelNotifyRequest："+ request.getOrderCode());
        // TODO Auto-generated method stub
        WmsOrderCancelNotifyResponse response = new WmsOrderCancelNotifyResponse();
        try {
            if (PacStockInOrderType.CHECK_OUT.getCodeStr().equals(request.getOrderType())) {
                WhApvService whApvService = SpringUtils.getBean(WhApvService.class);
                WhApvQueryCondition queryCondition = new WhApvQueryCondition();
                queryCondition.setApvNo("YSTN" + request.getOrderCode());
                List<WhApv> whApvs = whApvService.queryWhApvAndItemList(queryCondition, null);
                if (CollectionUtils.isNotEmpty(whApvs)){
                    WhApv existApv = whApvs.get(0);
                    if (existApv.getStatus().equals(ApvStatus.DELIVER.intCode()) || existApv.getStatus().equals(ApvStatus.LOADED.intCode())) {
                        log.warn("cancel failed YSTN:"+request.getOrderCode());
                        response.setErrorMsg("订单已出库");
                        response.setSuccess(false);//业务成功
                        return response;
                    }else{
                        //WhApv apv = new WhApv();
                        //apv.setId(existApv.getId());
                        //apv.setStatus(ApvStatus.CANCEL.intCode());
                        //apv.setLastModifiedTime(new Timestamp(System.currentTimeMillis()));
                        //whApvService.updateWhApv(apv);
                        //SystemLogUtils.APVLOG.log(apv.getId(), "订单取消", new String[][]{{"历史状态", existApv.getStatusCn()},
                        //        {"更改状态", ApvStatus.CANCEL.getName()}, {"DD号更改", existApv.getSalesRecordNumber()},
                        //        {StringUtils.isNotBlank(existApv.getCancelReason()) ? "原因" : null, existApv.getCancelReason()}});
                        ApvStatusUpdateService apvStatusUpdateService = SpringUtils.getBean(ApvStatusUpdateService.class);
                        apvStatusUpdateService.cancel(whApvs.get(0));
                        /*WhApv apv = new WhApv();
                        apv.setId(whApvs.get(0).getId());
                        apv.setStatus(ApvStatus.CANCEL.intCode());
                        whApvService.updateWhApv(apv);*/
                    }
                }
            }else {
                // 业务处理逻辑
                AmqMessage amqMeg = AssembleMessageDataUtils.assembleDataToFinance(PacCommonConfiger.WMS_ORDER_CANCEL_NOTIFY, amqMessage -> {
                    // 关联字段（expressId=uuid）用于消息回执删除消息
                    String relevantParam = request.getOrderCode().toString();
                    amqMessage.setRelevantParam(relevantParam);
                    // 消息体
                    String messageBody = JSON.toJSONString(request);
                    return messageBody;
                });
                AmqMessageService amqMessageService = SpringUtils.getBean(AmqMessageService.class);
                amqMessageService.createAmqMessage(amqMeg);
            }

          /*  // 回调平台成功
            PacClient pacClient = SpringUtils.getBean(PacClient.class);
            SendSysParams sendSysParams = SpringUtils.getBean(SendSysParams.class);

            WmsOrderStatusUploadRequest uploadRequest =  new WmsOrderStatusUploadRequest();
            uploadRequest.setOrderType(request.getOrderType().toString());
            uploadRequest.setOrderCode(request.getOrderCode());
            uploadRequest.setStatus("WMS_ACCEPT");
            uploadRequest.setOperator("SZU902");
            uploadRequest.setOperatorContact("12345656");
            uploadRequest.setOperateDate(new Date());
            pacClient.send(uploadRequest,sendSysParams);*/
            response.setSuccess(true);//业务成功
            log.info("WmsOrderCancelNotifyCpImpl success");
            return response;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new RuntimeException(e.getMessage());
        }
    }

}
