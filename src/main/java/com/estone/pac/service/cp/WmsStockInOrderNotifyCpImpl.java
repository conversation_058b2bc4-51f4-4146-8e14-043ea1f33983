package com.estone.pac.service.cp;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.estone.common.util.RedissonLockUtil;
import com.estone.common.util.SpringUtils;
import com.estone.pac.bean.PacBatchSendParam;
import com.estone.pac.bean.PacBatchSendParamQueryCondition;
import com.estone.pac.configer.PacCommonConfiger;
import com.estone.pac.enums.PacOrderUploadStatus;
import com.estone.pac.service.PacBatchSendParamService;
import com.estone.system.rabbitmq.bean.AmqMessage;
import com.estone.system.rabbitmq.service.AmqMessageService;
import com.estone.system.rabbitmq.utils.AssembleMessageDataUtils;
import com.taobao.pac.sdk.cp.PacClient;
import com.taobao.pac.sdk.cp.ReceiveListener;
import com.taobao.pac.sdk.cp.ReceiveSysParams;
import com.taobao.pac.sdk.cp.SendSysParams;
import com.taobao.pac.sdk.cp.dataobject.request.WMS_ORDER_STATUS_UPLOAD.WmsOrderStatusUploadRequest;
import com.taobao.pac.sdk.cp.dataobject.request.WMS_STOCK_IN_ORDER_NOTIFY.BatchSendCtrlParam;
import com.taobao.pac.sdk.cp.dataobject.request.WMS_STOCK_IN_ORDER_NOTIFY.WmsStockInOrderNotifyRequest;
import com.taobao.pac.sdk.cp.dataobject.response.WMS_STOCK_IN_ORDER_NOTIFY.WmsStockInOrderNotifyResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;
import java.util.Date;
import java.util.List;
import java.util.concurrent.Semaphore;
import java.util.stream.IntStream;


/**
 * 入库单通知
 * <AUTHOR>
 *
 */
@Slf4j
@Service
public class WmsStockInOrderNotifyCpImpl implements ReceiveListener<WmsStockInOrderNotifyRequest, WmsStockInOrderNotifyResponse> {

    private static String lockKey = "REDISSONLOCK:";

    @Override
    public WmsStockInOrderNotifyResponse execute(ReceiveSysParams arg0,
                                                 WmsStockInOrderNotifyRequest request) {
        //log.info("WmsStockInOrderNotifyCpImpl ReceiveSysParams："+ JSONObject.toJSONString(arg0));
        log.info("WmsStockInOrderNotifyCpImpl WmsStockInOrderNotifyRequest："+ request.getOrderCode());
        // TODO Auto-generated method stub
        WmsStockInOrderNotifyResponse response = new WmsStockInOrderNotifyResponse();
        String orderCode = request.getOrderCode();
        BatchSendCtrlParam batchSendCtrlParam = request.getBatchSendCtrlParam();
        
        // 是否分批下发参数 0 一次发送 1 多次发送
        Integer distributeType = 0;
        if (batchSendCtrlParam != null)
            distributeType = batchSendCtrlParam.getDistributeType();

        // 分批是否完成
        boolean isOver = true;
        try {
            if (RedissonLockUtil.tryLock(lockKey+orderCode, 5, 10)) {

                PacBatchSendParamService pacBatchSendParamService = SpringUtils.getBean(PacBatchSendParamService.class);
                PacBatchSendParamQueryCondition queryCondition = new PacBatchSendParamQueryCondition();
                queryCondition.setOrderCode(orderCode);
                List<PacBatchSendParam> pacBatchSendParams = pacBatchSendParamService.queryPacBatchSendParams(queryCondition, null);
                // 分批下发
                if (distributeType.equals(1) || CollectionUtils.isNotEmpty(pacBatchSendParams)){

                    boolean exist = pacBatchSendParams.stream().anyMatch(pacBatchSendParam ->
                            StringUtils.equalsIgnoreCase(pacBatchSendParam.getUniqueCode(), batchSendCtrlParam.getUniqueCode()));
                    //下发批次已经存在
                    if(exist){
                        response.setSuccess(true);//业务成功
                        return response;
                    }
                    //不存在
                    //下发总数
                    int totalOrderItemCount = batchSendCtrlParam.getTotalOrderItemCount() == null ? 0 : batchSendCtrlParam.getTotalOrderItemCount();
                    // 获取已下发数量之和
                    int existQty = pacBatchSendParams.stream().mapToInt(pacParam -> pacParam.getSumOrderItemCount() == null ? 0 : pacParam.getSumOrderItemCount()).sum();
                    // 获取本次下发数量之和
                    int curQty = request.getOrderItemList().size();
                    if (existQty + curQty < totalOrderItemCount) isOver = false;
                    // 保存本批次数据
                    PacBatchSendParam pacBatchSendParam = new PacBatchSendParam();
                    pacBatchSendParam.setOrderCode(orderCode);
                    pacBatchSendParam.setUniqueCode(batchSendCtrlParam.getUniqueCode());
                    pacBatchSendParam.setSumOrderItemCount(curQty);
                    pacBatchSendParam.setTotalOrderItemCount(totalOrderItemCount);
                    pacBatchSendParam.setDistributeType(distributeType);
                    pacBatchSendParamService.createPacBatchSendParam(pacBatchSendParam);
                }


                // 业务处理逻辑
                AmqMessage amqMeg = AssembleMessageDataUtils.assembleDataToFinance(PacCommonConfiger.WMS_STOCK_IN_ORDER_NOTIFY, amqMessage -> {
                    // 关联字段（expressId=uuid）用于消息回执删除消息
                    String relevantParam = request.getOrderCode();
                    amqMessage.setRelevantParam(relevantParam);
                    // 消息体
                    String messageBody = JSON.toJSONString(request);
                    return messageBody;
                });
                AmqMessageService amqMessageService = SpringUtils.getBean(AmqMessageService.class);
                amqMessageService.createAmqMessage(amqMeg);

                // 回调平台成功
                if (isOver) {
                    PacClient pacClient = SpringUtils.getBean(PacClient.class);
                    SendSysParams sendSysParams = SpringUtils.getBean(SendSysParams.class);
                    WmsOrderStatusUploadRequest uploadRequest = new WmsOrderStatusUploadRequest();
                    uploadRequest.setOrderType(request.getOrderType().toString());
                    uploadRequest.setOrderCode(request.getOrderCode());
                    uploadRequest.setStatus(PacOrderUploadStatus.WMS_ACCEPT.getCode());
                    uploadRequest.setOperator("SZU902");
                    uploadRequest.setOperatorContact("12345656");
                    uploadRequest.setOperateDate(new Date());
                    pacClient.send(uploadRequest, sendSysParams);
                }
                response.setSuccess(true);//业务成功
                log.info("WmsStockInOrderNotifyCpImpl success");
                return response;
            }else{
                response.setErrorMsg("tryLock time out");
                response.setSuccess(false);//业务成功
                log.info("WmsStockInOrderNotifyCpImpl failed");
                return response;
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new RuntimeException(e.getMessage());
        }finally {
            RedissonLockUtil.unlock(lockKey+orderCode);
        }
    }
}
