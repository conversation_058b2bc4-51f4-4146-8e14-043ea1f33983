package com.estone.pac.service;

import com.estone.pac.bean.PacStockInOrder;
import com.estone.pac.bean.PacStockInOrderQueryCondition;
import com.estone.system.rabbitmq.bean.AmqMessage;
import com.taobao.pac.sdk.cp.dataobject.request.WMS_STOCK_IN_ORDER_NOTIFY.WmsStockInOrderNotifyRequest;
import com.whq.tool.component.Pager;
import com.whq.tool.json.ResponseJson;

import java.util.List;

public interface PacStockInOrderService {
    List<PacStockInOrder> queryAllPacStockInOrders();

    List<PacStockInOrder> queryPacStockInOrders(PacStockInOrderQueryCondition query, Pager pager);

    PacStockInOrder getPacStockInOrder(Integer id);

    PacStockInOrder getPacStockInOrderDetail(Integer id);

    PacStockInOrder queryPacStockInOrder(PacStockInOrderQueryCondition query);

    void createPacStockInOrder(PacStockInOrder pacStockInOrder);

    void batchCreatePacStockInOrder(List<PacStockInOrder> entityList);

    void deletePacStockInOrder(Integer id);

    void updatePacStockInOrder(PacStockInOrder pacStockInOrder);

    void batchUpdatePacStockInOrder(List<PacStockInOrder> entityList);

    List<PacStockInOrder> queryPacStockInOrderAndItems(PacStockInOrderQueryCondition query, Pager page);

    ResponseJson doPacStockInOrder(AmqMessage amqMessage);

    void doPushPacReturnQty();

     void sendPac(WmsStockInOrderNotifyRequest request, PacStockInOrder pacStockInOrder);
}