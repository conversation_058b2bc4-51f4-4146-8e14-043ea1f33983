package com.estone.pac.service;

import com.estone.pac.bean.PacItemInfoSyncVo;
import com.estone.pac.bean.PacSkuInfo;
import com.estone.pac.bean.PacSkuInfoQueryCondition;
import com.estone.pac.bean.PacSkuWeightInfo;
import com.estone.sku.bean.WhSku;
import com.estone.system.rabbitmq.bean.AmqMessage;
import com.whq.tool.component.Pager;
import com.whq.tool.json.ResponseJson;
import java.util.List;


public interface PacSkuInfoService {
    List<PacSkuInfo> queryAllPacSkuInfos();

    List<PacSkuInfo> queryPacSkuInfos(PacSkuInfoQueryCondition query, Pager pager);

    PacSkuInfo getPacSkuInfo(Integer id);

    PacSkuInfo getPacSkuInfoDetail(Integer id);

    PacSkuInfo queryPacSkuInfo(PacSkuInfoQueryCondition query);

    void createPacSkuInfo(PacSkuInfo pacSkuInfo);

    void batchCreatePacSkuInfo(List<PacSkuInfo> entityList);

    void deletePacSkuInfo(Integer id);

    void updatePacSkuInfo(PacSkuInfo pacSkuInfo);

    void batchUpdatePacSkuInfo(List<PacSkuInfo> entityList);

    ResponseJson doPacSkuInfo(AmqMessage amqMessage);

    void pushPacSkuInfo(List<PacSkuWeightInfo> itemIds, ResponseJson response);

    void pushWmsItemQuery(List<String> itemIds, ResponseJson response);

    void updateSkuInfo(List<WhSku> updateWhSku);

    //同步重量等平台信息
    void syncPacItemInfo(List<Long> itemIds,Long ownerUserId);

    void onTransation();
    public void passBack(List<PacItemInfoSyncVo> listVo,ResponseJson responseJson);
}