package com.estone.pac.bean;

import lombok.Data;

/**
 * @Description: 新优选仓库存汇总
 * @Author: Yimeil
 * @Date: 2022/3/21 12:00
 * @Version: 1.0.0
 */
@Data
public class PacNewStockGroup {

    /**
     * 可用
     */
    private Long localSurplusQtySum;
    /**
     * 待分配
     */
    private Long waitingAllotQtySum;
    /**
     * oms预分配
     */
    private Long omsPredistributionQtySum;
    /**
     * 待盘点
     */
    private Long waitingCheckQtySum;
    /**
     * 优选仓可用良品
     */
    private Long qtySum;
    /**
     * 优选仓良品占用
     */
    private Long lockQtySum;
    /**
     * 差异
     */
    private Long differenceQtySum;
}
