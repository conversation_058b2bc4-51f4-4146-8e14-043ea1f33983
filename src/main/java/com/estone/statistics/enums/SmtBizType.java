package com.estone.statistics.enums;

/**
 * @Description: SMT库存类型
 * @Author: Yimeil
 * @Date: 2025/2/28 16:00
 * @Version: 1.0.0
 */
public enum SmtBizType {
    PO0("普通采购", "PO0"),
    RTV0("普通采购退货", "RTV0"),
    ADJ0("报废", "ADJ0"),
    ADJ1("盘点调整", "ADJ1"),
    ADJ2("状态调整", "ADJ2"),
    TSF0("调拨", "TSF0"),
    SO0("TOC销售", "SO0"),
    RSO0("TOC销售退货", "RSO0"),
    RSO4("RSO4", "RSO4");

    private String code;

    private String name;

    private SmtBizType(String name, String code) {
        this.name = name;
        this.code = code;
    }

    public String getCode() {
        return code;
    }

    public Integer intCode() {
        return Integer.valueOf(code);
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public static String getNameByCode(String code) {
        SmtBizType[] values = values();
        for (SmtBizType type : values) {
            if (type.code.equals(code)) {
                return type.getName();
            }
        }
        return null;
    }

    public static SmtBizType build(String code) {
        SmtBizType[] values = values();

        for (SmtBizType type : values) {
            if (type.code.equalsIgnoreCase(code)) {
                return type;
            }
        }

        return null;
    }
}
