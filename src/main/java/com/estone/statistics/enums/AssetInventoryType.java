package com.estone.statistics.enums;

/**
 * 资产库存类型
 * 
 * <AUTHOR>
 * @date 2019年6月25日
 * 
 */
public enum AssetInventoryType {

    CHECK_IN("入库", "1"),

    CHECK_OUT("出库", "2"),

    CHECK_REFUND("退件入库", "3"),

    INVENTORY("盘点", "4"),

    INVENTORY_ALLOCATION("库存调拨", "5"),
    
    ORDER_ALLOCATION("订单调拨", "6"),
    
    ALLOCATION_BACK("调拨返架", "7");

    private String code;

    private String name;

    private AssetInventoryType(String name, String code) {
        this.name = name;
        this.code = code;
    }

    public String getCode() {
        return code;
    }

    public Integer intCode() {
        return Integer.valueOf(code);
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public static String getNameByCode(String code) {
        AssetInventoryType[] values = values();
        for (AssetInventoryType type : values) {
            if (type.code.equals(code)) {
                return type.getName();
            }
        }
        return null;
    }
}
