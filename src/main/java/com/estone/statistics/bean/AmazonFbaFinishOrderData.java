package com.estone.statistics.bean;

import java.io.Serializable;
import java.math.BigDecimal;
import java.sql.Timestamp;

import com.opencsv.bean.CsvBindByName;
import lombok.Data;
import org.apache.commons.lang.StringUtils;

@Data
public class AmazonFbaFinishOrderData implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键 database column amazon_fba_finish_order_data.id
     */
    private Integer id;

    /**
     * 备货销售账号 database column amazon_fba_finish_order_data.account_number
     */
    private String accountNumber;

    /**
     * 配送日期 database column amazon_fba_finish_order_data.shipment_date
     */
    private Timestamp shipmentDate;
    @CsvBindByName(column = "shipment-date")
    private String shipmentDateStr;
    /**
     * sku database column amazon_fba_finish_order_data.sku
     */
    @CsvBindByName(column = "sku")
    private String sku;

    /**
     * fnsku database column amazon_fba_finish_order_data.fnsku
     */
    @CsvBindByName(column = "fnsku")
    private String fnsku;

    /**
     * ASIN database column amazon_fba_finish_order_data.asin
     */
    @CsvBindByName(column = "asin")
    private String asin;

    /**
     * 运营中心 database column amazon_fba_finish_order_data.fulfillment_center_id
     */
    @CsvBindByName(column = "fulfillment-center-id")
    private String fulfillmentCenterId;

    /**
     * 数量 database column amazon_fba_finish_order_data.quantity
     */
    @CsvBindByName(column = "quantity")
    private Integer quantity;

    /**
     * 亚马逊订单编号 database column amazon_fba_finish_order_data.amazon_order_id
     */
    @CsvBindByName(column = "amazon-order-id")
    private String amazonOrderId;

    /**
     * 货币类型 database column amazon_fba_finish_order_data.currency
     */
    @CsvBindByName(column = "currency")
    private String currency;

    /**
     * 商品金额 database column amazon_fba_finish_order_data.item_price_per_unit
     */
    @CsvBindByName(column = "item-price-per-unit")
    private BigDecimal itemPricePerUnit;

    /**
     * 运费 database column amazon_fba_finish_order_data.shipping_price
     */
    @CsvBindByName(column = "shipping-price")
    private BigDecimal shippingPrice;

    /**
     * 礼品金额 database column amazon_fba_finish_order_data.gift_wrap_price
     */
    @CsvBindByName(column = "gift-wrap-price")
    private BigDecimal giftWrapPrice;

    /**
     * 收货城市 database column amazon_fba_finish_order_data.ship_city
     */
    @CsvBindByName(column = "ship-city")
    private String shipCity;

    /**
     * 收货州 database column amazon_fba_finish_order_data.ship_state
     */
    @CsvBindByName(column = "ship-state")
    private String shipState;

    /**
     * 收货地邮政编码 database column amazon_fba_finish_order_data.ship_postal_code
     */
    @CsvBindByName(column = "ship-postal-code")
    private String shipPostalCode;

    /**
     * 创建人 database column amazon_fba_finish_order_data.created_by
     */
    private Integer createdBy;

    /**
     * 创建时间 database column amazon_fba_finish_order_data.creation_date
     */
    private Timestamp creationDate;

    /**
     * 最后修改人 database column amazon_fba_finish_order_data.last_update_user
     */
    private Integer lastUpdateUser;

    /**
     * 最后修改时间 database column amazon_fba_finish_order_data.last_update_date
     */
    private Timestamp lastUpdateDate;

    /**
     * 站点 database column amazon_fba_finish_order_data.market_place
     */
    private String marketPlace;

    /**
     * 卖家记号 database column amazon_fba_finish_order_data.merchant_id
     */
    private String merchantId;

    
    private String hashCodeStr;


    public String getHashCodeStr() {
        if (StringUtils.isNotEmpty(hashCodeStr)) {
            return hashCodeStr;
        }
        String dataStr = this.shipmentDate + this.sku + this.fnsku + this.fulfillmentCenterId + this.amazonOrderId
                + this.merchantId;
        return dataStr.hashCode() + "";
    }
}