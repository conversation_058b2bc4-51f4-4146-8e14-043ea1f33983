package com.estone.statistics.bean;

import lombok.Data;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021/9/27 11:53
 **/
@Data
@XmlAccessorType(XmlAccessType.FIELD)
@XmlRootElement
public class AmazonAnalysisOrder {
    @XmlElement(name = "Order")
    private List<AmazonAnalysisOrderDetails> amazonAnalysisOrderDetails;
}
