package com.estone.statistics.bean;

import java.io.Serializable;
import java.math.BigDecimal;
import java.sql.Timestamp;
import lombok.Data;

@Data
public class WhInvoicingChangeMonthCountLocal implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     *  database column wh_invoicing_change_month_count_local.id
     */
    private Integer id;

    /**
     * SKU database column wh_invoicing_change_month_count_local.sku
     */
    private String sku;

    /**
     * 仓库（单据取对应的店铺） database column wh_invoicing_change_month_count_local.accountNumber
     */
    private String accountnumber;

    /**
     * 库存日期 database column wh_invoicing_change_month_count_local.stock_date
     */
    private Timestamp stockDate;

    /**
     * 期末库存 database column wh_invoicing_change_month_count_local.quantity
     */
    private Integer quantity;

    /**
     * 期末采购单价 database column wh_invoicing_change_month_count_local.ending_purchase_price
     */
    private BigDecimal endingPurchasePrice;

    /**
     * 期末采购运费单价 database column wh_invoicing_change_month_count_local.ending_purchase_cost
     */
    private BigDecimal endingPurchaseCost;

    /**
     * 期末头程运费 database column wh_invoicing_change_month_count_local.ending_first_trip_cost
     */
    private BigDecimal endingFirstTripCost;

    /**
     * 期末头程税费 database column wh_invoicing_change_month_count_local.ending_first_trip_tax
     */
    private BigDecimal endingFirstTripTax;

    /**
     * 期末退税单价 database column wh_invoicing_change_month_count_local.ending_refund_tax
     */
    private BigDecimal endingRefundTax;

    /**
     * 期末调拨运费单价 database column wh_invoicing_change_month_count_local.ending_allot_cost
     */
    private BigDecimal endingAllotCost;

    /**
     * 期末库存金额 database column wh_invoicing_change_month_count_local.ending_amount
     */
    private BigDecimal endingAmount;

    /**
     * 平台仓报告库存 database column wh_invoicing_change_month_count_local.plant_report_quantity
     * todo 作为30天销量存储
     */
    private Integer plantReportQuantity;

    /**
     * 库存差异 database column wh_invoicing_change_month_count_local.quantity_diff
     */
    private Integer quantityDiff;

    /**
     * 统计时间 database column wh_invoicing_change_month_count_local.count_date
     */
    private Timestamp countDate;

    /**
     * 卖家记号 database column wh_invoicing_change_month_count_local.merchant_id
     */
    private String merchantId;

    /**
     * 期末单价 database column wh_invoicing_change_month_count_local.ending_price
     */
    private BigDecimal endingPrice;

    /**
     * 期末金额 database column wh_invoicing_change_month_count_local.ending_total_amount
     */
    private BigDecimal endingTotalAmount;

    /**
     * 销售属性 database column wh_invoicing_change_month_count_local.sales_property
     */
    private String salesProperty;

    /**
     * 折扣 database column wh_invoicing_change_month_count_local.discount
     */
    private BigDecimal discount;

    /**
     * 促销标签是否已取消 database column wh_invoicing_change_month_count_local.promotion_label
     */
    private String promotionLabel;

    /**
     * 更新时间 database column wh_invoicing_change_month_count_local.update_time
     */
    private Timestamp updateTime;
}