/**
 * @Title: WarehousingOperationStatisticsQueryCondition.java
 * @Package com.estone.statistics.bean
 * @Description: TODO
 * <AUTHOR>
 * @date 2019年2月14日
 * @version 0.0.2
 */
package com.estone.statistics.bean;

/**
 * @ClassName: WarehousingOperationStatisticsQueryCondition
 * @Description: TODO
 * <AUTHOR>
 * @date 2019年2月14日
 * @version 0.0.2
 *
 */
public class WarehousingOperationStatisticsQueryCondition extends WarehousingOperationStatistics{

    // 操作时间
    private String startTime;
    private String endTime;

    public String getStartTime() {
        return startTime;
    }

    public void setStartTime(String startTime) {
        this.startTime = startTime;
    }

    public String getEndTime() {
        return endTime;
    }

    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }
}
