package com.estone.statistics.bean;

import java.io.Serializable;

import com.estone.checkout.bean.SmtReturnOrderItem;
import com.estone.transfer.bean.WhFbaAllocationItem;

import lombok.Data;

@Data
public class SmtOrderComparison implements Serializable {
    private static final long serialVersionUID = 1L;

    public SmtOrderComparison() {
    };

    public SmtOrderComparison(Integer comparType, String orderType, Integer orderId, Integer orderQty,
            Integer inventoryId, Integer inventoryQty, String createTime) {
        this.comparType = comparType;
        this.orderType = orderType;
        this.orderId = orderId;
        this.inventoryId = inventoryId;
        this.inventoryQty = inventoryQty;
        this.orderQty = orderQty;
        this.createTime = createTime;
    }

    /**
     * 主键 database column smt_order_comparison.id
     */
    private Integer id;

    /**
     * 单据类型（1：备货单，2：发货单，3：退货单） database column smt_order_comparison.compar_type
     */
    private Integer comparType;

    /**
     * 单据类型（半托、全托） database column smt_order_comparison.order_type
     */
    private String orderType;

    /**
     * 单据ID database column smt_order_comparison.order_id
     */
    private Integer orderId;

    /**
     * 流水ID database column smt_order_comparison.inventory_id
     */
    private Integer inventoryId;

    /**
     * 同步时间 database column smt_order_comparison.create_time
     */
    private String createTime;

    private Integer inventoryQty;
    private Integer orderQty;

    private String sku;

    private SmtWarehouseInventory smtWarehouseInventory;
    private SmtReturnOrderItem smtReturnItem;

    private WhFbaAllocationItem whFbaAllocationItem;

    private SmtOmsOrder omsOrder;
}