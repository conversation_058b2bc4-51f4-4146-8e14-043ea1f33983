package com.estone.statistics.bean;

import java.io.Serializable;
import java.math.BigDecimal;
import java.sql.Timestamp;
import lombok.Data;

@Data
public class TemuSummaryMonthly implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     *  database column temu_inventory_summary_monthly.id
     */
    private Integer id;

    /**
     * sku database column temu_inventory_summary_monthly.sku
     */
    private String sku;

    /**
     * 销售属性 database column temu_inventory_summary_monthly.sale_attr
     */
    private String saleAttr;

    /**
     * 店铺 database column temu_inventory_summary_monthly.account_number
     */
    private String accountNumber;

    /**
     * 月份 yyyy-MM database column temu_inventory_summary_monthly.month
     */
    private String month;

    /**
     * 期末库存 database column temu_inventory_summary_monthly.end_quantity
     */
    private Integer endQuantity;

    /**
     * 期末采购成本单价 database column temu_inventory_summary_monthly.end_purchase_price
     */
    private BigDecimal endPurchasePrice;

    /**
     * 期末采购运费单价 database column temu_inventory_summary_monthly.end_purchase_cost
     */
    private BigDecimal endPurchaseCost;

    /**
     * 期末头程运费单价 database column temu_inventory_summary_monthly.end_first_trip_cost
     */
    private BigDecimal endFirstTripCost;

    /**
     * 期末库存金额 database column temu_inventory_summary_monthly.end_amount
     */
    private BigDecimal endAmount;

    /**
     * 期末未结算库存 database column temu_inventory_summary_monthly.unsettled_quantity
     */
    private Integer unsettledQuantity;

    /**
     * 期末未结算采购成本 database column temu_inventory_summary_monthly.unsettled_purchase_price
     */
    private BigDecimal unsettledPurchasePrice;

    /**
     * 期末未结算采购运费 database column temu_inventory_summary_monthly.unsettled_purchase_cost
     */
    private BigDecimal unsettledPurchaseCost;

    /**
     * 期末未结算头程运费 database column temu_inventory_summary_monthly.unsettled_first_trip_cost
     */
    private BigDecimal unsettledFirstTripCost;

    /**
     * 期末未结算库存金额
     */
    private BigDecimal unsettledAmount;

    /**
     * 实际期末库存 database column temu_inventory_summary_monthly.actual_end_quantity
     */
    private Integer actualEndQuantity;

    /**
     * 实际期末库存金额 database column temu_inventory_summary_monthly.actual_end_amount
     */
    private BigDecimal actualEndAmount;

    /**
     * 平台仓月度库存 database column temu_inventory_summary_monthly.platform_quantity
     */
    private Integer platformQuantity;

    /**
     * 库存差异 database column temu_inventory_summary_monthly.diff_quantity
     */
    private Integer diffQuantity;

    /**
     * 创建时间 database column temu_inventory_summary_monthly.creation_date
     */
    private Timestamp creationDate;
}