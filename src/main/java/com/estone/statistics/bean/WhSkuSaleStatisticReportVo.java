package com.estone.statistics.bean;

import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
public class WhSkuSaleStatisticReportVo {

    private List<WhSkuSaleStatisticReport> whSkuSaleStatisticReports;

    private Map<Integer, Integer> yTotal;

    private Map<String,Integer> xTotal;

    public WhSkuSaleStatisticReportVo() {
    }

    public WhSkuSaleStatisticReportVo(List<WhSkuSaleStatisticReport> whSkuSaleStatisticReports, Map<Integer, Integer> yTotal, Map<String, Integer> xTotal) {
        this.whSkuSaleStatisticReports = whSkuSaleStatisticReports;
        this.yTotal = yTotal;
        this.xTotal = xTotal;
    }
}

