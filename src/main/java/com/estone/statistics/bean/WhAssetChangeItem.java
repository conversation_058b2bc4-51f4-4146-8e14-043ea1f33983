package com.estone.statistics.bean;

import java.io.Serializable;
import java.math.BigDecimal;
import java.sql.Timestamp;

import com.estone.statistics.enums.AssetInventoryType;
import com.estone.statistics.enums.AssetOrderType;
import lombok.Data;

@Data
public class WhAssetChangeItem extends WhAssetChangeItemGroup implements Serializable {
    private static final long serialVersionUID = 1L;

    public WhAssetChangeItem() {

    }

    public WhAssetChangeItem(String sku, AssetInventoryType inventoryType, AssetOrderType orderType, String orderNo, Integer quantity) {
        this.sku = sku;
        this.inventoryType = inventoryType.intCode();
        this.orderType = orderType.intCode();
        this.orderNo = orderNo;
        this.currentCheckinQuantity = quantity;
    }

    /**
     * 主键
     */
    private Integer id;

    /**
     * SKU
     */
    private String sku;

    /**
     * 创建日期 This field corresponds to the database column
     * wh_asset_change_item.create_date
     */
    private Timestamp createDate;

    /**
     * 修改日期 This field corresponds to the database column
     * wh_asset_change_item.last_update_date
     */
    private Timestamp lastUpdateDate;

    /**
     * 库存类型 This field corresponds to the database column
     * wh_asset_change_item.inventory_type
     */
    private Integer inventoryType;

    /**
     * 单据类型 This field corresponds to the database column
     * wh_asset_change_item.order_type
     */
    private Integer orderType;

    /**
     * 单号 This field corresponds to the database column
     * wh_asset_change_item.order_no
     */
    private String orderNo;

    /**
     * 期初库存数量 This field corresponds to the database column
     * wh_asset_change_item.opening_inventory_quantity
     */
    private Integer openingInventoryQuantity;

    /**
     * 期初库存单价 This field corresponds to the database column
     * wh_asset_change_item.opening_inventory_price
     */
    private BigDecimal openingInventoryPrice;

    /**
     * 期初库存金额 This field corresponds to the database column
     * wh_asset_change_item.opening_inventory_amount
     */
    private BigDecimal openingInventoryAmount;

    /**
     * 本期入库数量 This field corresponds to the database column
     * wh_asset_change_item.current_checkin_quantity
     */
    private Integer currentCheckinQuantity;

    /**
     * 本期入库单价 This field corresponds to the database column
     * wh_asset_change_item.current_checkin_price
     */
    private BigDecimal currentCheckinPrice;

    /**
     * 本期入库金额 This field corresponds to the database column
     * wh_asset_change_item.current_checkin_amount
     */
    private BigDecimal currentCheckinAmount;

    /**
     * 期末库存数量 This field corresponds to the database column
     * wh_asset_change_item.ending_inventory_quantity
     */
    private Integer endingInventoryQuantity;

    /**
     * 期末库存单价 This field corresponds to the database column
     * wh_asset_change_item.ending_inventory_price
     */
    private BigDecimal endingInventoryPrice;

    /**
     * 期末库存金额 This field corresponds to the database column
     * wh_asset_change_item.ending_inventory_amount
     */
    private BigDecimal endingInventoryAmount;

    private Integer skuStatus;

    private BigDecimal cost;

    private BigDecimal price;

    public String getInventoryTypeName() {
        if (this.inventoryType != null) {
            for (AssetInventoryType type : AssetInventoryType.values()) {
                if (type.intCode().equals(this.inventoryType)) {
                    return type.getName();
                }
            }
        }
        return null;
    }

    public String getOrderTypeName() {
        if (this.orderType != null) {
            for (AssetOrderType type : AssetOrderType.values()) {
                if (type.intCode().equals(this.orderType)) {
                    return type.getName();
                }
            }
        }
        return null;
    }
}