package com.estone.statistics.bean;

import lombok.Data;

import java.util.List;

@Data
public class SmtWarehouseInventoryCountQueryCondition extends SmtWarehouseInventoryCount {
    private static final long serialVersionUID = 1L;

    private Boolean readOnly = false;

    private List<String> productIdList;
    private List<String> scItemCodeList;
    private List<String> skuList;

    private boolean querySkuRelation = false;
    private List<Integer> ids;
}