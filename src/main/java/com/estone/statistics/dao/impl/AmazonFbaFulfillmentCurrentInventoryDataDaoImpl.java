package com.estone.statistics.dao.impl;

import java.sql.Timestamp;
import java.util.List;

import org.springframework.stereotype.Repository;
import org.springframework.util.Assert;

import com.estone.common.util.SqlerTemplate;
import com.estone.statistics.bean.AmazonFbaFulfillmentCurrentInventoryData;
import com.estone.statistics.bean.AmazonFbaFulfillmentCurrentInventoryDataQueryCondition;
import com.estone.statistics.dao.AmazonFbaFulfillmentCurrentInventoryDataDao;
import com.estone.statistics.dao.mapper.AmazonFbaFulfillmentCurrentInventoryDataDBField;
import com.estone.statistics.dao.mapper.AmazonFbaFulfillmentCurrentInventoryDataMapper;
import com.whq.tool.component.Pager;
import com.whq.tool.context.DataContextHolder;
import com.whq.tool.sqler.SqlerRequest;
import com.whq.tool.sqler.analyzer.DataType;

@Repository("amazonFbaFulfillmentCurrentInventoryDataDao")
public class AmazonFbaFulfillmentCurrentInventoryDataDaoImpl implements AmazonFbaFulfillmentCurrentInventoryDataDao {

    private void setQueryCondition(SqlerRequest request, AmazonFbaFulfillmentCurrentInventoryDataQueryCondition query) {
        if (query == null) {
            return;
        }
        // 添加查询条件
        // 搜索条件不允许出现空字符
        request.setAllowBlank(false);
        
        request.addDataParam(AmazonFbaFulfillmentCurrentInventoryDataDBField.ID, DataType.INT, query.getId());
    }

    @Override
    public int queryAmazonFbaFulfillmentCurrentInventoryDataCount(AmazonFbaFulfillmentCurrentInventoryDataQueryCondition query) {
        SqlerRequest request = new SqlerRequest("queryAmazonFbaFulfillmentCurrentInventoryDataCount");
        setQueryCondition(request, query);
        return SqlerTemplate.queryForInt(request);
    }

    @Override
    public List<AmazonFbaFulfillmentCurrentInventoryData> queryAmazonFbaFulfillmentCurrentInventoryDataList() {
        SqlerRequest request = new SqlerRequest("queryAmazonFbaFulfillmentCurrentInventoryDataList");
        return SqlerTemplate.query(request, new AmazonFbaFulfillmentCurrentInventoryDataMapper());
    }

    @Override
    public List<AmazonFbaFulfillmentCurrentInventoryData> queryAmazonFbaFulfillmentCurrentInventoryDataList(AmazonFbaFulfillmentCurrentInventoryDataQueryCondition query, Pager pager) {
        SqlerRequest request = new SqlerRequest("queryAmazonFbaFulfillmentCurrentInventoryDataList");
        setQueryCondition(request, query);
        if(pager != null) {
            request.addFetch(pager.getPageNo(), pager.getPageSize());
        }
        return SqlerTemplate.query(request, new AmazonFbaFulfillmentCurrentInventoryDataMapper());
    }

    @Override
    public AmazonFbaFulfillmentCurrentInventoryData queryAmazonFbaFulfillmentCurrentInventoryData(Integer primaryKey) {
        Assert.notNull(primaryKey, "primaryKey is null!");
        SqlerRequest request = new SqlerRequest("queryAmazonFbaFulfillmentCurrentInventoryDataByPrimaryKey");
        request.addDataParam(AmazonFbaFulfillmentCurrentInventoryDataDBField.ID, DataType.INT, primaryKey);
        return SqlerTemplate.queryForObject(request, new AmazonFbaFulfillmentCurrentInventoryDataMapper());
    }

    @Override
    public AmazonFbaFulfillmentCurrentInventoryData queryAmazonFbaFulfillmentCurrentInventoryData(AmazonFbaFulfillmentCurrentInventoryDataQueryCondition query) {
        SqlerRequest request = new SqlerRequest("queryAmazonFbaFulfillmentCurrentInventoryData");
        setQueryCondition(request, query);
        return SqlerTemplate.queryForObject(request, new AmazonFbaFulfillmentCurrentInventoryDataMapper());
    }

    @Override
    public void createAmazonFbaFulfillmentCurrentInventoryData(AmazonFbaFulfillmentCurrentInventoryData entity) {
        SqlerRequest request = new SqlerRequest("createAmazonFbaFulfillmentCurrentInventoryData");
        request.addDataParam(AmazonFbaFulfillmentCurrentInventoryDataDBField.ACCOUNT_NUMBER, DataType.STRING, entity.getAccountNumber());
        request.addDataParam(AmazonFbaFulfillmentCurrentInventoryDataDBField.SNAPSHOT_DATE, DataType.TIMESTAMP, entity.getSnapshotDate());
        request.addDataParam(AmazonFbaFulfillmentCurrentInventoryDataDBField.FNSKU, DataType.STRING, entity.getFnsku());
        request.addDataParam(AmazonFbaFulfillmentCurrentInventoryDataDBField.SKU, DataType.STRING, entity.getSku());
        request.addDataParam(AmazonFbaFulfillmentCurrentInventoryDataDBField.PRODUCT_NAME, DataType.STRING, entity.getProductName());
        request.addDataParam(AmazonFbaFulfillmentCurrentInventoryDataDBField.QUANTITY, DataType.INT, entity.getQuantity());
        request.addDataParam(AmazonFbaFulfillmentCurrentInventoryDataDBField.FULFILLMENT_CENTER_ID, DataType.STRING, entity.getFulfillmentCenterId());
        request.addDataParam(AmazonFbaFulfillmentCurrentInventoryDataDBField.DETAILED_DISPOSITION, DataType.STRING, entity.getDetailedDisposition());
        request.addDataParam(AmazonFbaFulfillmentCurrentInventoryDataDBField.COUNTRY, DataType.STRING, entity.getCountry());
        request.addDataParam(AmazonFbaFulfillmentCurrentInventoryDataDBField.CREATED_BY, DataType.INT, entity.getCreatedBy() == null ? DataContextHolder.getUserId() : entity.getCreatedBy());
        request.addDataParam(AmazonFbaFulfillmentCurrentInventoryDataDBField.CREATION_DATE, DataType.TIMESTAMP, new Timestamp(System.currentTimeMillis()));
        request.addDataParam(AmazonFbaFulfillmentCurrentInventoryDataDBField.LAST_UPDATE_USER, DataType.INT, entity.getLastUpdateUser());
        request.addDataParam(AmazonFbaFulfillmentCurrentInventoryDataDBField.LAST_UPDATE_DATE, DataType.TIMESTAMP, new Timestamp(System.currentTimeMillis()));
        Integer autoIncrementId = SqlerTemplate.executeAndReturn(request);
        entity.setId(autoIncrementId);
    }

    @Override
    public void updateAmazonFbaFulfillmentCurrentInventoryData(AmazonFbaFulfillmentCurrentInventoryData entity) {
        SqlerRequest request = new SqlerRequest("updateAmazonFbaFulfillmentCurrentInventoryDataByPrimaryKey");
        request.addDataParam(AmazonFbaFulfillmentCurrentInventoryDataDBField.ID, DataType.INT, entity.getId());
        
        request.addDataParam(AmazonFbaFulfillmentCurrentInventoryDataDBField.ACCOUNT_NUMBER, DataType.STRING, entity.getAccountNumber());
        request.addDataParam(AmazonFbaFulfillmentCurrentInventoryDataDBField.SNAPSHOT_DATE, DataType.TIMESTAMP, entity.getSnapshotDate());
        request.addDataParam(AmazonFbaFulfillmentCurrentInventoryDataDBField.FNSKU, DataType.STRING, entity.getFnsku());
        request.addDataParam(AmazonFbaFulfillmentCurrentInventoryDataDBField.SKU, DataType.STRING, entity.getSku());
        request.addDataParam(AmazonFbaFulfillmentCurrentInventoryDataDBField.PRODUCT_NAME, DataType.STRING, entity.getProductName());
        request.addDataParam(AmazonFbaFulfillmentCurrentInventoryDataDBField.QUANTITY, DataType.INT, entity.getQuantity());
        request.addDataParam(AmazonFbaFulfillmentCurrentInventoryDataDBField.FULFILLMENT_CENTER_ID, DataType.STRING, entity.getFulfillmentCenterId());
        request.addDataParam(AmazonFbaFulfillmentCurrentInventoryDataDBField.DETAILED_DISPOSITION, DataType.STRING, entity.getDetailedDisposition());
        request.addDataParam(AmazonFbaFulfillmentCurrentInventoryDataDBField.COUNTRY, DataType.STRING, entity.getCountry());
        
        
        request.addDataParam(AmazonFbaFulfillmentCurrentInventoryDataDBField.LAST_UPDATE_USER, DataType.INT, entity.getLastUpdateUser());
        request.addDataParam(AmazonFbaFulfillmentCurrentInventoryDataDBField.LAST_UPDATE_DATE, DataType.TIMESTAMP, new Timestamp(System.currentTimeMillis()));
        SqlerTemplate.execute(request);
    }

    @Override
    public void batchInsertOrUpdateAmazonFbaFulfillmentCurrentInventoryData(List<AmazonFbaFulfillmentCurrentInventoryData> entityList) {
        if (entityList != null && !entityList.isEmpty()) {
            SqlerRequest request = new SqlerRequest("batchInsertOrUpdateAmazonFbaFulfillmentCurrentInventoryData");
            StringBuffer insertSql = new StringBuffer();
            StringBuffer updateSql = new StringBuffer();
            for (int i = 0; i < entityList.size(); i++) {
                AmazonFbaFulfillmentCurrentInventoryData a = entityList.get(i);
                insertSql.append("('").append(a.getAccountNumber()).append("','").append(a.getMarketPlace()).append("','")
                        .append(a.getMerchantId()).append("','").append(a.getSnapshotDate()).append("','")
                        .append(a.getFnsku()).append("','").append(a.getSku()).append("','")
                        .append(a.getProductName()).append("',").append(a.getQuantity()).append(",'")
                        .append(a.getFulfillmentCenterId()).append("','").append(a.getDetailedDisposition()).append("','").append(a.getCountry()).append("',")
                        .append(a.getCreatedBy()).append(",'").append(a.getCreationDate()).append("',")
                        .append(a.getLastUpdateUser()).append(",'").append(a.getLastUpdateDate()).append("')");
                if (i < entityList.size() - 1) insertSql.append(",");
            }
            request.addSqlDataParam("INSERT_SQL", insertSql.toString());

            updateSql.append(AmazonFbaFulfillmentCurrentInventoryDataDBField.LAST_UPDATE_DATE).append("=VALUES(").append(AmazonFbaFulfillmentCurrentInventoryDataDBField.LAST_UPDATE_DATE).append(")");
            request.addSqlDataParam("UPDATE_SQL",updateSql.toString());
            SqlerTemplate.execute(request);
        }
    }

    @Override
    public void batchCreateAmazonFbaFulfillmentCurrentInventoryData(List<AmazonFbaFulfillmentCurrentInventoryData> entityList) {
        if (entityList != null && !entityList.isEmpty()) {
            SqlerRequest request = new SqlerRequest("createAmazonFbaFulfillmentCurrentInventoryData");
            for (AmazonFbaFulfillmentCurrentInventoryData entity : entityList) {
                request.addBatchDataParam(AmazonFbaFulfillmentCurrentInventoryDataDBField.ACCOUNT_NUMBER, DataType.STRING, entity.getAccountNumber());
                request.addBatchDataParam(AmazonFbaFulfillmentCurrentInventoryDataDBField.SNAPSHOT_DATE, DataType.TIMESTAMP, entity.getSnapshotDate());
                request.addBatchDataParam(AmazonFbaFulfillmentCurrentInventoryDataDBField.FNSKU, DataType.STRING, entity.getFnsku());
                request.addBatchDataParam(AmazonFbaFulfillmentCurrentInventoryDataDBField.SKU, DataType.STRING, entity.getSku());
                request.addBatchDataParam(AmazonFbaFulfillmentCurrentInventoryDataDBField.PRODUCT_NAME, DataType.STRING, entity.getProductName());
                request.addBatchDataParam(AmazonFbaFulfillmentCurrentInventoryDataDBField.QUANTITY, DataType.INT, entity.getQuantity());
                request.addBatchDataParam(AmazonFbaFulfillmentCurrentInventoryDataDBField.FULFILLMENT_CENTER_ID, DataType.STRING, entity.getFulfillmentCenterId());
                request.addBatchDataParam(AmazonFbaFulfillmentCurrentInventoryDataDBField.DETAILED_DISPOSITION, DataType.STRING, entity.getDetailedDisposition());
                request.addBatchDataParam(AmazonFbaFulfillmentCurrentInventoryDataDBField.COUNTRY, DataType.STRING, entity.getCountry());
                request.addBatchDataParam(AmazonFbaFulfillmentCurrentInventoryDataDBField.CREATED_BY, DataType.INT, entity.getCreatedBy() == null ? DataContextHolder.getUserId() : entity.getCreatedBy());
                request.addBatchDataParam(AmazonFbaFulfillmentCurrentInventoryDataDBField.CREATION_DATE, DataType.TIMESTAMP, new Timestamp(System.currentTimeMillis()));
                request.addBatchDataParam(AmazonFbaFulfillmentCurrentInventoryDataDBField.LAST_UPDATE_USER, DataType.INT, entity.getLastUpdateUser());
                request.addBatchDataParam(AmazonFbaFulfillmentCurrentInventoryDataDBField.LAST_UPDATE_DATE, DataType.TIMESTAMP, new Timestamp(System.currentTimeMillis()));
                request.addBatch();
            }
            SqlerTemplate.batchUpdate(request);
        }
    }

    @Override
    public void batchUpdateAmazonFbaFulfillmentCurrentInventoryData(List<AmazonFbaFulfillmentCurrentInventoryData> entityList) {
        if (entityList != null && !entityList.isEmpty()) {
            SqlerRequest request = new SqlerRequest("updateAmazonFbaFulfillmentCurrentInventoryDataByPrimaryKey");
            for (AmazonFbaFulfillmentCurrentInventoryData entity : entityList) {
                request.addBatchDataParam(AmazonFbaFulfillmentCurrentInventoryDataDBField.ID, DataType.INT, entity.getId());
                
                request.addBatchDataParam(AmazonFbaFulfillmentCurrentInventoryDataDBField.ACCOUNT_NUMBER, DataType.STRING, entity.getAccountNumber());
                request.addBatchDataParam(AmazonFbaFulfillmentCurrentInventoryDataDBField.SNAPSHOT_DATE, DataType.TIMESTAMP, entity.getSnapshotDate());
                request.addBatchDataParam(AmazonFbaFulfillmentCurrentInventoryDataDBField.FNSKU, DataType.STRING, entity.getFnsku());
                request.addBatchDataParam(AmazonFbaFulfillmentCurrentInventoryDataDBField.SKU, DataType.STRING, entity.getSku());
                request.addBatchDataParam(AmazonFbaFulfillmentCurrentInventoryDataDBField.PRODUCT_NAME, DataType.STRING, entity.getProductName());
                request.addBatchDataParam(AmazonFbaFulfillmentCurrentInventoryDataDBField.QUANTITY, DataType.INT, entity.getQuantity());
                request.addBatchDataParam(AmazonFbaFulfillmentCurrentInventoryDataDBField.FULFILLMENT_CENTER_ID, DataType.STRING, entity.getFulfillmentCenterId());
                request.addBatchDataParam(AmazonFbaFulfillmentCurrentInventoryDataDBField.DETAILED_DISPOSITION, DataType.STRING, entity.getDetailedDisposition());
                request.addBatchDataParam(AmazonFbaFulfillmentCurrentInventoryDataDBField.COUNTRY, DataType.STRING, entity.getCountry());
                
                
                request.addBatchDataParam(AmazonFbaFulfillmentCurrentInventoryDataDBField.LAST_UPDATE_USER, DataType.INT, entity.getLastUpdateUser());
                request.addBatchDataParam(AmazonFbaFulfillmentCurrentInventoryDataDBField.LAST_UPDATE_DATE, DataType.TIMESTAMP, new Timestamp(System.currentTimeMillis()));
                request.addBatch();
            }
            SqlerTemplate.batchUpdate(request);
        }
    }

    @Override
    public void deleteAmazonFbaFulfillmentCurrentInventoryData(Integer primaryKey) {
        SqlerRequest request = new SqlerRequest("deleteAmazonFbaFulfillmentCurrentInventoryDataByPrimaryKey");
        request.addDataParam(AmazonFbaFulfillmentCurrentInventoryDataDBField.ID, DataType.INT, primaryKey);
        SqlerTemplate.execute(request);
    }
}