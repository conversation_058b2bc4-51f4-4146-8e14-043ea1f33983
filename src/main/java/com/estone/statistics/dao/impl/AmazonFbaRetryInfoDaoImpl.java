package com.estone.statistics.dao.impl;

import java.sql.Timestamp;
import java.util.List;
import java.util.Objects;

import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Repository;
import org.springframework.util.Assert;

import com.estone.common.util.SqlerTemplate;
import com.estone.statistics.bean.AmazonFbaRetryInfo;
import com.estone.statistics.bean.AmazonFbaRetryInfoQueryCondition;
import com.estone.statistics.dao.AmazonFbaRetryInfoDao;
import com.estone.statistics.dao.mapper.AmazonFbaRetryInfoDBField;
import com.estone.statistics.dao.mapper.AmazonFbaRetryInfoMapper;
import com.whq.tool.component.Pager;
import com.whq.tool.sqler.SqlerRequest;
import com.whq.tool.sqler.analyzer.DataType;

import jodd.util.StringUtil;

@Repository("amazonFbaRetryInfoDao")
public class AmazonFbaRetryInfoDaoImpl implements AmazonFbaRetryInfoDao {

    private void setQueryCondition(SqlerRequest request, AmazonFbaRetryInfoQueryCondition query) {
        if (query == null) {
            return;
        }
        if (Objects.nonNull(query.getReadOnly()) && query.getReadOnly()){
            request.setReadOnly(true);
        }
        // 添加查询条件
        // 搜索条件不允许出现空字符
        request.setAllowBlank(false);

        request.addDataParam(AmazonFbaRetryInfoDBField.ID, DataType.INT, query.getId());
        request.addDataParam(AmazonFbaRetryInfoDBField.STATUS, DataType.INT , query.getStatus());
        request.addDataParam(AmazonFbaRetryInfoDBField.CREATE_TIME, DataType.TIMESTAMP, query.getCreateTime());

        if (StringUtil.isNotBlank(query.getStartRequestTime())){
            request.addDataParam("startRequestTime", DataType.STRING , query.getStartRequestTime());
        }
        if (StringUtil.isNotBlank(query.getEndRequestTime())){
            request.addDataParam("endRequestTime", DataType.STRING, query.getEndRequestTime());
        }
        if (CollectionUtils.isNotEmpty(query.getIds())){
            request.addDataParam("ids", DataType.LONG, query.getIds());
        }
        if (StringUtil.isNotBlank(query.getSystemAccount())){
            request.addDataParam(AmazonFbaRetryInfoDBField.SYSTEM_ACCOUNT, DataType.STRING, "%"+query.getSystemAccount()+"%");
        }
        if (StringUtil.isNotBlank(query.getMerchantId())){
            request.addDataParam(AmazonFbaRetryInfoDBField.MERCHANT_ID, DataType.STRING ,query.getMerchantId());
        }
        if (StringUtil.isNotBlank(query.getReportTypeDes())){
            request.addDataParam(AmazonFbaRetryInfoDBField.REPORT_TYPE_DES, DataType.STRING, query.getReportTypeDes()+"%");
        }
        if (query.getRequestResult() != null){
            request.addDataParam(AmazonFbaRetryInfoDBField.REQUEST_RESULT,DataType.INT, query.getRequestResult());
        }
        if (query.getRetryId() != null){
            request.addDataParam(AmazonFbaRetryInfoDBField.RETRY_ID,DataType.INT, query.getRetryId());
        }
        if (StringUtil.isNotBlank(query.getAccountNumber())){
            request.addDataParam(AmazonFbaRetryInfoDBField.ACCOUNT_NUMBER,DataType.STRING, query.getAccountNumber());
        }
        if (StringUtil.isNotBlank(query.getReportType())){
            request.addDataParam(AmazonFbaRetryInfoDBField.REPORT_TYPE,DataType.STRING, query.getReportType());
        }
        if (query.getProgressId() != null){
            request.addBatchDataParam(AmazonFbaRetryInfoDBField.PROGRESS_ID,DataType.INT, query.getProgressId());
        }
        if (query.getRetryCount() != null){
            request.addSqlDataParam("LESS_RETRY_COUNT"," AND retry_count < "+query.getRetryCount());
        }
    }

    @Override
    public int queryAmazonFbaRetryInfoCount(AmazonFbaRetryInfoQueryCondition query) {
        SqlerRequest request = new SqlerRequest("queryAmazonFbaRetryInfoCount");
        setQueryCondition(request, query);
        return SqlerTemplate.queryForInt(request);
    }

    @Override
    public List<AmazonFbaRetryInfo> queryAmazonFbaRetryInfoList() {
        SqlerRequest request = new SqlerRequest("queryAmazonFbaRetryInfoList");
        return SqlerTemplate.query(request, new AmazonFbaRetryInfoMapper());
    }

    @Override
    public List<AmazonFbaRetryInfo> queryAmazonFbaRetryInfoList(AmazonFbaRetryInfoQueryCondition query, Pager pager) {
        SqlerRequest request = new SqlerRequest("queryAmazonFbaRetryInfoList");
        setQueryCondition(request, query);
        if(pager != null) {
            request.addFetch(pager.getPageNo(), pager.getPageSize());
        }
        return SqlerTemplate.query(request, new AmazonFbaRetryInfoMapper());
    }

    @Override
    public AmazonFbaRetryInfo queryAmazonFbaRetryInfo(Integer primaryKey) {
        Assert.notNull(primaryKey, "primaryKey is null!");
        SqlerRequest request = new SqlerRequest("queryAmazonFbaRetryInfoByPrimaryKey");
        request.addDataParam(AmazonFbaRetryInfoDBField.ID, DataType.INT, primaryKey);
        return SqlerTemplate.queryForObject(request, new AmazonFbaRetryInfoMapper());
    }

    @Override
    public AmazonFbaRetryInfo queryAmazonFbaRetryInfo(AmazonFbaRetryInfoQueryCondition query) {
        SqlerRequest request = new SqlerRequest("queryAmazonFbaRetryInfo");
        setQueryCondition(request, query);
        return SqlerTemplate.queryForObject(request, new AmazonFbaRetryInfoMapper());
    }

    @Override
    public void createAmazonFbaRetryInfo(AmazonFbaRetryInfo entity) {
        SqlerRequest request = new SqlerRequest("createAmazonFbaRetryInfo");
        request.addDataParam(AmazonFbaRetryInfoDBField.ACCOUNT_NUMBER, DataType.STRING, entity.getAccountNumber());
        request.addDataParam(AmazonFbaRetryInfoDBField.ACCOUNT_DETAIL, DataType.STRING, entity.getAccountDetail());
        request.addDataParam(AmazonFbaRetryInfoDBField.STATUS, DataType.INT, entity.getStatus());
        request.addDataParam(AmazonFbaRetryInfoDBField.CREATE_TIME, DataType.TIMESTAMP, entity.getCreateTime());
        request.addDataParam(AmazonFbaRetryInfoDBField.REPORT_TYPE, DataType.STRING, entity.getReportType());
        request.addDataParam(AmazonFbaRetryInfoDBField.SYNC_DATE, DataType.STRING, entity.getSyncDate());
        request.addDataParam(AmazonFbaRetryInfoDBField.START_TIME,DataType.STRING,entity.getStartTime());
        request.addDataParam(AmazonFbaRetryInfoDBField.END_TIME,DataType.STRING,entity.getEndTime());
        request.addDataParam(AmazonFbaRetryInfoDBField.MERCHANT_ID,DataType.STRING,entity.getMerchantId());
        request.addDataParam(AmazonFbaRetryInfoDBField.SYSTEM_ACCOUNT,DataType.STRING,entity.getSystemAccount());
        request.addDataParam(AmazonFbaRetryInfoDBField.REPORT_TYPE_DES,DataType.STRING,entity.getReportTypeDes());
        request.addDataParam(AmazonFbaRetryInfoDBField.SYNC_RANGE,DataType.STRING,entity.getSyncRange());
        request.addDataParam(AmazonFbaRetryInfoDBField.SYNC_FREQUENCY,DataType.STRING,entity.getSyncFrequency());
        request.addDataParam(AmazonFbaRetryInfoDBField.REQUEST_RESULT,DataType.INT,entity.getRequestResult());
        request.addDataParam(AmazonFbaRetryInfoDBField.ERROR_DETAIL,DataType.STRING,entity.getErrorDetail());
        request.addDataParam(AmazonFbaRetryInfoDBField.RETRY_COUNT,DataType.INT,entity.getRetryCount());
        request.addDataParam(AmazonFbaRetryInfoDBField.REQUEST_TIME,DataType.TIMESTAMP,entity.getRequestTime());
        request.addDataParam(AmazonFbaRetryInfoDBField.FINISH_TIME,DataType.TIMESTAMP,entity.getFinishTime());
        request.addDataParam(AmazonFbaRetryInfoDBField.RETRY_ID,DataType.INT,entity.getRetryId());
        request.addDataParam(AmazonFbaRetryInfoDBField.PROGRESS_ID,DataType.INT,entity.getProgressId());
        Integer autoIncrementId = SqlerTemplate.executeAndReturn(request);
        entity.setId(autoIncrementId);
    }

    @Override
    public void updateAmazonFbaRetryInfo(AmazonFbaRetryInfo entity) {
        SqlerRequest request = new SqlerRequest("updateAmazonFbaRetryInfoByPrimaryKey");
        request.addDataParam(AmazonFbaRetryInfoDBField.ID, DataType.INT, entity.getId());
        
        request.addDataParam(AmazonFbaRetryInfoDBField.ACCOUNT_NUMBER, DataType.STRING, entity.getAccountNumber());
        request.addDataParam(AmazonFbaRetryInfoDBField.ACCOUNT_DETAIL, DataType.STRING, entity.getAccountDetail());
        request.addDataParam(AmazonFbaRetryInfoDBField.CREATE_TIME, DataType.TIMESTAMP, entity.getCreateTime());
        request.addDataParam(AmazonFbaRetryInfoDBField.REPORT_TYPE, DataType.STRING, entity.getReportType());
        request.addDataParam(AmazonFbaRetryInfoDBField.SYNC_DATE, DataType.STRING, entity.getSyncDate());
        request.addDataParam(AmazonFbaRetryInfoDBField.FINISH_TIME, DataType.TIMESTAMP, entity.getFinishTime());
        request.addDataParam(AmazonFbaRetryInfoDBField.STATUS, DataType.INT, entity.getStatus());
        request.addDataParam(AmazonFbaRetryInfoDBField.REQUEST_RESULT, DataType.INT, entity.getRequestResult());
        request.addDataParam(AmazonFbaRetryInfoDBField.ERROR_DETAIL, DataType.STRING, entity.getErrorDetail());
        SqlerTemplate.execute(request);
    }

    @Override
    public void saveOrUpdate(AmazonFbaRetryInfo a) {
        if (a != null ) {
            SqlerRequest request = new SqlerRequest("saveOrUpdateAmazonFbaRetryInfo");
            StringBuffer insertSql = new StringBuffer();
            StringBuffer updateSql = new StringBuffer();

                insertSql.append("('").append(a.getAccountNumber()).append("','").append(a.getAccountDetail())
                        .append("',").append(a.getStatus()).append(",'").append(a.getCreateTime()).append("','").append(a.getReportType()).append("','").append(a.getSyncDate())
                        .append("','").append(a.getStartTime()).append("','").append(a.getEndTime())
                        .append("')");
            request.addSqlDataParam("INSERT_SQL", insertSql.toString());

            updateSql.append(AmazonFbaRetryInfoDBField.UPDATE_TIME).append("='").append(a.getUpdateTime()).append("',").append(AmazonFbaRetryInfoDBField.STATUS).append("=").append(a.getStatus());
            request.addSqlDataParam("UPDATE_SQL",updateSql.toString());
            SqlerTemplate.execute(request);
        }
    }

    @Override
    public void updateAmazonFbaRetryInfoStatus(AmazonFbaRetryInfo entity) {
        SqlerRequest request = new SqlerRequest("updateAmazonFbaRetryInfoStatus");
        request.addDataParam(AmazonFbaRetryInfoDBField.ID, DataType.INT, entity.getId());
        request.addDataParam(AmazonFbaRetryInfoDBField.STATUS, DataType.INT, entity.getStatus());
        request.addDataParam(AmazonFbaRetryInfoDBField.UPDATE_TIME, DataType.TIMESTAMP, new Timestamp(System.currentTimeMillis()));
        request.addDataParam(AmazonFbaRetryInfoDBField.REQUEST_RESULT,DataType.INT,entity.getRequestResult());
        SqlerTemplate.execute(request);
    }

    @Override
    public void batchCreateAmazonFbaRetryInfo(List<AmazonFbaRetryInfo> entityList) {
        if (entityList != null && !entityList.isEmpty()) {
            SqlerRequest request = new SqlerRequest("createAmazonFbaRetryInfo");
            for (AmazonFbaRetryInfo entity : entityList) {
                request.addBatchDataParam(AmazonFbaRetryInfoDBField.ACCOUNT_NUMBER, DataType.STRING, entity.getAccountNumber());
                request.addBatchDataParam(AmazonFbaRetryInfoDBField.ACCOUNT_DETAIL, DataType.STRING, entity.getAccountDetail());
                request.addBatchDataParam(AmazonFbaRetryInfoDBField.STATUS, DataType.OBJECT, entity.getStatus());
                request.addBatchDataParam(AmazonFbaRetryInfoDBField.CREATE_TIME, DataType.TIMESTAMP, entity.getCreateTime());
                request.addBatchDataParam(AmazonFbaRetryInfoDBField.REPORT_TYPE, DataType.STRING, entity.getReportType());
                request.addBatchDataParam(AmazonFbaRetryInfoDBField.SYNC_DATE, DataType.STRING, entity.getSyncDate());
                request.addBatch();
            }
            SqlerTemplate.batchUpdate(request);
        }
    }

    @Override
    public void batchUpdateAmazonFbaRetryInfo(List<AmazonFbaRetryInfo> entityList) {
        if (entityList != null && !entityList.isEmpty()) {
            SqlerRequest request = new SqlerRequest("updateAmazonFbaRetryInfoByPrimaryKey");
            for (AmazonFbaRetryInfo entity : entityList) {
                request.addBatchDataParam(AmazonFbaRetryInfoDBField.ID, DataType.INT, entity.getId());
                
                request.addBatchDataParam(AmazonFbaRetryInfoDBField.ACCOUNT_NUMBER, DataType.STRING, entity.getAccountNumber());
                request.addBatchDataParam(AmazonFbaRetryInfoDBField.ACCOUNT_DETAIL, DataType.STRING, entity.getAccountDetail());
                request.addBatchDataParam(AmazonFbaRetryInfoDBField.STATUS, DataType.INT, entity.getStatus());
                request.addBatchDataParam(AmazonFbaRetryInfoDBField.CREATE_TIME, DataType.TIMESTAMP, entity.getCreateTime());
                request.addBatchDataParam(AmazonFbaRetryInfoDBField.REPORT_TYPE, DataType.STRING, entity.getReportType());
                request.addBatchDataParam(AmazonFbaRetryInfoDBField.SYNC_DATE, DataType.STRING, entity.getSyncDate());
                request.addBatch();
            }
            SqlerTemplate.batchUpdate(request);
        }
    }

    @Override
    public void deleteAmazonFbaRetryInfo(Integer primaryKey) {
        SqlerRequest request = new SqlerRequest("deleteAmazonFbaRetryInfoByPrimaryKey");
        request.addDataParam(AmazonFbaRetryInfoDBField.ID, DataType.INT, primaryKey);
        SqlerTemplate.execute(request);
    }
}