package com.estone.statistics.dao.impl;

import java.util.List;
import java.util.Objects;

import com.estone.common.util.CommonUtils;
import com.estone.statistics.dao.mapper.SmtWarehouseInventoryDBField;
import com.estone.statistics.enums.SmtComparType;
import com.estone.transfer.dao.mapper.WhFbaAllocationItemDBField;
import com.whq.tool.sqler.dialect.DialectFactory;
import com.whq.tool.sqler.dialect.SQLDialect;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Repository;
import org.springframework.util.Assert;

import com.estone.common.util.SqlerTemplate;
import com.estone.statistics.bean.SmtOrderComparison;
import com.estone.statistics.bean.SmtOrderComparisonQueryCondition;
import com.estone.statistics.dao.SmtOrderComparisonDao;
import com.estone.statistics.dao.mapper.SmtOrderComparisonDBField;
import com.estone.statistics.dao.mapper.SmtOrderComparisonDetailMapper;
import com.estone.statistics.dao.mapper.SmtOrderComparisonMapper;
import com.whq.tool.component.Pager;
import com.whq.tool.sqler.SqlerRequest;
import com.whq.tool.sqler.analyzer.DataType;

@Repository("smtOrderComparisonDao")
public class SmtOrderComparisonDaoImpl implements SmtOrderComparisonDao {

    private void setQueryCondition(SqlerRequest request, SmtOrderComparisonQueryCondition query) {
        if (query == null) {
            return;
        }
        // 添加只读权限
        if (query.getReadOnly() != null && query.getReadOnly())
        {
            request.setReadOnly(true);
        }
        // 添加查询条件
        // 搜索条件不允许出现空字符
        request.setAllowBlank(false);
        
        request.addDataParam(SmtOrderComparisonDBField.ID, DataType.INT, query.getId());
        request.addDataParam(SmtOrderComparisonDBField.COMPAR_TYPE, DataType.INT, query.getComparType());

        if (CollectionUtils.isNotEmpty(query.getIdList())){
            request.addDataParam("idList", DataType.INT, query.getIdList());
        }

        //1-匹配的数据，2-未匹配的系统订单，3-未匹配的平台流水
        if (StringUtils.isNotBlank(query.getMatchType())){
            if ("1".equals(query.getMatchType())){
                request.addSqlDataParam("ORDER_MATCH"," AND order_id IS NOT NULL AND inventory_id IS NOT NULL ");
            }else if("2".equals(query.getMatchType())){
                request.addSqlDataParam("ORDER_MATCH"," AND order_id IS NOT NULL AND inventory_id IS NULL ");
            }else if("3".equals(query.getMatchType())){
                request.addSqlDataParam("ORDER_MATCH"," AND order_id IS NULL AND inventory_id IS NOT NULL ");
            }
        }

        if (StringUtils.isNotBlank(query.getStore())){
            if (query.getStore().contains(",")){
                request.addDataParam("storeList", DataType.STRING, CommonUtils.splitList(query.getStore(), ","));
            }else{
                request.addDataParam(SmtWarehouseInventoryDBField.STORE, DataType.STRING, query.getStore());
            }
        }

        if (CollectionUtils.isNotEmpty(query.getOrderIdList())){
            request.addDataParam("orderIdList", DataType.INT, query.getOrderIdList());
        }

        if (CollectionUtils.isNotEmpty(query.getInventoryIdList())){
            request.addDataParam("inventoryIdList", DataType.INT, query.getInventoryIdList());
        }

        if (StringUtils.isNotBlank(query.getAccountNumber())){
            String queryPrefix = "";
            if ("2".equals(query.getMatchType())){
                queryPrefix = "order_";
            }
            if (query.getAccountNumber().contains(",")){
                request.addDataParam(queryPrefix + "accountNumberList", DataType.STRING, CommonUtils.splitList(query.getAccountNumber(), ","));
            }else{
                request.addDataParam(queryPrefix + SmtWarehouseInventoryDBField.ACCOUNT_NUMBER, DataType.STRING, query.getAccountNumber());
            }
        }

        if (StringUtils.isNotBlank(query.getSku())){
            String queryPrefix = "";
            if ("2".equals(query.getMatchType())){
                queryPrefix = "order_";
            }
            if (query.getSku().contains(",")){
                request.addDataParam(queryPrefix + "skuList", DataType.STRING, CommonUtils.splitList(query.getSku(), ","));
            }else{
                request.addDataParam(queryPrefix + WhFbaAllocationItemDBField.PRODUCT_SKU, DataType.STRING, query.getSku());
            }
        }

        if (StringUtils.isNotBlank(query.getLbx())){
            if (query.getLbx().contains(",")){
                request.addDataParam("lbxList", DataType.STRING, CommonUtils.splitList(query.getLbx(), ","));
            }else{
                request.addDataParam(WhFbaAllocationItemDBField.TEMU_TAG_URL, DataType.STRING, query.getLbx());
            }
        }

        if(StringUtils.isNotBlank(query.getProductId())){
            if (query.getProductId().contains(",")){
                request.addDataParam("productIdList", DataType.STRING, CommonUtils.splitList(query.getProductId(), ","));
            }else{
                request.addDataParam(SmtWarehouseInventoryDBField.PRODUCT_ID, DataType.STRING, query.getProductId());
            }
        }

        if (StringUtils.isNotBlank(query.getScItemCode())){
            if (query.getScItemCode().contains(",")){
                request.addDataParam("scItemCodeList", DataType.STRING, CommonUtils.splitList(query.getScItemCode(), ","));
            }else{
                request.addDataParam(SmtWarehouseInventoryDBField.SCITEM_CODE, DataType.STRING, query.getScItemCode());
            }
        }

        if (StringUtils.isNotBlank(query.getPlatformOrderId())) {
            if (query.getPlatformOrderId().contains(",")) {
                request.addDataParam("platformOrderIdList", DataType.STRING,
                        CommonUtils.splitList(query.getPlatformOrderId(), ","));
            }
            else {
                request.addDataParam("platformOrderId", DataType.STRING, query.getPlatformOrderId());
            }
        }

        if(StringUtils.isNotBlank(query.getFromDeliveryDate())){
            request.addDataParam("fromDeliveryDate", DataType.STRING, query.getFromDeliveryDate());
        }
        if (StringUtils.isNotBlank(query.getToDeliveryDate())){
            request.addDataParam("toDeliveryDate", DataType.STRING, query.getToDeliveryDate());
        }
        if (StringUtils.isNotBlank(query.getFromGmtDate())){
            request.addDataParam("fromGmtDate", DataType.STRING, query.getFromGmtDate());
        }
        if (StringUtils.isNotBlank(query.getToGmtDate())){
            request.addDataParam("toGmtDate", DataType.STRING, query.getToGmtDate());
        }
        if (Objects.nonNull(query.getHasDiscrepancy())){
            request.addDataParam("hasDiscrepancy", DataType.BOOLEAN, query.getHasDiscrepancy());
        }
    }

    @Override
    public int querySmtOrderComparisonCount(SmtOrderComparisonQueryCondition query) {
        SqlerRequest request = new SqlerRequest("querySmtOrderComparisonCount");
        setQueryCondition(request, query);
        return SqlerTemplate.queryForInt(request);
    }

    @Override
    public List<SmtOrderComparison> querySmtOrderComparisonList() {
        SqlerRequest request = new SqlerRequest("querySmtOrderComparisonList");
        return SqlerTemplate.query(request, new SmtOrderComparisonMapper());
    }

    @Override
    public List<SmtOrderComparison> querySmtOrderComparisonList(SmtOrderComparisonQueryCondition query, Pager pager) {
        SqlerRequest request = new SqlerRequest("querySmtOrderComparisonList");
        setQueryCondition(request, query);
        if(pager != null) {
            request.addFetch(pager.getPageNo(), pager.getPageSize());
        }
        return SqlerTemplate.query(request, new SmtOrderComparisonMapper());
    }

    @Override
    public SmtOrderComparison querySmtOrderComparison(Integer primaryKey) {
        Assert.notNull(primaryKey, "primaryKey is null!");
        SqlerRequest request = new SqlerRequest("querySmtOrderComparisonByPrimaryKey");
        request.addDataParam(SmtOrderComparisonDBField.ID, DataType.INT, primaryKey);
        return SqlerTemplate.queryForObject(request, new SmtOrderComparisonMapper());
    }

    @Override
    public SmtOrderComparison querySmtOrderComparison(SmtOrderComparisonQueryCondition query) {
        SqlerRequest request = new SqlerRequest("querySmtOrderComparison");
        setQueryCondition(request, query);
        return SqlerTemplate.queryForObject(request, new SmtOrderComparisonMapper());
    }

    @Override
    public void createSmtOrderComparison(SmtOrderComparison entity) {
        SqlerRequest request = new SqlerRequest("createSmtOrderComparison");
        request.addDataParam(SmtOrderComparisonDBField.COMPAR_TYPE, DataType.INT, entity.getComparType());
        request.addDataParam(SmtOrderComparisonDBField.ORDER_TYPE, DataType.STRING, entity.getOrderType());
        request.addDataParam(SmtOrderComparisonDBField.ORDER_ID, DataType.INT, entity.getOrderId());
        request.addDataParam(SmtOrderComparisonDBField.INVENTORY_ID, DataType.INT, entity.getInventoryId());
        request.addDataParam(SmtOrderComparisonDBField.INVENTORY_QTY, DataType.INT, entity.getInventoryQty());
        request.addDataParam(SmtOrderComparisonDBField.ORDER_QTY, DataType.INT, entity.getOrderQty());
        request.addDataParam(SmtOrderComparisonDBField.CREATE_TIME, DataType.STRING, entity.getCreateTime());
        Integer autoIncrementId = SqlerTemplate.executeAndReturn(request);
        entity.setId(autoIncrementId);
    }

    @Override
    public void updateSmtOrderComparison(SmtOrderComparison entity) {
        SqlerRequest request = new SqlerRequest("updateSmtOrderComparisonByPrimaryKey");
        request.addDataParam(SmtOrderComparisonDBField.ID, DataType.INT, entity.getId());
        
        request.addDataParam(SmtOrderComparisonDBField.COMPAR_TYPE, DataType.INT, entity.getComparType());
        request.addDataParam(SmtOrderComparisonDBField.ORDER_TYPE, DataType.STRING, entity.getOrderType());
        request.addDataParam(SmtOrderComparisonDBField.ORDER_ID, DataType.INT, entity.getOrderId());
        request.addDataParam(SmtOrderComparisonDBField.INVENTORY_ID, DataType.INT, entity.getInventoryId());
        request.addDataParam(SmtOrderComparisonDBField.INVENTORY_QTY, DataType.INT, entity.getInventoryQty());
        request.addDataParam(SmtOrderComparisonDBField.ORDER_QTY, DataType.INT, entity.getOrderQty());
        request.addDataParam(SmtOrderComparisonDBField.CREATE_TIME, DataType.STRING, entity.getCreateTime());
        SqlerTemplate.execute(request);
    }

    @Override
    public int querySmtOrderComparisonAndOrderCount(SmtOrderComparisonQueryCondition query) {
        SqlerRequest request = new SqlerRequest("querySmtOrderComparisonAndPrepareOrderCount");
        if (SmtComparType.RETURN_ORDER.intCode().equals(query.getComparType())) {
            request = new SqlerRequest("querySmtReturnComparisonCount");
        }
        else if (SmtComparType.SHIPMENTS_ORDER.intCode().equals(query.getComparType())) {
            request = new SqlerRequest("querySmtOmsOrderComparisonCount");
        }
        boolean querySku = "3".equals(query.getMatchType());
        if (querySku) {
            request = new SqlerRequest("queryComparisonNotMatchInventoryCount");
        }
        setQueryCondition(request, query);
        return SqlerTemplate.queryForInt(request);
    }

    @Override
    public List<SmtOrderComparison> querySmtOrderComparisonAndOrderList(SmtOrderComparisonQueryCondition query, Pager pager) {
        SqlerRequest request = new SqlerRequest("querySmtOrderComparisonAndPrepareOrderList");
        if (SmtComparType.RETURN_ORDER.intCode().equals(query.getComparType())) {
            request = new SqlerRequest("querySmtReturnComparisonList");
        }
        else if (SmtComparType.SHIPMENTS_ORDER.intCode().equals(query.getComparType())) {
            request = new SqlerRequest("querySmtOmsOrderComparisonList");
        }
        boolean querySku = "3".equals(query.getMatchType());
        if (querySku) {
            request = new SqlerRequest("queryComparisonNotMatchInventoryList");
        }
        setQueryCondition(request, query);
        if (pager != null) {
            if (querySku) {
                SQLDialect dial = DialectFactory.createDialect(null);

                long start = (long) (pager.getPageNo() - 1) * pager.getPageSize();

                long end = start + pager.getPageSize() - 1L;

                request.addSqlDataParam("LIMIT", dial.queryFirst2Last("", (int) start, (int) end));
            }
            else {
                request.addFetch(pager.getPageNo(), pager.getPageSize());

            }
        }
        return SqlerTemplate.query(request, new SmtOrderComparisonDetailMapper(query.getComparType(),querySku));
    }

    @Override
    public void batchCreateSmtOrderComparison(List<SmtOrderComparison> entityList) {
        if (entityList != null && !entityList.isEmpty()) {
            SqlerRequest request = new SqlerRequest("createSmtOrderComparison");
            for (SmtOrderComparison entity : entityList) {
                request.addBatchDataParam(SmtOrderComparisonDBField.COMPAR_TYPE, DataType.INT, entity.getComparType());
                request.addBatchDataParam(SmtOrderComparisonDBField.ORDER_TYPE, DataType.STRING, entity.getOrderType());
                request.addBatchDataParam(SmtOrderComparisonDBField.ORDER_ID, DataType.INT, entity.getOrderId());
                request.addBatchDataParam(SmtOrderComparisonDBField.INVENTORY_ID, DataType.INT, entity.getInventoryId());
                request.addBatchDataParam(SmtOrderComparisonDBField.INVENTORY_QTY, DataType.INT, entity.getInventoryQty());
                request.addBatchDataParam(SmtOrderComparisonDBField.ORDER_QTY, DataType.INT, entity.getOrderQty());
                request.addBatchDataParam(SmtOrderComparisonDBField.CREATE_TIME, DataType.STRING, entity.getCreateTime());
                request.addBatch();
            }
            SqlerTemplate.batchUpdate(request);
        }
    }

    @Override
    public void batchUpdateSmtOrderComparison(List<SmtOrderComparison> entityList) {
        if (entityList != null && !entityList.isEmpty()) {
            SqlerRequest request = new SqlerRequest("updateSmtOrderComparisonByPrimaryKey");
            for (SmtOrderComparison entity : entityList) {
                request.addBatchDataParam(SmtOrderComparisonDBField.ID, DataType.INT, entity.getId());
                
                request.addBatchDataParam(SmtOrderComparisonDBField.COMPAR_TYPE, DataType.INT, entity.getComparType());
                request.addBatchDataParam(SmtOrderComparisonDBField.ORDER_TYPE, DataType.STRING, entity.getOrderType());
                request.addBatchDataParam(SmtOrderComparisonDBField.ORDER_ID, DataType.INT, entity.getOrderId());
                request.addBatchDataParam(SmtOrderComparisonDBField.INVENTORY_ID, DataType.INT, entity.getInventoryId());
                request.addBatchDataParam(SmtOrderComparisonDBField.INVENTORY_QTY, DataType.INT, entity.getInventoryQty());
                request.addBatchDataParam(SmtOrderComparisonDBField.ORDER_QTY, DataType.INT, entity.getOrderQty());
                request.addBatchDataParam(SmtOrderComparisonDBField.CREATE_TIME, DataType.STRING, entity.getCreateTime());
                request.addBatch();
            }
            SqlerTemplate.batchUpdate(request);
        }
    }

    @Override
    public void deleteSmtOrderComparison(Integer primaryKey) {
        SqlerRequest request = new SqlerRequest("deleteSmtOrderComparisonByPrimaryKey");
        request.addDataParam(SmtOrderComparisonDBField.ID, DataType.INT, primaryKey);
        SqlerTemplate.execute(request);
    }

    @Override
    public void deleteByOrderIds(Integer compareType, List<Integer> orderIds) {
        if (compareType == null || CollectionUtils.isEmpty(orderIds)) {
            return;
        }
        SqlerRequest request = new SqlerRequest("deleteByOrderIds");
        request.addDataParam(SmtOrderComparisonDBField.COMPAR_TYPE, DataType.INT, compareType);
        request.addDataParam("orderIdList", DataType.INT, orderIds);
        SqlerTemplate.execute(request);
    }
}