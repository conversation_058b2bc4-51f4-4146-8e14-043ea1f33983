package com.estone.statistics.dao.impl;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.*;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import com.taobao.pac.sdk.cp.dataobject.request.MODUAN_ORDER_CREATE.request;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.stereotype.Repository;

import com.alibaba.fastjson.JSON;
import com.estone.apv.common.ApvStatus;
import com.estone.asn.enums.AsnPackageMethodEnum;
import com.estone.checkin.enums.CheckInWhType;
import com.estone.common.SaleChannel;
import com.estone.common.util.CacheUtils;
import com.estone.common.util.SqlerTemplate;
import com.estone.picking.enums.AllocationPickingTaskStatus;
import com.estone.statistics.bean.ArossDemandApvItem;
import com.estone.statistics.bean.ArossPickCheckoutStatistics;
import com.estone.statistics.bean.CheckoutStatistics;
import com.estone.statistics.bean.CheckoutStatisticsQueryCondition;
import com.estone.statistics.dao.CheckoutStatisticsDao;
import com.estone.statistics.dao.mapper.*;
import com.estone.statistics.enums.CheckoutStatisticsEnum;
import com.estone.statistics.service.CheckoutStatisticsService;
import com.estone.temu.enums.TemuPackageStatus;
import com.estone.transfer.enums.AsnPrepareStatus;
import com.whq.tool.sqler.SqlerRequest;
import com.whq.tool.sqler.analyzer.DataType;

@Repository("checkoutStatisticsDao")
public class CheckoutStatisticsDaoImpl implements CheckoutStatisticsDao {

    @Resource
    @Lazy
    private CheckoutStatisticsService checkoutStatisticsService;

    private void setQueryCondition(SqlerRequest request, CheckoutStatisticsQueryCondition query) {
        if (query == null) {
            return;
        }
        if (Objects.nonNull(query.getReadOnly()) && query.getReadOnly()){
            request.setReadOnly(true);
        }
        // 搜索条件不允许出现空字符
        request.setAllowBlank(false);

        /** 统计的发货单状态 */
        List<Integer> apvStatusList = new ArrayList<Integer>();
        apvStatusList.add(ApvStatus.DELIVER.intCode());
        apvStatusList.add(ApvStatus.LOADED.intCode());
        if (query.getIsDirect()!=null && query.getIsDirect()) {
            apvStatusList.add(ApvStatus.WAITING_DELIVER.intCode());
            request.addSqlDataParam("APV_ORDER_TYPE","AND apv.ship_status = 21 ");
        }
        else{
            request.addSqlDataParam("APV_ORDER_TYPE","AND apv.ship_status != 21 ");
        }
        if (query.getIsCurrentTime()) {
            apvStatusList.add(ApvStatus.ALLOT.intCode());
            apvStatusList.add(ApvStatus.SINGLETON_TOUCHING.intCode());
            apvStatusList.add(ApvStatus.EXCESSIVE_PARTS_TOUCHING.intCode());
            apvStatusList.add(ApvStatus.MULTI_TOUCHING.intCode());
            apvStatusList.add(ApvStatus.CHECK_PRINT.intCode());
            apvStatusList.add(ApvStatus.WAITING_DELIVER.intCode());
        }
        request.addDataParam("apv_status_condition", DataType.INT, apvStatusList);
        if (CheckInWhType.TEMU.intCode().equals(query.getType())){
            request.addDataParam("apv_status_condition", DataType.INT, Arrays.asList(
                    TemuPackageStatus.LOADED.intCode(),
                    TemuPackageStatus.DELIVER.intCode(),
                    TemuPackageStatus.WAITING_DELIVER.intCode(),
                    TemuPackageStatus.WAIT_SORTING.intCode(),
                    TemuPackageStatus.WAIT_CHECK.intCode(),
                    TemuPackageStatus.WAITING_GRID.intCode(),
                    TemuPackageStatus.PICKING.intCode(),
                    TemuPackageStatus.MERGED.intCode()));
        }


        /** 用户 */
        if (StringUtils.isNotBlank(query.getUserIds())) {
            List<Integer> userIdList = new ArrayList<Integer>();
            String[] ids = query.getUserIds().split(",");
            for (String userId : ids) {
                userIdList.add(Integer.valueOf(userId));
            }
            request.addDataParam("user_id_list", DataType.INT, userIdList);
        }

        /** 合单时间 */
        request.addDataParam("start_merge_time", DataType.STRING, query.getStartMergeTime());
        request.addDataParam("end_merge_time", DataType.STRING, query.getEndMergeTime());

        /** 拣货时间 */
        request.addDataParam("start_pick_time", DataType.STRING, query.getStartPickTime());
        request.addDataParam("end_pick_time", DataType.STRING, query.getEndPickTime());

        /** 播种时间 */
        request.addDataParam("start_soft_time", DataType.STRING, query.getStartSoftTime());
        request.addDataParam("end_soft_time", DataType.STRING, query.getEndSoftTime());

        /** 包装时间 */
        request.addDataParam("start_packing_time", DataType.STRING, query.getStartPackingTime());
        request.addDataParam("end_packing_time", DataType.STRING, query.getEndPackingTime());

        /** 扫描时间 */
        request.addDataParam("start_scan_time", DataType.STRING, query.getStartScanTime());
        request.addDataParam("end_scan_time", DataType.STRING, query.getEndScanTime());

        /** 装车时间 */
        request.addDataParam("start_loading_time", DataType.STRING, query.getStartLoadingTime());
        request.addDataParam("end_loading_time", DataType.STRING, query.getEndLoadingTime());

        /** 操作时间 */
        request.addDataParam("start_handle_time", DataType.STRING, query.getStartHandleTime());
        request.addDataParam("end_handle_time", DataType.STRING, query.getEndHandleTime());

        /** SKU仓库属性 */
        request.addDataParam("sku_warehouse_id", DataType.INT, CacheUtils.getLocalWarehouseId());


        String transferOrderTypes = "('"+SaleChannel.CHANNEL_SHEIN+"',"+ AsnPackageMethodEnum.URGENT.getCode()+"),"
                + "('"+SaleChannel.CHANNEL_SHEIN+"',"+ AsnPackageMethodEnum.BACKUP.getCode()+")";
        List<String> transferOrderChannels = Arrays.asList(SaleChannel.CHANNEL_SMT,SaleChannel.CHANNEL_SHEIN);
        request.addDataParam("purposeHouseList", DataType.STRING, transferOrderChannels);
        request.addSqlDataParam("TRANSFER_ORDER_FILTER"," AND fa.id IN (SELECT wh_asn_id FROM wh_asn_extra ae WHERE (fa.purpose_house,ae.package_method) NOT IN ("+transferOrderTypes+") " +
                " OR ae.package_method IS NULL ) AND CASE WHEN fa.purpose_house='"+SaleChannel.CHANNEL_SMT +"' THEN fa.is_asn IS NULL ELSE 1=1 END");
    }

    @Override
    public List<CheckoutStatistics> queryMergeCheckoutStatistics(CheckoutStatisticsQueryCondition query,
            boolean isAll) {
        SqlerRequest request = new SqlerRequest("queryMergeCheckoutStatisticsList");
        setQueryCondition(request, query);
        if (isAll) {
            String dateFormat = query.getDateFormat();
            if (StringUtils.isBlank(dateFormat)) {// 出库总作业报表
                request.addSqlDataParam("merge_date", ", DATE_FORMAT(pick.created_date, '%Y-%m-%d') AS mergeDate");
                request.addSqlDataParam("group_by_merge_date", "GROUP BY pick.create_by, mergeDate");
            }
            else {// 出库看板
                request.addSqlDataParam("merge_date",
                        ", DATE_FORMAT(pick.created_date, '" + dateFormat + "') AS mergeDate");
                request.addSqlDataParam("group_by_merge_date", "GROUP BY mergeDate");
            }
        }
        else {// 出库作业报表
            request.addSqlDataParam("group_by_merge_date", "GROUP BY pick.create_by");
        }
        return SqlerTemplate.query(request, new CheckoutStatisticsMapper(CheckoutStatisticsEnum.MERGE, isAll));
    }

    @Override
    public List<CheckoutStatistics> queryPickCheckoutStatistics(CheckoutStatisticsQueryCondition query, boolean isAll) {
        SqlerRequest request = new SqlerRequest("queryPickCheckoutStatisticsListByTaskType");
        setQueryCondition(request, query);
        String dateFormat = query.getDateFormat();
        if (isAll) {
            if (StringUtils.isBlank(dateFormat)) {// 出库总作业报表
                request.addSqlDataParam("pick_date", ", DATE_FORMAT(pick.picking_end_date, '%Y-%m-%d') AS pickDate");
                request.addSqlDataParam("group_by_pick_date", "GROUP BY pick.receive_person, pickDate");
            }
            else {// 出库看板
                request.addSqlDataParam("pick_date",
                        ", DATE_FORMAT(pick.picking_end_date, '" + dateFormat + "') AS pickDate");
                request.addSqlDataParam("group_by_pick_date", "GROUP BY pickDate");
            }
        }
        else {// 出库作业报表
            request.addSqlDataParam("group_by_pick_date", "GROUP BY pick.receive_person");
        }
        if (Integer.valueOf(1).equals(CacheUtils.getLocalWarehouseId())) {
            // 汉海达加条件，只对多品多件筛选条件
            request.addDataParam("pick_warehouse_id", DataType.INT, CacheUtils.getLocalWarehouseId());
        }

        /** 本仓拣货数据 */
        List<CheckoutStatistics> localStatisticsList = SqlerTemplate.query(request,
                new CheckoutStatisticsMapper(CheckoutStatisticsEnum.PICK, isAll));

        // 出库看板数据
        if (isAll && StringUtils.isNotBlank(dateFormat)) {
            return localStatisticsList;
        }

        /** FBA拣货*/
        List<CheckoutStatistics> fbaStatisticsList = queryPickCheckoutStatisticsFromFba(query, isAll);
        List<CheckoutStatistics> pickList = handlePickData(localStatisticsList, fbaStatisticsList, isAll);
        List<CheckoutStatistics> pickLendList = queryPickLendStatistics(query, isAll);
        List<CheckoutStatistics> mergePickList = mergePickQuantity(pickLendList,pickList);
        List<CheckoutStatistics> pickSpoList = queryPickSpoStatistics(query, isAll);
        List<CheckoutStatistics> pickSpoStatistics = mergeSpoPickQuantity(pickSpoList, mergePickList);
        /**拼多多*/
        List<CheckoutStatistics> temuStatisticsList = queryPickCheckoutStatisticsFromTemu(query, isAll);
        List<CheckoutStatistics> checkoutStatistics = mergeTemuPickQuantity(temuStatisticsList, pickSpoStatistics);
        //添加不入库直发统计方法
        List<CheckoutStatistics> saleDirectStatisticsList = querySaleDirectCheckoutStatistics(query, isAll);
        List<CheckoutStatistics> allCheckoutStatistics = mergeSaleDirectQuantity(saleDirectStatisticsList, checkoutStatistics);
        //调拨
        List<CheckoutStatistics> allocationStatisticsList = queryAllocationPickCheckoutStatisticsList(query, isAll);
        return handlePickAllocationData(allCheckoutStatistics,allocationStatisticsList);

    }

    // 调拨拣货数据合并
    public List<CheckoutStatistics> handlePickAllocationData(List<CheckoutStatistics> list, List<CheckoutStatistics> allocationList) {
        if (CollectionUtils.isEmpty(allocationList)) {
            return list;
        }
        if (CollectionUtils.isEmpty(list)) {
            return allocationList;
        }
        Map<String, CheckoutStatistics> map = list.stream()
                .collect(Collectors.toMap(s -> s.getUserId() + s.getPickDate(), item -> item));
        Set<String> skuSet = map.keySet();
        allocationList.forEach(s -> {
            if (skuSet.contains(s.getUserId() + s.getPickDate())) {
                CheckoutStatistics rfoPick = map.get(s.getUserId() + s.getPickDate());
                rfoPick.setAllocationSkuCount(rfoPick.getAllocationSkuCount() == null ? 0 : rfoPick.getAllocationSkuCount()
                        + s.getAllocationSkuCount());
                rfoPick.setAllocationPcsCount(rfoPick.getAllocationPcsCount() == null ? 0 : rfoPick.getAllocationPcsCount()
                        + s.getAllocationPcsCount());
            } else {
                map.put(s.getUserId() + s.getPickDate(), s);
            }
        });
        return new ArrayList<>(map.values());
    }

    // 调拨拣货
    private List<CheckoutStatistics> queryAllocationPickCheckoutStatisticsList(CheckoutStatisticsQueryCondition query, boolean isAll) {
        SqlerRequest request = new SqlerRequest("queryAllocationPickCheckoutStatisticsList");
        setQueryCondition(request, query);
        String dateFormat = query.getDateFormat();
        if (isAll) {
            if (StringUtils.isBlank(dateFormat)) {// 出库总作业报表
                request.addSqlDataParam("pick_date", ", DATE_FORMAT(pick_task.update_time, '%Y-%m-%d') AS pickDate");
                request.addSqlDataParam("group_by_pick_date", "GROUP BY userId, pickDate,pick_task.task_no");
            }
            else {// 出库看板
                request.addSqlDataParam("pick_date",
                        ", DATE_FORMAT(pick_task.update_time, '" + dateFormat + "') AS pickDate");
                request.addSqlDataParam("group_by_pick_date", "GROUP BY pickDate");
            }
        }
        else {// 出库作业报表
            request.addSqlDataParam("group_by_pick_date", "GROUP BY userId,pick_task.task_no");
        }
        request.addDataParam("status", DataType.INT, AllocationPickingTaskStatus.COMPLETED.intCode());
        List<CheckoutStatistics> pickList = SqlerTemplate.query(request,
                (rs, rowNum) -> {
                    CheckoutStatistics entity = new CheckoutStatistics();
                    entity.setUserId(rs.getObject("userId") == null ? null : rs.getInt("userId"));
                    entity.setUserName(rs.getString("userName"));
                    entity.setName(rs.getString("name"));
                    entity.setAllocationSkuCount(rs.getLong("allocationSkuCount"));
                    entity.setAllocationPcsCount(rs.getLong("allocationPcsCount"));
                    if (isAll) {
                        entity.setPickDate(rs.getString("pickDate"));
                    }
                    return entity;
                });

        return pickList;
    }

    private List<CheckoutStatistics> querySaleDirectCheckoutStatistics(CheckoutStatisticsQueryCondition query, boolean isAll) {
        SqlerRequest request = new SqlerRequest("querySaleDirectCheckoutStatisticsList");
        setQueryCondition(request, query);
        String dateFormat = query.getDateFormat();
        if (isAll) {
            if (StringUtils.isBlank(dateFormat)) {// 出库总作业报表
                request.addSqlDataParam("OBTAIN_TIME", ", DATE_FORMAT(wc.obtain_time, '%Y-%m-%d') AS obtainTime");
                request.addSqlDataParam("group_by_obtain_time", "GROUP BY wc.obtain_user, obtainTime");
            }
            else {// 出库看板
                request.addSqlDataParam("OBTAIN_TIME",
                        ", DATE_FORMAT(wc.obtain_time, '" + dateFormat + "') AS obtainTime");
                request.addSqlDataParam("group_by_obtain_time", "GROUP BY obtainTime");
            }
        }
        else {// 出库作业报表
            request.addSqlDataParam("group_by_obtain_time", "GROUP BY wc.obtain_user");
        }
        request.addSqlDataParam("FILED_NAME",",COUNT(DISTINCT CONCAT(item.sku)) AS saleDirectSkuCount" +
                ", SUM( IFNULL(item.pick_qty,0))  AS saleDirectPcsCount");
        request.addSqlDataParam("TABLE_NAME"," LEFT JOIN wh_check_in_item item ON wc.in_id = item.in_id");
        List<CheckoutStatistics> pickList = SqlerTemplate.query(request,
                (rs, rowNum) -> {
                    CheckoutStatistics entity = new CheckoutStatistics();
                    entity.setUserId(rs.getObject("userId") == null ? null : rs.getInt("userId"));
                    entity.setUserName(rs.getString("userName"));
                    entity.setName(rs.getString("name"));
                    entity.setSaleDirectSkuCount(rs.getLong("saleDirectSkuCount"));
                    entity.setSaleDirectPcsCount(rs.getLong("saleDirectPcsCount"));
                    if (isAll) {
                        entity.setPickDate(rs.getString("obtainTime"));
                    }
                    return entity;
                });
        Map<String, CheckoutStatistics> temuSkuCountMap = null;
        if (isAll) {
            temuSkuCountMap = CollectionUtils.isEmpty(pickList) ? new HashMap<>()
                    : pickList.stream()
                    .collect(Collectors.toMap(item -> item.getUserId() + item.getPickDate(), item -> item));
        }else {
            temuSkuCountMap = CollectionUtils.isEmpty(pickList) ? new HashMap<>()
                    : pickList.stream().collect(Collectors.toMap(item -> item.getUserId().toString(), item -> item));
        }
        request.addSqlDataParam("FILED_NAME"," , COUNT(DISTINCT item.in_id) AS saleDirectCount");
        request.addSqlDataParam("TABLE_NAME","  LEFT JOIN wh_check_in_item item ON wc.in_id = item.in_id");
        Map<String, CheckoutStatistics> finalTemuSkuCountMap = temuSkuCountMap;
        return SqlerTemplate.query(request,
                (rs, rowNum) -> {
                    Integer userId = (rs.getObject("userId") == null) ? null : rs.getInt("userId");
                    CheckoutStatistics checkoutStatistics = new CheckoutStatistics();
                    if (userId != null){
                        if (isAll) {
                            String pickDate = rs.getString("obtainTime");
                            checkoutStatistics = finalTemuSkuCountMap.get(userId + pickDate);
                        }else {
                            checkoutStatistics = finalTemuSkuCountMap.get(userId.toString());
                        }
                        if (checkoutStatistics != null) {
                            checkoutStatistics.setSaleDirectCount(rs.getLong("saleDirectCount"));
                        }
                    }
                    return checkoutStatistics;
                });
    }

    private List<CheckoutStatistics>  mergeSaleDirectQuantity(List<CheckoutStatistics> checkoutStatistics, List<CheckoutStatistics> pickSpoStatistics) {
        if (CollectionUtils.isEmpty(checkoutStatistics)) {
            return pickSpoStatistics;
        }
        if (CollectionUtils.isEmpty(pickSpoStatistics)) {
            return checkoutStatistics;
        }
        Map<String, CheckoutStatistics> map = pickSpoStatistics.stream()
                .collect(Collectors.toMap(s -> s.getUserId() + s.getPickDate(), item -> item));
        Set<String> skuSet = map.keySet();
        checkoutStatistics.forEach(s -> {
            if (skuSet.contains(s.getUserId() + s.getPickDate())) {
                CheckoutStatistics rfoPick = map.get(s.getUserId() + s.getPickDate());
                rfoPick.setSaleDirectCount(s.getSaleDirectCount());
                rfoPick.setSaleDirectSkuCount(s.getSaleDirectSkuCount());
                rfoPick.setSaleDirectPcsCount(s.getSaleDirectPcsCount());
            }
            else {
                map.put(s.getUserId() + s.getPickDate(), s);
            }
        });
        return new ArrayList<>(map.values());

    }


    // temu
    private List<CheckoutStatistics> queryPickCheckoutStatisticsFromTemu(CheckoutStatisticsQueryCondition query, boolean isAll) {
        SqlerRequest request = new SqlerRequest("queryPickCheckoutStatisticsByTemu");
        setQueryCondition(request, query);
        String dateFormat = query.getDateFormat();
        if (isAll) {
            if (StringUtils.isBlank(dateFormat)) {// 出库总作业报表
                request.addSqlDataParam("pick_date", ", DATE_FORMAT(pick.picking_end_date, '%Y-%m-%d') AS pickDate");
                request.addSqlDataParam("group_by_pick_date", "GROUP BY pick.receive_person, pickDate");
            }
            else {// 出库看板
                request.addSqlDataParam("pick_date",
                        ", DATE_FORMAT(pick.picking_end_date, '" + dateFormat + "') AS pickDate");
                request.addSqlDataParam("group_by_pick_date", "GROUP BY pickDate");
            }
        }
        else {// 出库作业报表
            request.addSqlDataParam("group_by_pick_date", "GROUP BY pick.receive_person");
        }
        request.addSqlDataParam("FILED_NAME",",COUNT(DISTINCT CONCAT(pick.task_no, tpoi.sku)) AS pickTemuSkuCount" +
                ", SUM( IFNULL(tpoi.pick_quantity,0))  AS pickTemuPcsCount"+
                ", COUNT(DISTINCT IF(tpoi.real_quantity = 1,CONCAT(pick.task_no, tpoi.sku), NULL)) AS pickTemuSsSkuCount"+
                ", SUM( IF(tpoi.real_quantity = 1,IFNULL(tpoi.pick_quantity, 0),0))  AS pickTemuSsPcsCount"+
                ", COUNT(DISTINCT IF(tpoi.real_quantity > 1,CONCAT(pick.task_no, tpoi.sku), NULL)) AS pickTemuSmSkuCount"+
                ", SUM( IF(tpoi.real_quantity> 1,IFNULL(tpoi.pick_quantity,0),0))  AS pickTemuSmPcsCount");
        request.addSqlDataParam("TABLE_NAME"," LEFT JOIN wh_picking_task_item wpti ON pick.id = wpti.task_id " +
                "LEFT JOIN temu_prepare_order_item tpoi ON wpti.apv_id = tpoi.id ");
        List<CheckoutStatistics> pickList = SqlerTemplate.query(request,
                (rs, rowNum) -> {
                    CheckoutStatistics entity = new CheckoutStatistics();
                    entity.setUserId(rs.getObject("userId") == null ? null : rs.getInt("userId"));
                    entity.setUserName(rs.getString("userName"));
                    entity.setName(rs.getString("name"));
                    entity.setPickTemuSkuCount(rs.getLong("pickTemuSkuCount"));
                    entity.setPickTemuPcsCount(rs.getLong("pickTemuPcsCount"));
                    entity.setPickTemuSsSkuCount(rs.getLong("pickTemuSsSkuCount"));
                    entity.setPickTemuSsPcsCount(rs.getLong("pickTemuSsPcsCount"));
                    entity.setPickTemuSmSkuCount(rs.getLong("pickTemuSmSkuCount"));
                    entity.setPickTemuSmPcsCount(rs.getLong("pickTemuSmPcsCount"));
                    if (isAll) {
                        entity.setPickDate(rs.getString("pickDate"));
                    }
                    return entity;
                });
        Map<String, CheckoutStatistics> temuSkuCountMap = null;
        if (isAll) {
            temuSkuCountMap = CollectionUtils.isEmpty(pickList) ? new HashMap<>()
                    : pickList.stream()
                    .collect(Collectors.toMap(item -> item.getUserId() + item.getPickDate(), item -> item));
        }else {
            temuSkuCountMap = CollectionUtils.isEmpty(pickList) ? new HashMap<>()
                    : pickList.stream().collect(Collectors.toMap(item -> item.getUserId().toString(), item -> item));
        }
        request.addSqlDataParam("FILED_NAME"," , COUNT(DISTINCT wpti.apv_id) AS pickTemuCount"+
                ", COUNT(DISTINCT IF(tpoi.real_quantity = 1 " +
                "   AND (SELECT COUNT(1)>0 FROM temu_prepare_order tpo WHERE tpo.status != 20 AND tpo.id = tpoi.prepare_order_id), wpti.apv_id, NULL)) AS pickTemuSsCount"+
                ", COUNT(DISTINCT IF(tpoi.real_quantity > 1 " +
                "   AND (SELECT COUNT(1)>0 FROM temu_prepare_order tpo WHERE tpo.status != 20 AND tpo.id = tpoi.prepare_order_id), wpti.apv_id, NULL)) AS pickTemuSmCount");
        request.addSqlDataParam("TABLE_NAME"," LEFT JOIN wh_picking_task_item wpti ON pick.id = wpti.task_id "+
                " LEFT JOIN temu_prepare_order_item tpoi ON wpti.apv_id = tpoi.id ");
        Map<String, CheckoutStatistics> finalTemuSkuCountMap = temuSkuCountMap;
        return SqlerTemplate.query(request,
                (rs, rowNum) -> {
                    Integer userId = (rs.getObject("userId") == null) ? null : rs.getInt("userId");
                    CheckoutStatistics checkoutStatistics = new CheckoutStatistics();
                    if (userId != null){
                        if (isAll) {
                            String pickDate = rs.getString("pickDate");
                            checkoutStatistics = finalTemuSkuCountMap.get(userId + pickDate);
                        }else {
                            checkoutStatistics = finalTemuSkuCountMap.get(userId.toString());
                        }
                        if (checkoutStatistics != null) {
                            checkoutStatistics.setPickTemuCount(rs.getLong("pickTemuCount"));
                            checkoutStatistics.setPickTemuSsCount(rs.getLong("pickTemuSsCount"));
                            checkoutStatistics.setPickTemuSmCount(rs.getLong("pickTemuSmCount"));
                        }
                    }
                    return checkoutStatistics;
                });
    }

    public List<CheckoutStatistics> mergeTemuPickQuantity(List<CheckoutStatistics> temuStatisticsList,
                                                         List<CheckoutStatistics> pickSpoStatistics) {
        if (CollectionUtils.isEmpty(temuStatisticsList)) {
            return pickSpoStatistics;
        }
        if (CollectionUtils.isEmpty(pickSpoStatistics)) {
            return temuStatisticsList;
        }
        Map<String, CheckoutStatistics> map = pickSpoStatistics.stream()
                .collect(Collectors.toMap(s -> s.getUserId() + s.getPickDate(), item -> item));
        Set<String> skuSet = map.keySet();
        temuStatisticsList.forEach(s -> {
            if (skuSet.contains(s.getUserId() + s.getPickDate())) {
                CheckoutStatistics rfoPick = map.get(s.getUserId() + s.getPickDate());
                rfoPick.setPickTemuCount(s.getPickTemuCount());
                rfoPick.setPickTemuSkuCount(s.getPickTemuSkuCount());
                rfoPick.setPickTemuPcsCount(s.getPickTemuPcsCount());
                rfoPick.setPickTemuSsCount(s.getPickTemuSsCount());
                rfoPick.setPickTemuSsSkuCount(s.getPickTemuSsSkuCount());
                rfoPick.setPickTemuSsPcsCount(s.getPickTemuSsPcsCount());
                rfoPick.setPickTemuSmCount(s.getPickTemuSmCount());
                rfoPick.setPickTemuSmSkuCount(s.getPickTemuSmSkuCount());
                rfoPick.setPickTemuSmPcsCount(s.getPickTemuSmPcsCount());
            }
            else {
                map.put(s.getUserId() + s.getPickDate(), s);
            }
        });
        return new ArrayList<>(map.values());
    }

    @Override
    public List<CheckoutStatistics> queryPickCheckoutStatisticsFromFba(CheckoutStatisticsQueryCondition query, boolean isAll) {
        SqlerRequest request = new SqlerRequest("queryPickCheckoutStatisticsListByFba");
        setQueryCondition(request, query);
        String dateFormat = query.getDateFormat();
        if (isAll) {
            if (StringUtils.isBlank(dateFormat)) {// 出库总作业报表
                request.addSqlDataParam("pick_date", ", DATE_FORMAT(pick.picking_end_date, '%Y-%m-%d') AS pickDate");
                request.addSqlDataParam("group_by_pick_date", "GROUP BY pick.receive_person, pickDate");
            }
            else {// 出库看板
                request.addSqlDataParam("pick_date",
                        ", DATE_FORMAT(pick.picking_end_date, '" + dateFormat + "') AS pickDate");
                request.addSqlDataParam("group_by_pick_date", "GROUP BY pickDate");
            }
        }
        else {// 出库作业报表
            request.addSqlDataParam("group_by_pick_date", "GROUP BY pick.receive_person");
        }

        /** 本仓拣货数据 */
        List<CheckoutStatistics> localStatisticsList = SqlerTemplate.query(request,
                new CheckoutStatisticsByFbaMapper(CheckoutStatisticsEnum.PICK, isAll));

        // 出库看板数据
        if (isAll && StringUtils.isNotBlank(dateFormat)) {
            return localStatisticsList;
        }
        return localStatisticsList;
    }

    @Override
    public List<CheckoutStatistics> queryPickCheckoutFbaSingCount(CheckoutStatisticsQueryCondition query, boolean isAll) {
        SqlerRequest request = new SqlerRequest("queryPickCheckoutFbaSingCount");
        setQueryCondition(request, query);
        String dateFormat = query.getDateFormat();
        if (isAll) {
            if (StringUtils.isBlank(dateFormat)) {// 出库总作业报表
                request.addSqlDataParam("pick_date", ", DATE_FORMAT(pick.picking_end_date, '%Y-%m-%d') AS pickDate");
                request.addSqlDataParam("group_by_pick_date", "GROUP BY pick.receive_person, pickDate");
            }
            else {// 出库看板
                request.addSqlDataParam("pick_date",
                        ", DATE_FORMAT(pick.picking_end_date, '" + dateFormat + "') AS pickDate");
                request.addSqlDataParam("group_by_pick_date", "GROUP BY pickDate");
            }
        }
        else {// 出库作业报表
            request.addSqlDataParam("group_by_pick_date", "GROUP BY pick.receive_person");
        }
        List<CheckoutStatistics> pickList = SqlerTemplate.query(request,
                new RowMapper<CheckoutStatistics>() {
                    @Override
                    public CheckoutStatistics mapRow(ResultSet rs, int rowNum) throws SQLException {
                        CheckoutStatistics entity = new CheckoutStatistics();
                        entity.setUserId(rs.getObject("userId") == null ? null : rs.getInt("userId"));
                        entity.setUserName(rs.getString("userName"));
                        entity.setName(rs.getString("name"));
                        entity.setPickFbaSingCount(rs.getLong("pickFbaSingCount"));
                        entity.setPickOverseaHeadwaySingCount(rs.getLong("pickOverseaHeadwaySingCount"));
                        entity.setPickTransferSsCount(rs.getLong("pickTransferSsCount"));
                        entity.setPickTransferSmCount(rs.getLong("pickTransferSmCount"));
                        entity.setPickTransferMmCount(rs.getLong("pickTransferMmCount"));
                        entity.setPickTransferCrossMmCount(rs.getLong("pickTransferCrossMmCount"));
                        entity.setPickWarehouseSingleCount(rs.getLong("pickWarehouseSingleCount"));
                        entity.setPickWarehouseSingleSsCount(rs.getLong("pickWarehouseSingleSsCount"));
                        entity.setPickWarehouseSingleSmCount(rs.getLong("pickWarehouseSingleSmCount"));
                        entity.setPickWarehouseMmCount(rs.getLong("pickWarehouseMmCount"));
                        if (isAll) {
                            entity.setPickDate(rs.getString("pickDate"));
                        }
                        return entity;
                    }
                });

        return pickList;
    }

    @Override
    public List<CheckoutStatistics> queryPickRfoStatistics(CheckoutStatisticsQueryCondition query, boolean isAll) {
        SqlerRequest request = new SqlerRequest("queryPickRfoStatistics");
        setQueryCondition(request, query);
        String dateFormat = query.getDateFormat();
        if (isAll) {
            if (StringUtils.isBlank(dateFormat)) {// 出库总作业报表
                request.addSqlDataParam("pick_date", ", DATE_FORMAT(t.pick_end_time, '%Y-%m-%d') AS pickDate");
                request.addSqlDataParam("group_by_pick_date", "GROUP BY t.pick_by, pickDate");
            }
            else {// 出库看板
                request.addSqlDataParam("pick_date",
                        ", DATE_FORMAT(t.pick_end_time, '" + dateFormat + "') AS pickDate");
                request.addSqlDataParam("group_by_pick_date", "GROUP BY pickDate");
            }
        }
        else {// 出库作业报表
            request.addSqlDataParam("group_by_pick_date", "GROUP BY t.pick_by");
        }
        List<CheckoutStatistics> pickList = SqlerTemplate.query(request,
                new RowMapper<CheckoutStatistics>() {
                    @Override
                    public CheckoutStatistics mapRow(ResultSet rs, int rowNum) throws SQLException {
                        CheckoutStatistics entity = new CheckoutStatistics();
                        entity.setUserId(rs.getObject("userId") == null ? null : rs.getInt("userId"));
                        entity.setUserName(rs.getString("userName"));
                        entity.setName(rs.getString("name"));
                        entity.setPickRfoPcsCount(rs.getLong("pickRfoPcsCount"));
                        entity.setPickRfoSkuCount(rs.getLong("pickRfoSkuCount"));
                        entity.setPickRfoSingCount(rs.getLong("pickRfoSingCount"));
                        if (isAll) {
                            entity.setPickDate(rs.getString("pickDate"));
                        }
                        return entity;
                    }
                });

        return pickList;
    }

    public List<CheckoutStatistics> queryPickSpoStatistics(CheckoutStatisticsQueryCondition query, boolean isAll) {
        SqlerRequest request = new SqlerRequest("queryPickSpoStatistics");
        setQueryCondition(request, query);
        String dateFormat = query.getDateFormat();
        if (isAll) {
            if (StringUtils.isBlank(dateFormat)) {// 出库总作业报表
                request.addSqlDataParam("pick_date", ", DATE_FORMAT(t.pick_end_time, '%Y-%m-%d') AS pickDate");
                request.addSqlDataParam("group_by_pick_date", "GROUP BY t.pick_by, pickDate");
            }
            else {// 出库看板
                request.addSqlDataParam("pick_date",
                        ", DATE_FORMAT(t.pick_end_time, '" + dateFormat + "') AS pickDate");
                request.addSqlDataParam("group_by_pick_date", "GROUP BY pickDate");
            }
        }
        else {// 出库作业报表
            request.addSqlDataParam("group_by_pick_date", "GROUP BY t.pick_by");
        }
        List<CheckoutStatistics> pickList = SqlerTemplate.query(request,
                new RowMapper<CheckoutStatistics>() {
                    @Override
                    public CheckoutStatistics mapRow(ResultSet rs, int rowNum) throws SQLException {
                        CheckoutStatistics entity = new CheckoutStatistics();
                        entity.setUserId(rs.getObject("userId") == null ? null : rs.getInt("userId"));
                        entity.setUserName(rs.getString("userName"));
                        entity.setName(rs.getString("name"));
                        entity.setPickSpoPcsCount(rs.getLong("pickSpoPcsCount"));
                        entity.setPickSpoSkuCount(rs.getLong("pickSpoSkuCount"));
                        entity.setPickSpoSingCount(rs.getLong("pickSpoSingCount"));
                        if (isAll) {
                            entity.setPickDate(rs.getString("pickDate"));
                        }
                        return entity;
                    }
                });

        return pickList;
    }


    @Override
    public List<CheckoutStatistics> queryPickLendStatistics(CheckoutStatisticsQueryCondition query, boolean isAll) {
        SqlerRequest request = new SqlerRequest("queryPickLendStatistics");
        setQueryCondition(request, query);
        String dateFormat = query.getDateFormat();
        if (isAll) {
            if (StringUtils.isBlank(dateFormat)) {// 出库总作业报表
                request.addSqlDataParam("pick_date", ", DATE_FORMAT(lend.picking_end_date, '%Y-%m-%d') AS pickDate");
                request.addSqlDataParam("group_by_pick_date", "GROUP BY lend.receive_person, pickDate");
            }
            else {// 出库看板
                request.addSqlDataParam("pick_date",
                        ", DATE_FORMAT(lend.picking_end_date, '" + dateFormat + "') AS pickDate");
                request.addSqlDataParam("group_by_pick_date", "GROUP BY pickDate");
            }
        }
        else {// 出库作业报表
            request.addSqlDataParam("group_by_pick_date", "GROUP BY lend.receive_person");
        }
        List<CheckoutStatistics> pickLendList = SqlerTemplate.query(request,
                new RowMapper<CheckoutStatistics>() {
                    @Override
                    public CheckoutStatistics mapRow(ResultSet rs, int rowNum) throws SQLException {
                        CheckoutStatistics entity = new CheckoutStatistics();
                        entity.setUserId(rs.getObject("userId") == null ? null : rs.getInt("userId"));
                        entity.setUserName(rs.getString("userName"));
                        entity.setName(rs.getString("name"));
                        entity.setPickLendSingCount(rs.getLong("pickLendSingCount"));
                        entity.setPickLendSkuCount(rs.getLong("pickLendSkuCount"));
                        entity.setPickLendPcsCount(rs.getLong("pickLendPcsCount"));
                        if (isAll) {
                            entity.setPickDate(rs.getString("pickDate"));
                        }
                        return entity;
                    }
                });

        List<CheckoutStatistics> pickRfoList = queryPickRfoStatistics(query, isAll);

        return mergeRfoPickQuantity(pickRfoList,pickLendList);
    }

    public List<CheckoutStatistics> mergePickQuantity(List<CheckoutStatistics> pickLendList,
            List<CheckoutStatistics> pickList) {
        if (CollectionUtils.isEmpty(pickList)) {
            return pickLendList;
        }
        if (CollectionUtils.isEmpty(pickLendList)) {
            return pickList;
        }
        Map<String, CheckoutStatistics> map = pickList.stream()
                .collect(Collectors.toMap(s -> s.getUserId() + s.getPickDate(), item -> item));
        Set<String> skuSet = map.keySet();
        pickLendList.forEach(s -> {
            if (skuSet.contains(s.getUserId() + s.getPickDate())) {
                CheckoutStatistics pickEntity = map.get(s.getUserId() + s.getPickDate());
                pickEntity.setPickRfoSingCount(s.getPickRfoSingCount());
                pickEntity.setPickRfoSkuCount(s.getPickRfoSkuCount());
                pickEntity.setPickRfoPcsCount(s.getPickRfoPcsCount());
                pickEntity.setPickLendSingCount(s.getPickLendSingCount());
                pickEntity.setPickLendSkuCount(s.getPickLendSkuCount());
                pickEntity.setPickLendPcsCount(s.getPickLendPcsCount());
            }
            else {
                map.put(s.getUserId() + s.getPickDate(), s);
            }
        });
        return new ArrayList<>(map.values());
    }

    public List<CheckoutStatistics> mergeRfoPickQuantity(List<CheckoutStatistics> pickRfoList,
            List<CheckoutStatistics> pickLendList) {
        if (CollectionUtils.isEmpty(pickRfoList)) {
            return pickLendList;
        }
        if (CollectionUtils.isEmpty(pickLendList)) {
            return pickRfoList;
        }
        Map<String, CheckoutStatistics> map = pickRfoList.stream()
                .collect(Collectors.toMap(s -> s.getUserId() + s.getPickDate(), item -> item));
        Set<String> skuSet = map.keySet();
        pickLendList.forEach(s -> {
            if (skuSet.contains(s.getUserId() + s.getPickDate())) {
                CheckoutStatistics rfoPick = map.get(s.getUserId() + s.getPickDate());
                rfoPick.setPickLendSingCount(s.getPickLendSingCount());
                rfoPick.setPickLendSkuCount(s.getPickLendSkuCount());
                rfoPick.setPickLendPcsCount(s.getPickLendPcsCount());
            }
            else {
                map.put(s.getUserId() + s.getPickDate(), s);
            }
        });
        return new ArrayList<>(map.values());
    }

    public List<CheckoutStatistics> mergeSpoPickQuantity(List<CheckoutStatistics> pickSpoList,
                                                         List<CheckoutStatistics> pickList) {
        if (CollectionUtils.isEmpty(pickSpoList)) {
            return pickList;
        }
        if (CollectionUtils.isEmpty(pickList)) {
            return pickSpoList;
        }
        Map<String, CheckoutStatistics> map = pickList.stream()
                .collect(Collectors.toMap(s -> s.getUserId() + s.getPickDate(), item -> item));
        Set<String> skuSet = map.keySet();
        pickSpoList.forEach(s -> {
            if (skuSet.contains(s.getUserId() + s.getPickDate())) {
                CheckoutStatistics rfoPick = map.get(s.getUserId() + s.getPickDate());
                rfoPick.setPickSpoSingCount(s.getPickSpoSingCount());
                rfoPick.setPickSpoSkuCount(s.getPickSpoSkuCount());
                rfoPick.setPickSpoPcsCount(s.getPickSpoPcsCount());
            }
            else {
                map.put(s.getUserId() + s.getPickDate(), s);
            }
        });
        return new ArrayList<>(map.values());
    }

    @Override
    public List<CheckoutStatistics> queryGridRfoStatistics(CheckoutStatisticsQueryCondition query, boolean isAll) {
        SqlerRequest request = new SqlerRequest("queryGridRfoStatistics");
        setQueryCondition(request, query);
        String dateFormat = query.getDateFormat();
        if (isAll) {
            if (StringUtils.isBlank(dateFormat)) {// 出库总作业报表
                request.addSqlDataParam("soft_date", ", DATE_FORMAT(t.grid_time, '%Y-%m-%d') AS softDate");
                request.addSqlDataParam("group_by_soft_date", "GROUP BY t.grid_by, softDate");
            }
            else {// 出库看板
                request.addSqlDataParam("soft_date",
                        ", DATE_FORMAT(t.grid_time, '" + dateFormat + "') AS softDate");
                request.addSqlDataParam("group_by_soft_date", "GROUP BY softDate");
            }
        }
        else {// 出库作业报表
            request.addSqlDataParam("group_by_soft_date", "GROUP BY t.grid_by");
        }
        List<CheckoutStatistics> pickList = SqlerTemplate.query(request,
                new RowMapper<CheckoutStatistics>() {
                    @Override
                    public CheckoutStatistics mapRow(ResultSet rs, int rowNum) throws SQLException {
                        CheckoutStatistics entity = new CheckoutStatistics();
                        entity.setUserId(rs.getObject("userId") == null ? null : rs.getInt("userId"));
                        entity.setUserName(rs.getString("userName"));
                        entity.setName(rs.getString("name"));
                        entity.setSoftRfoSingCount(rs.getLong("softRfoSingCount"));
                        entity.setSoftRfoPcsCount(rs.getLong("softRfoPcsCount"));
                        entity.setSoftRfoSkuCount(rs.getLong("softRfoSkuCount"));
                        if (isAll) {
                            entity.setSoftDate(rs.getString("softDate"));
                        }
                        return entity;
                    }
                });

        return pickList;
    }


    /**
     * 查询内购出库统计
     * @param query
     * @param isAll
     * @return
     */
    public List<CheckoutStatistics> queryGridSpoStatistics(CheckoutStatisticsQueryCondition query, boolean isAll) {
        SqlerRequest request = new SqlerRequest("queryGridSpoStatistics");
        setQueryCondition(request, query);
        String dateFormat = query.getDateFormat();
        if (isAll) {
            if (StringUtils.isBlank(dateFormat)) {// 出库总作业报表
                request.addSqlDataParam("soft_date", ", DATE_FORMAT(t.grid_time, '%Y-%m-%d') AS softDate");
                request.addSqlDataParam("group_by_soft_date", "GROUP BY t.grid_by, softDate");
            }
            else {// 出库看板
                request.addSqlDataParam("soft_date",
                        ", DATE_FORMAT(t.grid_time, '" + dateFormat + "') AS softDate");
                request.addSqlDataParam("group_by_soft_date", "GROUP BY softDate");
            }
        }
        else {// 出库作业报表
            request.addSqlDataParam("group_by_soft_date", "GROUP BY t.grid_by");
        }
        List<CheckoutStatistics> pickList = SqlerTemplate.query(request,
                new RowMapper<CheckoutStatistics>() {
                    @Override
                    public CheckoutStatistics mapRow(ResultSet rs, int rowNum) throws SQLException {
                        CheckoutStatistics entity = new CheckoutStatistics();
                        entity.setUserId(rs.getObject("userId") == null ? null : rs.getInt("userId"));
                        entity.setUserName(rs.getString("userName"));
                        entity.setName(rs.getString("name"));
                        entity.setSoftSpoSingCount(rs.getLong("softSpoSingCount"));
                        entity.setSoftSpoPcsCount(rs.getLong("softSpoPcsCount"));
                        entity.setSoftSpoSkuCount(rs.getLong("softSpoSkuCount"));
                        if (isAll) {
                            entity.setSoftDate(rs.getString("softDate"));
                        }
                        return entity;
                    }
                });

        return pickList;
    }

    public List<CheckoutStatistics> mergeGridSpoQuantity(List<CheckoutStatistics> gridSpoList,
                                                      List<CheckoutStatistics> gridList) {
        if (CollectionUtils.isEmpty(gridSpoList)) {
            return gridList;
        }
        if (CollectionUtils.isEmpty(gridList)) {
            return gridSpoList;
        }
        Map<String, CheckoutStatistics> map = gridList.stream()
                .collect(Collectors.toMap(s -> s.getUserId() + s.getSoftDate(), item -> item));
        Set<String> skuSet = map.keySet();
        gridSpoList.forEach(s -> {
            if (skuSet.contains(s.getUserId() + s.getSoftDate())) {
                CheckoutStatistics softEntity = map.get(s.getUserId() + s.getSoftDate());
                softEntity.setSoftSpoSingCount(s.getSoftSpoSingCount());
                softEntity.setSoftSpoSkuCount(s.getSoftSpoSkuCount());
                softEntity.setSoftSpoPcsCount(s.getSoftSpoPcsCount());
            }
            else {
                map.put(s.getUserId() + s.getSoftDate(), s);
            }
        });
        return new ArrayList<>(map.values());
    }

    /**
     * 组装fba拣货数据
     *
     * @param localStatisticsList
     * @param fbaStatisticsList
     * @param isAll
     * @return
     */
    private List<CheckoutStatistics> handlePickData(List<CheckoutStatistics> localStatisticsList,
                                                    List<CheckoutStatistics> fbaStatisticsList, boolean isAll) {
        if (isAll) {
            for (CheckoutStatistics localEntity : localStatisticsList) {
                for (CheckoutStatistics fbaEntity : fbaStatisticsList) {
                    if (localEntity.getUserId().equals(fbaEntity.getUserId())
                            && localEntity.getPickDate().equals(fbaEntity.getPickDate())) {
                        localEntity.setPickFbaSkuCount(fbaEntity.getPickFbaSkuCount());
                        localEntity.setPickFbaPcsCount(fbaEntity.getPickFbaPcsCount());
                        localEntity.setPickOverseaHeadwaySkuCount(fbaEntity.getPickOverseaHeadwaySkuCount());
                        localEntity.setPickOverseaHeadwayPcsCount(fbaEntity.getPickOverseaHeadwayPcsCount());
                        localEntity.setPickTransferSsSkuCount(fbaEntity.getPickTransferSsSkuCount());
                        localEntity.setPickTransferSsPcsCount(fbaEntity.getPickTransferSsPcsCount());
                        localEntity.setPickTransferSmSkuCount(fbaEntity.getPickTransferSmSkuCount());
                        localEntity.setPickTransferSmPcsCount(fbaEntity.getPickTransferSmPcsCount());
                        localEntity.setPickTransferMmSkuCount(fbaEntity.getPickTransferMmSkuCount());
                        localEntity.setPickTransferMmPcsCount(fbaEntity.getPickTransferMmPcsCount());
                        localEntity.setPickTransferCrossMmSkuCount(fbaEntity.getPickTransferCrossMmSkuCount());
                        localEntity.setPickTransferCrossMmPcsCount(fbaEntity.getPickTransferCrossMmPcsCount());
                        localEntity.setPickWarehouseSinglePcsCount(fbaEntity.getPickWarehouseSinglePcsCount());
                        localEntity.setPickWarehouseSingleSkuCount(fbaEntity.getPickWarehouseSingleSkuCount());
                        localEntity.setPickWarehouseSingleSsPcsCount(fbaEntity.getPickWarehouseSingleSsPcsCount());
                        localEntity.setPickWarehouseSingleSsSkuCount(fbaEntity.getPickWarehouseSingleSsSkuCount());
                        localEntity.setPickWarehouseSingleSmPcsCount(fbaEntity.getPickWarehouseSingleSmPcsCount());
                        localEntity.setPickWarehouseSingleSmSkuCount(fbaEntity.getPickWarehouseSingleSmSkuCount());
                        localEntity.setPickWarehouseMmPcsCount(fbaEntity.getPickWarehouseMmPcsCount());
                        localEntity.setPickWarehouseMmSkuCount(fbaEntity.getPickWarehouseMmSkuCount());
                        fbaStatisticsList.remove(fbaEntity);
                        break;
                    }
                }
            }
            for (CheckoutStatistics fbaEntity : fbaStatisticsList) {
                localStatisticsList.add(fbaEntity);
            }
        }
        else {
            for (CheckoutStatistics localEntity : localStatisticsList) {
                for (CheckoutStatistics fbaEntity : fbaStatisticsList) {
                    if (localEntity.getUserId().equals(fbaEntity.getUserId())) {
                        localEntity.setPickFbaSkuCount(fbaEntity.getPickFbaSkuCount());
                        localEntity.setPickFbaPcsCount(fbaEntity.getPickFbaPcsCount());
                        localEntity.setPickOverseaHeadwaySkuCount(fbaEntity.getPickOverseaHeadwaySkuCount());
                        localEntity.setPickOverseaHeadwayPcsCount(fbaEntity.getPickOverseaHeadwayPcsCount());
                        localEntity.setPickTransferSsSkuCount(fbaEntity.getPickTransferSsSkuCount());
                        localEntity.setPickTransferSsPcsCount(fbaEntity.getPickTransferSsPcsCount());
                        localEntity.setPickTransferSmSkuCount(fbaEntity.getPickTransferSmSkuCount());
                        localEntity.setPickTransferSmPcsCount(fbaEntity.getPickTransferSmPcsCount());
                        localEntity.setPickTransferMmSkuCount(fbaEntity.getPickTransferMmSkuCount());
                        localEntity.setPickTransferMmPcsCount(fbaEntity.getPickTransferMmPcsCount());
                        localEntity.setPickTransferCrossMmSkuCount(fbaEntity.getPickTransferCrossMmSkuCount());
                        localEntity.setPickTransferCrossMmPcsCount(fbaEntity.getPickTransferCrossMmPcsCount());
                        localEntity.setPickWarehouseSinglePcsCount(fbaEntity.getPickWarehouseSinglePcsCount());
                        localEntity.setPickWarehouseSingleSkuCount(fbaEntity.getPickWarehouseSingleSkuCount());
                        localEntity.setPickWarehouseSingleSsPcsCount(fbaEntity.getPickWarehouseSingleSsPcsCount());
                        localEntity.setPickWarehouseSingleSsSkuCount(fbaEntity.getPickWarehouseSingleSsSkuCount());
                        localEntity.setPickWarehouseSingleSmPcsCount(fbaEntity.getPickWarehouseSingleSmPcsCount());
                        localEntity.setPickWarehouseSingleSmSkuCount(fbaEntity.getPickWarehouseSingleSmSkuCount());
                        localEntity.setPickWarehouseMmPcsCount(fbaEntity.getPickWarehouseMmPcsCount());
                        localEntity.setPickWarehouseMmSkuCount(fbaEntity.getPickWarehouseMmSkuCount());
                        fbaStatisticsList.remove(fbaEntity);
                        break;
                    }
                }
            }
            for (CheckoutStatistics fbaEntity : fbaStatisticsList) {
                localStatisticsList.add(fbaEntity);
            }
        }

        return localStatisticsList;
    }

    @Override
    public List<CheckoutStatistics> querySoftCheckoutStatistics(CheckoutStatisticsQueryCondition query, boolean isAll) {
        SqlerRequest request = new SqlerRequest("querySoftCheckoutStatisticsList");
        setQueryCondition(request, query);
        String dateFormat = query.getDateFormat();
        if (isAll) {
            if (StringUtils.isBlank(dateFormat)) {// 出库总作业报表
                request.addSqlDataParam("soft_date", ", DATE_FORMAT(track.sow_finish_time, '%Y-%m-%d') AS softDate");
                request.addSqlDataParam("group_by_soft_date", "GROUP BY track.sow_user, softDate");
            }
            else {// 出库看板
                request.addSqlDataParam("soft_date",
                        ", DATE_FORMAT(track.sow_finish_time, '" + dateFormat + "') AS softDate");
                request.addSqlDataParam("group_by_soft_date", "GROUP BY softDate");
            }
        }
        else {// 出库作业报表
            request.addSqlDataParam("group_by_soft_date", "GROUP BY track.sow_user");
        }

        /** 播种数据 */
        List<CheckoutStatistics> localStatisticsList = SqlerTemplate.query(request,
                new CheckoutStatisticsMapper(CheckoutStatisticsEnum.SOFT, isAll));

        // 出库看板数据
        if (isAll && StringUtils.isNotBlank(dateFormat)) {
            return localStatisticsList;
        }

        List<CheckoutStatistics> gridRfoList = queryGridRfoStatistics(query, isAll);
        List<CheckoutStatistics> gridList = mergeSoftQuantity(gridRfoList,localStatisticsList);
        List<CheckoutStatistics> gridSpoList = queryGridSpoStatistics(query, isAll);
        return mergeGridSpoQuantity(gridSpoList, gridList);
    }

    public List<CheckoutStatistics> mergeSoftQuantity(List<CheckoutStatistics> gridRfoList,
            List<CheckoutStatistics> softList) {
        if (CollectionUtils.isEmpty(gridRfoList)) {
            return softList;
        }
        if (CollectionUtils.isEmpty(softList)) {
            return gridRfoList;
        }
        Map<String, CheckoutStatistics> map = softList.stream()
                .collect(Collectors.toMap(s -> s.getUserId() + s.getSoftDate(), item -> item));
        Set<String> skuSet = map.keySet();
        gridRfoList.forEach(s -> {
            if (skuSet.contains(s.getUserId() + s.getSoftDate())) {
                CheckoutStatistics softEntity = map.get(s.getUserId() + s.getSoftDate());
                softEntity.setSoftRfoSingCount(s.getSoftRfoSingCount());
                softEntity.setSoftRfoSkuCount(s.getSoftRfoSkuCount());
                softEntity.setSoftRfoPcsCount(s.getSoftRfoPcsCount());
            }
            else {
                map.put(s.getUserId() + s.getSoftDate(), s);
            }
        });
        return new ArrayList<>(map.values());
    }

    @Override
    public List<CheckoutStatistics> queryAsnSoftCheckoutStatistics(CheckoutStatisticsQueryCondition query,
            boolean isAll) {
        SqlerRequest request = new SqlerRequest("queryAsnSoftCheckoutStatistics");
        setQueryCondition(request, query);
        String dateFormat = query.getDateFormat();
        if (isAll) {
            if (StringUtils.isBlank(dateFormat)) {// 出库总作业报表
                request.addSqlDataParam("soft_date", ", DATE_FORMAT(wfa.deliver_time, '%Y-%m-%d') AS softDate");
                request.addSqlDataParam("group_by_soft_date", "GROUP BY wfa.sow_user, softDate");
            }
            else {// 出库看板
                request.addSqlDataParam("soft_date",
                        ", DATE_FORMAT(wfa.deliver_time, '" + dateFormat + "') AS softDate");
                request.addSqlDataParam("group_by_soft_date", "GROUP BY softDate");
            }
        }
        else {// 出库作业报表
            request.addSqlDataParam("group_by_soft_date", "GROUP BY wfa.sow_user");
        }
        request.addDataParam("purposeHouseNotInList",DataType.STRING, SaleChannel.saleChannels);
        /** FBA播种数据 */
        List<CheckoutStatistics> asnFbaList = SqlerTemplate.query(request,
                (rs, rowNum) -> {

                    CheckoutStatistics entity = new CheckoutStatistics();
                    entity.setUserId(rs.getObject("userId") == null ? null : rs.getInt("userId"));
                    entity.setUserName(rs.getString("userName"));
                    entity.setName(rs.getString("name"));
                    if (isAll) {
                        entity.setSoftDate(rs.getString("softDate"));
                    }
                    entity.setAsnFbaPcsCount(rs.getLong("softPcsCount"));
                    entity.setAsnFbaSingCount(rs.getLong("softSingCount"));
                    entity.setAsnFbaSkuCount(rs.getLong("softSkuCount"));
                    return entity;
                });
        /** 出库看板数据 */
        Map<String, CheckoutStatistics> asnFbaSkuCountMap = null;
        if (isAll) {
            asnFbaSkuCountMap = CollectionUtils.isEmpty(asnFbaList) ? new HashMap<>()
                    : asnFbaList.stream()
                    .collect(Collectors.toMap(item -> item.getUserId() + item.getSoftDate(), item -> item));
        }else {
            asnFbaSkuCountMap = CollectionUtils.isEmpty(asnFbaList) ? new HashMap<>()
                    : asnFbaList.stream().collect(Collectors.toMap(item -> item.getUserId().toString(), item -> item));
        }

        // 海外仓
        request.addDataParam("purposeHouseNotInList", DataType.STRING, null);
        request.addDataParam("purposeHouseInList", DataType.STRING, SaleChannel.saleChannels);
        request.addSqlDataParam("TRANSFER_CONDITION"," AND f.id NOT IN (" +
                " SELECT wh_asn_id " +
                " FROM wh_asn_extra ae " +
                " WHERE (f.purpose_house, ae.package_method) IN (('SMT', 4), ('SMT', 7), ('Shein', 8), ('Shein', 9)))");
        Map<String, CheckoutStatistics> finalAsnFbaSkuCountMap = asnFbaSkuCountMap;
        SqlerTemplate.query(request,
                (rs, rowNum) -> {
                    Integer userId = (rs.getObject("userId") == null) ? null : rs.getInt("userId");
                    CheckoutStatistics checkoutStatistics= null;
                    if (userId != null){
                        if (isAll) {
                            String softDate = rs.getString("softDate");
                            checkoutStatistics = finalAsnFbaSkuCountMap.get(userId + softDate);
                        }else {
                            checkoutStatistics = finalAsnFbaSkuCountMap.get(userId.toString());
                        }
                        if (checkoutStatistics == null) {
                            checkoutStatistics = new CheckoutStatistics();
                            checkoutStatistics.setUserId(rs.getObject("userId") == null ? null : rs.getInt("userId"));
                            checkoutStatistics.setUserName(rs.getString("userName"));
                            checkoutStatistics.setName(rs.getString("name"));
                            if (isAll) {
                                checkoutStatistics.setSoftDate(rs.getString("softDate"));
                                finalAsnFbaSkuCountMap.put(userId + checkoutStatistics.getSoftDate(), checkoutStatistics);
                            }else{
                                finalAsnFbaSkuCountMap.put(userId.toString(), checkoutStatistics);
                            }
                        }
                            checkoutStatistics.setAsnFirstPcsCount(rs.getLong("softPcsCount"));
                            checkoutStatistics.setAsnFirstSingCount(rs.getLong("softSingCount"));
                            checkoutStatistics.setAsnFirstSkuCount(rs.getLong("softSkuCount"));

                    }
                    return checkoutStatistics;
                });
        // 中转仓
        List<String> purposeHouseList = Arrays.asList("SMT", "Shein");
        request.addDataParam("purposeHouseInList", DataType.STRING, purposeHouseList);
        request.addSqlDataParam("TRANSFER_CONDITION"," AND f.id IN ( " +
                " SELECT wh_asn_id " +
                " FROM wh_asn_extra ae " +
                " WHERE (f.purpose_house, ae.package_method) IN (('Shein', 8), ('Shein', 9), ('SMT', 4), ('SMT', 7), ('SMT', 5), ('SMT', 6)) " +
                " OR ae.package_method IS NULL) ");
         request.setNamingLabel("queryTransferSoftCheckoutStatistics");
         SqlerTemplate.query(request,
                (rs, rowNum) -> {
                    Integer userId = (rs.getObject("userId") == null) ? null : rs.getInt("userId");
                    CheckoutStatistics checkoutStatistics = null;
                    if (userId != null){
                        if (isAll) {
                            String softDate = rs.getString("softDate");
                            checkoutStatistics = finalAsnFbaSkuCountMap.get(userId + softDate);
                        }else {
                            checkoutStatistics = finalAsnFbaSkuCountMap.get(userId.toString());
                        }
                        if (checkoutStatistics == null) {
                            checkoutStatistics = new CheckoutStatistics();
                            checkoutStatistics.setUserId(rs.getObject("userId") == null ? null : rs.getInt("userId"));
                            checkoutStatistics.setUserName(rs.getString("userName"));
                            checkoutStatistics.setName(rs.getString("name"));
                            if (isAll) {
                                checkoutStatistics.setSoftDate(rs.getString("softDate"));
                                finalAsnFbaSkuCountMap.put(userId + checkoutStatistics.getSoftDate(), checkoutStatistics);
                            }else {
                                finalAsnFbaSkuCountMap.put(userId.toString(), checkoutStatistics);
                            }
                        }
                        checkoutStatistics.setAsnTransferPcsCount(rs.getLong("softPcsCount"));
                        checkoutStatistics.setAsnTransferSingCount(rs.getLong("softSingCount"));
                        checkoutStatistics.setAsnTransferSkuCount(rs.getLong("softSkuCount"));
                    }
                    return checkoutStatistics;
        });
//
//         //仓发
//        request.setNamingLabel("queryWarehouseSoftCheckoutStatistics");
//        SqlerTemplate.query(request,
//                (rs, rowNum) -> {
//                    Integer userId = (rs.getObject("userId") == null) ? null : rs.getInt("userId");
//                    CheckoutStatistics checkoutStatistics = null;
//                    if (userId != null){
//                        if (isAll) {
//                            String softDate = rs.getString("softDate");
//                            checkoutStatistics = finalAsnFbaSkuCountMap.get(userId + softDate);
//                        }else {
//                            checkoutStatistics = finalAsnFbaSkuCountMap.get(userId.toString());
//                        }
//                        if (checkoutStatistics == null) {
//                            checkoutStatistics = new CheckoutStatistics();
//                            checkoutStatistics.setUserId(rs.getObject("userId") == null ? null : rs.getInt("userId"));
//                            checkoutStatistics.setUserName(rs.getString("userName"));
//                            checkoutStatistics.setName(rs.getString("name"));
//                            if (isAll) {
//                                checkoutStatistics.setSoftDate(rs.getString("softDate"));
//                                finalAsnFbaSkuCountMap.put(userId + checkoutStatistics.getSoftDate(), checkoutStatistics);
//                            }else {
//                                finalAsnFbaSkuCountMap.put(userId.toString(), checkoutStatistics);
//                            }
//                        }
//                        checkoutStatistics.setWarehouseSoftGridPcsCount(rs.getLong("warehouseSoftGridPcsCount"));
//                        checkoutStatistics.setWarehouseSoftGridSingCount(rs.getLong("warehouseSoftGridSingCount"));
//                        checkoutStatistics.setWarehouseSoftGridSkuCount(rs.getLong("warehouseSoftGridSkuCount"));
//                    }
//                    return checkoutStatistics;
//                });
        //海外仓
        request.setNamingLabel("queryAsnSoftOverseasCheckoutStatistics");
        SqlerTemplate.query(request,
                (rs, rowNum) -> {
                    Integer userId = (rs.getObject("userId") == null) ? null : rs.getInt("userId");
                    CheckoutStatistics checkoutStatistics= null;
                    if (userId != null){
                        if (isAll) {
                            String softDate = rs.getString("softDate");
                            checkoutStatistics = finalAsnFbaSkuCountMap.get(userId + softDate);
                        }else {
                            checkoutStatistics = finalAsnFbaSkuCountMap.get(userId.toString());
                        }
                        if (checkoutStatistics == null) {
                            checkoutStatistics = new CheckoutStatistics();
                            checkoutStatistics.setUserId(rs.getObject("userId") == null ? null : rs.getInt("userId"));
                            checkoutStatistics.setUserName(rs.getString("userName"));
                            checkoutStatistics.setName(rs.getString("name"));
                            if (isAll) {
                                checkoutStatistics.setSoftDate(rs.getString("softDate"));
                                finalAsnFbaSkuCountMap.put(userId + checkoutStatistics.getSoftDate(), checkoutStatistics);
                            }else{
                                finalAsnFbaSkuCountMap.put(userId.toString(), checkoutStatistics);
                            }
                        }
                        checkoutStatistics.setAsnSoftOverseasSingCount(rs.getLong("asnSoftOverseasSingCount"));
                        checkoutStatistics.setAsnSoftOverseasPcsCount(rs.getLong("asnSoftOverseasPcsCount"));
                        checkoutStatistics.setAsnSoftOverseasSkuCount(rs.getLong("asnSoftOverseasSkuCount"));

                    }
                    return checkoutStatistics;
                });
        return finalAsnFbaSkuCountMap.entrySet().stream().map(e -> e.getValue()).collect(Collectors.toList());
    }

    /**
     * 合并海外仓头程
     *
     * @param asnFbaList
     * @param asnFirstList
     * @return
     */
    public List<CheckoutStatistics> mergeAsnSoftQuantity(List<CheckoutStatistics> asnFbaList,
            List<CheckoutStatistics> asnFirstList) {
        if (CollectionUtils.isEmpty(asnFbaList)) {
            return asnFirstList;
        }
        if (CollectionUtils.isEmpty(asnFirstList)) {
            return asnFbaList;
        }
        Map<String, CheckoutStatistics> map = asnFbaList.stream()
                .collect(Collectors.toMap(s -> s.getUserId() + s.getSoftDate(), item -> item));
        Set<String> skuSet = map.keySet();
        asnFirstList.forEach(s -> {
            if (skuSet.contains(s.getUserId() + s.getSoftDate())) {
                CheckoutStatistics asnFba = map.get(s.getUserId() + s.getSoftDate());
                asnFba.setAsnFirstPcsCount(s.getAsnFirstPcsCount());
                asnFba.setAsnFirstSingCount(s.getAsnFirstSingCount());
                asnFba.setAsnFirstSkuCount(s.getAsnFirstSkuCount());
            }
            else {
                map.put(s.getUserId() + s.getSoftDate(), s);
            }
        });
        return new ArrayList<>(map.values());
    }

    /**
     * 组装本仓和跨仓播种数据
     *
     * @param localStatisticsList
     * @param acrossStatisticsList
     * @param isAll
     * @return
     */
    private List<CheckoutStatistics> handleSoftData(List<CheckoutStatistics> localStatisticsList,
            List<CheckoutStatistics> acrossStatisticsList, boolean isAll) {
        if (isAll) {
            for (CheckoutStatistics localEntity : localStatisticsList) {
                for (CheckoutStatistics acrossEntity : acrossStatisticsList) {
                    if (localEntity.getUserId().equals(acrossEntity.getUserId())
                            && localEntity.getSoftDate().equals(acrossEntity.getSoftDate())) {
                        localEntity.setAllocationSoftSingCount(acrossEntity.getAllocationSoftSingCount());
                        localEntity.setAllocationSoftSkuCount(acrossEntity.getAllocationSoftSkuCount());
                        localEntity.setAllocationSoftPcsCount(acrossEntity.getAllocationSoftPcsCount());
                        acrossStatisticsList.remove(acrossEntity);
                        break;
                    }
                }
            }
            for (CheckoutStatistics acrossEntity : acrossStatisticsList) {
                localStatisticsList.add(acrossEntity);
            }
        }
        else {
            for (CheckoutStatistics localEntity : localStatisticsList) {
                for (CheckoutStatistics acrossEntity : acrossStatisticsList) {
                    if (localEntity.getUserId().equals(acrossEntity.getUserId())) {
                        localEntity.setAllocationSoftSingCount(acrossEntity.getAllocationSoftSingCount());
                        localEntity.setAllocationSoftSkuCount(acrossEntity.getAllocationSoftSkuCount());
                        localEntity.setAllocationSoftPcsCount(acrossEntity.getAllocationSoftPcsCount());
                        acrossStatisticsList.remove(acrossEntity);
                        break;
                    }
                }
            }
            for (CheckoutStatistics acrossEntity : acrossStatisticsList) {
                localStatisticsList.add(acrossEntity);
            }
        }

        return localStatisticsList;
    }

    /**
     * 跨仓调拨单播种数据（美景仓才会统计）
     *
     * @param query
     * @param isAll
     * @return
     */
    public List<CheckoutStatistics> queryArossSoftCheckoutStatisticsList(CheckoutStatisticsQueryCondition query,
            boolean isAll) {
        SqlerRequest request = new SqlerRequest("queryArossSoftCheckoutStatisticsList");
        setQueryCondition(request, query);
        if (isAll) {// 出库总作业报表
            request.addSqlDataParam("group_by_soft_date", "GROUP BY softIdentify, softTime");
        }
        else {// 出库作业报表
            request.addSqlDataParam("group_by_soft_date", "GROUP BY softIdentify");
        }

        /** 查询某时间段内已完成的订单调拨拣货sku列表 */
        List<ArossPickCheckoutStatistics> pickList = SqlerTemplate.query(request,
                new RowMapper<ArossPickCheckoutStatistics>() {
                    @Override
                    public ArossPickCheckoutStatistics mapRow(ResultSet rs, int rowNum) throws SQLException {
                        ArossPickCheckoutStatistics entity = new ArossPickCheckoutStatistics();
                        entity.setUserId(rs.getObject("userId") == null ? null : rs.getInt("userId"));
                        entity.setUserName(rs.getString("userName"));
                        entity.setName(rs.getString("name"));
                        entity.setPickTime(rs.getString("softTime"));
                        entity.setAllocationNo(rs.getString("allocationNo"));
                        entity.setSku(rs.getString("sku"));
                        entity.setPickIdentify(rs.getString("softIdentify"));
                        return entity;
                    }
                });

        if (CollectionUtils.isNotEmpty(pickList)) {
            /** 查询出已交运的调拨需求，解析apvItem中sku的数量 */
            return getAllocationDemandApvItems(pickList, isAll, CheckoutStatisticsEnum.SOFT.intCode());
        }

        return new ArrayList<CheckoutStatistics>();
    }

    /**
     * 查询出已交运的调拨需求，解析apvItem中sku的数量
     */
    public List<CheckoutStatistics> getAllocationDemandApvItems(List<ArossPickCheckoutStatistics> pickList,
            boolean isAll, int queryType) {

        SqlerRequest demandRequest = new SqlerRequest("queryDemandApvItemList");
        Set<String> allocationNoList = new HashSet<String>();
        pickList.forEach(entity -> {
            allocationNoList.add(entity.getAllocationNo());
        });
        if (CheckoutStatisticsEnum.PICK.intCode() == queryType) {
            demandRequest.addDataParam("task_status", DataType.INT, 3);
        }
        else if (CheckoutStatisticsEnum.SOFT.intCode() == queryType) {
            demandRequest.addSqlDataParam("SOFT_QUERY_SQL",
                    " LEFT JOIN wh_apv_allocation allocation ON allocation.allocation_no = demand.allocation_no");
            demandRequest.addSqlDataParam("ALLOCATION_STATUS_SQL", " AND allocation.allocation_status IN (5,6)");
        }
        demandRequest.addDataParam("allocation_no_list", DataType.STRING, allocationNoList);
        Map<String, ArossDemandApvItem> identifyMap = new HashMap<String, ArossDemandApvItem>();
        SqlerTemplate.query(demandRequest, new RowMapper<ArossDemandApvItem>() {
            @Override
            @SuppressWarnings("unchecked")
            public ArossDemandApvItem mapRow(ResultSet rs, int rowNum) throws SQLException {
                String allocationNo = rs.getString("allocation_no");
                String apvNo = rs.getString("apv_no");
                String apvItem = rs.getString("apv_item");
                if (StringUtils.isNotBlank(apvItem)) {
                    Map<String, Integer> skuMap = JSON.parseObject(apvItem, Map.class);
                    for (String sku : skuMap.keySet()) {
                        String pickIdentify = allocationNo.concat(sku);
                        ArossDemandApvItem existItem = identifyMap.get(pickIdentify);
                        ArossDemandApvItem entity = new ArossDemandApvItem();
                        entity.setPickIdentify(pickIdentify);
                        entity.setAllocationNo(allocationNo);
                        entity.setApvNo(apvNo);
                        entity.setSku(sku);
                        Integer skuQuantity = skuMap.get(sku);
                        if (existItem != null) {
                            skuQuantity = existItem.getQuantity() + skuQuantity;
                            if (!entity.getApvNos().contains(apvNo))
                                existItem.getApvNos().add(apvNo);
                            entity.getApvNos().addAll(existItem.getApvNos());
                        }
                        else {
                            entity.getApvNos().add(apvNo);
                        }
                        entity.setQuantity(skuQuantity);
                        identifyMap.put(pickIdentify, entity);
                    }
                }
                return null;
            }

        });

        if (CollectionUtils.isNotEmpty(identifyMap.keySet())) {
            /** 单量统计 */
            Map<String, Set<String>> singCountMap = new HashMap<String, Set<String>>();
            /** sku数统计 */
            Map<String, Set<String>> skuCountMap = new HashMap<String, Set<String>>();
            /** pcs数统计 */
            Map<String, Long> pcsCountMap = new HashMap<String, Long>();
            /** 拣货人 */
            Map<String, ArossPickCheckoutStatistics> userMap = new HashMap<String, ArossPickCheckoutStatistics>();
            pickList.forEach(entity -> {
                Integer userId = entity.getUserId();
                String pickTime = entity.getPickTime();

                String sKey = String.valueOf(userId);
                if (isAll) {
                    sKey = sKey.concat(";").concat(pickTime);
                }

                String pickIdentify = entity.getPickIdentify();
                ArossDemandApvItem apvItem = identifyMap.get(pickIdentify);
                if (null != apvItem) {
                    Set<String> apvSet = singCountMap.get(sKey);
                    if (null == apvSet) {
                        apvSet = new HashSet<String>();
                    }
                    // apvSet.add(apvItem.getApvNo());
                    apvSet.addAll(apvItem.getApvNos());
                    singCountMap.put(sKey, apvSet);

                    Set<String> skuSet = skuCountMap.get(sKey);
                    if (null == skuSet) {
                        skuSet = new HashSet<String>();
                    }
                    skuSet.add(apvItem.getPickIdentify());
                    skuCountMap.put(sKey, skuSet);

                    Long pcsCount = pcsCountMap.get(sKey) == null ? 0L : pcsCountMap.get(sKey);
                    Long quantity = apvItem.getQuantity() == null ? 0L : Long.valueOf(apvItem.getQuantity());
                    pcsCountMap.put(sKey, pcsCount + quantity);

                    userMap.put(sKey, entity);
                }
            });

            if (CollectionUtils.isNotEmpty(userMap.keySet())) {
                /** 跨仓拣货单量、sku数、pcs数 */
                List<CheckoutStatistics> statisticsList = new ArrayList<CheckoutStatistics>();
                userMap.forEach((k, v) -> {
                    Integer userId = v.getUserId();
                    String pickTime = v.getPickTime();
                    if (isAll) {
                        String[] ut = k.split(";");
                        for (int i = 0; i < ut.length; i++) {
                            if (i == 0)
                                userId = Integer.valueOf(ut[0]);
                            if (i == 1)
                                pickTime = ut[1];
                        }

                    }

                    CheckoutStatistics checkoutStatistics = new CheckoutStatistics();
                    checkoutStatistics.setUserId(userId);
                    checkoutStatistics.setUserName(v.getUserName());
                    checkoutStatistics.setName(v.getName());
                    if (CheckoutStatisticsEnum.PICK.intCode() == queryType) {
                        checkoutStatistics.setPickDate(pickTime);
                        checkoutStatistics.setAllocationSkuCount(Long.valueOf(skuCountMap.get(k).size()));
                        checkoutStatistics.setAllocationPcsCount(pcsCountMap.get(k));
                    }
                    else if (CheckoutStatisticsEnum.SOFT.intCode() == queryType) {
                        checkoutStatistics.setSoftDate(pickTime);
                        checkoutStatistics.setAllocationSoftSingCount(Long.valueOf(singCountMap.get(k).size()));
                        checkoutStatistics.setAllocationSoftSkuCount(Long.valueOf(skuCountMap.get(k).size()));
                        checkoutStatistics.setAllocationSoftPcsCount(pcsCountMap.get(k));
                    }
                    statisticsList.add(checkoutStatistics);
                });
                return statisticsList;
            }
        }
        return new ArrayList<CheckoutStatistics>();
    }

    @Override
    public List<CheckoutStatistics> queryPackingCheckoutStatistics(CheckoutStatisticsQueryCondition query,
            boolean isAll) {
        SqlerRequest request = new SqlerRequest("queryPackingCheckoutStatisticsList");
        setQueryCondition(request, query);
        if (isAll) {
            String dateFormat = query.getDateFormat();
            if (StringUtils.isBlank(dateFormat)) {// 出库总作业报表
                request.addSqlDataParam("packing_date",
                        ", DATE_FORMAT(track.pack_finish_time, '%Y-%m-%d') AS packingDate");
                request.addSqlDataParam("group_by_packing_date", "GROUP BY track.pack_user, packingDate");
            }
            else {// 出库看板
                request.addSqlDataParam("packing_date",
                        ", DATE_FORMAT(track.pack_finish_time, '" + dateFormat + "') AS packingDate");
                request.addSqlDataParam("group_by_packing_date", "GROUP BY packingDate");
            }
        }
        else {// 出库作业报表
            request.addSqlDataParam("group_by_packing_date", "GROUP BY track.pack_user");
        }
        List<CheckoutStatistics> notJitList = SqlerTemplate.query(request, new CheckoutStatisticsMapper(CheckoutStatisticsEnum.PACKING, isAll));
        //查询GPSR
        List<CheckoutStatistics> gpsrList = queryGpsrPackingCheckoutStatisticsList(query, isAll);
        //合并gpsr
        List<CheckoutStatistics> mglist = mergeGpsrPackingData(notJitList, gpsrList);
        //查询直发包装数据
        query.setIsDirect(true);
        List<CheckoutStatistics> directPackingList = queryDirectPackingCheckoutStatisticsList(query, isAll);
        List<CheckoutStatistics> mergeDirectList = mergeDirectPackingData(mglist, directPackingList);
        query.setIsDirect(false);
        List<CheckoutStatistics> jitList = queryJitPackingCheckoutStatistics(query, isAll);
        return mergePackingQuantity(mergeDirectList, jitList);
    }

    private List<CheckoutStatistics> mergeDirectPackingData(List<CheckoutStatistics> mergeList, List<CheckoutStatistics> directPackingList) {
        if (CollectionUtils.isEmpty(mergeList))
            return directPackingList;
        if (CollectionUtils.isEmpty(directPackingList))
            return mergeList;
        Map<String, CheckoutStatistics> map = mergeList.stream()
                .collect(Collectors.toMap(s -> s.getUserId() + s.getPackingDate(), item -> item));
        Set<String> skuSet = map.keySet();
        directPackingList.forEach(s -> {
            if (skuSet.contains(s.getUserId() + s.getPackingDate())) {
                CheckoutStatistics entity = map.get(s.getUserId() + s.getPackingDate());
                entity.setDirectPackingSsSingCount(s.getDirectPackingSsSingCount());
                entity.setDirectPackingSmSingCount(s.getDirectPackingSmSingCount());
                entity.setDirectPackingMmSingCount(s.getDirectPackingMmSingCount());
                entity.setDirectPackingSsPcsCount(s.getDirectPackingSsPcsCount());
                entity.setDirectPackingSmPcsCount(s.getDirectPackingSmPcsCount());
                entity.setDirectPackingMmPcsCount(s.getDirectPackingMmPcsCount());
            }
            else {
                map.put(s.getUserId() + s.getPackingDate(), s);
            }
        });
        return new ArrayList<>(map.values());
    }

    private List<CheckoutStatistics> mergeGpsrPackingData(List<CheckoutStatistics> mergeList, List<CheckoutStatistics> gpsrList) {
        if (CollectionUtils.isEmpty(mergeList))
            return gpsrList;
        if (CollectionUtils.isEmpty(gpsrList))
            return mergeList;
        Map<String, CheckoutStatistics> map = mergeList.stream()
                .collect(Collectors.toMap(s -> s.getUserId() + s.getPackingDate(), item -> item));
        Set<String> skuSet = map.keySet();
        gpsrList.forEach(s -> {
            if (skuSet.contains(s.getUserId() + s.getPackingDate())) {
                CheckoutStatistics entity = map.get(s.getUserId() + s.getPackingDate());
                entity.setGpsrPackingSsCount(s.getGpsrPackingSsCount());
                entity.setGpsrPackingSmCount(s.getGpsrPackingSmCount());
                entity.setGpsrPackingMmCount(s.getGpsrPackingMmCount());
                entity.setGpsrPackingSsPcsCount(s.getGpsrPackingSsPcsCount());
                entity.setGpsrPackingSmPcsCount(s.getGpsrPackingSmPcsCount());
                entity.setGpsrPackingMmPcsCount(s.getGpsrPackingMmPcsCount());
            }
            else {
                map.put(s.getUserId() + s.getPackingDate(), s);
            }
        });
        return new ArrayList<>(map.values());
    }
    
    public List<CheckoutStatistics> queryJitPackingCheckoutStatistics(CheckoutStatisticsQueryCondition query,
                                                                   boolean isAll) {
        SqlerRequest request = new SqlerRequest("queryJitPackingCheckoutStatistics");
        setQueryCondition(request, query);
        if (isAll) {
            String dateFormat = query.getDateFormat();
            if (StringUtils.isBlank(dateFormat)) {// 出库总作业报表
                request.addSqlDataParam("packing_date",
                        ", DATE_FORMAT(track.pack_finish_time, '%Y-%m-%d') AS packingDate");
                request.addSqlDataParam("group_by_packing_date", "GROUP BY track.pack_user, packingDate");
            }
            else {// 出库看板
                request.addSqlDataParam("packing_date",
                        ", DATE_FORMAT(track.pack_finish_time, '" + dateFormat + "') AS packingDate");
                request.addSqlDataParam("group_by_packing_date", "GROUP BY packingDate");
            }
        }
        else {// 出库作业报表
            request.addSqlDataParam("group_by_packing_date", "GROUP BY track.pack_user");
        }
        return SqlerTemplate.query(request, new CheckoutStatisticsMapper(CheckoutStatisticsEnum.PACKING, isAll,false,CheckInWhType.LOCAL.intCode()));
    }

    /**
     * 查询GPSR的包装数据
     * @param query
     * @param isAll
     * @return
     */
    public List<CheckoutStatistics> queryGpsrPackingCheckoutStatisticsList(CheckoutStatisticsQueryCondition query,
                                                                      boolean isAll) {
        SqlerRequest request = new SqlerRequest("queryGpsrPackingCheckoutStatisticsList");
        setQueryCondition(request, query);
        if (isAll) {
            String dateFormat = query.getDateFormat();
            if (StringUtils.isBlank(dateFormat)) {// 出库总作业报表
                request.addSqlDataParam("packing_date",
                        ", DATE_FORMAT(track.pack_finish_time, '%Y-%m-%d') AS packingDate");
                request.addSqlDataParam("group_by_packing_date", "GROUP BY track.pack_user, packingDate");
            }
            else {// 出库看板
                request.addSqlDataParam("packing_date",
                        ", DATE_FORMAT(track.pack_finish_time, '" + dateFormat + "') AS packingDate");
                request.addSqlDataParam("group_by_packing_date", "GROUP BY packingDate");
            }
        }
        else {// 出库作业报表
            request.addSqlDataParam("group_by_packing_date", "GROUP BY track.pack_user");
        }
        return SqlerTemplate.query(request, new RowMapper<CheckoutStatistics>() {
            @Override
            public CheckoutStatistics mapRow(ResultSet rs, int i) throws SQLException {
                CheckoutStatistics entity = new CheckoutStatistics();
                entity.setUserId(rs.getInt("userId"));
                entity.setUserName(rs.getString("userName"));
                entity.setName(rs.getString("name"));
                entity.setGpsrPackingSsCount(rs.getLong("gpsrPackingSsCount"));
                entity.setGpsrPackingSmCount(rs.getLong("gpsrPackingSmCount"));
                entity.setGpsrPackingMmCount(rs.getLong("gpsrPackingMmCount"));
                entity.setGpsrPackingSsPcsCount(rs.getLong("gpsrPackingSsPcsCount"));
                entity.setGpsrPackingSmPcsCount(rs.getLong("gpsrPackingSmPcsCount"));
                entity.setGpsrPackingMmPcsCount(rs.getLong("gpsrPackingMmPcsCount"));
                if (isAll) {
                    entity.setPackingDate(rs.getString("packingDate"));
                }
                return entity;
            }
        });
    }


    /**
     * 合并jit数据
     * 
     * @param notJitList
     * @param jitList
     * @return
     */
    public List<CheckoutStatistics> mergePackingQuantity(List<CheckoutStatistics> notJitList,
            List<CheckoutStatistics> jitList) {
        if (CollectionUtils.isEmpty(notJitList)) {
            return jitList;
        }
        if (CollectionUtils.isEmpty(jitList)) {
            return notJitList;
        }
        Map<String, CheckoutStatistics> map = notJitList.stream()
                .collect(Collectors.toMap(s -> s.getUserId() + s.getPackingDate(), item -> item));
        Set<String> skuSet = map.keySet();
        jitList.forEach(s -> {
            String key = s.getUserId() + s.getPackingDate();
            if (skuSet.contains(key)) {
                CheckoutStatistics notJit = map.get(key);
                notJit.setPackingJitSsSingCount(s.getPackingJitSsSingCount());
                notJit.setPackingJitSmSingCount(s.getPackingJitSmSingCount());
                notJit.setPackingJitMmSingCount(s.getPackingJitMmSingCount());
                notJit.setPackingJitSsPcsCount(s.getPackingJitSsPcsCount());
                notJit.setPackingJitSmPcsCount(s.getPackingJitSmPcsCount());
                notJit.setPackingJitMmPcsCount(s.getPackingJitMmPcsCount());
            }
            else {
                map.put(key, s);
            }
        });
        return new ArrayList<>(map.values());
    }

    @Override
    public List<CheckoutStatistics> queryAsnPackingCheckoutStatistics(CheckoutStatisticsQueryCondition query, boolean isAll) {
        //shein 单量和pcs数量
        SqlerRequest request = new SqlerRequest("queryAsnPackingCheckoutStatisticsList");
        setQueryCondition(request, query);
        /** 统计的发货单状态 */
        List<Integer> apvStatusList = new ArrayList<Integer>();
        apvStatusList.add(AsnPrepareStatus.DELIVER.intCode());
        apvStatusList.add(AsnPrepareStatus.LOADED.intCode());
        if (query.getIsCurrentTime()) {
            apvStatusList.add(AsnPrepareStatus.WAITING_GEN.intCode());
            apvStatusList.add(AsnPrepareStatus.SINGLETON_TOUCHING.intCode());
            apvStatusList.add(AsnPrepareStatus.EXCESSIVE_PARTS_TOUCHING.intCode());
            apvStatusList.add(AsnPrepareStatus.MULTI_TOUCHING.intCode());
            apvStatusList.add(AsnPrepareStatus.CHECK_PRINT.intCode());
            apvStatusList.add(AsnPrepareStatus.WAITING_DELIVER.intCode());
        }
        request.addDataParam("fba_status_condition", DataType.INT, apvStatusList);
        if (isAll) {
            String dateFormat = query.getDateFormat();
            if (StringUtils.isBlank(dateFormat)) {// 出库总作业报表
                request.addSqlDataParam("packing_date",
                        ", DATE_FORMAT(track.pack_finish_time, '%Y-%m-%d') AS packingDate");
                request.addSqlDataParam("group_by_packing_date", "GROUP BY track.pack_user, packingDate");
            }
            else {// 出库看板
                request.addSqlDataParam("packing_date",
                        ", DATE_FORMAT(track.pack_finish_time, '" + dateFormat + "') AS packingDate");
                request.addSqlDataParam("group_by_packing_date", "GROUP BY packingDate");
            }
        }
        else {// 出库作业报表
            request.addSqlDataParam("group_by_packing_date", "GROUP BY track.pack_user");
        }
        List<CheckoutStatistics> statisticList=  SqlerTemplate.query(request, (rs, i) -> {
            CheckoutStatistics statistics = new CheckoutStatistics();
            statistics.setUserId(rs.getObject("userId") == null ? null : rs.getInt("userId"));
            statistics.setUserName(rs.getString("userName"));
            statistics.setName(rs.getString("name"));
            if (isAll) {
                statistics.setPackingDate(rs.getString("packingDate"));
            }
            statistics.setAsnPackingSsCount(rs.getLong("asnPackingSsCount"));
            statistics.setAsnPackingSsWashingCount(rs.getLong("asnPackingSsWashingCount"));
            statistics.setAsnPackingSmCount(rs.getLong("asnPackingSmCount"));
            statistics.setAsnPackingSmWashingCount(rs.getLong("asnPackingSmWashingCount"));
            statistics.setAsnPackingMmCount(rs.getLong("asnPackingMmCount"));
            statistics.setAsnPackingMmWashingCount(rs.getLong("asnPackingMmWashingCount"));
            statistics.setAsnPackingSsPcsCount(rs.getLong("asnPackingSsPcsCount"));
            statistics.setAsnPackingSsWashingPcsCount(rs.getLong("asnPackingSsWashingPcsCount"));
            statistics.setAsnPackingSmPcsCount(rs.getLong("asnPackingSmPcsCount"));
            statistics.setAsnPackingSmWashingPcsCount(rs.getLong("asnPackingSmWashingPcsCount"));
            statistics.setAsnPackingMmPcsCount(rs.getLong("asnPackingMmPcsCount"));
            statistics.setAsnPackingMmWashingPcsCount(rs.getLong("asnPackingMmWashingPcsCount"));
            return statistics;
        });

        request.setNamingLabel("queryWarehousePackingCheckoutStatistics");

        List<CheckoutStatistics> checkoutStatisticsList=  SqlerTemplate.query(request, (rs, i) -> {
            CheckoutStatistics statistics = new CheckoutStatistics();
            statistics.setUserId(rs.getObject("userId") == null ? null : rs.getInt("userId"));
            statistics.setUserName(rs.getString("userName"));
            statistics.setName(rs.getString("name"));
            if (isAll) {
                statistics.setPackingDate(rs.getString("packingDate"));
            }
            statistics.setWarehousePackingSmCount(rs.getLong("warehousePackingSmCount"));
            statistics.setWarehousePackingSsCount(rs.getLong("warehousePackingSsCount"));
            statistics.setWarehousePackingMmCount(rs.getLong("warehousePackingMmCount"));
            statistics.setWarehousePackingSmPcsCount(rs.getLong("warehousePackingSmPcsCount"));
            statistics.setWarehousePackingSsPcsCount(rs.getLong("warehousePackingSsPcsCount"));
            statistics.setWarehousePackingMmPcsCount(rs.getLong("warehousePackingMmPcsCount"));
            return statistics;
        });

        request.setNamingLabel("queryAsnFirstPackingCheckoutStatistics");
        setTransferQueryCondition(request, 4);

        List<CheckoutStatistics> asnFirstList=  SqlerTemplate.query(request, (rs, i) -> {
            CheckoutStatistics statistics = new CheckoutStatistics();
            statistics.setUserId(rs.getObject("userId") == null ? null : rs.getInt("userId"));
            statistics.setUserName(rs.getString("userName"));
            statistics.setName(rs.getString("name"));
            if (isAll) {
                statistics.setPackingDate(rs.getString("packingDate"));
            }
            statistics.setAsnFirstPackCount(rs.getLong("asnFirstPackCount"));
            statistics.setAsnFirstPackPcsCount(rs.getLong("asnFirstPackPcsCount"));
            return statistics;
        });

        List<CheckoutStatistics> mergePackingList = checkoutStatisticsService.mergePackingData(statisticList, checkoutStatisticsList);

        List<CheckoutStatistics> statisticsList = mergeAsnFirstPackQuantity(mergePackingList, asnFirstList);

        if (isAll) {
            String dateFormat = query.getDateFormat();
            if (StringUtils.isBlank(dateFormat)) {// 出库总作业报表
                request.addSqlDataParam("temu_packing_date",
                        ", DATE_FORMAT(wfa.grid_time, '%Y-%m-%d') AS temuPackingDate");
                request.addSqlDataParam("temu_group_by_packing_date", "GROUP BY wfa.sow_user, temuPackingDate");
            }
            else {// 出库看板
                request.addSqlDataParam("temu_packing_date",
                        ", DATE_FORMAT(wfa.grid_time, '" + dateFormat + "') AS temuPackingDate");
                request.addSqlDataParam("temu_group_by_packing_date", "GROUP BY temuPackingDate");
            }
        }
        else {// 出库作业报表
            request.addSqlDataParam("temu_group_by_packing_date", "GROUP BY wfa.sow_user");
        }


        // 拼多多
        request.setNamingLabel("queryTemuSoftCheckoutStatistics");
        List<CheckoutStatistics> statisticss = SqlerTemplate.query(request, (rs, rowNum) -> {
                CheckoutStatistics  checkoutStatistics = new CheckoutStatistics();
                checkoutStatistics.setUserId(rs.getObject("userId") == null ? null : rs.getInt("userId"));
                checkoutStatistics.setUserName(rs.getString("userName"));
                checkoutStatistics.setName(rs.getString("name"));
                if (isAll) {
                    checkoutStatistics.setPackingDate(rs.getString("temuPackingDate"));
                }
                checkoutStatistics.setTemuPcsCount(rs.getLong("softPcsCount"));
                checkoutStatistics.setTemuSingCount(rs.getLong("softSingCount"));
              //  checkoutStatistics.setTemuSkuCount(rs.getLong("softSkuCount"));
                checkoutStatistics.setTemuPcsSsCount(rs.getLong("softPcsSsCount"));
                checkoutStatistics.setTemuSingSsCount(rs.getLong("softSingSsCount"));
              //  checkoutStatistics.setTemuSkuSsCount(rs.getLong("softSkuSsCount"));
                checkoutStatistics.setTemuPcsSmCount(rs.getLong("softPcsSmCount"));
                checkoutStatistics.setTemuSingSmCount(rs.getLong("softSingSmCount"));
              //  checkoutStatistics.setTemuSkuSmCount(rs.getLong("softSkuSmCount"));
                return checkoutStatistics;
        });
        return mergeTemuFirstPackQuantity(statisticsList, statisticss);
    }

    public List<CheckoutStatistics> mergeTemuFirstPackQuantity(List<CheckoutStatistics> mergePackingList,
                                                              List<CheckoutStatistics> asnFirstList) {
        if (CollectionUtils.isEmpty(mergePackingList)) {
            return asnFirstList;
        }
        if (CollectionUtils.isEmpty(asnFirstList)) {
            return mergePackingList;
        }
        Map<String, CheckoutStatistics> mergeMap = mergePackingList.stream()
                .collect(Collectors.toMap(s -> s.getUserId() + s.getPackingDate(), item -> item));
        Set<String> skuSet = mergeMap.keySet();
        asnFirstList.forEach(s -> {
            String key = s.getUserId() + s.getPackingDate();
            if (skuSet.contains(key)) {
                CheckoutStatistics merged = mergeMap.get(key);
                merged.setTemuSingCount(s.getTemuSingCount());
                //merged.setTemuSkuCount(s.getTemuSkuCount());
                merged.setTemuPcsCount(s.getTemuPcsCount());
                merged.setTemuSingSsCount(s.getTemuSingSsCount());
                //merged.setTemuSkuSsCount(s.getTemuSkuSsCount());
                merged.setTemuPcsSsCount(s.getTemuPcsSsCount());
                merged.setTemuSingSmCount(s.getTemuSingSmCount());
                //merged.setTemuSkuSmCount(s.getTemuSkuSmCount());
                merged.setTemuPcsSmCount(s.getTemuPcsSmCount());
            }
            else {
                mergeMap.put(key, s);
            }
        });
        return new ArrayList<>(mergeMap.values());
    }

    public List<CheckoutStatistics> mergeAsnFirstPackQuantity(List<CheckoutStatistics> mergePackingList,
                                                         List<CheckoutStatistics> asnFirstList) {
        if (CollectionUtils.isEmpty(mergePackingList)) {
            return asnFirstList;
        }
        if (CollectionUtils.isEmpty(asnFirstList)) {
            return mergePackingList;
        }
        Map<String, CheckoutStatistics> mergeMap = mergePackingList.stream()
                .collect(Collectors.toMap(s -> s.getUserId() + s.getPackingDate(), item -> item));
        Set<String> skuSet = mergeMap.keySet();
        asnFirstList.forEach(s -> {
            String key = s.getUserId() + s.getPackingDate();
            if (skuSet.contains(key)) {
                CheckoutStatistics merged = mergeMap.get(key);
                merged.setAsnFirstPackCount(s.getAsnFirstPackCount());
                merged.setAsnFirstPackPcsCount(s.getAsnFirstPackPcsCount());
            }
            else {
                mergeMap.put(key, s);
            }
        });
        return new ArrayList<>(mergeMap.values());
    }

    @Override
    public List<CheckoutStatistics> queryScanCheckoutStatistics(CheckoutStatisticsQueryCondition query, boolean isAll) {
        SqlerRequest request = new SqlerRequest("queryScanCheckoutStatisticsList");
        setQueryCondition(request, query);
        if (isAll) {
            String dateFormat = query.getDateFormat();
            if (StringUtils.isBlank(dateFormat)) {// 出库总作业报表
                request.addSqlDataParam("scan_date", ", DATE_FORMAT(track.deliver_time, '%Y-%m-%d') AS scanDate");
                request.addSqlDataParam("group_by_scan_date", "GROUP BY track.deliver_user, scanDate");
            }
            else {// 出库看板
                request.addSqlDataParam("scan_date",
                        ", DATE_FORMAT(track.deliver_time, '" + dateFormat + "') AS scanDate");
                request.addSqlDataParam("group_by_scan_date", "GROUP BY scanDate");
            }
        }
        else {// 出库作业报表
            request.addSqlDataParam("group_by_scan_date", "GROUP BY track.deliver_user");
        }
        return SqlerTemplate.query(request, new CheckoutStatisticsMapper(CheckoutStatisticsEnum.SCAN, isAll));
    }

    // 出库看板
    @Override
    public List<CheckoutStatistics> queryScanCheckoutPanelStatistics(CheckoutStatisticsQueryCondition query) {
        SqlerRequest request = new SqlerRequest("queryScanCheckoutPanelStatistics");
        setQueryCondition(request, query);
        request.addSqlDataParam("scan_date",
                ", DATE_FORMAT(track.deliver_time, '" + query.getDateFormat() + "') AS scanDate");
        request.addSqlDataParam("group_by_scan_date", "GROUP BY scanDate");
        return SqlerTemplate.query(request, (rs, i) -> {
            CheckoutStatistics entity = new CheckoutStatistics();
            entity.setUserId(rs.getObject("userId") == null ? null : rs.getInt("userId"));
            entity.setUserName(rs.getString("userName"));
            entity.setName(rs.getString("name"));
            entity.setScanDate(rs.getString("scanDate"));
            entity.setScanSingCount(rs.getLong("scanSingCount"));
            entity.setScanJitOrderCount(rs.getLong("scanJitOrderCount"));
            return entity;
        });
    }

    @Override
    public List<CheckoutStatistics> queryScanTransferCheckoutStatistics(CheckoutStatisticsQueryCondition query, boolean isAll) {
        SqlerRequest request = new SqlerRequest("queryScanTransferCheckoutStatistics");
        setQueryCondition(request, query);
        if (isAll) {
            String dateFormat = query.getDateFormat();
            if (StringUtils.isBlank(dateFormat)) {// 出库总作业报表
                request.addSqlDataParam("scan_date", ", DATE_FORMAT(fa.deliver_time, '%Y-%m-%d') AS scanDate");
                request.addSqlDataParam("group_by_scan_date", "GROUP BY fa.deliver_by, scanDate");
            }
            else {// 出库看板
                request.addSqlDataParam("scan_date",
                        ", DATE_FORMAT(fa.deliver_time, '" + dateFormat + "') AS scanDate");
                request.addSqlDataParam("group_by_scan_date", "GROUP BY scanDate");
            }
        }
        else {// 出库作业报表
            request.addSqlDataParam("group_by_scan_date", "GROUP BY fa.deliver_by");
        }
        List<CheckoutStatistics> checkoutStatisticList = SqlerTemplate.query(request, new CheckoutTransferStatisticsMapper(CheckoutStatisticsEnum.SCAN, isAll));


        Map<String, CheckoutStatistics> asnFbaSkuCountMap = null;
        if (isAll) {
            asnFbaSkuCountMap = CollectionUtils.isEmpty(checkoutStatisticList) ? new HashMap<>()
                    : checkoutStatisticList.stream()
                    .collect(Collectors.toMap(item -> item.getUserId() + item.getScanDate(), item -> item));
        }else {
            asnFbaSkuCountMap = CollectionUtils.isEmpty(checkoutStatisticList) ? new HashMap<>()
                    : checkoutStatisticList.stream().collect(Collectors.toMap(item -> item.getUserId().toString(), item -> item));
        }

        //仓发
        request.setNamingLabel("queryScanWarehouseCheckoutStatistics");
        Map<String, CheckoutStatistics> finalAsnFbaSkuCountMap = asnFbaSkuCountMap;
        SqlerTemplate.query(request,
                (rs, rowNum) -> {
                    Integer userId = (rs.getObject("userId") == null) ? null : rs.getInt("userId");
                    CheckoutStatistics checkoutStatistics = null;
                    if (userId != null){
                        if (isAll) {
                            String softDate = rs.getString("scanDate");
                            checkoutStatistics = finalAsnFbaSkuCountMap.get(userId + softDate);
                        }else {
                            checkoutStatistics = finalAsnFbaSkuCountMap.get(userId.toString());
                        }
                        if (checkoutStatistics == null) {
                            checkoutStatistics = new CheckoutStatistics();
                            checkoutStatistics.setUserId(rs.getObject("userId") == null ? null : rs.getInt("userId"));
                            checkoutStatistics.setUserName(rs.getString("userName"));
                            checkoutStatistics.setName(rs.getString("name"));
                            if (isAll) {
                                checkoutStatistics.setScanDate(rs.getString("scanDate"));
                                finalAsnFbaSkuCountMap.put(userId + checkoutStatistics.getScanDate(), checkoutStatistics);
                            }else {
                                finalAsnFbaSkuCountMap.put(userId.toString(), checkoutStatistics);
                            }
                        }
                        checkoutStatistics.setWarehouseOrderCount(rs.getLong("warehouseOrderCount"));
                        checkoutStatistics.setWarehouseOrderPcsCount(rs.getLong("warehouseOrderPcsCount"));
                    }
                    return checkoutStatistics;
                });
        return finalAsnFbaSkuCountMap.entrySet().stream().map(e -> e.getValue()).collect(Collectors.toList());
    }

    @Override
    public List<CheckoutStatistics> queryScanTemuCheckoutStatistics(CheckoutStatisticsQueryCondition query, boolean isAll) {
        SqlerRequest request = new SqlerRequest("queryScanTemuCheckoutStatistics");
        setQueryCondition(request, query);
        if (isAll) {
            String dateFormat = query.getDateFormat();
            if (StringUtils.isBlank(dateFormat)) {// 出库总作业报表
                request.addSqlDataParam("scan_date", ", DATE_FORMAT(i.deliver_time, '%Y-%m-%d') AS scanDate");
                request.addSqlDataParam("group_by_scan_date", "GROUP BY i.deliver_by, scanDate");
            }
            else {// 出库看板
                request.addSqlDataParam("scan_date",
                        ", DATE_FORMAT(i.deliver_time, '" + dateFormat + "') AS scanDate");
                request.addSqlDataParam("group_by_scan_date", "GROUP BY scanDate");
            }
        }
        else {// 出库作业报表
            request.addSqlDataParam("group_by_scan_date", "GROUP BY i.deliver_by");
        }
        return SqlerTemplate.query(request, new CheckoutTemuStatisticsMapper(CheckoutStatisticsEnum.SCAN, isAll));
    }

    @Override
    public List<CheckoutStatistics> queryScanPackageCardStatistics(CheckoutStatisticsQueryCondition query, boolean isAll) {
        SqlerRequest request = new SqlerRequest("queryScanPackageCardStatistics");
        setQueryCondition(request, query);
        if (isAll) {
            String dateFormat = query.getDateFormat();
            if (StringUtils.isBlank(dateFormat)) {// 出库总作业报表
                request.addSqlDataParam("scan_date", ", DATE_FORMAT(wss.scan_date, '%Y-%m-%d') AS scanDate");
                request.addSqlDataParam("group_by_scan_date", "GROUP BY wss.scanner, scanDate");
            }
            else {// 出库看板
                request.addSqlDataParam("scan_date",
                        ", DATE_FORMAT(wss.scan_date, '" + dateFormat + "') AS scanDate");
                request.addSqlDataParam("group_by_scan_date", "GROUP BY scanDate");
            }
        }
        else {// 出库作业报表
            request.addSqlDataParam("group_by_scan_date", "GROUP BY wss.scanner");
        }
        return SqlerTemplate.query(request, new CheckoutPackageCardStatisticsMapper(CheckoutStatisticsEnum.SCAN, isAll));
    }

    @Override
    public List<CheckoutStatistics> queryLoadingPackageCardStatistics(CheckoutStatisticsQueryCondition query, boolean isAll) {
        SqlerRequest request = new SqlerRequest("queryLoadingPackageCardStatistics");
        setQueryCondition(request, query);
        if (isAll) {
            String dateFormat = query.getDateFormat();
            if (StringUtils.isBlank(dateFormat)) {// 出库总作业报表
                request.addSqlDataParam("loading_date", ", DATE_FORMAT(wss.load_date, '%Y-%m-%d') AS loadingDate");
                request.addSqlDataParam("group_by_loading_date", "GROUP BY wss.load_user, loadingDate");
            }
            else {// 出库看板
                request.addSqlDataParam("loading_date",
                        ", DATE_FORMAT(wss.load_date, '" + dateFormat + "') AS loadingDate");
                request.addSqlDataParam("group_by_loading_date", "GROUP BY loadingDate");
            }
        }
        else {// 出库作业报表
            request.addSqlDataParam("group_by_loading_date", "GROUP BY wss.load_user");
        }
        return SqlerTemplate.query(request, new CheckoutPackageCardStatisticsMapper(CheckoutStatisticsEnum.LOADING, isAll));
    }

    @Override
    public List<CheckoutStatistics> queryLoadingCheckoutStatistics(CheckoutStatisticsQueryCondition query,
            boolean isAll) {
        SqlerRequest request = new SqlerRequest("queryLoadingCheckoutStatisticsList");
        setQueryCondition(request, query);
        if (isAll) {
            String dateFormat = query.getDateFormat();
            if (StringUtils.isBlank(dateFormat)) {// 出库总作业报表
                request.addSqlDataParam("loading_date", ", DATE_FORMAT(track.load_date, '%Y-%m-%d') AS loadingDate");
                request.addSqlDataParam("group_by_loading_date", "GROUP BY track.load_user, loadingDate");

                request.addSqlDataParam("warehouse_loading_date", ", DATE_FORMAT(wfa.load_time, '%Y-%m-%d') AS loadingDate");
                request.addSqlDataParam("warehouse_group_by_loading_date", "GROUP BY wfa.load_by, loadingDate");
            }
            else {// 出库看板
                request.addSqlDataParam("loading_date",
                        ", DATE_FORMAT(track.load_date, '" + dateFormat + "') AS loadingDate");
                request.addSqlDataParam("group_by_loading_date", "GROUP BY loadingDate");

                request.addSqlDataParam("warehouse_loading_date",
                        ", DATE_FORMAT(wfa.load_time, '" + dateFormat + "') AS loadingDate");
                request.addSqlDataParam("warehouse_group_by_loading_date", "GROUP BY loadingDate");
            }
        }
        else {// 出库作业报表
            request.addSqlDataParam("group_by_loading_date", "GROUP BY track.load_user");

            request.addSqlDataParam("warehouse_group_by_loading_date", "GROUP BY wfa.load_by");
        }
        List<CheckoutStatistics> checkoutStatisticList = SqlerTemplate.query(request, new CheckoutStatisticsMapper(CheckoutStatisticsEnum.LOADING, isAll));

        Map<String, CheckoutStatistics> asnFbaSkuCountMap=null;
        if (isAll) {
            asnFbaSkuCountMap = CollectionUtils.isEmpty(checkoutStatisticList) ? new HashMap<>()
                    : checkoutStatisticList.stream()
                    .collect(Collectors.toMap(item -> item.getUserId() + item.getLoadingDate(), item -> item));
        }else {
            asnFbaSkuCountMap = CollectionUtils.isEmpty(checkoutStatisticList) ? new HashMap<>()
                    : checkoutStatisticList.stream().collect(Collectors.toMap(item -> item.getUserId().toString(), item -> item));
        }
        //仓发
        request.setNamingLabel("queryWarehouseCheckoutStatistics");
        Map<String, CheckoutStatistics> finalAsnFbaSkuCountMap = asnFbaSkuCountMap;
        SqlerTemplate.query(request,
                (rs, rowNum) -> {
                    Integer userId = (rs.getObject("userId") == null) ? null : rs.getInt("userId");
                    CheckoutStatistics checkoutStatistics = null;
                    if (userId != null){
                        if (isAll) {
                            String softDate = rs.getString("loadingDate");
                            checkoutStatistics = finalAsnFbaSkuCountMap.get(userId + softDate);
                        }else {
                            checkoutStatistics = finalAsnFbaSkuCountMap.get(userId.toString());
                        }
                        if (checkoutStatistics == null) {
                            checkoutStatistics = new CheckoutStatistics();
                            checkoutStatistics.setUserId(rs.getObject("userId") == null ? null : rs.getInt("userId"));
                            checkoutStatistics.setUserName(rs.getString("userName"));
                            checkoutStatistics.setName(rs.getString("name"));
                            if (isAll) {
                                checkoutStatistics.setLoadingDate(rs.getString("loadingDate"));
                                finalAsnFbaSkuCountMap.put(userId + checkoutStatistics.getScanDate(), checkoutStatistics);
                            }else {
                                finalAsnFbaSkuCountMap.put(userId.toString(), checkoutStatistics);
                            }
                        }
                        checkoutStatistics.setWarehouseLoadingSingCount(rs.getLong("warehouseLoadingSingCount"));
                        checkoutStatistics.setWarehouseLoadingWeight(rs.getDouble("warehouseLoadingWeight"));
                    }
                    return checkoutStatistics;
                });
        return finalAsnFbaSkuCountMap.entrySet().stream().map(e -> e.getValue()).collect(Collectors.toList());
    }
    
    @Override
    public List<CheckoutStatistics> queryPickCheckoutStatisticsList(CheckoutStatisticsQueryCondition query) {
        SqlerRequest request = new SqlerRequest("queryPickCheckoutStatisticsList");
        setQueryCondition(request, query);
        return SqlerTemplate.query(request, new CheckoutStatisticsMapper(CheckoutStatisticsEnum.PICK, true));
    }

    @Override
    public List<CheckoutStatistics> queryFbaTagStatistics(CheckoutStatisticsQueryCondition query, boolean isAll) {
        SqlerRequest request = new SqlerRequest("queryFbaFnSkuTagStatisticsList");
        handelFbaTagParam(query, isAll, request);
        List<CheckoutStatistics> fnSkuTagList = SqlerTemplate.query(request,
                new CheckoutStatisticsMapper(CheckoutStatisticsEnum.LABEL, isAll, true));
        List<CheckoutStatistics> skuTagList = queryFbaSkuTagStatistics(query, isAll);
        if (CollectionUtils.isEmpty(fnSkuTagList))
            return new ArrayList<>();
        Map<String, CheckoutStatistics> skuTagMap = Optional.ofNullable(skuTagList).orElse(new ArrayList<>()).stream()
                .collect(Collectors.toMap(s -> s.getUserId() + s.getTagDate(), s -> s));
        fnSkuTagList.forEach(f -> {
            CheckoutStatistics skuEntity = skuTagMap.get(f.getUserId() + f.getTagDate());
            f.setPtSkuCount(skuEntity == null || skuEntity.getPtSkuCount() == null ? 0 : skuEntity.getPtSkuCount());
            f.setPltSkuCount(skuEntity == null || skuEntity.getPltSkuCount() == null ? 0 : skuEntity.getPltSkuCount());
            f.setPwtSkuCount(skuEntity == null || skuEntity.getPwtSkuCount() == null ? 0 : skuEntity.getPwtSkuCount());
            f.setPtPcsCount(skuEntity == null || skuEntity.getPtPcsCount() == null ? 0 : skuEntity.getPtPcsCount());
            f.setPltPcsCount(skuEntity == null || skuEntity.getPltPcsCount() == null ? 0 : skuEntity.getPltPcsCount());
            f.setPwtPcsCount(skuEntity == null || skuEntity.getPwtPcsCount() == null ? 0 : skuEntity.getPwtPcsCount());
        });
        return fnSkuTagList;
    }

    private List<CheckoutStatistics> queryFbaSkuTagStatistics(CheckoutStatisticsQueryCondition query, boolean isAll) {
        SqlerRequest request = new SqlerRequest("queryFbaSkuTagStatistics");
        handelFbaTagParam(query, isAll, request);
        return SqlerTemplate
                .query(request, new CheckoutStatisticsMapper(CheckoutStatisticsEnum.LABEL, isAll));
    }

    private void handelFbaTagParam(CheckoutStatisticsQueryCondition query, boolean isAll, SqlerRequest request) {
        setQueryCondition(request, query);
        if (isAll) {
            String dateFormat = query.getDateFormat();
            if (StringUtils.isBlank(dateFormat)) {// 出库总作业报表
                request.addSqlDataParam("tag_date", ", DATE_FORMAT(wfai.tag_time, '%Y-%m-%d') AS tagDate");
                request.addSqlDataParam("group_by_tag_date", "GROUP BY wfai.tag_by, tagDate");
            }
            else {// 出库看板
                request.addSqlDataParam("tag_date",
                        ", DATE_FORMAT(wfai.tag_date, '" + dateFormat + "') AS tagDate");
                request.addSqlDataParam("group_by_tag_date", "GROUP BY tagDate");
            }
        }
        else {// 出库作业报表
            request.addSqlDataParam("group_by_tag_date", "GROUP BY wfai.tag_by");
        }
    }

    /**
     * 分拣
     *
     * @param query
     * @param isAll
     * @return
     */
    @Override
    public List<CheckoutStatistics> querySortingStatistics(CheckoutStatisticsQueryCondition query, boolean isAll) {
        SqlerRequest request = new SqlerRequest("queryJitPickStatistics");
        handelSortingParam(query, isAll, request);
        /** jit分拣数据 */
        List<CheckoutStatistics> jitPickList = SqlerTemplate.query(request, new RowMapper<CheckoutStatistics>() {
            @Override
            public CheckoutStatistics mapRow(ResultSet rs, int rowNum) throws SQLException {
                CheckoutStatistics entity = new CheckoutStatistics();
                entity.setUserId(rs.getObject("userId") == null ? null : rs.getInt("userId"));
                entity.setUserName(rs.getString("userName"));
                entity.setName(rs.getString("name"));
                if (isAll) {
                    entity.setSortingDate(rs.getString("sortingDate"));
                }
                entity.setJitPickOrderCount(rs.getLong("jitPickOrderCount"));
                entity.setJitPickSkuCount(rs.getLong("jitPickSkuCount"));
                return entity;
            }
        });

        request = new SqlerRequest("queryPddPickStatistics");
        handelSortingParam(query, isAll, request);

        List<CheckoutStatistics> pddPickList = SqlerTemplate.query(request, new RowMapper<CheckoutStatistics>() {
            @Override
            public CheckoutStatistics mapRow(ResultSet rs, int rowNum) throws SQLException {
                CheckoutStatistics entity = new CheckoutStatistics();
                entity.setUserId(rs.getObject("userId") == null ? null : rs.getInt("userId"));
                entity.setUserName(rs.getString("userName"));
                entity.setName(rs.getString("name"));
                if (isAll) {
                    entity.setSortingDate(rs.getString("sortingDate"));
                }
                entity.setPddPickOrderCount(rs.getLong("pddPickOrderCount"));
                entity.setPddPickSkuCount(rs.getLong("pddPickSkuCount"));
                return entity;
            }
        });
        return mergeJitPickQuantity(jitPickList, pddPickList);
    }

    @Override
    public List<CheckoutStatistics> queryDirectPackingCheckoutStatisticsList(CheckoutStatisticsQueryCondition query, boolean isAll) {
        SqlerRequest request = new SqlerRequest("queryDirectPackingCheckoutStatisticsList");
        setQueryCondition(request, query);
        if (isAll) {
            String dateFormat = query.getDateFormat();
            if (StringUtils.isBlank(dateFormat)) {// 出库总作业报表
                request.addSqlDataParam("packing_date",
                        ", DATE_FORMAT(track.pack_finish_time, '%Y-%m-%d') AS packingDate");
                request.addSqlDataParam("group_by_packing_date", "GROUP BY track.pack_user, packingDate");
            }
            else {// 出库看板
                request.addSqlDataParam("packing_date",
                        ", DATE_FORMAT(track.pack_finish_time, '" + dateFormat + "') AS packingDate");
                request.addSqlDataParam("group_by_packing_date", "GROUP BY packingDate");
            }
        }
        else {// 出库作业报表
            request.addSqlDataParam("group_by_packing_date", "GROUP BY track.pack_user");
        }
        return SqlerTemplate.query(request, new RowMapper<CheckoutStatistics>() {
            @Override
            public CheckoutStatistics mapRow(ResultSet rs, int i) throws SQLException {
                CheckoutStatistics entity = new CheckoutStatistics();
                entity.setUserId(rs.getInt("userId"));
                entity.setUserName(rs.getString("userName"));
                entity.setName(rs.getString("name"));
                entity.setDirectPackingSsSingCount(rs.getLong("directPackingSsSingCount"));
                entity.setDirectPackingSmSingCount(rs.getLong("directPackingSmSingCount"));
                entity.setDirectPackingMmSingCount(rs.getLong("directPackingMmSingCount"));
                entity.setDirectPackingSsPcsCount(rs.getLong("directPackingSsPcsCount"));
                entity.setDirectPackingSmPcsCount(rs.getLong("directPackingSmPcsCount"));
                entity.setDirectPackingMmPcsCount(rs.getLong("directPackingMmPcsCount"));
                if (isAll) {
                    entity.setPackingDate(rs.getString("packingDate"));
                }
                return entity;
            }
        });

    }

    /**
     * //1.中转仓、2.拼多多、3.fba、4.海外仓
     * @param request
     * @param type
     */
    private void setTransferQueryCondition(SqlerRequest request,Integer type) {
        if (type == null) {
            return;
        }

        switch (type) {
            case 1:
                // 中转仓
                request.addSqlDataParam("TRANSFER_CONDITION", " AND fba.id IN (" +
                        " SELECT wh_asn_id " +
                        " FROM wh_asn_extra ae " +
                        " WHERE (fba.purpose_house, ae.package_method) IN (('Shein', 8), ('Shein', 9)))");
                List<String> transferOrderChannels = Arrays.asList(SaleChannel.CHANNEL_SMT, SaleChannel.CHANNEL_SHEIN);
                request.addDataParam("transferPurposeHouseList", DataType.STRING, transferOrderChannels);
                break;
            case 3:
                //fba
                request.addDataParam("purposeHouseNotInList", DataType.STRING, SaleChannel.saleChannels);
                break;
            case 4:
                String transferOrderTypes = "('"+SaleChannel.CHANNEL_SMT+"',"+ AsnPackageMethodEnum.JIT.getCode()+"),"
                        + "('"+SaleChannel.CHANNEL_SMT+"',"+ AsnPackageMethodEnum.JIT_HALF.getCode()+"),"
                        + "('"+SaleChannel.CHANNEL_SHEIN+"',"+ AsnPackageMethodEnum.URGENT.getCode()+"),"
                        + "('"+SaleChannel.CHANNEL_SHEIN+"',"+ AsnPackageMethodEnum.BACKUP.getCode()+")";
                //海外仓
                request.addDataParam("asnPurposeHouseList", DataType.STRING, SaleChannel.saleChannels);
                // 排除中转仓订单的数据
                request.addSqlDataParam("ASN_TRANSFER_ORDER_FILTER"," AND fba.id IN (SELECT wh_asn_id FROM wh_asn_extra ae WHERE (fba.purpose_house,ae.package_method) NOT IN ("+transferOrderTypes+") " +
                        " OR ae.package_method IS NULL ) ");
                break;
            case 5:
                // 仓发
                request.addDataParam("transferPurposeHouseList", DataType.STRING, SaleChannel.CHANNEL_SMT);
                String whTypes = "('"+SaleChannel.CHANNEL_SMT+"',"+ AsnPackageMethodEnum.JIT.getCode()+"),"
                    + "('"+SaleChannel.CHANNEL_SMT+"',"+ AsnPackageMethodEnum.JIT_HALF.getCode()+"),"
                    + "('"+SaleChannel.CHANNEL_SMT+"',"+ AsnPackageMethodEnum.SMT_PRIORITY_WAREHOUSE.getCode()+"),"
                    + "('"+SaleChannel.CHANNEL_SMT+"',"+ AsnPackageMethodEnum.SMT_ABROAD_WAREHOUSE.getCode()+")";
                request.addSqlDataParam("TRANSFER_CONDITION"," AND fba.id IN (SELECT wh_asn_id FROM wh_asn_extra ae WHERE (fba.purpose_house,ae.package_method) IN ("+whTypes+") " +
                        " OR ae.package_method IS NULL )");
                break;
        }

        request.addDataParam("transfer_status_condition", DataType.INT,Arrays.asList(
                AsnPrepareStatus.LOADED.intCode(),
                AsnPrepareStatus.DELIVER.intCode(),
                AsnPrepareStatus.SINGLETON_TOUCHING.intCode(),
                AsnPrepareStatus.CHECK_PRINT.intCode(),
                AsnPrepareStatus.MULTI_TOUCHING.intCode(),
                AsnPrepareStatus.WAITING_DELIVER.intCode(),
                AsnPrepareStatus.EXCESSIVE_PARTS_TOUCHING.intCode(),
                AsnPrepareStatus.WAITING_CONFIRM.intCode(),
                AsnPrepareStatus.WAITING_BOX.intCode(),
                AsnPrepareStatus.WAITING_CHECK.intCode(),
                AsnPrepareStatus.WAITING_LABEL.intCode(),
                AsnPrepareStatus.WAITING_GRID.intCode(),
                AsnPrepareStatus.WAITING_PICK.intCode()));


        request.addDataParam("pdd_status_condition", DataType.INT, Arrays.asList(
                TemuPackageStatus.LOADED.intCode(),
                TemuPackageStatus.DELIVER.intCode(),
                TemuPackageStatus.WAITING_DELIVER.intCode(),
                TemuPackageStatus.WAIT_SORTING.intCode(),
                TemuPackageStatus.WAIT_CHECK.intCode(),
                TemuPackageStatus.WAITING_GRID.intCode(),
                TemuPackageStatus.PICKING.intCode(),
                TemuPackageStatus.MERGED.intCode()));


    }


    @Override
    public List<CheckoutStatistics> queryTransferPickCheckoutStatisticsList(CheckoutStatisticsQueryCondition queryCondition) {
        if (queryCondition.getType() ==  null){
            return new ArrayList<>();
        }
        SqlerRequest request = new SqlerRequest("queryTransferPickCheckoutStatisticsList");
        if (CheckInWhType.TEMU.intCode().equals(queryCondition.getType())) {
            request = new SqlerRequest("queryTemuPickCheckoutStatisticsList");
        }
        setQueryCondition(request, queryCondition);
        return SqlerTemplate.query(request, new CheckoutStatisticsMapper(CheckoutStatisticsEnum.PICK, true,false,queryCondition.getType()));
    }

    @Override
    public List<CheckoutStatistics> queryTransferSoftCheckoutStatistics(CheckoutStatisticsQueryCondition queryCondition, boolean isAll, Integer type) {
        SqlerRequest request = new SqlerRequest("queryTransferSoftCheckoutStatisticList");
        setQueryCondition(request, queryCondition);
        setTransferQueryCondition(request,type);
        return SqlerTemplate.query(request, new CheckoutStatisticsMapper(CheckoutStatisticsEnum.SOFT, isAll,false,queryCondition.getType()));
    }

    @Override
    public List<CheckoutStatistics> queryTransferPackingCheckoutStatistics(CheckoutStatisticsQueryCondition queryCondition,Integer type) {
        if (type == null){
            return new ArrayList<>();
        }
        SqlerRequest request = new SqlerRequest("queryFbaPackingCheckoutStatistics");
        if(type==2){
            // 拼多多
            request = new SqlerRequest("queryPddPackingCheckoutStatistics");
        }
        setQueryCondition(request, queryCondition);
        setTransferQueryCondition(request, type);
        return SqlerTemplate.query(request, new RowMapper<CheckoutStatistics>() {
            @Override
            public CheckoutStatistics mapRow(ResultSet rs, int rowNum) throws SQLException {
                CheckoutStatistics entity = new CheckoutStatistics();
                entity.setUserId(rs.getObject("userId") == null ? null : rs.getInt("userId"));
                entity.setUserName(rs.getString("userName"));
                entity.setName(rs.getString("name"));
                entity.setPackingDate(rs.getString("packingDate"));
                //1.中转仓、2.拼多多、3.fba、4.海外仓、5.仓发
                if (type==1){
                    entity.setAsnPackingSingCount(rs.getLong("asnPackingSingCount"));
                }
                if (type==3){
                    entity.setFbaPackingSingCount(rs.getLong("asnPackingSingCount"));
                }
                if (type==4){
                    entity.setUkPackingSingCount(rs.getLong("asnPackingSingCount"));
                }
                if (type==2){
                    entity.setTemuPackingSingCount(rs.getLong("asnPackingSingCount"));
                }
                if (type == 1 || type == 2 || type == 5) {
                    entity.setPackingTransferSsCount(rs.getLong("packingTransferSsCount"));
                    entity.setPackingTransferSmCount(rs.getLong("packingTransferSmCount"));
                    entity.setPackingTransferMmCount(rs.getLong("packingTransferMmCount"));
                }
                return entity;
            }
        });
    }

    @Override
    public List<CheckoutStatistics> queryTransferCheckoutStatistics(CheckoutStatisticsQueryCondition queryCondition, boolean bool,Integer type) {
        if (type == null){
            return new ArrayList<>();
        }
        SqlerRequest request = new SqlerRequest("queryTransferCheckoutStatistics");
        if(type==2){
            // 拼多多
            request = new SqlerRequest("queryPddCheckoutStatistics");
        }
        setQueryCondition(request, queryCondition);
        setTransferQueryCondition(request, type);
        return SqlerTemplate.query(request, new RowMapper<CheckoutStatistics>() {
            @Override
            public CheckoutStatistics mapRow(ResultSet rs, int rowNum) throws SQLException {
                CheckoutStatistics entity = new CheckoutStatistics();
                entity.setUserId(rs.getObject("userId") == null ? null : rs.getInt("userId"));
                entity.setUserName(rs.getString("userName"));
                entity.setName(rs.getString("name"));
                entity.setLoadingDate(rs.getString("loadingDate"));
                //1.中转仓、2.拼多多、3.fba、4.海外仓
                if (type==1){
                    entity.setTransferLoadingSingCount(rs.getLong("loadingSingCount"));
                    entity.setTransferLoadingWeight(rs.getDouble("loadingWeight"));
                }
                if (type==2){
                    entity.setTemuLoadingSingCount(rs.getLong("loadingSingCount"));
                    entity.setTemuLoadingWeight(rs.getDouble("loadingWeight"));
                }

                return entity;
            }
        });
    }

    // 组装分拣查询参数
    private void handelSortingParam(CheckoutStatisticsQueryCondition query, boolean isAll, SqlerRequest request) {
        setQueryCondition(request, query);
        if (isAll) {
            String dateFormat = query.getDateFormat();
            if (StringUtils.isBlank(dateFormat)) {// 出库总作业报表
                request.addSqlDataParam("sorting_date", ", DATE_FORMAT(p.pick_time, '%Y-%m-%d') AS sortingDate");
                request.addSqlDataParam("group_by_sorting_date", "GROUP BY p.pick_by, sortingDate");
            }
            else {// 出库看板
                request.addSqlDataParam("sorting_date",
                        ", DATE_FORMAT(p.pick_time, '" + dateFormat + "') AS sortingDate");
                request.addSqlDataParam("group_by_sorting_date", "GROUP BY sortingDate");
            }
        }
        else {// 出库作业报表
            request.addSqlDataParam("group_by_sorting_date", "GROUP BY p.pick_by");
        }
    }

    /**
     * 合并jit/pdd分拣数据
     * @param jitPickList
     * @param pddPickList
     * @return
     */
    public List<CheckoutStatistics> mergeJitPickQuantity(List<CheckoutStatistics> jitPickList,
            List<CheckoutStatistics> pddPickList) {
        if (CollectionUtils.isEmpty(jitPickList)) {
            return pddPickList;
        }
        if (CollectionUtils.isEmpty(pddPickList)) {
            return jitPickList;
        }
        Map<String, CheckoutStatistics> map = jitPickList.stream()
                .collect(Collectors.toMap(s -> s.getUserId() + s.getSortingDate(), item -> item));
        Set<String> skuSet = map.keySet();
        pddPickList.forEach(s -> {
            String key = s.getUserId() + s.getSortingDate();
            if (skuSet.contains(key)) {
                CheckoutStatistics jit = map.get(key);
                jit.setPddPickSkuCount(s.getPddPickSkuCount());
                jit.setPddPickOrderCount(s.getPddPickOrderCount());
            }
            else {
                map.put(key, s);
            }
        });
        return new ArrayList<>(map.values());
    }
}
