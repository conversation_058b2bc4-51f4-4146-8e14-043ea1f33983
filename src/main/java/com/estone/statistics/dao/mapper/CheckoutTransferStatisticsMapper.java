package com.estone.statistics.dao.mapper;

import com.estone.statistics.bean.CheckoutStatistics;
import com.estone.statistics.enums.CheckoutStatisticsEnum;
import org.springframework.jdbc.core.RowMapper;

import java.sql.ResultSet;
import java.sql.SQLException;

public class CheckoutTransferStatisticsMapper implements RowMapper<CheckoutStatistics> {

    private CheckoutStatisticsEnum linkTypeEnum;

    private boolean isAll;


    public CheckoutTransferStatisticsMapper() {

    }

    public CheckoutTransferStatisticsMapper(CheckoutStatisticsEnum linkTypeEnum, boolean isAll) {
        this.linkTypeEnum = linkTypeEnum;
        this.isAll = isAll;
    }

    @Override
    public CheckoutStatistics mapRow(ResultSet rs, int rowNum) throws SQLException {
        CheckoutStatistics entity = new CheckoutStatistics();

        entity.setUserId(rs.getInt("userId"));
        entity.setUserName(rs.getString("userName"));
        entity.setName(rs.getString("name"));

        switch (linkTypeEnum) {
            case SCAN: {
                entity.setTransferOrderCount(rs.getLong("transferOrderCount"));
                entity.setTransferOrderPcsCount(rs.getLong("transferOrderPcsCount"));
                if (isAll) {
                    entity.setScanDate(rs.getString("scanDate"));
                }
            }
            break;
        }
        return entity;
    }

}
