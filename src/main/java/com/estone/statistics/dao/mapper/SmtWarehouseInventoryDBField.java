package com.estone.statistics.dao.mapper;

public interface SmtWarehouseInventoryDBField {
    String ID = "id";

    String ACCOUNT_NUMBER = "account_number";

    String PRODUCT_NAME = "product_name";

    String MAIN_ORDER = "main_order";

    String MAIN_SUB_ORDER = "main_sub_order";

    String PRODUCT_ID = "product_id";

    String SCITEM_CODE = "scitem_code";

    String WAREHOUSE_NAME = "warehouse_name";

    String WAREHOUSE_CODE = "warehouse_code";

    String TRADE_ORDER = "trade_order";

    String TRADE_SUB_ORDER = "trade_sub_order";

    String STORE = "store";

    String BIZ_ACTIVITY_TYPE = "biz_activity_type";

    String INVENTORY_TYPE = "inventory_type";
    String ORDER_TYPE = "order_type";

    String CHANGE_QUANTITY = "change_quantity";

    String RESULT_QUANTITY = "result_quantity";

    String BEFORE_QTY = "before_qty";

    String PURCHASE_PRICE = "purchase_price";
    String END_AMOUNT = "end_amount";
    String END_COST = "end_cost";

    String PURCHASE_COST = "purchase_cost";

    String ALLOCATION_PRICE = "allocation_price";

    String RELATION_NO = "relation_no";

    String RELATION_BATCH = "relation_batch";

    String GMT_CREATE = "gmt_create";

    String CRAWL_TIME = "crawl_time";
    String DIFF_TYPE = "diff_type";
}