package com.estone.statistics.dao.mapper;

import com.estone.statistics.bean.AmazonFulfilledShipmentsData;

import java.math.BigDecimal;
import java.sql.ResultSet;
import java.sql.SQLException;
import org.springframework.jdbc.core.RowMapper;

public class AmazonFulfilledShipmentsDataMapper implements RowMapper<AmazonFulfilledShipmentsData> {

    @Override
    public AmazonFulfilledShipmentsData mapRow(ResultSet rs, int rowNum) throws SQLException {
        AmazonFulfilledShipmentsData entity = new AmazonFulfilledShipmentsData();
        entity.setId(rs.getObject(AmazonFulfilledShipmentsDataDBField.ID) == null ? null : rs.getInt(AmazonFulfilledShipmentsDataDBField.ID));
        entity.setAccountNumber(rs.getString(AmazonFulfilledShipmentsDataDBField.ACCOUNT_NUMBER));
        entity.setAmazonOrderId(rs.getString(AmazonFulfilledShipmentsDataDBField.AMAZON_ORDER_ID));
        entity.setMerchantOrderId(rs.getString(AmazonFulfilledShipmentsDataDBField.MERCHANT_ORDER_ID));
        entity.setShipmentId(rs.getString(AmazonFulfilledShipmentsDataDBField.SHIPMENT_ID));
        entity.setShipmentItemId(rs.getString(AmazonFulfilledShipmentsDataDBField.SHIPMENT_ITEM_ID));
        entity.setAmazonOrderItemId(rs.getString(AmazonFulfilledShipmentsDataDBField.AMAZON_ORDER_ITEM_ID));
        entity.setMerchantOrderItemId(rs.getString(AmazonFulfilledShipmentsDataDBField.MERCHANT_ORDER_ITEM_ID));
        entity.setPurchaseDate(rs.getTimestamp(AmazonFulfilledShipmentsDataDBField.PURCHASE_DATE));
        entity.setPaymentsDate(rs.getTimestamp(AmazonFulfilledShipmentsDataDBField.PAYMENTS_DATE));
        entity.setShipmentDate(rs.getTimestamp(AmazonFulfilledShipmentsDataDBField.SHIPMENT_DATE));
        entity.setReportingDate(rs.getTimestamp(AmazonFulfilledShipmentsDataDBField.REPORTING_DATE));
        entity.setBuyerEmail(rs.getString(AmazonFulfilledShipmentsDataDBField.BUYER_EMAIL));
        entity.setBuyerName(rs.getString(AmazonFulfilledShipmentsDataDBField.BUYER_NAME));
        entity.setBuyerPhoneNumber(rs.getString(AmazonFulfilledShipmentsDataDBField.BUYER_PHONE_NUMBER));
        entity.setSku(rs.getString(AmazonFulfilledShipmentsDataDBField.SKU));
        entity.setProductName(rs.getString(AmazonFulfilledShipmentsDataDBField.PRODUCT_NAME));
        entity.setQuantityShipped(rs.getObject(AmazonFulfilledShipmentsDataDBField.QUANTITY_SHIPPED) == null ? null : rs.getInt(AmazonFulfilledShipmentsDataDBField.QUANTITY_SHIPPED));
        entity.setCurrency(rs.getString(AmazonFulfilledShipmentsDataDBField.CURRENCY));
        entity.setItemPrice(rs.getObject(AmazonFulfilledShipmentsDataDBField.ITEM_PRICE) == null ? null : rs.getBigDecimal(AmazonFulfilledShipmentsDataDBField.ITEM_PRICE));
        entity.setItemTax(rs.getObject(AmazonFulfilledShipmentsDataDBField.ITEM_TAX) == null ? null : rs.getBigDecimal(AmazonFulfilledShipmentsDataDBField.ITEM_TAX));
        entity.setShippingPrice(rs.getObject(AmazonFulfilledShipmentsDataDBField.SHIPPING_PRICE) == null ? null : rs.getBigDecimal(AmazonFulfilledShipmentsDataDBField.SHIPPING_PRICE));
        entity.setShippingTax(rs.getObject(AmazonFulfilledShipmentsDataDBField.SHIPPING_TAX)== null ? null : rs.getBigDecimal(AmazonFulfilledShipmentsDataDBField.SHIPPING_TAX));
        entity.setGiftWrapPrice(rs.getObject(AmazonFulfilledShipmentsDataDBField.GIFT_WRAP_PRICE)== null ? null : rs.getBigDecimal(AmazonFulfilledShipmentsDataDBField.GIFT_WRAP_PRICE));
        entity.setGiftWrapTax(rs.getObject(AmazonFulfilledShipmentsDataDBField.GIFT_WRAP_TAX)== null ? null : rs.getBigDecimal(AmazonFulfilledShipmentsDataDBField.GIFT_WRAP_TAX));
        entity.setShipServiceLevel(rs.getString(AmazonFulfilledShipmentsDataDBField.SHIP_SERVICE_LEVEL));
        entity.setRecipientName(rs.getString(AmazonFulfilledShipmentsDataDBField.RECIPIENT_NAME));
        entity.setShipAddress1(rs.getString(AmazonFulfilledShipmentsDataDBField.SHIP_ADDRESS_1));
        entity.setShipAddress2(rs.getString(AmazonFulfilledShipmentsDataDBField.SHIP_ADDRESS_2));
        entity.setShipAddress3(rs.getString(AmazonFulfilledShipmentsDataDBField.SHIP_ADDRESS_3));
        entity.setShipCity(rs.getString(AmazonFulfilledShipmentsDataDBField.SHIP_CITY));
        entity.setShipState(rs.getString(AmazonFulfilledShipmentsDataDBField.SHIP_STATE));
        entity.setShipPostalCode(rs.getString(AmazonFulfilledShipmentsDataDBField.SHIP_POSTAL_CODE));
        entity.setShipCountry(rs.getString(AmazonFulfilledShipmentsDataDBField.SHIP_COUNTRY));
        entity.setShipPhoneNumber(rs.getString(AmazonFulfilledShipmentsDataDBField.SHIP_PHONE_NUMBER));
        entity.setBillAddress1(rs.getString(AmazonFulfilledShipmentsDataDBField.BILL_ADDRESS_1));
        entity.setBillAddress2(rs.getString(AmazonFulfilledShipmentsDataDBField.BILL_ADDRESS_2));
        entity.setBillAddress3(rs.getString(AmazonFulfilledShipmentsDataDBField.BILL_ADDRESS_3));
        entity.setBillCity(rs.getString(AmazonFulfilledShipmentsDataDBField.BILL_CITY));
        entity.setBillState(rs.getString(AmazonFulfilledShipmentsDataDBField.BILL_STATE));
        entity.setBillPostalCode(rs.getString(AmazonFulfilledShipmentsDataDBField.BILL_POSTAL_CODE));
        entity.setBillCountry(rs.getString(AmazonFulfilledShipmentsDataDBField.BILL_COUNTRY));
        entity.setItemPromotionDiscount(rs.getObject(AmazonFulfilledShipmentsDataDBField.ITEM_PROMOTION_DISCOUNT)== null ? null : rs.getBigDecimal(AmazonFulfilledShipmentsDataDBField.ITEM_PROMOTION_DISCOUNT));
        entity.setShipPromotionDiscount(rs.getObject(AmazonFulfilledShipmentsDataDBField.SHIP_PROMOTION_DISCOUNT)== null ? null : rs.getBigDecimal(AmazonFulfilledShipmentsDataDBField.SHIP_PROMOTION_DISCOUNT));
        entity.setCarrier(rs.getString(AmazonFulfilledShipmentsDataDBField.CARRIER));
        entity.setTrackingNumber(rs.getString(AmazonFulfilledShipmentsDataDBField.TRACKING_NUMBER));
        entity.setEstimatedArrivalDate(rs.getTimestamp(AmazonFulfilledShipmentsDataDBField.ESTIMATED_ARRIVAL_DATE));
        entity.setFulfillmentCenterId(rs.getString(AmazonFulfilledShipmentsDataDBField.FULFILLMENT_CENTER_ID));
        entity.setFulfillmentChannel(rs.getString(AmazonFulfilledShipmentsDataDBField.FULFILLMENT_CHANNEL));
        entity.setSalesChannel(rs.getString(AmazonFulfilledShipmentsDataDBField.SALES_CHANNEL));
        entity.setCreatedBy(rs.getObject(AmazonFulfilledShipmentsDataDBField.CREATED_BY) == null ? null : rs.getInt(AmazonFulfilledShipmentsDataDBField.CREATED_BY));
        entity.setCreationDate(rs.getTimestamp(AmazonFulfilledShipmentsDataDBField.CREATION_DATE));
        entity.setLastUpdateUser(rs.getObject(AmazonFulfilledShipmentsDataDBField.LAST_UPDATE_USER) == null ? null : rs.getInt(AmazonFulfilledShipmentsDataDBField.LAST_UPDATE_USER));
        entity.setLastUpdateDate(rs.getTimestamp(AmazonFulfilledShipmentsDataDBField.LAST_UPDATE_DATE));
        return entity;
    }
}