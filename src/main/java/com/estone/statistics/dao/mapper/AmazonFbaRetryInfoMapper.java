package com.estone.statistics.dao.mapper;

import com.estone.statistics.bean.AmazonFbaRetryInfo;
import java.sql.ResultSet;
import java.sql.SQLException;
import org.springframework.jdbc.core.RowMapper;

public class AmazonFbaRetryInfoMapper implements RowMapper<AmazonFbaRetryInfo> {

    public AmazonFbaRetryInfo mapRow(ResultSet rs, int rowNum) throws SQLException {
        AmazonFbaRetryInfo entity = new AmazonFbaRetryInfo();
        entity.setId(rs.getObject(AmazonFbaRetryInfoDBField.ID) == null ? null : rs.getInt(AmazonFbaRetryInfoDBField.ID));
        entity.setAccountNumber(rs.getString(AmazonFbaRetryInfoDBField.ACCOUNT_NUMBER));
        entity.setAccountDetail(rs.getString(AmazonFbaRetryInfoDBField.ACCOUNT_DETAIL));
        entity.setStatus(rs.getObject(AmazonFbaRetryInfoDBField.STATUS) == null ? null : rs.getInt(AmazonFbaRetryInfoDBField.STATUS));
        entity.setCreateTime(rs.getTimestamp(AmazonFbaRetryInfoDBField.CREATE_TIME));
        entity.setReportType(rs.getString(AmazonFbaRetryInfoDBField.REPORT_TYPE));
        entity.setSyncDate(rs.getString(AmazonFbaRetryInfoDBField.SYNC_DATE));
        entity.setStartTime(rs.getString(AmazonFbaRetryInfoDBField.START_TIME));
        entity.setEndTime(rs.getString(AmazonFbaRetryInfoDBField.END_TIME));
        entity.setMerchantId(rs.getString(AmazonFbaRetryInfoDBField.MERCHANT_ID));
        entity.setSystemAccount(rs.getString(AmazonFbaRetryInfoDBField.SYSTEM_ACCOUNT));
        entity.setReportTypeDes(rs.getString(AmazonFbaRetryInfoDBField.REPORT_TYPE_DES));
        entity.setSyncRange(rs.getString(AmazonFbaRetryInfoDBField.SYNC_RANGE));
        entity.setSyncFrequency(rs.getString(AmazonFbaRetryInfoDBField.SYNC_FREQUENCY));
        entity.setRequestResult(rs.getInt(AmazonFbaRetryInfoDBField.REQUEST_RESULT));
        entity.setErrorDetail(rs.getString(AmazonFbaRetryInfoDBField.ERROR_DETAIL));
        entity.setRetryCount(rs.getInt(AmazonFbaRetryInfoDBField.RETRY_COUNT));
        entity.setRequestTime(rs.getTimestamp(AmazonFbaRetryInfoDBField.REQUEST_TIME));
        entity.setFinishTime(rs.getTimestamp(AmazonFbaRetryInfoDBField.FINISH_TIME));
        entity.setRetryId(rs.getObject(AmazonFbaRetryInfoDBField.RETRY_ID) == null ?null :rs.getInt(AmazonFbaRetryInfoDBField.RETRY_ID));
        entity.setProgressId(rs.getObject(AmazonFbaRetryInfoDBField.PROGRESS_ID) == null ? null : rs.getInt(AmazonFbaRetryInfoDBField.PROGRESS_ID));
        return entity;
    }
}