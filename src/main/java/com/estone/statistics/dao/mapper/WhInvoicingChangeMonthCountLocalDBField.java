package com.estone.statistics.dao.mapper;

public interface WhInvoicingChangeMonthCountLocalDBField {
    String ID = "id";

    String SKU = "sku";

    String ACCOUNTNUMBER = "accountNumber";

    String STOCK_DATE = "stock_date";

    String QUANTITY = "quantity";

    String ENDING_PURCHASE_PRICE = "ending_purchase_price";

    String ENDING_PURCHASE_COST = "ending_purchase_cost";

    String ENDING_FIRST_TRIP_COST = "ending_first_trip_cost";

    String ENDING_FIRST_TRIP_TAX = "ending_first_trip_tax";

    String ENDING_REFUND_TAX = "ending_refund_tax";

    String ENDING_ALLOT_COST = "ending_allot_cost";

    String ENDING_AMOUNT = "ending_amount";

    String PLANT_REPORT_QUANTITY = "plant_report_quantity";

    String QUANTITY_DIFF = "quantity_diff";

    String COUNT_DATE = "count_date";

    String MERCHANT_ID = "merchant_id";

    String ENDING_PRICE = "ending_price";

    String ENDING_TOTAL_AMOUNT = "ending_total_amount";

    String SALES_PROPERTY = "sales_property";

    String DISCOUNT = "discount";

    String PROMOTION_LABEL = "promotion_label";

    String UPDATE_TIME = "update_time";
}