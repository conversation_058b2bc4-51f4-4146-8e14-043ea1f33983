package com.estone.statistics.dao.mapper;

public interface WhApvStatisticsDBField {
    String ID = "id";

    String CREATION_DATE = "creation_date";

    String CREATED_BY = "created_by";

    String COUNT_DATE = "count_date";

    String WAREHOUSE_ID = "warehouse_id";

    String SMT_PUSH_QTY = "smt_push_qty";

    String SHOPEE_PUSH_QTY = "shopee_push_qty";

    String AMAZON_PUSH_QTY = "amazon_push_qty";

    String LAZADA_PUSH_QTY = "lazada_push_qty";

    String EBAY_PUSH_QTY = "ebay_push_qty";

    String WISH_PUSH_QTY = "wish_push_qty";

    String JOOM_PUSH_QTY = "joom_push_qty";

    String WALMART_PUSH_QTY = "walmart_push_qty";

    String OTHER_PUSH_QTY = "other_push_qty";

    String SMT_DELIVER_QTY = "smt_deliver_qty";

    String SHOPEE_DELIVER_QTY = "shopee_deliver_qty";

    String AMAZON_DELIVER_QTY = "amazon_deliver_qty";

    String LAZADA_DELIVER_QTY = "lazada_deliver_qty";

    String EBAY_DELIVER_QTY = "ebay_deliver_qty";

    String WISH_DELIVER_QTY = "wish_deliver_qty";

    String JOOM_DELIVER_QTY = "joom_deliver_qty";

    String WALMART_DELIVER_QTY = "walmart_deliver_qty";

    String OTHER_DELIVER_QTY = "other_deliver_qty";

    String OZON_PUSH_QTY = "ozon_push_qty";

    String TIKTOK_PUSH_QTY = "tiktok_push_qty";

    String COUPAN_PUSH_QTY = "coupan_push_qty";

    String SMT_JIT_PUSH_QTY = "smt_jit_push_qty";

    String OZON_DELIVER_QTY = "ozon_deliver_qty";

    String COUPAN_DELIVER_QTY = "coupan_deliver_qty";

    String TIKTOK_DELIVER_QTY = "tiktok_deliver_qty";

    String SMT_JIT_DELIVER_QTY = "smt_jit_deliver_qty";

    String TRANSFER_PUSH_QTY = "transfer_push_qty";
    String TRANSFER_DELIVER_QTY = "transfer_deliver_qty";
    String OW_PUSH_QTY = "ow_push_qty";
    String OW_DELIVER_QTY = "ow_deliver_qty";
    String TEMU_PUSH_QTY = "temu_push_qty";
    String TEMU_DELIVER_QTY = "temu_deliver_qty";
    String FBA_PUSH_QTY = "fba_push_qty";
    String FBA_DELIVER_QTY = "fba_deliver_qty";
    String SMT_JIT_ASN_PUSH_QTY = "smt_jit_asn_push_qty";
    String SMT_JIT_ASN_DELIVER_QTY = "smt_jit_asn_deliver_qty";
}