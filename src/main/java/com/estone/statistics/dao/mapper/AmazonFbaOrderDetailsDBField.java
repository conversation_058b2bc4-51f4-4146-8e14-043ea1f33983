package com.estone.statistics.dao.mapper;

public interface AmazonFbaOrderDetailsDBField {
    String ID = "id";

    String AMAZON_ORDER_ID = "amazon_order_id";

    String PURCHASE_DATE = "purchase_date";

    String ORDER_STATUS = "order_status";

    String AMAZON_ORDER_ITEM_CODE = "amazon_order_item_code";

    String ASIN = "asin";

    String SKU = "sku";

    String PRODUCT_NAME = "product_name";

    String QUANTITY = "quantity";

    String CREATED_BY = "created_by";

    String CREATION_DATE = "creation_date";

    String LAST_UPDATE_USER = "last_update_user";

    String LAST_UPDATE_DATE = "last_update_date";

    String ACCOUNT_NUMBER="account_number";

    String MARKET_PLACE = "market_place";

    String MERCHANT_ID = "merchant_id";
}