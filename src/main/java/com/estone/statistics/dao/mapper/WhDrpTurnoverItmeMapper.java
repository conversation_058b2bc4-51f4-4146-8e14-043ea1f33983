package com.estone.statistics.dao.mapper;

import java.math.BigDecimal;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.Optional;

import com.estone.checkin.enums.CheckInWhType;
import com.whq.tool.sqler.analyzer.DataType;
import org.springframework.jdbc.core.RowMapper;

import com.estone.statistics.bean.WhDrpTurnoverItme;

public class WhDrpTurnoverItmeMapper implements RowMapper<WhDrpTurnoverItme> {

    private boolean queryZsItem = false;
    public WhDrpTurnoverItmeMapper(){

    }

    public WhDrpTurnoverItmeMapper(boolean queryZsItem){
        this.queryZsItem = queryZsItem;
    }

    public WhDrpTurnoverItme mapRow(ResultSet rs, int rowNum) throws SQLException {
        WhDrpTurnoverItme entity = new WhDrpTurnoverItme();
        entity.setId(rs.getObject(WhDrpTurnoverItmeDBField.ID) == null ? null : rs.getLong(WhDrpTurnoverItmeDBField.ID));
        entity.setFoFlag(rs.getObject(WhDrpTurnoverItmeDBField.FO_FLAG) == null ? null : rs.getInt(WhDrpTurnoverItmeDBField.FO_FLAG));
        entity.setBatchNo(rs.getString(WhDrpTurnoverItmeDBField.BATCH_NO));
        entity.setMerchantId(rs.getString(WhDrpTurnoverItmeDBField.MERCHANT_ID));
        entity.setRelationBatchNo(rs.getString(WhDrpTurnoverItmeDBField.RELATION_BATCH_NO));
        entity.setStoreCode(rs.getString(WhDrpTurnoverItmeDBField.STORE_CODE));
        entity.setQuantityType(rs.getObject(WhDrpTurnoverItmeDBField.QUANTITY_TYPE) == null ? null : rs.getInt(WhDrpTurnoverItmeDBField.QUANTITY_TYPE));
        entity.setOrderType(rs.getObject(WhDrpTurnoverItmeDBField.ORDER_TYPE) == null ? null : rs.getInt(WhDrpTurnoverItmeDBField.ORDER_TYPE));
        entity.setCheckInType(rs.getObject(WhDrpTurnoverItmeDBField.CHECK_IN_TYPE) == null ? null : rs.getInt(WhDrpTurnoverItmeDBField.CHECK_IN_TYPE));
        entity.setOrderNo(rs.getString(WhDrpTurnoverItmeDBField.ORDER_NO));
        entity.setSku(rs.getString(WhDrpTurnoverItmeDBField.SKU));
        entity.setQuantity(rs.getObject(WhDrpTurnoverItmeDBField.QUANTITY) == null ? 0 : rs.getInt(WhDrpTurnoverItmeDBField.QUANTITY));
        entity.setPurchaseCostPrice(rs.getObject(WhDrpTurnoverItmeDBField.PURCHASE_COST_PRICE)==null? BigDecimal.ZERO:rs.getBigDecimal(WhDrpTurnoverItmeDBField.PURCHASE_COST_PRICE));
        entity.setPurchaseFreightPrice(rs.getObject(WhDrpTurnoverItmeDBField.PURCHASE_FREIGHT_PRICE)==null? BigDecimal.ZERO:rs.getBigDecimal(WhDrpTurnoverItmeDBField.PURCHASE_FREIGHT_PRICE));
        entity.setProductFreightPrice(rs.getObject(WhDrpTurnoverItmeDBField.PRODUCT_FREIGHT_PRICE)==null? BigDecimal.ZERO:rs.getBigDecimal(WhDrpTurnoverItmeDBField.PRODUCT_FREIGHT_PRICE));
        entity.setProductTaxPrice(rs.getObject(WhDrpTurnoverItmeDBField.PRODUCT_TAX_PRICE)==null? BigDecimal.ZERO:rs.getBigDecimal(WhDrpTurnoverItmeDBField.PRODUCT_TAX_PRICE));
        entity.setTaxReimbursementPrice(rs.getObject(WhDrpTurnoverItmeDBField.TAX_REIMBURSEMENT_PRICE)==null? BigDecimal.ZERO:rs.getBigDecimal(WhDrpTurnoverItmeDBField.TAX_REIMBURSEMENT_PRICE));
        entity.setAllotFreightPrice(rs.getObject(WhDrpTurnoverItmeDBField.ALLOT_FREIGHT_PRICE)==null? BigDecimal.ZERO:rs.getBigDecimal(WhDrpTurnoverItmeDBField.ALLOT_FREIGHT_PRICE));
        entity.setFirstInBatch(rs.getString(WhDrpTurnoverItmeDBField.FIRST_IN_BATCH));
        entity.setFirstInTime(rs.getTimestamp(WhDrpTurnoverItmeDBField.FIRST_IN_TIME));
        entity.setOverseasFirstInBatch(rs.getString(WhDrpTurnoverItmeDBField.OVERSEAS_FIRST_IN_BATCH));
        entity.setOverseasFirstInTime(rs.getTimestamp(WhDrpTurnoverItmeDBField.OVERSEAS_FIRST_IN_TIME));
        entity.setPlatformFirstInBatch(rs.getString(WhDrpTurnoverItmeDBField.PLATFORM_FIRST_IN_BATCH));
        entity.setPlatformFirstInTime(rs.getTimestamp(WhDrpTurnoverItmeDBField.PLATFORM_FIRST_IN_TIME));
        entity.setEndPurchaseCostPrice(rs.getObject(WhDrpTurnoverItmeDBField.END_PURCHASE_COST_PRICE)==null? BigDecimal.ZERO:rs.getBigDecimal(WhDrpTurnoverItmeDBField.END_PURCHASE_COST_PRICE));
        entity.setEndPurchaseFreightPrice(rs.getObject(WhDrpTurnoverItmeDBField.END_PURCHASE_FREIGHT_PRICE)==null? BigDecimal.ZERO:rs.getBigDecimal(WhDrpTurnoverItmeDBField.END_PURCHASE_FREIGHT_PRICE));
        entity.setEndProductFreightPrice(rs.getObject(WhDrpTurnoverItmeDBField.END_PRODUCT_FREIGHT_PRICE)==null? BigDecimal.ZERO:rs.getBigDecimal(WhDrpTurnoverItmeDBField.END_PRODUCT_FREIGHT_PRICE));
        entity.setEndProductTaxPrice(rs.getObject(WhDrpTurnoverItmeDBField.END_PRODUCT_TAX_PRICE)==null? BigDecimal.ZERO:rs.getBigDecimal(WhDrpTurnoverItmeDBField.END_PRODUCT_TAX_PRICE));
        entity.setEndStock(rs.getObject(WhDrpTurnoverItmeDBField.END_STOCK)==null? 0 :rs.getInt(WhDrpTurnoverItmeDBField.END_STOCK));
        entity.setMatchQuantity(rs.getObject(WhDrpTurnoverItmeDBField.MATCH_QUANTITY)==null? 0 :rs.getInt(WhDrpTurnoverItmeDBField.MATCH_QUANTITY));
        entity.setCreateTime(rs.getTimestamp(WhDrpTurnoverItmeDBField.CREATE_TIME));
        entity.setReportTime(rs.getTimestamp(WhDrpTurnoverItmeDBField.REPORT_TIME));
        entity.setUpdateTime(rs.getTimestamp(WhDrpTurnoverItmeDBField.UPDATE_TIME));
        entity.setPlatform(rs.getString(WhDrpTurnoverItmeDBField.PLATFORM));
        if (entity.getCheckInType() == null || entity.getCheckInType() == 0) {
            entity.setAsin(rs.getString(WhDrpTurnoverItmeDBField.ASIN));
            entity.setFnSku(rs.getString(WhDrpTurnoverItmeDBField.FN_SKU));
            entity.setSellSku(rs.getString(WhDrpTurnoverItmeDBField.SELL_SKU));
        }
        if (CheckInWhType.TEMU.intCode().equals(entity.getCheckInType())){
            entity.setEstimatePendingSettleAmount(rs.getObject(WhDrpTurnoverItmeDBField.ESTIMATE_PENDING_SETTLE_AMOUNT)==null? BigDecimal.ZERO:rs.getBigDecimal(WhDrpTurnoverItmeDBField.ESTIMATE_PENDING_SETTLE_AMOUNT));
        }
        if (queryZsItem) {
            entity.setSkuStatus(Optional.ofNullable(rs.getInt("skuStatus")).orElse(null));
        }
        return entity;
    }
}