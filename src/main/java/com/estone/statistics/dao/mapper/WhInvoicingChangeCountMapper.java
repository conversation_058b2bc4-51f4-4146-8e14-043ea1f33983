package com.estone.statistics.dao.mapper;

import com.estone.checkin.enums.CheckInWhType;
import com.estone.statistics.bean.WhInvoicingChangeCount;
import com.estone.statistics.bean.WhInvoicingChangeCountQueryCondition;
import org.apache.commons.lang.StringUtils;
import org.springframework.jdbc.core.RowMapper;

import java.math.BigDecimal;
import java.sql.ResultSet;
import java.sql.SQLException;

public class WhInvoicingChangeCountMapper implements RowMapper<WhInvoicingChangeCount> {

    private boolean isQueryTotal;

    private WhInvoicingChangeCountQueryCondition query = new WhInvoicingChangeCountQueryCondition();

    public WhInvoicingChangeCountMapper() {
        super();
    }

    public WhInvoicingChangeCountMapper(boolean isQueryTotal) {
        super();
        this.isQueryTotal = isQueryTotal;
    }
    public WhInvoicingChangeCountMapper(WhInvoicingChangeCountQueryCondition query) {
        this.query = query;
    }

    @Override
    public WhInvoicingChangeCount mapRow(ResultSet rs, int rowNum) throws SQLException {
        WhInvoicingChangeCount entity = null;
        if(isQueryTotal){
            entity=new WhInvoicingChangeCount();
            entity.setTotalEndingInventoryQuantity(rs.getLong("totalEndingInventoryQuantity"));
            entity.setTotalEndingInventoryAmount(rs.getBigDecimal("totalEndingInventoryAmount"));
            entity.setTotalPlantReport(rs.getLong("totalPlantReport"));
            entity.setTotalQuantityDiff(rs.getBigDecimal("totalQuantityDiff"));
        }
        else {
            entity=new WhInvoicingChangeCount();
            entity.setId(rs.getObject(WhInvoicingChangeCountDBField.ID) == null ? null : rs.getInt(WhInvoicingChangeCountDBField.ID));
            entity.setSku(rs.getString(WhInvoicingChangeCountDBField.SKU));
            entity.setMerchantId(rs.getString(WhInvoicingChangeCountDBField.MERCHANT_ID));
            entity.setAccountnumber(rs.getString(WhInvoicingChangeCountDBField.ACCOUNTNUMBER));
            entity.setStockDate(rs.getTimestamp(WhInvoicingChangeCountDBField.STOCK_DATE));
            entity.setQuantity(rs.getObject(WhInvoicingChangeCountDBField.QUANTITY) == null ? 0 : rs.getInt(WhInvoicingChangeCountDBField.QUANTITY));
            entity.setEndingPurchasePrice(rs.getObject(WhInvoicingChangeCountDBField.ENDING_PURCHASE_PRICE) == null ? BigDecimal.ZERO : rs.getBigDecimal(WhInvoicingChangeCountDBField.ENDING_PURCHASE_PRICE));
            entity.setEndingPurchaseCost(rs.getObject(WhInvoicingChangeCountDBField.ENDING_PURCHASE_COST) == null ? BigDecimal.ZERO : rs.getBigDecimal(WhInvoicingChangeCountDBField.ENDING_PURCHASE_COST));
            entity.setEndingFirstTripCost(rs.getObject(WhInvoicingChangeCountDBField.ENDING_FIRST_TRIP_COST) == null ? BigDecimal.ZERO : rs.getBigDecimal(WhInvoicingChangeCountDBField.ENDING_FIRST_TRIP_COST));
            entity.setEndingFirstTripTax(rs.getObject(WhInvoicingChangeCountDBField.ENDING_FIRST_TRIP_TAX) == null ? BigDecimal.ZERO : rs.getBigDecimal(WhInvoicingChangeCountDBField.ENDING_FIRST_TRIP_TAX));
            entity.setEndingRefundTax(rs.getObject(WhInvoicingChangeCountDBField.ENDING_REFUND_TAX) == null ? BigDecimal.ZERO : rs.getBigDecimal(WhInvoicingChangeCountDBField.ENDING_REFUND_TAX));
            entity.setEndingAllotCost(rs.getObject(WhInvoicingChangeCountDBField.ENDING_ALLOT_COST) == null ? BigDecimal.ZERO : rs.getBigDecimal(WhInvoicingChangeCountDBField.ENDING_ALLOT_COST));
            entity.setEndingAmount(rs.getObject(WhInvoicingChangeCountDBField.ENDING_AMOUNT) == null ? BigDecimal.ZERO : rs.getBigDecimal(WhInvoicingChangeCountDBField.ENDING_AMOUNT));
            entity.setPlantReportQuantity(rs.getObject(WhInvoicingChangeCountDBField.PLANT_REPORT_QUANTITY) == null ? 0 : rs.getInt(WhInvoicingChangeCountDBField.PLANT_REPORT_QUANTITY));
            entity.setQuantityDiff(rs.getObject(WhInvoicingChangeCountDBField.QUANTITY_DIFF) == null ? 0 : rs.getInt(WhInvoicingChangeCountDBField.QUANTITY_DIFF));
            entity.setCountDate(rs.getTimestamp(WhInvoicingChangeCountDBField.COUNT_DATE));
        }
        if(CheckInWhType.LOCAL.getEnName().equals(query.getWarehouseType()) && StringUtils.equals(query.getType(),"month")){
            entity.setEndingPrice(rs.getObject(WhInvoicingChangeCountDBField.ENDING_PRICE) == null ? BigDecimal.ZERO : rs.getBigDecimal(WhInvoicingChangeCountDBField.ENDING_PRICE));
            entity.setEndingTotalAmount(rs.getObject(WhInvoicingChangeCountDBField.ENDING_TOTAL_AMOUNT) == null ? BigDecimal.ZERO : rs.getBigDecimal(WhInvoicingChangeCountDBField.ENDING_TOTAL_AMOUNT));
            entity.setSalesProperty(rs.getString(WhInvoicingChangeCountDBField.SALES_PROPERTY));
            entity.setDiscount(rs.getObject(WhInvoicingChangeCountDBField.DISCOUNT) == null ? BigDecimal.ZERO : rs.getBigDecimal(WhInvoicingChangeCountDBField.DISCOUNT));
        }

        return entity;
    }
}