package com.estone.statistics.dao.mapper;

import com.estone.statistics.bean.PickStatisticsRecord;
import java.sql.ResultSet;
import java.sql.SQLException;
import org.springframework.jdbc.core.RowMapper;

public class PickStatisticsRecordMapper implements <PERSON><PERSON>apper<PickStatisticsRecord> {


    public PickStatisticsRecord mapRow(ResultSet rs, int rowNum) throws SQLException {
        PickStatisticsRecord entity = new PickStatisticsRecord();
        entity.setTaskNum(rs.getObject(PickStatisticsRecordDBField.TASK_NUM) == null ? 0 : rs.getInt(PickStatisticsRecordDBField.TASK_NUM));
        entity.setOrderNum(rs.getObject(PickStatisticsRecordDBField.ORDER_NUM) == null ? 0 : rs.getInt(PickStatisticsRecordDBField.ORDER_NUM));
        entity.setNum(rs.getObject(PickStatisticsRecordDBField.NUM) == null ? 0 : rs.getInt(PickStatisticsRecordDBField.NUM));
        entity.setSkuNum(rs.getObject(PickStatisticsRecordDBField.SKU_NUM) == null ? 0 : rs.getInt(PickStatisticsRecordDBField.SKU_NUM));
        entity.setLocationNum(rs.getObject(PickStatisticsRecordDBField.LOCATION_NUM) == null ? 0 : rs.getInt(PickStatisticsRecordDBField.LOCATION_NUM));
        entity.setGroupDay(rs.getString("groupDay"));
        return entity;
    }
}