package com.estone.statistics.dao;

import com.estone.statistics.bean.ScrapRecommendMonth;
import com.estone.statistics.bean.ScrapRecommendMonthQueryCondition;
import com.whq.tool.component.Pager;
import java.util.List;

public interface ScrapRecommendMonthDao {
    int queryScrapRecommendMonthCount(ScrapRecommendMonthQueryCondition query);

    List<ScrapRecommendMonth> queryScrapRecommendMonthList();

    List<ScrapRecommendMonth> queryScrapRecommendMonthList(ScrapRecommendMonthQueryCondition query, Pager pager);

    ScrapRecommendMonth queryScrapRecommendMonth(Integer primaryKey);

    ScrapRecommendMonth queryScrapRecommendMonth(ScrapRecommendMonthQueryCondition query);

    void createScrapRecommendMonth(ScrapRecommendMonth entity);

    void batchCreateScrapRecommendMonth(List<ScrapRecommendMonth> entityList);

    void batchUpdateScrapRecommendMonth(List<ScrapRecommendMonth> entityList);

    void deleteScrapRecommendMonth(Integer primaryKey);

    void updateScrapRecommendMonth(ScrapRecommendMonth entity);

    List<ScrapRecommendMonth> queryexpStockScrap();

    List<ScrapRecommendMonth> queryThirtyDaysSalesDays0StockScrap();

    List<ScrapRecommendMonth> querySeasonalLibraryAge4StockScrap(List<Integer> allsStockIds);

    List<ScrapRecommendMonth> queryNonSeasonalLibraryAge3StockScrap(List<Integer> allsStockIds);

    List<ScrapRecommendMonth> queryStockInAge2(List<Integer> allsStockIds);

    void deleteScrapRecommendMonthByDate(String currDate);
}