package com.estone.statistics.service;

import java.util.List;

import com.estone.statistics.bean.WhAssetChangeGroup;
import com.estone.statistics.bean.WhAssetChangeGroupQueryCondition;
import com.whq.tool.component.Pager;

public interface WhAssetChangeGroupService {

    List<WhAssetChangeGroup> queryWhAssetChangeGroups(WhAssetChangeGroupQueryCondition query);

    WhAssetChangeGroup queryWhAssetChangeGroupTotal(WhAssetChangeGroupQueryCondition query);

    List<String> queryAssetItemSkuList(WhAssetChangeGroupQueryCondition query, Pager pager);
}
