package com.estone.statistics.service;

import com.estone.statistics.bean.WhDrpAdjustment;
import com.estone.statistics.bean.WhDrpAdjustmentQueryCondition;
import com.whq.tool.component.Pager;
import com.whq.tool.json.ResponseJson;

import java.util.List;

public interface WhDrpAdjustmentService {
    List<WhDrpAdjustment> queryAllWhDrpAdjustments();

    List<WhDrpAdjustment> queryWhDrpAdjustments(WhDrpAdjustmentQueryCondition query, Pager pager);

    WhDrpAdjustment getWhDrpAdjustment(Integer id);

    WhDrpAdjustment getWhDrpAdjustmentDetail(Integer id);

    WhDrpAdjustment queryWhDrpAdjustment(WhDrpAdjustmentQueryCondition query);

    void createWhDrpAdjustment(WhDrpAdjustment whDrpAdjustment);

    void batchCreateWhDrpAdjustment(List<WhDrpAdjustment> entityList);

    void deleteWhDrpAdjustment(Integer id);

    void updateWhDrpAdjustment(WhDrpAdjustment whDrpAdjustment);

    void batchUpdateWhDrpAdjustment(List<WhDrpAdjustment> entityList);

}