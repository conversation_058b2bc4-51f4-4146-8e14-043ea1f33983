package com.estone.statistics.service;

import com.estone.elasticsearch.model.request.SmtWarehouseInventoryRequest;
import com.estone.statistics.bean.SmtWarehouseInvoicingCount;
import com.estone.statistics.bean.SmtWarehouseInvoicingCountQueryCondition;
import com.whq.tool.component.Pager;
import java.util.List;

public interface SmtWarehouseInvoicingCountService {
    List<SmtWarehouseInvoicingCount> queryAllSmtWarehouseInvoicingCounts();

    List<SmtWarehouseInvoicingCount> querySmtWarehouseInvoicingCounts(SmtWarehouseInvoicingCountQueryCondition query, Pager pager);
    List<SmtWarehouseInvoicingCount> queryPageList(SmtWarehouseInvoicingCountQueryCondition query, Pager pager);

    SmtWarehouseInvoicingCount getSmtWarehouseInvoicingCount(Integer id);

    SmtWarehouseInvoicingCount getSmtWarehouseInvoicingCountDetail(Integer id);

    SmtWarehouseInvoicingCount querySmtWarehouseInvoicingCount(SmtWarehouseInvoicingCountQueryCondition query);

    void createSmtWarehouseInvoicingCount(SmtWarehouseInvoicingCount smtWarehouseInvoicingCount);

    void batchCreateSmtWarehouseInvoicingCount(List<SmtWarehouseInvoicingCount> entityList);

    void deleteSmtWarehouseInvoicingCount(Integer id);

    void updateSmtWarehouseInvoicingCount(SmtWarehouseInvoicingCount smtWarehouseInvoicingCount);

    void batchUpdateSmtWarehouseInvoicingCount(List<SmtWarehouseInvoicingCount> entityList);

    void doDailyCount(SmtWarehouseInventoryRequest request);
}