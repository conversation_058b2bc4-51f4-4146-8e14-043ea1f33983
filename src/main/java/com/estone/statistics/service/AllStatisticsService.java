package com.estone.statistics.service;


import com.estone.statistics.bean.AllStatistics;
import com.estone.statistics.bean.AllStatisticsCount;
import com.estone.statistics.bean.AllStatisticsQueryCondition;
import com.whq.tool.component.Pager;

import java.util.List;

public interface AllStatisticsService {


    List<AllStatistics> queryAllStatistics(AllStatisticsQueryCondition query, Pager page);

    AllStatisticsCount total(AllStatisticsQueryCondition search,List<String> xAxisDates);
}
