package com.estone.statistics.service;

import com.estone.statistics.bean.AmazonFbaFulfillmentMonthlyInventoryData;
import com.estone.statistics.bean.AmazonFbaFulfillmentMonthlyInventoryDataQueryCondition;
import com.whq.tool.component.Pager;
import java.util.List;

public interface AmazonFbaFulfillmentMonthlyInventoryDataService {
    List<AmazonFbaFulfillmentMonthlyInventoryData> queryAllAmazonFbaFulfillmentMonthlyInventoryDatas();

    List<AmazonFbaFulfillmentMonthlyInventoryData> queryAmazonFbaFulfillmentMonthlyInventoryDatas(AmazonFbaFulfillmentMonthlyInventoryDataQueryCondition query, Pager pager);

    AmazonFbaFulfillmentMonthlyInventoryData getAmazonFbaFulfillmentMonthlyInventoryData(Integer id);

    AmazonFbaFulfillmentMonthlyInventoryData getAmazonFbaFulfillmentMonthlyInventoryDataDetail(Integer id);

    AmazonFbaFulfillmentMonthlyInventoryData queryAmazonFbaFulfillmentMonthlyInventoryData(AmazonFbaFulfillmentMonthlyInventoryDataQueryCondition query);

    void createAmazonFbaFulfillmentMonthlyInventoryData(AmazonFbaFulfillmentMonthlyInventoryData amazonFbaFulfillmentMonthlyInventoryData);

    void batchCreateAmazonFbaFulfillmentMonthlyInventoryData(List<AmazonFbaFulfillmentMonthlyInventoryData> entityList);

    void deleteAmazonFbaFulfillmentMonthlyInventoryData(Integer id);

    void updateAmazonFbaFulfillmentMonthlyInventoryData(AmazonFbaFulfillmentMonthlyInventoryData amazonFbaFulfillmentMonthlyInventoryData);

    void batchUpdateAmazonFbaFulfillmentMonthlyInventoryData(List<AmazonFbaFulfillmentMonthlyInventoryData> entityList);
}