package com.estone.statistics.service.impl;

import com.estone.common.util.DateUtils;
import com.estone.exquisite.service.BaseService;
import com.estone.sku.bean.ExpManage;
import com.estone.sku.bean.ExpManageQueryCondition;
import com.estone.sku.service.ExpManageService;
import com.estone.statistics.bean.ScrapRecommendMonth;
import com.estone.statistics.bean.ScrapRecommendMonthQueryCondition;
import com.estone.statistics.dao.ScrapRecommendMonthDao;
import com.estone.statistics.service.ScrapRecommendMonthService;
import com.whq.tool.component.Pager;
import com.whq.tool.exception.BusinessException;
import com.whq.tool.exception.DBErrorCode;
import com.whq.tool.sqler.SqlerException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Service("scrapRecommendMonthService")
@Slf4j
public class ScrapRecommendMonthServiceImpl extends BaseService<ScrapRecommendMonthQueryCondition, ScrapRecommendMonth>  implements ScrapRecommendMonthService {
    @Resource
    private ScrapRecommendMonthDao scrapRecommendMonthDao;

    @Resource
    private ExpManageService expManageService;

    @Override
    public ScrapRecommendMonth getScrapRecommendMonth(Integer id) {
        ScrapRecommendMonth scrapRecommendMonth = scrapRecommendMonthDao.queryScrapRecommendMonth(id);
        return scrapRecommendMonth;
    }

    @Override
    public ScrapRecommendMonth getScrapRecommendMonthDetail(Integer id) {
        ScrapRecommendMonth scrapRecommendMonth = scrapRecommendMonthDao.queryScrapRecommendMonth(id);
        // 关联查询
        return scrapRecommendMonth;
    }

    @Override
    public ScrapRecommendMonth queryScrapRecommendMonth(ScrapRecommendMonthQueryCondition query) {
        Assert.notNull(query, "query is null!");
        ScrapRecommendMonth scrapRecommendMonth = scrapRecommendMonthDao.queryScrapRecommendMonth(query);
        return scrapRecommendMonth;
    }

    @Override
    public List<ScrapRecommendMonth> queryAllScrapRecommendMonths() {
        return scrapRecommendMonthDao.queryScrapRecommendMonthList();
    }

    @Override
    public List<ScrapRecommendMonth> queryScrapRecommendMonths(ScrapRecommendMonthQueryCondition query, Pager pager) {
        Assert.notNull(query, "query is null!");
        if (pager != null && pager.isQueryCount()) {
            int count = scrapRecommendMonthDao.queryScrapRecommendMonthCount(query);
            pager.setTotalCount(count);
            if ( count == 0) {
                return new ArrayList<ScrapRecommendMonth>();
            }
        }
        List<ScrapRecommendMonth> scrapRecommendMonths = scrapRecommendMonthDao.queryScrapRecommendMonthList(query, pager);
        return scrapRecommendMonths;
    }

    @Override
    public void createScrapRecommendMonth(ScrapRecommendMonth scrapRecommendMonth) {
        try {
            scrapRecommendMonthDao.createScrapRecommendMonth(scrapRecommendMonth);
        }
        catch (SqlerException e) {
            log.error(e.getMessage(), e);
            throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
        }
    }

    @Override
    public void batchCreateScrapRecommendMonth(List<ScrapRecommendMonth> entityList) {
        if (CollectionUtils.isNotEmpty(entityList)) {
            try {
                scrapRecommendMonthDao.batchCreateScrapRecommendMonth(entityList);
            }
            catch (SqlerException e) {
                log.error(e.getMessage(), e);
                throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
            }
        }
    }

    @Override
    public void deleteScrapRecommendMonth(Integer id) {
        try {
            scrapRecommendMonthDao.deleteScrapRecommendMonth(id);
        }
        catch (SqlerException e) {
            log.error(e.getMessage(), e);
            throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
        }
    }

    @Override
    public void updateScrapRecommendMonth(ScrapRecommendMonth scrapRecommendMonth) {
        try {
            scrapRecommendMonthDao.updateScrapRecommendMonth(scrapRecommendMonth);
        }
        catch (SqlerException e) {
            log.error(e.getMessage(), e);
            throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
        }
    }

    @Override
    public void batchUpdateScrapRecommendMonth(List<ScrapRecommendMonth> entityList) {
        if (CollectionUtils.isNotEmpty(entityList)) {
            try {
                scrapRecommendMonthDao.batchUpdateScrapRecommendMonth(entityList);
            }
            catch (SqlerException e) {
                log.error(e.getMessage(), e);
                throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
            }
        }
    }

    // 报废建议
    @Override
    public void syncScrapRecommendMonth() {
        //先删除当天
        String currDate = DateUtils.dateToString(DateUtils.getFirstDayOfLastMonth(new Date()), "yyyy-MM");
        scrapRecommendMonthDao.deleteScrapRecommendMonthByDate(currDate);
        List<ScrapRecommendMonth> allScrapRecommendMonths = new ArrayList<>();
        // 超过保质期：截止计算时保质期SKU有库存且已过期的部分
        Map<String, Integer> skuQtyMap = expStockScrap(allScrapRecommendMonths);
        List<Integer> allsStockIds = new ArrayList<>();
        // 入库12个月且0动销：截止计算时SKU只存在采购上架库存变动且入库时间超过12个月
        queryThirtyDaysSalesDays0StockScrap(allScrapRecommendMonths,allsStockIds,skuQtyMap);
        // 季节性产品库龄4年以上：SKU为季节性SKU，按照最近入库上架时间截止到计算时超过4年
        querySeasonalLibraryAge4StockScrap(allScrapRecommendMonths,allsStockIds,skuQtyMap);
        // 非季节性库龄3年以上：SKU为非季节性SKU，按照最近入库上架时间截止到计算时超过3年
        queryNonSeasonalLibraryAge3StockScrap(allScrapRecommendMonths,allsStockIds,skuQtyMap);
        // 入库2年以上，数量小于3，采购单价小于10：按照最近入库上架时间截止到计算时超过2年且0＜剩余库存≤3且期末采购单价＜10
        queryStockInAge2(allScrapRecommendMonths,allsStockIds,skuQtyMap);
        batchCreateScrapRecommendMonth(allScrapRecommendMonths);
    }

    // 入库2年以上，数量小于3，采购单价小于10：按照最近入库上架时间截止到计算时超过2年且0＜剩余库存≤3且期末采购单价＜10
    private void queryStockInAge2(List<ScrapRecommendMonth> allScrapRecommendMonths, List<Integer> allsStockIds, Map<String, Integer> skuQtyMap) {
        List<ScrapRecommendMonth> scrapRecommendMonths = scrapRecommendMonthDao.queryStockInAge2(allsStockIds);
        // 判断排除保质期已推荐部分
        if (MapUtils.isNotEmpty(skuQtyMap)){
            Iterator<ScrapRecommendMonth> iterator = scrapRecommendMonths.iterator();
            while (iterator.hasNext()) {
                ScrapRecommendMonth scrapRecommendMonth = iterator.next();
                Integer qty = skuQtyMap.get(scrapRecommendMonth.getSku());
                if (qty == null) continue;
                if (scrapRecommendMonth.getEndingInventoryQuantity() <= qty) {
                    iterator.remove();  // 安全删除元素
                } else {
                    scrapRecommendMonth.setEndingInventoryQuantity(scrapRecommendMonth.getEndingInventoryQuantity() - qty);
                }
            }
        }
        if (CollectionUtils.isNotEmpty(scrapRecommendMonths)) {
            allScrapRecommendMonths.addAll(scrapRecommendMonths);
            allsStockIds.addAll(scrapRecommendMonths.stream().flatMap(s -> Arrays.stream(s.getRelevantIds().split(",")))
                    .map(Integer::parseInt).collect(Collectors.toList()));
        }
    }

    // 非季节性库龄3年以上：SKU为非季节性SKU，按照最近入库上架时间截止到计算时超过3年
    private void queryNonSeasonalLibraryAge3StockScrap(List<ScrapRecommendMonth> allScrapRecommendMonths, List<Integer> allsStockIds, Map<String, Integer> skuQtyMap) {
        List<ScrapRecommendMonth> scrapRecommendMonths = scrapRecommendMonthDao.queryNonSeasonalLibraryAge3StockScrap(allsStockIds);
        // 判断排除保质期已推荐部分
        if (MapUtils.isNotEmpty(skuQtyMap)){
            Iterator<ScrapRecommendMonth> iterator = scrapRecommendMonths.iterator();
            while (iterator.hasNext()) {
                ScrapRecommendMonth scrapRecommendMonth = iterator.next();
                Integer qty = skuQtyMap.get(scrapRecommendMonth.getSku());
                if (qty == null) continue;
                if (scrapRecommendMonth.getEndingInventoryQuantity() <= qty) {
                    iterator.remove();  // 安全删除元素
                } else {
                    scrapRecommendMonth.setEndingInventoryQuantity(scrapRecommendMonth.getEndingInventoryQuantity() - qty);
                }
            }
        }
        if (CollectionUtils.isNotEmpty(scrapRecommendMonths)) {
            allScrapRecommendMonths.addAll(scrapRecommendMonths);
            allsStockIds.addAll(scrapRecommendMonths.stream().flatMap(s -> Arrays.stream(s.getRelevantIds().split(",")))
                    .map(Integer::parseInt).collect(Collectors.toList()));
        }
    }

    // 季节性产品库龄4年以上：SKU为季节性SKU，按照最近入库上架时间截止到计算时超过4年
    private void querySeasonalLibraryAge4StockScrap(List<ScrapRecommendMonth> allScrapRecommendMonths, List<Integer> allsStockIds, Map<String, Integer> skuQtyMap) {
        List<ScrapRecommendMonth> scrapRecommendMonths = scrapRecommendMonthDao.querySeasonalLibraryAge4StockScrap(allsStockIds);
        // 判断排除保质期已推荐部分
        if (MapUtils.isNotEmpty(skuQtyMap)){
            Iterator<ScrapRecommendMonth> iterator = scrapRecommendMonths.iterator();
            while (iterator.hasNext()) {
                ScrapRecommendMonth scrapRecommendMonth = iterator.next();
                Integer qty = skuQtyMap.get(scrapRecommendMonth.getSku());
                if (qty == null) continue;
                if (scrapRecommendMonth.getEndingInventoryQuantity() <= qty) {
                    iterator.remove();  // 安全删除元素
                } else {
                    scrapRecommendMonth.setEndingInventoryQuantity(scrapRecommendMonth.getEndingInventoryQuantity() - qty);
                }
            }
        }
        if (CollectionUtils.isNotEmpty(scrapRecommendMonths)) {
            allScrapRecommendMonths.addAll(scrapRecommendMonths);
            allsStockIds.addAll(scrapRecommendMonths.stream().flatMap(s -> Arrays.stream(s.getRelevantIds().split(",")))
                    .map(Integer::parseInt).collect(Collectors.toList()));
        }
    }

    // 入库12个月且0动销：截止计算时SKU只存在采购上架库存变动且入库时间超过12个月
    private void queryThirtyDaysSalesDays0StockScrap(List<ScrapRecommendMonth> allScrapRecommendMonths, List<Integer> allsStockIds,Map<String, Integer> skuQtyMap) {
        List<ScrapRecommendMonth> scrapRecommendMonths = scrapRecommendMonthDao.queryThirtyDaysSalesDays0StockScrap();
        // 判断排除保质期已推荐部分
        if (MapUtils.isNotEmpty(skuQtyMap)){
            Iterator<ScrapRecommendMonth> iterator = scrapRecommendMonths.iterator();
            while (iterator.hasNext()) {
                ScrapRecommendMonth scrapRecommendMonth = iterator.next();
                Integer qty = skuQtyMap.get(scrapRecommendMonth.getSku());
                if (qty == null) continue;
                if (scrapRecommendMonth.getEndingInventoryQuantity() <= qty) {
                    iterator.remove();  // 安全删除元素
                } else {
                    scrapRecommendMonth.setEndingInventoryQuantity(scrapRecommendMonth.getEndingInventoryQuantity() - qty);
                }
            }
        }
        if (CollectionUtils.isNotEmpty(scrapRecommendMonths)) {
            allScrapRecommendMonths.addAll(scrapRecommendMonths);
            allsStockIds.addAll(scrapRecommendMonths.stream().flatMap(s -> Arrays.stream(s.getRelevantIds().split(",")))
                    .map(Integer::parseInt).collect(Collectors.toList()));
        }
    }

    //保质期报废
    private Map<String, Integer> expStockScrap(List<ScrapRecommendMonth> allScrapRecommendMonths) {
        List<ScrapRecommendMonth> scrapRecommendMonths = scrapRecommendMonthDao.queryexpStockScrap();
        allScrapRecommendMonths.addAll(scrapRecommendMonths);
        // 获取保质期库存记录对应的报废建议数量
        if (CollectionUtils.isNotEmpty(scrapRecommendMonths)) {
            List<Integer> expManageIdList = scrapRecommendMonths.stream().flatMap(s -> Arrays.stream(s.getRelevantIds().split(",")))
                    .map(Integer::parseInt).collect(Collectors.toList());
            ExpManageQueryCondition expManageQueryCondition = new ExpManageQueryCondition();
            expManageQueryCondition.setIdList(expManageIdList);
            List<ExpManage> expManages = expManageService.queryExpManages(expManageQueryCondition, null);
            return expManages.stream()
                    .collect(Collectors.toMap(ExpManage::getSku, ExpManage::getQuantity, Integer::sum));
        }
        return new HashMap<>(2);
    }


    @Override
    public List<ScrapRecommendMonth> list(ScrapRecommendMonthQueryCondition search, Pager pager) {
        return queryScrapRecommendMonths(search,pager);
    }
}