package com.estone.statistics.service.impl;

import com.estone.statistics.bean.AmazonFbaFulfillmentCustomerReturnsData;
import com.estone.statistics.bean.AmazonFbaFulfillmentCustomerReturnsDataQueryCondition;
import com.estone.statistics.dao.AmazonFbaFulfillmentCustomerReturnsDataDao;
import com.estone.statistics.service.AmazonFbaFulfillmentCustomerReturnsDataService;
import com.whq.tool.component.Pager;
import com.whq.tool.exception.BusinessException;
import com.whq.tool.exception.DBErrorCode;
import com.whq.tool.sqler.SqlerException;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

@Service("amazonFbaFulfillmentCustomerReturnsDataService")
@Slf4j
public class AmazonFbaFulfillmentCustomerReturnsDataServiceImpl implements AmazonFbaFulfillmentCustomerReturnsDataService {
    @Resource
    private AmazonFbaFulfillmentCustomerReturnsDataDao amazonFbaFulfillmentCustomerReturnsDataDao;

    @Override
    public AmazonFbaFulfillmentCustomerReturnsData getAmazonFbaFulfillmentCustomerReturnsData(Integer id) {
        AmazonFbaFulfillmentCustomerReturnsData amazonFbaFulfillmentCustomerReturnsData = amazonFbaFulfillmentCustomerReturnsDataDao.queryAmazonFbaFulfillmentCustomerReturnsData(id);
        return amazonFbaFulfillmentCustomerReturnsData;
    }

    @Override
    public AmazonFbaFulfillmentCustomerReturnsData getAmazonFbaFulfillmentCustomerReturnsDataDetail(Integer id) {
        AmazonFbaFulfillmentCustomerReturnsData amazonFbaFulfillmentCustomerReturnsData = amazonFbaFulfillmentCustomerReturnsDataDao.queryAmazonFbaFulfillmentCustomerReturnsData(id);
        // 关联查询
        return amazonFbaFulfillmentCustomerReturnsData;
    }

    @Override
    public AmazonFbaFulfillmentCustomerReturnsData queryAmazonFbaFulfillmentCustomerReturnsData(AmazonFbaFulfillmentCustomerReturnsDataQueryCondition query) {
        Assert.notNull(query, "query is null!");
        AmazonFbaFulfillmentCustomerReturnsData amazonFbaFulfillmentCustomerReturnsData = amazonFbaFulfillmentCustomerReturnsDataDao.queryAmazonFbaFulfillmentCustomerReturnsData(query);
        return amazonFbaFulfillmentCustomerReturnsData;
    }

    @Override
    public List<AmazonFbaFulfillmentCustomerReturnsData> queryAllAmazonFbaFulfillmentCustomerReturnsDatas() {
        return amazonFbaFulfillmentCustomerReturnsDataDao.queryAmazonFbaFulfillmentCustomerReturnsDataList();
    }

    @Override
    public List<AmazonFbaFulfillmentCustomerReturnsData> queryAmazonFbaFulfillmentCustomerReturnsDatas(AmazonFbaFulfillmentCustomerReturnsDataQueryCondition query, Pager pager) {
        Assert.notNull(query, "query is null!");
        if (pager != null && pager.isQueryCount()) {
            int count = amazonFbaFulfillmentCustomerReturnsDataDao.queryAmazonFbaFulfillmentCustomerReturnsDataCount(query);
            pager.setTotalCount(count);
            if ( count == 0) {
                return new ArrayList<AmazonFbaFulfillmentCustomerReturnsData>();
            }
        }
        List<AmazonFbaFulfillmentCustomerReturnsData> amazonFbaFulfillmentCustomerReturnsDatas = amazonFbaFulfillmentCustomerReturnsDataDao.queryAmazonFbaFulfillmentCustomerReturnsDataList(query, pager);
        return amazonFbaFulfillmentCustomerReturnsDatas;
    }

    @Override
    public void createAmazonFbaFulfillmentCustomerReturnsData(AmazonFbaFulfillmentCustomerReturnsData amazonFbaFulfillmentCustomerReturnsData) {
        try {
            amazonFbaFulfillmentCustomerReturnsDataDao.createAmazonFbaFulfillmentCustomerReturnsData(amazonFbaFulfillmentCustomerReturnsData);
        }
        catch (SqlerException e) {
            log.error(e.getMessage(), e);
            throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
        }
    }

    @Override
    public void batchCreateAmazonFbaFulfillmentCustomerReturnsData(List<AmazonFbaFulfillmentCustomerReturnsData> entityList) {
        if (CollectionUtils.isNotEmpty(entityList)) {
            try {
                amazonFbaFulfillmentCustomerReturnsDataDao.batchCreateAmazonFbaFulfillmentCustomerReturnsData(entityList);
            }
            catch (SqlerException e) {
                log.error(e.getMessage(), e);
                throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
            }
        }
    }

    @Override
    public void deleteAmazonFbaFulfillmentCustomerReturnsData(Integer id) {
        try {
            amazonFbaFulfillmentCustomerReturnsDataDao.deleteAmazonFbaFulfillmentCustomerReturnsData(id);
        }
        catch (SqlerException e) {
            log.error(e.getMessage(), e);
            throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
        }
    }

    @Override
    public void updateAmazonFbaFulfillmentCustomerReturnsData(AmazonFbaFulfillmentCustomerReturnsData amazonFbaFulfillmentCustomerReturnsData) {
        try {
            amazonFbaFulfillmentCustomerReturnsDataDao.updateAmazonFbaFulfillmentCustomerReturnsData(amazonFbaFulfillmentCustomerReturnsData);
        }
        catch (SqlerException e) {
            log.error(e.getMessage(), e);
            throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
        }
    }

    @Override
    public void batchUpdateAmazonFbaFulfillmentCustomerReturnsData(List<AmazonFbaFulfillmentCustomerReturnsData> entityList) {
        if (CollectionUtils.isNotEmpty(entityList)) {
            try {
                amazonFbaFulfillmentCustomerReturnsDataDao.batchUpdateAmazonFbaFulfillmentCustomerReturnsData(entityList);
            }
            catch (SqlerException e) {
                log.error(e.getMessage(), e);
                throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
            }
        }
    }
}