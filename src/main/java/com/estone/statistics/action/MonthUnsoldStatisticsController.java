package com.estone.statistics.action;

import java.io.OutputStream;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import com.estone.common.enums.ExportType;
import com.estone.common.util.DateUtils;
import com.estone.common.util.POIUtils;
import com.estone.statistics.bean.MonthUnsoldStatistics;
import com.estone.statistics.bean.MonthUnsoldStatisticsQueryCondition;
import com.estone.statistics.domain.MonthUnsoldStatisticsDo;
import com.estone.statistics.service.MonthUnsoldStatisticsService;
import com.whq.tool.component.Pager;
import com.whq.tool.json.ResponseJson;

import lombok.extern.slf4j.Slf4j;

@Controller
@Slf4j
@RequestMapping(value = "monthUnsold")
public class MonthUnsoldStatisticsController {
    @Resource
    private MonthUnsoldStatisticsService monthUnsoldStatisticsService;

    @RequestMapping(method = {RequestMethod.GET})
    public String init(@ModelAttribute("domain") MonthUnsoldStatisticsDo domain) {
        initFormData(domain);
        return "statistics/whInvoicingChangeMonthCountLocal";
    }

    private void initFormData(@ModelAttribute("domain") MonthUnsoldStatisticsDo domain) {
        MonthUnsoldStatisticsQueryCondition query = domain.getQuery();
        if (query == null) {
            query = new MonthUnsoldStatisticsQueryCondition();
            domain.setQuery(query);
        }
        query.setReadOnly(true);
        String fromDate = query.getStartTime();
        if (StringUtils.isBlank(fromDate)) {
            Date firstDayOfMonth = DateUtils.getFirstDayOfLastMonth(new Date());
            query.setStartTime(DateUtils.dateToString(firstDayOfMonth, "yyyy-MM"));
        }
    }

    private void queryMonthUnsoldStatisticss(@ModelAttribute("domain") MonthUnsoldStatisticsDo domain) {
        MonthUnsoldStatisticsQueryCondition query = domain.getQuery();
        Pager page = domain.getPage();
        if (query == null) {
            query = new MonthUnsoldStatisticsQueryCondition();
            domain.setQuery(query);
        }
        // 默认读从库
        query.setReadOnly(true);
        List<MonthUnsoldStatistics> monthUnsoldStatisticss = monthUnsoldStatisticsService
                .queryMonthUnsoldStatisticss(query, page);
        domain.setMonthUnsoldStatisticss(monthUnsoldStatisticss);
    }

    @RequestMapping(value="search", method = {RequestMethod.POST})
    public String search(@ModelAttribute("domain") MonthUnsoldStatisticsDo domain) {
        initFormData(domain);
        queryMonthUnsoldStatisticss(domain);
        return "statistics/whInvoicingChangeMonthCountLocal";
    }

    @RequestMapping(value = "delete", method = { RequestMethod.GET })
    @ResponseBody
    public ResponseJson deleteMonthUnsoldStatistics(@ModelAttribute("domain") MonthUnsoldStatisticsDo domain,
            @RequestParam("monthUnsoldStatisticsId") Integer monthUnsoldStatisticsId) {
        ResponseJson response = new ResponseJson();
        monthUnsoldStatisticsService.deleteMonthUnsoldStatistics(monthUnsoldStatisticsId);
        return response;
    }

    // 临时促销部分数据
    @RequestMapping(value = "tempSync", method = { RequestMethod.GET })
    @ResponseBody
    public ResponseJson deleteMonthUnsoldStatistics() {
        ResponseJson response = new ResponseJson();
        monthUnsoldStatisticsService.tempSync();
        return response;
    }

    // 临时推送所有注销数据
    @RequestMapping(value = "tempSyncAll", method = { RequestMethod.GET })
    @ResponseBody
    public ResponseJson tempSyncAll() {
        ResponseJson response = new ResponseJson();
        monthUnsoldStatisticsService.tempSyncAll();
        return response;
    }

    private static String[] HEADERS = { "统计月份", "SKU", "销售属性", "末期库存", "末期单价", "末期金额", "折扣", "促销标签是否已取消"};

    /**
     * 导出
     *
     * @param domain
     * @param exportType
     * @param ids
     * @param response
     */
    @SuppressWarnings("incomplete-switch")
    @RequestMapping(value = "download", method = { RequestMethod.POST })
    @ResponseBody
    public void download(@ModelAttribute("domain") MonthUnsoldStatisticsDo domain,
            @RequestParam("exportType") String exportType,
            @RequestParam(value = "ids", required = false) List<Integer> ids, HttpServletResponse response) {

        MonthUnsoldStatisticsQueryCondition query = domain.getQuery();

        ExportType exportTypeEnum = ExportType.build(exportType);

        if (query == null) {
            query = new MonthUnsoldStatisticsQueryCondition();
        }else{
            if (!StringUtils.isEmpty(query.getSkuSplit())) {
                query.setSkuList(Arrays.asList(query.getSkuSplit().split(",")));
            }
        }
        query.setReadOnly(true);
        List<MonthUnsoldStatistics> whInvoicingChangeMonthCountLocals = new ArrayList<>();
        // 查询导出的数据
        switch (exportTypeEnum) {
            case ALL: {
                whInvoicingChangeMonthCountLocals = monthUnsoldStatisticsService.queryMonthUnsoldStatisticss(query, null);
            }
            break;
            case PAGE: {
                whInvoicingChangeMonthCountLocals = monthUnsoldStatisticsService.queryMonthUnsoldStatisticss(query, domain.getPage());
            }
            break;
            case CHECKED: {
                if (CollectionUtils.isNotEmpty(ids)) {
                    query = new MonthUnsoldStatisticsQueryCondition();
                    query.setIds(ids);
                    whInvoicingChangeMonthCountLocals = monthUnsoldStatisticsService.queryMonthUnsoldStatisticss(query, null);
                }
            }
            break;
        }

        OutputStream os = null;
        try {
            String fileName = "月度滞销统计" + System.currentTimeMillis() + ".xls";
            response.setContentType("application/ms-excel");
            response.setHeader("Content-Disposition",
                    "attachment; filename=" + java.net.URLEncoder.encode(fileName, "UTF-8"));
            os = response.getOutputStream();
            log.warn("download file name: " + fileName);
            final List<List<String>> apvData = new ArrayList<List<String>>();
            POIUtils.createExcel(HEADERS, whInvoicingChangeMonthCountLocals, entity -> {

                apvData.clear();

                List<String> apvlist = new ArrayList<String>(HEADERS.length);
                Timestamp stockDate = entity.getStockDate();
                if (stockDate == null) {
                    apvlist.add(POIUtils.transferObj2Str(""));
                }
                else {
                    apvlist.add(POIUtils.transferObj2Str(DateUtils.dateToString(stockDate, "yyyy-MM")));
                }

                apvlist.add(POIUtils.transferObj2Str(entity.getSku()));
                apvlist.add(POIUtils.transferObj2Str(entity.getSalesProperty()));
                apvlist.add(POIUtils.transferObj2Str(entity.getQuantity()));
                apvlist.add(POIUtils.transferObj2Str(entity.getEndingPurchasePrice()));
                apvlist.add(POIUtils.transferObj2Str(entity.getEndingAmount()));
                apvlist.add(POIUtils.transferObj2Str(entity.getDiscount())+"%");
                apvlist.add(POIUtils.transferObj2Str(entity.getPromotionLabel()));

                apvData.add(apvlist);
                return apvData;

            }, true, os);
            log.warn("---task execute end---");
        }
        catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        finally {
            IOUtils.closeQuietly(os);
        }
    }
}