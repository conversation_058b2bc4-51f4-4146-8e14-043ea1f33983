package com.estone.statistics.action;

import java.io.OutputStream;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.estone.common.SelectJson;
import com.estone.common.util.CompatibleSkuUtils;
import com.estone.common.util.DateUtils;
import com.estone.common.util.JedisUtils;
import com.estone.common.util.POIUtils;
import com.estone.sku.enums.WhSkuStatus;
import com.estone.statistics.bean.AssetChangeItemQueryCondition;
import com.estone.statistics.bean.WhAssetChangeItem;
import com.estone.statistics.bean.WhAssetChangeItemCalcBean;
import com.estone.statistics.bean.WhAssetChangeItemQueryCondition;
import com.estone.statistics.domain.WhAssetChangeItemDo;
import com.estone.statistics.enums.AssetInventoryType;
import com.estone.statistics.enums.AssetOrderType;
import com.estone.statistics.service.AssetChangeItemService;
import com.estone.statistics.service.WhAssetChangeItemService;
import com.whq.tool.action.BaseController;
import com.whq.tool.component.Pager;
import com.whq.tool.component.StatusCode;
import com.whq.tool.json.ResponseJson;

@Controller
@RequestMapping(value = "asset/item")
@Slf4j
public class WhAssetChangeItemController extends BaseController {
    @Resource
    private WhAssetChangeItemService whAssetChangeItemService;

    @Resource
    private AssetChangeItemService assetChangeItemService;

    final String ASSET_CALC_LOCK = "ASSET_CALC_LOCK_KEY";

    @RequestMapping(method = { RequestMethod.GET })
    public String init(@ModelAttribute("domain") WhAssetChangeItemDo domain) {
        initFormData(domain);
        return "statistics/asset_change_item_list";
    }

    private void initFormData(@ModelAttribute("domain") WhAssetChangeItemDo domain) {
        domain.setInventoryTypes(SelectJson.getList(AssetInventoryType.values()));// 资产库存类型
        domain.setOrderTypes(SelectJson.getList(AssetOrderType.values()));// 订单类型
        domain.setStatusSelectJson(SelectJson.getList(WhSkuStatus.values()));// sku状态

        WhAssetChangeItemQueryCondition query = domain.getQuery();
        if (query == null) {
            query = new WhAssetChangeItemQueryCondition();
            domain.setQuery(query);
        }
        String fromDate = query.getFromDate();
        if (StringUtils.isBlank(fromDate)) {
            Date firstDayOfMonth = DateUtils.getBeforeDate(new Date(), 1);
            fromDate = DateUtils.dateToString(firstDayOfMonth, "yyyy-MM-dd");
            query.setFromDate(fromDate.concat(" 00:00:00"));
        }

        String toDate = query.getToDate();
        if (StringUtils.isBlank(toDate)) {
            Date firstDayOfMonth = DateUtils.getBeforeDate(new Date(), 1);
            toDate = DateUtils.dateToString(firstDayOfMonth, "yyyy-MM-dd");
            query.setToDate(toDate.concat(" 23:59:59"));
        }
    }

    private void queryWhAssetChangeItems(@ModelAttribute("domain") WhAssetChangeItemDo domain) {
        WhAssetChangeItemQueryCondition query = domain.getQuery();
        Pager page = domain.getPage();
        if (query == null) {
            query = new WhAssetChangeItemQueryCondition();
            domain.setQuery(query);
        }
        query.setReadOnly(true);
        //兼容SKU编码和唯一码
        if(StringUtils.isNotBlank(query.getSkuStr())){
            query.setSkuStr(CompatibleSkuUtils.getSku(query.getSkuStr()));
        }
        if (query.getCheckOrderAssetDetail() != null && query.getCheckOrderAssetDetail()) {
            AssetChangeItemQueryCondition queryCondition = JSONObject.parseObject(JSONObject.toJSONString(query),
                    new TypeReference<AssetChangeItemQueryCondition>() {
                    });
            assetChangeItemService.queryAssetChangeItemsCount(queryCondition, page, domain);
        }
        else {
            whAssetChangeItemService.queryWhAssetChangeItemsCount(query, page, domain);
        }
    }

    @RequestMapping(value = "search", method = { RequestMethod.POST })
    public String search(@ModelAttribute("domain") WhAssetChangeItemDo domain) {
        initFormData(domain);
        queryWhAssetChangeItems(domain);
        return "statistics/asset_change_item_list";
    }

    @RequestMapping(value = "create", method = { RequestMethod.GET })
    public String toCreateWhAssetChangeItem(@ModelAttribute("domain") WhAssetChangeItemDo domain) {
        return "{模块}/{页面}";
    }

    @RequestMapping(value = "create", method = { RequestMethod.POST })
    public String createWhAssetChangeItem(@ModelAttribute("domain") WhAssetChangeItemDo domain, HttpSession session) {
        WhAssetChangeItem whAssetChangeItem = domain.getWhAssetChangeItem();
        whAssetChangeItemService.createWhAssetChangeItem(whAssetChangeItem);
        return "redirect:/{查询列表}";
    }

    @RequestMapping(value = "update", method = { RequestMethod.GET })
    public String toUpdateWhAssetChangeItem(@ModelAttribute("domain") WhAssetChangeItemDo domain,
            @RequestParam("whAssetChangeItemId") Integer whAssetChangeItemId) {
        WhAssetChangeItem whAssetChangeItem = whAssetChangeItemService.getWhAssetChangeItem(whAssetChangeItemId);
        domain.setWhAssetChangeItem(whAssetChangeItem);
        return "{模块}/{页面}";
    }

    @RequestMapping(value = "update", method = { RequestMethod.POST })
    public String updateWhAssetChangeItem(@ModelAttribute("domain") WhAssetChangeItemDo domain, HttpSession session) {
        WhAssetChangeItem whAssetChangeItem = domain.getWhAssetChangeItem();
        whAssetChangeItemService.updateWhAssetChangeItem(whAssetChangeItem);
        return "redirect:/{查询列表}";
    }

    @RequestMapping(value = "delete", method = { RequestMethod.GET })
    @ResponseBody
    public ResponseJson deleteWhAssetChangeItem(@ModelAttribute("domain") WhAssetChangeItemDo domain,
            @RequestParam("whAssetChangeItemId") Integer whAssetChangeItemId) {
        ResponseJson response = new ResponseJson();
        whAssetChangeItemService.deleteWhAssetChangeItem(whAssetChangeItemId);
        return response;
    }

    String[] headers = { "ID", "SKU", "SKU状态", "日期", "库存类型", "单据类型", "单号", "期初库存数量", "期初库存单价", "期初库存金额", "本期入库数量",
            "本期入库单价", "本期入库金额", "期末库存数量", "期末库存金额", "期末库存单价" };

    @RequestMapping(value = "download", method = { RequestMethod.POST })
    @ResponseBody
    public void download(@ModelAttribute("domain") WhAssetChangeItemDo domain,
            @RequestParam("exportType") String exportType, HttpServletResponse response) {
        WhAssetChangeItemQueryCondition query = domain.getQuery();
        if (query == null) {
            query = new WhAssetChangeItemQueryCondition();
            domain.setQuery(query);
        }
        query.setReadOnly(true);
        List<WhAssetChangeItem> itemList = new ArrayList<WhAssetChangeItem>();
        Pager page = new Pager();
        switch (exportType) {
            case "CURRENT_ALL":
                page.setPageSize(100000);
                if (query.getCheckOrderAssetDetail() != null && query.getCheckOrderAssetDetail()) {
                    AssetChangeItemQueryCondition queryCondition = JSONObject.parseObject(
                            JSONObject.toJSONString(query), new TypeReference<AssetChangeItemQueryCondition>() {
                            });
                    itemList = JSONObject.parseObject(
                            JSONObject.toJSONString(assetChangeItemService.queryAssetChangeItems(queryCondition, page)),
                            new TypeReference<List<WhAssetChangeItem>>() {
                            });
                }
                else {
                    itemList = whAssetChangeItemService.queryWhAssetChangeItems(query, page);
                }
                break;
            case "CURRENT_PAGE":
                page = domain.getPage();
                if (query.getCheckOrderAssetDetail() != null && query.getCheckOrderAssetDetail()) {
                    AssetChangeItemQueryCondition queryCondition = JSONObject.parseObject(
                            JSONObject.toJSONString(query), new TypeReference<AssetChangeItemQueryCondition>() {
                            });
                    itemList = JSONObject.parseObject(
                            JSONObject.toJSONString(assetChangeItemService.queryAssetChangeItems(queryCondition, page)),
                            new TypeReference<List<WhAssetChangeItem>>() {
                            });
                }
                else {
                    itemList = whAssetChangeItemService.queryWhAssetChangeItems(query, page);
                }
                break;
            case "CURRENT_CHECK":
                if (CollectionUtils.isNotEmpty(query.getIds())) {
                    WhAssetChangeItemQueryCondition queryCondition = new WhAssetChangeItemQueryCondition();
                    queryCondition.setIds(query.getIds());
                    if (query.getCheckOrderAssetDetail() != null && query.getCheckOrderAssetDetail()) {
                        AssetChangeItemQueryCondition query1 = new AssetChangeItemQueryCondition();
                        query1.setIds(query.getIds());
                        itemList = JSONObject.parseObject(
                                JSONObject.toJSONString(assetChangeItemService.queryAssetChangeItems(query1, page)),
                                new TypeReference<List<WhAssetChangeItem>>() {
                                });
                    }
                    else {
                        itemList = whAssetChangeItemService.queryWhAssetChangeItems(queryCondition, null);
                    }
                }
                break;
        }
        OutputStream os = null;
        try {
            String fileName = "资产变动明细" + System.currentTimeMillis() + ".xls";
            response.setContentType("application/ms-excel");
            response.setHeader("Content-Disposition",
                    "attachment; filename=" + java.net.URLEncoder.encode(fileName, "UTF-8"));
            os = response.getOutputStream();
            final List<List<String>> whAssetItemData = new ArrayList<List<String>>();
            if (CollectionUtils.isNotEmpty(itemList)) {
                POIUtils.createExcel(headers, itemList, item -> {
                    whAssetItemData.clear();
                    List<String> datalist = new ArrayList<String>(headers.length);
                    datalist.add(POIUtils.transferObj2Str(item.getId()));
                    datalist.add(POIUtils.transferObj2Str(item.getSku()));
                    datalist.add(
                            POIUtils.transferObj2Str(WhSkuStatus.getDisplayByCode(item.getSkuStatus().toString())));
                    datalist.add(POIUtils.transferObj2Str(item.getCreateDate()));
                    datalist.add(POIUtils.transferObj2Str(item.getInventoryTypeName()));
                    datalist.add(POIUtils.transferObj2Str(item.getOrderTypeName()));
                    datalist.add(POIUtils.transferObj2Str(item.getOrderNo()));
                    datalist.add(POIUtils.transferObj2Str(item.getOpeningInventoryQuantity()));
                    datalist.add(POIUtils.transferObj2Str(item.getOpeningInventoryPrice()));
                    datalist.add(POIUtils.transferObj2Str(item.getOpeningInventoryAmount()));
                    datalist.add(POIUtils.transferObj2Str(item.getCurrentCheckinQuantity()));
                    datalist.add(POIUtils.transferObj2Str(item.getCurrentCheckinPrice()));
                    datalist.add(POIUtils.transferObj2Str(item.getCurrentCheckinAmount()));
                    datalist.add(POIUtils.transferObj2Str(item.getEndingInventoryQuantity()));
                    datalist.add(POIUtils.transferObj2Str(item.getEndingInventoryAmount()));
                    datalist.add(POIUtils.transferObj2Str(item.getEndingInventoryPrice()));
                    whAssetItemData.add(datalist);
                    return whAssetItemData;
                }, true, os);
            }
        }
        catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        finally {
            IOUtils.closeQuietly(os);
        }
    }
    
    @RequestMapping(value = "calc", method = { RequestMethod.POST })
    @ResponseBody
    public ResponseJson calc(@ModelAttribute("domain") WhAssetChangeItemDo domain) {
        ResponseJson response = new ResponseJson();
        response.setStatus(StatusCode.SUCCESS);
        if (JedisUtils.exists(ASSET_CALC_LOCK)) {
            response.setStatus(StatusCode.FAIL);
            response.setMessage("有账号正在计算，请稍后再试！");
            return response;
        }
        else {
            JedisUtils.set(ASSET_CALC_LOCK, "lock", 600L);
        }
        try {
            List<WhAssetChangeItemCalcBean> calcBeanList = whAssetChangeItemService.queryWhAssetChangeItemCalcBean();
            whAssetChangeItemService.batchUpdateWhAssetChangeItemByCalculate(calcBeanList);
        }
        finally {
            JedisUtils.del(ASSET_CALC_LOCK);
        }
        return response;
    }

    @RequestMapping(value = "calcFistSku", method = { RequestMethod.GET })
    @ResponseBody
    public ResponseJson calcFistSku(@ModelAttribute("domain") WhAssetChangeItemDo domain) {
        ResponseJson response = new ResponseJson();
        response.setStatus(StatusCode.SUCCESS);
        if (JedisUtils.exists(ASSET_CALC_LOCK)) {
            response.setStatus(StatusCode.FAIL);
            response.setMessage("有账号正在计算，请稍后再试！");
            return response;
        }
        else {
            JedisUtils.set(ASSET_CALC_LOCK, "lock", 600L);
        }
        try {
            whAssetChangeItemService.callFistAndPriceZeroWhAssetChangeItemGroupBySku();
        }
        finally {
            JedisUtils.del(ASSET_CALC_LOCK);
        }
        return response;
    }
}