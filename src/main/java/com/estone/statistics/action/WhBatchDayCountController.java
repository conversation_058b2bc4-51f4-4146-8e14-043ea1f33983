package com.estone.statistics.action;

import java.io.OutputStream;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.poi.util.IOUtils;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import com.estone.common.util.DateUtils;
import com.estone.common.util.POIUtils;
import com.estone.statistics.bean.WhBatchDayCount;
import com.estone.statistics.bean.WhBatchDayCountQueryCondition;
import com.estone.statistics.domain.WhBatchDayCountDo;
import com.estone.statistics.service.WhBatchDayCountService;
import com.whq.tool.action.BaseController;
import com.whq.tool.component.Pager;
import com.whq.tool.json.ResponseJson;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Controller
@RequestMapping(value = "batchDayCount")
public class WhBatchDayCountController extends BaseController {
    @Resource
    private WhBatchDayCountService whBatchDayCountService;

    @RequestMapping(method = {RequestMethod.GET})
    public String init(@ModelAttribute("domain") WhBatchDayCountDo domain) {
        WhBatchDayCountQueryCondition query = domain.getQuery();
        if (query == null) {
            query = new WhBatchDayCountQueryCondition();
        }
        domain.setQuery(query);
        initFormData(domain);
        queryWhBatchDayCounts(domain);
        return "statistics/wh_batch_day_count_list";

    }

    private void initFormData(@ModelAttribute("domain") WhBatchDayCountDo domain) {
        WhBatchDayCountQueryCondition query = domain.getQuery();
        if (query == null) {
            query = new WhBatchDayCountQueryCondition();
            domain.setQuery(query);
        }
        query.setReadOnly(true);
        String fromDate = query.getQueryTime();
        if (StringUtils.isBlank(fromDate)) {
            Date firstDayOfMonth = DateUtils.getYesterdayDate(new Date());
            String fmt = "yyyy-MM-dd";
            query.setQueryTime(DateUtils.dateToString(firstDayOfMonth, fmt));
        }
    }

    private void queryWhBatchDayCounts(@ModelAttribute("domain") WhBatchDayCountDo domain) {
        WhBatchDayCountQueryCondition query = domain.getQuery();
        Pager page = domain.getPage();
        if (query == null)
        {
            query = new WhBatchDayCountQueryCondition();
            domain.setQuery(query);
        }
        List<WhBatchDayCount> whBatchDayCounts = whBatchDayCountService.queryWhBatchDayCounts(query, page);
        domain.setWhBatchDayCounts(whBatchDayCounts);
        if (query.getStatisticsTotal()!=null&&query.getStatisticsTotal()) {
            domain.setWhBatchDayCount(whBatchDayCountService.queryWhBatchDayCountSum(query));
        }
    }

    @RequestMapping(value="search", method = {RequestMethod.POST})
    public String search(@ModelAttribute("domain") WhBatchDayCountDo domain) {
        initFormData(domain);
        queryWhBatchDayCounts(domain);
        return "statistics/wh_batch_day_count_list";
    }

    @RequestMapping(value="create", method = {RequestMethod.GET})
    public String toCreateWhBatchDayCount(@ModelAttribute("domain") WhBatchDayCountDo domain) {
        return "{模块}/{页面}";
    }

    @RequestMapping(value="create", method = {RequestMethod.POST})
    public String createWhBatchDayCount(@ModelAttribute("domain") WhBatchDayCountDo domain) {
        WhBatchDayCount whBatchDayCount = domain.getWhBatchDayCount();
        whBatchDayCountService.createWhBatchDayCount(whBatchDayCount);
        return "redirect:/{查询列表}";
    }

    @RequestMapping(value="update", method = {RequestMethod.GET})
    public String toUpdateWhBatchDayCount(@ModelAttribute("domain") WhBatchDayCountDo domain, @RequestParam("whBatchDayCountId") Integer whBatchDayCountId) {
        WhBatchDayCount whBatchDayCount = whBatchDayCountService.getWhBatchDayCount(whBatchDayCountId);
        domain.setWhBatchDayCount(whBatchDayCount);
        return "{模块}/{页面}";
    }

    @RequestMapping(value="update", method = {RequestMethod.POST})
    public String updateWhBatchDayCount(@ModelAttribute("domain") WhBatchDayCountDo domain) {
        WhBatchDayCount whBatchDayCount = domain.getWhBatchDayCount();
        whBatchDayCountService.updateWhBatchDayCount(whBatchDayCount);
        return "redirect:/{查询列表}";
    }

    @RequestMapping(value="delete", method = {RequestMethod.GET})
    @ResponseBody
    public ResponseJson deleteWhBatchDayCount(@ModelAttribute("domain") WhBatchDayCountDo domain, @RequestParam("whBatchDayCountId") Integer whBatchDayCountId) {
        ResponseJson response = new ResponseJson();
        whBatchDayCountService.deleteWhBatchDayCount(whBatchDayCountId);
        return response;
    }

    /**
     * 导出
     * @param domain
     * @param ids
     * @param response
     */
    @RequestMapping(value = "download", method = { RequestMethod.POST })
    @ResponseBody
    public void download(@ModelAttribute("domain") WhBatchDayCountDo domain,
            @RequestParam(value = "ids", required = false) List<Integer> ids, HttpServletResponse response) {
        String[] HEADERS = { "编号", "仓库", "批次号", "SKU", "库存日期", "入库库存", "期末库存", "采购成本单价", "采购运费单价", "头程运费单价", "头程税费单价",
                "期末库存金额", "入库时间" };

        WhBatchDayCountQueryCondition query = domain.getQuery();
        if (query == null || CollectionUtils.isNotEmpty(ids)) {
            query = new WhBatchDayCountQueryCondition();
            query.setIds(ids);
        }
        query.setReadOnly(true);
        List<WhBatchDayCount> whBatchDayCountList = whBatchDayCountService.queryWhBatchDayCounts(query, null);

        // 大于100W不能导出
        if (CollectionUtils.isEmpty(whBatchDayCountList) || whBatchDayCountList.size() > 1000000) {
            return;
        }
        OutputStream os = null;
        try {
            String fileName = "出入库明细每日批次汇总" + System.currentTimeMillis() + ".xls";
            response.setContentType("application/ms-excel");
            response.setHeader("Content-Disposition",
                    "attachment; filename=" + java.net.URLEncoder.encode(fileName, "UTF-8"));
            os = response.getOutputStream();
            log.warn("download file name: " + fileName);
            final List<List<String>> batchReturnData = new ArrayList<>();
            String[] finalHEADERS = HEADERS;
            POIUtils.createExcel(HEADERS, whBatchDayCountList, whBatchDayCount -> {

                batchReturnData.clear();

                List<String> batchReturnList = new ArrayList<String>(finalHEADERS.length);
                batchReturnList.add(POIUtils.transferObj2Str(whBatchDayCount.getId()));
                batchReturnList.add(POIUtils.transferObj2Str(whBatchDayCount.getStoreCode()));
                batchReturnList.add(POIUtils.transferObj2Str(whBatchDayCount.getBatchNo()));
                batchReturnList.add(POIUtils.transferObj2Str(whBatchDayCount.getSku()));
                Timestamp stockDate = whBatchDayCount.getCountDate();
                if (stockDate == null) {
                    batchReturnList.add(POIUtils.transferObj2Str(""));
                }
                else {
                    batchReturnList.add(POIUtils.transferObj2Str(DateUtils.dateToString(stockDate, "yyyy-MM-dd")));
                }
                batchReturnList.add(POIUtils.transferObj2Str(whBatchDayCount.getQuantity()));
                batchReturnList.add(POIUtils.transferObj2Str(whBatchDayCount.getEndStock()));
                batchReturnList.add(POIUtils.transferObj2Str(whBatchDayCount.getPurchaseCostPrice()));
                batchReturnList.add(POIUtils.transferObj2Str(whBatchDayCount.getPurchaseFreightPrice()));
                batchReturnList.add(POIUtils.transferObj2Str(whBatchDayCount.getProductFreightPrice()));
                batchReturnList.add(POIUtils.transferObj2Str(whBatchDayCount.getProductTaxPrice()));
                batchReturnList.add(POIUtils.transferObj2Str(whBatchDayCount.getEndAmount()));
                batchReturnList.add(POIUtils.transferObj2Str(DateUtils
                        .dateToString(whBatchDayCount.getReportTime(), DateUtils.STANDARD_DATE_PATTERN)));
                batchReturnData.add(batchReturnList);
                return batchReturnData;

            }, true, os);
            log.warn("---task execute end---");
        }
        catch (Exception e) {
            log.warn(e.getMessage(), e);
        }
        finally {
            IOUtils.closeQuietly(os);
        }
    }
}