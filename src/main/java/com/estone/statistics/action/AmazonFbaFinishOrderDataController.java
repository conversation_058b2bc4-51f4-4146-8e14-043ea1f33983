package com.estone.statistics.action;

import com.estone.statistics.bean.AmazonFbaFinishOrderData;
import com.estone.statistics.bean.AmazonFbaFinishOrderDataQueryCondition;
import com.estone.statistics.domain.AmazonFbaFinishOrderDataDo;
import com.estone.statistics.service.AmazonFbaFinishOrderDataService;
import com.whq.tool.action.BaseController;
import com.whq.tool.component.Pager;
import com.whq.tool.json.ResponseJson;
import java.util.List;
import javax.annotation.Resource;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping(value = "finishOrderData")
public class AmazonFbaFinishOrderDataController extends BaseController {
    @Resource
    private AmazonFbaFinishOrderDataService amazonFbaFinishOrderDataService;

    @RequestMapping(method = {RequestMethod.GET})
    public String init(@ModelAttribute("domain") AmazonFbaFinishOrderDataDo domain) {
        return "{模块}/{页面}";
    }

    private void initFormData(@ModelAttribute("domain") AmazonFbaFinishOrderDataDo domain) {
         
    }

    private void queryAmazonFbaFinishOrderDatas(@ModelAttribute("domain") AmazonFbaFinishOrderDataDo domain) {
        AmazonFbaFinishOrderDataQueryCondition query = domain.getQuery();
        Pager page = domain.getPage();
        if (query == null)
        {
            query = new AmazonFbaFinishOrderDataQueryCondition();
            domain.setQuery(query);
        }
        // 默认读从库
        query.setReadOnly(true);
        List<AmazonFbaFinishOrderData> amazonFbaFinishOrderDatas = amazonFbaFinishOrderDataService.queryAmazonFbaFinishOrderDatas(query, page);
        domain.setAmazonFbaFinishOrderDatas(amazonFbaFinishOrderDatas);
    }

    @RequestMapping(value="search", method = {RequestMethod.POST})
    public String search(@ModelAttribute("domain") AmazonFbaFinishOrderDataDo domain) {
        initFormData(domain);
        queryAmazonFbaFinishOrderDatas(domain);
        return "{模块}/{页面}";
    }

    @RequestMapping(value="create", method = {RequestMethod.GET})
    public String toCreateAmazonFbaFinishOrderData(@ModelAttribute("domain") AmazonFbaFinishOrderDataDo domain) {
        return "{模块}/{页面}";
    }

    @RequestMapping(value="create", method = {RequestMethod.POST})
    public String createAmazonFbaFinishOrderData(@ModelAttribute("domain") AmazonFbaFinishOrderDataDo domain) {
        AmazonFbaFinishOrderData amazonFbaFinishOrderData = domain.getAmazonFbaFinishOrderData();
        amazonFbaFinishOrderDataService.createAmazonFbaFinishOrderData(amazonFbaFinishOrderData);
        return "redirect:/{查询列表}";
    }

    @RequestMapping(value="update", method = {RequestMethod.GET})
    public String toUpdateAmazonFbaFinishOrderData(@ModelAttribute("domain") AmazonFbaFinishOrderDataDo domain, @RequestParam("amazonFbaFinishOrderDataId") Integer amazonFbaFinishOrderDataId) {
        AmazonFbaFinishOrderData amazonFbaFinishOrderData = amazonFbaFinishOrderDataService.getAmazonFbaFinishOrderData(amazonFbaFinishOrderDataId);
        domain.setAmazonFbaFinishOrderData(amazonFbaFinishOrderData);
        return "{模块}/{页面}";
    }

    @RequestMapping(value="update", method = {RequestMethod.POST})
    public String updateAmazonFbaFinishOrderData(@ModelAttribute("domain") AmazonFbaFinishOrderDataDo domain) {
        AmazonFbaFinishOrderData amazonFbaFinishOrderData = domain.getAmazonFbaFinishOrderData();
        amazonFbaFinishOrderDataService.updateAmazonFbaFinishOrderData(amazonFbaFinishOrderData);
        return "redirect:/{查询列表}";
    }

    @RequestMapping(value="delete", method = {RequestMethod.GET})
    @ResponseBody
    public ResponseJson deleteAmazonFbaFinishOrderData(@ModelAttribute("domain") AmazonFbaFinishOrderDataDo domain, @RequestParam("amazonFbaFinishOrderDataId") Integer amazonFbaFinishOrderDataId) {
        ResponseJson response = new ResponseJson();
        amazonFbaFinishOrderDataService.deleteAmazonFbaFinishOrderData(amazonFbaFinishOrderDataId);
        return response;
    }
}