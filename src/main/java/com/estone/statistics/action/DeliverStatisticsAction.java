package com.estone.statistics.action;

import com.alibaba.excel.EasyExcel;
import com.estone.common.util.model.ApiResult;
import com.estone.statistics.bean.DeliverStatisticRecord;
import com.estone.statistics.bean.DeliverStatisticsQueryCondition;
import com.estone.statistics.service.DeliverStatisticsRecordService;
import com.whq.tool.action.BaseController;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.List;

/**
 * 交运报表
 */
@Slf4j
@RestController
@RequestMapping(value = "statistics/deliver")
public class DeliverStatisticsAction extends BaseController {

    @Resource
    private DeliverStatisticsRecordService deliverStatisticsRecordService;

    @RequestMapping(value = "search", method = { RequestMethod.POST })
    public ApiResult search(@RequestBody DeliverStatisticsQueryCondition query) {
        if(query.getDateType() == null || StringUtils.isBlank(query.getStartTime()) || StringUtils.isBlank(query.getEndTime())){
            return ApiResult.newError("参数缺失");
        }
        query.setReadOnly(true);
        return ApiResult.newSuccess(deliverStatisticsRecordService.queryDeliverStatisticsRecord(query));
    }

    @RequestMapping(value = "download", method = { RequestMethod.POST })
    public void download(@RequestBody DeliverStatisticsQueryCondition query, HttpServletResponse response) {
        if(query.getDateType() == null || StringUtils.isBlank(query.getStartTime()) || StringUtils.isBlank(query.getEndTime())){
            return;
        }
        query.setReadOnly(true);
        List<DeliverStatisticRecord> records = deliverStatisticsRecordService.queryDeliverStatisticsRecord(query);
        if (CollectionUtils.isEmpty(records)) {
            return;
        }
        try {
            response.setContentType("application/vnd.ms-excel");
            response.setCharacterEncoding("UTF-8");
            response.setHeader("Content-disposition", "attachment;filename=交运销售属性报表" + System.currentTimeMillis() + ".xlsx");
            EasyExcel.write(response.getOutputStream()).autoCloseStream(Boolean.TRUE)
                    .head(getExcelHeads(records)).sheet("交运销售属性报表")
                    .doWrite(getExcelDatas(records));
        } catch (Exception e) {
            log.error("download DeliverStatisticsRecord error:" + e.getMessage(), e);
        }
    }

    private List<List<String>> getExcelHeads(List<DeliverStatisticRecord> records){
        List<List<String>> heads = new ArrayList<>();
        ArrayList<String> head1 = new ArrayList<>();
        head1.add("销售属性");
        heads.add(head1);
        for (DeliverStatisticRecord record : records) {
            ArrayList<String> head = new ArrayList<>();
            head.add(record.getGroupDay());
            head.add("订单数");
            ArrayList<String> head2 = new ArrayList<>();
            head2.add(record.getGroupDay());
            head2.add("占比");
            heads.add(head);
            heads.add(head2);
        }
        return heads;
    }

    public static final String[] COLUMNS = {"爆款", "热销", "畅销", "平销", "低销", "滞销", "短呆滞", "长呆滞", "新品", "空白", "合计"};

    private List<List<String>> getExcelDatas(List<DeliverStatisticRecord> records){
        List<List<String>> datas = new ArrayList<>();
        for (String column : COLUMNS) {
            List<String> row = new ArrayList();
            row.add(column);
            for (DeliverStatisticRecord record : records) {
                switch (column) {
                    case "爆款":
                        row.add(String.valueOf(record.getInVogueNum()));
                        row.add(record.getInVogueRatio());
                        break;
                    case "热销":
                        row.add(String.valueOf(record.getHotSaleNum()));
                        row.add(record.getHotSaleRatio());
                        break;
                    case "畅销":
                        row.add(String.valueOf(record.getBestsellingNum()));
                        row.add(record.getBestsellingRatio());
                        break;
                    case "平销":
                        row.add(String.valueOf(record.getNormalNum()));
                        row.add(record.getNormalRatio());
                        break;
                    case "低销":
                        row.add(String.valueOf(record.getLowSellingNum()));
                        row.add(record.getLowSellingRatio());
                        break;
                    case "滞销":
                        row.add(String.valueOf(record.getUnsoldNum()));
                        row.add(record.getUnsoldRatio());
                        break;
                    case "短呆滞":
                        row.add(String.valueOf(record.getShortTermUnsoldNum()));
                        row.add(record.getShortTermUnsoldRatio());
                        break;
                    case "长呆滞":
                        row.add(String.valueOf(record.getLongTermUnsoldNum()));
                        row.add(record.getLongTermUnsoldRatio());
                        break;
                    case "新品":
                        row.add(String.valueOf(record.getNewProductNum()));
                        row.add(record.getNewProductRatio());
                        break;
                    case "空白":
                        row.add(String.valueOf(record.getBlankNum()));
                        row.add(record.getBlankRatio());
                        break;
                    case "合计":
                        row.add(String.valueOf(record.getCount()));
                        row.add(record.getCountRatio());
                        break;
                }
            }
            datas.add(row);
        }
        return datas;
    }

}
