package com.estone.statistics.domain;

import com.estone.statistics.bean.WhSkuSaleStatisticReport;
import com.estone.statistics.bean.WhSkuSaleStatisticReportQueryCondition;
import com.whq.tool.component.Pager;
import java.util.ArrayList;
import java.util.List;

public class WhSkuSaleStatisticReportDo {
    private WhSkuSaleStatisticReport whSkuSaleStatisticReport;

    private WhSkuSaleStatisticReportQueryCondition query;

    private List<WhSkuSaleStatisticReport> whSkuSaleStatisticReports = new ArrayList<WhSkuSaleStatisticReport>();

    private Pager page = new Pager();

    public WhSkuSaleStatisticReport getWhSkuSaleStatisticReport() {
        return whSkuSaleStatisticReport;
    }

    public void setWhSkuSaleStatisticReport(WhSkuSaleStatisticReport whSkuSaleStatisticReport) {
        this.whSkuSaleStatisticReport = whSkuSaleStatisticReport;
    }

    public WhSkuSaleStatisticReportQueryCondition getQuery() {
        return query;
    }

    public void setQuery(WhSkuSaleStatisticReportQueryCondition query) {
        this.query = query;
    }

    public List<WhSkuSaleStatisticReport> getWhSkuSaleStatisticReports() {
        return whSkuSaleStatisticReports;
    }

    public void setWhSkuSaleStatisticReports(List<WhSkuSaleStatisticReport> whSkuSaleStatisticReports) {
        this.whSkuSaleStatisticReports = whSkuSaleStatisticReports;
    }

    public Pager getPage() {
        return page;
    }

    public void setPage(Pager page) {
        this.page = page;
    }
}