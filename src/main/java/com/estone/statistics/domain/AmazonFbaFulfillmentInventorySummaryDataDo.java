package com.estone.statistics.domain;

import com.estone.statistics.bean.AmazonFbaFulfillmentInventorySummaryData;
import com.estone.statistics.bean.AmazonFbaFulfillmentInventorySummaryDataQueryCondition;
import com.whq.tool.component.Pager;
import java.util.ArrayList;
import java.util.List;

public class AmazonFbaFulfillmentInventorySummaryDataDo {
    private AmazonFbaFulfillmentInventorySummaryData amazonFbaFulfillmentInventorySummaryData;

    private AmazonFbaFulfillmentInventorySummaryDataQueryCondition query;

    private List<AmazonFbaFulfillmentInventorySummaryData> amazonFbaFulfillmentInventorySummaryDatas = new ArrayList<AmazonFbaFulfillmentInventorySummaryData>();

    private Pager page = new Pager();

    public AmazonFbaFulfillmentInventorySummaryData getAmazonFbaFulfillmentInventorySummaryData() {
        return amazonFbaFulfillmentInventorySummaryData;
    }

    public void setAmazonFbaFulfillmentInventorySummaryData(AmazonFbaFulfillmentInventorySummaryData amazonFbaFulfillmentInventorySummaryData) {
        this.amazonFbaFulfillmentInventorySummaryData = amazonFbaFulfillmentInventorySummaryData;
    }

    public AmazonFbaFulfillmentInventorySummaryDataQueryCondition getQuery() {
        return query;
    }

    public void setQuery(AmazonFbaFulfillmentInventorySummaryDataQueryCondition query) {
        this.query = query;
    }

    public List<AmazonFbaFulfillmentInventorySummaryData> getAmazonFbaFulfillmentInventorySummaryDatas() {
        return amazonFbaFulfillmentInventorySummaryDatas;
    }

    public void setAmazonFbaFulfillmentInventorySummaryDatas(List<AmazonFbaFulfillmentInventorySummaryData> amazonFbaFulfillmentInventorySummaryDatas) {
        this.amazonFbaFulfillmentInventorySummaryDatas = amazonFbaFulfillmentInventorySummaryDatas;
    }

    public Pager getPage() {
        return page;
    }

    public void setPage(Pager page) {
        this.page = page;
    }
}