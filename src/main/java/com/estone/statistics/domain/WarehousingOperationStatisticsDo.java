/**
 * @Title: WarehousingOperationStatisticsDo.java
 * @Package com.estone.statistics.domain
 * @Description: TODO
 * <AUTHOR>
 * @date 2019年2月14日
 * @version 0.0.2
 */
package com.estone.statistics.domain;

import java.util.ArrayList;
import java.util.List;

import com.estone.statistics.bean.WarehousingOperationStatistics;
import com.estone.statistics.bean.WarehousingOperationStatisticsQueryCondition;
import com.estone.warehouse.bean.WhWarehouse;
import com.whq.tool.component.Pager;

/**
 * @ClassName: WarehousingOperationStatisticsDo
 * @Description: TODO
 * <AUTHOR>
 * @date 2019年2月14日
 * @version 0.0.2
 *
 */
public class WarehousingOperationStatisticsDo {
    private WarehousingOperationStatistics warehousingOperationStatistics;

    private WarehousingOperationStatisticsQueryCondition query = new WarehousingOperationStatisticsQueryCondition();

    private List<WarehousingOperationStatistics> warehousingOperationStatistices;

    private Pager page = new Pager();

    private List<WhWarehouse> warehouseList = new ArrayList<>();// 仓库

    public WarehousingOperationStatistics getWarehousingOperationStatistics() {
        return warehousingOperationStatistics;
    }

    public void setWarehousingOperationStatistics(WarehousingOperationStatistics warehousingOperationStatistics) {
        this.warehousingOperationStatistics = warehousingOperationStatistics;
    }

    public WarehousingOperationStatisticsQueryCondition getQuery() {
        return query;
    }

    public void setQuery(WarehousingOperationStatisticsQueryCondition query) {
        this.query = query;
    }

    public List<WarehousingOperationStatistics> getWarehousingOperationStatistices() {
        return warehousingOperationStatistices;
    }

    public void setWarehousingOperationStatistices(
            List<WarehousingOperationStatistics> warehousingOperationStatistices) {
        this.warehousingOperationStatistices = warehousingOperationStatistices;
    }

    public Pager getPage() {
        return page;
    }

    public void setPage(Pager page) {
        this.page = page;
    }

    public List<WhWarehouse> getWarehouseList() {
        return warehouseList;
    }

    public void setWarehouseList(List<WhWarehouse> warehouseList) {
        this.warehouseList = warehouseList;
    }

}
