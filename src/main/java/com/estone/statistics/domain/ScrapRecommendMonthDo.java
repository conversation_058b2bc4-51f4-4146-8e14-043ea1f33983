package com.estone.statistics.domain;

import com.estone.statistics.bean.ScrapRecommendMonth;
import com.estone.statistics.bean.ScrapRecommendMonthQueryCondition;
import com.whq.tool.component.Pager;
import java.util.ArrayList;
import java.util.List;

public class ScrapRecommendMonthDo {
    private ScrapRecommendMonth scrapRecommendMonth;

    private ScrapRecommendMonthQueryCondition query;

    private List<ScrapRecommendMonth> scrapRecommendMonths = new ArrayList<ScrapRecommendMonth>();

    private Pager page = new Pager();

    public ScrapRecommendMonth getScrapRecommendMonth() {
        return scrapRecommendMonth;
    }

    public void setScrapRecommendMonth(ScrapRecommendMonth scrapRecommendMonth) {
        this.scrapRecommendMonth = scrapRecommendMonth;
    }

    public ScrapRecommendMonthQueryCondition getQuery() {
        return query;
    }

    public void setQuery(ScrapRecommendMonthQueryCondition query) {
        this.query = query;
    }

    public List<ScrapRecommendMonth> getScrapRecommendMonths() {
        return scrapRecommendMonths;
    }

    public void setScrapRecommendMonths(List<ScrapRecommendMonth> scrapRecommendMonths) {
        this.scrapRecommendMonths = scrapRecommendMonths;
    }

    public Pager getPage() {
        return page;
    }

    public void setPage(Pager page) {
        this.page = page;
    }
}