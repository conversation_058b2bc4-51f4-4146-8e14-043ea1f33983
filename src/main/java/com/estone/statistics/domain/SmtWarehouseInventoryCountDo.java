package com.estone.statistics.domain;

import com.estone.statistics.bean.SmtWarehouseInventoryCount;
import com.estone.statistics.bean.SmtWarehouseInventoryCountQueryCondition;
import com.whq.tool.component.Pager;
import java.util.ArrayList;
import java.util.List;

public class SmtWarehouseInventoryCountDo {
    private SmtWarehouseInventoryCount smtWarehouseInventoryCount;

    private SmtWarehouseInventoryCountQueryCondition query;

    private List<SmtWarehouseInventoryCount> smtWarehouseInventoryCounts = new ArrayList<SmtWarehouseInventoryCount>();

    private Pager page = new Pager();

    public SmtWarehouseInventoryCount getSmtWarehouseInventoryCount() {
        return smtWarehouseInventoryCount;
    }

    public void setSmtWarehouseInventoryCount(SmtWarehouseInventoryCount smtWarehouseInventoryCount) {
        this.smtWarehouseInventoryCount = smtWarehouseInventoryCount;
    }

    public SmtWarehouseInventoryCountQueryCondition getQuery() {
        return query;
    }

    public void setQuery(SmtWarehouseInventoryCountQueryCondition query) {
        this.query = query;
    }

    public List<SmtWarehouseInventoryCount> getSmtWarehouseInventoryCounts() {
        return smtWarehouseInventoryCounts;
    }

    public void setSmtWarehouseInventoryCounts(List<SmtWarehouseInventoryCount> smtWarehouseInventoryCounts) {
        this.smtWarehouseInventoryCounts = smtWarehouseInventoryCounts;
    }

    public Pager getPage() {
        return page;
    }

    public void setPage(Pager page) {
        this.page = page;
    }
}