package com.estone.sowstockout.dao;

import com.estone.sowstockout.bean.GridExceptionItem;
import com.estone.sowstockout.bean.GridExceptionItemQueryCondition;
import com.whq.tool.component.Pager;
import java.util.List;

public interface GridExceptionItemDao {
    int queryGridExceptionItemCount(GridExceptionItemQueryCondition query);

    List<GridExceptionItem> queryGridExceptionItemList();

    List<GridExceptionItem> queryGridExceptionItemList(GridExceptionItemQueryCondition query, Pager pager);

    GridExceptionItem queryGridExceptionItem(Integer primaryKey);

    GridExceptionItem queryGridExceptionItem(GridExceptionItemQueryCondition query);

    void createGridExceptionItem(GridExceptionItem entity);

    void batchCreateGridExceptionItem(List<GridExceptionItem> entityList);

    void batchUpdateGridExceptionItem(List<GridExceptionItem> entityList);

    void deleteGridExceptionItem(Integer primaryKey);

    void updateGridExceptionItem(GridExceptionItem entity);
}