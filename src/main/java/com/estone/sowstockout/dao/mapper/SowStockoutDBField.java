package com.estone.sowstockout.dao.mapper;

public interface SowStockoutDBField {
    String ID = "id";

    String STOCKOUT_BOX = "stockout_box";

    String BOX = "box";

    String APV_NO = "apv_no";

    String APV_ID = "apv_id";

    String SKU = "sku";

    String NEED_QUANTITY = "need_quantity";

    String SCAN_QUANTITY = "scan_quantity";

    String CREATED_BY = "created_by";

    String CREATION_DATE = "creation_date";

    String STATUS = "status";

    String OLD_TASK_TYPE = "old_task_type";

    String FN_SKU = "fn_sku";
}