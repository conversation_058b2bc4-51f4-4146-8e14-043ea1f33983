package com.estone.sowstockout.service;

import com.estone.sowstockout.bean.SowStockout;
import com.estone.sowstockout.bean.SowStockoutQueryCondition;
import com.whq.tool.component.Pager;
import com.whq.tool.json.ResponseJson;

import java.util.List;

public interface SowStockoutService {
    /**
     * This method corresponds to the database table sow_stockout
     *
     * @mbggenerated Thu Oct 11 17:02:09 CST 2018
     */
    List<SowStockout> queryAllSowStockouts();

    /**
     * This method corresponds to the database table sow_stockout
     *
     * @mbggenerated Thu Oct 11 17:02:09 CST 2018
     */
    List<SowStockout> querySowStockouts(SowStockoutQueryCondition query, Pager pager);

    /**
     * This method corresponds to the database table sow_stockout
     *
     * @mbggenerated Thu Oct 11 17:02:09 CST 2018
     */
    SowStockout getSowStockout(Integer id);

    /**
     * This method corresponds to the database table sow_stockout
     *
     * @mbggenerated Thu Oct 11 17:02:09 CST 2018
     */
    SowStockout getSowStockoutDetail(Integer id);

    /**
     * This method corresponds to the database table sow_stockout
     *
     * @mbggenerated Thu Oct 11 17:02:09 CST 2018
     */
    SowStockout querySowStockout(SowStockoutQueryCondition query);

    /**
     * This method corresponds to the database table sow_stockout
     *
     * @mbggenerated Thu Oct 11 17:02:09 CST 2018
     */
    void createSowStockout(SowStockout sowStockout);

    /**
     * This method corresponds to the database table sow_stockout
     *
     * @mbggenerated Thu Oct 11 17:02:09 CST 2018
     */
    void batchCreateSowStockout(List<SowStockout> entityList);

    /**
     * This method corresponds to the database table sow_stockout
     *
     * @mbggenerated Thu Oct 11 17:02:09 CST 2018
     */
    void deleteSowStockout(Integer id);
    
    
    void deleteSowStockoutByStockoutBox(String stockoutBox);

    /**
     * This method corresponds to the database table sow_stockout
     *
     * @mbggenerated Thu Oct 11 17:02:09 CST 2018
     */
    void updateSowStockout(SowStockout sowStockout);

    /**
     * This method corresponds to the database table sow_stockout
     *
     * @mbggenerated Thu Oct 11 17:02:09 CST 2018
     */
    void batchUpdateSowStockout(List<SowStockout> entityList);

    void updateSowStockoutsByStockoutBox(String stockoutBox, Integer status);

    ResponseJson normalComplete(String box);
}