package com.estone.sowstockout.bean;

import lombok.Data;

import java.util.List;

@Data
public class GridExceptionQueryCondition extends GridException {
    private static final long serialVersionUID = 1L;

    private Boolean readOnly = false;

    private List<Integer> idList;
    private List<Integer> apvIdList;
    private List<String> apvNoList;
    private List<String> orgTaskNoList;
    private List<String> orgBoxNoList;
    private List<String> newTaskNoList;
    private List<String> newBoxNoList;
    private List<String> skuList;

    private String fromCreateDate;
    private String toCreateDate;

    private String fromHandleDate;
    private String toHandleDate;
}