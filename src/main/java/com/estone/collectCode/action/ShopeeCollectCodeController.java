package com.estone.collectCode.action;

import com.estone.collectCode.bean.ShopeeCollectCode;
import com.estone.collectCode.bean.ShopeeCollectCodeItem;
import com.estone.collectCode.bean.ShopeeCollectCodeItemQueryCondition;
import com.estone.collectCode.bean.ShopeeCollectCodeQueryCondition;
import com.estone.collectCode.domain.ShopeeCollectCodeDo;
import com.estone.collectCode.domain.ShopeeCollectCodeItemDo;
import com.estone.collectCode.service.ShopeeCollectCodeItemService;
import com.estone.collectCode.service.ShopeeCollectCodeService;
import com.whq.tool.action.BaseController;
import com.whq.tool.component.Pager;
import com.whq.tool.component.StatusCode;
import com.whq.tool.json.ResponseJson;
import lombok.extern.log4j.Log4j;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@Controller
@RequestMapping(value = "collectCode/shopeeCollectCode")
public class ShopeeCollectCodeController extends BaseController {

    @Resource
    private ShopeeCollectCodeService shopeeCollectCodeService;

    @Resource
    private ShopeeCollectCodeItemService shopeeCollectCodeItemService;

    @RequestMapping(method = {RequestMethod.GET})
    public String init(@ModelAttribute("domain") ShopeeCollectCodeDo domain) {
        return "collectCode/shopeeCollectCodeList";
    }

    private void initFormData(@ModelAttribute("domain") ShopeeCollectCodeDo domain) {
         
    }

    private void queryShopeeCollectCodes(@ModelAttribute("domain") ShopeeCollectCodeDo domain) {
        ShopeeCollectCodeQueryCondition query = domain.getQuery();
        Pager page = domain.getPage();
        if (query == null)
        {
            query = new ShopeeCollectCodeQueryCondition();
            domain.setQuery(query);
        }
        query.setReadOnly(true);
        List<ShopeeCollectCode> shopeeCollectCodes = shopeeCollectCodeService.queryShopeeCollectCodes(query, page);
        domain.setShopeeCollectCodes(shopeeCollectCodes);
    }

    @RequestMapping(value="search", method = {RequestMethod.GET})
    public String getSearch(@ModelAttribute("domain") ShopeeCollectCodeDo domain) {
        return "collectCode/shopeeCollectCodeList";
    }

    @RequestMapping(value="search", method = {RequestMethod.POST})
    public String search(@ModelAttribute("domain") ShopeeCollectCodeDo domain) {
        initFormData(domain);
        queryShopeeCollectCodes(domain);
        return "collectCode/shopeeCollectCodeList";
    }


    @RequestMapping(value = "details", method = {RequestMethod.GET})
    public String getDetail(@ModelAttribute("domain") ShopeeCollectCodeItemDo domain, @RequestParam("id") Integer id) {

        ShopeeCollectCode shopeeCollectCode = shopeeCollectCodeService.getShopeeCollectCode(id);
        domain.setShopeeCollectCodeId(id);
        if (shopeeCollectCode != null) {
            domain.setCollectCode(shopeeCollectCode.getCollectCode());
        }
        return "collectCode/shopeeCollectCodeItemList";
    }

    @RequestMapping(value = "details", method = {RequestMethod.POST})
    public String detail(@ModelAttribute("domain") ShopeeCollectCodeItemDo domain, @RequestParam("id") Integer id) {

        ShopeeCollectCode shopeeCollectCode = shopeeCollectCodeService.getShopeeCollectCode(id);
        domain.setShopeeCollectCodeId(id);
        if (shopeeCollectCode != null) {
            domain.setCollectCode(shopeeCollectCode.getCollectCode());
        }

        ShopeeCollectCodeItemQueryCondition query = domain.getQuery();
        Pager page = domain.getPage();
        if (query == null)
        {
            query = new ShopeeCollectCodeItemQueryCondition();
            domain.setQuery(query);
        }
        List<ShopeeCollectCodeItem> shopeeCollectCodeItems = shopeeCollectCodeItemService.queryShopeeCollectCodeItems(query, page);
        domain.setShopeeCollectCodeItems(shopeeCollectCodeItems);
        return "collectCode/shopeeCollectCodeItemList";
    }


    @ResponseBody
    @RequestMapping(value = "synFromOms", method = {RequestMethod.GET})
    public ResponseJson synFromOms(String dateMonth) {

        ResponseJson response = new ResponseJson();
        response.setStatus(StatusCode.FAIL);

        try {
            shopeeCollectCodeService.synFromOms(dateMonth);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            log.error(e.getMessage());
            response.setMessage(e.getMessage());
            return response;
        }

        response.setStatus(StatusCode.SUCCESS);
        return response;
    }

}