package com.estone.collectCode.action;

import com.estone.collectCode.bean.ShopeeCollectCodeItem;
import com.estone.collectCode.bean.ShopeeCollectCodeItemQueryCondition;
import com.estone.collectCode.domain.ShopeeCollectCodeItemDo;
import com.estone.collectCode.service.ShopeeCollectCodeItemService;
import com.whq.tool.action.BaseController;
import com.whq.tool.component.Pager;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.annotation.Resource;
import java.util.List;

@Controller
@RequestMapping(value = "collectCode/shopeeCollectCodeItem")
public class ShopeeCollectCodeItemController extends BaseController {
    @Resource
    private ShopeeCollectCodeItemService shopeeCollectCodeItemService;

    @RequestMapping(method = {RequestMethod.GET})
    public String init(@ModelAttribute("domain") ShopeeCollectCodeItemDo domain) {
        return "collectCode/shopeeCollectCodeItem";
    }

    private void initFormData(@ModelAttribute("domain") ShopeeCollectCodeItemDo domain) {
         
    }

    private void queryShopeeCollectCodeItems(@ModelAttribute("domain") ShopeeCollectCodeItemDo domain) {
        ShopeeCollectCodeItemQueryCondition query = domain.getQuery();
        Pager page = domain.getPage();
        if (query == null)
        {
            query = new ShopeeCollectCodeItemQueryCondition();
            domain.setQuery(query);
        }
        List<ShopeeCollectCodeItem> shopeeCollectCodeItems = shopeeCollectCodeItemService.queryShopeeCollectCodeItems(query, page);
        domain.setShopeeCollectCodeItems(shopeeCollectCodeItems);
    }

    @RequestMapping(value="search", method = {RequestMethod.POST})
    public String search(@ModelAttribute("domain") ShopeeCollectCodeItemDo domain) {
        initFormData(domain);
        queryShopeeCollectCodeItems(domain);
        return "collectCode/shopeeCollectCodeItem";
    }
}