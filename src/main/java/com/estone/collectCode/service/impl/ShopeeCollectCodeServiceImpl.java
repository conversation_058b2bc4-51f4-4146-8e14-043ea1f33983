package com.estone.collectCode.service.impl;

import java.sql.Timestamp;
import java.util.*;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import com.estone.common.util.*;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.aop.framework.AopContext;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.StringUtils;

import com.estone.collectCode.bean.*;
import com.estone.collectCode.dao.ShopeeCollectCodeDao;
import com.estone.collectCode.enums.BindingStatus;
import com.estone.collectCode.service.ShopeeCollectCodeItemService;
import com.estone.collectCode.service.ShopeeCollectCodeService;
import com.estone.common.util.model.ApiResult;
import com.estone.scan.deliver.bean.WhPackCarRecord;
import com.estone.scan.deliver.service.WhPackCarRecordService;
import com.estone.scan.deliver.service.WhScanShipmentService;
import com.estone.system.param.bean.SystemParam;
import com.whq.tool.component.Pager;
import com.whq.tool.exception.BusinessException;
import com.whq.tool.exception.DBErrorCode;
import com.whq.tool.sqler.SqlerException;

import lombok.extern.slf4j.Slf4j;

@Service("shopeeCollectCodeService")
@Slf4j
public class ShopeeCollectCodeServiceImpl implements ShopeeCollectCodeService {
    @Resource
    private ShopeeCollectCodeDao shopeeCollectCodeDao;

    @Resource
    private WhScanShipmentService whScanShipmentService;

    @Resource
    private WhPackCarRecordService whPackCarRecordService;

    @Resource
    private ShopeeCollectCodeItemService shopeeCollectCodeItemService;


    @Override
    public ShopeeCollectCode getShopeeCollectCode(Integer id) {
        ShopeeCollectCode shopeeCollectCode = shopeeCollectCodeDao.queryShopeeCollectCode(id);
        return shopeeCollectCode;
    }

    @Override
    public ShopeeCollectCode getShopeeCollectCodeDetail(Integer id) {
        ShopeeCollectCode shopeeCollectCode = shopeeCollectCodeDao.queryShopeeCollectCode(id);
        // 关联查询
        return shopeeCollectCode;
    }

    @Override
    public ShopeeCollectCode queryShopeeCollectCode(ShopeeCollectCodeQueryCondition query) {
        Assert.notNull(query, "query is null!");
        ShopeeCollectCode shopeeCollectCode = shopeeCollectCodeDao.queryShopeeCollectCode(query);
        return shopeeCollectCode;
    }

    @Override
    public List<ShopeeCollectCode> queryAllShopeeCollectCodes() {
        return shopeeCollectCodeDao.queryShopeeCollectCodeList();
    }

    @Override
    public List<ShopeeCollectCode> queryShopeeCollectCodes(ShopeeCollectCodeQueryCondition query, Pager pager) {
        Assert.notNull(query, "query is null!");
        if (pager != null && pager.isQueryCount()) {
            int count = shopeeCollectCodeDao.queryShopeeCollectCodeCount(query);
            pager.setTotalCount(count);
            if ( count == 0) {
                return new ArrayList<ShopeeCollectCode>();
            }
        }
        List<ShopeeCollectCode> shopeeCollectCodes = shopeeCollectCodeDao.queryShopeeCollectCodeList(query, pager);
        return shopeeCollectCodes;
    }

    @Override
    public void createShopeeCollectCode(ShopeeCollectCode shopeeCollectCode) {
        try {
            shopeeCollectCodeDao.createShopeeCollectCode(shopeeCollectCode);
        }
        catch (SqlerException e) {
            log.error(e.getMessage(), e);
            throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
        }
    }

    @Override
    public void batchCreateShopeeCollectCode(List<ShopeeCollectCode> entityList) {
        if (CollectionUtils.isNotEmpty(entityList)) {
            try {
                shopeeCollectCodeDao.batchCreateShopeeCollectCode(entityList);
            }
            catch (SqlerException e) {
                log.error(e.getMessage(), e);
                throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
            }
        }
    }

    @Override
    public void deleteShopeeCollectCode(Integer id) {
        try {
            shopeeCollectCodeDao.deleteShopeeCollectCode(id);
        }
        catch (SqlerException e) {
            log.error(e.getMessage(), e);
            throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
        }
    }

    @Override
    public void updateShopeeCollectCode(ShopeeCollectCode shopeeCollectCode) {
        try {
            shopeeCollectCodeDao.updateShopeeCollectCode(shopeeCollectCode);
        }
        catch (SqlerException e) {
            log.error(e.getMessage(), e);
            throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
        }
    }

    @Override
    public void batchUpdateShopeeCollectCode(List<ShopeeCollectCode> entityList) {
        if (CollectionUtils.isNotEmpty(entityList)) {
            try {
                shopeeCollectCodeDao.batchUpdateShopeeCollectCode(entityList);
            }
            catch (SqlerException e) {
                log.error(e.getMessage(), e);
                throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
            }
        }
    }

    @Override
    public void doCreateShopeeCollectCode(Integer packCarId, List<String> apvList) {

        WhPackCarRecord whPackCarRecordDetail = whPackCarRecordService.getWhPackCarRecordDetail(packCarId);

        ShopeeCollectCode shopeeCollectCode = new ShopeeCollectCode();
        shopeeCollectCode.setPackCarRecordId(packCarId);
        shopeeCollectCode.setShippingCompanyCode(whPackCarRecordDetail.getShippingCompanyCode());
        shopeeCollectCode.setShippingCompanyName(whPackCarRecordDetail.getShippingCompanyName());
        shopeeCollectCode.setDeliverDate(DateUtils.formatDate(whPackCarRecordDetail.getLoadDate(), "yyyy-MM-dd"));

        shopeeCollectCodeDao.createShopeeCollectCode(shopeeCollectCode);

        Integer id = shopeeCollectCode.getId();

        List<ShopeeCollectCodeItem> itemList = new ArrayList<>();
        List<String> list = whScanShipmentService.queryYstnListByPackCarId(packCarId);
        if (CollectionUtils.isNotEmpty(list)) {
            list.forEach(apvNo ->{
                ShopeeCollectCodeItem item = new ShopeeCollectCodeItem();
                item.setShopeeCollectCodeId(id);
                item.setDeliverNo(apvNo);
                item.setBindingResult(BindingStatus.UN_SYN.intCode());
                itemList.add(item);
            });
            shopeeCollectCodeItemService.batchCreateShopeeCollectCodeItem(itemList);

            //成功之后修改主表总件数、未同步单量
            shopeeCollectCode.setTotal(list.size());
            shopeeCollectCode.setUnsynNumber(list.size());
            shopeeCollectCodeDao.updateShopeeCollectCode(shopeeCollectCode);
        }
    }

    @Override
    public ShopeeCollectCode queryShopeeCollectCodeByPackCarId(Integer packCarId) {
        ShopeeCollectCodeQueryCondition queryCondition = new ShopeeCollectCodeQueryCondition();
        queryCondition.setPackCarRecordId(packCarId);

        return shopeeCollectCodeDao.queryShopeeCollectCode(queryCondition);
    }

    @Override
    public void synFromOms(String date) {

        /*
            1、查出所有未同步的发货单（不包含失败的）
            2、返回的数据 根据揽收码（批次号）分组
            3、一组一组处理，设置订单号、绑定结果、同步时间，  主记录的相关数量也更新
         */
        List<ShopeeCollectCodeItem> unSynItemList = null;
        if(StringUtils.isEmpty(date)) {
            // 直接查询所有未同步数据
            ShopeeCollectCodeItemQueryCondition queryCondition = new ShopeeCollectCodeItemQueryCondition();
            queryCondition.setBindingStatus(BindingStatus.UN_SYN.intCode());
            unSynItemList = shopeeCollectCodeItemService.queryShopeeCollectCodeItems(queryCondition, null);
        }else{
            // 手动传输日期查询
            ShopeeCollectCodeQueryCondition condition = new ShopeeCollectCodeQueryCondition();
            condition.setDeliverDateMonth(date);
            List<ShopeeCollectCode> shopeeCollectCodes = this.queryShopeeCollectCodes(condition, null);
            if(CollectionUtils.isNotEmpty(shopeeCollectCodes)){
                List<Integer> codeIds = shopeeCollectCodes.stream().map(ShopeeCollectCode::getId).collect(Collectors.toList());
                ShopeeCollectCodeItemQueryCondition queryCondition = new ShopeeCollectCodeItemQueryCondition();
                queryCondition.setBindingStatus(BindingStatus.UN_SYN.intCode());
                queryCondition.setShopeeCollectIds(codeIds);
                unSynItemList = shopeeCollectCodeItemService.queryShopeeCollectCodeItems(queryCondition, null);
            }
        }
        if (CollectionUtils.isEmpty(unSynItemList)) {
            log.warn("synShopeeCollectCodeFromOms 查询未同步数据为空。。。");
            return;
        }

        //调用oms接口  参数：unSynItemList

        SystemParam param = CacheUtils.SystemParamGet("OMS_PARAM.GET_FM_BIND_RESULT");

        if (param == null) {
            log.warn("synShopeeCollectCodeFromOms 未配置请求地址。。。");
            return;
        }

        String url = param.getParamValue();

        List<String> deliverNoList = unSynItemList.stream().map(ShopeeCollectCodeItem::getDeliverNo).collect(Collectors.toList());

        ApiResult<List<Object>> result = HttpExtendUtils.post(url, HttpUtils.ACCESS_TOKEN, deliverNoList, ApiResult.class, 120000,120000);

        //ApiResult<List<Object>> result = HttpUtils.post(url, HttpUtils.ACCESS_TOKEN, deliverNoList, ApiResult.class);

        if (null == result || !result.isSuccess()) {
            log.error("synShopeeCollectCodeFromOms 请求OMS失败：" + result.getErrorMsg());
            return;
        }

        List<ShopeeTrackingRecord> omsList = BeanConvertUtils.convertList(result.getResult(), ShopeeTrackingRecord.class);

        if (CollectionUtils.isEmpty(omsList)) {
            log.error("synShopeeCollectCodeFromOms 返回数据为空：" + omsList);
            return;
        }

        //这里不能直接使用this调用，使用this不是代理对象，spring的事务需要代理对象调用才会生效
        ShopeeCollectCodeService shopeeCollectCodeService = (ShopeeCollectCodeService) AopContext.currentProxy();
        shopeeCollectCodeService.doUpdateItemStatus(omsList);
    }

    @Override
    public void doUpdateItemStatus(List<ShopeeTrackingRecord> omsList) {

        //记录这次有变更的数量，最后主记录 +成功的  +失败的 -未同步的
        Map<Integer, Integer> unSynCountMap = new HashMap<>();
        Map<Integer, Integer> successCountMap = new HashMap<>();
        Map<Integer, Integer> failCountMap = new HashMap<>();
        //记录oms返回的揽收码
        Map<Integer, String> collectCodeMap = new HashMap<>();

        if (CollectionUtils.isEmpty(omsList)) {
            return;
        }

        //先根据物流单号查询item列表
        List<String> ystList = omsList.stream().map(ShopeeTrackingRecord::getYst).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(ystList)) {
            //为空时直接退出，避免select查询所有
            return;
        }
        //oms数据转map yst为key
        Map<String, ShopeeTrackingRecord> map = omsList.stream().collect(Collectors.toMap(ShopeeTrackingRecord::getYst, item -> item));

        ShopeeCollectCodeItemQueryCondition itemQueryCondition = new ShopeeCollectCodeItemQueryCondition();
        itemQueryCondition.setDeliverNoList(ystList);
        //这里就是最后要编辑的item数据
        List<ShopeeCollectCodeItem> shopeeCollectCodeItems = shopeeCollectCodeItemService.queryShopeeCollectCodeItems(itemQueryCondition, null);

        shopeeCollectCodeItems.forEach(item -> {

            //获取主记录id
            Integer shopeeCollectCodeId = item.getShopeeCollectCodeId();

            ShopeeTrackingRecord shopeeTrackingRecord = map.get(item.getDeliverNo());
            if (shopeeTrackingRecord == null) {
                return;
            }

            item.setBindingResult(shopeeTrackingRecord.getBandResult() ? BindingStatus.SUCCESS.intCode() : BindingStatus.FAIL.intCode());
            item.setOrderNo(shopeeTrackingRecord.getShopeeOrderId());
            item.setSynTime(new Timestamp(System.currentTimeMillis()));

            //成功时加上成功的数量，失败时加上失败的数量
            if (shopeeTrackingRecord.getBandResult()) {
                Integer num = Optional.ofNullable(successCountMap.get(shopeeCollectCodeId)).orElse(0);
                successCountMap.put(shopeeCollectCodeId, ++num);
            } else {
                Integer num = Optional.ofNullable(failCountMap.get(shopeeCollectCodeId)).orElse(0);
                failCountMap.put(shopeeCollectCodeId, ++num);
            }

            //无论成功还是失败，每次都减去未同步数量
            Integer num = Optional.ofNullable(unSynCountMap.get(shopeeCollectCodeId)).orElse(0);
            unSynCountMap.put(shopeeCollectCodeId, ++num);

            //记录揽收码（批次号）
            collectCodeMap.put(shopeeCollectCodeId, shopeeTrackingRecord.getFmCode());
        });

        shopeeCollectCodeItemService.batchUpdateShopeeCollectCodeItem(shopeeCollectCodeItems);

        //更新主记录相关数量
        ShopeeCollectCodeQueryCondition queryCondition = new ShopeeCollectCodeQueryCondition();
        queryCondition.setIdList(unSynCountMap.keySet());

        List<ShopeeCollectCode> shopeeCollectCodeList = shopeeCollectCodeDao.queryShopeeCollectCodeList(queryCondition, null);
        if (CollectionUtils.isEmpty(shopeeCollectCodeList)) {
            return;
        }

        shopeeCollectCodeList.forEach(item -> {

            Integer id = item.getId();

            //如果是第一次同步，则记录揽收码
            if (StringUtils.isEmpty(item.getCollectCode())) {
                item.setCollectCode(collectCodeMap.get(id));
            }

            //-去未同步的数量
            item.setUnsynNumber(item.getUnsynNumber() - unSynCountMap.get(id));
            //+成功的数量和失败的数量
            item.setSuccessNumber(item.getSuccessNumber() + successCountMap.getOrDefault(id, 0));
            item.setFailNumber(item.getFailNumber() + failCountMap.getOrDefault(id, 0));
        });

        shopeeCollectCodeDao.batchUpdateShopeeCollectCode(shopeeCollectCodeList);
    }

    @Override
    public List<ShopeeCollectCodeStatistics> queryShopeeCollectCodeStatisticsList(ShopeeCollectCodeStatisticsQueryCondition query, Pager pager) {
        Assert.notNull(query, "query is null!");
        if (pager != null && pager.isQueryCount()) {
            int count = shopeeCollectCodeDao.queryShopeeCollectCodeStatisticsCount(query);
            pager.setTotalCount(count);
            if (count == 0) {
                return new ArrayList<>();
            }
        }
        return shopeeCollectCodeDao.queryShopeeCollectCodeStatisticsList(query, pager);
    }
}