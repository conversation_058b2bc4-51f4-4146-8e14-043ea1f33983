package com.estone.collectCode.bean;

import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2022-01-21 14:32
 */
@Data
public class ShopeeTrackingRecord {


    /**
     * 发货单号
     */
    private String yst;

    /**
     * 订单号
     */
    private String shopeeOrderId;

    /**
     * 批次号
     */
    private String fmCode;

    /**
     * 是否绑定成功
     */
    private Boolean bandResult;

    /**
     * 绑定失败原因
     */
    private String failReason;
}