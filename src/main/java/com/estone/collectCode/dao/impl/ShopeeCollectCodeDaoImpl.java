package com.estone.collectCode.dao.impl;

import com.estone.collectCode.bean.ShopeeCollectCode;
import com.estone.collectCode.bean.ShopeeCollectCodeQueryCondition;
import com.estone.collectCode.bean.ShopeeCollectCodeStatistics;
import com.estone.collectCode.bean.ShopeeCollectCodeStatisticsQueryCondition;
import com.estone.collectCode.dao.ShopeeCollectCodeDao;
import com.estone.collectCode.dao.mapper.ShopeeCollectCodeDBField;
import com.estone.collectCode.dao.mapper.ShopeeCollectCodeMapper;
import com.estone.collectCode.dao.mapper.ShopeeCollectCodeStatisticsMapper;
import com.whq.tool.component.Pager;
import com.whq.tool.context.DataContextHolder;
import com.whq.tool.sqler.SqlerRequest;
import com.whq.tool.sqler.analyzer.DataType;
import com.estone.common.util.SqlerTemplate;

import java.sql.Timestamp;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

@Repository("shopeeCollectCodeDao")
public class ShopeeCollectCodeDaoImpl implements ShopeeCollectCodeDao {

    private void setQueryCondition(SqlerRequest request, ShopeeCollectCodeQueryCondition query) {
        if (query == null) {
            return;
        }
        if (Objects.nonNull(query.getReadOnly()) && query.getReadOnly()) {
            request.setReadOnly(true);
        }
        // 添加查询条件
        // 搜索条件不允许出现空字符
        request.setAllowBlank(false);

        request.addDataParam(ShopeeCollectCodeDBField.ID, DataType.INT, query.getId());

        //装车id
        request.addDataParam(ShopeeCollectCodeDBField.PACK_CAR_RECORD_ID, DataType.INT, query.getPackCarRecordId());

        //发货单号
        if (StringUtils.isNotBlank(query.getDeliverNo())) {
            request.addDataParam("deliverNo", DataType.STRING, query.getDeliverNo() + "%");
        }

        if (query.getIsBindingFail() != null) {
            //是否有绑定失败
            if (query.getIsBindingFail()) {
                request.addSqlDataParam("isBindingFail", " AND fail_number > 0");
            } else {
                request.addSqlDataParam("isBindingFail", " AND fail_number <= 0");
            }
        }

        //idList
        if (!CollectionUtils.isEmpty(query.getIdList())) {
            request.addDataParam("idList", DataType.STRING, query.getIdList());
        }
        if (StringUtils.isNotBlank(query.getDeliverDateMonth())) {
            request.addDataParam("deliver_date_month", DataType.STRING, query.getDeliverDateMonth() + "%");
        }
    }

    @Override
    public int queryShopeeCollectCodeCount(ShopeeCollectCodeQueryCondition query) {
        SqlerRequest request = new SqlerRequest("queryShopeeCollectCodeCount");
        setQueryCondition(request, query);
        return SqlerTemplate.queryForInt(request);
    }

    @Override
    public List<ShopeeCollectCode> queryShopeeCollectCodeList() {
        SqlerRequest request = new SqlerRequest("queryShopeeCollectCodeList");
        return SqlerTemplate.query(request, new ShopeeCollectCodeMapper());
    }

    @Override
    public List<ShopeeCollectCode> queryShopeeCollectCodeList(ShopeeCollectCodeQueryCondition query, Pager pager) {
        SqlerRequest request = new SqlerRequest("queryShopeeCollectCodeList");
        setQueryCondition(request, query);
        if (pager != null) {
            request.addFetch(pager.getPageNo(), pager.getPageSize());
        }
        return SqlerTemplate.query(request, new ShopeeCollectCodeMapper());
    }

    @Override
    public ShopeeCollectCode queryShopeeCollectCode(Integer primaryKey) {
        Assert.notNull(primaryKey, "primaryKey is null!");
        SqlerRequest request = new SqlerRequest("queryShopeeCollectCodeByPrimaryKey");
        request.addDataParam(ShopeeCollectCodeDBField.ID, DataType.INT, primaryKey);
        return SqlerTemplate.queryForObject(request, new ShopeeCollectCodeMapper());
    }

    @Override
    public ShopeeCollectCode queryShopeeCollectCode(ShopeeCollectCodeQueryCondition query) {
        SqlerRequest request = new SqlerRequest("queryShopeeCollectCode");
        setQueryCondition(request, query);
        return SqlerTemplate.queryForObject(request, new ShopeeCollectCodeMapper());
    }

    @Override
    public void createShopeeCollectCode(ShopeeCollectCode entity) {
        SqlerRequest request = new SqlerRequest("createShopeeCollectCode");
        request.addDataParam(ShopeeCollectCodeDBField.PACK_CAR_RECORD_ID, DataType.INT, entity.getPackCarRecordId());
        request.addDataParam(ShopeeCollectCodeDBField.SHIPPING_COMPANY_CODE, DataType.STRING, entity.getShippingCompanyCode());
        request.addDataParam(ShopeeCollectCodeDBField.SHIPPING_COMPANY_NAME, DataType.STRING, entity.getShippingCompanyName());
        request.addDataParam(ShopeeCollectCodeDBField.CREATED_BY, DataType.INT, entity.getCreatedBy() == null ? DataContextHolder.getUserId() : entity.getCreatedBy());
        request.addDataParam(ShopeeCollectCodeDBField.CREATION_DATE, DataType.TIMESTAMP, new Timestamp(System.currentTimeMillis()));
        request.addDataParam(ShopeeCollectCodeDBField.UPDATED_BY, DataType.INT, entity.getUpdatedBy());
        request.addDataParam(ShopeeCollectCodeDBField.UPDATE_DATE, DataType.TIMESTAMP, entity.getUpdateDate());
        request.addDataParam(ShopeeCollectCodeDBField.DELIVER_DATE, DataType.STRING, entity.getDeliverDate());

        request.addDataParam(ShopeeCollectCodeDBField.TOTAL, DataType.INT, Optional.ofNullable(entity.getTotal()).orElse(0));
        request.addDataParam(ShopeeCollectCodeDBField.SUCCESS_NUMBER, DataType.INT, Optional.ofNullable(entity.getSuccessNumber()).orElse(0));
        request.addDataParam(ShopeeCollectCodeDBField.FAIL_NUMBER, DataType.INT, Optional.ofNullable(entity.getFailNumber()).orElse(0));
        request.addDataParam(ShopeeCollectCodeDBField.UNSYN_NUMBER, DataType.INT, Optional.ofNullable(entity.getUnsynNumber()).orElse(0));
        request.addDataParam(ShopeeCollectCodeDBField.COLLECT_CODE, DataType.STRING, entity.getCollectCode());
        Integer autoIncrementId = SqlerTemplate.executeAndReturn(request);
        entity.setId(autoIncrementId);
    }

    @Override
    public void updateShopeeCollectCode(ShopeeCollectCode entity) {
        SqlerRequest request = new SqlerRequest("updateShopeeCollectCodeByPrimaryKey");
        request.addDataParam(ShopeeCollectCodeDBField.ID, DataType.INT, entity.getId());

        request.addDataParam(ShopeeCollectCodeDBField.PACK_CAR_RECORD_ID, DataType.INT, entity.getPackCarRecordId());
        request.addDataParam(ShopeeCollectCodeDBField.SHIPPING_COMPANY_CODE, DataType.STRING, entity.getShippingCompanyCode());
        request.addDataParam(ShopeeCollectCodeDBField.SHIPPING_COMPANY_NAME, DataType.STRING, entity.getShippingCompanyName());


        request.addDataParam(ShopeeCollectCodeDBField.UPDATED_BY, DataType.INT, entity.getUpdatedBy());
        request.addDataParam(ShopeeCollectCodeDBField.UPDATE_DATE, DataType.TIMESTAMP, entity.getUpdateDate());
        request.addDataParam(ShopeeCollectCodeDBField.DELIVER_DATE, DataType.STRING, entity.getDeliverDate());
        request.addDataParam(ShopeeCollectCodeDBField.TOTAL, DataType.INT, entity.getTotal());
        request.addDataParam(ShopeeCollectCodeDBField.SUCCESS_NUMBER, DataType.INT, entity.getSuccessNumber());
        request.addDataParam(ShopeeCollectCodeDBField.FAIL_NUMBER, DataType.INT, entity.getFailNumber());
        request.addDataParam(ShopeeCollectCodeDBField.UNSYN_NUMBER, DataType.INT, entity.getUnsynNumber());
        request.addDataParam(ShopeeCollectCodeDBField.COLLECT_CODE, DataType.STRING, entity.getCollectCode());
        SqlerTemplate.execute(request);
    }

    @Override
    public void batchCreateShopeeCollectCode(List<ShopeeCollectCode> entityList) {
        if (entityList != null && !entityList.isEmpty()) {
            SqlerRequest request = new SqlerRequest("createShopeeCollectCode");
            for (ShopeeCollectCode entity : entityList) {
                request.addBatchDataParam(ShopeeCollectCodeDBField.PACK_CAR_RECORD_ID, DataType.INT, entity.getPackCarRecordId());
                request.addBatchDataParam(ShopeeCollectCodeDBField.SHIPPING_COMPANY_CODE, DataType.STRING, entity.getShippingCompanyCode());
                request.addBatchDataParam(ShopeeCollectCodeDBField.SHIPPING_COMPANY_NAME, DataType.STRING, entity.getShippingCompanyName());
                request.addBatchDataParam(ShopeeCollectCodeDBField.CREATED_BY, DataType.STRING, entity.getCreatedBy() == null ? DataContextHolder.getUserId() : entity.getCreatedBy());
                request.addBatchDataParam(ShopeeCollectCodeDBField.CREATION_DATE, DataType.TIMESTAMP, new Timestamp(System.currentTimeMillis()));
                request.addBatchDataParam(ShopeeCollectCodeDBField.UPDATED_BY, DataType.INT, entity.getUpdatedBy());
                request.addBatchDataParam(ShopeeCollectCodeDBField.UPDATE_DATE, DataType.TIMESTAMP, entity.getUpdateDate());
                request.addBatchDataParam(ShopeeCollectCodeDBField.DELIVER_DATE, DataType.STRING, entity.getDeliverDate());
                request.addBatchDataParam(ShopeeCollectCodeDBField.TOTAL, DataType.INT, entity.getTotal());
                request.addBatchDataParam(ShopeeCollectCodeDBField.SUCCESS_NUMBER, DataType.INT, entity.getSuccessNumber());
                request.addBatchDataParam(ShopeeCollectCodeDBField.FAIL_NUMBER, DataType.INT, entity.getFailNumber());
                request.addBatchDataParam(ShopeeCollectCodeDBField.UNSYN_NUMBER, DataType.INT, entity.getUnsynNumber());
                request.addBatchDataParam(ShopeeCollectCodeDBField.COLLECT_CODE, DataType.STRING, entity.getCollectCode());
                request.addBatch();
            }
            SqlerTemplate.batchUpdate(request);
        }
    }

    @Override
    public void batchUpdateShopeeCollectCode(List<ShopeeCollectCode> entityList) {
        if (entityList != null && !entityList.isEmpty()) {
            SqlerRequest request = new SqlerRequest("updateShopeeCollectCodeByPrimaryKey");
            for (ShopeeCollectCode entity : entityList) {
                request.addBatchDataParam(ShopeeCollectCodeDBField.ID, DataType.INT, entity.getId());

                request.addBatchDataParam(ShopeeCollectCodeDBField.PACK_CAR_RECORD_ID, DataType.INT, entity.getPackCarRecordId());
                request.addBatchDataParam(ShopeeCollectCodeDBField.SHIPPING_COMPANY_CODE, DataType.STRING, entity.getShippingCompanyCode());
                request.addBatchDataParam(ShopeeCollectCodeDBField.SHIPPING_COMPANY_NAME, DataType.STRING, entity.getShippingCompanyName());


                request.addBatchDataParam(ShopeeCollectCodeDBField.UPDATED_BY, DataType.INT, entity.getUpdatedBy());
                request.addBatchDataParam(ShopeeCollectCodeDBField.UPDATE_DATE, DataType.TIMESTAMP, entity.getUpdateDate());
                request.addBatchDataParam(ShopeeCollectCodeDBField.DELIVER_DATE, DataType.STRING, entity.getDeliverDate());
                request.addBatchDataParam(ShopeeCollectCodeDBField.TOTAL, DataType.INT, entity.getTotal());
                request.addBatchDataParam(ShopeeCollectCodeDBField.SUCCESS_NUMBER, DataType.INT, entity.getSuccessNumber());
                request.addBatchDataParam(ShopeeCollectCodeDBField.FAIL_NUMBER, DataType.INT, entity.getFailNumber());
                request.addBatchDataParam(ShopeeCollectCodeDBField.UNSYN_NUMBER, DataType.INT, entity.getUnsynNumber());
                request.addBatchDataParam(ShopeeCollectCodeDBField.COLLECT_CODE, DataType.STRING, entity.getCollectCode());
                request.addBatch();
            }
            SqlerTemplate.batchUpdate(request);
        }
    }

    @Override
    public void deleteShopeeCollectCode(Integer primaryKey) {
        SqlerRequest request = new SqlerRequest("deleteShopeeCollectCodeByPrimaryKey");
        request.addDataParam(ShopeeCollectCodeDBField.ID, DataType.INT, primaryKey);
        SqlerTemplate.execute(request);
    }

    @Override
    public List<ShopeeCollectCodeStatistics> queryShopeeCollectCodeStatisticsList(ShopeeCollectCodeStatisticsQueryCondition query, Pager pager) {
        SqlerRequest request = new SqlerRequest("queryShopeeCollectCodeStatisticsList");
        if (Objects.nonNull(query.getReadOnly()) && query.getReadOnly()) {
            request.setReadOnly(true);
        }
        if (StringUtils.isNotBlank(query.getDeliverDateBegin())) {
            request.addDataParam("deliverDateBegin", DataType.STRING, query.getDeliverDateBegin());
        }
        if (StringUtils.isNotBlank(query.getDeliverDateEnd())) {
            request.addDataParam("deliverDateEnd", DataType.STRING, query.getDeliverDateEnd());
        }
        if (pager != null) {
            request.addFetch(pager.getPageNo(), pager.getPageSize());
        }
        return SqlerTemplate.query(request, new ShopeeCollectCodeStatisticsMapper());
    }

    @Override
    public int queryShopeeCollectCodeStatisticsCount(ShopeeCollectCodeStatisticsQueryCondition query) {
        SqlerRequest request = new SqlerRequest("queryShopeeCollectCodeStatisticsCount");
        if (Objects.nonNull(query.getReadOnly()) && query.getReadOnly()) {
            request.setReadOnly(true);
        }
        if (StringUtils.isNotBlank(query.getDeliverDateBegin())) {
            request.addDataParam("deliverDateBegin", DataType.STRING, query.getDeliverDateBegin());
        }
        if (StringUtils.isNotBlank(query.getDeliverDateEnd())) {
            request.addDataParam("deliverDateEnd", DataType.STRING, query.getDeliverDateEnd());
        }
        return SqlerTemplate.queryForInt(request);
    }

}