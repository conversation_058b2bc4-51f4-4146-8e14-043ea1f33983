package com.estone.collectCode.dao.impl;

import com.estone.collectCode.bean.ShopeeCollectCodeItem;
import com.estone.collectCode.bean.ShopeeCollectCodeItemQueryCondition;
import com.estone.collectCode.dao.ShopeeCollectCodeItemDao;
import com.estone.collectCode.dao.mapper.ShopeeCollectCodeItemDBField;
import com.estone.collectCode.dao.mapper.ShopeeCollectCodeItemMapper;
import com.whq.tool.component.Pager;
import com.whq.tool.context.DataContextHolder;
import com.whq.tool.sqler.SqlerRequest;
import com.whq.tool.sqler.analyzer.DataType;
import com.estone.common.util.SqlerTemplate;
import java.sql.Timestamp;
import java.util.List;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;
import org.springframework.util.Assert;

@Repository("shopeeCollectCodeItemDao")
public class ShopeeCollectCodeItemDaoImpl implements ShopeeCollectCodeItemDao {

    private void setQueryCondition(SqlerRequest request, ShopeeCollectCodeItemQueryCondition query) {
        if (query == null) {
            return;
        }
        // 添加查询条件
        // 搜索条件不允许出现空字符
        request.setAllowBlank(false);
        
        request.addDataParam(ShopeeCollectCodeItemDBField.ID, DataType.INT, query.getId());
        request.addDataParam(ShopeeCollectCodeItemDBField.SHOPEE_COLLECT_CODE_ID, DataType.INT, query.getShopeeCollectCodeId());
        request.addDataParam(ShopeeCollectCodeItemDBField.BINDING_RESULT, DataType.INT, query.getBindingStatus());

        if (StringUtils.isNotBlank(query.getDeliverNo())) {
            request.addDataParam(ShopeeCollectCodeItemDBField.DELIVER_NO, DataType.STRING, "%" + query.getDeliverNo() + "%");
        }

        if (CollectionUtils.isNotEmpty(query.getDeliverNoList())) {
            request.addDataParam("deliverNoList", DataType.STRING, query.getDeliverNoList());
        }
        if(CollectionUtils.isNotEmpty(query.getShopeeCollectIds())){
            request.addDataParam("shopeeCollectIds", DataType.STRING, query.getShopeeCollectIds());
        }
    }

    @Override
    public int queryShopeeCollectCodeItemCount(ShopeeCollectCodeItemQueryCondition query) {
        SqlerRequest request = new SqlerRequest("queryShopeeCollectCodeItemCount");
        setQueryCondition(request, query);
        return SqlerTemplate.queryForInt(request);
    }

    @Override
    public List<ShopeeCollectCodeItem> queryShopeeCollectCodeItemList() {
        SqlerRequest request = new SqlerRequest("queryShopeeCollectCodeItemList");
        return SqlerTemplate.query(request, new ShopeeCollectCodeItemMapper());
    }

    @Override
    public List<ShopeeCollectCodeItem> queryShopeeCollectCodeItemList(ShopeeCollectCodeItemQueryCondition query, Pager pager) {
        SqlerRequest request = new SqlerRequest("queryShopeeCollectCodeItemList");
        setQueryCondition(request, query);
        if(pager != null) {
            request.addFetch(pager.getPageNo(), pager.getPageSize());
        }
        return SqlerTemplate.query(request, new ShopeeCollectCodeItemMapper());
    }

    @Override
    public ShopeeCollectCodeItem queryShopeeCollectCodeItem(Integer primaryKey) {
        Assert.notNull(primaryKey, "primaryKey is null!");
        SqlerRequest request = new SqlerRequest("queryShopeeCollectCodeItemByPrimaryKey");
        request.addDataParam(ShopeeCollectCodeItemDBField.ID, DataType.INT, primaryKey);
        return SqlerTemplate.queryForObject(request, new ShopeeCollectCodeItemMapper());
    }

    @Override
    public ShopeeCollectCodeItem queryShopeeCollectCodeItem(ShopeeCollectCodeItemQueryCondition query) {
        SqlerRequest request = new SqlerRequest("queryShopeeCollectCodeItem");
        setQueryCondition(request, query);
        return SqlerTemplate.queryForObject(request, new ShopeeCollectCodeItemMapper());
    }

    @Override
    public void createShopeeCollectCodeItem(ShopeeCollectCodeItem entity) {
        SqlerRequest request = new SqlerRequest("createShopeeCollectCodeItem");
        request.addDataParam(ShopeeCollectCodeItemDBField.SHOPEE_COLLECT_CODE_ID, DataType.INT, entity.getShopeeCollectCodeId());
        request.addDataParam(ShopeeCollectCodeItemDBField.DELIVER_NO, DataType.STRING, entity.getDeliverNo());
        request.addDataParam(ShopeeCollectCodeItemDBField.ORDER_NO, DataType.STRING, entity.getOrderNo());
        request.addDataParam(ShopeeCollectCodeItemDBField.BINDING_RESULT, DataType.INT, entity.getBindingResult());
        request.addDataParam(ShopeeCollectCodeItemDBField.SYN_TIME, DataType.TIMESTAMP, entity.getSynTime());
        Integer autoIncrementId = SqlerTemplate.executeAndReturn(request);
        entity.setId(autoIncrementId);
    }

    @Override
    public void updateShopeeCollectCodeItem(ShopeeCollectCodeItem entity) {
        SqlerRequest request = new SqlerRequest("updateShopeeCollectCodeItemByPrimaryKey");
        request.addDataParam(ShopeeCollectCodeItemDBField.ID, DataType.INT, entity.getId());
        
        request.addDataParam(ShopeeCollectCodeItemDBField.SHOPEE_COLLECT_CODE_ID, DataType.INT, entity.getShopeeCollectCodeId());
        request.addDataParam(ShopeeCollectCodeItemDBField.DELIVER_NO, DataType.STRING, entity.getDeliverNo());
        request.addDataParam(ShopeeCollectCodeItemDBField.ORDER_NO, DataType.STRING, entity.getOrderNo());
        request.addDataParam(ShopeeCollectCodeItemDBField.BINDING_RESULT, DataType.INT, entity.getBindingResult());
        request.addDataParam(ShopeeCollectCodeItemDBField.SYN_TIME, DataType.TIMESTAMP, entity.getSynTime());
        SqlerTemplate.execute(request);
    }

    @Override
    public void batchCreateShopeeCollectCodeItem(List<ShopeeCollectCodeItem> entityList) {
        if (entityList != null && !entityList.isEmpty()) {
            SqlerRequest request = new SqlerRequest("createShopeeCollectCodeItem");
            for (ShopeeCollectCodeItem entity : entityList) {
                request.addBatchDataParam(ShopeeCollectCodeItemDBField.SHOPEE_COLLECT_CODE_ID, DataType.INT, entity.getShopeeCollectCodeId());
                request.addBatchDataParam(ShopeeCollectCodeItemDBField.DELIVER_NO, DataType.STRING, entity.getDeliverNo());
                request.addBatchDataParam(ShopeeCollectCodeItemDBField.ORDER_NO, DataType.STRING, entity.getOrderNo());
                request.addBatchDataParam(ShopeeCollectCodeItemDBField.BINDING_RESULT, DataType.INT, entity.getBindingResult());
                request.addBatchDataParam(ShopeeCollectCodeItemDBField.SYN_TIME, DataType.TIMESTAMP, entity.getSynTime());
                request.addBatch();
            }
            SqlerTemplate.batchUpdate(request);
        }
    }

    @Override
    public void batchUpdateShopeeCollectCodeItem(List<ShopeeCollectCodeItem> entityList) {
        if (entityList != null && !entityList.isEmpty()) {
            SqlerRequest request = new SqlerRequest("updateShopeeCollectCodeItemByPrimaryKey");
            for (ShopeeCollectCodeItem entity : entityList) {
                request.addBatchDataParam(ShopeeCollectCodeItemDBField.ID, DataType.INT, entity.getId());
                
                request.addBatchDataParam(ShopeeCollectCodeItemDBField.SHOPEE_COLLECT_CODE_ID, DataType.INT, entity.getShopeeCollectCodeId());
                request.addBatchDataParam(ShopeeCollectCodeItemDBField.DELIVER_NO, DataType.STRING, entity.getDeliverNo());
                request.addBatchDataParam(ShopeeCollectCodeItemDBField.ORDER_NO, DataType.STRING, entity.getOrderNo());
                request.addBatchDataParam(ShopeeCollectCodeItemDBField.BINDING_RESULT, DataType.INT, entity.getBindingResult());
                request.addBatchDataParam(ShopeeCollectCodeItemDBField.SYN_TIME, DataType.TIMESTAMP, entity.getSynTime());
                request.addBatch();
            }
            SqlerTemplate.batchUpdate(request);
        }
    }

    @Override
    public void deleteShopeeCollectCodeItem(Integer primaryKey) {
        SqlerRequest request = new SqlerRequest("deleteShopeeCollectCodeItemByPrimaryKey");
        request.addDataParam(ShopeeCollectCodeItemDBField.ID, DataType.INT, primaryKey);
        SqlerTemplate.execute(request);
    }
}