package com.estone.multiplelocation.bean;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2023-02-24 15:11
 */
@Data
public class ExpRuleSetting {

    /**
     * 主键Id
     */
    private Integer id;

    /**
     * 保质期天数范围中所小于等于的值，即较大的那个数
     */
    private Integer expLe;

    /**
     * 保质期天数范围中所大于等于的值，即较小的那个数
     */
    private Integer expGe;

    /**
     * 同库位保质期到期时间间隔，此数为转化成百分比后的值
     */
    private Integer sameLocationExpSpan;

    /**
     * 类型，1：按比例，2：按天数
     */
    private Integer dayType;
}
