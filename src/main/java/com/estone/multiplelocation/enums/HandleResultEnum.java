package com.estone.multiplelocation.enums;


import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2023-02-28 18:23
 */
@AllArgsConstructor
public enum HandleResultEnum {

    NEW("新库位", 1),
    ORIGIN("原库位", 2),
    STOCK("存货库位", 3),
    ;
    @Getter
    private String name;

    @Getter
    private Integer code;


    public static HandleResultEnum getInstanceByCode(Integer code) {
        return Arrays.stream(values())
                .filter(val -> Objects.equals(val.getCode(), code))
                .findAny()
                .orElse(null);
    }
}
