package com.estone.temu.bean;

import lombok.Data;

import java.util.List;

@Data
public class TemuPrepareOrderQueryCondition extends TemuPrepareOrder {
    private static final long serialVersionUID = 1L;

    private List<Integer> statusList;

    private Boolean readOnly = false;

    private List<Integer> ids;

    private String sku;

    /** 创建时间 */
    private String fromCreationDate;
    private String toCreationDate;

    /** 推单时间 */
    private String fromPushTime;
    private String toPushTime;

    /** 播种时间 */
    private String fromGirdTime;
    private String toGirdTime;

    /** 交运时间 */
    private String fromDeliverTime;
    private String toDeliverTime;

    /** 装车时间 */
    private String fromLoadTime;
    private String toLoadTime;

    /** 合单时间 */
    private String fromMergeTime;
    private String toMergeTime;

    /** 分拣时间*/
    private String fromPickTime;
    private String toPickTime;

    /** 拣货时间*/
    private String fromPickOrderTime;
    private String toPickOrderTime;

    private Integer taskId;

    private String taskNo;

    private Boolean queryNullReceive = false;

    // 包裹号
    private String packageSn;
    // 发货单号列表
    private List<String> deliverOrderNoList;


    /**
     * 通道
     */
    private String access;

    /**
     * 区域
     */
    private String area;

    // 实际追踪号
    private String trackingNumber;

    //包裹追状态
    private Integer packageStatus;
    private List<Integer> packageStatusList;

    /**
     * 订单来源
     */
    private Integer sourceFrom;

    //明细id
    private Integer itemId;
    private List<Integer> itemIdList;

    //物流单号是否上传Temu平台查询
    private boolean logisticsUploaded;

    // 分拣筐号
    private String pickBoxNumber;

    private String apvType;

    private Integer taskType;

}