package com.estone.temu.bean;

import com.estone.apv.bean.WhApvOutStockChain;
import lombok.Data;

import java.io.Serializable;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;

@Data
public class TemuPackageInfo implements Serializable {
    private static final long serialVersionUID = 4126280151423916210L;

    /**
     *  database column temu_prepare_order.id
     */
    private Integer pId;

    public Integer getpId() {
        return pId;
    }

    // 是否热销
    private Boolean hotSale;

    /**
     * 店铺 database column temu_prepare_order.account_number
     */
    private String accountNumber;

    /**
     * 销售 database column temu_prepare_order.seller
     */
    private String seller;

    /**
     * 备货单号 database column temu_prepare_order.prepare_order_no
     */
    private String prepareOrderNo;

    /**
     * 类型 database column temu_prepare_order.type
     */
    private Integer type;

    /**
     * 到仓ID database column temu_prepare_order.receive_house_id
     */
    private String receiveHouseId;

    /**
     * 到货仓库 database column temu_prepare_order.receive_house
     */
    private String receiveHouse;

    /**
     * 状态 database column temu_prepare_order.status
     */
    private Integer status;

    // 平台状态
    private Integer platformStatus;

    /**
     * 发货单号 database column temu_prepare_order.deliver_order_no
     */
    private String deliverOrderNo;

    /**
     * 包裹号 database column temu_prepare_order.bag_no
     */
    private String bagNo;

    /**
     * 推单时间 database column temu_prepare_order.push_time
     */
    private Timestamp pushTime;

    /**
     * 合单时间 database column temu_prepare_order.merge_time
     */
    private Timestamp mergeTime;

    /**
     * 分拣时间 database column temu_prepare_order.pick_time
     */
    private Timestamp pickTime;

    /**
     * 拣货时间 database column temu_prepare_order_item.pick_order_time
     */
    private Timestamp pickOrderTime;

    /**
     * 播种时间 database column temu_prepare_order.gird_time
     */
    private Timestamp girdTime;

    /**
     * 交运时间 database column temu_prepare_order.deliver_time
     */
    private Timestamp deliverTime;

    /**
     * 交运人 database column temu_prepare_order.deliver_by
     */
    private Integer deliverBy;

    /**
     * 装车时间 database column temu_prepare_order.load_time
     */
    private Timestamp loadTime;

    /**
     * 创建时间 database column temu_prepare_order.creation_date
     */
    private Timestamp creationDate;

    /**
     * 创建人 database column temu_prepare_order.create_by
     */
    private Integer createBy;

    /**
     * 更新时间 database column temu_prepare_order.update_date
     */
    private Timestamp updateDate;

    /**
     * 更新人 database column temu_prepare_order.update_by
     */
    private Integer updateBy;

    //发货方式
    private Integer deliveryMethod;

    //物流单号
    private String shippingOrderNo;

    //快递公司名称
    private String shippingCompany;

    //联系方式
    private String driverPhone;

    //发货单取货时间
    private Timestamp receiveTime;

    //快递单号
    private String expressDelivery;

    /**
     *  database column temu_prepare_order_item.id
     */
    private Integer id;

    /**
     * sku database column temu_prepare_order_item.sku
     */
    private String sku;

    /**
     * sku名称 database column temu_prepare_order_item.sku_name
     */
    private String skuName;

    /**
     * 备货数量 database column temu_prepare_order_item.prepare_quantity
     */
    private Integer prepareQuantity;

    /**
     * 播种数量 database column temu_prepare_order_item.grid_quantity
     */
    private Integer gridQuantity;

    /**
     * 拣货数量 database column temu_prepare_order_item.pick_quantity
     */
    private Integer pickQuantity;

    private Integer allotQuantity;

    private Integer realQuantity;

    // 包裹号
    private String packageSn;

    // 包裹状态
    private Integer packageStatus;

    /**
     * 订单来源
     */
    private Integer sourceFrom;

    /**
     * 交运重量
     */
    private Double actualWeight;

    private String expressInfo;


    //虚拟快递公司
    private String virtualShippingCompany;

    // 分拣筐号
    private Integer pickBoxNumber;

    private String location;

    private String skuId;

    private String tag;

    /**
     * sku条码
     */
    private String temuCodeUrl;

    /**
     * 打包标签
     */
    private String temuTagUrl;

    private List<WhApvOutStockChain> whApvOutStockChains = new ArrayList<>();
}