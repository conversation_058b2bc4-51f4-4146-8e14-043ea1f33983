package com.estone.temu.dao.mapper;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.HashMap;
import java.util.Map;

import org.springframework.jdbc.core.RowMapper;

import com.estone.temu.bean.TemuPrepareOrder;
import com.estone.temu.bean.TemuPrepareOrderItem;

/**
 * 
 * @ClassName: WhTemuOrderAndItemForPickingMapper
 * @Description: 拣货查询
 * <AUTHOR>
 * @date 2019年5月10日
 * @version 0.0.2
 *
 */
public class WhTemuOrderAndItemForPickingMapper implements RowMapper<TemuPrepareOrder> {

    private Map<Integer, TemuPrepareOrder> exist = new HashMap<Integer, TemuPrepareOrder>();

    @Override
    public TemuPrepareOrder mapRow(ResultSet rs, int rowNum) throws SQLException {
        // 订单id
        Integer orderId = rs.getInt("t.id");

        TemuPrepareOrder order = exist.get(orderId);

        boolean isExist = false;

        if (order == null) {
            // 订单信息
            order = new TemuPrepareOrder();
            order.setId(orderId);
            order.setPrepareOrderNo(rs.getString("t.prepare_order_no"));
            order.setStatus(rs.getInt("t.status"));
            order.setPushTime(rs.getTimestamp("t.push_time"));
            order.setAccountNumber(rs.getString("t.account_number"));
            exist.put(orderId, order);
        }
        else {
            isExist = true;
        }

        TemuPrepareOrderItem item = new TemuPrepareOrderItem();
        item.setId(rs.getInt("ti.id"));
        item.setPrepareOrderId(rs.getInt("ti.prepare_order_id"));
        item.setPrepareOrderNo(rs.getString("t.prepare_order_no"));
        item.setSku(rs.getString("ti.sku"));
        item.setSkc(rs.getString("ti.skc"));
        item.setSkuId(rs.getString("ti.sku_id"));
        item.setSkuName(rs.getString("ti.sku_name"));
        item.setPrepareQuantity(rs.getObject("ti.prepare_quantity") == null ? 0 : rs.getInt("ti.prepare_quantity"));
        item.setAllotQuantity(rs.getObject("ti.allot_quantity") == null ? 0 : rs.getInt("ti.allot_quantity"));
        item.setPickQuantity(rs.getObject("ti.pick_quantity") == null ? 0 : rs.getInt("ti.pick_quantity"));
        item.setGridQuantity(rs.getObject("ti.grid_quantity") == null ? 0 : rs.getInt("ti.grid_quantity"));
        item.setGridStatus(rs.getObject("ti.grid_status") == null ? null : rs.getInt("ti.grid_status"));
        item.setPackageStatus(rs.getObject("ti.package_status") == null ? null : rs.getInt("ti.package_status"));
        item.setPackageSn(rs.getString("ti.package_sn"));
        order.addItem(item);
        return isExist ? null : order;
    }

}
