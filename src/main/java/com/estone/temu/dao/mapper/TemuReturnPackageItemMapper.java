package com.estone.temu.dao.mapper;

import com.estone.temu.bean.TemuReturnPackageItem;
import java.sql.ResultSet;
import java.sql.SQLException;
import org.springframework.jdbc.core.RowMapper;

public class TemuReturnPackageItemMapper implements <PERSON><PERSON>apper<TemuReturnPackageItem> {

    public TemuReturnPackageItem mapRow(ResultSet rs, int rowNum) throws SQLException {
        TemuReturnPackageItem entity = new TemuReturnPackageItem();
        entity.setId(rs.getObject(TemuReturnPackageItemDBField.ID) == null ? null : rs.getInt(TemuReturnPackageItemDBField.ID));
        entity.setReturnId(rs.getObject(TemuReturnPackageItemDBField.RETURN_ID) == null ? null : rs.getInt(TemuReturnPackageItemDBField.RETURN_ID));
        entity.setSku(rs.getString(TemuReturnPackageItemDBField.SKU));
        entity.setSkuName(rs.getString(TemuReturnPackageItemDBField.SKU_NAME));
        entity.setProductSkcId(rs.getString(TemuReturnPackageItemDBField.PRODUCT_SKC_ID));
        entity.setProductSkuId(rs.getString(TemuReturnPackageItemDBField.PRODUCT_SKU_ID));
        entity.setQuantity(rs.getObject(TemuReturnPackageItemDBField.QUANTITY) == null ? null : rs.getInt(TemuReturnPackageItemDBField.QUANTITY));
        entity.setWmsReturnQty(rs.getObject(TemuReturnPackageItemDBField.WMS_RETURN_QTY) == null ? null : rs.getInt(TemuReturnPackageItemDBField.WMS_RETURN_QTY));
        entity.setOutBoundTime(rs.getTimestamp(TemuReturnPackageItemDBField.OUT_BOUND_TIME));
        entity.setWmsReturnTime(rs.getTimestamp(TemuReturnPackageItemDBField.WMS_RETURN_TIME));
        entity.setWmsReturnBy(rs.getObject(TemuReturnPackageItemDBField.WMS_RETURN_BY) == null ? null : rs.getInt(TemuReturnPackageItemDBField.WMS_RETURN_BY));
        entity.setPackageNo(rs.getString(TemuReturnPackageItemDBField.PACKAGE_NO));
        entity.setPurchaseSubOrderSn(rs.getString(TemuReturnPackageItemDBField.PURCHASE_SUB_ORDER_SN));
        entity.setQcResult(rs.getString(TemuReturnPackageItemDBField.QC_RESULT));
        entity.setQcInfoJson(rs.getString(TemuReturnPackageItemDBField.QC_INFO_JSON));
        return entity;
    }
}