package com.estone.temu.request;

import com.estone.common.util.CreateTaskNoUtils;
import com.estone.common.util.RedisConstant;
import com.estone.common.util.StringRedisUtils;
import com.estone.temu.bean.TemuPrepareOrder;
import com.estone.temu.bean.TemuPrepareOrderItem;
import com.estone.temu.response.LogisticsMatchResponse;
import com.estone.temu.response.ReceiveAddressResponse;
import com.estone.temu.response.SupplierAddressResponse;
import com.estone.temu.response.ThirdPartyExpressDeliveryInfoVO;
import com.whq.tool.exception.BusinessException;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

@Data
public class ShipOrdervCreateRequest {
    // 发货单创建组列表
    private List<DeliveryOrderCreateGroup> deliveryOrderCreateGroupList;

    @Data
    static class DeliveryOrderCreateGroup{
        private List<LogisticsMatchRequest.DeliveryOrderCreateInfo> deliveryOrderCreateInfos;
        // 收货子仓 id
        private String  subWarehouseId;
        // 发货方式 1、自送 2、平台物流 3、三方物流
        private Integer  deliverMethod;
        // 平台物流信息
        private LogisticsMatchResponse thirdPartyDeliveryInfo;
        // 收货新增
        private ReceiveAddressResponse receiveAddressInfo;
        //第三方配送
        private ThirdPartyExpressDeliveryInfoVO thirdPartyExpressDeliveryInfoVO;


    }

    public static  ShipOrdervCreateRequest builderShipOrdervCreateRequest(List<TemuPrepareOrder> temuPrepareOrderList,SupplierAddressResponse supplierAddressResponse){
       // String expressDeliverySn = CreateTaskNoUtils.createExpressDeliverySn();
        ShipOrdervCreateRequest request = new ShipOrdervCreateRequest();
        DeliveryOrderCreateGroup deliveryOrderCreateGroup = new DeliveryOrderCreateGroup();
       // deliveryOrderCreateGroup.setDeliverMethod(3);
        deliveryOrderCreateGroup.setSubWarehouseId(temuPrepareOrderList.get(0).getReceiveHouseId());
        //deliveryOrderCreateGroup.setDeliveryAddressId(supplierAddressResponse.getId());
        Object receiveAddressInfoObj = StringRedisUtils.hGet(RedisConstant.TEMU_RECEIVE_ADDRESS, deliveryOrderCreateGroup.getSubWarehouseId());
        if (receiveAddressInfoObj == null)  throw new BusinessException(String.format("[%s]收货仓无收货地址详细信息!", deliveryOrderCreateGroup.getSubWarehouseId()));
        deliveryOrderCreateGroup.setReceiveAddressInfo((ReceiveAddressResponse) receiveAddressInfoObj);
        deliveryOrderCreateGroup.setDeliveryOrderCreateInfos(temuPrepareOrderList.stream().map(temuPrepareOrder -> {
           // temuPrepareOrder.setShippingOrderNo(expressDeliverySn);
           // temuPrepareOrder.setVirtualShippingCompany("极兔速递");
            List<TemuPrepareOrderItem> itemList = temuPrepareOrder.getItemList();
            LogisticsMatchRequest.DeliveryOrderCreateInfo deliveryOrderCreateInfo = new LogisticsMatchRequest.DeliveryOrderCreateInfo();
            deliveryOrderCreateInfo.setSubPurchaseOrderSn(temuPrepareOrder.getPrepareOrderNo());
            deliveryOrderCreateInfo.setDeliveryAddressId(supplierAddressResponse.getId());
            List<LogisticsMatchRequest.PackageInfo> packageInfos = new ArrayList<>(itemList.size());
            List<LogisticsMatchRequest.DeliverOrderDetailInfo> deliverOrderDetailInfos = new ArrayList<>(itemList.size());
            deliveryOrderCreateInfo.setPackageInfos(packageInfos);
            deliveryOrderCreateInfo.setDeliverOrderDetailInfos(deliverOrderDetailInfos);
            for (TemuPrepareOrderItem temuPrepareOrderDetail : itemList) {
                if (temuPrepareOrderDetail.getAllotQuantity() == null || temuPrepareOrderDetail.getAllotQuantity() == 0) continue;
                LogisticsMatchRequest.PackageInfo packageInfo = new LogisticsMatchRequest.PackageInfo();
                LogisticsMatchRequest.PackageDetailSaveInfo packageDetailSaveInfo = new LogisticsMatchRequest.PackageDetailSaveInfo();
                packageDetailSaveInfo.setProductSkuId(temuPrepareOrderDetail.getSkuId());
                packageDetailSaveInfo.setSkuNum(temuPrepareOrderDetail.getAllotQuantity());
                packageInfo.setPackageDetailSaveInfos(Arrays.asList(packageDetailSaveInfo));
                packageInfos.add(packageInfo);
            }
            if (CollectionUtils.isNotEmpty(itemList)) {
                itemList.stream()
                        .collect(Collectors.groupingBy(item -> item.getSku()))
                        .forEach((sku, temuPrepareOrderDetails) -> {
                            if (CollectionUtils.isEmpty(temuPrepareOrderDetails)) {
                                return;
                            }
                            Integer allotQuantity=temuPrepareOrderDetails.stream().mapToInt(temuPrepareOrderDetail -> Optional.ofNullable(temuPrepareOrderDetail.getAllotQuantity()).orElse(0)).sum();
                            LogisticsMatchRequest.DeliverOrderDetailInfo deliverOrderDetailInfo = new LogisticsMatchRequest.DeliverOrderDetailInfo();
                            deliverOrderDetailInfo.setProductSkuId(temuPrepareOrderDetails.get(0).getSkuId());
                            deliverOrderDetailInfo.setDeliverSkuNum(allotQuantity);
                            deliverOrderDetailInfos.add(deliverOrderDetailInfo);
                        });
            }
            return deliveryOrderCreateInfo;
        }).collect(Collectors.toList()));
        //ThirdPartyExpressDeliveryInfoVO thirdPartyExpressDeliveryInfoVO = new ThirdPartyExpressDeliveryInfoVO();
        //thirdPartyExpressDeliveryInfoVO.setExpressCompanyId("384");
        //thirdPartyExpressDeliveryInfoVO.setExpressCompanyName("极兔速递");
        //极兔速递单号规则格式15位
        //thirdPartyExpressDeliveryInfoVO.setExpressDeliverySn(expressDeliverySn);
        //thirdPartyExpressDeliveryInfoVO.setExpressPackageNum(1);
        //deliveryOrderCreateGroup.setThirdPartyExpressDeliveryInfoVO(thirdPartyExpressDeliveryInfoVO);
        request.setDeliveryOrderCreateGroupList(Arrays.asList(deliveryOrderCreateGroup));
        return request;
    }
}
