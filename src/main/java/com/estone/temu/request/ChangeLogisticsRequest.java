package com.estone.temu.request;

import com.estone.temu.response.ThirdPartyExpressDeliveryInfoVO;
import lombok.Data;

import java.util.List;

/**
 * @Description: 修改物流查询参数
 */
@Data
public class ChangeLogisticsRequest {
    /**
     *  发货批次
     */
    private String expressBatchSn;

    /**
     *  发货方式
     */
    private Integer deliverMethod;

    /**
     *  发货地址id
     */
    private String deliveryAddressId;


    /**
     * 第三方配送
     */
    private ThirdPartyExpressDeliveryInfoVO thirdPartyExpressDeliveryInfoVO;

}
