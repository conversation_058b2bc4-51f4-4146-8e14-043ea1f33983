package com.estone.temu.action;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import com.estone.android.PdaExceptionCode;
import com.estone.apv.domain.WhApvMoreProductsDo;
import com.estone.apv.util.ApvGridUtils;
import com.estone.common.util.CompatibleSkuUtils;
import com.estone.common.util.RedisKeys;
import com.estone.common.util.StringRedisUtils;
import com.estone.picking.bean.WhPickingTask;
import com.estone.picking.bean.WhPickingTaskQueryCondition;
import com.estone.picking.enums.PickTaskGridStatus;
import com.estone.picking.enums.PickingTaskType;
import com.estone.picking.service.PickTaskExpandService;
import com.estone.picking.service.WhPickingTaskService;
import com.estone.sku.enums.UniqueSkuStep;
import com.estone.sowstockout.bean.SowStockout;
import com.estone.sowstockout.bean.SowStockoutQueryCondition;
import com.estone.sowstockout.service.SowStockoutService;
import com.estone.temu.bean.TemuPrepareOrder;
import com.estone.temu.bean.TemuPrepareOrderItem;
import com.estone.temu.domain.PddGridDo;
import com.estone.temu.enums.TemuOrderStatus;
import com.estone.temu.service.PddGridService;
import com.estone.temu.utils.PddGridUtils;
import com.estone.warehouse.bean.WhBox;
import com.estone.warehouse.service.WhBoxService;
import com.whq.tool.component.StatusCode;
import com.whq.tool.json.ResponseJson;

import lombok.extern.slf4j.Slf4j;

/**
 * @Description: PDD播种
 * @Author: Yimeil
 * @Date: 2022/8/26 14:54
 * @Version: 1.0.0
 */
@Controller
@RequestMapping(value = "pdd/grid")
@Slf4j
public class PddGridController {

    @Resource
    private WhBoxService whBoxService;

    @Resource
    private WhPickingTaskService whPickingTaskService;

    @Resource
    private SowStockoutService sowStockoutService;

    @Resource
    private PddGridService pddGridService;

    @Resource
    private PickTaskExpandService pickTaskExpandService;

    private final static String GRID_SCAN_VIEW = "transfer/pdd_grid_scan_view";

    private final static String GRID_SCAN_BOX_VIEW = "transfer/pdd_grid_scan_box_view";

    private final static String GRID_SCAN = "transfer/pdd_grid_scan";

    @RequestMapping(value = "grid", method = { RequestMethod.GET })
    public String checkScan(@ModelAttribute("domain") WhApvMoreProductsDo domain) {
        return GRID_SCAN;
    }

    /**
     * 扫描序列号
     *
     * @param domain
     * @param box 周转筐
     * @return
     */
    @RequestMapping(value = "scanTaskNo", method = { RequestMethod.GET })
    public String scanBox(@ModelAttribute("domain") PddGridDo domain, @RequestParam("box") String box) {
        box = StringUtils.trim(box);
        ResponseJson rsp = whBoxService.checkPackBoxNoScan(box, null);
        if (StringUtils.equals(rsp.getStatus(), StatusCode.FAIL)) {
            domain.setErrorMsg(rsp.getMessage());
            return GRID_SCAN_BOX_VIEW;
        }
        rsp.setStatus(StatusCode.FAIL);
        String taskNo = rsp.getMessage();
        if (StringUtils.isEmpty(taskNo)) {
            domain.setErrorMsg("拣货任务号为空！");
            return GRID_SCAN_BOX_VIEW;
        }
        WhPickingTaskQueryCondition taskQuery = new WhPickingTaskQueryCondition();
        taskQuery.setQueryGridExpand(true);
        taskQuery.setTaskNo(taskNo);
        taskQuery.setGridStatusList(
                Arrays.asList(PickTaskGridStatus.UN_GRID.intCode(), PickTaskGridStatus.GRIDING.intCode()));
        List<WhPickingTask> whPickingTasks = whPickingTaskService.queryWhPickingTasks(taskQuery, null);
        if (CollectionUtils.isEmpty(whPickingTasks)) {
            domain.setErrorMsg("没有可用的发货单");
            return GRID_SCAN_BOX_VIEW;
        }
        if (!PickingTaskType.getPddIntCode().contains(whPickingTasks.get(0).getTaskType())) {
            domain.setErrorMsg("不是拼多多备货拣货任务！");
            return GRID_SCAN_BOX_VIEW;
        }

        box = whPickingTasks.get(0).getBoxNo();
        try {
            domain.setWhPickingTask(whPickingTasks.get(0));
            domain.setBoxNo(box);
            pddGridService.scanBoxToBuildGridData(domain);
            boolean canceled = domain.getOrderList().stream()
                    .allMatch(f -> TemuOrderStatus.CANCEL.intCode().equals(f.getStatus()));
            if (canceled) {
                pddGridService.doBindUuidAndUpdateTaskStatus(domain, whPickingTasks.get(0), domain.getOrderList(),
                        domain.getTaskItemList());
                domain.setWhPickingTaskSkuList(null);
                domain.setErrorMsg("发货单已取消，请将货物放到已拣返架区域！");
                return GRID_SCAN_BOX_VIEW;
            }
            if (whPickingTasks.get(0).getPickTaskExpand() != null && whPickingTasks.get(0).getPickTaskExpand()
                    .getGridStatus().equals(PickTaskGridStatus.UN_GRID.intCode()))
                // 设置播种中状态
                pickTaskExpandService.updatePickTaskExpandStatusByTaskNo(taskNo, PickTaskGridStatus.GRIDING.intCode());
        }
        catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        domain.setTaskNo(taskNo);
        domain.setBoxNo(box);
        return GRID_SCAN_BOX_VIEW;
    }

    /**
     * 扫描唯一码播种
     * 
     * @param domain
     * @param box
     * @param sku
     * @param uuid
     * @return
     */
    @RequestMapping(value = "grid/sku", method = { RequestMethod.GET })
    public String scanUuidGrid(@ModelAttribute("domain") PddGridDo domain, @RequestParam("box") String box,
            @RequestParam("sku") String sku, @RequestParam("uuid") String uuid) {
        // 不能为空
        if (StringUtils.isBlank(sku)) {
            return GRID_SCAN_VIEW;
        }
        // 兼容SKU编码和唯一码
        sku = CompatibleSkuUtils.getSku(sku);
        // 通过扫描的 周转筐/拣货任务号 校验 捡货任务任务
        ResponseJson rsp = whBoxService.checkPackBoxNoScan(box, null);
        if (StringUtils.equalsIgnoreCase(rsp.getStatus(), StatusCode.FAIL)) {
            domain.setErrorMsg(rsp.getMessage());
            return GRID_SCAN_VIEW;

        }
        // 序列号
        String serialNumber = rsp.getMessage();

        if (StringUtils.isEmpty(serialNumber)) {
            domain.setErrorMsg("任务号为空！");
            return GRID_SCAN_VIEW;
        }
        WhPickingTask whPickingTask = (WhPickingTask) rsp.getBody().get("whPickingTask");
        WhPickingTaskQueryCondition query = new WhPickingTaskQueryCondition();
        query.setId(whPickingTask.getId());
        query.setQueryGridExpand(true);
        query.setGridStatus(PickTaskGridStatus.COMPLETED.intCode());
        List<WhPickingTask> whPickingTasks = whPickingTaskService.queryWhPickingTasks(query, null);
        if (CollectionUtils.isNotEmpty(whPickingTasks)) {
            domain.setErrorMsg("任务号" + serialNumber + "播种状态为：已播种！");
            return GRID_SCAN_VIEW;
        }
        // 校验唯一码
        String errorMsg = pddGridService.checkAsnGridScanUniqueSku(uuid, serialNumber, UniqueSkuStep.GRID.intCode());
        if (StringUtils.isNotEmpty(errorMsg)) {
            domain.setErrorMsg(errorMsg);
            return GRID_SCAN_VIEW;
        }
        domain.setSku(sku);
        domain.setUuid(uuid);
        domain.setWhPickingTask(whPickingTask);
        domain.setBoxNo(box);
        try {
            rsp = pddGridService.scanSkuToGrid(domain);
            if (StringUtils.equalsIgnoreCase(rsp.getStatus(), StatusCode.FAIL)) {
                domain.setErrorMsg(rsp.getMessage());
                return GRID_SCAN_VIEW;
            }
        }
        catch (Exception e) {
            log.error(e.getMessage(), e);
            domain.setErrorMsg(e.getMessage());
            return GRID_SCAN_VIEW;
        }
        return GRID_SCAN_VIEW;
    }

    /**
     * Sku播种完成
     * 
     * @param stockoutBox
     * @param boxCayi
     * @param box
     * @param orderNo
     * @param sku
     * @return
     */
    @RequestMapping(value = "complete", method = { RequestMethod.POST })
    @ResponseBody
    public ResponseJson gridFinish(@RequestParam(name = "stockoutBox", required = false) String stockoutBox,
            @RequestParam(name = "boxCayi", required = false) String boxCayi, @RequestParam("box") String box,
            @RequestParam(value = "orderNo", required = false) String orderNo,
            @RequestParam(value = "force", required = false) Boolean force,
            @RequestParam(value = "sku", required = false) String sku) {

        ResponseJson responseJson = new ResponseJson();
        responseJson.setStatus(StatusCode.FAIL);

        ResponseJson rsp = whBoxService.checkPackBoxNoScan(box, null);
        if (StringUtils.equalsIgnoreCase(rsp.getStatus(), StatusCode.FAIL)) {
            responseJson.setMessage(rsp.getMessage());
            return responseJson;
        }
        String serialNumber = rsp.getMessage();
        if (StringUtils.isEmpty(serialNumber)) {
            responseJson.setMessage("找不到拣货任务号 ！");
            return responseJson;
        }

        WhPickingTask whPickingTask = (WhPickingTask) rsp.getBody().get("whPickingTask");

        try {
            PddGridDo domain = new PddGridDo();
            domain.setForceFinish(force);
            domain.setWhPickingTask(whPickingTask);
            domain.setPrepareOrderNo(orderNo);
            domain.setSku(sku);
            domain.setBoxNo(box);
            domain.setBoxCayi(boxCayi);
            domain.setBoxStockOut(stockoutBox);
            pddGridService.scanBoxToBuildGridData(domain);
            boolean canceled = domain.getOrderList().stream()
                    .allMatch(f -> TemuOrderStatus.CANCEL.intCode().equals(f.getStatus()));
            if (canceled) {
                pddGridService.doBindUuidAndUpdateTaskStatus(domain, whPickingTask, domain.getOrderList(),
                        domain.getTaskItemList());
                responseJson.setStatus(StatusCode.SUCCESS);
                List<String> prepareOrderNoList = domain.getOrderList().stream().map(TemuPrepareOrder::getPrepareOrderNo).collect(Collectors.toList());
                responseJson.setMessage("发货单"+prepareOrderNoList+"已取消，请将货物放到已拣返架区域！");
                StringRedisUtils.del(RedisKeys.getPddGridSku(whPickingTask.getTaskNo()));
                return responseJson;
            }
            List<Integer> idList = domain.getOrderList().stream().filter(f -> TemuOrderStatus.CANCEL.intCode().equals(f.getStatus())).map(TemuPrepareOrder::getId).collect(Collectors.toList());

            List<TemuPrepareOrder> prepareOrderList = domain.getOrderList().stream().filter(f -> TemuOrderStatus.CANCEL.intCode().equals(f.getStatus()) && f.getPrepareOrderNo().equals(orderNo)).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(prepareOrderList)) {
                responseJson.setStatus(StatusCode.FAIL);
                responseJson.setExceptionCode(PdaExceptionCode.CANCEL_ORDER);
                StringRedisUtils.del(RedisKeys.getPddGridSku(whPickingTask.getTaskNo()));
                List<String> prepareOrderNoList = prepareOrderList.stream().map(TemuPrepareOrder::getPrepareOrderNo).collect(Collectors.toList());
                responseJson.setMessage("发货单"+prepareOrderNoList+"已取消，请将货物放到已拣返架区域！");
                return responseJson;
            }

            List<TemuPrepareOrderItem> lessItemList = new ArrayList<>();
            lessItemList.addAll(domain.getLessPickItemList());
            lessItemList.addAll(domain.getLessGridItemList());
            boolean fnSkuFinish = StringUtils.isNotEmpty(sku) && StringUtils.isNotEmpty(orderNo);
            boolean fbaAllGrid = PddGridUtils.checkTaskAllGrid(domain);
            boolean exceptionFinish = PickingTaskType.TEMU_BZYC.intCode().equals(whPickingTask.getTaskType())
                    || PickingTaskType.TEMU_BZCY.intCode().equals(whPickingTask.getTaskType());
            domain.setAllGrid(fbaAllGrid);
            if (fnSkuFinish) {
                lessItemList = lessItemList.stream()
                        .filter(i -> StringUtils.equalsIgnoreCase(orderNo, i.getPrepareOrderNo())
                                && StringUtils.equalsIgnoreCase(sku, i.getSku()))
                        .collect(Collectors.toList());
                // 当前FNSKU少拣少播
                List<TemuPrepareOrderItem> lessPickItemList = Optional.ofNullable(domain.getLessPickItemList())
                        .orElse(new ArrayList<>()).stream().filter(i -> StringUtils.equalsIgnoreCase(orderNo, i.getPrepareOrderNo())
                                && StringUtils.equalsIgnoreCase(sku, i.getSku()))
                        .collect(Collectors.toList());
                List<TemuPrepareOrderItem> lessGridItemList = Optional.ofNullable(domain.getLessGridItemList())
                        .orElse(new ArrayList<>()).stream().filter(i -> StringUtils.equalsIgnoreCase(orderNo, i.getPrepareOrderNo())
                                && StringUtils.equalsIgnoreCase(sku, i.getSku()))
                        .collect(Collectors.toList());
                domain.setLessPickItemList(lessPickItemList);
                domain.setLessGridItemList(lessGridItemList);

                List<TemuPrepareOrderItem> itemList = domain.getTaskItemList().stream()
                        .filter(i -> StringUtils.equalsIgnoreCase(orderNo, i.getPrepareOrderNo())
                                && StringUtils.equalsIgnoreCase(sku, i.getSku()))
                        .collect(Collectors.toList());
                domain.setTaskItemList(itemList);
            }
            else if (fbaAllGrid && exceptionFinish) {
                List<TemuPrepareOrderItem> lessPickList = domain.getTaskItemList().stream()
                        .filter(i -> PickTaskGridStatus.PICK_EXCEPTION.intCode().equals(i.getGridStatus()))
                        .collect(Collectors.toList());
                List<TemuPrepareOrderItem> lessGridList = domain.getTaskItemList().stream()
                        .filter(i -> PickTaskGridStatus.GRID_EXCEPTION.intCode().equals(i.getGridStatus()))
                        .collect(Collectors.toList());
                domain.setLessPickItemList(lessPickList);
                domain.setLessGridItemList(lessGridList);
                lessItemList.clear();
                lessItemList.addAll(lessPickList);
                lessItemList.addAll(lessGridList);
            }

            // 当前发货单少拣少播
            List<String> pickOutSkus = Optional.ofNullable(lessItemList).orElse(new ArrayList<>()).stream()
                    .filter(i->!idList.contains(i.getPrepareOrderId()))
                    .map(TemuPrepareOrderItem::getSku).distinct().collect(Collectors.toList());

            // 拣货缺货货 or 播种差异
            if (CollectionUtils.isNotEmpty(pickOutSkus)) {
                responseJson = pddGridService.updateLessPickAndLessGridFinish(pickOutSkus, domain, lessItemList);
            }
            else {
                responseJson = pddGridService.normalGridComplete(domain);
            }
            StringRedisUtils.del(RedisKeys.getPddGridSku(whPickingTask.getTaskNo()));
            responseJson.setLocation(rsp.getLocation());
            return responseJson;
        }
        catch (Exception e) {
            log.error(e.getMessage(), e);
            responseJson.setMessage("操作失败：" + e.getMessage());
            return responseJson;
        }
    }

    /**
     * 绑定异常周转筐(二次配货)
     *
     * @param stockoutBox 播种异常
     * @param boxCayi 播种差异
     * @param box 拣货周转筐
     * @return
     */
    @RequestMapping(value = "binding/stockout", method = { RequestMethod.POST })
    @ResponseBody
    public ResponseJson bindingStockoutBox(@RequestParam(name = "stockoutBox", required = false) String stockoutBox,
            @RequestParam(name = "boxCayi", required = false) String boxCayi,
            @RequestParam(value = "orderNo", required = false) String orderNo, @RequestParam("box") String box,
            @RequestParam(value = "sku", required = false) String sku) {

        ResponseJson responseJson = new ResponseJson();
        responseJson.setStatus(StatusCode.FAIL);

        ResponseJson rsp = whBoxService.checkPackBoxNoScan(box, null);
        if (StringUtils.equalsIgnoreCase(rsp.getStatus(), StatusCode.FAIL)) {
            responseJson.setMessage(rsp.getMessage());
            return responseJson;
        }
        String serialNumber = rsp.getMessage();
        if (StringUtils.isEmpty(serialNumber)) {
            responseJson.setMessage("找不到拣货任务号 ！");
            return responseJson;
        }

        WhPickingTask whPickingTask = (WhPickingTask) rsp.getBody().get("whPickingTask");
        String errorMes = null;

        try {

            WhBox stockoutWhBox = null;

            if (StringUtils.isNotBlank(stockoutBox)) {
                stockoutWhBox = whBoxService.queryWhBoxByBoxNo(stockoutBox);
                Integer taskType = whPickingTask == null ? null : whPickingTask.getTaskType();
                // 检查提交的周转筐是否可用
                errorMes = ApvGridUtils.checkSubmitTransferStockoutWhBox(stockoutWhBox,taskType);
                if (StringUtils.isNotBlank(errorMes)) {
                    responseJson.setMessage(errorMes);
                    return responseJson;
                }
            }

            WhBox caYiWhBox = null;
            if (StringUtils.isNotBlank(boxCayi)) {
                caYiWhBox = whBoxService.queryWhBoxByBoxNo(boxCayi);
                // 检查提交的周转筐是否可用
                errorMes = ApvGridUtils.checkSubmitTranferCaYiWhBox(caYiWhBox, whPickingTask);
                if (StringUtils.isNotBlank(errorMes)) {
                    responseJson.setMessage(errorMes);
                    return responseJson;
                }
            }

            // 1,先查询缺货数量有多少
            String findBoxNo = whBoxService.findBoxNo(box);
            if (StringUtils.isEmpty(findBoxNo)) {
                responseJson.setMessage("找不到周转筐");
                return responseJson;
            }
            WhBox whBox = whBoxService.queryWhBoxByBoxNo(findBoxNo);
            if (whBox == null) {
                responseJson.setMessage("找不到周转筐");
                return responseJson;
            }

            PddGridDo domain = new PddGridDo();
            domain.setWhPickingTask(whPickingTask);
            domain.setPrepareOrderNo(orderNo);
            domain.setSku(sku);
            domain.setBoxCayi(boxCayi);
            domain.setBoxStockOut(stockoutBox);
            domain.setWhBox(whBox);
            domain.setBoxNo(box);
            pddGridService.scanBoxToBuildGridData(domain);

            boolean fnSkuFinish = StringUtils.isNotEmpty(sku) && StringUtils.isNotEmpty(orderNo);
            if (fnSkuFinish) {
                // 当前FNSKU少拣少播
                List<TemuPrepareOrderItem> lessPickItemList = Optional.ofNullable(domain.getLessPickItemList())
                        .orElse(new ArrayList<>()).stream().filter(i -> i.getSku().equalsIgnoreCase(sku))
                        .collect(Collectors.toList());
                List<TemuPrepareOrderItem> lessGridItemList = Optional.ofNullable(domain.getLessGridItemList())
                        .orElse(new ArrayList<>()).stream().filter(i -> i.getSku().equalsIgnoreCase(sku))
                        .collect(Collectors.toList());
                domain.setLessPickItemList(lessPickItemList);
                domain.setLessGridItemList(lessGridItemList);
            }

            if (StringUtils.isNotBlank(boxCayi)) {
                errorMes = PddGridUtils.checkSubmitFinishGridData(boxCayi, domain.getLessGridItemList());
                if (StringUtils.isNotBlank(errorMes)) {
                    responseJson.setMessage(errorMes);
                    return responseJson;
                }
            }

            if (stockoutWhBox != null) {
                errorMes = PddGridUtils.checkSubmitStockOutGridData(stockoutBox, domain.getLessPickItemList());
                if (StringUtils.isNotBlank(errorMes)) {
                    responseJson.setMessage(errorMes);
                    return responseJson;
                }
                WhPickingTaskQueryCondition taskQuery = new WhPickingTaskQueryCondition();
                taskQuery.setTaskTypeList(Arrays.asList(PickingTaskType.BZYC.intCode(),
                        PickingTaskType.TRANSFER_BZYC.intCode(), PickingTaskType.TEMU_BZYC.intCode()));
                taskQuery.setYcBoxNo(stockoutBox);
                List<WhPickingTask> taskList = whPickingTaskService.queryWhPickingTasks(taskQuery, null);
                if (CollectionUtils.isNotEmpty(taskList)){
                    responseJson.setMessage("周转筐已被绑定！");
                    return responseJson;
                }
                SowStockoutQueryCondition query = new SowStockoutQueryCondition();
                query.setStockoutBox(stockoutBox);
                List<SowStockout> sowStockouts = sowStockoutService.querySowStockouts(query, null);

                if (!fnSkuFinish && CollectionUtils.isNotEmpty(sowStockouts)) {
                    responseJson.setMessage("播种差异周转筐在FBA播种时只能绑定一个拣货任务!");
                    return responseJson;
                }

                boolean canBind = true;
                if (fnSkuFinish) {
                    canBind = PddGridUtils.checkStockOutBoxBindFnSku(sowStockouts, sku);
                }

                if (CollectionUtils.isNotEmpty(sowStockouts) && !canBind) {
                    // 校验一个周转筐只绑定一种FNSKU
                    responseJson.setMessage("播种差异周转筐在FBA播种时只能绑定一个FNSKU!");
                    return responseJson;
                }
            }

            responseJson = pddGridService.updateStockOutFinishGrid(stockoutWhBox, caYiWhBox, domain);

            responseJson.setStatus(StatusCode.SUCCESS);

        }
        catch (Exception e) {
            responseJson.setMessage(e.getMessage());
        }

        return responseJson;
    }
}
