package com.estone.temu.action;

import com.alibaba.fastjson.JSON;
import com.estone.temu.bean.TemuPrepareOrder;
import com.estone.temu.bean.TemuPrepareOrderItem;
import com.estone.temu.bean.TemuPrepareOrderItemQueryCondition;
import com.estone.temu.bean.TemuPrepareOrderQueryCondition;
import com.estone.temu.domain.TemuPrepareOrderItemDo;
import com.estone.temu.response.PackageInfoResponse;
import com.estone.temu.service.TemuCallService;
import com.estone.temu.service.TemuPrepareOrderItemService;
import com.estone.temu.service.TemuPrepareOrderService;
import com.estone.temu.service.TemuShipmentOrderService;
import com.whq.tool.action.BaseController;
import com.whq.tool.component.Pager;
import com.whq.tool.component.StatusCode;
import com.whq.tool.json.ResponseJson;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@RestController
@RequestMapping(value = "temuPrepareOrderItem")
public class TemuPrepareOrderItemController extends BaseController {
    @Resource
    private TemuPrepareOrderItemService temuPrepareOrderItemService;

    @Autowired
    private TemuShipmentOrderService temuShipmentOrderService;

    @Autowired
    private TemuPrepareOrderService temuPrepareOrderService;

    @Autowired
    private TemuCallService temuCallService;

    @RequestMapping(method = {RequestMethod.GET})
    public String init(@ModelAttribute("domain") TemuPrepareOrderItemDo domain) {
        return "{模块}/{页面}";
    }

    private void initFormData(@ModelAttribute("domain") TemuPrepareOrderItemDo domain) {

    }

    private void queryTemuPrepareOrderItems(@ModelAttribute("domain") TemuPrepareOrderItemDo domain) {
        TemuPrepareOrderItemQueryCondition query = domain.getQuery();
        Pager page = domain.getPage();
        if (query == null) {
            query = new TemuPrepareOrderItemQueryCondition();
            domain.setQuery(query);
        }
        // 默认读从库
        query.setReadOnly(true);
        List<TemuPrepareOrderItem> temuPrepareOrderItems = temuPrepareOrderItemService.queryTemuPrepareOrderItems(query, page);
        domain.setTemuPrepareOrderItems(temuPrepareOrderItems);
    }

    @RequestMapping(value = "search", method = {RequestMethod.POST})
    public String search(@ModelAttribute("domain") TemuPrepareOrderItemDo domain) {
        initFormData(domain);
        queryTemuPrepareOrderItems(domain);
        return "{模块}/{页面}";
    }

    @RequestMapping(value = "create", method = {RequestMethod.GET})
    public String toCreateTemuPrepareOrderItem(@ModelAttribute("domain") TemuPrepareOrderItemDo domain) {
        return "{模块}/{页面}";
    }

    @RequestMapping(value = "create", method = {RequestMethod.POST})
    public String createTemuPrepareOrderItem(@ModelAttribute("domain") TemuPrepareOrderItemDo domain) {
        TemuPrepareOrderItem temuPrepareOrderItem = domain.getTemuPrepareOrderItem();
        temuPrepareOrderItemService.createTemuPrepareOrderItem(temuPrepareOrderItem);
        return "redirect:/{查询列表}";
    }

    @RequestMapping(value = "update", method = {RequestMethod.GET})
    public String toUpdateTemuPrepareOrderItem(@ModelAttribute("domain") TemuPrepareOrderItemDo domain, @RequestParam("temuPrepareOrderItemId") Integer temuPrepareOrderItemId) {
        TemuPrepareOrderItem temuPrepareOrderItem = temuPrepareOrderItemService.getTemuPrepareOrderItem(temuPrepareOrderItemId);
        domain.setTemuPrepareOrderItem(temuPrepareOrderItem);
        return "{模块}/{页面}";
    }

    @RequestMapping(value = "update", method = {RequestMethod.POST})
    public String updateTemuPrepareOrderItem(@ModelAttribute("domain") TemuPrepareOrderItemDo domain) {
        TemuPrepareOrderItem temuPrepareOrderItem = domain.getTemuPrepareOrderItem();
        temuPrepareOrderItemService.updateTemuPrepareOrderItem(temuPrepareOrderItem);
        return "redirect:/{查询列表}";
    }

    @RequestMapping(value = "delete", method = {RequestMethod.GET})
    @ResponseBody
    public ResponseJson deleteTemuPrepareOrderItem(@ModelAttribute("domain") TemuPrepareOrderItemDo domain, @RequestParam("temuPrepareOrderItemId") Integer temuPrepareOrderItemId) {
        ResponseJson response = new ResponseJson();
        temuPrepareOrderItemService.deleteTemuPrepareOrderItem(temuPrepareOrderItemId);
        return response;
    }

    @PostMapping(value = "renewAssemblePackageInfos")
    @ResponseBody
    public ResponseJson renewAssemblePackageInfos(@RequestParam(name = "ids") List<Integer> orderItemIds) {
        ResponseJson responseJson = new ResponseJson(StatusCode.FAIL);
        if (CollectionUtils.isEmpty(orderItemIds)) {
            responseJson.setMessage("要更新的备货单明细ID为空!");
            return responseJson;
        }
        TemuPrepareOrderQueryCondition query = new TemuPrepareOrderQueryCondition();
        query.setItemIdList(orderItemIds);
        List<TemuPrepareOrder> orders = temuPrepareOrderService.queryTemuPrepareOrderAndItems(query, null);
        if (CollectionUtils.isEmpty(orders)) {
            responseJson.setMessage("根据备货单明细ID:" + JSON.toJSONString(orderItemIds) + "无法查询出对应备货单对象信息!");
            return responseJson;
        }

        for (TemuPrepareOrder temuPrepareOrder : orders) {
            //查询包裹
            List<PackageInfoResponse> temuPackag = temuCallService.getTemuPackag(temuPrepareOrder.getDeliverOrderNo(), temuPrepareOrder.getAccountNumber());
            //组装包裹
            ResponseJson response = temuShipmentOrderService.renewAssemblePackageInfo(temuPackag, temuPrepareOrder);
            if (Objects.equals(StatusCode.FAIL, response.getStatus())){
                responseJson.setMessage(responseJson.getMessage()  + response.getMessage() + "<br/>");
            }
        }
        if (StringUtils.isBlank(responseJson.getMessage())){
            responseJson.setStatus(StatusCode.SUCCESS);
        }
        return responseJson;
    }

}