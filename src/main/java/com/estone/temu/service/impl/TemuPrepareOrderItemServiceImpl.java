package com.estone.temu.service.impl;

import com.estone.common.util.SystemLogUtils;
import com.estone.temu.bean.TemuPrepareOrderItem;
import com.estone.temu.bean.TemuPrepareOrderItemQueryCondition;
import com.estone.temu.dao.TemuPrepareOrderItemDao;
import com.estone.temu.service.TemuPrepareOrderItemService;
import com.whq.tool.component.Pager;
import com.whq.tool.exception.BusinessException;
import com.whq.tool.exception.DBErrorCode;
import com.whq.tool.sqler.SqlerException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@Service("temuPrepareOrderItemService")
@Slf4j
public class TemuPrepareOrderItemServiceImpl implements TemuPrepareOrderItemService {
    @Resource
    private TemuPrepareOrderItemDao temuPrepareOrderItemDao;

    @Override
    public TemuPrepareOrderItem getTemuPrepareOrderItem(Integer id) {
        TemuPrepareOrderItem temuPrepareOrderItem = temuPrepareOrderItemDao.queryTemuPrepareOrderItem(id);
        return temuPrepareOrderItem;
    }

    @Override
    public TemuPrepareOrderItem getTemuPrepareOrderItemDetail(Integer id) {
        TemuPrepareOrderItem temuPrepareOrderItem = temuPrepareOrderItemDao.queryTemuPrepareOrderItem(id);
        // 关联查询
        return temuPrepareOrderItem;
    }

    @Override
    public TemuPrepareOrderItem queryTemuPrepareOrderItem(TemuPrepareOrderItemQueryCondition query) {
        Assert.notNull(query, "query is null!");
        TemuPrepareOrderItem temuPrepareOrderItem = temuPrepareOrderItemDao.queryTemuPrepareOrderItem(query);
        return temuPrepareOrderItem;
    }

    @Override
    public List<TemuPrepareOrderItem> queryAllTemuPrepareOrderItems() {
        return temuPrepareOrderItemDao.queryTemuPrepareOrderItemList();
    }

    @Override
    public List<TemuPrepareOrderItem> queryTemuPrepareOrderItems(TemuPrepareOrderItemQueryCondition query, Pager pager) {
        Assert.notNull(query, "query is null!");
        if (pager != null && pager.isQueryCount()) {
            int count = temuPrepareOrderItemDao.queryTemuPrepareOrderItemCount(query);
            pager.setTotalCount(count);
            if ( count == 0) {
                return new ArrayList<TemuPrepareOrderItem>();
            }
        }
        List<TemuPrepareOrderItem> temuPrepareOrderItems = temuPrepareOrderItemDao.queryTemuPrepareOrderItemList(query, pager);
        return temuPrepareOrderItems;
    }

    @Override
    public void createTemuPrepareOrderItem(TemuPrepareOrderItem temuPrepareOrderItem) {
        try {
            temuPrepareOrderItemDao.createTemuPrepareOrderItem(temuPrepareOrderItem);
        }
        catch (SqlerException e) {
            log.error(e.getMessage(), e);
            throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
        }
    }

    @Override
    public void batchCreateTemuPrepareOrderItem(List<TemuPrepareOrderItem> entityList) {
        if (CollectionUtils.isNotEmpty(entityList)) {
            try {
                temuPrepareOrderItemDao.batchCreateTemuPrepareOrderItem(entityList);
            }
            catch (SqlerException e) {
                log.error(e.getMessage(), e);
                throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
            }
        }
    }

    @Override
    public void deleteTemuPrepareOrderItem(Integer id) {
        try {
            temuPrepareOrderItemDao.deleteTemuPrepareOrderItem(id);
        }
        catch (SqlerException e) {
            log.error(e.getMessage(), e);
            throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
        }
    }

    @Override
    public void updateTemuPrepareOrderItem(TemuPrepareOrderItem temuPrepareOrderItem) {
        try {
            temuPrepareOrderItemDao.updateTemuPrepareOrderItem(temuPrepareOrderItem);
        }
        catch (SqlerException e) {
            log.error(e.getMessage(), e);
            throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
        }
    }

    @Override
    public void batchUpdateTemuPrepareOrderItem(List<TemuPrepareOrderItem> entityList) {
        if (CollectionUtils.isNotEmpty(entityList)) {
            try {
                temuPrepareOrderItemDao.batchUpdateTemuPrepareOrderItem(entityList);
            }
            catch (SqlerException e) {
                log.error(e.getMessage(), e);
                throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
            }
        }
    }

    // 记录包裹日志
    @Override
    public void addLogs(List<Integer> ids, String operation, String statusName) {
        for (Integer id : ids) {
            SystemLogUtils.TEMU_PACKAGE.log(id, operation, new String[][]{
                    {"状态", statusName}});
        }
    }

    @Override
    public int unPackCountInTemuTask(String packageSn,Integer taskType) {
        return temuPrepareOrderItemDao.unPackCountInTemuTask(packageSn,taskType);
    }
}