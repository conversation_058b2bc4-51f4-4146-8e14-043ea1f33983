package com.estone.temu.enums;

/**
 * @Description: temu退货状态
 * @Author: Yimeil
 * @Date: 2024/3/13 9:33
 * @Version: 1.0.0
 */
public enum TemuReturnStatus {

    WAIT_RECEIVE("1", "待提货"),
    OUT_OF_STORAGE("3", "已出库"),
    WMS_RETURNED("5", "已退仓"),

    RETURN("20", "承运商退回"),
    ;

    private String code;

    private String name;

    private TemuReturnStatus(String code, String name) {
        this.name = name;
        this.code = code;
    }
    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
