package com.estone.temu.domain;

import com.estone.temu.bean.TemuExpressRelation;
import com.estone.temu.bean.TemuExpressRelationQueryCondition;
import com.whq.tool.component.Pager;
import java.util.ArrayList;
import java.util.List;

public class TemuExpressRelationDo {
    private TemuExpressRelation temuExpressRelation;

    private TemuExpressRelationQueryCondition query;

    private List<TemuExpressRelation> temuExpressRelations = new ArrayList<TemuExpressRelation>();

    private Pager page = new Pager();

    public TemuExpressRelation getTemuExpressRelation() {
        return temuExpressRelation;
    }

    public void setTemuExpressRelation(TemuExpressRelation temuExpressRelation) {
        this.temuExpressRelation = temuExpressRelation;
    }

    public TemuExpressRelationQueryCondition getQuery() {
        return query;
    }

    public void setQuery(TemuExpressRelationQueryCondition query) {
        this.query = query;
    }

    public List<TemuExpressRelation> getTemuExpressRelations() {
        return temuExpressRelations;
    }

    public void setTemuExpressRelations(List<TemuExpressRelation> temuExpressRelations) {
        this.temuExpressRelations = temuExpressRelations;
    }

    public Pager getPage() {
        return page;
    }

    public void setPage(Pager page) {
        this.page = page;
    }
}