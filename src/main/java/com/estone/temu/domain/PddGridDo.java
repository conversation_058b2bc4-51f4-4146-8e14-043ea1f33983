package com.estone.temu.domain;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import com.estone.picking.bean.WhPickingTask;
import com.estone.picking.bean.WhPickingTaskSku;
import com.estone.sku.bean.SkuTagInfo;
import com.estone.sku.bean.WhSkuWithPmsInfo;
import com.estone.temu.bean.TemuPackageInfo;
import com.estone.temu.bean.TemuPrepareOrder;
import com.estone.temu.bean.TemuPrepareOrderItem;
import com.estone.temu.bean.TemuSkuLabel;
import com.estone.temu.bean.dto.TemuForPackDto;
import com.estone.warehouse.bean.WhBox;

import lombok.Data;

@Data
public class PddGridDo {

    /**
     * 错误消息
     */
    private String errorMsg;

    /**
     * 扫描完成的apv数量
     */
    private Integer successApvCount;

    private Integer pickQuantity;

    private Integer gridQuantity;

    /**
     * 周转框
     */
    private String boxNo;

    private String sku;

    private String uuid;

    private List<TemuPrepareOrder> orderList;

    private List<TemuPrepareOrderItem> taskItemList; // fNSKU

    private List<TemuPrepareOrderItem> lessGridItemList;// 播种差异（播种<已拣）

    private List<TemuPrepareOrderItem> lessPickItemList;// 少拣的

    private List<TemuPrepareOrderItem> cancelGridItemList;// 取消的

    private List<WhPickingTaskSku> whPickingTaskSkuList;

    private boolean allGrid;// 发货单对应的FNSKU全部播种完成

    private boolean taskAllGrid;// 拣货任务全部播种完成

    private TemuPrepareOrderItem skuItem;

    private TemuSkuLabel skuLabel;

    private String preventSuffocation;

    private Integer scanNumber;

    private Integer taskType;

    private Integer gridStatus;

    private String lastSku;

    private WhPickingTask whPickingTask;

    private String prepareOrderNo;

    private String boxCayi;

    private String boxStockOut;

    private WhBox whBox;

    private String taskNo;

    private Double fnSkuTollWeight;

    private Boolean forceFinish;

    private SkuTagInfo tagInfo;

    private String skuTag;

    private TemuForPackDto packageInfo;

    private TemuPackageInfo temuPackageInfo;

    private String packageSn;
    /**
     * sku对应的 搭配包材 等
     */
    private List<WhSkuWithPmsInfo> whSkuWithPmsInfos = new ArrayList<WhSkuWithPmsInfo>();

    private Map<String, String> skuTagMap;
}