package com.estone.combineSku.dao.impl;

import com.estone.combineSku.bean.WhCombineSkuTask;
import com.estone.combineSku.bean.WhCombineSkuTaskQueryCondition;
import com.estone.combineSku.dao.WhCombineSkuTaskDao;
import com.estone.combineSku.dao.mapper.WhCombineSkuDBField;
import com.estone.combineSku.dao.mapper.WhCombineSkuTaskDBField;
import com.estone.combineSku.dao.mapper.WhCombineSkuTaskMapper;
import com.estone.common.util.CommonUtils;
import com.estone.shop.dao.mapper.ShopOrderDBField;
import com.whq.tool.component.Pager;
import com.whq.tool.context.DataContextHolder;
import com.whq.tool.sqler.SqlerRequest;
import com.whq.tool.sqler.analyzer.DataType;
import com.whq.tool.sqler.dialect.DialectFactory;
import com.whq.tool.sqler.dialect.SQLDialect;
import com.estone.common.util.SqlerTemplate;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Repository;
import org.springframework.util.Assert;

import java.sql.Timestamp;
import java.util.List;

@Repository("whCombineSkuTaskDao")
public class WhCombineSkuTaskDaoImpl implements WhCombineSkuTaskDao {

    private void setQueryCondition(SqlerRequest request, WhCombineSkuTaskQueryCondition query) {
        if (query == null) {
            return;
        }
        // 添加只读权限
        if (query.getReadOnly() != null && query.getReadOnly())
        {
            request.setReadOnly(true);
        }
        // 添加查询条件
        // 搜索条件不允许出现空字符
        request.setAllowBlank(false);
        
        request.addDataParam(WhCombineSkuTaskDBField.ID, DataType.INT, query.getId());
        if (CollectionUtils.isNotEmpty(query.getIdList())) {
            request.addDataParam("id_list", DataType.INT,query.getIdList());
        }
        if (StringUtils.contains(query.getSpu(),",")){
            request.addDataParam("spu_list", DataType.STRING,  CommonUtils.splitList(query.getSpu(), ","));
        } else {
            request.addDataParam(WhCombineSkuTaskDBField.SPU, DataType.STRING, query.getSpu());
        }
        if (StringUtils.isNotBlank(query.getSku())){
            request.addDataParam("sku_list", DataType.STRING,  CommonUtils.splitList(query.getSku(), ","));
        }
        request.addDataParam(WhCombineSkuTaskDBField.STATUS, DataType.INT, query.getStatus());
        if (CollectionUtils.isNotEmpty(query.getStatusList())){
            request.addDataParam("status_list", DataType.INT, query.getStatusList());
        }
        request.addDataParam(WhCombineSkuTaskDBField.STOCK_ID, DataType.INT, query.getStockId());
        request.addDataParam("from_create_time", DataType.STRING, query.getFromCreationDate());
        request.addDataParam("to_create_time", DataType.STRING, query.getToCreationDate());
        if (query.getReceiveFlag() != null && query.getReceiveFlag()) {
            request.addSqlDataParam("RECEIVE_PERSON_FILED_SQL",",(SELECT receive_person from wh_combine_sku_picking_task where id =  (SELECT max(spt.id) FROM wh_combine_sku_picking_task spt\n" +
                    "        LEFT JOIN wh_combine_sku_picking_task_item spti ON spti.task_id = spt.id where spti.combine_task_id = s.id and spt.task_status != 5)) as receivePerson");
            request.addSqlDataParam("RECEIVE_DATE_FILED_SQL",",(SELECT receive_date from wh_combine_sku_picking_task where id =  (SELECT max(spt.id) FROM wh_combine_sku_picking_task spt\n" +
                    "        LEFT JOIN wh_combine_sku_picking_task_item spti ON spti.task_id = spt.id where spti.combine_task_id = s.id and spt.task_status != 5)) as receiveDate");
            if (query.getReceivePerson() != null) {
                request.addSqlDataParam("RECEIVE_DATE_CONDITION_SQL","AND id in (SELECT spti.combine_task_id FROM wh_combine_sku_picking_task spt " +
                        "        INNER JOIN wh_combine_sku_picking_task_item spti ON spti.task_id = spt.id where spt.receive_person = "+query.getReceivePerson()+")");
            }
        }
    }

    @Override
    public int queryWhCombineSkuTaskCount(WhCombineSkuTaskQueryCondition query) {
        SqlerRequest request = new SqlerRequest("queryWhCombineSkuTaskCount");
        setQueryCondition(request, query);
        return SqlerTemplate.queryForInt(request);
    }

    @Override
    public List<WhCombineSkuTask> queryWhCombineSkuTaskList() {
        SqlerRequest request = new SqlerRequest("queryWhCombineSkuTaskList");
        return SqlerTemplate.query(request, new WhCombineSkuTaskMapper());
    }

    @Override
    public List<WhCombineSkuTask> queryWhCombineSkuTaskList(WhCombineSkuTaskQueryCondition query, Pager pager) {
        SqlerRequest request = new SqlerRequest("queryWhCombineSkuTaskList");
        setQueryCondition(request, query);
        if(pager != null) {
            SQLDialect dial = DialectFactory.createDialect(null);
            long start = (long) (pager.getPageNo() - 1) * pager.getPageSize();
            long end = start + pager.getPageSize() - 1L;
            request.addSqlDataParam("LIMIT", dial.queryFirst2Last("", (int) start, (int) end));
        }
        return SqlerTemplate.query(request, new WhCombineSkuTaskMapper(true,query.getReceiveFlag()));
    }

    @Override
    public WhCombineSkuTask queryWhCombineSkuTask(Integer primaryKey) {
        Assert.notNull(primaryKey, "primaryKey is null!");
        SqlerRequest request = new SqlerRequest("queryWhCombineSkuTaskByPrimaryKey");
        request.addDataParam(WhCombineSkuTaskDBField.ID, DataType.INT, primaryKey);
        return SqlerTemplate.queryForObject(request, new WhCombineSkuTaskMapper());
    }

    @Override
    public WhCombineSkuTask queryWhCombineSkuTask(WhCombineSkuTaskQueryCondition query) {
        SqlerRequest request = new SqlerRequest("queryWhCombineSkuTask");
        setQueryCondition(request, query);
        return SqlerTemplate.queryForObject(request, new WhCombineSkuTaskMapper());
    }

    @Override
    public void createWhCombineSkuTask(WhCombineSkuTask entity) {
        SqlerRequest request = new SqlerRequest("createWhCombineSkuTask");
        request.addDataParam(WhCombineSkuTaskDBField.ID, DataType.INT, entity.getId());
        request.addDataParam(WhCombineSkuTaskDBField.SPU, DataType.STRING, entity.getSpu());
        request.addDataParam(WhCombineSkuTaskDBField.STATUS, DataType.INT, entity.getStatus());
        request.addDataParam(WhCombineSkuTaskDBField.CREATED_DATE, DataType.TIMESTAMP, new Timestamp(System.currentTimeMillis()));
        request.addDataParam(WhCombineSkuTaskDBField.CREATE_BY, DataType.INT,entity.getCreateBy() == null ? DataContextHolder.getUserId() : entity.getCreateBy());
        request.addDataParam(WhCombineSkuTaskDBField.COMBINE_DATE, DataType.TIMESTAMP, entity.getCombineDate());
        request.addDataParam(WhCombineSkuTaskDBField.COMBINE_BY, DataType.INT, entity.getCombineBy());
        request.addDataParam(WhCombineSkuTaskDBField.UP_DATE, DataType.TIMESTAMP, entity.getUpDate());
        request.addDataParam(WhCombineSkuTaskDBField.UP_BY, DataType.INT, entity.getUpBy());
        request.addDataParam(WhCombineSkuTaskDBField.COMBINE_QUANTITY, DataType.INT, entity.getCombineQuantity());
        request.addDataParam(WhCombineSkuTaskDBField.REAL_COMBINE_QUANTITY, DataType.INT, entity.getRealCombineQuantity());
        request.addDataParam(WhCombineSkuTaskDBField.LOCATION_NUMBER, DataType.STRING, entity.getLocationNumber());
        request.addDataParam(WhCombineSkuTaskDBField.STOCK_ID, DataType.INT, entity.getStockId());
        request.addDataParam(WhCombineSkuTaskDBField.UP_QUANTITY, DataType.INT, entity.getUpQuantity());
        request.addDataParam(WhCombineSkuTaskDBField.ESTIMATED_COMBINE_QUANTITY, DataType.INT, entity.getEstimatedCombineQuantity());
        request.addDataParam(WhCombineSkuTaskDBField.ALLOT_ESTIMATED_COMBINE_QUANTITY, DataType.INT, entity.getAllotEstimatedCombineQuantity());
        Integer autoIncrementId = SqlerTemplate.executeAndReturn(request);
        entity.setId(autoIncrementId);
    }

    @Override
    public void updateWhCombineSkuTask(WhCombineSkuTask entity) {
        SqlerRequest request = new SqlerRequest("updateWhCombineSkuTaskByPrimaryKey");
        request.addDataParam(WhCombineSkuTaskDBField.ID, DataType.INT, entity.getId());
        
        request.addDataParam(WhCombineSkuTaskDBField.SPU, DataType.STRING, entity.getSpu());
        request.addDataParam(WhCombineSkuTaskDBField.STATUS, DataType.INT, entity.getStatus());
        request.addDataParam(WhCombineSkuTaskDBField.CREATED_DATE, DataType.TIMESTAMP, entity.getCreatedDate());
        request.addDataParam(WhCombineSkuTaskDBField.CREATE_BY, DataType.INT, entity.getCreateBy());
        request.addDataParam(WhCombineSkuTaskDBField.COMBINE_DATE, DataType.TIMESTAMP, entity.getCombineDate());
        request.addDataParam(WhCombineSkuTaskDBField.COMBINE_BY, DataType.INT, entity.getCombineBy());
        request.addDataParam(WhCombineSkuTaskDBField.UP_DATE, DataType.TIMESTAMP, entity.getUpDate());
        request.addDataParam(WhCombineSkuTaskDBField.UP_BY, DataType.INT, entity.getUpBy());
        request.addDataParam(WhCombineSkuTaskDBField.COMBINE_QUANTITY, DataType.INT, entity.getCombineQuantity());
        request.addDataParam(WhCombineSkuTaskDBField.REAL_COMBINE_QUANTITY, DataType.INT, entity.getRealCombineQuantity());
        request.addDataParam(WhCombineSkuTaskDBField.LOCATION_NUMBER, DataType.STRING, entity.getLocationNumber());
        request.addDataParam(WhCombineSkuTaskDBField.STOCK_ID, DataType.INT, entity.getStockId());
        request.addDataParam(WhCombineSkuTaskDBField.UP_QUANTITY, DataType.INT, entity.getUpQuantity());
        request.addDataParam(WhCombineSkuTaskDBField.ESTIMATED_COMBINE_QUANTITY, DataType.INT, entity.getEstimatedCombineQuantity());
        request.addDataParam(WhCombineSkuTaskDBField.ALLOT_ESTIMATED_COMBINE_QUANTITY, DataType.INT, entity.getAllotEstimatedCombineQuantity());
        SqlerTemplate.execute(request);
    }

    @Override
    public void batchCreateWhCombineSkuTask(List<WhCombineSkuTask> entityList) {
        if (entityList != null && !entityList.isEmpty()) {
            SqlerRequest request = new SqlerRequest("createWhCombineSkuTask");
            for (WhCombineSkuTask entity : entityList) {
                request.addBatchDataParam(WhCombineSkuTaskDBField.ID, DataType.INT, entity.getId());
                request.addBatchDataParam(WhCombineSkuTaskDBField.SPU, DataType.STRING, entity.getSpu());
                request.addBatchDataParam(WhCombineSkuTaskDBField.STATUS, DataType.INT, entity.getStatus());
                request.addBatchDataParam(WhCombineSkuTaskDBField.CREATED_DATE, DataType.TIMESTAMP, new Timestamp(System.currentTimeMillis()));
                request.addBatchDataParam(WhCombineSkuTaskDBField.CREATE_BY, DataType.INT,entity.getCreateBy() == null ? DataContextHolder.getUserId() : entity.getCreateBy());
                request.addBatchDataParam(WhCombineSkuTaskDBField.COMBINE_DATE, DataType.TIMESTAMP, entity.getCombineDate());
                request.addBatchDataParam(WhCombineSkuTaskDBField.COMBINE_BY, DataType.INT, entity.getCombineBy());
                request.addBatchDataParam(WhCombineSkuTaskDBField.UP_DATE, DataType.TIMESTAMP, entity.getUpDate());
                request.addBatchDataParam(WhCombineSkuTaskDBField.UP_BY, DataType.INT, entity.getUpBy());
                request.addBatchDataParam(WhCombineSkuTaskDBField.COMBINE_QUANTITY, DataType.INT, entity.getCombineQuantity());
                request.addBatchDataParam(WhCombineSkuTaskDBField.REAL_COMBINE_QUANTITY, DataType.INT, entity.getRealCombineQuantity());
                request.addBatchDataParam(WhCombineSkuTaskDBField.LOCATION_NUMBER, DataType.STRING, entity.getLocationNumber());
                request.addBatchDataParam(WhCombineSkuTaskDBField.STOCK_ID, DataType.INT, entity.getStockId());
                request.addBatchDataParam(WhCombineSkuTaskDBField.UP_QUANTITY, DataType.INT, entity.getUpQuantity());
                request.addBatchDataParam(WhCombineSkuTaskDBField.ESTIMATED_COMBINE_QUANTITY, DataType.INT, entity.getEstimatedCombineQuantity());
                request.addBatchDataParam(WhCombineSkuTaskDBField.ALLOT_ESTIMATED_COMBINE_QUANTITY, DataType.INT, entity.getAllotEstimatedCombineQuantity());
                request.addBatch();
            }
            SqlerTemplate.batchUpdate(request);
        }
    }

    @Override
    public void batchUpdateWhCombineSkuTask(List<WhCombineSkuTask> entityList) {
        if (entityList != null && !entityList.isEmpty()) {
            SqlerRequest request = new SqlerRequest("updateWhCombineSkuTaskByPrimaryKey");
            for (WhCombineSkuTask entity : entityList) {
                request.addBatchDataParam(WhCombineSkuTaskDBField.ID, DataType.INT, entity.getId());
                
                request.addBatchDataParam(WhCombineSkuTaskDBField.SPU, DataType.STRING, entity.getSpu());
                request.addBatchDataParam(WhCombineSkuTaskDBField.STATUS, DataType.INT, entity.getStatus());
                request.addBatchDataParam(WhCombineSkuTaskDBField.CREATED_DATE, DataType.TIMESTAMP, entity.getCreatedDate());
                request.addBatchDataParam(WhCombineSkuTaskDBField.CREATE_BY, DataType.INT, entity.getCreateBy());
                request.addBatchDataParam(WhCombineSkuTaskDBField.COMBINE_DATE, DataType.TIMESTAMP, entity.getCombineDate());
                request.addBatchDataParam(WhCombineSkuTaskDBField.COMBINE_BY, DataType.INT, entity.getCombineBy());
                request.addBatchDataParam(WhCombineSkuTaskDBField.UP_DATE, DataType.TIMESTAMP, entity.getUpDate());
                request.addBatchDataParam(WhCombineSkuTaskDBField.UP_BY, DataType.INT, entity.getUpBy());
                request.addBatchDataParam(WhCombineSkuTaskDBField.COMBINE_QUANTITY, DataType.INT, entity.getCombineQuantity());
                request.addBatchDataParam(WhCombineSkuTaskDBField.REAL_COMBINE_QUANTITY, DataType.INT, entity.getRealCombineQuantity());
                request.addBatchDataParam(WhCombineSkuTaskDBField.LOCATION_NUMBER, DataType.STRING, entity.getLocationNumber());
                request.addBatchDataParam(WhCombineSkuTaskDBField.STOCK_ID, DataType.INT, entity.getStockId());
                request.addBatchDataParam(WhCombineSkuTaskDBField.UP_QUANTITY, DataType.INT, entity.getUpQuantity());
                request.addBatchDataParam(WhCombineSkuTaskDBField.ESTIMATED_COMBINE_QUANTITY, DataType.INT, entity.getEstimatedCombineQuantity());
                request.addBatchDataParam(WhCombineSkuTaskDBField.ALLOT_ESTIMATED_COMBINE_QUANTITY, DataType.INT, entity.getAllotEstimatedCombineQuantity());
                request.addBatch();
            }
            SqlerTemplate.batchUpdate(request);
        }
    }

    @Override
    public void deleteWhCombineSkuTask(Integer primaryKey) {
        SqlerRequest request = new SqlerRequest("deleteWhCombineSkuTaskByPrimaryKey");
        request.addDataParam(WhCombineSkuTaskDBField.ID, DataType.INT, primaryKey);
        SqlerTemplate.execute(request);
    }
}