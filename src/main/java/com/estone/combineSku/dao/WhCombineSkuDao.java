package com.estone.combineSku.dao;

import com.estone.combineSku.bean.WhCombineSku;
import com.estone.combineSku.bean.WhCombineSkuQueryCondition;
import com.whq.tool.component.Pager;
import java.util.List;
import java.util.Map;

public interface WhCombineSkuDao {
    int queryWhCombineSkuCount(WhCombineSkuQueryCondition query);

    List<WhCombineSku> queryWhCombineSkuList();

    List<WhCombineSku> queryWhCombineSkuList(WhCombineSkuQueryCondition query, Pager pager);

    WhCombineSku queryWhCombineSku(Integer primaryKey);

    WhCombineSku queryWhCombineSku(WhCombineSkuQueryCondition query);

    void createWhCombineSku(WhCombineSku entity);

    void batchCreateWhCombineSku(List<WhCombineSku> entityList);

    void batchUpdateWhCombineSku(List<WhCombineSku> entityList);

    void deleteWhCombineSku(Integer primaryKey);

    void updateWhCombineSku(WhCombineSku entity);

    Map<String, Integer> queryIdBySpus(WhCombineSkuQueryCondition queryCondition);
}