package com.estone.combineSku.bean;

import lombok.Data;

import java.util.List;

@Data
public class WhCombineSkuQueryCondition extends WhCombineSku {
    private static final long serialVersionUID = 1L;

    private Boolean readOnly = false;

    private String sku;

    private List<Integer> ids ;

    /** 创建时间 */
    private String fromCreationDate;
    private String toCreationDate;

    // 是否查询库存信息
    private boolean stockFlag;

    // 指定库位
    private String locationNumber;

    private List<String> spus;
}