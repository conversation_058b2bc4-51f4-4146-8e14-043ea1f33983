package com.estone.combineSku.bean;

import com.estone.combineSku.enums.WhCombineSkuTaskStatus;
import com.estone.common.util.CompareParam;
import com.estone.warehouse.bean.WhStock;
import lombok.Data;

import java.io.Serializable;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;

@Data
public class WhCombineSkuTask implements Serializable, CompareParam {
    private static final long serialVersionUID = 1L;

    /**
     *  database column wh_combine_sku_task.id
     */
    private Integer id;

    /**
     * spu database column wh_combine_sku_task.spu
     */
    private String spu;

    /**
     * 状态 database column wh_combine_sku_task.status
     */
    private Integer status;

    /**
     * 创建时间 database column wh_combine_sku_task.created_date
     */
    private Timestamp createdDate;

    /**
     * 创建人 database column wh_combine_sku_task.create_by
     */
    private Integer createBy;

    /**
     * 组装时间 database column wh_combine_sku_task.combine_date
     */
    private Timestamp combineDate;

    /**
     * 组装人 database column wh_combine_sku_task.combine_by
     */
    private Integer combineBy;

    /**
     * 领取人 database column wh_combine_sku_picking_task.receive_person
     */
    private Integer receivePerson;

    /**
     * 领取时间 database column wh_combine_sku_picking_task.receive_date
     */
    private Timestamp receiveDate;

    /**
     * 上架人 database column wh_combine_sku_task.up_date
     */
    private Timestamp upDate;

    /**
     * 上架时间 database column wh_combine_sku_task.up_by
     */
    private Integer upBy;

    /**
     * 组装数量 database column wh_combine_sku_task.combine_quantity
     */
    private Integer combineQuantity;

    /**
     * 实际组装数量 database column wh_combine_sku_task.real_combine_quantity
     */
    private Integer realCombineQuantity;

    /**
     * 预估可组装数量
     */
    private Integer estimatedCombineQuantity;

    /**
     * 分配预估可组装数量
     */
    private Integer allotEstimatedCombineQuantity;

    /**
     * 上架数量 database column wh_combine_sku_task.up_quantity
     */
    private Integer upQuantity;

    private Integer stockId;

    /**
     * 指定库位
     */
    private String locationNumber;

    /******************************* 非数据库字段 ********************************/

    /**
     * 上架库位 database column wh_combine_sku_task.location_number
     */
    private WhStock whStock;

    private WhCombineSku whCombineSku;

    public String getLocationNumberStr() {
        if (whStock == null) return null;
        return whStock.getLocationNumber();
    }

    private List<WhCombineSkuTaskItem> itemList = new ArrayList<>();


    public String getStatusStr() {return  status != null ? WhCombineSkuTaskStatus.getNameByCode(status.toString()) : null;}

    @Override
    public String getSku() {
        return spu;
    }
}