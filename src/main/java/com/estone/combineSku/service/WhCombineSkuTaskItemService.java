package com.estone.combineSku.service;

import com.estone.combineSku.bean.WhCombineSkuTaskItem;
import com.estone.combineSku.bean.WhCombineSkuTaskItemQueryCondition;
import com.whq.tool.component.Pager;
import java.util.List;

public interface WhCombineSkuTaskItemService {
    List<WhCombineSkuTaskItem> queryAllWhCombineSkuTaskItems();

    List<WhCombineSkuTaskItem> queryWhCombineSkuTaskItems(WhCombineSkuTaskItemQueryCondition query, Pager pager);

    WhCombineSkuTaskItem getWhCombineSkuTaskItem(Integer id);

    WhCombineSkuTaskItem getWhCombineSkuTaskItemDetail(Integer id);

    WhCombineSkuTaskItem queryWhCombineSkuTaskItem(WhCombineSkuTaskItemQueryCondition query);

    void createWhCombineSkuTaskItem(WhCombineSkuTaskItem whCombineSkuTaskItem);

    void batchCreateWhCombineSkuTaskItem(List<WhCombineSkuTaskItem> entityList);

    void deleteWhCombineSkuTaskItem(Integer id);

    void updateWhCombineSkuTaskItem(WhCombineSkuTaskItem whCombineSkuTaskItem);

    void batchUpdateWhCombineSkuTaskItem(List<WhCombineSkuTaskItem> entityList);
}