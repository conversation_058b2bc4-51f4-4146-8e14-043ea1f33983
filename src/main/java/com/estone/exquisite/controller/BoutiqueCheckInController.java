package com.estone.exquisite.controller;

import java.util.*;

import javax.annotation.Resource;

import com.estone.common.util.CommonUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.web.bind.annotation.*;

import com.estone.common.util.POIUtils;
import com.estone.common.util.TaglibUtils;
import com.estone.common.util.model.ApiResult;
import com.estone.common.util.model.CQuery;
import com.estone.common.util.model.CQueryResult;
import com.estone.exquisite.bean.BoutiqueCheckIn;
import com.estone.exquisite.bean.BoutiquePurchaseItem;
import com.estone.exquisite.bean.BoutiquePurchaseItemQueryCondition;
import com.estone.exquisite.bean.BqPurchaseOrder;
import com.estone.exquisite.service.BoutiqueCheckInService;
import com.estone.exquisite.service.BoutiquePurchaseOrderService;
import com.estone.exquisite.service.impl.BoutiquePurchaseOrderServiceImpl;
import com.estone.system.downloadcenter.enums.WhDownloadContentEnum;
import com.estone.system.downloadcenter.service.WhDownloadCenterService;
import com.whq.tool.action.BaseController;
import com.whq.tool.component.Pager;

import lombok.extern.slf4j.Slf4j;

import static java.util.stream.Collectors.toList;

@RestController
@RequestMapping(value = "bqCheckIn")
@Slf4j
public class BoutiqueCheckInController extends BaseController {
    @Resource
    private BoutiqueCheckInService boutiqueCheckInService;
    @Resource
    private BoutiquePurchaseOrderService boutiquePurchaseOrderService;
    @Resource
    private WhDownloadCenterService whDownloadCenterService;

    @PostMapping(value = "createCheckIn")
    public ApiResult<?> createCheckIn(@RequestBody BoutiqueCheckIn checkIn) {
        try {
            return boutiqueCheckInService.createCheckIn(Arrays.asList(checkIn.getSku()), checkIn);
        }
        catch (Exception e) {
            log.error(e.getMessage(), e);
            return ApiResult.newError(e.getMessage());
        }
    }

    @PostMapping(value = "purchaseList")
    public ApiResult<?> getBqPurchaseList(@RequestBody CQuery<BoutiquePurchaseItemQueryCondition> query) {
        CQueryResult<BqPurchaseOrder> result = null;
        try {
            if (query == null)
                return ApiResult.newError("param error");
            if (query.getSearch() == null) {
                query.setSearch(new BoutiquePurchaseItemQueryCondition());
            }
            result = ((BoutiquePurchaseOrderServiceImpl) boutiquePurchaseOrderService).list(query);
            return ApiResult.newSuccess(result);
        }
        catch (Exception e) {
            log.error(e.getMessage(), e);
            return ApiResult.newError(e.getMessage());
        }
    }

    @GetMapping(value = "getBqPurchaseDetail/{id}")
    public ApiResult<?> getBqPurchaseDetail(@PathVariable("id") Integer id) {
        try {
            BoutiquePurchaseItemQueryCondition query = new BoutiquePurchaseItemQueryCondition();
            query.setPId(id);
            query.setQueryItem(true);
            List<BqPurchaseOrder> bqPurchaseOrders = boutiquePurchaseOrderService.queryWhPurchaseOrders(query, null);
            if (CollectionUtils.isEmpty(bqPurchaseOrders)) {
                return ApiResult.newError("没有找到采购单");
            }
            BqPurchaseOrder purchaseOrder = bqPurchaseOrders.get(0);
            List<BoutiqueCheckIn> checkInList = Optional.ofNullable(purchaseOrder.getJpCheckInList())
                    .orElse(new ArrayList<>()).stream()
                    .sorted(Comparator.comparing(BoutiqueCheckIn::getInId).reversed()).collect(toList());
            purchaseOrder.setJpCheckInList(checkInList);
            return ApiResult.newSuccess(purchaseOrder);
        }
        catch (Exception e) {
            log.error(e.getMessage(), e);
            return ApiResult.newError(e.getMessage());
        }
    }

    @GetMapping(value = "getBqPurchaseItemList")
    public ApiResult<?> getBqPurchaseItemList(@RequestParam("pId") Integer pId, @RequestParam("sku") String sku) {
        try {
            BoutiquePurchaseItemQueryCondition query = new BoutiquePurchaseItemQueryCondition();
            query.setPId(pId);
            query.setSku(sku);
            query.setQueryItem(true);
            List<BqPurchaseOrder> bqPurchaseOrders = boutiquePurchaseOrderService.queryWhPurchaseOrders(query, null);
            if (CollectionUtils.isEmpty(bqPurchaseOrders)
                    || CollectionUtils.isEmpty(bqPurchaseOrders.get(0).getJpItems())) {
                return ApiResult.newError("没有找到采购单明细");
            }
            if (StringUtils.isNotEmpty(sku)) {
                List<String> skuList = CommonUtils.splitList(sku, ",");
                bqPurchaseOrders.get(0).getJpItems().removeIf(item -> !skuList.contains(item.getSku()));
            }
            return ApiResult.newSuccess(bqPurchaseOrders.get(0).getJpItems());
        }
        catch (Exception e) {
            log.error(e.getMessage(), e);
            return ApiResult.newError(e.getMessage());
        }
    }

    @GetMapping(value = "getBqPurchaseCheckInList")
    public ApiResult<?> getBqPurchaseCheckInList(@RequestParam("purchaseOrderNo") String purchaseOrderNo,
            @RequestParam(value = "sku", required = false) String sku) {
        try {
            BoutiquePurchaseItemQueryCondition query = new BoutiquePurchaseItemQueryCondition();
            query.setPurchaseOrderNo(purchaseOrderNo);
            query.setSku(sku);
            query.setQueryItem(true);
            List<BqPurchaseOrder> bqPurchaseOrders = boutiquePurchaseOrderService.queryWhPurchaseOrders(query, null);
            if (CollectionUtils.isEmpty(bqPurchaseOrders)
                    || CollectionUtils.isEmpty(bqPurchaseOrders.get(0).getJpCheckInList())) {
                return ApiResult.newError("没有找到采购单入库明细");
            }
            if (StringUtils.isNotEmpty(sku)) {
                List<String> skuList = CommonUtils.splitList(sku, ",");
                bqPurchaseOrders.get(0).getJpCheckInList().removeIf(item -> !skuList.contains(item.getSku()));
            }
            List<BoutiqueCheckIn> checkIns = bqPurchaseOrders.get(0).getJpCheckInList().stream()
                    .sorted((s1, s2) -> s2.getInId().compareTo(s1.getInId())).collect(toList());
            return ApiResult.newSuccess(checkIns);
        }
        catch (Exception e) {
            log.error(e.getMessage(), e);
            return ApiResult.newError(e.getMessage());
        }
    }

    private static String[] HEADERS = { "采购单号", "快递单号", "SKU数量", "SKU件数", "已收货件数", "入库仓库", "采购员", "采购时间", "入库完成时间",
            "采购单状态" };
    private static String[] HEADERS_ITEM = { "采购单号", "快递单号", "采购员", "采购时间", "入库完成时间", "采购单状态", "入库单号", "SKU", "SKU件数",
            "入库良品数", "入库不良品数", "入库仓库", "入库人", "入库时间" };

    /**
     * 导出
     */
    @RequestMapping(value = "download", method = { RequestMethod.POST })
    @ResponseBody
    public ApiResult download(@RequestBody BoutiquePurchaseItemQueryCondition query) {
        if (CollectionUtils.isNotEmpty(query.getIds())) {
            List<Integer> ids = query.getIds();
            boolean queryItem = query.isQueryItem();
            query = new BoutiquePurchaseItemQueryCondition();
            query.setQueryItem(queryItem);
            query.setIds(ids);
        }
        query.setReadOnly(true);
        String[] HEADER = HEADERS;
        String fileName = "精品采购单导出" + System.currentTimeMillis() + ".xlsx";
        if (query.isQueryItem()) {
            fileName = "精品采购单明细导出" + System.currentTimeMillis() + ".xlsx";
            HEADER = HEADERS_ITEM;
        }

        boolean isAll = whDownloadCenterService.checkFieldAllNull(query);
        Pager pager = new Pager();
        pager.setPageNo(-1);
        BoutiquePurchaseItemQueryCondition finalQuery = query;
        whDownloadCenterService.downloading(fileName, HEADER, WhDownloadContentEnum.BOUTIQUE_PURCHASE_ORDER, isAll,
                pager, (page) -> {
                    List<BqPurchaseOrder> bqPurchaseOrders = boutiquePurchaseOrderService
                            .queryWhPurchaseOrders(finalQuery, null);
                    if (finalQuery.isQueryItem()) {
                        return this.getItemExportList(bqPurchaseOrders);
                    }
                    else {
                        return this.getExportList(bqPurchaseOrders);
                    }
                });
        return ApiResult.newSuccess("导出任务已经创建，请到下载中心查看结果");
    }

    private List<List<String>> getExportList(List<BqPurchaseOrder> orderList) {
        List<List<String>> data = new ArrayList<List<String>>();
        if (CollectionUtils.isEmpty(orderList)) {
            return data;
        }
        for (BqPurchaseOrder order : orderList) {
            List<String> orderlist = new ArrayList<String>(HEADERS.length);
            orderlist.add(POIUtils.transferObj2Str(order.getPurchaseOrderNo()));
            orderlist.add(POIUtils.transferObj2Str(order.getExpressId()));
            orderlist.add(POIUtils.transferObj2Str(order.getSkuSize()));
            orderlist.add(POIUtils.transferObj2Str(order.getSkuQty()));
            orderlist.add(POIUtils.transferObj2Str(order.getCheckInQty()));
            orderlist.add(POIUtils.transferObj2Str(order.getPurposeHouse()));
            orderlist.add(POIUtils.transferObj2Str(order.getPurchaseUserName()));
            orderlist.add(POIUtils.transferObj2Str(order.getPurchaseDate()));
            orderlist.add(POIUtils.transferObj2Str(order.getFinishTime()));
            orderlist.add(POIUtils.transferObj2Str(order.getStatusName()));
            data.add(orderlist);
        }
        return data;
    }

    private List<List<String>> getItemExportList(List<BqPurchaseOrder> orderList) {
        List<List<String>> data = new ArrayList<List<String>>();
        if (CollectionUtils.isEmpty(orderList)) {
            return data;
        }
        for (BqPurchaseOrder order : orderList) {
            if (CollectionUtils.isEmpty(order.getJpItems()))
                continue;
            for (BoutiquePurchaseItem item : order.getJpItems()) {
                if (CollectionUtils.isEmpty(item.getCheckInList()))
                    continue;
                for (BoutiqueCheckIn checkIn : item.getCheckInList()) {
                    List<String> orderlist = new ArrayList<String>(HEADERS_ITEM.length);
                    orderlist.add(POIUtils.transferObj2Str(order.getPurchaseOrderNo()));
                    orderlist.add(POIUtils.transferObj2Str(order.getExpressId()));
                    orderlist.add(POIUtils.transferObj2Str(order.getPurchaseUserName()));
                    orderlist.add(POIUtils.transferObj2Str(order.getPurchaseDate()));
                    orderlist.add(POIUtils.transferObj2Str(order.getFinishTime()));
                    orderlist.add(POIUtils.transferObj2Str(order.getStatusName()));
                    orderlist.add(POIUtils.transferObj2Str(checkIn.getInId()));
                    orderlist.add(POIUtils.transferObj2Str(item.getSku()));
                    orderlist.add(POIUtils.transferObj2Str(order.getSkuSize()));
                    orderlist.add(POIUtils.transferObj2Str(checkIn.getGpQty()));
                    orderlist.add(POIUtils.transferObj2Str(checkIn.getBadQty()));
                    orderlist.add(POIUtils.transferObj2Str(checkIn.getPurposeHouse()));
                    orderlist.add(
                            POIUtils.transferObj2Str(TaglibUtils.getEmployeeNameByUserId(checkIn.getCreateUser())));
                    orderlist.add(POIUtils.transferObj2Str(checkIn.getCreateDate()));
                    data.add(orderlist);
                }
            }
        }
        return data;
    }
}