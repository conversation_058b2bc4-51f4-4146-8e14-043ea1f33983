package com.estone.exquisite.service;

import com.estone.exquisite.bean.WhBoutiqueStockRelation;
import com.estone.exquisite.bean.WhBoutiqueStockRelationQueryCondition;
import com.whq.tool.component.Pager;
import java.util.List;

public interface WhBoutiqueStockRelationService {
    List<WhBoutiqueStockRelation> queryAllWhBoutiqueStockRelations();

    List<WhBoutiqueStockRelation> queryWhBoutiqueStockRelations(WhBoutiqueStockRelationQueryCondition query, Pager pager);

    WhBoutiqueStockRelation getWhBoutiqueStockRelation(Integer id);

    WhBoutiqueStockRelation getWhBoutiqueStockRelationDetail(Integer id);

    WhBoutiqueStockRelation queryWhBoutiqueStockRelation(WhBoutiqueStockRelationQueryCondition query);

    void createWhBoutiqueStockRelation(WhBoutiqueStockRelation whBoutiqueStockRelation);

    void batchCreateWhBoutiqueStockRelation(List<WhBoutiqueStockRelation> entityList);

    void deleteWhBoutiqueStockRelation(Integer id);

    void updateWhBoutiqueStockRelation(WhBoutiqueStockRelation whBoutiqueStockRelation);

    void batchUpdateWhBoutiqueStockRelation(List<WhBoutiqueStockRelation> entityList);
}