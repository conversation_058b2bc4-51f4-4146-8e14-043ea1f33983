package com.estone.exquisite.service.impl;

import com.estone.elasticsearch.model.ClothingBoutiqueDevProductEs;
import com.estone.elasticsearch.service.EsClothingBoutiqueDevProductService;
import com.estone.exquisite.bean.ClothingBoutiqueDevProductQueryCondition;
import com.estone.exquisite.service.BaseService;
import com.estone.exquisite.service.ClothingBoutiqueDevProductService;
import com.whq.tool.component.Pager;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 精品sku业务类
 */
@Service
@Slf4j
public class ClothingBoutiqueDevProductServiceImpl extends BaseService<ClothingBoutiqueDevProductQueryCondition,ClothingBoutiqueDevProductEs>
        implements ClothingBoutiqueDevProductService {

    @Resource
    private EsClothingBoutiqueDevProductService esClothingBoutiqueDevProductService;

    @Override
    public List<ClothingBoutiqueDevProductEs> list(ClothingBoutiqueDevProductQueryCondition search, Pager pager) {
        return esClothingBoutiqueDevProductService.page(search, pager);
    }

    @Override
    public ClothingBoutiqueDevProductEs getById(Integer id) {
        ClothingBoutiqueDevProductQueryCondition search = new ClothingBoutiqueDevProductQueryCondition();
        search.setId(id);
        List<ClothingBoutiqueDevProductEs> page = esClothingBoutiqueDevProductService.page(search, new Pager());
        if (CollectionUtils.isNotEmpty(page))
            return page.get(0);
        return null;
    }

}
