package com.estone.exquisite.service;

import java.util.List;
import java.util.Map;

import com.estone.exquisite.bean.BoutiqueStock;
import com.estone.exquisite.bean.BoutiqueStockQueryCondition;
import com.estone.system.rabbitmq.bean.AmqMessage;
import com.estone.transfer.bean.WhFbaAllocation;
import com.whq.tool.component.Pager;
import com.whq.tool.json.ResponseJson;

public interface BoutiqueStockService {
    List<BoutiqueStock> queryAllBoutiqueStocks();

    List<BoutiqueStock> queryBoutiqueStocks(BoutiqueStockQueryCondition query, Pager pager);

    BoutiqueStock getBoutiqueStock(Integer id);

    BoutiqueStock getBoutiqueStockDetail(Integer id);

    BoutiqueStock queryBoutiqueStock(BoutiqueStockQueryCondition query);

    void createBoutiqueStock(BoutiqueStock boutiqueStock);

    void batchCreateBoutiqueStock(List<BoutiqueStock> entityList);

    void deleteBoutiqueStock(Integer id);

    void updateBoutiqueStock(BoutiqueStock boutiqueStock);

    void batchUpdateBoutiqueStock(List<BoutiqueStock> entityList);

    void pushBoutiqueStockNewRedis(AmqMessage amqMessage, String setUrl, String getUrl);

    void generateStockChangeMag(Map<String, Integer> stockChangeMap);

    ResponseJson addBoutiqueSku(BoutiqueStock boutiqueStock);

    void doAllot(WhFbaAllocation whFbaAllocation);
}