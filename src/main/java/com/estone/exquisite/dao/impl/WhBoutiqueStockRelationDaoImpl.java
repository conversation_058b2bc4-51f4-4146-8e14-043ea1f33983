package com.estone.exquisite.dao.impl;

import com.estone.exquisite.bean.WhBoutiqueStockRelation;
import com.estone.exquisite.bean.WhBoutiqueStockRelationQueryCondition;
import com.estone.exquisite.dao.WhBoutiqueStockRelationDao;
import com.estone.exquisite.dao.mapper.WhBoutiqueStockRelationDBField;
import com.estone.exquisite.dao.mapper.WhBoutiqueStockRelationMapper;
import com.whq.tool.component.Pager;
import com.whq.tool.context.DataContextHolder;
import com.whq.tool.sqler.SqlerRequest;
import com.whq.tool.sqler.analyzer.DataType;
import com.estone.common.util.SqlerTemplate;
import java.sql.Timestamp;
import java.util.Arrays;
import java.util.List;

import jodd.util.StringUtil;
import org.springframework.stereotype.Repository;
import org.springframework.util.Assert;

@Repository("whBoutiqueStockRelationDao")
public class WhBoutiqueStockRelationDaoImpl implements WhBoutiqueStockRelationDao {

    private void setQueryCondition(SqlerRequest request, WhBoutiqueStockRelationQueryCondition query) {
        if (query == null) {
            return;
        }
        // 添加只读权限
        if (query.getReadOnly() != null && query.getReadOnly())
        {
            request.setReadOnly(true);
        }
        // 添加查询条件
        // 搜索条件不允许出现空字符
        request.setAllowBlank(false);
        
        request.addDataParam(WhBoutiqueStockRelationDBField.ID, DataType.INT, query.getId());
        request.addDataParam(WhBoutiqueStockRelationDBField.SKU, DataType.STRING, query.getSku());
        request.addDataParam(WhBoutiqueStockRelationDBField.SELL_SKU, DataType.STRING, query.getSellSku());
        request.addDataParam(WhBoutiqueStockRelationDBField.FN_SKU, DataType.STRING, query.getFnSku());
        request.addDataParam(WhBoutiqueStockRelationDBField.MATERIAL_ARTICLE_NUMBER, DataType.STRING, query.getMaterialArticleNumber());
        request.addDataParam(WhBoutiqueStockRelationDBField.STORE, DataType.STRING, query.getStore());
        request.addDataParam("ids", DataType.INT, query.getIds());
        if (StringUtil.isNotBlank(query.getSkuStr())) {
            request.addDataParam("skuList", DataType.STRING, Arrays.asList(StringUtil.split(query.getSkuStr(),",")));
        }
        if (StringUtil.isNotBlank(query.getSellSkuStr())) {
            request.addDataParam("sellSkuList", DataType.STRING, Arrays.asList(StringUtil.split(query.getSellSkuStr(),",")));
        }
        if (StringUtil.isNotBlank(query.getFnSkuStr())) {
            request.addDataParam("fnSkuList", DataType.STRING, Arrays.asList(StringUtil.split(query.getFnSkuStr(),",")));
        }


    }

    @Override
    public int queryWhBoutiqueStockRelationCount(WhBoutiqueStockRelationQueryCondition query) {
        SqlerRequest request = new SqlerRequest("queryWhBoutiqueStockRelationCount");
        setQueryCondition(request, query);
        return SqlerTemplate.queryForInt(request);
    }

    @Override
    public List<WhBoutiqueStockRelation> queryWhBoutiqueStockRelationList() {
        SqlerRequest request = new SqlerRequest("queryWhBoutiqueStockRelationList");
        return SqlerTemplate.query(request, new WhBoutiqueStockRelationMapper());
    }

    @Override
    public List<WhBoutiqueStockRelation> queryWhBoutiqueStockRelationList(WhBoutiqueStockRelationQueryCondition query, Pager pager) {
        SqlerRequest request = new SqlerRequest("queryWhBoutiqueStockRelationList");
        setQueryCondition(request, query);
        if(pager != null) {
            request.addFetch(pager.getPageNo(), pager.getPageSize());
        }
        return SqlerTemplate.query(request, new WhBoutiqueStockRelationMapper());
    }

    @Override
    public WhBoutiqueStockRelation queryWhBoutiqueStockRelation(Integer primaryKey) {
        Assert.notNull(primaryKey, "primaryKey is null!");
        SqlerRequest request = new SqlerRequest("queryWhBoutiqueStockRelationByPrimaryKey");
        request.addDataParam(WhBoutiqueStockRelationDBField.ID, DataType.INT, primaryKey);
        return SqlerTemplate.queryForObject(request, new WhBoutiqueStockRelationMapper());
    }

    @Override
    public WhBoutiqueStockRelation queryWhBoutiqueStockRelation(WhBoutiqueStockRelationQueryCondition query) {
        SqlerRequest request = new SqlerRequest("queryWhBoutiqueStockRelation");
        setQueryCondition(request, query);
        return SqlerTemplate.queryForObject(request, new WhBoutiqueStockRelationMapper());
    }

    @Override
    public void createWhBoutiqueStockRelation(WhBoutiqueStockRelation entity) {
        SqlerRequest request = new SqlerRequest("createWhBoutiqueStockRelation");
        request.addDataParam(WhBoutiqueStockRelationDBField.SKU, DataType.STRING, entity.getSku());
        request.addDataParam(WhBoutiqueStockRelationDBField.SELL_SKU, DataType.STRING, entity.getSellSku());
        request.addDataParam(WhBoutiqueStockRelationDBField.FN_SKU, DataType.STRING, entity.getFnSku());
        request.addDataParam(WhBoutiqueStockRelationDBField.STORE, DataType.STRING, entity.getStore());
        request.addDataParam(WhBoutiqueStockRelationDBField.NAME, DataType.STRING, entity.getName());
        request.addDataParam(WhBoutiqueStockRelationDBField.MATERIAL_ARTICLE_NUMBER, DataType.STRING, entity.getMaterialArticleNumber());
        Integer autoIncrementId = SqlerTemplate.executeAndReturn(request);
        entity.setId(autoIncrementId);
    }

    @Override
    public void updateWhBoutiqueStockRelation(WhBoutiqueStockRelation entity) {
        SqlerRequest request = new SqlerRequest("updateWhBoutiqueStockRelationByPrimaryKey");
        request.addDataParam(WhBoutiqueStockRelationDBField.ID, DataType.INT, entity.getId());
        
        request.addDataParam(WhBoutiqueStockRelationDBField.SKU, DataType.STRING, entity.getSku());
        request.addDataParam(WhBoutiqueStockRelationDBField.SELL_SKU, DataType.STRING, entity.getSellSku());
        request.addDataParam(WhBoutiqueStockRelationDBField.FN_SKU, DataType.STRING, entity.getFnSku());
        request.addDataParam(WhBoutiqueStockRelationDBField.STORE, DataType.STRING, entity.getStore());
        request.addDataParam(WhBoutiqueStockRelationDBField.NAME, DataType.STRING, entity.getName());
        request.addDataParam(WhBoutiqueStockRelationDBField.MATERIAL_ARTICLE_NUMBER, DataType.STRING, entity.getMaterialArticleNumber());
        SqlerTemplate.execute(request);
    }

    @Override
    public void batchCreateWhBoutiqueStockRelation(List<WhBoutiqueStockRelation> entityList) {
        if (entityList != null && !entityList.isEmpty()) {
            SqlerRequest request = new SqlerRequest("createWhBoutiqueStockRelation");
            for (WhBoutiqueStockRelation entity : entityList) {
                request.addBatchDataParam(WhBoutiqueStockRelationDBField.SKU, DataType.STRING, entity.getSku());
                request.addBatchDataParam(WhBoutiqueStockRelationDBField.SELL_SKU, DataType.STRING, entity.getSellSku());
                request.addBatchDataParam(WhBoutiqueStockRelationDBField.FN_SKU, DataType.STRING, entity.getFnSku());
                request.addBatchDataParam(WhBoutiqueStockRelationDBField.STORE, DataType.STRING, entity.getStore());
                request.addBatchDataParam(WhBoutiqueStockRelationDBField.NAME, DataType.STRING, entity.getName());
                request.addBatchDataParam(WhBoutiqueStockRelationDBField.MATERIAL_ARTICLE_NUMBER, DataType.STRING, entity.getMaterialArticleNumber());
                request.addBatch();
            }
            SqlerTemplate.batchUpdate(request);
        }
    }

    @Override
    public void batchUpdateWhBoutiqueStockRelation(List<WhBoutiqueStockRelation> entityList) {
        if (entityList != null && !entityList.isEmpty()) {
            SqlerRequest request = new SqlerRequest("updateWhBoutiqueStockRelationByPrimaryKey");
            for (WhBoutiqueStockRelation entity : entityList) {
                request.addBatchDataParam(WhBoutiqueStockRelationDBField.ID, DataType.INT, entity.getId());
                
                request.addBatchDataParam(WhBoutiqueStockRelationDBField.SKU, DataType.STRING, entity.getSku());
                request.addBatchDataParam(WhBoutiqueStockRelationDBField.SELL_SKU, DataType.STRING, entity.getSellSku());
                request.addBatchDataParam(WhBoutiqueStockRelationDBField.FN_SKU, DataType.STRING, entity.getFnSku());
                request.addBatchDataParam(WhBoutiqueStockRelationDBField.STORE, DataType.STRING, entity.getStore());
                request.addBatchDataParam(WhBoutiqueStockRelationDBField.NAME, DataType.STRING, entity.getName());
                request.addBatchDataParam(WhBoutiqueStockRelationDBField.MATERIAL_ARTICLE_NUMBER, DataType.STRING, entity.getMaterialArticleNumber());
                request.addBatch();
            }
            SqlerTemplate.batchUpdate(request);
        }
    }

    @Override
    public void deleteWhBoutiqueStockRelation(Integer primaryKey) {
        SqlerRequest request = new SqlerRequest("deleteWhBoutiqueStockRelationByPrimaryKey");
        request.addDataParam(WhBoutiqueStockRelationDBField.ID, DataType.INT, primaryKey);
        SqlerTemplate.execute(request);
    }
}