package com.estone.exquisite.dao;

import java.util.List;

import com.estone.exquisite.bean.BoutiqueStockLog;
import com.estone.exquisite.bean.BoutiqueStockLogQueryCondition;
import com.whq.tool.component.Pager;

public interface BoutiqueStockLogDao {
    int queryBoutiqueStockLogCount(BoutiqueStockLogQueryCondition query);

    List<BoutiqueStockLog> queryBoutiqueStockLogList();

    List<BoutiqueStockLog> queryBoutiqueStockLogList(BoutiqueStockLogQueryCondition query, Pager pager);

    BoutiqueStockLog queryBoutiqueStockLog(Integer primaryKey);

    BoutiqueStockLog queryBoutiqueStockLog(BoutiqueStockLogQueryCondition query);

    void createBoutiqueStockLog(BoutiqueStockLog entity);

    void batchCreateBoutiqueStockLog(List<BoutiqueStockLog> entityList);

    void batchUpdateBoutiqueStockLog(List<BoutiqueStockLog> entityList);

    void deleteBoutiqueStockLog(Integer primaryKey);

    void updateBoutiqueStockLog(BoutiqueStockLog entity);

    BoutiqueStockLog queryLastBqStockLogByCurrentLog(BoutiqueStockLog stockLog);
}