package com.estone.exquisite.domain;

import com.estone.exquisite.bean.BoutiqueStockLog;
import com.estone.exquisite.bean.BoutiqueStockLogQueryCondition;
import com.whq.tool.component.Pager;
import java.util.ArrayList;
import java.util.List;

public class BoutiqueStockLogDo {
    private BoutiqueStockLog boutiqueStockLog;

    private BoutiqueStockLogQueryCondition query;

    private List<BoutiqueStockLog> boutiqueStockLogs = new ArrayList<BoutiqueStockLog>();

    private Pager page = new Pager();

    public BoutiqueStockLog getBoutiqueStockLog() {
        return boutiqueStockLog;
    }

    public void setBoutiqueStockLog(BoutiqueStockLog boutiqueStockLog) {
        this.boutiqueStockLog = boutiqueStockLog;
    }

    public BoutiqueStockLogQueryCondition getQuery() {
        return query;
    }

    public void setQuery(BoutiqueStockLogQueryCondition query) {
        this.query = query;
    }

    public List<BoutiqueStockLog> getBoutiqueStockLogs() {
        return boutiqueStockLogs;
    }

    public void setBoutiqueStockLogs(List<BoutiqueStockLog> boutiqueStockLogs) {
        this.boutiqueStockLogs = boutiqueStockLogs;
    }

    public Pager getPage() {
        return page;
    }

    public void setPage(Pager page) {
        this.page = page;
    }
}