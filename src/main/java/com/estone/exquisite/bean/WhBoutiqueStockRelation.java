package com.estone.exquisite.bean;

import java.io.Serializable;
import lombok.Data;

@Data
public class WhBoutiqueStockRelation implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键id database column wh_boutique_stock_relation.id
     */
    private Integer id;

    /**
     * sku database column wh_boutique_stock_relation.sku
     */
    private String sku;

    /**
     * SellSKU database column wh_boutique_stock_relation.sell_sku
     */
    private String sellSku;

    /**
     * FNSKU database column wh_boutique_stock_relation.fn_sku
     */
    private String fnSku;

    /**
     * 店铺 database column wh_boutique_stock_relation.store
     */
    private String store;

    /**
     * 产品名称 database column wh_boutique_stock_relation.name
     */
    private String name;

    /**
     * 耗材 database column wh_boutique_stock_relation.material_article_number
     */
    private String materialArticleNumber;
}