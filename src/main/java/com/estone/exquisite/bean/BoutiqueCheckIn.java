package com.estone.exquisite.bean;

import java.io.Serializable;
import java.sql.Timestamp;

import com.estone.common.util.TaglibUtils;
import lombok.Data;

@Data
public class BoutiqueCheckIn implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 入库单号 database column boutique_check_in.in_id
     */
    private Integer inId;

    /**
     * 退换货单号 database column boutique_check_in.purchase_order_no
     */
    private String purchaseOrderNo;

    /**
     * sku database column boutique_check_in.sku
     */
    private String sku;

    /**
     * 采购数量 database column boutique_check_in.quantity
     */
    private Integer quantity;

    /**
     * 仓库 database column boutique_check_in.purpose_house
     */
    private String purposeHouse;

    /**
     * 入库良品数 database column boutique_check_in.gp_qty
     */
    private Integer gpQty;

    /**
     * 入库不良品数 database column boutique_check_in.bad_qty
     */
    private Integer badQty;

    /**
     * 创建时间 database column boutique_check_in.create_date
     */
    private Timestamp createDate;

    /**
     * 创建人 database column boutique_check_in.create_user
     */
    private Integer createUser;

    /**
     * 采购上架是否成功 database column boutique_check_in.is_purchase_stock_in
     */
    private Boolean isPurchaseStockIn;

    /**
     * 加库存是否成功 database column boutique_check_in.is_in_stock
     */
    private Boolean isInStock;
    
    public String getCreateUserName() {
        return TaglibUtils.getEmployeeNameByUserId(createUser);
    }
}