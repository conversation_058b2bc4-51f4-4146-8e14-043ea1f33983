package com.estone.sku.action;

import com.estone.sku.bean.WhSkuExpandAttr;
import com.estone.sku.bean.WhSkuExpandAttrQueryCondition;
import com.estone.sku.domain.WhSkuExpandAttrDo;
import com.estone.sku.service.WhSkuExpandAttrService;
import com.whq.tool.action.BaseController;
import com.whq.tool.component.Pager;
import java.util.List;
import javax.annotation.Resource;

import com.whq.tool.json.ResponseJson;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping(value = "whSkuExpandAttr")
public class WhSkuExpandAttrController extends BaseController {
    @Resource
    private WhSkuExpandAttrService whSkuExpandAttrService;

    @RequestMapping(method = {RequestMethod.GET})
    public String init(@ModelAttribute("domain") WhSkuExpandAttrDo domain) {
        return "{模块}/{页面}";
    }

    private void initFormData(@ModelAttribute("domain") WhSkuExpandAttrDo domain) {
         
    }

    private void queryWhSkuExpandAttrs(@ModelAttribute("domain") WhSkuExpandAttrDo domain) {
        WhSkuExpandAttrQueryCondition query = domain.getQuery();
        Pager page = domain.getPage();
        if (query == null)
        {
            query = new WhSkuExpandAttrQueryCondition();
            domain.setQuery(query);
        }
        List<WhSkuExpandAttr> whSkuExpandAttrs = whSkuExpandAttrService.queryWhSkuExpandAttrs(query, page);
        domain.setWhSkuExpandAttrs(whSkuExpandAttrs);
    }

    @RequestMapping(value="search", method = {RequestMethod.POST})
    public String search(@ModelAttribute("domain") WhSkuExpandAttrDo domain) {
        initFormData(domain);
        queryWhSkuExpandAttrs(domain);
        return "{模块}/{页面}";
    }

    @RequestMapping(value="create", method = {RequestMethod.GET})
    public String toCreateWhSkuExpandAttr(@ModelAttribute("domain") WhSkuExpandAttrDo domain) {
        return "{模块}/{页面}";
    }

    @RequestMapping(value="create", method = {RequestMethod.POST})
    public String createWhSkuExpandAttr(@ModelAttribute("domain") WhSkuExpandAttrDo domain) {
        WhSkuExpandAttr whSkuExpandAttr = domain.getWhSkuExpandAttr();
        whSkuExpandAttrService.createWhSkuExpandAttr(whSkuExpandAttr);
        return "redirect:/{查询列表}";
    }

    @RequestMapping(value="update", method = {RequestMethod.GET})
    public String toUpdateWhSkuExpandAttr(@ModelAttribute("domain") WhSkuExpandAttrDo domain, @RequestParam("whSkuExpandAttrId") Integer whSkuExpandAttrId) {
        WhSkuExpandAttr whSkuExpandAttr = whSkuExpandAttrService.getWhSkuExpandAttr(whSkuExpandAttrId);
        domain.setWhSkuExpandAttr(whSkuExpandAttr);
        return "{模块}/{页面}";
    }

    @RequestMapping(value="update", method = {RequestMethod.POST})
    public String updateWhSkuExpandAttr(@ModelAttribute("domain") WhSkuExpandAttrDo domain) {
        WhSkuExpandAttr whSkuExpandAttr = domain.getWhSkuExpandAttr();
        whSkuExpandAttrService.updateWhSkuExpandAttr(whSkuExpandAttr);
        return "redirect:/{查询列表}";
    }

    @RequestMapping(value="delete", method = {RequestMethod.GET})
    @ResponseBody
    public ResponseJson deleteWhSkuExpandAttr(@ModelAttribute("domain") WhSkuExpandAttrDo domain, @RequestParam("whSkuExpandAttrId") Integer whSkuExpandAttrId) {
        ResponseJson response = new ResponseJson();
        whSkuExpandAttrService.deleteWhSkuExpandAttr(whSkuExpandAttrId);
        return response;
    }
}