package com.estone.sku.action;

import com.estone.common.util.SystemLogUtils;
import com.estone.sku.bean.WhBarCodeSku;
import com.estone.sku.bean.WhBarCodeSkuQueryCondition;
import com.estone.sku.bean.WhSku;
import com.estone.sku.domain.WhBarCodeSkuDo;
import com.estone.sku.service.WhBarCodeSkuService;
import com.estone.sku.service.WhSkuService;
import com.whq.tool.action.BaseController;
import com.whq.tool.component.Pager;
import com.whq.tool.component.StatusCode;
import com.whq.tool.json.ResponseJson;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.util.List;

@Controller
@Slf4j
@RequestMapping(value = "barCodeSku")
public class WhBarCodeSkuController extends BaseController {
    @Resource
    private WhBarCodeSkuService whBarCodeSkuService;

    @Resource
    private WhSkuService whSkuService;

    @RequestMapping(method = { RequestMethod.GET })
    public String init(@ModelAttribute("domain") WhBarCodeSkuDo domain) {
        return "{模块}/{页面}";
    }

    private void initFormData(@ModelAttribute("domain") WhBarCodeSkuDo domain) {

    }

    private void queryWhBarCodeSkus(@ModelAttribute("domain") WhBarCodeSkuDo domain) {
        WhBarCodeSkuQueryCondition query = domain.getQuery();
        Pager page = domain.getPage();
        if (query == null) {
            query = new WhBarCodeSkuQueryCondition();
            domain.setQuery(query);
        }
        List<WhBarCodeSku> whBarCodeSkus = whBarCodeSkuService.queryWhBarCodeSkus(query, page);
        domain.setWhBarCodeSkus(whBarCodeSkus);
    }

    @RequestMapping(value = "search", method = { RequestMethod.POST })
    public String search(@ModelAttribute("domain") WhBarCodeSkuDo domain) {
        initFormData(domain);
        queryWhBarCodeSkus(domain);
        return "{模块}/{页面}";
    }

    @RequestMapping(value = "create", method = { RequestMethod.GET })
    public String toCreateWhBarCodeSku(@ModelAttribute("domain") WhBarCodeSkuDo domain) {
        return "{模块}/{页面}";
    }

    @RequestMapping(value = "create", method = { RequestMethod.POST })
    public String createWhBarCodeSku(@ModelAttribute("domain") WhBarCodeSkuDo domain) {
        WhBarCodeSku whBarCodeSku = domain.getWhBarCodeSku();
        whBarCodeSkuService.createWhBarCodeSku(whBarCodeSku);
        return "redirect:/{查询列表}";
    }

    @RequestMapping(value = "update", method = { RequestMethod.GET })
    public String toUpdateWhBarCodeSku(@ModelAttribute("domain") WhBarCodeSkuDo domain,
            @RequestParam("whBarCodeSkuId") Integer whBarCodeSkuId) {
        WhBarCodeSku whBarCodeSku = whBarCodeSkuService.getWhBarCodeSku(whBarCodeSkuId);
        domain.setWhBarCodeSku(whBarCodeSku);
        return "{模块}/{页面}";
    }

    @RequestMapping(value = "update", method = { RequestMethod.POST })
    public String updateWhBarCodeSku(@ModelAttribute("domain") WhBarCodeSkuDo domain) {
        WhBarCodeSku whBarCodeSku = domain.getWhBarCodeSku();
        whBarCodeSkuService.updateWhBarCodeSku(whBarCodeSku);
        return "redirect:/{查询列表}";
    }

    @RequestMapping(value = "delete", method = { RequestMethod.GET })
    @ResponseBody
    public ResponseJson deleteWhBarCodeSku(@ModelAttribute("domain") WhBarCodeSkuDo domain,
            @RequestParam("whBarCodeSkuId") Integer whBarCodeSkuId) {
        ResponseJson response = new ResponseJson();
        whBarCodeSkuService.deleteWhBarCodeSku(whBarCodeSkuId);
        return response;
    }

    /**
     * 编辑更新SKU编码
     *
     * @param sku
     * @param barCode
     * @param skuId
     * @return
     */
    @RequestMapping(value = "updateSkuBarCode", method = RequestMethod.POST)
    @ResponseBody
    public ResponseJson updateSkuBarCode(@RequestParam("sku") String sku, @RequestParam("barCode") String barCode,
            @RequestParam("skuId") Integer skuId) {
        log.info("barCodeSku/updateSkuBarCode sku= " + sku + " barCode = " + barCode);
        ResponseJson response = new ResponseJson(StatusCode.FAIL);
        if (skuId == null || StringUtils.isBlank(sku) || StringUtils.isBlank(barCode)) {
            response.setMessage("参数为空");
            return response;
        }
        WhBarCodeSkuQueryCondition queryCondition = new WhBarCodeSkuQueryCondition();
        queryCondition.setSkuBarCode(barCode);
        queryCondition.setSku(sku);
        queryCondition.setQueryBySkuOrCode(true);
        List<WhBarCodeSku> dbWhBarCodeSkuList = whBarCodeSkuService.queryWhBarCodeSkusBySkuOrBarCode(queryCondition);

        WhBarCodeSku barCodeSku = new WhBarCodeSku();
        barCodeSku.setSku(sku);
        barCodeSku.setSkuBarCode(barCode);

        String beforeBarCode = "";
        if (CollectionUtils.isNotEmpty(dbWhBarCodeSkuList)) {
            for (WhBarCodeSku whBarCodeSku : dbWhBarCodeSkuList) {
                if (barCode.equalsIgnoreCase(whBarCodeSku.getSkuBarCode())) {
                    response.setMessage("编码：" + barCode + " 已存在，SKU【" + whBarCodeSku.getSku() + "】");
                    return response;
                }
                if (sku.equals(whBarCodeSku.getSku())) {
                    barCodeSku.setId(whBarCodeSku.getId());
                    beforeBarCode = whBarCodeSku.getSkuBarCode();
                    continue;
                }
            }
        }

        if (barCodeSku.getId() == null) {
            whBarCodeSkuService.createWhBarCodeSku(barCodeSku);
        }
        else {
            whBarCodeSkuService.updateWhBarCodeSku(barCodeSku);
        }

        WhSku updateSku = new WhSku();
        updateSku.setId(skuId);
        updateSku.setCompletionDate(new Timestamp(System.currentTimeMillis()));
        whSkuService.updateWhSku(updateSku);

        SystemLogUtils.SKULOG.log(skuId, "SKU编码变更",
                new String[][] { { "修改前", beforeBarCode }, { "修改后", barCode } });
        response.setStatus(StatusCode.SUCCESS);
        return response;
    }

    /**
     * 根据SKU编码获取sku
     *
     * @param barCode
     * @return
     */
    @RequestMapping(value = "getSkuByBarCode", method = RequestMethod.GET)
    @ResponseBody
    public String getSkuByBarCode(@RequestParam("barCode") String barCode) {
        log.info("barCodeSku/getSkuByBarCode barCode= " + barCode);
        return barCode;
        /*WhBarCodeSkuQueryCondition query = new WhBarCodeSkuQueryCondition();
        query.setSkuBarCode(barCode);
        WhBarCodeSku whBarCodeSku = whBarCodeSkuService.queryWhBarCodeSku(query);
        if(whBarCodeSku == null){
            return barCode;
        }else {
            return whBarCodeSku.getSku();
        }*/
    }
}