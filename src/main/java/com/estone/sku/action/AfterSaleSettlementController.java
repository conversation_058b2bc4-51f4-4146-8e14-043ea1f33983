package com.estone.sku.action;

import java.io.OutputStream;
import java.util.ArrayList;
import java.util.List;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.io.IOUtils;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.estone.checkin.enums.CheckInWhType;
import com.estone.common.SelectJson;
import com.estone.common.enums.ExportType;
import com.estone.common.util.POIUtils;
import com.estone.sku.bean.AfterSaleSettlement;
import com.estone.sku.bean.AfterSaleSettlementQueryCondition;
import com.estone.sku.domain.AfterSaleSettlementDo;
import com.estone.sku.enums.SalePropertyEnums;
import com.estone.sku.enums.WhSkuStatus;
import com.estone.sku.service.AfterSaleSettlementService;
import com.estone.warehouse.service.WhLocationService;
import com.whq.tool.action.BaseController;
import com.whq.tool.component.Pager;

@Slf4j
@Controller
@RequestMapping(value = "afterSale")
public class AfterSaleSettlementController extends BaseController {
    @Resource
    private AfterSaleSettlementService afterSaleSettlementService;
    @Resource
    private WhLocationService whLocationService;

    @RequestMapping(method = {RequestMethod.GET})
    public String init(@ModelAttribute("domain") AfterSaleSettlementDo domain) {
        initFormData(domain);
        return "sku/afterSaleSettlementList";
    }

    private void initFormData(@ModelAttribute("domain") AfterSaleSettlementDo domain) {
        domain.setSalesPropertys(SelectJson.getListById(SalePropertyEnums.values()));//销售属性
        List<String> locationList = whLocationService.selectRegion(null);
        JSONArray arrayRegion = new JSONArray();
        for (String str : locationList) {
            JSONObject obj = new JSONObject();
            obj.put("id", str);
            obj.put("text", str);
            arrayRegion.add(obj);
        }
        domain.setLocationRegionList(arrayRegion.toJSONString());
    }

    private void queryAfterSaleSettlements(@ModelAttribute("domain") AfterSaleSettlementDo domain) {
        AfterSaleSettlementQueryCondition query = domain.getQuery();
        Pager page = domain.getPage();
        if (query == null)
        {
            query = new AfterSaleSettlementQueryCondition();
            domain.setQuery(query);
        }
        // 默认读从库
        query.setReadOnly(true);
        List<AfterSaleSettlement> afterSaleSettlements = afterSaleSettlementService.queryAfterSaleSettlements(query,
                page);
        domain.setAfterSaleSettlements(afterSaleSettlements);
        if (query.getIsTotal() != null && query.getIsTotal()) {
            domain.setAfterSaleSettlement(afterSaleSettlementService.queryAfterSaleSettlementGroup(query));
        }
    }

    @RequestMapping(value="search", method = {RequestMethod.POST})
    public String search(@ModelAttribute("domain") AfterSaleSettlementDo domain) {
        initFormData(domain);
        queryAfterSaleSettlements(domain);
        return "sku/afterSaleSettlementList";
    }

    private String[] headers = new String[] { "编号", "sku", "商品名称", "供应商", "单品状态", "仓库销售属性", "销售频次", "动销频次", "仓库", "库存ID",
            "在库库存", "外借在途", "采购入库", "存货变动", "盘亏", "订单出库", "调拨出库", "组装出库", "报废出库", "不良品出库", "外借出库", "退货出库" };

    @ResponseBody
    @RequestMapping(value = "export", method = { RequestMethod.POST })
    public void export(@ModelAttribute("domain") AfterSaleSettlementDo domain,
            @RequestParam("exportType") String exportType, HttpServletResponse response) {
        AfterSaleSettlementQueryCondition query = domain.getQuery();

        ExportType exportTypeEnum = ExportType.build(exportType);

        OutputStream os = null;
        Pager pager = null;
        try {
            // 查询导出的数据
            switch (exportTypeEnum) {
                case ALL:
                    query.setIds(null);
                    break;
                case PAGE:
                    pager = domain.getPage();
                    break;
                case CHECKED:
                    if (CollectionUtils.isEmpty(query.getIds())) {
                        os = response.getOutputStream();
                        os.write("请选择导出的SKU！".getBytes("GBK"));
                        return;
                    }
                    List<Integer> ids = query.getIds();
                    query = new AfterSaleSettlementQueryCondition();
                    query.setIds(ids);
                    break;
            }

            List<AfterSaleSettlement> dataList = afterSaleSettlementService.queryAfterSaleSettlements(query, pager);

            String fileName = "售后结算管理" + System.currentTimeMillis() + ".xlsx";
            response.setContentType("application/ms-excel");
            response.setHeader("Content-Disposition",
                    "attachment; filename=" + java.net.URLEncoder.encode(fileName, "UTF-8"));
            os = response.getOutputStream();
            final List<List<String>> recordsData = new ArrayList<List<String>>();
            POIUtils.createExcel(headers, dataList, afterSale -> {
                recordsData.clear();
                List<String> downloadList = new ArrayList<String>(headers.length);
                downloadList.add(POIUtils.transferObj2Str(afterSale.getId()));
                downloadList.add(POIUtils.transferObj2Str(afterSale.getSku()));
                downloadList.add(POIUtils.transferObj2Str(afterSale.getSkuName()));
                downloadList.add(POIUtils.transferObj2Str(afterSale.getVendorName()));
                downloadList.add(
                        POIUtils.transferObj2Str(WhSkuStatus.getNameByCode(String.valueOf(afterSale.getSkuStatus()))));

                downloadList.add(POIUtils.transferObj2Str(afterSale.getSaleAttributeSettingStr()));
                downloadList.add(POIUtils.transferObj2Str(afterSale.getThirtyDaysSalesOrders()));
                downloadList.add(POIUtils.transferObj2Str(afterSale.getThirtyDaysSalesDays()));
                downloadList.add(POIUtils
                        .transferObj2Str(CheckInWhType.getNameByCode(String.valueOf(afterSale.getCheckInType()))));
                downloadList.add(POIUtils.transferObj2Str(afterSale.getStockId()));
                downloadList.add(POIUtils.transferObj2Str(afterSale.getQuantity()));
                downloadList.add(POIUtils.transferObj2Str(afterSale.getLendOnWayQty()));
                downloadList.add(POIUtils.transferObj2Str(afterSale.getCheckInQty()));
                downloadList.add(POIUtils.transferObj2Str(afterSale.getPrestorageQty()));
                downloadList.add(POIUtils.transferObj2Str(afterSale.getDecrQty()));
                downloadList.add(POIUtils.transferObj2Str(afterSale.getDeliverQty()));
                downloadList.add(POIUtils.transferObj2Str(afterSale.getAllocationOutQty()));
                downloadList.add(POIUtils.transferObj2Str(afterSale.getCombineOutQty()));
                downloadList.add(POIUtils.transferObj2Str(afterSale.getScrapQty()));
                downloadList.add(POIUtils.transferObj2Str(afterSale.getBadProductQty()));
                downloadList.add(POIUtils.transferObj2Str(afterSale.getLendOutQty()));
                downloadList.add(POIUtils.transferObj2Str(afterSale.getRfoQty()));
                recordsData.add(downloadList);
                return recordsData;
            }, true, os);
        }
        catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        finally {
            IOUtils.closeQuietly(os);
        }
    }
}