package com.estone.sku.action;

import java.util.List;

import javax.annotation.Resource;

import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import com.estone.sku.bean.ExpManageItem;
import com.estone.sku.bean.ExpManageItemQueryCondition;
import com.estone.sku.domain.ExpManageItemDo;
import com.estone.sku.service.ExpManageItemService;
import com.whq.tool.action.BaseController;
import com.whq.tool.component.Pager;
import com.whq.tool.json.ResponseJson;

@Controller
@RequestMapping(value = "expManageItem")
public class ExpManageItemController extends BaseController {
    @Resource
    private ExpManageItemService expManageItemService;

    @RequestMapping(method = {RequestMethod.GET})
    public String init(@ModelAttribute("domain") ExpManageItemDo domain) {
        return "{模块}/{页面}";
    }

    private void initFormData(@ModelAttribute("domain") ExpManageItemDo domain) {
         
    }

    private void queryExpManageItems(@ModelAttribute("domain") ExpManageItemDo domain) {
        ExpManageItemQueryCondition query = domain.getQuery();
        Pager page = domain.getPage();
        if (query == null)
        {
            query = new ExpManageItemQueryCondition();
            domain.setQuery(query);
        }
        List<ExpManageItem> expManageItems = expManageItemService.queryExpManageItems(query, page);
        domain.setExpManageItems(expManageItems);
    }

    @RequestMapping(value="search", method = {RequestMethod.POST})
    public String search(@ModelAttribute("domain") ExpManageItemDo domain) {
        initFormData(domain);
        queryExpManageItems(domain);
        return "{模块}/{页面}";
    }

    @RequestMapping(value="create", method = {RequestMethod.GET})
    public String toCreateExpManageItem(@ModelAttribute("domain") ExpManageItemDo domain) {
        return "{模块}/{页面}";
    }

    @RequestMapping(value="create", method = {RequestMethod.POST})
    public String createExpManageItem(@ModelAttribute("domain") ExpManageItemDo domain) {
        ExpManageItem expManageItem = domain.getExpManageItem();
        expManageItemService.createExpManageItem(expManageItem);
        return "redirect:/{查询列表}";
    }

    @RequestMapping(value="update", method = {RequestMethod.GET})
    public String toUpdateExpManageItem(@ModelAttribute("domain") ExpManageItemDo domain, @RequestParam("expManageItemId") Integer expManageItemId) {
        ExpManageItem expManageItem = expManageItemService.getExpManageItem(expManageItemId);
        domain.setExpManageItem(expManageItem);
        return "{模块}/{页面}";
    }

    @RequestMapping(value="update", method = {RequestMethod.POST})
    public String updateExpManageItem(@ModelAttribute("domain") ExpManageItemDo domain) {
        ExpManageItem expManageItem = domain.getExpManageItem();
        expManageItemService.updateExpManageItem(expManageItem);
        return "redirect:/{查询列表}";
    }

    @RequestMapping(value="delete", method = {RequestMethod.GET})
    @ResponseBody
    public ResponseJson deleteExpManageItem(@ModelAttribute("domain") ExpManageItemDo domain, @RequestParam("expManageItemId") Integer expManageItemId) {
        ResponseJson response = new ResponseJson();
        expManageItemService.deleteExpManageItem(expManageItemId);
        return response;
    }
}