package com.estone.sku.action;

import com.estone.common.util.model.ApiResult;
import com.estone.common.util.model.CQuery;
import com.estone.common.util.model.CQueryResult;
import com.estone.sku.bean.MergeSku;
import com.estone.sku.bean.MergeSkuQueryCondition;
import com.estone.sku.domain.MergeSkuDO;
import com.estone.sku.service.MergeSkuService;
import com.estone.sku.service.impl.MergeSkuServiceImpl;
import com.whq.tool.component.Pager;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2023-12-12 14:43
 */
@Slf4j
@RestController
@RequestMapping("/mergeSku")
public class MergeSkuController {
    @Autowired
    private MergeSkuService mergeSkuService;


    @PostMapping("/search")
    public ApiResult<?> search(@RequestBody CQuery<MergeSkuQueryCondition> query) {
        CQueryResult<MergeSku> result = null;
        try {
            if (query == null) {
                return ApiResult.newError("param error");
            }
            if (query.getSearch() == null) {
                query.setSearch(new MergeSkuQueryCondition());
            }
            query.getSearch().setQueryDiscardSkuStocks(true);
            query.getSearch().setQueryMatchingSkuStocks(true);
            result = ((MergeSkuServiceImpl) mergeSkuService).list(query);
        }
        catch (Exception e) {
            log.error(e.getMessage(), e);
            return ApiResult.newError(e.getMessage());
        }
        return ApiResult.newSuccess(result);
    }


    @PostMapping("/download")
    public ApiResult<?> download(@RequestBody MergeSkuQueryCondition queryCondition) {
        queryCondition.setDownload(true);
        queryCondition.setQueryDiscardSkuStocks(true);
        Pager page = new Pager();
        return mergeSkuService.download(queryCondition, page);
    }

    @PostMapping("/noStockCompleted")
    public ApiResult<?> completed(@RequestBody CQuery<MergeSkuQueryCondition> query) {
        String tips = "";
        try {
            if (query == null) {
                return ApiResult.newError("param error");
            }
            if (query.getSearch() == null) {
                query.setSearch(new MergeSkuQueryCondition());
            }
            if (CollectionUtils.isEmpty(query.getSearch().getIds())){
                return ApiResult.newError("ID列表为空!");
            }
            List<Integer> successIds = mergeSkuService.noStockCompleted(query.getSearch().getIds());
            tips = "成功修改数据"+successIds.size()+"条";
        }
        catch (Exception e) {
            log.error(e.getMessage(), e);
            return ApiResult.newError(e.getMessage());
        }
        return ApiResult.newSuccess(tips);
    }


}
