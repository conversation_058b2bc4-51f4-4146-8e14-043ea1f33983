package com.estone.sku.dao;

import com.estone.sku.bean.WhSkuExpandAttr;
import com.estone.sku.bean.WhSkuExpandAttrQueryCondition;
import com.whq.tool.component.Pager;
import java.util.List;

public interface WhSkuExpandAttrDao {
    int queryWhSkuExpandAttrCount(WhSkuExpandAttrQueryCondition query);

    List<WhSkuExpandAttr> queryWhSkuExpandAttrList();

    List<WhSkuExpandAttr> queryWhSkuExpandAttrList(WhSkuExpandAttrQueryCondition query, Pager pager);

    WhSkuExpandAttr queryWhSkuExpandAttr(Integer primaryKey);

    WhSkuExpandAttr queryWhSkuExpandAttr(WhSkuExpandAttrQueryCondition query);

    void createWhSkuExpandAttr(WhSkuExpandAttr entity);

    void batchCreateWhSkuExpandAttr(List<WhSkuExpandAttr> entityList);

    void batchUpdateWhSkuExpandAttr(List<WhSkuExpandAttr> entityList);

    void deleteWhSkuExpandAttr(Integer primaryKey);

    void updateWhSkuExpandAttr(WhSkuExpandAttr entity);

    void deleteWhSkuExpandAttrBySku(String sku);

    void insertOrUpdateWhSkuExpandAttr(List<WhSkuExpandAttr> entityList);
}