package com.estone.sku.dao;

import com.estone.sku.bean.VerifySkuWeightTask;
import com.estone.sku.bean.VerifySkuWeightTaskQueryCondition;
import com.whq.tool.component.Pager;
import java.util.List;

public interface VerifySkuWeightTaskDao {
    int queryVerifySkuWeightTaskCount(VerifySkuWeightTaskQueryCondition query);

    List<VerifySkuWeightTask> queryVerifySkuWeightTaskList();

    List<VerifySkuWeightTask> queryVerifySkuWeightTaskList(VerifySkuWeightTaskQueryCondition query, Pager pager);

    VerifySkuWeightTask queryVerifySkuWeightTask(Integer primaryKey);

    VerifySkuWeightTask queryVerifySkuWeightTask(VerifySkuWeightTaskQueryCondition query);

    void createVerifySkuWeightTask(VerifySkuWeightTask entity);

    void batchCreateVerifySkuWeightTask(List<VerifySkuWeightTask> entityList);

    void batchUpdateVerifySkuWeightTask(List<VerifySkuWeightTask> entityList);

    void deleteVerifySkuWeightTask(Integer primaryKey);

    int updateVerifySkuWeightTask(VerifySkuWeightTask entity);

    int queryVerifySkuWeightTaskAndItemCount(VerifySkuWeightTaskQueryCondition query);

    List<VerifySkuWeightTask> queryVerifySkuWeightTaskAndItemList(VerifySkuWeightTaskQueryCondition query, Pager pager);

    Integer queryVerifySkuWeightTaskMaxLevel();
}