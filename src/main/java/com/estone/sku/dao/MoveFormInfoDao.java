package com.estone.sku.dao;

import com.estone.sku.bean.MoveFormInfo;
import com.estone.sku.bean.MoveFormInfoQueryCondition;
import com.estone.sku.bean.MoveFormQueryCondition;
import com.whq.tool.component.Pager;

import java.util.List;

public interface MoveFormInfoDao {
    int queryMoveFormInfoCount(MoveFormInfoQueryCondition query);

    List<MoveFormInfo> queryMoveFormInfoList();

    List<MoveFormInfo> queryMoveFormInfoList(MoveFormInfoQueryCondition query, Pager pager);

    MoveFormInfo queryMoveFormInfo(Integer primaryKey);

    MoveFormInfo queryMoveFormInfo(MoveFormInfoQueryCondition query);

    void createMoveFormInfo(MoveFormInfo entity);

    void batchCreateMoveFormInfo(List<MoveFormInfo> entityList);

    void batchUpdateMoveFormInfo(List<MoveFormInfo> entityList);

    void deleteMoveFormInfo(Integer primaryKey);

    void deleteMoveFormInfoList(List<Integer> primaryKeys);

    void updateMoveFormInfo(MoveFormInfo entity);

    int queryRelevanceMoveForm(MoveFormQueryCondition query);
}