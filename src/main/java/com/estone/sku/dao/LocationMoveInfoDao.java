package com.estone.sku.dao;

import java.util.List;

import com.estone.sku.bean.LocationMoveInfo;
import com.estone.sku.bean.LocationMoveInfoQueryCondition;
import com.whq.tool.component.Pager;

public interface LocationMoveInfoDao {
    /**
     * This method corresponds to the database table location_move_info
     *
     * @mbggenerated Mon Jan 07 11:20:46 CST 2019
     */
    int queryLocationMoveInfoCount(LocationMoveInfoQueryCondition query);

    /**
     * This method corresponds to the database table location_move_info
     *
     * @mbggenerated Mon Jan 07 11:20:46 CST 2019
     */
    List<LocationMoveInfo> queryLocationMoveInfoList();

    /**
     * This method corresponds to the database table location_move_info
     *
     * @mbggenerated Mon Jan 07 11:20:46 CST 2019
     */
    List<LocationMoveInfo> queryLocationMoveInfoList(LocationMoveInfoQueryCondition query, Pager pager);

    /**
     * This method corresponds to the database table location_move_info
     *
     * @mbggenerated Mon Jan 07 11:20:46 CST 2019
     */
    LocationMoveInfo queryLocationMoveInfo(Integer primaryKey);

    /**
     * This method corresponds to the database table location_move_info
     *
     * @mbggenerated Mon Jan 07 11:20:46 CST 2019
     */
    LocationMoveInfo queryLocationMoveInfo(LocationMoveInfoQueryCondition query);

    /**
     * This method corresponds to the database table location_move_info
     *
     * @mbggenerated Mon Jan 07 11:20:46 CST 2019
     */
    void createLocationMoveInfo(LocationMoveInfo entity);

    /**
     * This method corresponds to the database table location_move_info
     *
     * @mbggenerated Mon Jan 07 11:20:46 CST 2019
     */
    void batchCreateLocationMoveInfo(List<LocationMoveInfo> entityList);

    /**
     * This method corresponds to the database table location_move_info
     *
     * @mbggenerated Mon Jan 07 11:20:46 CST 2019
     */
    void batchUpdateLocationMoveInfo(List<LocationMoveInfo> entityList);

    /**
     * This method corresponds to the database table location_move_info
     *
     * @mbggenerated Mon Jan 07 11:20:46 CST 2019
     */
    void deleteLocationMoveInfo(Integer primaryKey);

    /**
     * This method corresponds to the database table location_move_info
     *
     * @mbggenerated Mon Jan 07 11:20:46 CST 2019
     */
    void updateLocationMoveInfo(LocationMoveInfo entity);

    void deleteLocationMoveInfoByBoxNoAndSku(String boxNO, String sku);
}