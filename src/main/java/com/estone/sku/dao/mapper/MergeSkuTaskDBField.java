package com.estone.sku.dao.mapper;

public interface MergeSkuTaskDBField {
    String ID = "id";

    String TASK_NO = "task_no";

    String STATUS = "status";

    String IS_PRINTING = "is_printing";

    String RECEIVE_PERSON = "receive_person";

    String RECEIVE_DATE = "receive_date";

    String UP_USER = "up_user";

    String UP_DATE = "up_date";

    String CREATED_BY = "created_by";

    String CREATED_DATE = "created_date";

    String LAST_UPDATE_DATE = "last_update_date";

    String LAST_UPDATE_BY = "last_update_by";

    String PICKING_END_DATE = "picking_end_date";
}