package com.estone.sku.dao.mapper;

public interface MergeSkuStockMatchRecordDBField {
    String ID = "id";

    String MERGE_SKU_MANAGE_ID = "merge_sku_manage_id";

    String DISCARD_STOCK_ID = "discard_stock_id";

    String MATCH_STOCK_ID = "match_stock_id";

    String MATCH_STOCK_LOCATION = "match_stock_location";

    String CREATION_DATE = "creation_date";

    String CREATION_BY = "creation_by";

    String LAST_UPDATE_BY = "last_update_by";

    String LAST_UPDATE_DATE = "last_update_date";
}