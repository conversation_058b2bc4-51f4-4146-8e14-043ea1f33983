package com.estone.sku.dao.mapper;

public interface UniqueSkuExpRelationDBField {
    String ID = "id";

    String RELATION_ID = "relation_id";

    String SKU = "sku";

    String UUID = "uuid";

    String PRO_DATE = "pro_date";

    String EXP_DATE = "exp_date";

    String DAYS = "days";

    String EXP_NO = "exp_no";

    String CREATION_DATE = "creation_date";
    String RELATION_NO = "relation_no";
}