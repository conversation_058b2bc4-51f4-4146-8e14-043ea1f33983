package com.estone.sku.dao.mapper;

import com.estone.sku.bean.WhIdentificationCategory;
import java.sql.ResultSet;
import java.sql.SQLException;
import org.springframework.jdbc.core.RowMapper;

public class WhIdentificationCategoryMapper implements RowMapper<WhIdentificationCategory> {

    public WhIdentificationCategory mapRow(ResultSet rs, int rowNum) throws SQLException {
        WhIdentificationCategory entity = new WhIdentificationCategory();
        entity.setId(rs.getObject(WhIdentificationCategoryDBField.ID) == null ? null : rs.getInt(WhIdentificationCategoryDBField.ID));
        entity.setCode(rs.getObject(WhIdentificationCategoryDBField.CODE) == null ? null : rs.getInt(WhIdentificationCategoryDBField.CODE));
        entity.setName(rs.getString(WhIdentificationCategoryDBField.NAME));
        entity.setUpdateBy(rs.getObject(WhIdentificationCategoryDBField.UPDATE_BY) == null ? null : rs.getInt(WhIdentificationCategoryDBField.UPDATE_BY));
        entity.setUpdateTime(rs.getTimestamp(WhIdentificationCategoryDBField.UPDATE_TIME));
        return entity;
    }
}