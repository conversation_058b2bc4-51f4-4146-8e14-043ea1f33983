package com.estone.sku.dao.mapper;

public interface NewProductMaintenanceDBField {
    String ID = "id";

    String SKU = "sku";

    String STOCK_ID = "stock_id";

    String LOCATION_NUMBER = "location_number";

    String TASK_NO = "task_no";

    String STATUS = "status";

    String QUANTITY = "quantity";

    String CREATION_DATE = "creation_date";

    String TASK_DATE = "task_date";

    String CHECK_DATE = "check_date";

    String CHECK_BY = "check_by";
}