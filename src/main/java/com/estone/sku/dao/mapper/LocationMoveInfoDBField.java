package com.estone.sku.dao.mapper;

public interface LocationMoveInfoDBField {
    String MOVE_ID = "move_id";

    String BOX_NO = "box_no";

    String SKU = "sku";

    String QUANTITY = "quantity";

    String NAME = "name";

    String OLD_LOCATION = "old_location";

    String NEW_LOCATION = "new_location";

    String USER_ID = "user_id";

    String CREATION_DATE = "creation_date";

    String LAST_UPDATE_DATE = "last_update_date";

    String REMARK = "remark";

    String LOG_TEXT = "log_text";

    String OPERATION_WAY = "operation_way";
}