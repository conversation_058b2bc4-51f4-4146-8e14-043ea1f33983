package com.estone.sku.dao.mapper;

import com.estone.sku.bean.CeManage;
import org.springframework.jdbc.core.RowMapper;

import java.sql.ResultSet;
import java.sql.SQLException;

public class CeManageMapper implements <PERSON>Mapper<CeManage> {

    public CeManageMapper(){}

    public CeManage mapRow(ResultSet rs, int rowNum) throws SQLException {
        return getMapRow(rs, "");
    }


    public CeManage getMapRow(ResultSet rs, String prefix) throws SQLException {
        CeManage entity = new CeManage();
        entity.setId(rs.getObject(prefix+CeManageDBField.ID) == null ? null : rs.getInt(prefix+CeManageDBField.ID));
        entity.setSku(rs.getString(prefix+CeManageDBField.SKU));
        entity.setToyFlag(rs.getObject(prefix+CeManageDBField.TOY_FLAG) == null ? null : rs.getBoolean(prefix+CeManageDBField.TOY_FLAG));
        entity.setUploadImageFlag(rs.getObject(prefix+CeManageDBField.UPLOAD_IMAGE_FLAG) == null ? null : rs.getBoolean(prefix+CeManageDBField.UPLOAD_IMAGE_FLAG));
        entity.setSyncCeInfoDate(rs.getTimestamp(prefix+CeManageDBField.SYNC_CE_INFO_DATE));
        entity.setPhotoDate(rs.getTimestamp(prefix+CeManageDBField.PHOTO_DATE));
        entity.setCreatedBy(rs.getObject(prefix+CeManageDBField.CREATED_BY) == null ? null : rs.getInt(prefix+CeManageDBField.CREATED_BY));
        entity.setCreationDate(rs.getTimestamp(prefix+CeManageDBField.CREATION_DATE));
        entity.setLastUpdatedBy(rs.getObject(prefix+CeManageDBField.LAST_UPDATED_BY) == null ? null : rs.getInt(prefix+CeManageDBField.LAST_UPDATED_BY));
        entity.setLastUpdateDate(rs.getTimestamp(prefix+CeManageDBField.LAST_UPDATE_DATE));
        entity.setWmsImageUrls(rs.getString(prefix+CeManageDBField.WMS_IMAGE_URLS));
        entity.setPackImageUrls(rs.getString(prefix+CeManageDBField.PACK_IMAGE_URLS));
        entity.setCeInfo(rs.getString(prefix+CeManageDBField.CE_INFO));
        entity.setSpu(rs.getString(prefix+CeManageDBField.SPU));
        entity.setCeImageUrls(rs.getString(prefix+CeManageDBField.CE_IMAGE_URLS));
        entity.setIsPhotographed(rs.getObject(prefix+CeManageDBField.IS_PHOTOGRAPHED) == null ? null : rs.getBoolean(prefix+CeManageDBField.IS_PHOTOGRAPHED));
        entity.setIsRealPhotoTaken(rs.getObject(prefix+CeManageDBField.IS_REAL_PHOTO_TAKEN) == null ? null : rs.getBoolean(prefix+CeManageDBField.IS_REAL_PHOTO_TAKEN));
        entity.setIsCePhotoTaken(rs.getObject(prefix+CeManageDBField.IS_CE_PHOTO_TAKEN) == null ? null : rs.getBoolean(prefix+CeManageDBField.IS_CE_PHOTO_TAKEN));
        entity.setNeedCeCertified(rs.getObject(prefix+CeManageDBField.NEED_CE_CERTIFIED) == null ? null : rs.getBoolean(prefix+CeManageDBField.NEED_CE_CERTIFIED));
        entity.setStatus(rs.getObject(prefix+CeManageDBField.STATUS) == null ? null : rs.getInt(prefix+CeManageDBField.STATUS));
        entity.setCeTaskNo(rs.getString(prefix+CeManageDBField.CE_TASK_NO));
        entity.setPlatCategoryId(rs.getString(prefix+CeManageDBField.PLAT_CATEGORY_ID));
        entity.setPlatformQualification(rs.getString(prefix+CeManageDBField.PLATFORM_QUALIFICATION));
        return entity;
    }
}