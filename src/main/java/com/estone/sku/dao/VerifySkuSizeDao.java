package com.estone.sku.dao;

import com.estone.sku.bean.VerifySkuSize;
import com.estone.sku.bean.VerifySkuSizeQueryCondition;
import com.whq.tool.component.Pager;
import java.util.List;

public interface VerifySkuSizeDao {
    int queryVerifySkuSizeCount(VerifySkuSizeQueryCondition query);

    List<VerifySkuSize> queryVerifySkuSizeList();

    List<VerifySkuSize> queryVerifySkuSizeList(VerifySkuSizeQueryCondition query, Pager pager);

    VerifySkuSize queryVerifySkuSize(Integer primaryKey);

    VerifySkuSize queryVerifySkuSize(VerifySkuSizeQueryCondition query);

    void createVerifySkuSize(VerifySkuSize entity);

    void batchCreateVerifySkuSize(List<VerifySkuSize> entityList);

    void batchUpdateVerifySkuSize(List<VerifySkuSize> entityList);

    void deleteVerifySkuSize(Integer primaryKey);

    void updateVerifySkuSize(VerifySkuSize entity);
}