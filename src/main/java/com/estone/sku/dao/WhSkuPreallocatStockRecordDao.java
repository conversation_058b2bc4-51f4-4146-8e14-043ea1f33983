package com.estone.sku.dao;

import com.estone.sku.bean.WhSkuPreallocatStockRecord;
import com.estone.sku.bean.WhSkuPreallocatStockRecordQueryCondition;
import com.whq.tool.component.Pager;
import java.util.List;

public interface WhSkuPreallocatStockRecordDao {
    int querywhSkuPreallocatStockRecordCount(WhSkuPreallocatStockRecordQueryCondition query);

    List<WhSkuPreallocatStockRecord> querywhSkuPreallocatStockRecordList();

    List<WhSkuPreallocatStockRecord> querywhSkuPreallocatStockRecordList(WhSkuPreallocatStockRecordQueryCondition query, Pager pager);

    WhSkuPreallocatStockRecord querywhSkuPreallocatStockRecord(Integer primaryKey);

    WhSkuPreallocatStockRecord querywhSkuPreallocatStockRecord(WhSkuPreallocatStockRecordQueryCondition query);

    void createwhSkuPreallocatStockRecord(WhSkuPreallocatStockRecord entity);

    void batchCreatewhSkuPreallocatStockRecord(List<WhSkuPreallocatStockRecord> entityList);

    void batchUpdatewhSkuPreallocatStockRecord(List<WhSkuPreallocatStockRecord> entityList);

    void deletewhSkuPreallocatStockRecord(Integer primaryKey);

    void updatewhSkuPreallocatStockRecord(WhSkuPreallocatStockRecord entity);

    void batchInsertOrUpdate(List<WhSkuPreallocatStockRecord> recordList);
}