package com.estone.sku.dao.impl;

import com.estone.sku.bean.VerifySkuWeightTask;
import com.estone.sku.bean.VerifySkuWeightTaskQueryCondition;
import com.estone.sku.dao.VerifySkuWeightTaskDao;
import com.estone.sku.dao.mapper.VerifySkuWeightTaskAndItemMapper;
import com.estone.sku.dao.mapper.VerifySkuWeightTaskDBField;
import com.estone.sku.dao.mapper.VerifySkuWeightTaskMapper;
import com.whq.tool.component.Pager;
import com.whq.tool.context.DataContextHolder;
import com.whq.tool.sqler.SqlerRequest;
import com.whq.tool.sqler.analyzer.DataType;
import com.whq.tool.sqler.dialect.DialectFactory;
import com.whq.tool.sqler.dialect.SQLDialect;
import com.estone.common.util.SqlerTemplate;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Repository;
import org.springframework.util.Assert;

import java.sql.Timestamp;
import java.util.List;

@Repository("verifySkuWeightTaskDao")
public class VerifySkuWeightTaskDaoImpl implements VerifySkuWeightTaskDao {

    private void setQueryCondition(SqlerRequest request, VerifySkuWeightTaskQueryCondition query) {
        if (query == null) {
            return;
        }
        // 添加查询条件
        // 搜索条件不允许出现空字符
        request.setAllowBlank(false);

        request.addDataParam(VerifySkuWeightTaskDBField.ID, DataType.INT, query.getId());
        request.addDataParam(VerifySkuWeightTaskDBField.TASK_STATUS, DataType.INT, query.getTaskStatus());
        request.addDataParam(VerifySkuWeightTaskDBField.CREATED_BY, DataType.INT, query.getCreatedBy());
        request.addDataParam(VerifySkuWeightTaskDBField.RECEIVE_BY, DataType.INT, query.getReceiveBy());
        request.addDataParam(VerifySkuWeightTaskDBField.FINISH_VERIFY_BY, DataType.INT, query.getFinishVerifyBy());
        request.addDataParam(VerifySkuWeightTaskDBField.LAST_UPDATE_BY, DataType.INT, query.getLastUpdateBy());
        request.addDataParam(VerifySkuWeightTaskDBField.TASK_NO, DataType.STRING, query.getTaskNo());
        request.addDataParam("sku", DataType.STRING, query.getSku());
        request.addDataParam("from_created_date", DataType.STRING, query.getFromCreateDate());
        request.addDataParam("to_created_date", DataType.STRING, query.getToCreateDate());
        request.addDataParam("from_receive_date", DataType.STRING, query.getFromReceiveDate());
        request.addDataParam("to_receive_date", DataType.STRING, query.getToReceiveDate());
        request.addDataParam("from_finish_verify_date", DataType.STRING, query.getFromFinishVerifyDate());
        request.addDataParam("to_finish_verify_date", DataType.STRING, query.getToFinishVerifyDate());
        if (CollectionUtils.isNotEmpty(query.getSkuList())) {
            request.addDataParam("sku_list", DataType.STRING, query.getSkuList());
        }
        if (CollectionUtils.isNotEmpty(query.getTaskIds())) {
            request.addDataParam("taskIds", DataType.STRING, query.getTaskIds());
        }
        if (CollectionUtils.isNotEmpty(query.getTaskStatusList())) {
            request.addDataParam("task_status_list", DataType.STRING, query.getTaskStatusList());
        }
    }

    @Override
    public int queryVerifySkuWeightTaskCount(VerifySkuWeightTaskQueryCondition query) {
        SqlerRequest request = new SqlerRequest("queryVerifySkuWeightTaskCount");
        setQueryCondition(request, query);
        return SqlerTemplate.queryForInt(request);
    }

    @Override
    public List<VerifySkuWeightTask> queryVerifySkuWeightTaskList() {
        SqlerRequest request = new SqlerRequest("queryVerifySkuWeightTaskList");
        return SqlerTemplate.query(request, new VerifySkuWeightTaskMapper());
    }

    @Override
    public List<VerifySkuWeightTask> queryVerifySkuWeightTaskList(VerifySkuWeightTaskQueryCondition query,
                                                                  Pager pager) {
        SqlerRequest request = new SqlerRequest("queryVerifySkuWeightTaskList");
        setQueryCondition(request, query);
        if (pager != null) {
            request.addFetch(pager.getPageNo(), pager.getPageSize());
        }
        return SqlerTemplate.query(request, new VerifySkuWeightTaskMapper());
    }

    @Override
    public VerifySkuWeightTask queryVerifySkuWeightTask(Integer primaryKey) {
        Assert.notNull(primaryKey, "primaryKey is null!");
        SqlerRequest request = new SqlerRequest("queryVerifySkuWeightTaskByPrimaryKey");
        request.addDataParam(VerifySkuWeightTaskDBField.ID, DataType.INT, primaryKey);
        return SqlerTemplate.queryForObject(request, new VerifySkuWeightTaskMapper());
    }

    @Override
    public VerifySkuWeightTask queryVerifySkuWeightTask(VerifySkuWeightTaskQueryCondition query) {
        SqlerRequest request = new SqlerRequest("queryVerifySkuWeightTask");
        setQueryCondition(request, query);
        return SqlerTemplate.queryForObject(request, new VerifySkuWeightTaskMapper());
    }

    @Override
    public void createVerifySkuWeightTask(VerifySkuWeightTask entity) {
        SqlerRequest request = new SqlerRequest("createVerifySkuWeightTask");
        request.addDataParam(VerifySkuWeightTaskDBField.TASK_NO, DataType.STRING, entity.getTaskNo());
        request.addDataParam(VerifySkuWeightTaskDBField.TASK_STATUS, DataType.INT, entity.getTaskStatus());
        request.addDataParam(VerifySkuWeightTaskDBField.TASK_LEVEL, DataType.INT, entity.getTaskLevel());
        request.addDataParam(VerifySkuWeightTaskDBField.CREATED_BY, DataType.INT,
                entity.getCreatedBy() == null ? DataContextHolder.getUserId() : entity.getCreatedBy());
        request.addDataParam(VerifySkuWeightTaskDBField.CREATED_DATE, DataType.TIMESTAMP,
                entity.getCreatedDate() == null ? new Timestamp(System.currentTimeMillis()) : entity.getCreatedDate());
        request.addDataParam(VerifySkuWeightTaskDBField.RECEIVE_BY, DataType.INT, entity.getReceiveBy());
        request.addDataParam(VerifySkuWeightTaskDBField.RECEIVE_DATE, DataType.TIMESTAMP, entity.getReceiveDate());
        request.addDataParam(VerifySkuWeightTaskDBField.LAST_UPDATE_DATE, DataType.TIMESTAMP,
                new Timestamp(System.currentTimeMillis()));
        request.addDataParam(VerifySkuWeightTaskDBField.LAST_UPDATE_BY, DataType.INT, entity.getLastUpdateBy());
        request.addDataParam(VerifySkuWeightTaskDBField.FINISH_VERIFY_DATE, DataType.TIMESTAMP,
                entity.getFinishVerifyDate());
        request.addDataParam(VerifySkuWeightTaskDBField.FINISH_VERIFY_BY, DataType.INT, entity.getFinishVerifyBy());
        Integer autoIncrementId = SqlerTemplate.executeAndReturn(request);
        entity.setId(autoIncrementId);
    }

    @Override
    public int updateVerifySkuWeightTask(VerifySkuWeightTask entity) {
        SqlerRequest request = new SqlerRequest("updateVerifySkuWeightTaskByPrimaryKey");
        request.addDataParam(VerifySkuWeightTaskDBField.ID, DataType.INT, entity.getId());

        request.addDataParam(VerifySkuWeightTaskDBField.TASK_NO, DataType.STRING, entity.getTaskNo());
        request.addDataParam(VerifySkuWeightTaskDBField.TASK_STATUS, DataType.INT, entity.getTaskStatus());
        request.addDataParam(VerifySkuWeightTaskDBField.TASK_LEVEL, DataType.INT, entity.getTaskLevel());

        request.addDataParam(VerifySkuWeightTaskDBField.CREATED_DATE, DataType.TIMESTAMP, entity.getCreatedDate());
        request.addDataParam(VerifySkuWeightTaskDBField.RECEIVE_BY, DataType.INT, entity.getReceiveBy());
        request.addDataParam(VerifySkuWeightTaskDBField.RECEIVE_DATE, DataType.TIMESTAMP, entity.getReceiveDate());
        request.addDataParam(VerifySkuWeightTaskDBField.LAST_UPDATE_DATE, DataType.TIMESTAMP,
                new Timestamp(System.currentTimeMillis()));
        request.addDataParam(VerifySkuWeightTaskDBField.LAST_UPDATE_BY, DataType.INT,
                entity.getLastUpdateBy() == null ? DataContextHolder.getUserId() : entity.getLastUpdateBy());
        request.addDataParam(VerifySkuWeightTaskDBField.FINISH_VERIFY_DATE, DataType.TIMESTAMP,
                entity.getFinishVerifyDate());
        request.addDataParam(VerifySkuWeightTaskDBField.FINISH_VERIFY_BY, DataType.INT, entity.getFinishVerifyBy());
        return SqlerTemplate.execute(request);
    }

    @Override
    public void batchCreateVerifySkuWeightTask(List<VerifySkuWeightTask> entityList) {
        if (entityList != null && !entityList.isEmpty()) {
            SqlerRequest request = new SqlerRequest("createVerifySkuWeightTask");
            for (VerifySkuWeightTask entity : entityList) {
                request.addBatchDataParam(VerifySkuWeightTaskDBField.TASK_NO, DataType.STRING, entity.getTaskNo());
                request.addBatchDataParam(VerifySkuWeightTaskDBField.TASK_STATUS, DataType.INT, entity.getTaskStatus());
                request.addBatchDataParam(VerifySkuWeightTaskDBField.TASK_LEVEL, DataType.INT, entity.getTaskLevel());
                request.addBatchDataParam(VerifySkuWeightTaskDBField.CREATED_BY, DataType.INT,
                        entity.getCreatedBy() == null ? DataContextHolder.getUserId() : entity.getCreatedBy());
                request.addBatchDataParam(VerifySkuWeightTaskDBField.CREATED_DATE, DataType.TIMESTAMP,
                        entity.getCreatedDate() == null ? new Timestamp(System.currentTimeMillis())
                                : entity.getCreatedDate());
                request.addBatchDataParam(VerifySkuWeightTaskDBField.RECEIVE_BY, DataType.INT, entity.getReceiveBy());
                request.addBatchDataParam(VerifySkuWeightTaskDBField.RECEIVE_DATE, DataType.TIMESTAMP,
                        entity.getReceiveDate());
                request.addBatchDataParam(VerifySkuWeightTaskDBField.LAST_UPDATE_DATE, DataType.TIMESTAMP,
                        new Timestamp(System.currentTimeMillis()));
                request.addBatchDataParam(VerifySkuWeightTaskDBField.LAST_UPDATE_BY, DataType.INT,
                        entity.getLastUpdateBy());
                request.addBatchDataParam(VerifySkuWeightTaskDBField.FINISH_VERIFY_DATE, DataType.TIMESTAMP,
                        entity.getFinishVerifyDate());
                request.addBatchDataParam(VerifySkuWeightTaskDBField.FINISH_VERIFY_BY, DataType.INT,
                        entity.getFinishVerifyBy());
                request.addBatch();
            }
            SqlerTemplate.batchUpdate(request);
        }
    }

    @Override
    public void batchUpdateVerifySkuWeightTask(List<VerifySkuWeightTask> entityList) {
        if (entityList != null && !entityList.isEmpty()) {
            SqlerRequest request = new SqlerRequest("updateVerifySkuWeightTaskByPrimaryKey");
            for (VerifySkuWeightTask entity : entityList) {
                request.addBatchDataParam(VerifySkuWeightTaskDBField.ID, DataType.INT, entity.getId());

                request.addBatchDataParam(VerifySkuWeightTaskDBField.TASK_NO, DataType.STRING, entity.getTaskNo());
                request.addBatchDataParam(VerifySkuWeightTaskDBField.TASK_STATUS, DataType.INT, entity.getTaskStatus());
                request.addBatchDataParam(VerifySkuWeightTaskDBField.TASK_LEVEL, DataType.INT, entity.getTaskLevel());

                request.addBatchDataParam(VerifySkuWeightTaskDBField.CREATED_DATE, DataType.TIMESTAMP,
                        entity.getCreatedDate());
                request.addBatchDataParam(VerifySkuWeightTaskDBField.RECEIVE_BY, DataType.INT, entity.getReceiveBy());
                request.addBatchDataParam(VerifySkuWeightTaskDBField.RECEIVE_DATE, DataType.TIMESTAMP,
                        entity.getReceiveDate());
                request.addBatchDataParam(VerifySkuWeightTaskDBField.LAST_UPDATE_DATE, DataType.TIMESTAMP,
                        new Timestamp(System.currentTimeMillis()));
                request.addBatchDataParam(VerifySkuWeightTaskDBField.LAST_UPDATE_BY, DataType.INT,
                        entity.getLastUpdateBy() == null ? DataContextHolder.getUserId() : entity.getLastUpdateBy());
                request.addBatchDataParam(VerifySkuWeightTaskDBField.FINISH_VERIFY_DATE, DataType.TIMESTAMP,
                        entity.getFinishVerifyDate());
                request.addBatchDataParam(VerifySkuWeightTaskDBField.FINISH_VERIFY_BY, DataType.INT,
                        entity.getFinishVerifyBy());
                request.addBatch();
            }
            SqlerTemplate.batchUpdate(request);
        }
    }

    @Override
    public void deleteVerifySkuWeightTask(Integer primaryKey) {
        SqlerRequest request = new SqlerRequest("deleteVerifySkuWeightTaskByPrimaryKey");
        request.addDataParam(VerifySkuWeightTaskDBField.ID, DataType.INT, primaryKey);
        SqlerTemplate.execute(request);
    }

    @Override
    public int queryVerifySkuWeightTaskAndItemCount(VerifySkuWeightTaskQueryCondition query) {
        SqlerRequest request = new SqlerRequest("queryVerifySkuWeightTaskAndItemCount");
        setQueryCondition(request, query);
        return SqlerTemplate.queryForInt(request);
    }

    @Override
    public List<VerifySkuWeightTask> queryVerifySkuWeightTaskAndItemList(VerifySkuWeightTaskQueryCondition query,
                                                                         Pager pager) {
        SqlerRequest request = new SqlerRequest("queryVerifySkuWeightTaskAndItemList");
        setQueryCondition(request, query);
        request.addSqlDataParam("ORDER_BY", "ORDER BY task.task_level DESC, task.id DESC, item.id DESC");
        if (pager != null) {
            // request.addFetch(pager.getPageNo(), pager.getPageSize());
            SQLDialect dial = DialectFactory.createDialect(null);
            long start = (long) (pager.getPageNo() - 1) * pager.getPageSize();
            long end = start + pager.getPageSize() - 1L;
            request.addSqlDataParam("LIMIT", dial.queryFirst2Last("", (int) start, (int) end));
        }
        return SqlerTemplate.query(request, new VerifySkuWeightTaskAndItemMapper(true));
    }

    @Override
    public Integer queryVerifySkuWeightTaskMaxLevel() {
        SqlerRequest request = new SqlerRequest("queryVerifySkuWeightTaskMaxLevel");
        return SqlerTemplate.queryForInt(request);
    }
}