package com.estone.sku.dao.impl;

import com.estone.sku.bean.MoveForm;
import com.estone.sku.bean.MoveFormQueryCondition;
import com.estone.sku.dao.MoveFormDao;
import com.estone.sku.dao.mapper.MoveFormDBField;
import com.estone.sku.dao.mapper.MoveFormMapper;
import com.whq.tool.component.Pager;
import com.whq.tool.sqler.SqlerRequest;
import com.whq.tool.sqler.analyzer.DataType;
import com.estone.common.util.SqlerTemplate;
import org.springframework.stereotype.Repository;
import org.springframework.util.Assert;
import org.springframework.util.StringUtils;

import java.sql.Timestamp;
import java.util.List;
import java.util.Random;

@Repository("moveFormDao")
public class MoveFormDaoImpl implements MoveFormDao {

    private void setQueryCondition(SqlerRequest request, MoveFormQueryCondition query) {
        if (query == null) {
            return;
        }
        // 添加查询条件
        // 搜索条件不允许出现空字符
        request.setAllowBlank(false);
        
        request.addDataParam(MoveFormDBField.ID, DataType.INT, query.getId());

        request.addDataParam("idList", DataType.INT, query.getIdList());

        request.addDataParam(MoveFormDBField.MOVE_ID, DataType.STRING, query.getMoveId());
        request.addDataParam("moveIdList", DataType.STRING, query.getMoveIdList());

        request.addDataParam("skus", DataType.STRING, query.getSkuList());

        request.addDataParam(MoveFormDBField.SKU_NUM, DataType.INT, query.getSkuNum());
        request.addDataParam(MoveFormDBField.PLAN_NUM, DataType.INT, query.getPlanNum());
        request.addDataParam(MoveFormDBField.MIDDLE_NUM, DataType.INT, query.getMiddleNum());
        request.addDataParam(MoveFormDBField.REALITY_NUM, DataType.INT, query.getRealityNum());
        request.addDataParam(MoveFormDBField.DIFF_NUM, DataType.INT, query.getDiffNum());
        request.addDataParam(MoveFormDBField.BOX_NO, DataType.STRING, query.getBoxNo());
        request.addDataParam(MoveFormDBField.STATE, DataType.STRING, query.getState());
        request.addDataParam("states", DataType.STRING, query.getStateList());

        request.addDataParam(MoveFormDBField.CREATION_ID, DataType.INT, query.getCreationId());
        request.addDataParam(MoveFormDBField.CREATION_TIME, DataType.TIMESTAMP, query.getCreationTime());
        request.addDataParam(MoveFormDBField.GAIN_TIME, DataType.TIMESTAMP, query.getGainTime());
        request.addDataParam(MoveFormDBField.PICKING_ID, DataType.INT, query.getPickingId());
        request.addDataParam(MoveFormDBField.PICKING_TIME, DataType.TIMESTAMP, query.getPickingTime());
        request.addDataParam(MoveFormDBField.PUTAWAY_ID, DataType.INT, query.getPutawayId());
        request.addDataParam(MoveFormDBField.PUTAWAY_TIME, DataType.TIMESTAMP, query.getPutawayTime());

        request.addDataParam("fromCreationTime", DataType.STRING, query.getFromCreationTime());
        request.addDataParam("toCreationTime", DataType.STRING, query.getToCreationTime());
        request.addDataParam("fromGainTime", DataType.STRING, query.getFromGainTime());
        request.addDataParam("toGainTime", DataType.STRING, query.getToGainTime());
        request.addDataParam("fromReceiveTime", DataType.STRING, query.getFromReceiveTime());
        request.addDataParam("toReceiveTime", DataType.STRING, query.getToReceiveTime());


    }

    @Override
    public int queryMoveFormCount(MoveFormQueryCondition query) {
        SqlerRequest request = new SqlerRequest("queryMoveFormCount");
        setQueryCondition(request, query);
        return SqlerTemplate.queryForInt(request);
    }

    @Override
    public List<MoveForm> queryMoveFormList() {
        SqlerRequest request = new SqlerRequest("queryMoveFormList");
        return SqlerTemplate.query(request, new MoveFormMapper());
    }

    @Override
    public List<MoveForm> queryMoveFormList(MoveFormQueryCondition query, Pager pager) {
        SqlerRequest request = new SqlerRequest("queryMoveFormList");
        setQueryCondition(request, query);
        if(pager != null) {
            request.addFetch(pager.getPageNo(), pager.getPageSize());
        }
        return SqlerTemplate.query(request, new MoveFormMapper());
    }

    @Override
    public MoveForm queryMoveForm(Integer primaryKey) {
        Assert.notNull(primaryKey, "primaryKey is null!");
        SqlerRequest request = new SqlerRequest("queryMoveFormByPrimaryKey");
        request.addDataParam(MoveFormDBField.ID, DataType.INT, primaryKey);
        return SqlerTemplate.queryForObject(request, new MoveFormMapper());
    }

    @Override
    public MoveForm queryMoveForm(MoveFormQueryCondition query) {
        SqlerRequest request = new SqlerRequest("queryMoveForm");
        setQueryCondition(request, query);
        return SqlerTemplate.queryForObject(request, new MoveFormMapper());
    }

    @Override
    public void createMoveForm(MoveForm entity) {
        SqlerRequest request = new SqlerRequest("createMoveForm");
        request.addDataParam(MoveFormDBField.MOVE_ID, DataType.STRING, StringUtils.isEmpty(entity.getMoveId()) ? String.valueOf(System.currentTimeMillis())+String.format("%04d",new Random().nextInt(9999)) : entity.getMoveId());
        request.addDataParam(MoveFormDBField.SKU_NUM, DataType.INT, entity.getSkuNum());
        request.addDataParam(MoveFormDBField.PLAN_NUM, DataType.INT, entity.getPlanNum());
        request.addDataParam(MoveFormDBField.MIDDLE_NUM, DataType.INT, entity.getMiddleNum());
        request.addDataParam(MoveFormDBField.REALITY_NUM, DataType.INT, entity.getRealityNum());
        request.addDataParam(MoveFormDBField.DIFF_NUM, DataType.INT, entity.getDiffNum());
        request.addDataParam(MoveFormDBField.BOX_NO, DataType.STRING, entity.getBoxNo());
        request.addDataParam(MoveFormDBField.STATE, DataType.STRING, entity.getState());
        request.addDataParam(MoveFormDBField.CREATION_ID, DataType.INT, entity.getCreationId());
        request.addDataParam(MoveFormDBField.CREATION_TIME, DataType.TIMESTAMP, entity.getCreationTime());
        request.addDataParam(MoveFormDBField.GAIN_TIME, DataType.TIMESTAMP, entity.getGainTime());
        request.addDataParam(MoveFormDBField.PICKING_ID, DataType.INT, entity.getPickingId());
        request.addDataParam(MoveFormDBField.PICKING_TIME, DataType.TIMESTAMP, entity.getPickingTime());
        request.addDataParam(MoveFormDBField.PUTAWAY_ID, DataType.INT, entity.getPutawayId());
        request.addDataParam(MoveFormDBField.PUTAWAY_TIME, DataType.TIMESTAMP, entity.getPutawayTime());
        Integer autoIncrementId = SqlerTemplate.executeAndReturn(request);
        entity.setId(autoIncrementId);
    }

    @Override
    public void updateMoveForm(MoveForm entity) {
        SqlerRequest request = new SqlerRequest("updateMoveFormByPrimaryKey");
        request.addDataParam(MoveFormDBField.ID, DataType.INT, entity.getId());
        
        request.addDataParam(MoveFormDBField.MOVE_ID, DataType.STRING, entity.getMoveId());
        request.addDataParam(MoveFormDBField.SKU_NUM, DataType.INT, entity.getSkuNum());
        request.addDataParam(MoveFormDBField.PLAN_NUM, DataType.INT, entity.getPlanNum());
        request.addDataParam(MoveFormDBField.MIDDLE_NUM, DataType.INT, entity.getMiddleNum());
        request.addDataParam(MoveFormDBField.REALITY_NUM, DataType.INT, entity.getRealityNum());
        request.addDataParam(MoveFormDBField.DIFF_NUM, DataType.INT, entity.getDiffNum());
        request.addDataParam(MoveFormDBField.BOX_NO, DataType.STRING, entity.getBoxNo());
        request.addDataParam(MoveFormDBField.STATE, DataType.STRING, entity.getState());
        request.addDataParam(MoveFormDBField.CREATION_ID, DataType.INT, entity.getCreationId());
        request.addDataParam(MoveFormDBField.CREATION_TIME, DataType.TIMESTAMP, entity.getCreationTime());
        request.addDataParam(MoveFormDBField.GAIN_TIME, DataType.TIMESTAMP, entity.getGainTime());
        request.addDataParam(MoveFormDBField.PICKING_ID, DataType.INT, entity.getPickingId());
        request.addDataParam(MoveFormDBField.PICKING_TIME, DataType.TIMESTAMP, entity.getPickingTime());
        request.addDataParam(MoveFormDBField.PUTAWAY_ID, DataType.INT, entity.getPutawayId());
        request.addDataParam(MoveFormDBField.PUTAWAY_TIME, DataType.TIMESTAMP, entity.getPutawayTime());
        SqlerTemplate.execute(request);
    }

    @Override
    public void batchCreateMoveForm(List<MoveForm> entityList) {
        if (entityList != null && !entityList.isEmpty()) {
            SqlerRequest request = new SqlerRequest("createMoveForm");
            for (MoveForm entity : entityList) {
                request.addBatchDataParam(MoveFormDBField.MOVE_ID, DataType.STRING, entity.getMoveId());
                request.addBatchDataParam(MoveFormDBField.SKU_NUM, DataType.INT, entity.getSkuNum());
                request.addBatchDataParam(MoveFormDBField.PLAN_NUM, DataType.INT, entity.getPlanNum());
                request.addBatchDataParam(MoveFormDBField.MIDDLE_NUM, DataType.INT, entity.getMiddleNum());
                request.addBatchDataParam(MoveFormDBField.REALITY_NUM, DataType.INT, entity.getRealityNum());
                request.addBatchDataParam(MoveFormDBField.DIFF_NUM, DataType.INT, entity.getDiffNum());
                request.addBatchDataParam(MoveFormDBField.BOX_NO, DataType.STRING, entity.getBoxNo());
                request.addBatchDataParam(MoveFormDBField.STATE, DataType.STRING, entity.getState());
                request.addBatchDataParam(MoveFormDBField.CREATION_ID, DataType.INT, entity.getCreationId());
                request.addBatchDataParam(MoveFormDBField.CREATION_TIME, DataType.TIMESTAMP, entity.getCreationTime());
                request.addBatchDataParam(MoveFormDBField.GAIN_TIME, DataType.TIMESTAMP, entity.getGainTime());
                request.addBatchDataParam(MoveFormDBField.PICKING_ID, DataType.INT, entity.getPickingId());
                request.addBatchDataParam(MoveFormDBField.PICKING_TIME, DataType.TIMESTAMP, entity.getPickingTime());
                request.addBatchDataParam(MoveFormDBField.PUTAWAY_ID, DataType.INT, entity.getPutawayId());
                request.addBatchDataParam(MoveFormDBField.PUTAWAY_TIME, DataType.TIMESTAMP, entity.getPutawayTime());
                request.addBatch();
            }
            SqlerTemplate.batchUpdate(request);
        }
    }

    @Override
    public void batchUpdateMoveForm(List<MoveForm> entityList) {
        if (entityList != null && !entityList.isEmpty()) {
            SqlerRequest request = new SqlerRequest("updateMoveFormByPrimaryKey");
            for (MoveForm entity : entityList) {
                request.addBatchDataParam(MoveFormDBField.ID, DataType.INT, entity.getId());
                
                request.addBatchDataParam(MoveFormDBField.MOVE_ID, DataType.STRING, entity.getMoveId());
                request.addBatchDataParam(MoveFormDBField.SKU_NUM, DataType.INT, entity.getSkuNum());
                request.addBatchDataParam(MoveFormDBField.PLAN_NUM, DataType.INT, entity.getPlanNum());
                request.addBatchDataParam(MoveFormDBField.MIDDLE_NUM, DataType.INT, entity.getMiddleNum());
                request.addBatchDataParam(MoveFormDBField.REALITY_NUM, DataType.INT, entity.getRealityNum());
                request.addBatchDataParam(MoveFormDBField.DIFF_NUM, DataType.INT, entity.getDiffNum());
                request.addBatchDataParam(MoveFormDBField.BOX_NO, DataType.STRING, entity.getBoxNo());
                request.addBatchDataParam(MoveFormDBField.STATE, DataType.STRING, entity.getState());
                request.addBatchDataParam(MoveFormDBField.CREATION_ID, DataType.INT, entity.getCreationId());
                request.addBatchDataParam(MoveFormDBField.CREATION_TIME, DataType.TIMESTAMP, entity.getCreationTime());
                request.addBatchDataParam(MoveFormDBField.GAIN_TIME, DataType.TIMESTAMP, entity.getGainTime());
                request.addBatchDataParam(MoveFormDBField.PICKING_ID, DataType.INT, entity.getPickingId());
                request.addBatchDataParam(MoveFormDBField.PICKING_TIME, DataType.TIMESTAMP, entity.getPickingTime());
                request.addBatchDataParam(MoveFormDBField.PUTAWAY_ID, DataType.INT, entity.getPutawayId());
                request.addBatchDataParam(MoveFormDBField.PUTAWAY_TIME, DataType.TIMESTAMP, entity.getPutawayTime());
                request.addBatch();
            }
            SqlerTemplate.batchUpdate(request);
        }
    }

    @Override
    public void deleteMoveForm(Integer primaryKey) {
        SqlerRequest request = new SqlerRequest("deleteMoveFormByPrimaryKey");
        request.addDataParam(MoveFormDBField.ID, DataType.INT, primaryKey);
        SqlerTemplate.execute(request);
    }
}