package com.estone.sku.dao.impl;

import java.sql.Timestamp;
import java.util.Arrays;
import java.util.List;

import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Repository;
import org.springframework.util.Assert;

import com.estone.common.util.SqlerTemplate;
import com.estone.picking.dao.mapper.CePhotoTaskDBField;
import com.estone.sku.bean.MergeSkuTask;
import com.estone.sku.bean.MergeSkuTaskQueryCondition;
import com.estone.sku.dao.MergeSkuTaskDao;
import com.estone.sku.dao.mapper.MergeSkuTaskDBField;
import com.estone.sku.dao.mapper.MergeSkuTaskMapper;
import com.whq.tool.component.Pager;
import com.whq.tool.context.DataContextHolder;
import com.whq.tool.sqler.SqlerRequest;
import com.whq.tool.sqler.analyzer.DataType;
import com.whq.tool.sqler.dialect.DialectFactory;
import com.whq.tool.sqler.dialect.SQLDialect;

@Repository("mergeSkuTaskDao")
public class MergeSkuTaskDaoImpl implements MergeSkuTaskDao {

    private void setQueryCondition(SqlerRequest request, MergeSkuTaskQueryCondition query) {
        if (query == null) {
            return;
        }
        // 添加只读权限
        if (query.getReadOnly() != null && query.getReadOnly())
        {
            request.setReadOnly(true);
        }
        // 添加查询条件
        // 搜索条件不允许出现空字符
        request.setAllowBlank(false);
        
        request.addDataParam(MergeSkuTaskDBField.ID, DataType.INT, query.getId());
        request.addDataParam(MergeSkuTaskDBField.STATUS, DataType.INT, query.getStatus());

        request.addDataParam(MergeSkuTaskDBField.UP_USER, DataType.INT, query.getUpDate());
        request.addDataParam(MergeSkuTaskDBField.RECEIVE_PERSON, DataType.INT, query.getReceivePerson());

        if (StringUtils.isNotBlank(query.getTaskNo())) {
            if (!query.getTaskNo().contains(",")) {
                request.addDataParam(CePhotoTaskDBField.TASK_NO, DataType.STRING, query.getTaskNo());
            }
            else{
                request.addDataParam("taskNoList",DataType.STRING,Arrays.asList(query.getTaskNo().split(",")));
            }
        }

        if(StringUtils.isNotBlank(query.getDiscardSku())){
            request.addDataParam("discardSkuList",DataType.STRING, Arrays.asList(query.getDiscardSku().split(",")));
        }

        if(StringUtils.isNotBlank(query.getMergeSku())){
            request.addDataParam("mergeSkuList",DataType.STRING, Arrays.asList(query.getMergeSku().split(",")));
        }
        request.addDataParam("ids", DataType.INT, query.getIds());
        request.addDataParam("fromCreatedDate", DataType.STRING, query.getFromCreatedDate());
        request.addDataParam("toCreatedDate", DataType.STRING, query.getToCreatedDate());
        request.addDataParam("fromPickingEndDate", DataType.STRING, query.getFromPickingEndDate());
        request.addDataParam("toPickingEndDate", DataType.STRING, query.getToPickingEndDate());
        request.addDataParam("statusList", DataType.INT, query.getStatusList());

    }

    @Override
    public int queryMergeSkuTaskCount(MergeSkuTaskQueryCondition query) {
        SqlerRequest request = new SqlerRequest("queryMergeSkuTaskCount");
        setQueryCondition(request, query);
        return SqlerTemplate.queryForInt(request);
    }

    @Override
    public List<MergeSkuTask> queryMergeSkuTaskList() {
        SqlerRequest request = new SqlerRequest("queryMergeSkuTaskList");
        return SqlerTemplate.query(request, new MergeSkuTaskMapper());
    }

    @Override
    public List<MergeSkuTask> queryMergeSkuTaskList(MergeSkuTaskQueryCondition query, Pager pager) {
        SqlerRequest request = new SqlerRequest("queryMergeSkuTaskList");
        setQueryCondition(request, query);
        if (pager != null) {
            SQLDialect dial = DialectFactory.createDialect(null);

            long start = (long) (pager.getPageNo() - 1) * pager.getPageSize();

            long end = start + pager.getPageSize() - 1L;

            request.addSqlDataParam("LIMIT", dial.queryFirst2Last("", (int) start, (int) end));
        }
        return SqlerTemplate.query(request, new MergeSkuTaskMapper(true));
    }

    @Override
    public MergeSkuTask queryMergeSkuTask(Integer primaryKey) {
        Assert.notNull(primaryKey, "primaryKey is null!");
        SqlerRequest request = new SqlerRequest("queryMergeSkuTaskByPrimaryKey");
        request.addDataParam(MergeSkuTaskDBField.ID, DataType.INT, primaryKey);
        return SqlerTemplate.queryForObject(request, new MergeSkuTaskMapper());
    }

    @Override
    public MergeSkuTask queryMergeSkuTask(MergeSkuTaskQueryCondition query) {
        SqlerRequest request = new SqlerRequest("queryMergeSkuTask");
        setQueryCondition(request, query);
        return SqlerTemplate.queryForObject(request, new MergeSkuTaskMapper());
    }

    @Override
    public void createMergeSkuTask(MergeSkuTask entity) {
        SqlerRequest request = new SqlerRequest("createMergeSkuTask");
        request.addDataParam(MergeSkuTaskDBField.TASK_NO, DataType.STRING, entity.getTaskNo());
        request.addDataParam(MergeSkuTaskDBField.STATUS, DataType.INT, entity.getStatus());
        request.addDataParam(MergeSkuTaskDBField.IS_PRINTING, DataType.INT, entity.getIsPrinting());
        request.addDataParam(MergeSkuTaskDBField.RECEIVE_PERSON, DataType.INT, entity.getReceivePerson());
        request.addDataParam(MergeSkuTaskDBField.RECEIVE_DATE, DataType.TIMESTAMP, entity.getReceiveDate());
        request.addDataParam(MergeSkuTaskDBField.UP_USER, DataType.INT, entity.getUpUser());
        request.addDataParam(MergeSkuTaskDBField.UP_DATE, DataType.TIMESTAMP, entity.getUpDate());
        request.addDataParam(MergeSkuTaskDBField.CREATED_BY, DataType.INT, entity.getCreatedBy() == null ? DataContextHolder.getUserId() : entity.getCreatedBy());
        request.addDataParam(MergeSkuTaskDBField.CREATED_DATE, DataType.TIMESTAMP, entity.getCreatedDate()==null?new Timestamp(System.currentTimeMillis()):entity.getCreatedDate());
        request.addDataParam(MergeSkuTaskDBField.LAST_UPDATE_DATE, DataType.TIMESTAMP, new Timestamp(System.currentTimeMillis()));
        request.addDataParam(MergeSkuTaskDBField.LAST_UPDATE_BY, DataType.INT, entity.getLastUpdateBy());
        request.addDataParam(MergeSkuTaskDBField.PICKING_END_DATE, DataType.TIMESTAMP, entity.getPickingEndDate());
        Integer autoIncrementId = SqlerTemplate.executeAndReturn(request);
        entity.setId(autoIncrementId);
    }

    @Override
    public void updateMergeSkuTask(MergeSkuTask entity) {
        SqlerRequest request = new SqlerRequest("updateMergeSkuTaskByPrimaryKey");
        request.addDataParam(MergeSkuTaskDBField.ID, DataType.INT, entity.getId());
        
        request.addDataParam(MergeSkuTaskDBField.TASK_NO, DataType.STRING, entity.getTaskNo());
        request.addDataParam(MergeSkuTaskDBField.STATUS, DataType.INT, entity.getStatus());
        request.addDataParam(MergeSkuTaskDBField.IS_PRINTING, DataType.INT, entity.getIsPrinting());
        request.addDataParam(MergeSkuTaskDBField.RECEIVE_PERSON, DataType.INT, entity.getReceivePerson());
        request.addDataParam(MergeSkuTaskDBField.RECEIVE_DATE, DataType.TIMESTAMP, entity.getReceiveDate());
        request.addDataParam(MergeSkuTaskDBField.UP_USER, DataType.INT, entity.getUpUser());
        request.addDataParam(MergeSkuTaskDBField.UP_DATE, DataType.TIMESTAMP, entity.getUpDate());
        
        request.addDataParam(MergeSkuTaskDBField.CREATED_DATE, DataType.TIMESTAMP, entity.getCreatedDate());
        request.addDataParam(MergeSkuTaskDBField.LAST_UPDATE_DATE, DataType.TIMESTAMP, new Timestamp(System.currentTimeMillis()));
        request.addDataParam(MergeSkuTaskDBField.LAST_UPDATE_BY, DataType.INT, entity.getLastUpdateBy());
        request.addDataParam(MergeSkuTaskDBField.PICKING_END_DATE, DataType.TIMESTAMP, entity.getPickingEndDate());
        SqlerTemplate.execute(request);
    }

    @Override
    public void batchCreateMergeSkuTask(List<MergeSkuTask> entityList) {
        if (entityList != null && !entityList.isEmpty()) {
            SqlerRequest request = new SqlerRequest("createMergeSkuTask");
            for (MergeSkuTask entity : entityList) {
                request.addBatchDataParam(MergeSkuTaskDBField.TASK_NO, DataType.STRING, entity.getTaskNo());
                request.addBatchDataParam(MergeSkuTaskDBField.STATUS, DataType.INT, entity.getStatus());
                request.addBatchDataParam(MergeSkuTaskDBField.IS_PRINTING, DataType.INT, entity.getIsPrinting());
                request.addBatchDataParam(MergeSkuTaskDBField.RECEIVE_PERSON, DataType.INT, entity.getReceivePerson());
                request.addBatchDataParam(MergeSkuTaskDBField.RECEIVE_DATE, DataType.TIMESTAMP, entity.getReceiveDate());
                request.addBatchDataParam(MergeSkuTaskDBField.UP_USER, DataType.INT, entity.getUpUser());
                request.addBatchDataParam(MergeSkuTaskDBField.UP_DATE, DataType.TIMESTAMP, entity.getUpDate());
                request.addBatchDataParam(MergeSkuTaskDBField.CREATED_BY, DataType.INT, entity.getCreatedBy() == null ? DataContextHolder.getUserId() : entity.getCreatedBy());
                request.addBatchDataParam(MergeSkuTaskDBField.CREATED_DATE, DataType.TIMESTAMP, entity.getCreatedDate()==null?new Timestamp(System.currentTimeMillis()):entity.getCreatedDate());
                request.addBatchDataParam(MergeSkuTaskDBField.LAST_UPDATE_DATE, DataType.TIMESTAMP, new Timestamp(System.currentTimeMillis()));
                request.addBatchDataParam(MergeSkuTaskDBField.LAST_UPDATE_BY, DataType.INT, entity.getLastUpdateBy());
                request.addBatchDataParam(MergeSkuTaskDBField.PICKING_END_DATE, DataType.TIMESTAMP, entity.getPickingEndDate());
                request.addBatch();
            }
            SqlerTemplate.batchUpdate(request);
        }
    }

    @Override
    public void batchUpdateMergeSkuTask(List<MergeSkuTask> entityList) {
        if (entityList != null && !entityList.isEmpty()) {
            SqlerRequest request = new SqlerRequest("updateMergeSkuTaskByPrimaryKey");
            for (MergeSkuTask entity : entityList) {
                request.addBatchDataParam(MergeSkuTaskDBField.ID, DataType.INT, entity.getId());
                
                request.addBatchDataParam(MergeSkuTaskDBField.TASK_NO, DataType.STRING, entity.getTaskNo());
                request.addBatchDataParam(MergeSkuTaskDBField.STATUS, DataType.INT, entity.getStatus());
                request.addBatchDataParam(MergeSkuTaskDBField.IS_PRINTING, DataType.INT, entity.getIsPrinting());
                request.addBatchDataParam(MergeSkuTaskDBField.RECEIVE_PERSON, DataType.INT, entity.getReceivePerson());
                request.addBatchDataParam(MergeSkuTaskDBField.RECEIVE_DATE, DataType.TIMESTAMP, entity.getReceiveDate());
                request.addBatchDataParam(MergeSkuTaskDBField.UP_USER, DataType.INT, entity.getUpUser());
                request.addBatchDataParam(MergeSkuTaskDBField.UP_DATE, DataType.TIMESTAMP, entity.getUpDate());
                
                request.addBatchDataParam(MergeSkuTaskDBField.CREATED_DATE, DataType.TIMESTAMP, entity.getCreatedDate());
                request.addBatchDataParam(MergeSkuTaskDBField.LAST_UPDATE_DATE, DataType.TIMESTAMP, new Timestamp(System.currentTimeMillis()));
                request.addBatchDataParam(MergeSkuTaskDBField.LAST_UPDATE_BY, DataType.INT, entity.getLastUpdateBy());
                request.addBatchDataParam(MergeSkuTaskDBField.PICKING_END_DATE, DataType.TIMESTAMP, entity.getPickingEndDate());
                request.addBatch();
            }
            SqlerTemplate.batchUpdate(request);
        }
    }

    @Override
    public void deleteMergeSkuTask(Integer primaryKey) {
        SqlerRequest request = new SqlerRequest("deleteMergeSkuTaskByPrimaryKey");
        request.addDataParam(MergeSkuTaskDBField.ID, DataType.INT, primaryKey);
        SqlerTemplate.execute(request);
    }
}