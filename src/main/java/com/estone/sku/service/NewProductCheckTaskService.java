package com.estone.sku.service;

import java.util.List;

import com.estone.android.domain.AndroidProductDo;
import com.estone.common.util.model.ApiResult;
import com.estone.sku.bean.NewProductCheckTask;
import com.estone.sku.bean.NewProductCheckTaskQueryCondition;
import com.whq.tool.component.Pager;
import com.whq.tool.json.ResponseJson;

public interface NewProductCheckTaskService {
    List<NewProductCheckTask> queryAllNewProductCheckTasks();

    List<NewProductCheckTask> queryNewProductCheckTasks(NewProductCheckTaskQueryCondition query, Pager pager);

    NewProductCheckTask getNewProductCheckTask(Integer id);

    NewProductCheckTask getNewProductCheckTaskDetail(Integer id);

    NewProductCheckTask queryNewProductCheckTask(NewProductCheckTaskQueryCondition query);

    void createNewProductCheckTask(NewProductCheckTask newProductCheckTask);

    void batchCreateNewProductCheckTask(List<NewProductCheckTask> entityList);

    void deleteNewProductCheckTask(Integer id);

    void updateNewProductCheckTask(NewProductCheckTask newProductCheckTask);

    void batchUpdateNewProductCheckTask(List<NewProductCheckTask> entityList);

    ApiResult<?> doDiscard(NewProductCheckTaskQueryCondition query);

    ResponseJson doNextStep(AndroidProductDo domain);
}