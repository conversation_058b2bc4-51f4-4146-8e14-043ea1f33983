package com.estone.sku.service;

import com.estone.sku.bean.WhSku;
import com.estone.sku.bean.WhSkuStandardSize;
import com.estone.sku.bean.WhSkuStandardSizeQueryCondition;
import com.whq.tool.component.Pager;

import java.util.List;

public interface WhSkuStandardSizeService {

    List<WhSkuStandardSize> queryWhSkuStandardSizes(WhSkuStandardSizeQueryCondition query, Pager pager);

    void createWhSkuStandardSize(WhSkuStandardSize whSkuStandardSize);

    void setStandardSizeChangeCount(List<WhSku> whSkus);
}