package com.estone.sku.service;

import com.estone.sku.bean.AfterSaleSettlementItem;
import com.estone.sku.bean.AfterSaleSettlementItemQueryCondition;
import com.whq.tool.component.Pager;
import java.util.List;

public interface AfterSaleSettlementItemService {
    List<AfterSaleSettlementItem> queryAllAfterSaleSettlementItems();

    List<AfterSaleSettlementItem> queryAfterSaleSettlementItems(AfterSaleSettlementItemQueryCondition query, Pager pager);

    AfterSaleSettlementItem getAfterSaleSettlementItem(Integer id);

    AfterSaleSettlementItem getAfterSaleSettlementItemDetail(Integer id);

    AfterSaleSettlementItem queryAfterSaleSettlementItem(AfterSaleSettlementItemQueryCondition query);

    void createAfterSaleSettlementItem(AfterSaleSettlementItem afterSaleSettlementItem);

    void batchCreateAfterSaleSettlementItem(List<AfterSaleSettlementItem> entityList);

    void deleteAfterSaleSettlementItem(Integer id);

    void updateAfterSaleSettlementItem(AfterSaleSettlementItem afterSaleSettlementItem);

    void batchUpdateAfterSaleSettlementItem(List<AfterSaleSettlementItem> entityList);
}