package com.estone.sku.service;

import com.estone.sku.bean.WhSkuSaleStatisticRecord;
import com.estone.sku.bean.WhSkuSaleStatisticRecordQueryCondition;
import com.whq.tool.component.Pager;
import java.util.List;
import java.util.Map;

public interface WhSkuSaleStatisticRecordService {
    List<WhSkuSaleStatisticRecord> queryAllWhSkuSaleStatisticRecords();

    List<WhSkuSaleStatisticRecord> queryWhSkuSaleStatisticRecords(WhSkuSaleStatisticRecordQueryCondition query, Pager pager);

    WhSkuSaleStatisticRecord getWhSkuSaleStatisticRecord(Integer id);

    WhSkuSaleStatisticRecord getWhSkuSaleStatisticRecordDetail(Integer id);

    WhSkuSaleStatisticRecord queryWhSkuSaleStatisticRecord(WhSkuSaleStatisticRecordQueryCondition query);

    void createWhSkuSaleStatisticRecord(WhSkuSaleStatisticRecord whSkuSaleStatisticRecord);

    void batchCreateWhSkuSaleStatisticRecord(List<WhSkuSaleStatisticRecord> entityList);

    void deleteWhSkuSaleStatisticRecord(Integer id);

    void updateWhSkuSaleStatisticRecord(WhSkuSaleStatisticRecord whSkuSaleStatisticRecord);

    void batchUpdateWhSkuSaleStatisticRecord(List<WhSkuSaleStatisticRecord> entityList);

    Map<Integer,String> syncSkuSaleStatisticRecord(List<String> allSkuList,Map<String, String> marketableMap);

    void batchInsertOrUpdate(List<WhSkuSaleStatisticRecord> entityList);

    void syncSkuTtyOrderFrequency() throws Exception;


    /**
     * 查询sku销售属性集合
     *
     * @param query
     * @return
     */
    Map<String, Object> getSkuSaleAttribute(WhSkuSaleStatisticRecordQueryCondition query);

    List<WhSkuSaleStatisticRecord> queryWhSkuBySkus(WhSkuSaleStatisticRecordQueryCondition query);

    void retrySyncSkuSaleStatisticRecord(List<String> skus);
}