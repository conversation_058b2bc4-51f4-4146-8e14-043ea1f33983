package com.estone.sku.service;

import com.estone.sku.bean.UniqueSkuExpRelation;
import com.estone.sku.bean.UniqueSkuExpRelationQueryCondition;
import com.whq.tool.component.Pager;
import java.util.List;

public interface UniqueSkuExpRelationService {
    List<UniqueSkuExpRelation> queryAllUniqueSkuExpRelations();

    List<UniqueSkuExpRelation> queryUniqueSkuExpRelations(UniqueSkuExpRelationQueryCondition query, Pager pager);

    UniqueSkuExpRelation getUniqueSkuExpRelation(Integer id);

    UniqueSkuExpRelation getUniqueSkuExpRelationDetail(Integer id);

    UniqueSkuExpRelation queryUniqueSkuExpRelation(UniqueSkuExpRelationQueryCondition query);

    void createUniqueSkuExpRelation(UniqueSkuExpRelation uniqueSkuExpRelation);

    void batchCreateUniqueSkuExpRelation(List<UniqueSkuExpRelation> entityList);

    void deleteUniqueSkuExpRelation(Integer id);

    void updateUniqueSkuExpRelation(UniqueSkuExpRelation uniqueSkuExpRelation);

    void batchUpdateUniqueSkuExpRelation(List<UniqueSkuExpRelation> entityList);
}