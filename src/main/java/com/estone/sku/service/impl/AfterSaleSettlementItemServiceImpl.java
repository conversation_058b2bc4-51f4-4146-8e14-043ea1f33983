package com.estone.sku.service.impl;

import com.estone.sku.bean.AfterSaleSettlementItem;
import com.estone.sku.bean.AfterSaleSettlementItemQueryCondition;
import com.estone.sku.dao.AfterSaleSettlementItemDao;
import com.estone.sku.service.AfterSaleSettlementItemService;
import com.whq.tool.component.Pager;
import com.whq.tool.exception.BusinessException;
import com.whq.tool.exception.DBErrorCode;
import com.whq.tool.sqler.SqlerException;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

@Service("afterSaleSettlementItemService")
@Slf4j
public class AfterSaleSettlementItemServiceImpl implements AfterSaleSettlementItemService {
    @Resource
    private AfterSaleSettlementItemDao afterSaleSettlementItemDao;

    @Override
    public AfterSaleSettlementItem getAfterSaleSettlementItem(Integer id) {
        AfterSaleSettlementItem afterSaleSettlementItem = afterSaleSettlementItemDao.queryAfterSaleSettlementItem(id);
        return afterSaleSettlementItem;
    }

    @Override
    public AfterSaleSettlementItem getAfterSaleSettlementItemDetail(Integer id) {
        AfterSaleSettlementItem afterSaleSettlementItem = afterSaleSettlementItemDao.queryAfterSaleSettlementItem(id);
        // 关联查询
        return afterSaleSettlementItem;
    }

    @Override
    public AfterSaleSettlementItem queryAfterSaleSettlementItem(AfterSaleSettlementItemQueryCondition query) {
        Assert.notNull(query, "query is null!");
        AfterSaleSettlementItem afterSaleSettlementItem = afterSaleSettlementItemDao.queryAfterSaleSettlementItem(query);
        return afterSaleSettlementItem;
    }

    @Override
    public List<AfterSaleSettlementItem> queryAllAfterSaleSettlementItems() {
        return afterSaleSettlementItemDao.queryAfterSaleSettlementItemList();
    }

    @Override
    public List<AfterSaleSettlementItem> queryAfterSaleSettlementItems(AfterSaleSettlementItemQueryCondition query, Pager pager) {
        Assert.notNull(query, "query is null!");
        if (pager != null && pager.isQueryCount()) {
            int count = afterSaleSettlementItemDao.queryAfterSaleSettlementItemCount(query);
            pager.setTotalCount(count);
            if ( count == 0) {
                return new ArrayList<AfterSaleSettlementItem>();
            }
        }
        List<AfterSaleSettlementItem> afterSaleSettlementItems = afterSaleSettlementItemDao.queryAfterSaleSettlementItemList(query, pager);
        return afterSaleSettlementItems;
    }

    @Override
    public void createAfterSaleSettlementItem(AfterSaleSettlementItem afterSaleSettlementItem) {
        try {
            afterSaleSettlementItemDao.createAfterSaleSettlementItem(afterSaleSettlementItem);
        }
        catch (SqlerException e) {
            log.error(e.getMessage(), e);
            throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
        }
    }

    @Override
    public void batchCreateAfterSaleSettlementItem(List<AfterSaleSettlementItem> entityList) {
        if (CollectionUtils.isNotEmpty(entityList)) {
            try {
                afterSaleSettlementItemDao.batchCreateAfterSaleSettlementItem(entityList);
            }
            catch (SqlerException e) {
                log.error(e.getMessage(), e);
                throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
            }
        }
    }

    @Override
    public void deleteAfterSaleSettlementItem(Integer id) {
        try {
            afterSaleSettlementItemDao.deleteAfterSaleSettlementItem(id);
        }
        catch (SqlerException e) {
            log.error(e.getMessage(), e);
            throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
        }
    }

    @Override
    public void updateAfterSaleSettlementItem(AfterSaleSettlementItem afterSaleSettlementItem) {
        try {
            afterSaleSettlementItemDao.updateAfterSaleSettlementItem(afterSaleSettlementItem);
        }
        catch (SqlerException e) {
            log.error(e.getMessage(), e);
            throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
        }
    }

    @Override
    public void batchUpdateAfterSaleSettlementItem(List<AfterSaleSettlementItem> entityList) {
        if (CollectionUtils.isNotEmpty(entityList)) {
            try {
                afterSaleSettlementItemDao.batchUpdateAfterSaleSettlementItem(entityList);
            }
            catch (SqlerException e) {
                log.error(e.getMessage(), e);
                throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
            }
        }
    }
}