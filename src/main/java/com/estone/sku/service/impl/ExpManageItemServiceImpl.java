package com.estone.sku.service.impl;

import com.estone.sku.bean.ExpManageItem;
import com.estone.sku.bean.ExpManageItemQueryCondition;
import com.estone.sku.dao.ExpManageItemDao;
import com.estone.sku.service.ExpManageItemService;
import com.whq.tool.component.Pager;
import com.whq.tool.exception.BusinessException;
import com.whq.tool.exception.DBErrorCode;
import com.whq.tool.sqler.SqlerException;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

@Service("expManageItemService")
@Slf4j
public class ExpManageItemServiceImpl implements ExpManageItemService {
    @Resource
    private ExpManageItemDao expManageItemDao;

    @Override
    public ExpManageItem getExpManageItem(Integer id) {
        ExpManageItem expManageItem = expManageItemDao.queryExpManageItem(id);
        return expManageItem;
    }

    @Override
    public ExpManageItem getExpManageItemDetail(Integer id) {
        ExpManageItem expManageItem = expManageItemDao.queryExpManageItem(id);
        // 关联查询
        return expManageItem;
    }

    @Override
    public ExpManageItem queryExpManageItem(ExpManageItemQueryCondition query) {
        Assert.notNull(query, "query is null!");
        ExpManageItem expManageItem = expManageItemDao.queryExpManageItem(query);
        return expManageItem;
    }

    @Override
    public List<ExpManageItem> queryAllExpManageItems() {
        return expManageItemDao.queryExpManageItemList();
    }

    @Override
    public List<ExpManageItem> queryExpManageItems(ExpManageItemQueryCondition query, Pager pager) {
        Assert.notNull(query, "query is null!");
        if (pager != null && pager.isQueryCount()) {
            int count = expManageItemDao.queryExpManageItemCount(query);
            pager.setTotalCount(count);
            if ( count == 0) {
                return new ArrayList<ExpManageItem>();
            }
        }
        List<ExpManageItem> expManageItems = expManageItemDao.queryExpManageItemList(query, pager);
        return expManageItems;
    }

    @Override
    public void createExpManageItem(ExpManageItem expManageItem) {
        try {
            expManageItemDao.createExpManageItem(expManageItem);
        }
        catch (SqlerException e) {
            log.error(e.getMessage(), e);
            throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
        }
    }

    @Override
    public void batchCreateExpManageItem(List<ExpManageItem> entityList) {
        if (CollectionUtils.isNotEmpty(entityList)) {
            try {
                expManageItemDao.batchCreateExpManageItem(entityList);
            }
            catch (SqlerException e) {
                log.error(e.getMessage(), e);
                throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
            }
        }
    }

    @Override
    public void deleteExpManageItem(Integer id) {
        try {
            expManageItemDao.deleteExpManageItem(id);
        }
        catch (SqlerException e) {
            log.error(e.getMessage(), e);
            throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
        }
    }

    @Override
    public void updateExpManageItem(ExpManageItem expManageItem) {
        try {
            expManageItemDao.updateExpManageItem(expManageItem);
        }
        catch (SqlerException e) {
            log.error(e.getMessage(), e);
            throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
        }
    }

    @Override
    public void batchUpdateExpManageItem(List<ExpManageItem> entityList) {
        if (CollectionUtils.isNotEmpty(entityList)) {
            try {
                expManageItemDao.batchUpdateExpManageItem(entityList);
            }
            catch (SqlerException e) {
                log.error(e.getMessage(), e);
                throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
            }
        }
    }
}