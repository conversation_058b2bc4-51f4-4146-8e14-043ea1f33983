package com.estone.sku.service.impl;

import com.estone.sku.bean.SkuTagInfo;
import com.estone.sku.bean.SkuTagInfoQueryCondition;
import com.estone.sku.dao.SkuTagInfoDao;
import com.estone.sku.service.SkuTagInfoService;
import com.whq.tool.component.Pager;
import com.whq.tool.exception.BusinessException;
import com.whq.tool.exception.DBErrorCode;
import com.whq.tool.sqler.SqlerException;

import java.util.*;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

@Service("skuTagInfoService")
@Slf4j
public class SkuTagInfoServiceImpl implements SkuTagInfoService {
    @Resource
    private SkuTagInfoDao skuTagInfoDao;

    @Override
    public SkuTagInfo getSkuTagInfo(Integer id) {
        SkuTagInfo skuTagInfo = skuTagInfoDao.querySkuTagInfo(id);
        return skuTagInfo;
    }

    @Override
    public SkuTagInfo getSkuTagInfoDetail(Integer id) {
        SkuTagInfo skuTagInfo = skuTagInfoDao.querySkuTagInfo(id);
        // 关联查询
        return skuTagInfo;
    }

    @Override
    public SkuTagInfo querySkuTagInfo(SkuTagInfoQueryCondition query) {
        Assert.notNull(query, "query is null!");
        SkuTagInfo skuTagInfo = skuTagInfoDao.querySkuTagInfo(query);
        return skuTagInfo;
    }

    @Override
    public List<SkuTagInfo> queryAllSkuTagInfos() {
        return skuTagInfoDao.querySkuTagInfoList();
    }

    @Override
    public List<SkuTagInfo> querySkuTagInfos(SkuTagInfoQueryCondition query, Pager pager) {
        Assert.notNull(query, "query is null!");
        if (pager != null && pager.isQueryCount()) {
            int count = skuTagInfoDao.querySkuTagInfoCount(query);
            pager.setTotalCount(count);
            if ( count == 0) {
                return new ArrayList<SkuTagInfo>();
            }
        }
        List<SkuTagInfo> skuTagInfos = skuTagInfoDao.querySkuTagInfoList(query, pager);
        return skuTagInfos;
    }

    @Override
    public void createSkuTagInfo(SkuTagInfo skuTagInfo) {
        try {
            skuTagInfoDao.createSkuTagInfo(skuTagInfo);
        }
        catch (SqlerException e) {
            log.error(e.getMessage(), e);
            throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
        }
    }

    @Override
    public void batchCreateSkuTagInfo(List<SkuTagInfo> entityList) {
        if (CollectionUtils.isNotEmpty(entityList)) {
            try {
                skuTagInfoDao.batchCreateSkuTagInfo(entityList);
            }
            catch (SqlerException e) {
                log.error(e.getMessage(), e);
                throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
            }
        }
    }

    @Override
    public void deleteSkuTagInfo(Integer id) {
        try {
            skuTagInfoDao.deleteSkuTagInfo(id);
        }
        catch (SqlerException e) {
            log.error(e.getMessage(), e);
            throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
        }
    }

    @Override
    public void updateSkuTagInfo(SkuTagInfo skuTagInfo) {
        try {
            skuTagInfoDao.updateSkuTagInfo(skuTagInfo);
        }
        catch (SqlerException e) {
            log.error(e.getMessage(), e);
            throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
        }
    }

    @Override
    public void batchUpdateSkuTagInfo(List<SkuTagInfo> entityList) {
        if (CollectionUtils.isNotEmpty(entityList)) {
            try {
                skuTagInfoDao.batchUpdateSkuTagInfo(entityList);
            }
            catch (SqlerException e) {
                log.error(e.getMessage(), e);
                throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
            }
        }
    }

    /**
     * 批量保存更新标签属性
     * @param tagInfoMap
     */
    @Override
    public void batchSaveTagInfo(Map<String, String> tagInfoMap) {
        if (MapUtils.isEmpty(tagInfoMap))
            return;
        SkuTagInfoQueryCondition query = new SkuTagInfoQueryCondition();
        query.setSkuList(new ArrayList<>(tagInfoMap.keySet()));
        List<SkuTagInfo> skuTagInfos = skuTagInfoDao.querySkuTagInfoList(query, null);
        Map<String, SkuTagInfo> existMap = Optional.ofNullable(skuTagInfos).orElse(new ArrayList<>()).stream()
                .collect(Collectors.toMap(SkuTagInfo::getSku, t -> t));
        List<SkuTagInfo> addList = new ArrayList<>();
        List<SkuTagInfo> updateList = new ArrayList<>();
        tagInfoMap.forEach((sku, tagJson) -> {
            SkuTagInfo skuTagInfo = existMap.get(sku);
            if (skuTagInfo == null) {
                skuTagInfo = new SkuTagInfo();
                skuTagInfo.setSku(sku);
                skuTagInfo.setTagJson(tagJson);
                addList.add(skuTagInfo);
            }
            else if (!StringUtils.equals(skuTagInfo.getTagJson(), tagJson)) {
                skuTagInfo.setTagJson(tagJson);
                updateList.add(skuTagInfo);
            }
        });
        batchUpdateSkuTagInfo(updateList);
        batchCreateSkuTagInfo(addList);
    }
}