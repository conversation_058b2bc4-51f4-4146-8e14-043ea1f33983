package com.estone.sku.service.impl;

import com.alibaba.fastjson.JSON;
import com.estone.android.PdaExceptionCode;
import com.estone.android.domain.AndroidVerifySkuWeightDo;
import com.estone.common.enums.LogModule;
import com.estone.common.util.ComparatorUtils;
import com.estone.common.util.CreateTaskNoUtils;
import com.estone.common.util.SystemLogUtils;
import com.estone.sku.bean.*;
import com.estone.sku.dao.VerifySkuWeightTaskDao;
import com.estone.sku.enums.VerifySkuFrom;
import com.estone.sku.enums.VerifySkuStatus;
import com.estone.sku.enums.VerifySkuWeightTaskStatus;
import com.estone.sku.service.*;
import com.estone.warehouse.bean.InventoryQueryResult;
import com.estone.warehouse.bean.WhStock;
import com.estone.warehouse.bean.WhStockQueryCondition;
import com.estone.warehouse.service.WhStockService;
import com.whq.tool.component.Pager;
import com.whq.tool.component.StatusCode;
import com.whq.tool.context.DataContextHolder;
import com.whq.tool.exception.BusinessException;
import com.whq.tool.exception.DBErrorCode;
import com.whq.tool.json.ResponseJson;
import com.whq.tool.sqler.SqlerException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.util.*;
import java.util.stream.Collectors;

@Service("verifySkuWeightTaskService")
@Slf4j
public class VerifySkuWeightTaskServiceImpl implements VerifySkuWeightTaskService {

    final static SystemLogUtils SKUWEIGHINGTASKLOG = SystemLogUtils.create(LogModule.SKU_WEIGHING_TASK.getCode());

    private static Logger logger = LoggerFactory.getLogger(VerifySkuWeightTaskServiceImpl.class);

    @Resource
    private VerifySkuWeightTaskDao verifySkuWeightTaskDao;

    @Resource
    private VerifySkuWeightTaskItemService verifySkuWeightTaskItemService;

    @Resource
    private VerifySkuWeightService verifySkuWeightService;

    @Resource
    private WhSkuService whSkuService;

    @Resource
    private WhStockService whStockService;

    @Resource
    private LocationMoveInfoService locationMoveInfoService;

    @Override
    public VerifySkuWeightTask getVerifySkuWeightTask(Integer id) {
        VerifySkuWeightTask verifySkuWeightTask = verifySkuWeightTaskDao.queryVerifySkuWeightTask(id);
        return verifySkuWeightTask;
    }

    @Override
    public VerifySkuWeightTask getVerifySkuWeightTaskDetail(Integer id) {
        VerifySkuWeightTask verifySkuWeightTask = verifySkuWeightTaskDao.queryVerifySkuWeightTask(id);
        // 关联查询
        return verifySkuWeightTask;
    }

    @Override
    public VerifySkuWeightTask queryVerifySkuWeightTask(VerifySkuWeightTaskQueryCondition query) {
        Assert.notNull(query, "query is null!");
        VerifySkuWeightTask verifySkuWeightTask = verifySkuWeightTaskDao.queryVerifySkuWeightTask(query);
        return verifySkuWeightTask;
    }

    @Override
    public List<VerifySkuWeightTask> queryAllVerifySkuWeightTasks() {
        return verifySkuWeightTaskDao.queryVerifySkuWeightTaskList();
    }

    @Override
    public List<VerifySkuWeightTask> queryVerifySkuWeightTasks(VerifySkuWeightTaskQueryCondition query, Pager pager) {
        Assert.notNull(query, "query is null!");
        if (pager != null && pager.isQueryCount()) {
            int count = verifySkuWeightTaskDao.queryVerifySkuWeightTaskCount(query);
            pager.setTotalCount(count);
            if (count == 0) {
                return new ArrayList<VerifySkuWeightTask>();
            }
        }
        List<VerifySkuWeightTask> verifySkuWeightTasks = verifySkuWeightTaskDao.queryVerifySkuWeightTaskList(query,
                pager);
        return verifySkuWeightTasks;
    }

    @Override
    public int queryVerifySkuWeightTaskAndItemCount(VerifySkuWeightTaskQueryCondition query) {
        Assert.notNull(query, "query is null!");
        return verifySkuWeightTaskDao.queryVerifySkuWeightTaskAndItemCount(query);
    }

    @Override
    public List<VerifySkuWeightTask> queryVerifySkuWeightTaskAndItems(VerifySkuWeightTaskQueryCondition query,
                                                                      Pager pager) {
        Assert.notNull(query, "query is null!");
        if (pager != null && pager.isQueryCount()) {
            int count = verifySkuWeightTaskDao.queryVerifySkuWeightTaskAndItemCount(query);
            pager.setTotalCount(count);
            if (count == 0) {
                return new ArrayList<VerifySkuWeightTask>();
            }
        }
        List<VerifySkuWeightTask> verifySkuWeightTasks = verifySkuWeightTaskDao
                .queryVerifySkuWeightTaskAndItemList(query, pager);
        return verifySkuWeightTasks;
    }

    @Override
    public void createVerifySkuWeightTask(VerifySkuWeightTask verifySkuWeightTask) {
        try {
            verifySkuWeightTaskDao.createVerifySkuWeightTask(verifySkuWeightTask);
        } catch (SqlerException e) {
            log.error(e.getMessage(), e);
            throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
        }
    }

    @Override
    public void batchCreateVerifySkuWeightTask(List<VerifySkuWeightTask> entityList) {
        if (CollectionUtils.isNotEmpty(entityList)) {
            try {
                verifySkuWeightTaskDao.batchCreateVerifySkuWeightTask(entityList);
            } catch (SqlerException e) {
                log.error(e.getMessage(), e);
                throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
            }
        }
    }

    @Override
    public void deleteVerifySkuWeightTask(Integer id) {
        try {
            verifySkuWeightTaskDao.deleteVerifySkuWeightTask(id);
        } catch (SqlerException e) {
            log.error(e.getMessage(), e);
            throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
        }
    }

    @Override
    public void updateVerifySkuWeightTask(VerifySkuWeightTask verifySkuWeightTask) {
        try {
            verifySkuWeightTaskDao.updateVerifySkuWeightTask(verifySkuWeightTask);
        } catch (SqlerException e) {
            log.error(e.getMessage(), e);
            throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
        }
    }

    @Override
    public void batchUpdateVerifySkuWeightTask(List<VerifySkuWeightTask> entityList) {
        if (CollectionUtils.isNotEmpty(entityList)) {
            try {
                verifySkuWeightTaskDao.batchUpdateVerifySkuWeightTask(entityList);
            } catch (SqlerException e) {
                log.error(e.getMessage(), e);
                throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
            }
        }
    }

    @Override
    public ResponseJson checkToCreateVerifySkuWeightTask(List<String> skus, List<WhSku> whSkuList, Integer locationCount) {
        ResponseJson response = new ResponseJson(StatusCode.FAIL);
        if (CollectionUtils.isEmpty(skus) || CollectionUtils.isEmpty(whSkuList)) {
            response.setMessage("sku参数为空！");
            return response;
        }
        // 称重任务中待审核和核重中的sku
        VerifySkuWeightTaskItemQueryCondition query = new VerifySkuWeightTaskItemQueryCondition();
        query.setSkus(skus);
        query.setQueryDiscardAndFinish(false);
        List<VerifySkuWeightTaskItem> verifySkuWeightTaskItemList = verifySkuWeightTaskItemService
                .queryVerifySkuWeightTaskItems(query, null);

        // 待审核的sku
        VerifySkuWeightQueryCondition queryCondition = new VerifySkuWeightQueryCondition();
        queryCondition.setSkus(skus);
        queryCondition.setStatus(VerifySkuStatus.WAITING_FOR_VERIFY.intCode());
        List<VerifySkuWeight> verifySkuWeightList = verifySkuWeightService.queryVerifySkuWeights(queryCondition, null);

        String existSkus = "";
        if (CollectionUtils.isNotEmpty(verifySkuWeightTaskItemList)) {
            for (VerifySkuWeightTaskItem item : verifySkuWeightTaskItemList) {
                existSkus += item.getSku() + ",";
            }
        }

        if (CollectionUtils.isNotEmpty(verifySkuWeightList)) {
            for (VerifySkuWeight verifySkuWeight : verifySkuWeightList) {
                existSkus += verifySkuWeight.getSku() + ",";
            }
        }

        if (StringUtils.isNotBlank(existSkus)) {
            response.setMessage("SKU： " + existSkus + "不能重复生成称重任务！");
            return response;
        }

        // 库位排序
        ComparatorUtils.sortByLocation(whSkuList);
        // 货位的map
        LinkedHashMap<String, List<WhSku>> locationMap = new LinkedHashMap<>();

        for (WhSku whSku : whSkuList) {
            String locationNumber = whSku.getLocationNumber();
            List<WhSku> whSkus = locationMap.get(locationNumber);
            if (whSkus == null) {
                whSkus = new ArrayList<>();
            }
            whSkus.add(whSku);
            locationMap.put(locationNumber, whSkus);
        }

        int nowPage = 0;
        List<WhSku> tempObj = new ArrayList<>();
        String message = "";
        for (Map.Entry<String, List<WhSku>> entry : locationMap.entrySet()) {
            List<WhSku> strObj = entry.getValue();

            if (nowPage == locationCount) {
                ResponseJson res = createVerifySkuTask(tempObj);
                message += res.getMessage();
                nowPage = 0;
                tempObj.clear();
                if (StatusCode.FAIL.equals(res.getStatus())) {
                    response.setMessage(message);
                    return response;
                }
            }
            nowPage++;
            tempObj.addAll(strObj);
        }

        if (CollectionUtils.isNotEmpty(tempObj)) {
            ResponseJson res = createVerifySkuTask(tempObj);
            message += res.getMessage();
            if (StatusCode.FAIL.equals(res.getStatus())) {
                response.setMessage(message);
                return response;
            }
        }
        response.setMessage(message);
        response.setStatus(StatusCode.SUCCESS);
        return response;
    }

    @Override
    public ResponseJson createVerifySkuTaskByWhSkuList(List<WhSku> whSkuList) {
        return createVerifySkuTask(whSkuList);
    }

    @Override
    public ResponseJson createVerifySkuTaskByVerifyMap(Map<WhSku, Integer> verifySku) {
        ResponseJson response = new ResponseJson(StatusCode.FAIL);

        List<String> skuList = Optional.ofNullable(verifySku.keySet()).orElse(new HashSet<>()).stream().map(WhSku::getSku).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(skuList)) {
            return response;
        }
        WhStockQueryCondition whStockQueryCondition = new WhStockQueryCondition();

        whStockQueryCondition.setSkus(skuList);

        List<WhStock> whStocks = whStockService.queryPageWhStocks(whStockQueryCondition, null);

        // 获取虚拟库位
        List<String> virtualLocationList = Optional.ofNullable(locationMoveInfoService.getVirtualLocationList()).orElse(new ArrayList<>());

        List<String> skus = Optional.ofNullable(whStocks).orElse(new ArrayList<>()).stream()
                .filter(s -> !virtualLocationList.contains(s.getLocationNumber()) && !StringUtils.startsWithIgnoreCase(s.getLocationNumber(),"ZF"))
                .filter(s -> s.getTotalLocationQuantity() == null || s.getTotalLocationQuantity() == 0).map(WhStock::getSku).collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(skus)) {
            response.setMessage(String.format("SKU: %s 库存为零,不能生成称重任务", skus));
            return response;
        }

        List<String> nonVirtualOrZf = Optional.ofNullable(whStocks).orElse(new ArrayList<>()).stream()
                .filter(s -> StringUtils.isNotBlank(s.getLocationNumber()) && !virtualLocationList.contains(s.getLocationNumber()) && !StringUtils.startsWithIgnoreCase(s.getLocationNumber(),"ZF"))
                .map(WhStock::getSku).distinct().collect(Collectors.toList());
        skuList.removeAll(nonVirtualOrZf);
        if (CollectionUtils.isNotEmpty(skuList)) {
            response.setMessage(String.format("SKU: %s 库存为零,不能生成称重任务", skuList));
            return response;
        }

        Integer maxLevel = verifySkuWeightTaskDao.queryVerifySkuWeightTaskMaxLevel();
        if (maxLevel == null) {
            maxLevel = 0;
        }

        VerifySkuWeightTask task = new VerifySkuWeightTask();
        String taskNo = CreateTaskNoUtils.createVerifySkuWeightTaskNo();
        task.setTaskNo(taskNo);
        task.setTaskLevel(maxLevel + 1);// 新生成默认置顶
        task.setTaskStatus(VerifySkuWeightTaskStatus.WAITING_FOR_RECEIVE.intCode());// 待领取
        verifySkuWeightTaskDao.createVerifySkuWeightTask(task);
        if (Objects.isNull(task.getId())) {
            response.setMessage("创建称重任务失败");
            return response;
        }
        List<VerifySkuWeightTaskItem> taskItems = new ArrayList<>();

        for (WhSku sku : verifySku.keySet()) {
            VerifySkuWeightTaskItem item = new VerifySkuWeightTaskItem();
            item.setTaskId(task.getId());
            item.setSku(sku.getSku());
            item.setStatus(VerifySkuWeightTaskStatus.WAITING_FOR_RECEIVE.intCode());
            item.setWeight(sku.getWeight());
            item.setGenerateByVerifySkuWeightId(verifySku.get(sku));
            if (StringUtils.equalsIgnoreCase(sku.getSpecification(), "true")) {
                item.setSpecification("是 -> 是");
            } else {
                item.setSpecification("否 -> 否");
            }
            taskItems.add(item);
        }
        verifySkuWeightTaskItemService.batchCreateVerifySkuWeightTaskItem(taskItems);
        SKUWEIGHINGTASKLOG.log(task.getId(), "生成称重任务",
                new String[][]{{"任务号", task.getTaskNo()}, {"SKU数", String.valueOf(taskItems.size())}});
        response.setStatus(StatusCode.SUCCESS);
        response.setMessage("任务号" + task.getTaskNo());
        return response;
    }

    private ResponseJson createVerifySkuTask(List<WhSku> whSkuList) {
        ResponseJson response = new ResponseJson(StatusCode.FAIL);

        List<String> skuList = Optional.ofNullable(whSkuList).orElse(new ArrayList<>()).stream().map(WhSku::getSku).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(skuList)) {
            return response;
        }
        WhStockQueryCondition whStockQueryCondition = new WhStockQueryCondition();

        whStockQueryCondition.setSkus(skuList);

        List<WhStock> whStocks = whStockService.queryPageWhStocks(whStockQueryCondition, null);

        // 获取虚拟库位
        List<String> virtualLocationList = Optional.ofNullable(locationMoveInfoService.getVirtualLocationList()).orElse(new ArrayList<>());

        List<String> skus = Optional.ofNullable(whStocks).orElse(new ArrayList<>()).stream()
                .filter(s -> StringUtils.isNotBlank(s.getLocationNumber()) && !virtualLocationList.contains(s.getLocationNumber()) && !StringUtils.startsWithIgnoreCase(s.getLocationNumber(),"ZF"))
                .filter(s -> s.getTotalLocationQuantity() == null || s.getTotalLocationQuantity() == 0).map(WhStock::getSku).collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(skus)) {
            response.setMessage(String.format("SKU: %s 库存为零,不能生成称重任务", skus));
            return response;
        }

        List<String> nonVirtualOrZf = Optional.ofNullable(whStocks).orElse(new ArrayList<>()).stream()
                .filter(s -> !virtualLocationList.contains(s.getLocationNumber()) && !StringUtils.startsWithIgnoreCase(s.getLocationNumber(),"ZF"))
                .map(WhStock::getSku).distinct().collect(Collectors.toList());
        skuList.removeAll(nonVirtualOrZf);
        if (CollectionUtils.isNotEmpty(skuList)) {
            response.setMessage(String.format("SKU: %s 库存为零,不能生成称重任务", skuList));
            return response;
        }

        Integer maxLevel = verifySkuWeightTaskDao.queryVerifySkuWeightTaskMaxLevel();
        if (maxLevel == null) {
            maxLevel = 0;
        }

        VerifySkuWeightTask task = new VerifySkuWeightTask();
        String taskNo = CreateTaskNoUtils.createVerifySkuWeightTaskNo();
        task.setTaskNo(taskNo);
        task.setTaskLevel(maxLevel + 1);// 新生成默认置顶
        task.setTaskStatus(VerifySkuWeightTaskStatus.WAITING_FOR_RECEIVE.intCode());// 待领取
        verifySkuWeightTaskDao.createVerifySkuWeightTask(task);
        if (task.getId() != null) {
            List<VerifySkuWeightTaskItem> taskItems = new ArrayList<>();

            for (WhSku sku : whSkuList) {
                VerifySkuWeightTaskItem item = new VerifySkuWeightTaskItem();
                item.setTaskId(task.getId());
                item.setSku(sku.getSku());
                item.setStatus(VerifySkuWeightTaskStatus.WAITING_FOR_RECEIVE.intCode());
                item.setWeight(sku.getWeight());
                if (StringUtils.equalsIgnoreCase(sku.getSpecification(), "true")) {
                    item.setSpecification("是 -> 是");
                } else {
                    item.setSpecification("否 -> 否");
                }
                taskItems.add(item);
            }
            verifySkuWeightTaskItemService.batchCreateVerifySkuWeightTaskItem(taskItems);
            SKUWEIGHINGTASKLOG.log(task.getId(), "生成称重任务",
                    new String[][]{{"任务号", task.getTaskNo()}, {"SKU数", String.valueOf(taskItems.size())}});
            response.setStatus(StatusCode.SUCCESS);
            response.setMessage("创建成功：" + task.getTaskNo());
        } else {
            response.setMessage("创建任务失败");
        }
        return response;
    }

    @Override
    public Boolean batchSetToTop(List<Integer> taskIds) {
        if (CollectionUtils.isEmpty(taskIds)) {
            return false;
        }
        Integer maxLevel = verifySkuWeightTaskDao.queryVerifySkuWeightTaskMaxLevel();
        if (maxLevel == null) {
            maxLevel = 0;
        }
        boolean flag = false;
        for (Integer id : taskIds) {
            VerifySkuWeightTask verifySkuWeightTask = new VerifySkuWeightTask();
            verifySkuWeightTask.setId(id);
            verifySkuWeightTask.setTaskLevel(maxLevel + 1);
            verifySkuWeightTaskDao.updateVerifySkuWeightTask(verifySkuWeightTask);
            SKUWEIGHINGTASKLOG.log(id, "称重任务置顶-修改等级=" + (maxLevel + 1), new String[][]{{"原等级", maxLevel + ""}});
            flag = true;
        }
        return flag;
    }

    @Override
    public Boolean discardTask(List<VerifySkuWeightTask> verifySkuWeightTaskList) {
        if (CollectionUtils.isEmpty(verifySkuWeightTaskList)) {
            return false;
        }
        boolean flag = false;

        for (VerifySkuWeightTask task : verifySkuWeightTaskList) {
            VerifySkuWeightTask updateTask = new VerifySkuWeightTask();
            updateTask.setId(task.getId());
            updateTask.setTaskStatus(VerifySkuWeightTaskStatus.DISCARD.intCode());

            int updateCount = verifySkuWeightTaskDao.updateVerifySkuWeightTask(updateTask);

            if (updateCount > 0) {
                List<VerifySkuWeightTaskItem> itemList = task.getVerifySkuWeightTaskItems();
                if (CollectionUtils.isNotEmpty(itemList)) {
                    for (VerifySkuWeightTaskItem item : itemList) {
                        VerifySkuWeightTaskItem updateItem = new VerifySkuWeightTaskItem();
                        updateItem.setId(item.getId());
                        updateItem.setStatus(VerifySkuWeightTaskStatus.DISCARD.intCode());
                        verifySkuWeightTaskItemService.updateVerifySkuWeightTaskItem(updateItem);
                    }
                }
                flag = true;
                SKUWEIGHINGTASKLOG.log(task.getId(), "废弃称重任务-修改状态=" + VerifySkuWeightTaskStatus.DISCARD.getName(),
                        new String[][]{
                                {"原状态", VerifySkuWeightTaskStatus.getNameByCode(task.getTaskStatus().toString())}});
            }

        }
        return flag;
    }

    @Override
    public ResponseJson receiveVerifySkuWeightTask(Integer receiveUser) {
        ResponseJson response = new ResponseJson();
        response.setStatus(StatusCode.FAIL);
        VerifySkuWeightTaskQueryCondition query = new VerifySkuWeightTaskQueryCondition();
        query.setTaskStatus(VerifySkuWeightTaskStatus.VERIFYING.intCode());
        query.setReceiveBy(receiveUser);
        List<VerifySkuWeightTask> tasks = queryVerifySkuWeightTaskAndItems(query, null);
        if (CollectionUtils.isNotEmpty(tasks)) {
            if (CollectionUtils.isNotEmpty(tasks.get(0).getVerifySkuWeightTaskItems())) {
                setLocationAndSort(tasks.get(0).getVerifySkuWeightTaskItems());
                response.setMessage(JSON.toJSONString(tasks.get(0)));
                response.setStatus(StatusCode.SUCCESS);
                return response;
            } else {
                response.setMessage("当前称重的任务明细为空!");
            }
        } else {
            query = new VerifySkuWeightTaskQueryCondition();
            query.setTaskStatus(VerifySkuWeightTaskStatus.WAITING_FOR_RECEIVE.intCode());
            tasks = queryVerifySkuWeightTaskAndItems(query, new Pager(0, 1));
            if (CollectionUtils.isNotEmpty(tasks)
                    && CollectionUtils.isNotEmpty(tasks.get(0).getVerifySkuWeightTaskItems())) {
                try {
                    response = receiveTaskAndUpdateStatus(tasks.get(0), receiveUser);
                } catch (Exception e) {
                    logger.error(e.getMessage(), e);
                    response.setMessage(e.getMessage());
                }
            } else {
                response.setMessage("当前用户没有任务可领");
            }
        }
        return response;
    }

    // 设置可用最大的库位作为称重任务库位
    public void setLocationAndSort(List<VerifySkuWeightTaskItem> verifySkuWeightTaskItems) {
        log.info("setLocationAndSort,verifySkuWeightTaskItems sort before=[" + JSON.toJSONString(verifySkuWeightTaskItems) + "]");
        String skus = verifySkuWeightTaskItems.stream().map(i -> i.getSku()).collect(Collectors.joining(","));
        Map<String, InventoryQueryResult> maxSurplusQtyStock = whStockService.getMaxSurplusQtyStock(skus);
        for (VerifySkuWeightTaskItem item : verifySkuWeightTaskItems) {
            InventoryQueryResult inventoryQueryResult = maxSurplusQtyStock.get(item.getSku());
            if (inventoryQueryResult == null) continue;
            item.setLocation(inventoryQueryResult.getLocation());
        }
        // 返回前按库位排序
        Collections.sort(verifySkuWeightTaskItems);
        log.info("setLocationAndSort,verifySkuWeightTaskItems sort after=[" + JSON.toJSONString(verifySkuWeightTaskItems) + "]");
    }



    /**
     * 领取称重任务-->更新状态
     */
    public ResponseJson receiveTaskAndUpdateStatus(VerifySkuWeightTask task, Integer receiveUser) {
        ResponseJson response = new ResponseJson();
        response.setStatus(StatusCode.FAIL);

        VerifySkuWeightTask updateTask = new VerifySkuWeightTask();
        updateTask.setId(task.getId());
        updateTask.setReceiveBy(receiveUser);
        updateTask.setReceiveDate(new Timestamp(System.currentTimeMillis()));
        updateTask.setTaskStatus(VerifySkuWeightTaskStatus.VERIFYING.intCode());
        int returnInt = verifySkuWeightTaskDao.updateVerifySkuWeightTask(updateTask);

        if (returnInt > 0) {

            List<VerifySkuWeightTaskItem> items = task.getVerifySkuWeightTaskItems();

            List<VerifySkuWeightTaskItem> updateItems = new ArrayList<>();

            for (VerifySkuWeightTaskItem item : items) {
                VerifySkuWeightTaskItem updateItem = new VerifySkuWeightTaskItem();
                updateItem.setId(item.getId());
                updateItem.setStatus(VerifySkuWeightTaskStatus.VERIFYING.intCode());
                updateItems.add(updateItem);

            }

            verifySkuWeightTaskItemService.batchUpdateVerifySkuWeightTaskItem(updateItems);

            SKUWEIGHINGTASKLOG.log(task.getId(), "领取称重任务",
                    new String[][]{
                            {"原状态", VerifySkuWeightTaskStatus.getNameByCode(task.getTaskStatus().toString())},
                            {"变更状态", VerifySkuWeightTaskStatus.VERIFYING.getName()}});
            if (CollectionUtils.isNotEmpty(items)) {
                setLocationAndSort(items);
            }
            response.setMessage(JSON.toJSONString(task));
            response.setStatus(StatusCode.SUCCESS);
            return response;
        } else {
            response.setMessage("领取任务失败，请重新领取！");
        }
        return response;
    }

    /**
     * 下一条
     *
     * @param itemId
     * @param weight
     * @return
     */
    @Override
    public ResponseJson updateVerifySkuWeightTaskItemComplete(AndroidVerifySkuWeightDo domain) {

        Integer taskId = domain.getTaskId();
        Integer itemId = domain.getTaskItemId();
        Double weight = domain.getWeighingWeight();
        String specification = domain.getSpecification();
        Integer packagingAttribute = domain.getPackagingAttribute();
        String updateSpecificationName = null;
        logger.info("updateVerifySkuWeightTaskItemComplete,itemId=[" + itemId + "],weight=[" + weight + "]");
        ResponseJson responseJson = new ResponseJson(StatusCode.FAIL);
        VerifySkuWeightTaskQueryCondition query = new VerifySkuWeightTaskQueryCondition();
        query.setId(taskId);
        List<VerifySkuWeightTask> tasks = queryVerifySkuWeightTaskAndItems(query, null);

        if (CollectionUtils.isEmpty(tasks)) {
            responseJson.setMessage("任务不存在！");
            return responseJson;
        }
        VerifySkuWeightTask task = tasks.get(0);
        if (task.getTaskStatus().equals(VerifySkuWeightTaskStatus.DISCARD.intCode())) {
            responseJson.setMessage("该任务已废弃！");
            return responseJson;
        }
        if (task.getTaskStatus().equals(VerifySkuWeightTaskStatus.FINISHED.intCode())) {
            responseJson.setMessage("该任务已完成！");
            return responseJson;
        }

        VerifySkuWeightTaskItem taskItem = null;
        List<VerifySkuWeightTaskItem> taskItemList = task.getVerifySkuWeightTaskItems();
        for (VerifySkuWeightTaskItem item : taskItemList) {
            if (item.getId().equals(itemId)) {
                taskItem = item;
                break;
            }
        }
        if (taskItem == null) {
            responseJson.setMessage("该sku任务明细不存在！");
            return responseJson;
        }
        if (taskItem.getStatus().equals(VerifySkuWeightTaskStatus.FINISHED.intCode())) {
            responseJson.setMessage("该sku已称重！");
            return responseJson;
        }
        if (taskItem.getStatus().equals(VerifySkuWeightTaskStatus.DISCARD.intCode())) {
            responseJson.setMessage("该sku已废弃！");
            return responseJson;
        }

        // 是否不规则
        if (StringUtils.isNotBlank(specification)) {
            if (StringUtils.isNotBlank(taskItem.getSpecification())) {
                String[] specificationArr = StringUtils.split(taskItem.getSpecification(), "->");
                if (specificationArr.length > 1 && StringUtils.isNotBlank(specificationArr[0])) {
                    updateSpecificationName = specificationArr[0] + " -> " + (StringUtils.equalsIgnoreCase(specification, "true") ? "是" : "否");
                }
            } else {
                updateSpecificationName = "null -> " + (StringUtils.equalsIgnoreCase(specification, "true") ? "是" : "否");
            }
        }


        //称重重量传null,表示跳过该sku称重
        if (weight == null) {
            VerifySkuWeightTaskItem updateItem = new VerifySkuWeightTaskItem();
            updateItem.setId(taskItem.getId());
            updateItem.setWeighingBy(DataContextHolder.getUserId());
            updateItem.setWeighingDate(new Timestamp(System.currentTimeMillis()));
            updateItem.setStatus(VerifySkuWeightTaskStatus.FINISHED.intCode());// 称重完成
            updateItem.setSpecification(updateSpecificationName);
            updateItem.setPackagingAttribute(packagingAttribute);
            verifySkuWeightTaskItemService.updateVerifySkuWeightTaskItem(updateItem);
            responseJson.setStatus(StatusCode.SUCCESS);
            return responseJson;
        }
        //称重重量不能大于标准重量
        WhSkuQueryCondition skuQuery = new WhSkuQueryCondition();
        skuQuery.setId(taskItem.getSkuId());
        WhSku whSku = whSkuService.queryWhSku(skuQuery);
        if (weight != null && !weight.equals(whSku.getCustomsWeight()) && whSku.getNetWeight() != null && whSku.getNetWeight() > 0 && weight > whSku.getNetWeight()) {
            responseJson.setMessage("称重重量大于标准重量，请核对后重新输入!");
            responseJson.setExceptionCode(PdaExceptionCode.CLEAR_WEIGHING_WEIGHT);
            responseJson.setStatus(StatusCode.FAIL);
            return responseJson;
        }

        // 更新任务明细称重重量、状态
        VerifySkuWeightTaskItem updateItem = new VerifySkuWeightTaskItem();
        updateItem.setId(taskItem.getId());
        updateItem.setWeighingWeight(weight);
        Double itemWeight = taskItem.getWeight() == null ? 0.0 : taskItem.getWeight();
        updateItem.setWeightDifference((double) Math.round((weight - itemWeight) * 100) / 100);
        updateItem.setWeighingBy(DataContextHolder.getUserId());
        updateItem.setWeighingDate(new Timestamp(System.currentTimeMillis()));
        updateItem.setStatus(VerifySkuWeightTaskStatus.FINISHED.intCode());// 称重完成
        updateItem.setSpecification(updateSpecificationName);
        updateItem.setPackagingAttribute(packagingAttribute);
        verifySkuWeightTaskItemService.updateVerifySkuWeightTaskItem(updateItem);

        //通过之后将标准重量设置为null（定时任务会去重新计算标准重量）
        WhSku sysWhSku = whSkuService.getBySku(taskItem.getSku());

        WhSku updateSku = new WhSku();
        updateSku.setSku(sysWhSku.getSku());
        updateSku.setNetWeight(null);
        whSkuService.updateNetWeightBySku(updateSku);

        String logStr = "置空sku标准重量，修改前=" + sysWhSku.getNetWeight() + "修改后=null";
        SystemLogUtils.SKULOG.log(sysWhSku.getId(), logStr);

        // 生成sku审核条目
        verifySkuWeightService.pdaWeighingSku(taskItem.getId(), taskItem.getSkuId(), weight, specification, packagingAttribute, null, null, VerifySkuFrom.CREATE_VERIFY_TASK.intCode());
        responseJson.setStatus(StatusCode.SUCCESS);
        return responseJson;
    }

    /**
     * 完成称重任务
     *
     * @param taskId
     * @return
     */
    @Override
    public ResponseJson updateVerifySkuWeightTaskComplete(Integer taskId) {
        logger.info("updateVerifySkuWeightTaskComplete, taskId=[" + taskId + "]");
        ResponseJson responseJson = new ResponseJson(StatusCode.FAIL);
        VerifySkuWeightTaskQueryCondition query = new VerifySkuWeightTaskQueryCondition();
        query.setId(taskId);
        List<VerifySkuWeightTask> tasks = queryVerifySkuWeightTaskAndItems(query, null);

        if (CollectionUtils.isEmpty(tasks)) {
            responseJson.setMessage("任务不存在！");
            responseJson.setExceptionCode("1");
            return responseJson;
        }

        VerifySkuWeightTask task = tasks.get(0);
        if (task.getTaskStatus().equals(VerifySkuWeightTaskStatus.DISCARD.intCode())) {
            responseJson.setMessage("该任务已废弃！");
            return responseJson;
        }
        if (task.getTaskStatus().equals(VerifySkuWeightTaskStatus.FINISHED.intCode())) {
            responseJson.setMessage("该任务已完成！");
            return responseJson;
        }

        VerifySkuWeightTask updateTask = new VerifySkuWeightTask();
        updateTask.setId(task.getId());
        updateTask.setTaskStatus(VerifySkuWeightTaskStatus.FINISHED.intCode());
        updateTask.setFinishVerifyBy(DataContextHolder.getUserId());
        updateTask.setFinishVerifyDate(new Timestamp(System.currentTimeMillis()));
        int returnInt = verifySkuWeightTaskDao.updateVerifySkuWeightTask(updateTask);
        if (returnInt > 0) {

            List<VerifySkuWeightTaskItem> items = task.getVerifySkuWeightTaskItems();

            List<VerifySkuWeightTaskItem> updateItems = new ArrayList<>();

            for (VerifySkuWeightTaskItem item : items) {
                VerifySkuWeightTaskItem updateItem = new VerifySkuWeightTaskItem();
                updateItem.setId(item.getId());
                updateItem.setStatus(VerifySkuWeightTaskStatus.FINISHED.intCode());
                // 是否不规则
                updateItem.setSpecification(item.getSpecification());
                updateItems.add(updateItem);
            }

            verifySkuWeightTaskItemService.batchUpdateVerifySkuWeightTaskItem(updateItems);

            SKUWEIGHINGTASKLOG.log(task.getId(), "称重任务核重完成",
                    new String[][]{
                            {"原状态", VerifySkuWeightTaskStatus.getNameByCode(task.getTaskStatus().toString())},
                            {"变更状态", VerifySkuWeightTaskStatus.FINISHED.getName()}});

            responseJson.setStatus(StatusCode.SUCCESS);
            return responseJson;
        } else {
            responseJson.setMessage("完成失败！");
        }
        return responseJson;
    }
}