package com.estone.sku.service.impl;

import com.alibaba.fastjson.JSON;
import com.estone.combineSku.bean.WhCombineSku;
import com.estone.combineSku.bean.WhCombineSkuQueryCondition;
import com.estone.combineSku.service.WhCombineSkuService;
import com.estone.common.util.CacheUtils;
import com.estone.common.util.CompatibleSkuUtils;
import com.estone.common.util.POIUtils;
import com.estone.common.util.model.ResultModel;
import com.estone.sku.bean.*;
import com.estone.sku.dao.MoveFormInfoDao;
import com.estone.sku.service.MoveFormInfoService;
import com.estone.sku.service.WhSkuService;
import com.estone.warehouse.bean.WhStock;
import com.estone.warehouse.bean.WhStockQueryCondition;
import com.estone.warehouse.dao.WhStockDao;
import com.estone.warehouse.service.WhStockService;
import com.whq.tool.component.Pager;
import com.whq.tool.exception.BusinessException;
import com.whq.tool.exception.DBErrorCode;
import com.whq.tool.sqler.SqlerException;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Service("moveFormInfoService")
@Slf4j
public class MoveFormInfoServiceImpl implements MoveFormInfoService {
    @Resource
    private MoveFormInfoDao moveFormInfoDao;
    @Resource
    private WhStockDao whStockDao;
    @Resource
    private WhStockService whStockService;
    @Resource
    private WhSkuService whSkuService;
    @Resource
    private WhCombineSkuService whCombineSkuService;

    @Override
    public MoveFormInfo getMoveFormInfo(Integer id) {
        MoveFormInfo moveFormInfo = moveFormInfoDao.queryMoveFormInfo(id);
        return moveFormInfo;
    }

    @Override
    public MoveFormInfo getMoveFormInfoDetail(Integer id) {
        MoveFormInfo moveFormInfo = moveFormInfoDao.queryMoveFormInfo(id);
        // 关联查询
        return moveFormInfo;
    }

    @Override
    public MoveFormInfo queryMoveFormInfo(MoveFormInfoQueryCondition query) {
        Assert.notNull(query, "query is null!");
        MoveFormInfo moveFormInfo = moveFormInfoDao.queryMoveFormInfo(query);
        return moveFormInfo;
    }

    @Override
    public List<MoveFormInfo> queryAllMoveFormInfos() {
        return moveFormInfoDao.queryMoveFormInfoList();
    }

    @Override
    public List<MoveFormInfo> queryMoveFormInfos(MoveFormInfoQueryCondition query, Pager pager) {
        Assert.notNull(query, "query is null!");
        if (pager != null && pager.isQueryCount()) {
            int count = moveFormInfoDao.queryMoveFormInfoCount(query);
            pager.setTotalCount(count);
            if (count == 0) {
                return new ArrayList<MoveFormInfo>();
            }
        }
        List<MoveFormInfo> moveFormInfos = moveFormInfoDao.queryMoveFormInfoList(query, pager);
        return moveFormInfos;
    }

    //根据sku查询sku的库位库存
    private List<WhStock> querySkuAllStock(List<String> skus) {
        WhStockQueryCondition query = new WhStockQueryCondition();
        query.setSkus(skus);
        query.setIsTotal(false);
        query.setIsShowDate(false);
        return whStockDao.queryPageStocks(query, null);
    }

    @Override
    public void createMoveFormInfo(MoveFormInfo moveFormInfo) {
        try {
            moveFormInfoDao.createMoveFormInfo(moveFormInfo);
        } catch (SqlerException e) {
            log.error(e.getMessage(), e);
            throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
        }
    }

    @Override
    public void batchCreateMoveFormInfo(List<MoveFormInfo> entityList) {
        if (CollectionUtils.isNotEmpty(entityList)) {
            try {
                moveFormInfoDao.batchCreateMoveFormInfo(entityList);
            } catch (SqlerException e) {
                log.error(e.getMessage(), e);
                throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
            }
        }
    }

    @Override
    public void deleteMoveFormInfo(Integer id) {
        try {
            moveFormInfoDao.deleteMoveFormInfo(id);
        } catch (SqlerException e) {
            log.error(e.getMessage(), e);
            throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
        }
    }

    @Override
    public void deleteMoveFormInfos(List<Integer> ids) {
        try {
            moveFormInfoDao.deleteMoveFormInfoList(ids);
        } catch (SqlerException e) {
            log.error(e.getMessage(), e);
            throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
        }
    }

    @Override
    public void updateMoveFormInfo(MoveFormInfo moveFormInfo) {
        try {
            moveFormInfoDao.updateMoveFormInfo(moveFormInfo);
        } catch (SqlerException e) {
            log.error(e.getMessage(), e);
            throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
        }
    }

    @Override
    public void batchUpdateMoveFormInfo(List<MoveFormInfo> entityList) {
        if (CollectionUtils.isNotEmpty(entityList)) {
            try {
                moveFormInfoDao.batchUpdateMoveFormInfo(entityList);
            } catch (SqlerException e) {
                log.error(e.getMessage(), e);
                throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
            }
        }
    }

    @Override
    public int queryRelevanceMoveForm(MoveFormQueryCondition query) {
        return moveFormInfoDao.queryRelevanceMoveForm(query);
    }

    @Override
    public List<MoveFormInfo> parseExcelFile(MultipartFile excelFile) {
        if (Objects.isNull(excelFile)) {
            return null;
        }
        final String[] EXCEL_HEADER = {"SKU", "移出库位", "目标库位"};
        try {
            ResultModel<MoveFormInfo> resultModel = POIUtils.readExcel(EXCEL_HEADER, excelFile, row -> {
                int cellNumber = 0;
                MoveFormInfo moveFormInfo = new MoveFormInfo();
                moveFormInfo.setSku(POIUtils.cellValue2Str(row.getCell(cellNumber++)));
                String oldLocation = POIUtils.cellValue2Str(row.getCell(cellNumber++));
                moveFormInfo.setOldLocation(Objects.isNull(oldLocation) ? "" : oldLocation);
                String newLocation = POIUtils.cellValue2Str(row.getCell(cellNumber++));
                moveFormInfo.setNewLocation(Objects.isNull(newLocation) ? "" : newLocation);
                return moveFormInfo;
            }, false);
            if (!resultModel.isSuccess()) {
                log.error(resultModel.getMsg());
                return null;
            }
            return resultModel.getList();
        } catch (Exception e) {
            log.error(e.getMessage());
        }
        return null;
    }

    @Override
    public List<MoveFormInfo> searchStockAndReturn(List<MoveFormInfo> list) {
        if (CollectionUtils.isEmpty(list)) {
            return new ArrayList<>();
        }
        return list.stream()
                .map(moveFormInfo -> {
                    // 当库存不为空才填充对象，否则不填充
                    Integer quantity = this.queryStockByMoveSku(moveFormInfo.getSku(), moveFormInfo.getOldLocation());
                    if (Objects.isNull(quantity) || quantity == 0) {
                        return moveFormInfo;
                    }
                    WhSku sku = this.querySkuInfo(moveFormInfo.getSku());
                    if (Objects.isNull(sku)) {
                        WhCombineSkuQueryCondition combineSkuQuery = new WhCombineSkuQueryCondition();
                        combineSkuQuery.setSpu(moveFormInfo.getSku());
                        List<WhCombineSku> whCombineSkus = whCombineSkuService.queryWhCombineSkus(combineSkuQuery, null);
                        if (CollectionUtils.isEmpty(whCombineSkus)) {
                            return moveFormInfo;
                        }
                        sku = new WhSku();
                        sku.setSku(whCombineSkus.get(0).getSpu());
                        sku.setName(whCombineSkus.get(0).getName());
                    }
                    moveFormInfo.setPlanNum(quantity);
                    moveFormInfo.setName(sku.getName());
                    moveFormInfo.setSku(sku.getSku());
//                    moveFormInfo.setOldLocation(Objects.isNull(sku.getLocationNumber()) ? "null" : sku.getLocationNumber());
                    return null;
                }).filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    @Override
    public List<MoveFormInfo> searchEmptyTotalsStockAndReturn(List<MoveFormInfo> list) {
        if (CollectionUtils.isEmpty(list)) {
            return new ArrayList<>();
        }

        boolean hasEmptySku = list.stream().anyMatch(moveFormInfo -> StringUtils.isEmpty(moveFormInfo.getSku()));
        if (hasEmptySku){
            throw new IllegalArgumentException("移库单" + list.get(0).getMoveId() + "存在空sku");
        }

        Map<String, List<MoveFormInfo>> skuMap = list.stream()
                .filter(moveFormInfo -> Objects.nonNull(moveFormInfo.getSku()))
                .collect(Collectors.groupingBy(c -> c.getSku() + c.getOldLocation()));
        if (MapUtils.isEmpty(skuMap)) {
            throw new IllegalArgumentException("移库单" + list.get(0).getMoveId() + "所有明细中sku为空");
        }
        List<String> skus = list.stream().map(MoveFormInfo::getSku).collect(Collectors.toList());

        WhStockQueryCondition whStockQueryCondition = new WhStockQueryCondition(skus);
//        whStockQueryCondition.setWarehouseId(CacheUtils.getLocalWarehouseId());
        List<WhStock> whStocks = whStockService.queryPageWhStocks(whStockQueryCondition, null);
        return Optional.ofNullable(whStocks)
                .orElse(new ArrayList<>())
                .stream()
                .filter(Objects::nonNull)
                .filter(whStock -> Objects.isNull(whStock.getTotalLocationQuantity()) || Objects.equals(0, whStock.getTotalLocationQuantity()))
                .filter(whStock -> Objects.nonNull(whStock.getSku()))
                .filter(c -> skuMap.containsKey(c.getSku() + c.getLocationNumber()))
                .map(c -> skuMap.get(c.getSku() + c.getLocationNumber()))
                .flatMap(Collection::stream)
                .collect(Collectors.toList());
    }

    /**
     * 通过要进行移库操作的sku名得到对应的总库存数目
     *
     * @param sku sku名称
     * @return sku的总库存数目
     */
    private Integer queryStockByMoveSku(String sku, String locationNum) {
        if (StringUtil.isBlank(sku) || StringUtils.isBlank(locationNum)) {
            return 0;
        }
        /*WhStockQueryCondition whStockQueryCondition = new WhStockQueryCondition();
        whStockQueryCondition.setSku(sku);
//        whStockQueryCondition.setWarehouseId(CacheUtils.getLocalWarehouseId());
        List<WhStock> whStocks = whStockService.queryPageWhStocks(whStockQueryCondition, null);
        if (CollectionUtils.isNotEmpty(whStocks) && whStocks.size() > 1){
            log.error("查询出多个sku库存:"+ JSON.toJSONString(whStocks));
        }*/
        List<WhStock> whStocks = whStockService.listFullStockBySku(Collections.singletonList(sku), locationNum);
        boolean isExistStocks = CollectionUtils.isNotEmpty(whStocks) && whStocks.get(0).getTotalLocationQuantity() != 0;
        if (isExistStocks) {
            return whStocks.get(0).getTotalLocationQuantity();
        }
        return 0;
    }

    /**
     * 通过要进行移库操作的sku名称得到对应的sku信息
     *
     * @param sku sku名称
     * @return sku信息对象
     */
    private WhSku querySkuInfo(String sku) {
        if (StringUtil.isBlank(sku)) {
            return null;
        }
        WhSkuQueryCondition query = new WhSkuQueryCondition();
        //兼容SKU编码和唯一码
        query.setSku(CompatibleSkuUtils.getSku(sku));
        List<WhSku> whSkuList = whSkuService.queryWhSkuStocks(query);
        if (CollectionUtils.isNotEmpty(whSkuList)) {
            return whSkuList.get(0);
        }
        return null;
    }
}