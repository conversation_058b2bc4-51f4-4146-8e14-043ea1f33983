package com.estone.sku.service.impl;

import com.estone.sku.bean.SkuCategoryTagRelation;
import com.estone.sku.bean.SkuCategoryTagRelationQueryCondition;
import com.estone.sku.dao.SkuCategoryTagRelationDao;
import com.estone.sku.service.SkuCategoryTagRelationService;
import com.whq.tool.component.Pager;
import com.whq.tool.exception.BusinessException;
import com.whq.tool.exception.DBErrorCode;
import com.whq.tool.sqler.SqlerException;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

@Service("skuCategoryTagRelationService")
@Slf4j
public class SkuCategoryTagRelationServiceImpl implements SkuCategoryTagRelationService {
    @Resource
    private SkuCategoryTagRelationDao skuCategoryTagRelationDao;

    @Override
    public SkuCategoryTagRelation getSkuCategoryTagRelation(Integer id) {
        SkuCategoryTagRelation skuCategoryTagRelation = skuCategoryTagRelationDao.querySkuCategoryTagRelation(id);
        return skuCategoryTagRelation;
    }

    @Override
    public SkuCategoryTagRelation getSkuCategoryTagRelationDetail(Integer id) {
        SkuCategoryTagRelation skuCategoryTagRelation = skuCategoryTagRelationDao.querySkuCategoryTagRelation(id);
        // 关联查询
        return skuCategoryTagRelation;
    }

    @Override
    public SkuCategoryTagRelation querySkuCategoryTagRelation(SkuCategoryTagRelationQueryCondition query) {
        Assert.notNull(query, "query is null!");
        SkuCategoryTagRelation skuCategoryTagRelation = skuCategoryTagRelationDao.querySkuCategoryTagRelation(query);
        return skuCategoryTagRelation;
    }

    @Override
    public List<SkuCategoryTagRelation> queryAllSkuCategoryTagRelations() {
        return skuCategoryTagRelationDao.querySkuCategoryTagRelationList();
    }

    @Override
    public List<SkuCategoryTagRelation> querySkuCategoryTagRelations(SkuCategoryTagRelationQueryCondition query, Pager pager) {
        Assert.notNull(query, "query is null!");
        if (pager != null && pager.isQueryCount()) {
            int count = skuCategoryTagRelationDao.querySkuCategoryTagRelationCount(query);
            pager.setTotalCount(count);
            if ( count == 0) {
                return new ArrayList<SkuCategoryTagRelation>();
            }
        }
        List<SkuCategoryTagRelation> skuCategoryTagRelations = skuCategoryTagRelationDao.querySkuCategoryTagRelationList(query, pager);
        return skuCategoryTagRelations;
    }

    @Override
    public void createSkuCategoryTagRelation(SkuCategoryTagRelation skuCategoryTagRelation) {
        try {
            skuCategoryTagRelationDao.createSkuCategoryTagRelation(skuCategoryTagRelation);
        }
        catch (SqlerException e) {
            log.error(e.getMessage(), e);
            throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
        }
    }

    @Override
    public void batchCreateSkuCategoryTagRelation(List<SkuCategoryTagRelation> entityList) {
        if (CollectionUtils.isNotEmpty(entityList)) {
            try {
                skuCategoryTagRelationDao.batchCreateSkuCategoryTagRelation(entityList);
            }
            catch (SqlerException e) {
                log.error(e.getMessage(), e);
                throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
            }
        }
    }

    @Override
    public void deleteSkuCategoryTagRelation(Integer id) {
        try {
            skuCategoryTagRelationDao.deleteSkuCategoryTagRelation(id);
        }
        catch (SqlerException e) {
            log.error(e.getMessage(), e);
            throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
        }
    }

    @Override
    public void updateSkuCategoryTagRelation(SkuCategoryTagRelation skuCategoryTagRelation) {
        try {
            skuCategoryTagRelationDao.updateSkuCategoryTagRelation(skuCategoryTagRelation);
        }
        catch (SqlerException e) {
            log.error(e.getMessage(), e);
            throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
        }
    }

    @Override
    public void batchUpdateSkuCategoryTagRelation(List<SkuCategoryTagRelation> entityList) {
        if (CollectionUtils.isNotEmpty(entityList)) {
            try {
                skuCategoryTagRelationDao.batchUpdateSkuCategoryTagRelation(entityList);
            }
            catch (SqlerException e) {
                log.error(e.getMessage(), e);
                throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
            }
        }
    }
}