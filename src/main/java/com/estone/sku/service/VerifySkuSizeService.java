package com.estone.sku.service;

import com.estone.sku.bean.VerifySkuSize;
import com.estone.sku.bean.VerifySkuSizeQueryCondition;
import com.estone.sku.bean.VerifySkuWeight;
import com.estone.sku.bean.WhSku;
import com.whq.tool.component.Pager;
import com.whq.tool.json.ResponseJson;

import java.util.List;

public interface VerifySkuSizeService {
    List<VerifySkuSize> queryAllVerifySkuSizes();

    List<VerifySkuSize> queryVerifySkuSizes(VerifySkuSizeQueryCondition query, Pager pager);

    VerifySkuSize getVerifySkuSize(Integer id);

    VerifySkuSize getVerifySkuSizeDetail(Integer id);

    VerifySkuSize queryVerifySkuSize(VerifySkuSizeQueryCondition query);

    void createVerifySkuSize(VerifySkuSize verifySkuSize);

    void batchCreateVerifySkuSize(List<VerifySkuSize> entityList);

    void deleteVerifySkuSize(Integer id);

    void updateVerifySkuSize(VerifySkuSize verifySkuSize);

    void batchUpdateVerifySkuSize(List<VerifySkuSize> entityList);

    ResponseJson checkSize(WhSku whSku, WhSku sysWhSku, String verifyRemark);

    ResponseJson checkSize(WhSku whSku, WhSku sysWhSku);

    ResponseJson checkSkuSize(List<Integer> ids, String checkResult);
}