package com.estone.sku.bean;

import com.estone.common.util.CompareParam;
import com.estone.sku.enums.PackagingAttributeEnum;
import lombok.Data;
import org.apache.commons.lang.ArrayUtils;
import org.apache.commons.lang.StringUtils;

import java.io.Serializable;
import java.sql.Timestamp;
import java.util.Objects;

@Data
public class VerifySkuWeightTaskItem implements Serializable , CompareParam, Comparable<VerifySkuWeightTaskItem> {
    private static final long serialVersionUID = 1L;

    /**
     * 主键 database column verify_sku_weight_task_item.id
     */
    private Integer id;

    /**
     * 称重任务主键ID database column verify_sku_weight_task_item.task_id
     */
    private Integer taskId;

    /**
     * sku database column verify_sku_weight_task_item.sku
     */
    private String sku;

    /**
     * 状态 database column verify_sku_weight_task_item.status
     */
    private Integer status;

    /**
     * 原净重(g) database column verify_sku_weight_task_item.weight
     */
    private Double weight;

    /**
     * 称重(g) database column verify_sku_weight_task_item.weighing_weight
     */
    private Double weighingWeight;

    /**
     * 重量差(g) database column verify_sku_weight_task_item.weight_difference
     */
    private Double weightDifference;

    /**
     * 创建人 database column verify_sku_weight_task_item.created_by
     */
    private Integer createdBy;

    /**
     * 创建时间 database column verify_sku_weight_task_item.created_date
     */
    private Timestamp createdDate;

    /**
     * 称重时间 database column verify_sku_weight_task_item.weighing_date
     */
    private Timestamp weighingDate;

    /**
     * 称重人 database column verify_sku_weight_task_item.weighing_by
     */
    private Integer weighingBy;

    /**
     * 最后修改时间 database column verify_sku_weight_task_item.last_update_date
     */
    private Timestamp lastUpdateDate;

    /**
     * 最后更新人 database column verify_sku_weight_task_item.last_update_by
     */
    private Integer lastUpdateBy;

    private Integer skuId;

    private String skuName;

    private String location;

    /**
     * 产品尺寸规格 ,是否不规则产品:true;false;默认空即为false
     */
    private String specification;

    /**
     * 审核单驳回并重新生成称重任务时，记录用于生成称重任务的审核单ID
     */
    private Integer generateByVerifySkuWeightId;

    /**
     * 包装属性
     * @see com.estone.sku.enums.PackagingAttributeEnum
     */
    private Integer packagingAttribute;


    public String getPackagingAttributeStr(){
        if (Objects.nonNull(this.getPackagingAttribute())) {
            return PackagingAttributeEnum.getInstanceByCode(this.getPackagingAttribute()).getName();
        }
        return "";
    }

    @Override
    public String getLocationNumber() {
        return location;
    }


    /**
     * 用时
     */
    public Double usableTime;

    @Override
    public int compareTo(VerifySkuWeightTaskItem o) {
        // 旧版本
        String StockLocation1 = transformStockLocation(location);
        String StockLocation2 = transformStockLocation(o.getLocation());

        // if (location.substring(0, 1).equals("A") &&
        // o.getLocation().substring(0, 1).equals("A")) {
        // // A区域的情况下
        // if (location.equals("A1-1-1-12")) {
        // StockLocation1 = "K1-1-1-22";
        // }
        // else if (o.getLocation().equals("A1-1-1-12")) {
        // StockLocation2 = "K1-1-1-22";
        // }
        // }

        String[] str1s = StockLocation1.split("-");
        if (str1s.length == 4) {
            if (str1s[0].length() == 2) {
                str1s[0] = str1s[0].substring(0, 1) + "0" + str1s[0].substring(1, 2);
            }
            if (str1s[1].length() == 1) {
                str1s[1] = "0" + str1s[1];
            }
            if (str1s[2].length() == 1) {
                str1s[2] = "0" + str1s[2];
            }
            if (str1s[3].length() == 1) {
                str1s[3] = "0" + str1s[3];
            }
            StockLocation1 = str1s[0] + "-" + str1s[1] + "-" + str1s[2] + "-" + str1s[3];
        }

        String[] str2s = StockLocation2.split("-");
        if (str2s.length == 4) {
            if (str2s[0].length() == 2) {
                str2s[0] = str2s[0].substring(0, 1) + "0" + str2s[0].substring(1, 2);
            }
            if (str2s[1].length() == 1) {
                str2s[1] = "0" + str2s[1];
            }
            if (str2s[2].length() == 1) {
                str2s[2] = "0" + str2s[2];
            }
            if (str2s[3].length() == 1) {
                str2s[3] = "0" + str2s[3];
            }
            StockLocation2 = str2s[0] + "-" + str2s[1] + "-" + str2s[2] + "-" + str2s[3];
        }

        int result = StockLocation1.compareTo(StockLocation2);

        if (result == 0) {
            return sku.compareTo(o.getSku());
        }
        return result;
    }

    // changed by liuguolin at 2017-12-26 新增新仓库对比规则。2G>2A>2B>2C>2D>2F>2E
    String[] newAreaSeqArr = new String[] { "2K", "2A", "2B", "2C", "2D", "2F", "2E" };
    String[] newAreaReplaceArr = new String[] { "2A", "2B", "2C", "2D", "2E", "2F", "2G" };
    // 旧仓库规则
    //String[] areaSeqArr = new String[] { "A", "Q", "B", "M", "G", "D", "N", "E", "H", "F", "J", "K" };
    //String[] areaReplaceArr = new String[] { "A", "B", "C", "D", "E", "F", "G", "H", "I", "J", "K", "L" };
    // 2021-06-04 最新对比规则 Y-D-E-F-G-H-J-K-L-M
    String[] areaSeqArr = new String[] { "Y", "D", "E", "F", "G", "H", "J", "K", "L", "M","Z" };
    String[] areaReplaceArr = new String[] { "A", "B", "C", "D", "E", "F", "G", "H", "I", "J","K" };

    private String transformStockLocation(String stockLocation) {
        String transformStockLocation = null;
        if (StringUtils.isNotEmpty(stockLocation)) {
            stockLocation = stockLocation.toUpperCase();
            String areaCode = stockLocation.substring(0, 1);
            if (ArrayUtils.contains(areaSeqArr, areaCode)) {
                int areaCodeIndex = ArrayUtils.indexOf(areaSeqArr, areaCode);
                String areaReplaceCode = areaReplaceArr[areaCodeIndex];
                stockLocation = stockLocation.replaceFirst(areaCode, areaReplaceCode);
                transformStockLocation = stockLocation;
            }
            else if ("2".equals(areaCode)) {
                String newAreaCode = stockLocation.substring(0, 2);
                if (ArrayUtils.contains(newAreaSeqArr, newAreaCode)) {
                    int newAreaCodeIndex = ArrayUtils.indexOf(newAreaSeqArr, newAreaCode);
                    String newAreaReplaceCode = newAreaReplaceArr[newAreaCodeIndex];
                    stockLocation = stockLocation.replaceFirst(newAreaCode, newAreaReplaceCode);
                    return stockLocation;
                }
            }
        }
        transformStockLocation = stockLocation;
        if (transformStockLocation == null) {
            transformStockLocation = "Z";
        }
        return transformStockLocation;
    }

}