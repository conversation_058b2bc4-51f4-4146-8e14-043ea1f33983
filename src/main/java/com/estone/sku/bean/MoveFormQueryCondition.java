package com.estone.sku.bean;

import lombok.Data;

import java.util.List;

@Data
public class MoveFormQueryCondition extends MoveForm {
    private static final long serialVersionUID = 1L;

    private List<Integer> idList;
    //单个sku
    private String singleSku;
    //新库位，用于判断是否可以放入
    private String location;
    //旧库位
    private String oldLocation;

    //逗号分隔的SKU
    private String skuSplit;

    private List<String> skuList;

    //逗号分隔移库单号
    private String moveIdSplit;

    private List<String> moveIdList;

    //逗号分隔状态
    private String stateSplit;

    private List<String> stateList;

    private String fromCreationTime;// 创建时间
    private String toCreationTime;// 创建时间

    private String fromGainTime;// 领取时间
    private String toGainTime;// 领取时间

    private String fromReceiveTime;// 完成时间
    private String toReceiveTime;// 完成时间

    @Override
    public String toString() {
        return "MoveFormQueryCondition{" +
                "singleSku='" + singleSku + '\'' +
                ", location='" + location + '\'' +
                ", oldLocation='" + oldLocation + '\'' +
                ", skuSplit='" + skuSplit + '\'' +
                ", skuList=" + skuList +
                ", moveIdSplit='" + moveIdSplit + '\'' +
                ", moveIdList=" + moveIdList +
                ", stateSplit='" + stateSplit + '\'' +
                ", stateList=" + stateList +
                ", fromCreationTime='" + fromCreationTime + '\'' +
                ", toCreationTime='" + toCreationTime + '\'' +
                ", fromGainTime='" + fromGainTime + '\'' +
                ", toGainTime='" + toGainTime + '\'' +
                ", fromReceiveTime='" + fromReceiveTime + '\'' +
                ", toReceiveTime='" + toReceiveTime + '\'' +
                '}';
    }
}