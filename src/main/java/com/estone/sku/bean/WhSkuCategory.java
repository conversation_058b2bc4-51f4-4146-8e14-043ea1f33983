package com.estone.sku.bean;

import java.io.Serializable;
import java.sql.Timestamp;

public class WhSkuCategory implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     * This field corresponds to the database column wh_sku_category.id
     *
     * @mbggenerated Wed Aug 15 09:19:49 CST 2018
     */
    private Integer id;

    /**
     * 父级ID
     * This field corresponds to the database column wh_sku_category.parent_id
     *
     * @mbggenerated Wed Aug 15 09:19:49 CST 2018
     */
    private Integer parentId;

    /**
     * 名称
     * This field corresponds to the database column wh_sku_category.name
     *
     * @mbggenerated Wed Aug 15 09:19:49 CST 2018
     */
    private String name;

    /**
     * 等级
     * This field corresponds to the database column wh_sku_category.level
     *
     * @mbggenerated Wed Aug 15 09:19:49 CST 2018
     */
    private Integer level;

    /**
     * 费用
     * This field corresponds to the database column wh_sku_category.fee
     *
     * @mbggenerated Wed Aug 15 09:19:49 CST 2018
     */
    private Double fee;

    /**
     * 最低费用
     * This field corresponds to the database column wh_sku_category.minimum_fee
     *
     * @mbggenerated Wed Aug 15 09:19:49 CST 2018
     */
    private Double minimumFee;

    /**
     * 创建时间
     * This field corresponds to the database column wh_sku_category.creation_date
     *
     * @mbggenerated Wed Aug 15 09:19:49 CST 2018
     */
    private Timestamp creationDate;

    /**
     * 创建人
     * This field corresponds to the database column wh_sku_category.created_by
     *
     * @mbggenerated Wed Aug 15 09:19:49 CST 2018
     */
    private Integer createdBy;

    /**
     * 最后更新时间
     * This field corresponds to the database column wh_sku_category.last_update_date
     *
     * @mbggenerated Wed Aug 15 09:19:49 CST 2018
     */
    private Timestamp lastUpdateDate;

    /**
     * 最后更新人
     * This field corresponds to the database column wh_sku_category.last_updated_by
     *
     * @mbggenerated Wed Aug 15 09:19:49 CST 2018
     */
    private Integer lastUpdatedBy;

    /**
     * 描述
     * This field corresponds to the database column wh_sku_category.category_desc
     *
     * @mbggenerated Wed Aug 15 09:19:49 CST 2018
     */
    private String categoryDesc;

    /**
     * 盈利性比率
     * This field corresponds to the database column wh_sku_category.profit_ratio
     *
     * @mbggenerated Wed Aug 15 09:19:49 CST 2018
     */
    private Double profitRatio;

    /**
     * 特征
     * This field corresponds to the database column wh_sku_category.feature
     *
     * @mbggenerated Wed Aug 15 09:19:49 CST 2018
     */
    private String feature;

    /**
     * 关键词
     * This field corresponds to the database column wh_sku_category.keywords
     *
     * @mbggenerated Wed Aug 15 09:19:49 CST 2018
     */
    private String keywords;

    /**
     * 平台
     * This field corresponds to the database column wh_sku_category.for_platform_category
     *
     * @mbggenerated Wed Aug 15 09:19:49 CST 2018
     */
    private String forPlatformCategory;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getParentId() {
        return parentId;
    }

    public void setParentId(Integer parentId) {
        this.parentId = parentId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Integer getLevel() {
        return level;
    }

    public void setLevel(Integer level) {
        this.level = level;
    }

    public Double getFee() {
        return fee;
    }

    public void setFee(Double fee) {
        this.fee = fee;
    }

    public Double getMinimumFee() {
        return minimumFee;
    }

    public void setMinimumFee(Double minimumFee) {
        this.minimumFee = minimumFee;
    }

    public Timestamp getCreationDate() {
        return creationDate;
    }

    public void setCreationDate(Timestamp creationDate) {
        this.creationDate = creationDate;
    }

    public Integer getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(Integer createdBy) {
        this.createdBy = createdBy;
    }

    public Timestamp getLastUpdateDate() {
        return lastUpdateDate;
    }

    public void setLastUpdateDate(Timestamp lastUpdateDate) {
        this.lastUpdateDate = lastUpdateDate;
    }

    public Integer getLastUpdatedBy() {
        return lastUpdatedBy;
    }

    public void setLastUpdatedBy(Integer lastUpdatedBy) {
        this.lastUpdatedBy = lastUpdatedBy;
    }

    public String getCategoryDesc() {
        return categoryDesc;
    }

    public void setCategoryDesc(String categoryDesc) {
        this.categoryDesc = categoryDesc;
    }

    public Double getProfitRatio() {
        return profitRatio;
    }

    public void setProfitRatio(Double profitRatio) {
        this.profitRatio = profitRatio;
    }

    public String getFeature() {
        return feature;
    }

    public void setFeature(String feature) {
        this.feature = feature;
    }

    public String getKeywords() {
        return keywords;
    }

    public void setKeywords(String keywords) {
        this.keywords = keywords;
    }

    public String getForPlatformCategory() {
        return forPlatformCategory;
    }

    public void setForPlatformCategory(String forPlatformCategory) {
        this.forPlatformCategory = forPlatformCategory;
    }
}