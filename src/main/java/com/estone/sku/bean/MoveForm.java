package com.estone.sku.bean;

import java.io.Serializable;
import java.sql.Timestamp;
import lombok.Data;

@Data
public class MoveForm implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * id database column move_form.id
     */
    private Integer id;

    /**
     * 移库单号 database column move_form.move_id
     */
    private String moveId;

    /**
     * sku数量 database column move_form.sku_num
     */
    private Integer skuNum;

    /**
     * 需要迁移数量 database column move_form.plan_num
     */
    private Integer planNum;

    /**
     * 周转筐中货品数量 database column move_form.middle_num
     */
    private Integer middleNum;

    /**
     * 实际迁移数量 database column move_form.reality_num
     */
    private Integer realityNum;

    /**
     * 差异数量 database column move_form.diff_num
     */
    private Integer diffNum;

    /**
     * 周转筐 database column move_form.box_no
     */
    private String boxNo;

    /**
     * state状态{1待领取、2拣货中、3待上架、4上架中、5已完成} database column move_form.state
     */
    private String state;

    private String stateCh;

    /**
     * 创建人 database column move_form.creation_id
     */
    private Integer creationId;

    /**
     * 创建时间 database column move_form.creation_time
     */
    private Timestamp creationTime;

    /**
     * 领取时间 database column move_form.gain_time
     */
    private Timestamp gainTime;

    /**
     * 拣货人 database column move_form.picking_id
     */
    private Integer pickingId;

    /**
     * 拣货完成时间 database column move_form.picking_time
     */
    private Timestamp pickingTime;

    /**
     * 上架人 database column move_form.putaway_id
     */
    private Integer putawayId;

    /**
     * 上架完成时间 database column move_form.putaway_time
     */
    private Timestamp putawayTime;

    @Override
    public String toString() {
        return "MoveForm{" +
                "id=" + id +
                ", moveId='" + moveId + '\'' +
                ", skuNum=" + skuNum +
                ", planNum=" + planNum +
                ", middleNum=" + middleNum +
                ", realityNum=" + realityNum +
                ", diffNum=" + diffNum +
                ", boxNo='" + boxNo + '\'' +
                ", state='" + state + '\'' +
                ", stateCh='" + stateCh + '\'' +
                ", creationId=" + creationId +
                ", creationTime=" + creationTime +
                ", gainTime=" + gainTime +
                ", pickingId=" + pickingId +
                ", pickingTime=" + pickingTime +
                ", putawayId=" + putawayId +
                ", putawayTime=" + putawayTime +
                '}';
    }
}