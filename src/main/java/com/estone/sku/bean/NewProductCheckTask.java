package com.estone.sku.bean;

import java.io.Serializable;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;

import com.estone.common.util.TaglibUtils;
import com.estone.sku.enums.ProductCheckTaskStatus;
import lombok.Data;

@Data
public class NewProductCheckTask implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键id database column new_product_check_task.id
     */
    private Integer id;

    /**
     * 核查任务 database column new_product_check_task.task_no
     */
    private String taskNo;

    /**
     * 状态 database column new_product_check_task.status
     */
    private Integer status;

    /**
     * 打印状态 database column new_product_check_task.print_status
     */
    private Integer printStatus;

    /**
     * 创建日期 database column new_product_check_task.creation_date
     */
    private Timestamp creationDate;

    /**
     * 创建人 database column new_product_check_task.creation_by
     */
    private Integer creationBy;

    /**
     * 领取时间 database column new_product_check_task.receive_date
     */
    private Timestamp receiveDate;

    /**
     * 领取人 database column new_product_check_task.receive_by
     */
    private Integer receiveBy;

    /**
     * 完成时间 database column new_product_check_task.check_date
     */
    private Timestamp checkDate;

    /**
     * 完成人 database column new_product_check_task.check_by
     */
    private Integer checkBy;

    private List<NewProductMaintenance> productMaintenanceList = new ArrayList<>();

    public Integer getSkuCount() {
    	if (productMaintenanceList == null) {
    		return 0;
    	}
    	return productMaintenanceList.size();
    }

    public String getStatusName() {
    	return ProductCheckTaskStatus.getNameByCode(status);
    }

    public String getCreationName() {
    	return TaglibUtils.getEmployeeNameByUserId(creationBy);
    }

    public String getReceiveName() {
        return TaglibUtils.getEmployeeNameByUserId(receiveBy);
    }
}