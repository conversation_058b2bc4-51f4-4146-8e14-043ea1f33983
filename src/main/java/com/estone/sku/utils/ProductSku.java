package com.estone.sku.utils;

import com.estone.sku.bean.WhSkuQcCategory;
import com.estone.sku.bean.WhSkuSpecialGoods;
import com.estone.sku.bean.WhSkuTags;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * @Description: 产品系统SKU信息
 * 
 * @ClassName: ProductSku
 * @Author: Administrator
 * @Date: 2019/08/22
 * @Version: 0.0.1
 */
@Data
public class ProductSku implements Serializable {

    /**
     * 
     */
    private static final long serialVersionUID = 1L;
    /**
     * 产品id
     */
    private Integer id;
    /**
     * sku
     */
    private String articleNumber;
    /**
     * 产品名称
     */
    private String name;
    /**
     * 分类信息
     */
    private Integer categoryId;
    private String categoryName;
    /**
     * 是否新仓 1：是； 0：否
     */
    private Boolean isxckrecord;
    /**
     * 包材
     */
    private Integer packingmaterialId;
    private String packingmaterialName;

    /**
     * 搭配包材
     */
    private String matchMaterials;
    private String matchMaterialsCode;
    
    /**
     * 采购助理
     */
    private Integer assistantproductmanagerId;
    private String assistantproductmanagerName;
    /**
     * 标准重量
     */
    private Double netweight;
    /**
     * 重量
     */
    private Double weight;
    /**
     * 中文报关名
     */
    private String declarecnname;
    /**
     * 英文报关名
     */
    private String declareenname;
    /**
     * 最后修改时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date lastupdatetime;
    /**
     * 是否需要质检 1：是； 0：否
     */
    private Boolean isneedinspection;
    /**
     * 是否加工装袋 1：是； 0：否
     */
    private Boolean processingBagging;
    /**
     * 成本价
     */
    private Double averagepurchaseprice;
    /**
     * 标题
     */
    private String title;
    /**
     * 描述
     */
    private String description;
    /**
     * 库位
     */
    private String stocklocation;
    /**
     * 产品经理
     */
    private Integer productmanagerId;
    private String productmanagerName;
    /**
     * 首图
     */
    private String productImage;
    /**
     * 单品状态
     */
    private String skulifecyclephase;
    private String skulifecyclephaseName;
    /**
     * 折扣率
     */
    private Double discountrate;

    /** SKU质检备注 */
    private List<WhSkuQcCategory> inspectionMemo;
    /**
     * 标准尺寸
     */
    private String size;
    /**
     * 录入时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date enrolldate;
    /**
     * 在途数量
     */
    private Integer transitingQuantity;
    /**
     * 14天销量
     */
    private Integer fourteenDaysSaleQuantity;
    /**
     * 30天销量
     */
    private Integer thirtyDaysSaleQuantity;
    /**
     * 90天销量
     */
    private Integer ninetyDaysSaleQuantity;
    /**
     * 是否剪标，1表示剪标，0表示不剪标
     */
    private Integer shearSign;
    /**
     * 是否备货，1表示不备货，0表示备货
     */
    private Integer noStockUp;
    /**
     * 是否废弃 0没有 ，1已废弃
     */
    private Integer discard;
    /**
     * 合并到的sku
     */
    private String mergeSku;

    /**
     * sku服装属性
     */
    private List<Map<String,String>> wmsCategoryAtts;

    /**
     * 特殊标签
     */
    private List<WhSkuSpecialGoods> specialGoods;

    // 分类全路径
    private String categoryPath;


    /**
     * 材质
     */
    private String material;

    /**
     * 海关编码
     */
    private String customsCode;
    /** SKU服装属性 */
    private String saleAtts;

    private WhSkuTags whSkuTags;

}
