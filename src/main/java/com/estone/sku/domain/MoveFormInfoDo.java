package com.estone.sku.domain;

import com.estone.sku.bean.MoveFormInfo;
import com.estone.sku.bean.MoveFormInfoQueryCondition;
import com.whq.tool.component.Pager;
import java.util.ArrayList;
import java.util.List;

public class MoveFormInfoDo {
    private MoveFormInfo moveFormInfo;

    private MoveFormInfoQueryCondition query;

    private List<MoveFormInfo> moveFormInfos = new ArrayList<MoveFormInfo>();

    private Pager page = new Pager();

    public MoveFormInfo getMoveFormInfo() {
        return moveFormInfo;
    }

    public void setMoveFormInfo(MoveFormInfo moveFormInfo) {
        this.moveFormInfo = moveFormInfo;
    }

    public MoveFormInfoQueryCondition getQuery() {
        return query;
    }

    public void setQuery(MoveFormInfoQueryCondition query) {
        this.query = query;
    }

    public List<MoveFormInfo> getMoveFormInfos() {
        return moveFormInfos;
    }

    public void setMoveFormInfos(List<MoveFormInfo> moveFormInfos) {
        this.moveFormInfos = moveFormInfos;
    }

    public Pager getPage() {
        return page;
    }

    public void setPage(Pager page) {
        this.page = page;
    }
}