package com.estone.sku.domain;

import com.estone.sku.bean.VerifySkuSize;
import com.estone.sku.bean.VerifySkuSizeQueryCondition;
import com.whq.tool.component.Pager;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class VerifySkuSizeDo {
    private VerifySkuSize verifySkuSize;

    private VerifySkuSizeQueryCondition query;

    private List<VerifySkuSize> verifySkuSizes = new ArrayList<VerifySkuSize>();

    private Pager page = new Pager();

    private String statuses;

}