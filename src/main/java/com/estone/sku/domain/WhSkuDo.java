package com.estone.sku.domain;

import com.alibaba.fastjson.JSONArray;
import com.estone.common.util.CacheUtils;
import com.estone.sku.bean.*;
import com.estone.system.param.bean.SystemParam;
import com.whq.tool.component.Pager;
import lombok.Data;
import org.apache.commons.lang.StringUtils;

import java.util.ArrayList;
import java.util.List;
@Data
public class WhSkuDo {
    private WhSku whSku;

    private WhSkuQueryCondition query = new WhSkuQueryCondition();

    private List<WhSku> whSkus = new ArrayList<WhSku>();

    private Pager page = new Pager();

    private List<PrintCheckInSku> printSku = new ArrayList<>();// 待打印的SKU

    private String printSkuUser;// 打印sku的操作人

    private List<SaleAttributeSetting> saleAttributeSettingList;

    private SystemParam systemParam;

    private String[] downLoadHeaders;// 导出选择的表头字段

    private String skuStatuses;// 状态

    private Integer printType;// 打印类型,1:批量打印，2:扫码打印

    private Boolean compareSkuWarehouseId = false;// 查询两仓仓库属性不一致SKU

    private List<String> locationRegionList = new ArrayList<String>();

    private String locationRegionSelect;

    private String locationRegionStr;

    private List<WhQcCategory> productQcCategoryList = new ArrayList<WhQcCategory>();// 产品系统质检类目

    private String deleteSkuQcCategoryIds;// 删除的质检类目

    private String matchMaterials;// 搭配耗材

    private String packageAttributes;// 包装属性

    private String packagingMaterials;// 包材下拉

    private List<WhSkuExpandAttr> whSkuExpandAttrList; // 附加属性

    private List<String> printList = new ArrayList<>();

    private String firstOrderType;//是否新品

    private String verifyRemark;//SKU重新称重的缘由

    private String productLogoStr;//产品标识

    private String salesPropertys;//销售属性

    private String skuTags;//产品标签

    private String floorLocationSelect;//加工属性

    private String skuTagsSelect;//sku标签

    private String skuSpecialTagSelect;

    private Double firstWeighQc; // 新品、供应商首单首次称重重量

    private Integer inId;

    // 首单称重重量二次校验结果，是否保存称重重量 true：走原逻辑 false：sku不保存称重重量
    private Boolean checkRepeatWeigh = true;

    private Boolean nonRepeatWeigh = true;

    private CETagDo ceTagDo;

    private OutProductTagDo outProductTagDo;
    /**
     * 界面的提示信息
     */
    private String tips;

    public String getSalesPropertys() {
        return salesPropertys;
    }

    public void setSalesPropertys(String salesPropertys) {
        this.salesPropertys = salesPropertys;
    }

    public String getProductLogoStr() {
        return productLogoStr;
    }

    public void setProductLogoStr(String productLogoStr) {
        this.productLogoStr = productLogoStr;
    }

    public String getFirstOrderType() {
        return firstOrderType;
    }

    public void setFirstOrderType(String firstOrderType) {
        this.firstOrderType = firstOrderType;
    }

    public List<String> getPrintList() {
        return printList;
    }

    public void setPrintList(List<String> printList) {
        this.printList = printList;
    }

    public List<WhSkuExpandAttr> getWhSkuExpandAttrList() {
        return whSkuExpandAttrList;
    }

    public void setWhSkuExpandAttrList(List<WhSkuExpandAttr> whSkuExpandAttrList) {
        this.whSkuExpandAttrList = whSkuExpandAttrList;
    }

    public String getDeleteSkuQcCategoryIds() {
        return deleteSkuQcCategoryIds;
    }

    public String getLocationRegionSelect() {
        return locationRegionSelect;
    }

    public void setLocationRegionSelect(String locationRegionSelect) {
        this.locationRegionSelect = locationRegionSelect;
    }

    public String getLocationRegionStr() {
        return locationRegionStr;
    }

    public void setLocationRegionStr(String locationRegionStr) {
        this.locationRegionStr = locationRegionStr;
    }

    public void setDeleteSkuQcCategoryIds(String deleteSkuQcCategoryIds) {
        this.deleteSkuQcCategoryIds = deleteSkuQcCategoryIds;
    }

    public List<WhQcCategory> getProductQcCategoryList() {
        return productQcCategoryList;
    }

    public void setProductQcCategoryList(List<WhQcCategory> productQcCategoryList) {
        this.productQcCategoryList = productQcCategoryList;
    }

    public WhSku getWhSku() {
        return whSku;
    }

    public void setWhSku(WhSku whSku) {
        this.whSku = whSku;
    }

    public WhSkuQueryCondition getQuery() {
        return query;
    }

    public void setQuery(WhSkuQueryCondition query) {
        this.query = query;
    }

    public List<WhSku> getWhSkus() {
        return whSkus;
    }

    public void setWhSkus(List<WhSku> whSkus) {
        this.whSkus = whSkus;
    }

    public Pager getPage() {
        return page;
    }

    public void setPage(Pager page) {
        this.page = page;
    }

    public List<PrintCheckInSku> getPrintSku() {
        return printSku;
    }

    public void setPrintSku(List<PrintCheckInSku> printSku) {
        this.printSku = printSku;
    }

    public String getPrintSkuUser() {
        return printSkuUser;
    }

    public void setPrintSkuUser(String printSkuUser) {
        this.printSkuUser = printSkuUser;
    }

    public SystemParam getSystemParam() {
        systemParam = CacheUtils.SystemParamGet("SALES_ATTRIBUTE_SETTING.SALES_ATTRIBUTE_SETTING");
        return systemParam;
    }

    public void setSystemParam(SystemParam systemParam) {
        this.systemParam = systemParam;
    }

    public List<SaleAttributeSetting> getSaleAttributeSettingList() {

        SystemParam sysParam = getSystemParam();

        if (sysParam != null && StringUtils.isNotBlank(sysParam.getParamValue())) {
            saleAttributeSettingList = JSONArray.parseArray(getSystemParam().getParamValue(), SaleAttributeSetting.class);
        }
        return saleAttributeSettingList;
    }

    public void setSaleAttributeSettingList(List<SaleAttributeSetting> saleAttributeSettingList) {
        this.saleAttributeSettingList = saleAttributeSettingList;
    }

    public String[] getDownLoadHeaders() {
        return downLoadHeaders;
    }

    public void setDownLoadHeaders(String[] downLoadHeaders) {
        this.downLoadHeaders = downLoadHeaders;
    }

    public Integer getPrintType() {
        return printType;
    }

    public void setPrintType(Integer printType) {
        this.printType = printType;
    }

    public List<String> getLocationRegionList() {
        return locationRegionList;
    }

    public void setLocationRegionList(List<String> locationRegionList) {
        this.locationRegionList = locationRegionList;
    }

    public Boolean getCompareSkuWarehouseId() {
        return compareSkuWarehouseId;
    }

    public void setCompareSkuWarehouseId(Boolean compareSkuWarehouseId) {
        this.compareSkuWarehouseId = compareSkuWarehouseId;
    }

    public String getSkuStatuses() {
        return skuStatuses;
    }

    public void setSkuStatuses(String skuStatuses) {
        this.skuStatuses = skuStatuses;
    }

    public String getMatchMaterials() {
        return matchMaterials;
    }

    public void setMatchMaterials(String matchMaterials) {
        this.matchMaterials = matchMaterials;
    }

}