package com.estone.sku.domain;

import com.estone.sku.bean.ExpManageItem;
import com.estone.sku.bean.ExpManageItemQueryCondition;
import com.whq.tool.component.Pager;
import java.util.ArrayList;
import java.util.List;

public class ExpManageItemDo {
    private ExpManageItem expManageItem;

    private ExpManageItemQueryCondition query;

    private List<ExpManageItem> expManageItems = new ArrayList<ExpManageItem>();

    private Pager page = new Pager();

    public ExpManageItem getExpManageItem() {
        return expManageItem;
    }

    public void setExpManageItem(ExpManageItem expManageItem) {
        this.expManageItem = expManageItem;
    }

    public ExpManageItemQueryCondition getQuery() {
        return query;
    }

    public void setQuery(ExpManageItemQueryCondition query) {
        this.query = query;
    }

    public List<ExpManageItem> getExpManageItems() {
        return expManageItems;
    }

    public void setExpManageItems(List<ExpManageItem> expManageItems) {
        this.expManageItems = expManageItems;
    }

    public Pager getPage() {
        return page;
    }

    public void setPage(Pager page) {
        this.page = page;
    }
}