server:
  port: 8181

yml-config:
  local-path: 'classpath:'

#cloud
spring:
  cloud:
    sentinel:
      #eager: false
      transport:
        dashboard: *************:8080
    nacos:
      discovery:
        server-addr: *************:8848
        file-extension: yml
        group: DEFAULT_GROUP
        namespace: b927834d-4684-4d3f-8691-c107484637c6
#  data:
#    elasticsearch:
#      #cluster-name: elasticsearch
#      #cluster-nodes: *************:9301
#      cluster-name: es-cluster-test
#      cluster-nodes: ************:9300,************:9300,************:9300
##      cluster-name: es-cluster-prod
##      cluster-nodes: ***********:9300,***********:9300,***********:9300
#  rabbitmq:
#    addresses: *************:5672
#    username: guest
#    password: guest
  freemarker:
    template-loader-path: /html/
  datasource:
    wms:
      type: com.alibaba.druid.pool.DruidDataSource
      driver-class-name: com.mysql.jdbc.Driver
      url: ***********************************************************************************************************************************
      username: root
      password: 123456
      #url: ***************************************************************************************************************************
      #username: readonly
      #password: '!QAZxsw2'
      #url: ***************************************************************************************************************************
      #username: fangxin
      #password: 'Fangxin123'
      validation-query: SELECT 1;
      sql-script-encoding: UTF-8
      initial-size: 1
      max-active: 1000
      max-wait: 60000
      min-idle: 1
      time-between-eviction-runs-millis: 60000
      min-evictable-idle-time-millis: 300000
      test-while-idle: true
      test-on-borrow: false
      test-on-return: false
      pool-prepared-statements: true
      max-open-prepared-statements: 20
      remove-abandoned-timeout: 5000
      max-pool-prepared-statement-per-connection-size: 300
    wms-query:
      type: com.alibaba.druid.pool.DruidDataSource
      driver-class-name: com.mysql.jdbc.Driver
      url: ***********************************************************************************************************************************
      username: root
      password: 123456
      #url: ***************************************************************************************************************************
      #username: readonly
      #password: '!QAZxsw2'
      validation-query: SELECT 1;
      sql-script-encoding: UTF-8
      initial-size: 1
      max-active: 1000
      max-wait: 60000
      min-idle: 1
      time-between-eviction-runs-millis: 60000
      min-evictable-idle-time-millis: 300000
      test-while-idle: true
      test-on-borrow: false
      test-on-return: false
      pool-prepared-statements: true
      max-open-prepared-statements: 20
      remove-abandoned-timeout: 5000
      max-pool-prepared-statement-per-connection-size: 300
    amq:
      type: com.alibaba.druid.pool.DruidDataSource
      driver-class-name: com.mysql.jdbc.Driver
      url: ***************************************************************************************************************************************
      username: root
      password: 123456
      validation-query: SELECT 1;
      sql-script-encoding: UTF-8
      initial-size: 1
      max-active: 1000
      max-wait: 60000
      min-idle: 1
      time-between-eviction-runs-millis: 60000
      min-evictable-idle-time-millis: 300000
      test-while-idle: true
      test-on-borrow: false
      test-on-return: false
      pool-prepared-statements: true
      max-open-prepared-statements: 20
      remove-abandoned-timeout: 5000
      max-pool-prepared-statement-per-connection-size: 300
    publish-tidb:
      url: ********************************************************************************************************************************************
      type: com.alibaba.druid.pool.DruidDataSource
      driver-class-name: com.mysql.jdbc.Driver
      username: root
      password: "P*q93jfBKr^18Y2-_0"
      validation-query: SELECT 1;
      sql-script-encoding: UTF-8
      initial-size: 1
      max-active: 300
      max-wait: 60000
      min-idle: 1
      time-between-eviction-runs-millis: 60000
      min-evictable-idle-time-millis: 300000
      test-while-idle: true
      test-on-borrow: false
      test-on-return: false
      pool-prepared-statements: true
      max-open-prepared-statements: 20
      remove-abandoned-timeout: 5000
      max-pool-prepared-statement-per-connection-size: 300
  redis:
    url: redis://************:6379
    host: ************
    #url: redis://************:6379
    #host: ************
    port: 6379
    timeout: 5000
    lettuce:
      pool:
        max-active: 200
        max-idle: 10
        max-wait: -1
        min-idle: 1
  kafka:
    # 指定kafka 代理地址，可以多个
    bootstrap-servers: ************:9092,************:9092,************:9092
    #bootstrap-servers: ************:9092
    producer:
      # 消息序列化方式
      key-serializer: org.apache.kafka.common.serialization.StringSerializer
      value-serializer: org.apache.kafka.common.serialization.StringSerializer
      # 重试次数
      retries: 1
      request:
        timeout:
          ms: 30000
    consumer:
      # 键的反序列化方式
      key-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      # 值的反序列化方式
      value-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      # 是否自动提交偏移量，默认值是true,为了避免出现重复数据和数据丢失，可以把它设置为false,然后手动提交偏移量
      enable-auto-commit: false
    listener:
      concurrency: 1
      #listner负责ack，每调用一次，就立即commit
      ack-mode: manual_immediate
# 菜鸟或作伙伴配置
cainiao-linkcp:
  appkey: SANDBOX348461
  secretKey: ********************************
  url: https://link.tbsandbox.com/gateway/link.do
  fromCode: SZU902
erp:
  # 文件服务器相关
  seaweed:
    url: http://172.16.10.51:8888
    max-total: 200
# 产品系统相关
product:
  config:
    push_check_in_exception_url: http://192.168.3.162/product/api/purchase/creatAbnormalOptimization

#极兔API配置
jitu:
  apiAccount: 178337126125932605
  privateKey: 0258d71b55fc45e3ad7a7f38bf4b201a
  url: https://uat-openapi.jtexpress.com.cn/webopenplatformapi/api
  code: J0086474299
  pwd: H5CD3zE6

#跨越API配置
ky-express:
  appKey: '83198'
  appSecret: B277CE3B0375EDF1C8FF2EA57C501F47
  customerCode: '************'
  platformFlag: 6820933B508C2977269F78B2DCD329CB
  callbackUrl: http://218.17.53.146:8181/wms/foreign/kyeCallback/printPdfUrl