<?xml version="1.0" encoding="UTF-8" ?>
<sqlmap >
  <sql datasource="dataSource" id="queryAfterSaleSettlementItemCount" >
    <content >
      <![CDATA[
        SELECT COUNT(1)
        FROM after_sale_settlement_item
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND ass_id = :ass_id]>
        <[AND relation_id = :relation_id]>
        <[AND relation_no = :relation_no]>
        <[AND type = :type]>
        <[AND before_qty = :before_qty]>
        <[AND quantity = :quantity]>
        <[AND end_qty = :end_qty]>
        <[AND creation_date = :creation_date]>
        <[AND creation_date >= :start_time]>
        <[AND creation_date <= :end_time]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryAfterSaleSettlementItemList" >
    <content >
      <![CDATA[
        SELECT id, ass_id, relation_id, relation_no, type, before_qty, quantity, end_qty, 
        creation_date
        FROM after_sale_settlement_item
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND ass_id = :ass_id]>
        <[AND relation_id = :relation_id]>
        <[AND relation_no = :relation_no]>
        <[AND type = :type]>
        <[AND before_qty = :before_qty]>
        <[AND quantity = :quantity]>
        <[AND end_qty = :end_qty]>
        <[AND creation_date = :creation_date]>
        <[AND creation_date >= :start_time]>
        <[AND creation_date <= :end_time]>
        ORDER BY id DESC
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryAfterSaleSettlementItemByPrimaryKey" >
    <content >
      <![CDATA[
        SELECT id, ass_id, relation_id, relation_no, type, before_qty, quantity, end_qty, 
        creation_date
        FROM after_sale_settlement_item
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryAfterSaleSettlementItem" >
    <content >
      <![CDATA[
        SELECT id, ass_id, relation_id, relation_no, type, before_qty, quantity, end_qty, 
        creation_date
        FROM after_sale_settlement_item
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND ass_id = :ass_id]>
        <[AND relation_id = :relation_id]>
        <[AND relation_no = :relation_no]>
        <[AND type = :type]>
        <[AND before_qty = :before_qty]>
        <[AND quantity = :quantity]>
        <[AND end_qty = :end_qty]>
        <[AND creation_date = :creation_date]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="createAfterSaleSettlementItem" >
    <content >
      <![CDATA[
        INSERT INTO after_sale_settlement_item (ass_id, relation_id, relation_no, type, before_qty, quantity, end_qty, 
          creation_date)
        VALUES (:ass_id, :relation_id, :relation_no, :type, :before_qty, :quantity, :end_qty, 
          :creation_date)
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="deleteAfterSaleSettlementItemByPrimaryKey" >
    <content >
      <![CDATA[
        DELETE FROM after_sale_settlement_item
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="updateAfterSaleSettlementItemByPrimaryKey" >
    <content >
      <![CDATA[
        UPDATE after_sale_settlement_item
        SET <[ass_id = :ass_id,]>
          <[relation_id = :relation_id,]>
          <[relation_no = :relation_no,]>
          <[type = :type,]>
          <[before_qty = :before_qty,]>
          <[quantity = :quantity,]>
          <[end_qty = :end_qty,]>
          <[creation_date = :creation_date,]>
        id = id
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
</sqlmap>