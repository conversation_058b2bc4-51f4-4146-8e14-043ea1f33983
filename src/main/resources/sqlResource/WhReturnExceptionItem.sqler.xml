<?xml version="1.0" encoding="UTF-8" ?>
<sqlmap >
  <sql datasource="dataSource" id="queryWhReturnExceptionItemCount" >
    <content >
      <![CDATA[
        SELECT COUNT(1)
        FROM wh_return_exception_item
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND id in (:ids)]>
        <[AND return_type = :return_type]>
        <[AND return_no in (:return_no)]>
        <[AND sku in (:sku)]>
        <[AND uuid in (:uuid)]>
        <[AND reason = :reason]>
        <[AND scan_time >= :fromScanTime]>
        <[AND scan_time <= :toScanTime]>
        <[AND return_user = :return_user]>
        <[AND create_user = :create_user]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryWhReturnExceptionItemList" >
    <content >
      <![CDATA[
        SELECT id, return_type, return_no, sku, uuid, reason, scan_time, return_user, create_user
        FROM wh_return_exception_item
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND id in (:ids)]>
        <[AND return_type = :return_type]>
        <[AND return_no in (:return_no)]>
        <[AND sku in (:sku)]>
        <[AND uuid in (:uuid)]>
        <[AND reason = :reason]>
        <[AND scan_time >= :fromScanTime]>
        <[AND scan_time <= :toScanTime]>
        <[AND return_user = :return_user]>
        <[AND create_user = :create_user]>
        ORDER BY id desc
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryWhReturnExceptionItemByPrimaryKey" >
    <content >
      <![CDATA[
        SELECT id, return_type, return_no, sku, uuid, reason, scan_time, return_user, create_user
        FROM wh_return_exception_item
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryWhReturnExceptionItem" >
    <content >
      <![CDATA[
        SELECT id, return_type, return_no, sku, uuid, reason, scan_time, return_user, create_user
        FROM wh_return_exception_item
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND id in (:ids)]>
        <[AND return_type = :return_type]>
        <[AND return_no in (:return_no)]>
        <[AND sku in (:sku)]>
        <[AND uuid in (:uuid)]>
        <[AND reason = :reason]>
        <[AND scan_time >= :fromScanTime]>
        <[AND scan_time <= :toScanTime]>
        <[AND return_user = :return_user]>
        <[AND create_user = :create_user]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="createWhReturnExceptionItem" >
    <content >
      <![CDATA[
        INSERT INTO wh_return_exception_item (return_type, return_no, sku, uuid, reason, scan_time, return_user, create_user
          )
        VALUES (:return_type, :return_no, :sku, :uuid, :reason, :scan_time, :return_user, :create_user
          )
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="deleteWhReturnExceptionItemByPrimaryKey" >
    <content >
      <![CDATA[
        DELETE FROM wh_return_exception_item
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="updateWhReturnExceptionItemByPrimaryKey" >
    <content >
      <![CDATA[
        UPDATE wh_return_exception_item
        SET <[return_type = :return_type,]>
          <[return_no = :return_no,]>
          <[sku = :sku]>
          <[uuid = :uuid,]>
          <[reason = :reason,]>
          <[scan_time = :scan_time,]>
          <[return_user = :return_user,]>
          <[create_user = :create_user,]>
        id = id
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
</sqlmap>