<?xml version="1.0" encoding="UTF-8" ?>
<sqlmap >
  <sql datasource="dataSource" id="queryWhSkuSaleStatisticReportCount" >
    <content >
      <![CDATA[
        SELECT COUNT(1)
        FROM wh_sku_sale_statistic_report
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND date = :date]>
        <[AND thirty_days_sales_days = :thirty_days_sales_days]>
        <[AND sales_1 = :sales_1]>
        <[AND sales_2 = :sales_2]>
        <[AND sales_3 = :sales_3]>
        <[AND sales_4 = :sales_4]>
        <[AND sales_5 = :sales_5]>
        <[AND sales_6 = :sales_6]>
        <[AND sales_7 = :sales_7]>
        <[AND sales_8 = :sales_8]>
        <[AND sales_9 = :sales_9]>
        <[AND sales_10 = :sales_10]>
        <[AND sales_11 = :sales_11]>
        <[AND sales_12 = :sales_12]>
        <[AND sales_13 = :sales_13]>
        <[AND sales_14 = :sales_14]>
        <[AND sales_15 = :sales_15]>
        <[AND sales_16 = :sales_16]>
        <[AND sales_17 = :sales_17]>
        <[AND sales_18 = :sales_18]>
        <[AND sales_19 = :sales_19]>
        <[AND sales_20 = :sales_20]>
        <[AND sales_21 = :sales_21]>
        <[AND sales_22 = :sales_22]>
        <[AND sales_23 = :sales_23]>
        <[AND sales_24 = :sales_24]>
        <[AND sales_25 = :sales_25]>
        <[AND sales_26 = :sales_26]>
        <[AND sales_27 = :sales_27]>
        <[AND sales_28 = :sales_28]>
        <[AND sales_29 = :sales_29]>
        <[AND sales_30 = :sales_30]>
        <[AND more_sales_30 = :more_sales_30]>
        <[AND created_date = :created_date]>
        <[AND modified_date = :modified_date]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryWhSkuSaleStatisticReportList" >
    <content >
      <![CDATA[
        SELECT id, date, thirty_days_sales_days, sales_1, sales_2, sales_3, sales_4, sales_5, 
        sales_6, sales_7, sales_8, sales_9, sales_10, sales_11, sales_12, sales_13, sales_14, 
        sales_15, sales_16, sales_17, sales_18, sales_19, sales_20, sales_21, sales_22, sales_23, 
        sales_24, sales_25, sales_26, sales_27, sales_28, sales_29, sales_30, more_sales_30, 
        created_date, modified_date
        FROM wh_sku_sale_statistic_report
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND date = :date]>
        <[AND date >= (:from_time)]>
        <[AND date <= (:to_time)]>
        <[AND thirty_days_sales_days = :thirty_days_sales_days]>
        <[AND sales_1 = :sales_1]>
        <[AND sales_2 = :sales_2]>
        <[AND sales_3 = :sales_3]>
        <[AND sales_4 = :sales_4]>
        <[AND sales_5 = :sales_5]>
        <[AND sales_6 = :sales_6]>
        <[AND sales_7 = :sales_7]>
        <[AND sales_8 = :sales_8]>
        <[AND sales_9 = :sales_9]>
        <[AND sales_10 = :sales_10]>
        <[AND sales_11 = :sales_11]>
        <[AND sales_12 = :sales_12]>
        <[AND sales_13 = :sales_13]>
        <[AND sales_14 = :sales_14]>
        <[AND sales_15 = :sales_15]>
        <[AND sales_16 = :sales_16]>
        <[AND sales_17 = :sales_17]>
        <[AND sales_18 = :sales_18]>
        <[AND sales_19 = :sales_19]>
        <[AND sales_20 = :sales_20]>
        <[AND sales_21 = :sales_21]>
        <[AND sales_22 = :sales_22]>
        <[AND sales_23 = :sales_23]>
        <[AND sales_24 = :sales_24]>
        <[AND sales_25 = :sales_25]>
        <[AND sales_26 = :sales_26]>
        <[AND sales_27 = :sales_27]>
        <[AND sales_28 = :sales_28]>
        <[AND sales_29 = :sales_29]>
        <[AND sales_30 = :sales_30]>
        <[AND more_sales_30 = :more_sales_30]>
        <[AND created_date = :created_date]>
        <[AND modified_date = :modified_date]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryWhSkuSaleStatisticReportByPrimaryKey" >
    <content >
      <![CDATA[
        SELECT id, date, thirty_days_sales_days, sales_1, sales_2, sales_3, sales_4, sales_5, 
        sales_6, sales_7, sales_8, sales_9, sales_10, sales_11, sales_12, sales_13, sales_14, 
        sales_15, sales_16, sales_17, sales_18, sales_19, sales_20, sales_21, sales_22, sales_23, 
        sales_24, sales_25, sales_26, sales_27, sales_28, sales_29, sales_30, more_sales_30, 
        created_date, modified_date
        FROM wh_sku_sale_statistic_report
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryWhSkuSaleStatisticReport" >
    <content >
      <![CDATA[
        SELECT id, date, thirty_days_sales_days, sales_1, sales_2, sales_3, sales_4, sales_5, 
        sales_6, sales_7, sales_8, sales_9, sales_10, sales_11, sales_12, sales_13, sales_14, 
        sales_15, sales_16, sales_17, sales_18, sales_19, sales_20, sales_21, sales_22, sales_23, 
        sales_24, sales_25, sales_26, sales_27, sales_28, sales_29, sales_30, more_sales_30, 
        created_date, modified_date
        FROM wh_sku_sale_statistic_report
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND date = :date]>
        <[AND thirty_days_sales_days = :thirty_days_sales_days]>
        <[AND sales_1 = :sales_1]>
        <[AND sales_2 = :sales_2]>
        <[AND sales_3 = :sales_3]>
        <[AND sales_4 = :sales_4]>
        <[AND sales_5 = :sales_5]>
        <[AND sales_6 = :sales_6]>
        <[AND sales_7 = :sales_7]>
        <[AND sales_8 = :sales_8]>
        <[AND sales_9 = :sales_9]>
        <[AND sales_10 = :sales_10]>
        <[AND sales_11 = :sales_11]>
        <[AND sales_12 = :sales_12]>
        <[AND sales_13 = :sales_13]>
        <[AND sales_14 = :sales_14]>
        <[AND sales_15 = :sales_15]>
        <[AND sales_16 = :sales_16]>
        <[AND sales_17 = :sales_17]>
        <[AND sales_18 = :sales_18]>
        <[AND sales_19 = :sales_19]>
        <[AND sales_20 = :sales_20]>
        <[AND sales_21 = :sales_21]>
        <[AND sales_22 = :sales_22]>
        <[AND sales_23 = :sales_23]>
        <[AND sales_24 = :sales_24]>
        <[AND sales_25 = :sales_25]>
        <[AND sales_26 = :sales_26]>
        <[AND sales_27 = :sales_27]>
        <[AND sales_28 = :sales_28]>
        <[AND sales_29 = :sales_29]>
        <[AND sales_30 = :sales_30]>
        <[AND more_sales_30 = :more_sales_30]>
        <[AND created_date = :created_date]>
        <[AND modified_date = :modified_date]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="createWhSkuSaleStatisticReport" >
    <content >
      <![CDATA[
        INSERT INTO wh_sku_sale_statistic_report (date, thirty_days_sales_days, sales_1, sales_2, sales_3, sales_4, sales_5, 
          sales_6, sales_7, sales_8, sales_9, sales_10, sales_11, sales_12, sales_13, 
          sales_14, sales_15, sales_16, sales_17, sales_18, sales_19, sales_20, sales_21, 
          sales_22, sales_23, sales_24, sales_25, sales_26, sales_27, sales_28, sales_29, 
          sales_30, more_sales_30, created_date, modified_date)
        VALUES (:date, :thirty_days_sales_days, :sales_1, :sales_2, :sales_3, :sales_4, :sales_5, 
          :sales_6, :sales_7, :sales_8, :sales_9, :sales_10, :sales_11, :sales_12, :sales_13, 
          :sales_14, :sales_15, :sales_16, :sales_17, :sales_18, :sales_19, :sales_20, :sales_21, 
          :sales_22, :sales_23, :sales_24, :sales_25, :sales_26, :sales_27, :sales_28, :sales_29, 
          :sales_30, :more_sales_30, :created_date, :modified_date)
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="deleteWhSkuSaleStatisticReportByPrimaryKey" >
    <content >
      <![CDATA[
        DELETE FROM wh_sku_sale_statistic_report
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="updateWhSkuSaleStatisticReportByPrimaryKey" >
    <content >
      <![CDATA[
        UPDATE wh_sku_sale_statistic_report
        SET <[date = :date,]>
          <[thirty_days_sales_days = :thirty_days_sales_days,]>
          <[sales_1 = :sales_1,]>
          <[sales_2 = :sales_2,]>
          <[sales_3 = :sales_3,]>
          <[sales_4 = :sales_4,]>
          <[sales_5 = :sales_5,]>
          <[sales_6 = :sales_6,]>
          <[sales_7 = :sales_7,]>
          <[sales_8 = :sales_8,]>
          <[sales_9 = :sales_9,]>
          <[sales_10 = :sales_10,]>
          <[sales_11 = :sales_11,]>
          <[sales_12 = :sales_12,]>
          <[sales_13 = :sales_13,]>
          <[sales_14 = :sales_14,]>
          <[sales_15 = :sales_15,]>
          <[sales_16 = :sales_16,]>
          <[sales_17 = :sales_17,]>
          <[sales_18 = :sales_18,]>
          <[sales_19 = :sales_19,]>
          <[sales_20 = :sales_20,]>
          <[sales_21 = :sales_21,]>
          <[sales_22 = :sales_22,]>
          <[sales_23 = :sales_23,]>
          <[sales_24 = :sales_24,]>
          <[sales_25 = :sales_25,]>
          <[sales_26 = :sales_26,]>
          <[sales_27 = :sales_27,]>
          <[sales_28 = :sales_28,]>
          <[sales_29 = :sales_29,]>
          <[sales_30 = :sales_30,]>
          <[more_sales_30 = :more_sales_30,]>
          <[created_date = :created_date,]>
          <[modified_date = :modified_date,]>
        id = id
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>

  <sql datasource="dataSource" id="deleteByDate" >
    <content >
      <![CDATA[
        DELETE FROM wh_sku_sale_statistic_report WHERE date = :date
      ]]>
    </content>
  </sql>
</sqlmap>