<?xml version="1.0" encoding="UTF-8" ?>
<sqlmap >
  <sql datasource="dataSource" id="querySheinReturnOrderCount" >
    <content >
      <![CDATA[
        SELECT COUNT(1)
        FROM shein_return_order
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND id in (:ids)]>
        <[AND shop_id = :shop_id]>
        <[AND account_number = :account_number]>
        <[AND return_no = :return_no]>
        <[AND return_plan_no = :return_plan_no]>
        <[AND handle_type = :handle_type]>
        <[AND return_method = :return_method]>
        <[AND platform_status = :platform_status]>
        <[AND status = :status]>
        <[AND reason = :reason]>
        <[AND remark = :remark]>
        <[AND return_warehouse = :return_warehouse]>
        <[AND express_no = :express_no]>
        <[AND sku_count = :sku_count]>
        <[AND return_quantity_total = :return_quantity_total]>
        <[AND create_date = :create_date]>
        <[AND receipt_date = :receipt_date]>
        <[AND handle_date = :handle_date]>
        <[AND update_time = :update_time]>

        <[AND create_date >= :createDateStart]>
        <[AND create_date <= :createDateEnd]>
        <[AND receipt_date >= :receiptDateStart]>
        <[AND receipt_date <= :receiptDateEnd]>
        <[AND handle_date >= :handleDateStart]>
        <[AND handle_date <= :handleDateEnd]>
        <[AND account_number in (:accountNumberList)]>
        <[AND return_no in (:returnNoList)]>
        <[AND return_plan_no in (:returnPlanNoList)]>
        <[AND express_no like :expressNoLike]>
        <[:EXPRESS_NO_FILTER]>
        <[AND id in (SELECT return_id FROM shein_return_order_item WHERE sku in (:skuList)) ]>
        <[AND id in (SELECT return_id FROM shein_return_order_item WHERE deliver_order_no in (:deliverOrderNoList)) ]>
      ]]>
    </content>
  </sql>

  <sql datasource="dataSource" id="querySheinReturnOrderAndItems" >
    <content >
      <![CDATA[
        SELECT r.*, ri.*
          , rai.sku, rai.good_quantity, rai.bad_quantity, rai.shelf_quantity
        FROM shein_return_order r
        LEFT JOIN shein_return_order_item ri ON ri.return_id = r.id
        LEFT JOIN wh_intermediate_return_apv_item rai ON ri.return_id = rai.smt_return_id AND ri.sku = rai.sku
        INNER JOIN (
          SELECT id FROM shein_return_order
          WHERE 1 = 1
          <[AND id = :id]>
          <[AND id in (:ids)]>
          <[AND shop_id = :shop_id]>
          <[AND account_number = :account_number]>
          <[AND return_no = :return_no]>
          <[AND return_plan_no = :return_plan_no]>
          <[AND handle_type = :handle_type]>
          <[AND return_method = :return_method]>
          <[AND platform_status = :platform_status]>
          <[AND status = :status]>
          <[AND reason = :reason]>
          <[AND remark = :remark]>
          <[AND return_warehouse = :return_warehouse]>
          <[AND express_no = :express_no]>
          <[AND sku_count = :sku_count]>
          <[AND return_quantity_total = :return_quantity_total]>
          <[AND create_date = :create_date]>
          <[AND receipt_date = :receipt_date]>
          <[AND handle_date = :handle_date]>
          <[AND update_time = :update_time]>

          <[AND create_date >= :createDateStart]>
          <[AND create_date <= :createDateEnd]>
          <[AND receipt_date >= :receiptDateStart]>
          <[AND receipt_date <= :receiptDateEnd]>
          <[AND handle_date >= :handleDateStart]>
          <[AND handle_date <= :handleDateEnd]>
          <[AND account_number in (:accountNumberList)]>
          <[AND return_no in (:returnNoList)]>
          <[AND return_plan_no in (:returnPlanNoList)]>
          <[AND express_no like :expressNoLike]>
          <[:EXPRESS_NO_FILTER]>
          <[AND id in (SELECT return_id FROM shein_return_order_item WHERE sku in (:skuList)) ]>
          <[AND id in (SELECT return_id FROM shein_return_order_item WHERE deliver_order_no in (:deliverOrderNoList)) ]>
          ORDER BY id DESC
          <[:LIMIT]>
        ) t ON t.id = r.id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="querySheinReturnOrderList" >
    <content >
      <![CDATA[
        SELECT id, shop_id, account_number, return_no, return_plan_no, handle_type, return_method,
        platform_status, status, reason, remark, return_warehouse, express_no, sku_count, 
        return_quantity_total, create_date, receipt_date, handle_date, update_time
        FROM shein_return_order
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND shop_id = :shop_id]>
        <[AND account_number = :account_number]>
        <[AND return_no = :return_no]>
        <[AND return_plan_no = :return_plan_no]>
        <[AND handle_type = :handle_type]>
        <[AND return_method = :return_method]>
        <[AND platform_status = :platform_status]>
        <[AND status = :status]>
        <[AND reason = :reason]>
        <[AND remark = :remark]>
        <[AND return_warehouse = :return_warehouse]>
        <[AND express_no = :express_no]>
        <[AND sku_count = :sku_count]>
        <[AND return_quantity_total = :return_quantity_total]>
        <[AND create_date = :create_date]>
        <[AND receipt_date = :receipt_date]>
        <[AND handle_date = :handle_date]>
        <[AND update_time = :update_time]>

        <[AND create_date >= :createDateStart]>
        <[AND create_date <= :createDateEnd]>
        <[AND receipt_date >= :receiptDateStart]>
        <[AND receipt_date <= :receiptDateEnd]>
        <[AND handle_date >= :handleDateStart]>
        <[AND handle_date <= :handleDateEnd]>
        <[AND account_number in (:accountNumberList)]>
        <[AND return_no in (:returnNoList)]>
        <[AND return_plan_no in (:returnPlanNoList)]>
        <[AND express_no like :expressNoLike]>
        <[:EXPRESS_NO_FILTER]>
        <[AND id in (SELECT return_id FROM shein_return_order_item WHERE sku in (:skuList)) ]>
        <[AND id in (SELECT return_id FROM shein_return_order_item WHERE deliver_order_no in (:deliverOrderNoList)) ]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="querySheinReturnOrderByPrimaryKey" >
    <content >
      <![CDATA[
        SELECT id, shop_id, account_number, return_no, return_plan_no, handle_type, return_method,
        platform_status, status, reason, remark, return_warehouse, express_no, sku_count, 
        return_quantity_total, create_date, receipt_date, handle_date, update_time
        FROM shein_return_order
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="querySheinReturnOrder" >
    <content >
      <![CDATA[
        SELECT id, shop_id, account_number, return_no, return_plan_no, handle_type, return_method,
        platform_status, status, reason, remark, return_warehouse, express_no, sku_count, 
        return_quantity_total, create_date, receipt_date, handle_date, update_time
        FROM shein_return_order
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND shop_id = :shop_id]>
        <[AND account_number = :account_number]>
        <[AND return_no = :return_no]>
        <[AND return_plan_no = :return_plan_no]>
        <[AND handle_type = :handle_type]>
        <[AND return_method = :return_method]>
        <[AND platform_status = :platform_status]>
        <[AND status = :status]>
        <[AND reason = :reason]>
        <[AND remark = :remark]>
        <[AND return_warehouse = :return_warehouse]>
        <[AND express_no = :express_no]>
        <[AND sku_count = :sku_count]>
        <[AND return_quantity_total = :return_quantity_total]>
        <[AND create_date = :create_date]>
        <[AND receipt_date = :receipt_date]>
        <[AND handle_date = :handle_date]>
        <[AND update_time = :update_time]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="createSheinReturnOrder" >
    <content >
      <![CDATA[
        INSERT INTO shein_return_order (shop_id, account_number, return_no, return_plan_no, handle_type, return_method,
          platform_status, status, reason, remark, return_warehouse, express_no, sku_count, 
          return_quantity_total, create_date, receipt_date, handle_date, update_time
          )
        VALUES (:shop_id, :account_number, :return_no, :return_plan_no, :handle_type, :return_method,
          :platform_status, :status, :reason, :remark, :return_warehouse, :express_no, :sku_count, 
          :return_quantity_total, :create_date, :receipt_date, :handle_date, :update_time
          )
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="deleteSheinReturnOrderByPrimaryKey" >
    <content >
      <![CDATA[
        DELETE FROM shein_return_order
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="updateSheinReturnOrderByPrimaryKey" >
    <content >
      <![CDATA[
        UPDATE shein_return_order
        SET <[account_number = :account_number,]>
          <[shop_id = :shop_id,]>
          <[return_no = :return_no,]>
          <[return_plan_no = :return_plan_no,]>
          <[handle_type = :handle_type,]>
          <[return_method = :return_method,]>
          <[platform_status = :platform_status,]>
          <[status = :status,]>
          <[reason = :reason,]>
          <[remark = :remark,]>
          <[return_warehouse = :return_warehouse,]>
          <[express_no = :express_no,]>
          <[sku_count = :sku_count,]>
          <[return_quantity_total = :return_quantity_total,]>
          <[create_date = :create_date,]>
          <[receipt_date = :receipt_date,]>
          <[handle_date = :handle_date,]>
          <[update_time = :update_time,]>
        id = id
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
</sqlmap>