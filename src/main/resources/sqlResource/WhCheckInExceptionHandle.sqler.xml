<?xml version="1.0" encoding="UTF-8" ?>
<sqlmap >
  <sql datasource="dataSource" id="queryWhCheckInExceptionHandleCount" >
    <content >
      <![CDATA[
        SELECT COUNT(1)
        FROM wh_check_in_exception_handle
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND exception_id = :exception_id]>
        <[AND exception_id in (:exceptionIds)]>
        <[AND handle_comment = :handle_comment]>
        <[AND handle_way = :handle_way]>
        <[AND status = :status]>
        <[AND created_by = :created_by]>
        <[AND creation_date = :creation_date]>
        <[AND quantity = :quantity]>
        <[AND status in (:statusList)]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryWhCheckInExceptionHandleList" >
    <content >
      <![CDATA[
        SELECT id, exception_id, handle_comment, handle_way, status, created_by, creation_date, create_user_name,
        quantity
        FROM wh_check_in_exception_handle
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND exception_id = :exception_id]>
        <[AND exception_id in (:exceptionIds)]>
        <[AND handle_comment = :handle_comment]>
        <[AND handle_way = :handle_way]>
        <[AND status = :status]>
        <[AND created_by = :created_by]>
        <[AND creation_date = :creation_date]>
        <[AND quantity = :quantity]>
        <[AND status in (:statusList)]>
        <[:ORDER_BY]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryWhCheckInExceptionHandleByPrimaryKey" >
    <content >
      <![CDATA[
        SELECT id, exception_id, handle_comment, handle_way, status, created_by, creation_date, create_user_name,
        quantity
        FROM wh_check_in_exception_handle
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryWhCheckInExceptionHandle" >
    <content >
      <![CDATA[
        SELECT id, exception_id, handle_comment, handle_way, status, created_by, creation_date, create_user_name,
        quantity
        FROM wh_check_in_exception_handle
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND exception_id = :exception_id]>
        <[AND handle_comment = :handle_comment]>
        <[AND handle_way = :handle_way]>
        <[AND status = :status]>
        <[AND created_by = :created_by]>
        <[AND creation_date = :creation_date]>
        <[AND quantity = :quantity]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="createWhCheckInExceptionHandle" >
    <content >
      <![CDATA[
        INSERT INTO wh_check_in_exception_handle (exception_id, handle_comment, handle_way, status, created_by, creation_date, create_user_name,
          quantity)
        VALUES (:exception_id, :handle_comment, :handle_way, :status, :created_by, :creation_date, :create_user_name,
          :quantity)
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="deleteWhCheckInExceptionHandleByPrimaryKey" >
    <content >
      <![CDATA[
        DELETE FROM wh_check_in_exception_handle
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="updateWhCheckInExceptionHandleByPrimaryKey" >
    <content >
      <![CDATA[
        UPDATE wh_check_in_exception_handle
        SET <[exception_id = :exception_id,]>
          <[handle_comment = :handle_comment,]>
          <[handle_way = :handle_way,]>
          <[status = :status,]>
          <[created_by = :created_by,]>
          <[create_user_name = :create_user_name,]>
          <[creation_date = :creation_date,]>
          <[quantity = :quantity,]>
        id = id
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
</sqlmap>