<?xml version="1.0" encoding="UTF-8" ?>
<sqlmap >
  <sql datasource="dataSource" id="queryApvOversizeCount" >
    <content >
      <![CDATA[
        SELECT COUNT(1)
        FROM apv_oversize
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND apv_no = :apv_no]>
        <[AND weight = :weight]>
        <[AND length = :length]>
        <[AND width = :width]>
        <[AND hight = :hight]>
        <[AND status = :status]>
        <[AND created_by = :created_by]>
        <[AND creation_date = :creation_date]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryApvOversizeList" >
    <content >
      <![CDATA[
        SELECT id, apv_no, weight, length, width, hight, status, created_by, creation_date, intercept_date, confirm_date, remark, label
        FROM apv_oversize
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND apv_no = :apv_no]>
        <[AND weight = :weight]>
        <[AND length = :length]>
        <[AND width = :width]>
        <[AND hight = :hight]>
        <[AND status = :status]>
        <[AND created_by = :created_by]>
        <[AND creation_date = :creation_date]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryApvOversizeByPrimaryKey" >
    <content >
      <![CDATA[
        SELECT id, apv_no, weight, length, width, hight, status, created_by, creation_date, intercept_date, confirm_date, remark, label
        FROM apv_oversize
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryApvOversize" >
    <content >
      <![CDATA[
        SELECT id, apv_no, weight, length, width, hight, status, created_by, creation_date, intercept_date, confirm_date, remark, label
        FROM apv_oversize
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND apv_no = :apv_no]>
        <[AND weight = :weight]>
        <[AND length = :length]>
        <[AND width = :width]>
        <[AND hight = :hight]>
        <[AND status = :status]>
        <[AND created_by = :created_by]>
        <[AND creation_date = :creation_date]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="createApvOversize" >
    <content >
      <![CDATA[
        INSERT INTO apv_oversize (apv_no, weight, length, width, hight, status, created_by, creation_date, intercept_date, confirm_date, remark, label
          )
        VALUES (:apv_no, :weight, :length, :width, :hight, :status, :created_by, :creation_date, :intercept_date, :confirm_date, :remark, :label
          )
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="deleteApvOversizeByPrimaryKey" >
    <content >
      <![CDATA[
        DELETE FROM apv_oversize
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="updateApvOversizeByPrimaryKey" >
    <content >
      <![CDATA[
        UPDATE apv_oversize
        SET <[apv_no = :apv_no,]>
          <[weight = :weight,]>
          <[length = :length,]>
          <[width = :width,]>
          <[hight = :hight,]>
          <[status = :status,]>
          <[created_by = :created_by,]>
          <[creation_date = :creation_date,]>
          <[intercept_date = :intercept_date,]>
          <[confirm_date = :confirm_date,]>
          <[remark = :remark,]>
          <[label = :label,]>
        id = id
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>

  <sql datasource="dataSource" id="updateApvOversizeByApvNo" >
    <content >
      <![CDATA[
        UPDATE apv_oversize
        SET
          <[weight = :weight,]>
          <[length = :length,]>
          <[width = :width,]>
          <[hight = :hight,]>
          <[status = :status,]>
          <[confirm_date = :confirm_date,]>
          <[remark = :remark,]>
          <[label = :label,]>
        id = id
        WHERE 1 = 1
        AND apv_no = :apv_no
        <[AND status = :old_status]>
      ]]>
    </content>
  </sql>

  <sql datasource="dataSource" id="resetApvOversize" >
    <content >
      <![CDATA[
        UPDATE apv_oversize
        SET
          weight = NULL,
          length = NULL,
          width = NULL,
          hight = NULL,
          <[status = :status,]>
        id = id
        WHERE 1 = 1
        AND apv_no = :apv_no
        <[AND status = :old_status]>
      ]]>
    </content>
  </sql>
</sqlmap>