<?xml version="1.0" encoding="UTF-8" ?>
<sqlmap >
  <sql datasource="dataSource" id="queryWhInventoryDetailsCount" >
    <content >
      <![CDATA[
        SELECT COUNT(1)
        FROM wh_inventory_details
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND article_number = :article_number]>
        <[AND name = :name]>
        <[AND order_no = :order_no]>
        <[AND stock_type = :stock_type]>
        <[AND order_type = :order_type]>
        <[AND initial_stock = :initial_stock]>
        <[AND change_stock = :change_stock]>
        <[AND closing_stock = :closing_stock]>
        <[AND creation_date = :creation_date]>
        <[AND creation_by = :creation_by]>
        <[AND last_update_date = :last_update_date]>
        <[AND last_updated_by = :last_updated_by]>
        <[AND id IN (:idList)]>
        <[AND article_number IN (:articleNumberList)]>
        <[AND order_no IN (:orderNoList)]>
        <[AND creation_date >= :from_creation_date]>
	    <[AND creation_date <= :to_creation_date]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryWhInventoryDetailsList" >
    <content >
      <![CDATA[
        SELECT id, article_number, name, order_no, stock_type, order_type, initial_stock, 
        change_stock, closing_stock, creation_date, creation_by, last_update_date, last_updated_by
        FROM wh_inventory_details
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND article_number = :article_number]>
        <[AND name = :name]>
        <[AND order_no = :order_no]>
        <[AND stock_type = :stock_type]>
        <[AND order_type = :order_type]>
        <[AND initial_stock = :initial_stock]>
        <[AND change_stock = :change_stock]>
        <[AND closing_stock = :closing_stock]>
        <[AND creation_date = :creation_date]>
        <[AND creation_by = :creation_by]>
        <[AND last_update_date = :last_update_date]>
        <[AND last_updated_by = :last_updated_by]>
        <[AND id IN (:idList)]>
        <[AND article_number IN (:articleNumberList)]>
        <[AND order_no IN (:orderNoList)]>
        <[AND creation_date >= :from_creation_date]>
	    <[AND creation_date <= :to_creation_date]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryWhInventoryDetailsByPrimaryKey" >
    <content >
      <![CDATA[
        SELECT id, article_number, name, order_no, stock_type, order_type, initial_stock, 
        change_stock, closing_stock, creation_date, creation_by, last_update_date, last_updated_by
        FROM wh_inventory_details
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryWhInventoryDetails" >
    <content >
      <![CDATA[
        SELECT id, article_number, name, order_no, stock_type, order_type, initial_stock, 
        change_stock, closing_stock, creation_date, creation_by, last_update_date, last_updated_by
        FROM wh_inventory_details
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND article_number = :article_number]>
        <[AND name = :name]>
        <[AND order_no = :order_no]>
        <[AND stock_type = :stock_type]>
        <[AND order_type = :order_type]>
        <[AND initial_stock = :initial_stock]>
        <[AND change_stock = :change_stock]>
        <[AND closing_stock = :closing_stock]>
        <[AND creation_date = :creation_date]>
        <[AND creation_by = :creation_by]>
        <[AND last_update_date = :last_update_date]>
        <[AND last_updated_by = :last_updated_by]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="createWhInventoryDetails" >
    <content >
      <![CDATA[
        INSERT INTO wh_inventory_details (article_number, name, order_no, stock_type, order_type, initial_stock, 
          change_stock, closing_stock, creation_date, creation_by, last_update_date, 
          last_updated_by)
        VALUES (:article_number, :name, :order_no, :stock_type, :order_type, :initial_stock, 
          :change_stock, :closing_stock, :creation_date, :creation_by, :last_update_date, 
          :last_updated_by)
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="deleteWhInventoryDetailsByPrimaryKey" >
    <content >
      <![CDATA[
        DELETE FROM wh_inventory_details
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="updateWhInventoryDetailsByPrimaryKey" >
    <content >
      <![CDATA[
        UPDATE wh_inventory_details
        SET <[article_number = :article_number,]>
          <[name = :name,]>
          <[order_no = :order_no,]>
          <[stock_type = :stock_type,]>
          <[order_type = :order_type,]>
          <[initial_stock = :initial_stock,]>
          <[change_stock = :change_stock,]>
          <[closing_stock = :closing_stock,]>
          <[creation_date = :creation_date,]>
          <[creation_by = :creation_by,]>
          <[last_update_date = :last_update_date,]>
          <[last_updated_by = :last_updated_by,]>
        id = id
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
</sqlmap>