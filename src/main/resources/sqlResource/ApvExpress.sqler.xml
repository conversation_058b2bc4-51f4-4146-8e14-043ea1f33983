<?xml version="1.0" encoding="UTF-8" ?>
<sqlmap >
  <sql datasource="dataSource" id="queryApvExpressCount" >
    <content >
      <![CDATA[
        SELECT COUNT(1)
        FROM apv_express
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND apv_no = :apv_no]>
        <[AND weight = :weight]>
        <[AND length = :length]>
        <[AND width = :width]>
        <[AND hight = :hight]>
        <[AND status = :status]>
        <[AND created_by = :created_by]>
        <[AND creation_date = :creation_date]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryApvExpressList" >
    <content >
      <![CDATA[
        SELECT id, apv_no, weight, length, width, hight, status, created_by, creation_date, box_number, box_info_json, shipping_company, shipping_method, tracking_number ,
        shipping_order_no, pdf_url,bat_no
        FROM apv_express
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND apv_no = :apv_no]>
        <[AND weight = :weight]>
        <[AND length = :length]>
        <[AND width = :width]>
        <[AND hight = :hight]>
        <[AND status = :status]>
        <[AND created_by = :created_by]>
        <[AND creation_date = :creation_date]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryApvExpressByPrimaryKey" >
    <content >
      <![CDATA[
        SELECT id, apv_no, weight, length, width, hight, status, created_by, creation_date, box_number, box_info_json, shipping_company, shipping_method, tracking_number
        ,shipping_order_no, pdf_url,bat_no
        FROM apv_express
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryApvExpress" >
    <content >
      <![CDATA[
        SELECT id, apv_no, weight, length, width, hight, status, created_by, creation_date, box_number, box_info_json, shipping_company, shipping_method, tracking_number
        ,shipping_order_no, pdf_url,bat_no
        FROM apv_express
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND apv_no = :apv_no]>
        <[AND weight = :weight]>
        <[AND length = :length]>
        <[AND width = :width]>
        <[AND hight = :hight]>
        <[AND status = :status]>
        <[AND created_by = :created_by]>
        <[AND creation_date = :creation_date]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="createApvExpress" >
    <content >
      <![CDATA[
        INSERT INTO apv_express (apv_no, weight, length, width, hight, status, created_by, creation_date, box_number, box_info_json
          )
        VALUES (:apv_no, :weight, :length, :width, :hight, :status, :created_by, :creation_date, :box_number, :box_info_json
          )
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="deleteApvExpressByPrimaryKey" >
    <content >
      <![CDATA[
        DELETE FROM apv_express
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="updateApvExpressByPrimaryKey" >
    <content >
      <![CDATA[
        UPDATE apv_express
        SET <[apv_no = :apv_no,]>
          <[weight = :weight,]>
          <[length = :length,]>
          <[width = :width,]>
          <[hight = :hight,]>
          <[status = :status,]>
          <[created_by = :created_by,]>
          <[creation_date = :creation_date,]>
          <[box_number = :box_number,]>
          <[box_info_json = :box_info_json,]>
          <[shipping_company = :shipping_company,]>
          <[shipping_method = :shipping_method,]>
          <[tracking_number = :tracking_number,]>
          <[shipping_order_no = :shipping_order_no,]>
          <[pdf_url = :pdf_url,]>
          <[bat_no = :bat_no,]>
        id = id
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>

  <sql datasource="dataSource" id="updateApvExpressByApvNo" >
    <content >
      <![CDATA[
        UPDATE apv_express
        SET
          <[weight = :weight,]>
          <[length = :length,]>
          <[width = :width,]>
          <[hight = :hight,]>
          <[status = :status,]>
        id = id
        WHERE 1 = 1
        AND apv_no = :apv_no
        <[AND status = :old_status]>
      ]]>
    </content>
  </sql>

  <sql datasource="dataSource" id="resetApvExpress" >
    <content >
      <![CDATA[
        UPDATE apv_express
        SET
          weight = NULL,
          length = NULL,
          width = NULL,
          hight = NULL,
          box_number = NULL,
          box_info_json = NULL,
          <[status = :status,]>
        id = id
        WHERE 1 = 1
        AND apv_no = :apv_no
        <[AND status = :old_status]>
      ]]>
    </content>
  </sql>
</sqlmap>