<?xml version="1.0" encoding="UTF-8" ?>
<sqlmap >
  <sql datasource="dataSource" id="queryWhStockMoveCount" >
    <content >
      <![CDATA[
        SELECT COUNT(1)
        FROM wh_stock_move
        WHERE 1 = 1
        <[AND move_id = :move_id]>
        <[AND quantity = :quantity]>
        <[AND out_location = :out_location]>
        <[AND in_location = :in_location]>
        <[AND create_by = :create_by]>
        <[AND create_date = :create_date]>
        <[AND last_update_date = :last_update_date]>
        <[AND create_date >= :fromCreateDate]>
        <[AND create_date <= :toCreateDate]>
        <[AND stock_type = :stock_type]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryWhStockMoveList" >
    <content >
      <![CDATA[
        SELECT move_id, quantity, out_location, in_location, create_by, create_date, last_update_date, remark, stock_type
        FROM wh_stock_move
        WHERE 1 = 1
        <[AND move_id = :move_id]>
        <[AND quantity = :quantity]>
        <[AND out_location = :out_location]>
        <[AND in_location = :in_location]>
        <[AND create_by = :create_by]>
        <[AND create_date = :create_date]>
        <[AND last_update_date = :last_update_date]>
        <[AND create_date >= :fromCreateDate]>
        <[AND create_date <= :toCreateDate]>
        <[AND stock_type = :stock_type]>
        ORDER BY create_date DESC
      ]]>
    </content>
  </sql>

  <sql datasource="dataSource" id="queryWhStockMoveAndItemsCount" >
    <content >
      <![CDATA[
        SELECT COUNT(1)
        FROM wh_stock_move move
        WHERE 1 = 1
        <[AND move.move_id = :move_id]>
        <[AND move_id IN (:move_id_list)]>
        <[AND move.quantity = :quantity]>
        <[AND move.out_location = :out_location]>
        <[AND move.in_location = :in_location]>
        <[AND move.create_by = :create_by]>
        <[AND move.create_date = :create_date]>
        <[AND move.last_update_date = :last_update_date]>
        <[AND move.create_date >= :fromCreateDate]>
        <[AND move.create_date <= :toCreateDate]>
        <[AND move.move_id IN ( SELECT move_id FROM wh_stock_move_item WHERE sku = :sku)]>
        <[AND move.move_id IN ( SELECT move_id FROM wh_stock_move_item WHERE store = :store)]>
        <[AND move.stock_type = :stock_type]>
      ]]>
    </content>
  </sql>

  <sql datasource="dataSource" id="queryWhStockMoveAndItemsList" >
    <content >
      <![CDATA[
        SELECT move.move_id, move.quantity, move.out_location, move.in_location, move.create_by, move.create_date, move.last_update_date,
        item.move_id, item.sku, item.quantity, item.out_location, item.in_location, move.remark, move.stock_type, item.store, item.site, item.location_number
        FROM wh_stock_move move
        LEFT JOIN wh_stock_move_item item ON item.move_id = move.move_id
        INNER JOIN (
            SELECT move_id FROM wh_stock_move
            WHERE 1 = 1
            <[AND move_id = :move_id]>
            <[AND move_id IN (:move_id_list)]>
            <[AND quantity = :quantity]>
            <[AND out_location = :out_location]>
            <[AND in_location = :in_location]>
            <[AND create_by = :create_by]>
            <[AND create_date = :create_date]>
            <[AND last_update_date = :last_update_date]>
            <[AND create_date >= :fromCreateDate]>
            <[AND create_date <= :toCreateDate]>
            <[AND move_id IN ( SELECT move_id FROM wh_stock_move_item WHERE sku = :sku)]>
            <[AND move_id IN ( SELECT move_id FROM wh_stock_move_item WHERE store = :store)]>
            <[AND stock_type = :stock_type]>
            ORDER BY create_date DESC
            <[:LIMIT]>
        )m ON m.move_id = move.move_id
      ]]>
    </content>
  </sql>


  <sql datasource="dataSource" id="queryWhStockMoveByPrimaryKey" >
    <content >
      <![CDATA[
        SELECT move_id, quantity, out_location, in_location, create_by, create_date, last_update_date, remark, stock_type
        FROM wh_stock_move
        WHERE 1 = 1
        AND move_id = :move_id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryWhStockMove" >
    <content >
      <![CDATA[
        SELECT move_id, quantity, out_location, in_location, create_by, create_date, last_update_date, remark, stock_type
        FROM wh_stock_move
        WHERE 1 = 1
        <[AND move_id = :move_id]>
        <[AND quantity = :quantity]>
        <[AND out_location = :out_location]>
        <[AND in_location = :in_location]>
        <[AND create_by = :create_by]>
        <[AND create_date = :create_date]>
        <[AND last_update_date = :last_update_date]>
        <[AND stock_type = :stock_type]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="createWhStockMove" >
    <content >
      <![CDATA[
        INSERT INTO wh_stock_move (quantity, out_location, in_location, create_by, create_date, last_update_date, remark, stock_type
          )
        VALUES (:quantity, :out_location, :in_location, :create_by, :create_date, :last_update_date, :remark, :stock_type
          )
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="deleteWhStockMoveByPrimaryKey" >
    <content >
      <![CDATA[
        DELETE FROM wh_stock_move
        WHERE 1 = 1
        AND move_id = :move_id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="updateWhStockMoveByPrimaryKey" >
    <content >
      <![CDATA[
        UPDATE wh_stock_move
        SET <[quantity = :quantity,]>
          <[out_location = :out_location,]>
          <[in_location = :in_location,]>
          <[remark = :remark,]>
          <[create_by = :create_by,]>
          <[create_date = :create_date,]>
          <[last_update_date = :last_update_date,]>
          <[stock_type = :stock_type,]>
        move_id = move_id
        WHERE 1 = 1
        AND move_id = :move_id
      ]]>
    </content>
  </sql>
</sqlmap>