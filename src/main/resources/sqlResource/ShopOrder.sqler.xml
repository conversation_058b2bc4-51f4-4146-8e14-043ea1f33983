<?xml version="1.0" encoding="UTF-8" ?>
<sqlmap >
  <sql datasource="dataSource" id="queryShopOrderCount" >
    <content >
      <![CDATA[
        SELECT COUNT(1)
        FROM shop_order
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND emp_no = :emp_no]>
        <[AND emp_name = :emp_name]>
        <[AND recipient = :recipient]>
        <[AND order_no = :order_no]>
        <[AND merchant_order_id = :merchant_order_id]>
        <[AND status = :status]>
        <[AND order_total = :order_total]>
        <[AND refund_amount = :refund_amount]>
        <[AND shipping_address_details = :shipping_address_details]>
        <[AND tel = :tel]>
        <[AND delivery_method = :delivery_method]>
        <[AND shipping_cost = :shipping_cost]>
        <[AND payment_time = :payment_time]>
        <[AND is_refunded = :is_refunded]>
        <[AND cancel_date = :cancel_date]>
        <[AND cancel_by = :cancel_by]>
        <[AND creation_date = :creation_date]>
        <[AND sync_time = :sync_time]>
        <[AND pick_by = :pick_by]>
        <[AND pick_end_time = :pick_end_time]>
        <[AND grid_by = :grid_by]>
        <[AND grid_time = :grid_time]>
        <[AND ship_by = :ship_by]>
        <[AND ship_time = :ship_time]>
        <[AND pack_by = :pack_by]>
        <[AND pack_time = :pack_time]>
        <[AND shipping_company = :shipping_company]>
        <[AND shipping_order_no = :shipping_order_no]>
        <[AND sync_time >= :from_sync_time]>
        <[AND sync_time <= :to_sync_time]>
        <[AND pick_end_time >= :from_pick_end_time]>
        <[AND pick_end_time <= :to_pick_end_time]>
        <[AND grid_time >= :from_grid_time]>
        <[AND grid_time <= :to_grid_time]>
        <[AND ship_time >= :from_ship_time]>
        <[AND ship_time <= :to_ship_time]>
        <[AND pack_time >= :from_pack_time]>
        <[AND pack_time <= :to_pack_time]>
        <[AND order_no IN (:order_no_list)]>
        <[AND id IN (:id_list)]>
        <[AND shipping_order_no IN (:shipping_order_no_list)]>
        <[AND status IN (:status_list)]>
        <[AND id IN (SELECT order_id FROM shop_order_item WHERE sku = :sku)]>
        <[AND id IN (SELECT order_id FROM shop_order_item WHERE sku IN (:sku_list))]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryShopOrderList" >
    <content >
      <![CDATA[
        SELECT s.id, s.emp_no, s.emp_name, s.order_no, s.merchant_order_id, s.status, s.order_total,s.recipient,s.payment_amount,
        s.refund_amount, s.shipping_address_details, s.tel, s.delivery_method, s.shipping_cost, s.payment_time,s.remark,
        s.is_refunded, s.cancel_date, s.cancel_by, s.creation_date, s.sync_time, s.pick_by, s.pick_end_time,s.weight,
        s.grid_by, s.grid_time, s.ship_by, s.ship_time, s.shipping_company, s.shipping_order_no,s.pack_time,s.pack_by,
        i.id, i.order_id, i.sku, i.image_url, i.name, i.discounted_price, i.cost_price, i.quantity,s.update_stock,
        i.allot_qty, i.pick_qty, i.grid_qty, i.remark
        ,(
          SELECT
              GROUP_CONCAT( ws.location_number )
          FROM
              wh_apv_out_stock_Chain wsc
              LEFT JOIN wh_stock ws ON wsc.stock_id = ws.id
          WHERE
              wsc.relevant_no = s.order_no
              AND wsc.sku = i.sku
          GROUP BY
              wsc.relevant_no,
              wsc.sku 
	    ) AS location
        FROM shop_order s
        LEFT JOIN shop_order_item i ON i.order_id = s.id
        INNER JOIN (
          SELECT id
		  FROM shop_order
          WHERE 1 = 1
          <[AND id = :id]>
          <[AND emp_no = :emp_no]>
          <[AND emp_name = :emp_name]>
          <[AND recipient = :recipient]>
          <[AND order_no = :order_no]>
          <[AND merchant_order_id = :merchant_order_id]>
          <[AND status = :status]>
          <[AND order_total = :order_total]>
          <[AND refund_amount = :refund_amount]>
          <[AND shipping_address_details = :shipping_address_details]>
          <[AND tel = :tel]>
          <[AND delivery_method = :delivery_method]>
          <[AND shipping_cost = :shipping_cost]>
          <[AND payment_time = :payment_time]>
          <[AND is_refunded = :is_refunded]>
          <[AND cancel_date = :cancel_date]>
          <[AND cancel_by = :cancel_by]>
          <[AND creation_date = :creation_date]>
          <[AND sync_time = :sync_time]>
          <[AND pick_by = :pick_by]>
          <[AND pick_end_time = :pick_end_time]>
          <[AND grid_by = :grid_by]>
          <[AND grid_time = :grid_time]>
          <[AND ship_by = :ship_by]>
          <[AND ship_time = :ship_time]>
          <[AND pack_by = :pack_by]>
          <[AND pack_time = :pack_time]>
          <[AND shipping_company = :shipping_company]>
          <[AND shipping_order_no = :shipping_order_no]>
          <[AND sync_time >= :from_sync_time]>
          <[AND sync_time <= :to_sync_time]>
          <[AND pick_end_time >= :from_pick_end_time]>
          <[AND pick_end_time <= :to_pick_end_time]>
          <[AND grid_time >= :from_grid_time]>
          <[AND grid_time <= :to_grid_time]>
          <[AND ship_time >= :from_ship_time]>
          <[AND ship_time <= :to_ship_time]>
          <[AND pack_time >= :from_pack_time]>
          <[AND pack_time <= :to_pack_time]>
          <[AND order_no IN (:order_no_list)]>
          <[AND id IN (:id_list)]>
          <[AND shipping_order_no IN (:shipping_order_no_list)]>
          <[AND status IN (:status_list)]>
          <[AND id IN (SELECT order_id FROM shop_order_item WHERE sku = :sku)]>
          <[AND id IN (SELECT order_id FROM shop_order_item WHERE sku IN (:sku_list))]>
          <[:LIMIT]>
        ) so on so.id = s.id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryShopOrderByPrimaryKey" >
    <content >
      <![CDATA[
        SELECT id, emp_no, emp_name, order_no, merchant_order_id, status, order_total, recipient,payment_amount,
        refund_amount, shipping_address_details, tel, delivery_method, shipping_cost, payment_time, update_stock,
        is_refunded, cancel_date, cancel_by, creation_date, sync_time, pick_by, pick_end_time, weight,remark,
        grid_by, grid_time, ship_by, ship_time, shipping_company, shipping_order_no,pack_time,pack_by
        FROM shop_order
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryShopOrder" >
    <content >
      <![CDATA[
        SELECT id, emp_no, emp_name, order_no, merchant_order_id, status, order_total, recipient,payment_amount,
        refund_amount, shipping_address_details, tel, delivery_method, shipping_cost, payment_time, update_stock,
        is_refunded, cancel_date, cancel_by, creation_date, sync_time, pick_by, pick_end_time, weight,remark,
        grid_by, grid_time, ship_by, ship_time, shipping_company, shipping_order_no,pack_time,pack_by
        FROM shop_order
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND emp_no = :emp_no]>
        <[AND emp_name = :emp_name]>
        <[AND recipient = :recipient]>
        <[AND order_no = :order_no]>
        <[AND merchant_order_id = :merchant_order_id]>
        <[AND status = :status]>
        <[AND order_total = :order_total]>
        <[AND refund_amount = :refund_amount]>
        <[AND shipping_address_details = :shipping_address_details]>
        <[AND tel = :tel]>
        <[AND delivery_method = :delivery_method]>
        <[AND shipping_cost = :shipping_cost]>
        <[AND payment_time = :payment_time]>
        <[AND is_refunded = :is_refunded]>
        <[AND cancel_date = :cancel_date]>
        <[AND cancel_by = :cancel_by]>
        <[AND creation_date = :creation_date]>
        <[AND sync_time = :sync_time]>
        <[AND pick_by = :pick_by]>
        <[AND pick_end_time = :pick_end_time]>
        <[AND grid_by = :grid_by]>
        <[AND grid_time = :grid_time]>
        <[AND ship_by = :ship_by]>
        <[AND ship_time = :ship_time]>
        <[AND pack_by = :pack_by]>
        <[AND pack_time = :pack_time]>
        <[AND shipping_company = :shipping_company]>
        <[AND shipping_order_no = :shipping_order_no]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="createShopOrder" >
    <content >
      <![CDATA[
        INSERT INTO shop_order (emp_no, emp_name, order_no, merchant_order_id, status, order_total, payment_amount,
          refund_amount, shipping_address_details, tel, delivery_method, shipping_cost, recipient,update_stock,
          payment_time, is_refunded, cancel_date, cancel_by, creation_date, sync_time, pack_time,pack_by,
          pick_by, pick_end_time, grid_by, grid_time, ship_by, ship_time, shipping_company, weight,remark,
          shipping_order_no)
        VALUES (:emp_no, :emp_name, :order_no, :merchant_order_id, :status, :order_total, :payment_amount,
          :refund_amount, :shipping_address_details, :tel, :delivery_method, :shipping_cost, :recipient,:update_stock,
          :payment_time, :is_refunded, :cancel_date, :cancel_by, :creation_date, :sync_time, :pack_time,:pack_by,
          :pick_by, :pick_end_time, :grid_by, :grid_time, :ship_by, :ship_time, :shipping_company, :weight,:remark,
          :shipping_order_no)
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="deleteShopOrderByPrimaryKey" >
    <content >
      <![CDATA[
        DELETE FROM shop_order
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>

  <sql datasource="dataSource" id="queryShopUserList" >
    <content >
      <![CDATA[
        SELECT emp_no,emp_name FROM shop_order GROUP BY emp_no
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="updateShopOrderByPrimaryKey" >
    <content >
      <![CDATA[
        UPDATE shop_order
        SET <[emp_no = :emp_no,]>
          <[emp_name = :emp_name,]>
          <[recipient = :recipient,]>
          <[order_no = :order_no,]>
          <[merchant_order_id = :merchant_order_id,]>
          <[status = :status,]>
          <[order_total = :order_total,]>
          <[refund_amount = :refund_amount,]>
          <[shipping_address_details = :shipping_address_details,]>
          <[tel = :tel,]>
          <[delivery_method = :delivery_method,]>
          <[shipping_cost = :shipping_cost,]>
          <[payment_time = :payment_time,]>
          <[is_refunded = :is_refunded,]>
          <[cancel_date = :cancel_date,]>
          <[cancel_by = :cancel_by,]>
          <[creation_date = :creation_date,]>
          <[sync_time = :sync_time,]>
          <[pick_by = :pick_by,]>
          <[pick_end_time = :pick_end_time,]>
          <[grid_by = :grid_by,]>
          <[grid_time = :grid_time,]>
          <[ship_by = :ship_by,]>
          <[ship_time = :ship_time,]>
          <[pack_by = :pack_by,]>
          <[pack_time = :pack_time,]>
          <[shipping_company = :shipping_company,]>
          <[shipping_order_no = :shipping_order_no,]>
          <[weight = :weight,]>
          <[payment_amount = :payment_amount,]>
          <[update_stock = :update_stock,]>
          <[remark = :remark,]>
          <[wms_check_by = :wms_check_by,]>
          <[wms_check_time = :wms_check_time,]>
        id = id
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  <sql datasource="dataSource" id="getPrintGridData" >
    <content >
      <![CDATA[
        SELECT
            g.serial_number,g.number,s.order_no, s.recipient, s.shipping_address_details,s.tel,s.delivery_method
        FROM
            wh_apv_grid g
            LEFT JOIN shop_order s ON s.id = g.apv_id
        WHERE g.location = 1
            AND s.order_no IS NOT NULL
            AND g.serial_number = :serial_number
      ]]>
    </content>
  </sql>
</sqlmap>