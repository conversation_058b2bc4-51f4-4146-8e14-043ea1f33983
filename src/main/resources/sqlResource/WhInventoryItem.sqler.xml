<?xml version="1.0" encoding="UTF-8" ?>
<sqlmap >
  <sql datasource="dataSource" id="queryWhInventoryItemCount" >
    <content >
      <![CDATA[
        SELECT COUNT(1)
        FROM wh_inventory_item
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND id IN (:id_list)]>
        <[AND inventory_id = :inventory_id]>
        <[AND sku = :sku]>
        <[AND sku_location = :sku_location]>
        <[AND sku_name = :sku_name]>
        <[AND sku_num = :sku_num]>
        <[AND inventory_num = :inventory_num]>
        <[AND differences_num = :differences_num]>
        <[AND price = :price]>
        <[AND status = :status]>
        <[AND remark = :remark]>
        <[AND created_by = :created_by]>
        <[AND created_date = :created_date]>
        <[AND last_update_date = :last_update_date]>
        <[AND last_updated_by = :last_updated_by]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryWhInventoryItemList" >
    <content >
      <![CDATA[
        SELECT item.id, item.inventory_id, item.sku, whsku.location_number sku_location, whsku.name sku_name, item.sku_num, item.inventory_num, item.differences_num, item.
        price, item.status, item.remark, item.created_by, item.created_date, item.last_update_date, item.last_updated_by
        FROM wh_inventory_item item
        LEFT JOIN wh_sku whsku on whsku.sku = item.sku
        WHERE 1 = 1
        <[AND item.id = :id]>
        <[AND item.id IN (:id_list)]>
        <[AND item.inventory_id = :inventory_id]>
        <[AND item.sku = :sku]>
        <[AND item.sku_location = :sku_location]>
        <[AND item.sku_name = :sku_name]>
        <[AND item.sku_num = :sku_num]>
        <[AND item.inventory_num = :inventory_num]>
        <[AND item.differences_num = :differences_num]>
        <[AND item.price = :price]>
        <[AND item.status = :status]>
        <[AND item.remark = :remark]>
        <[AND item.created_by = :created_by]>
        <[AND item.created_date = :created_date]>
        <[AND item.last_update_date = :last_update_date]>
        <[AND item.last_updated_by = :last_updated_by]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryWhInventoryItemByPrimaryKey" >
    <content >
      <![CDATA[
        SELECT id, inventory_id, sku, sku_location, sku_name, sku_num, inventory_num, differences_num, 
        price, status, remark, created_by, created_date, last_update_date, last_updated_by
        FROM wh_inventory_item
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryWhInventoryItem" >
    <content >
      <![CDATA[
        SELECT id, inventory_id, sku, sku_location, sku_name, sku_num, inventory_num, differences_num, 
        price, status, remark, created_by, created_date, last_update_date, last_updated_by
        FROM wh_inventory_item
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND inventory_id = :inventory_id]>
        <[AND sku = :sku]>
        <[AND sku_location = :sku_location]>
        <[AND sku_name = :sku_name]>
        <[AND sku_num = :sku_num]>
        <[AND inventory_num = :inventory_num]>
        <[AND differences_num = :differences_num]>
        <[AND price = :price]>
        <[AND status = :status]>
        <[AND remark = :remark]>
        <[AND created_by = :created_by]>
        <[AND created_date = :created_date]>
        <[AND last_update_date = :last_update_date]>
        <[AND last_updated_by = :last_updated_by]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="createWhInventoryItem" >
    <content >
      <![CDATA[
        INSERT INTO wh_inventory_item (inventory_id, sku, sku_location, sku_name, sku_num, inventory_num, 
          differences_num, price, status, remark, created_by, created_date, last_update_date, 
          last_updated_by)
        VALUES (:inventory_id, :sku, :sku_location, :sku_name, :sku_num, :inventory_num, 
          :differences_num, :price, :status, :remark, :created_by, :created_date, :last_update_date, 
          :last_updated_by)
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="deleteWhInventoryItemByPrimaryKey" >
    <content >
      <![CDATA[
        DELETE FROM wh_inventory_item
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="updateWhInventoryItemByPrimaryKey" >
    <content >
      <![CDATA[
        UPDATE wh_inventory_item
        SET <[inventory_id = :inventory_id,]>
          <[sku = :sku,]>
          <[sku_location = :sku_location,]>
          <[sku_name = :sku_name,]>
          <[sku_num = :sku_num,]>
          <[inventory_num = :inventory_num,]>
          <[differences_num = :differences_num,]>
          <[price = :price,]>
          <[status = :status,]>
          <[remark = :remark,]>
          <[created_by = :created_by,]>
          <[created_date = :created_date,]>
          <[last_update_date = :last_update_date,]>
          <[last_updated_by = :last_updated_by,]>
        id = id
        WHERE 1 = 1
        AND (status IS NULL OR status = 1)
        AND id = :id
      ]]>
    </content>
  </sql>
</sqlmap>