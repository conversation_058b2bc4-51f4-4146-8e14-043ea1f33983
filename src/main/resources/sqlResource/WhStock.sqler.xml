<?xml version="1.0" encoding="UTF-8" ?>
<sqlmap >
  <sql datasource="dataSource" id="queryWhStockCount" >
    <content >
      <![CDATA[
        SELECT COUNT(1)
        FROM wh_stock T1
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND sku = :sku]>
        <[AND sku IN (:skus)]>
        <[AND location_number = :location_number]>
        <[AND location_number IN (:location_no_list)]>
        <[AND quantity = :quantity]>
        <[AND qc_quantity = :qc_quantity]>
        <[AND waiting_up_quantity = :waiting_up_quantity]>
        <[AND up_quantity = :up_quantity]>
        <[AND surplus_quantity = :surplus_quantity]>
        <[AND allot_quantity = :allot_quantity]>
        <[AND pick_quantity = :pick_quantity]>
        <[AND pick_return_quantity = :pick_return_quantity]>
        <[AND pick_not_quantity = :pick_not_quantity]>
        <[AND cancel_quantity = :cancel_quantity]>
        <[AND allocation_quantity = :allocation_quantity]>
        <[AND order_allocation_quantity = :order_allocation_quantity]>
        <[AND deliver_quantity = :deliver_quantity]>
        <[AND warehouse_id = :warehouse_id]>
        <[AND remark = :remark]>
        <[AND creation_date = :creation_date]>
        <[AND last_update_date = :last_update_date]>
        <[AND last_updated_by = :last_updated_by]>
        <[AND last_surplus_date = :last_surplus_date]>
        <[AND surplus_quantity >= :thenSurplusQuantity]>
	    <[AND surplus_quantity <= :lessSurplusQuantity]>
	    <[AND id IN (:id_list)]>
	    <[AND archive_flag = :archive_flag]>
	    <[:VIRTUAL_SHIPPERS_SQL]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryWhStockList" >
    <content >
      <![CDATA[
        SELECT T1.id,T1.sku,T1.location_number,T1.location_tag,T1.quantity,T1.qc_quantity,T1.waiting_up_quantity,T1.up_quantity,T1.surplus_quantity,T1.allocation_on_way_quantity,
        T1.allot_quantity,T1.pick_quantity,T1.pick_return_quantity,T1.pick_not_quantity,T1.cancel_quantity,T1.allocation_quantity,T1.order_allocation_quantity,
        T1.deliver_quantity,T1.warehouse_id,T1.remark,T1.creation_date,T1.last_update_date,T1.last_updated_by,T1.last_surplus_date,T1.return_quantity,
        IFNULL(frozen.frozen_quantity, 0) AS frozen_quantity
		,IFNULL(frozen.quantity, 0) AS batch_return_quantity
		,IFNULL(frozen.lend_onway_quantity, 0) AS lend_onway_quantity
		,IFNULL(frozen.lend_quantity, 0) AS lend_quantity
		,IFNULL(frozen.scrap_quantity, 0) AS scrap_quantity
		,IFNULL(frozen.bad_product_quantity, 0) AS bad_product_quantity
		<[:QUERY_LAST_UP_TIME]>
		<[:QUERY_LOCATION_TYPE]>
        FROM
            wh_stock T1
            LEFT JOIN frozen_stock frozen ON frozen.stock_id = T1.id
        WHERE 1 = 1
        <[AND T1.id = :id]>
        <[AND T1.sku = :sku]>
        <[AND T1.sku IN (:skus)]>
        <[AND T1.location_number = :location_number]>
        <[AND T1.location_number IN (:location_no_list)]>
        <[AND T1.quantity = :quantity]>
        <[AND T1.qc_quantity = :qc_quantity]>
        <[AND T1.waiting_up_quantity = :waiting_up_quantity]>
        <[AND T1.up_quantity = :up_quantity]>
        <[AND T1.surplus_quantity = :surplus_quantity]>
        <[AND T1.allot_quantity = :allot_quantity]>
        <[AND T1.pick_quantity = :pick_quantity]>
        <[AND T1.pick_return_quantity = :pick_return_quantity]>
        <[AND T1.pick_not_quantity = :pick_not_quantity]>
        <[AND T1.cancel_quantity = :cancel_quantity]>
        <[AND T1.allocation_quantity = :allocation_quantity]>
        <[AND T1.order_allocation_quantity = :order_allocation_quantity]>
        <[AND T1.deliver_quantity = :deliver_quantity]>
        <[AND T1.warehouse_id = :warehouse_id]>
        <[AND T1.remark = :remark]>
        <[AND T1.creation_date = :creation_date]>
        <[AND T1.last_update_date = :last_update_date]>
        <[AND T1.last_updated_by = :last_updated_by]>
        <[AND T1.last_surplus_date = :last_surplus_date]>
        <[AND T1.surplus_quantity >= :thenSurplusQuantity]>
	    <[AND T1.surplus_quantity <= :lessSurplusQuantity]>
	    <[AND T1.id IN (:id_list)]>
	    <[AND T1.archive_flag = :archive_flag]>
	    <[:VIRTUAL_SHIPPERS_SQL]>
      ]]>
    </content>
  </sql>


  <sql datasource="dataSource" id="queryPageStockCount" >
    <content >
      <![CDATA[
        SELECT
        	COUNT(1)
        FROM
        	wh_stock T1
        	<[:wh_sku]>
        	<[:wh_combine_sku]>
        	<[:SKU_MORE_LOCATION_CONDITION_SQL]>
            LEFT JOIN frozen_stock frozen ON frozen.stock_id = T1.id
        	<[:LOCATION_COUNT_JOIN_SQL]>
        WHERE
        	1 = 1
	        <[:skus_condition]>
            <[AND T1.location_number  IN (:location_no_list)]>
            <[AND T1.location_number = :location_number]>
	        <[:area_condition]>
	        <[:access_condition]>
	        <[:sku_status]>
	        <[:combine_sku_status]>
	        <[AND T1.sku IN (SELECT T2.sku FROM wh_sku T2 WHERE T2.name LIKE :sku_name_condition UNION ALL SELECT T4.spu FROM wh_combine_sku T4 WHERE T4.name LIKE :sku_name_condition)]>

	        <[AND T1.sku IN (SELECT T2.sku FROM wh_sku T2 WHERE T2.no_stock_up = :no_stock_up)]>
	        <[ :no_stock_up_not_true_count]>

	        <[AND (T1.qc_quantity+T1.waiting_up_quantity+T1.up_quantity+T1.allot_quantity+T1.pick_quantity+T1.pick_not_quantity+T1.cancel_quantity+T1.surplus_quantity) >= :thenQuantity]>
	        <[AND (T1.qc_quantity+T1.waiting_up_quantity+T1.up_quantity+T1.allot_quantity+T1.pick_quantity+T1.pick_not_quantity+T1.cancel_quantity+T1.surplus_quantity) <= :lessQuantity]>

	        <[AND T1.surplus_quantity >= :thenSurplusQuantity]>
	        <[AND T1.surplus_quantity <= :lessSurplusQuantity]>

	        <[AND T1.pick_quantity >= :thenPickQuantity]>
	        <[AND T1.pick_quantity <= :lessPickQuantity]>

	        <[AND (SELECT SUM(IF(purchase_item.on_way_quantity=NULL, 0, purchase_item.on_way_quantity)) FROM wh_purchase_item purchase_item WHERE purchase_item.sku=T1.sku AND purchase_item.status NOT IN ('All_Stock_In','Abandon')) >= :thenOnPassageQuantity]>
	        <[AND (SELECT SUM(IF(purchase_item.on_way_quantity=NULL, 0, purchase_item.on_way_quantity)) FROM wh_purchase_item purchase_item WHERE purchase_item.sku=T1.sku AND purchase_item.status NOT IN ('All_Stock_In','Abandon')) <= :lessOnPassageQuantity]>

	        <[:filter_zero_record]>

	        <[AND last_surplus_date >= :last_surplus_date]>
	        <[AND last_surplus_date <= :last_surplus_date]>

	        <[AND T1.id IN (:id_list)]>
	        <[AND T1.sku IN (:skus)]>
	        <[AND T1.archive_flag = :archive_flag]>
	        <[AND frozen.last_up_time >= :from_up_time]>
            <[AND frozen.last_up_time <= :to_up_time]>
            <[AND ( CASE WHEN frozen.allocation_out_time > frozen.allocation_in_time THEN frozen.allocation_out_time ELSE frozen.allocation_in_time END ) >= :from_out_last_time]>
            <[AND ( CASE WHEN frozen.allocation_out_time > frozen.allocation_in_time THEN frozen.allocation_out_time ELSE frozen.allocation_in_time END ) <= :to_out_last_time]>
            <[AND frozen.last_move_time >= :from_move_time]>
            <[AND frozen.last_move_time <= :to_move_time]>
            <[AND frozen.check_in_up_time >= :from_check_in_up_time]>
            <[AND frozen.check_in_up_time <= :to_check_in_up_time]>
            <[AND frozen.min_exp_date >= :from_min_exp_date]>
            <[AND frozen.min_exp_date <= :to_min_exp_date]>
            <[:VALUABLE_PRODUCTS_FILTER]>
            <[:TOTAL_SURPLUS_QUANTITY]>
            <[:ON_WAY_QTY_COUNT_SQL]>
            <[:SKU_MORE_LOCATION_SQL]>
            <[:LOCATION_TAG_SQL]>
            <[:VIRTUAL_SHIPPERS_SQL]>

      ]]>
    </content>
  </sql>

  <sql datasource="dataSource" id="queryPageStocks" >
    <content >
      <![CDATA[
	    SELECT
            T1.id,
            T1.sku,
            T1.location_number,
            T1.location_tag,
            T1.quantity,
            T1.qc_quantity,
            T1.waiting_up_quantity,
            T1.up_quantity,
            T1.surplus_quantity,
            T1.allot_quantity,
            T1.pick_quantity,
            T1.pick_return_quantity,
            T1.return_quantity,
            T1.pick_not_quantity,
            T1.cancel_quantity,
            T1.allocation_quantity,
            T1.allocation_on_way_quantity,
            T1.order_allocation_quantity,
            T1.deliver_quantity,
            T1.warehouse_id,
            T1.remark,
            T1.creation_date,
            T1.last_update_date,
            T1.last_updated_by,
            T1.last_surplus_date,
			CASE WHEN INSTR(T1.location_tag,"4") THEN T4.spu ELSE T2.sku END AS 'whSku.sku',
            CASE WHEN INSTR(T1.location_tag,"4") THEN T4.name ELSE T2.name END AS 'whSku.name',
            CASE WHEN INSTR(T1.location_tag,"4") THEN T4.status ELSE T2.status END AS 'whSku.status',
			T3.thirty_days_sales_orders AS 'whSku.thirtyDaysSalesOrders',
			IFNULL(T3.thirty_days_sales_days,0) AS 'whSku.thirtyDaysSalesDays',
			T3.sale_attribute_setting_str AS 'sale_attribute_setting_str1',
			IFNULL(frozen.frozen_quantity, 0) AS frozen_quantity
			,IFNULL(frozen.quantity, 0) AS batch_return_quantity
			,IFNULL(frozen.lend_onway_quantity, 0) AS lend_onway_quantity
			,IFNULL(frozen.lend_quantity, 0) AS lend_quantity
			,IFNULL(frozen.scrap_quantity, 0) AS scrap_quantity
			,IFNULL(frozen.bad_product_quantity, 0) AS bad_product_quantity
			,frozen.last_up_time
			,frozen.last_up_user
			,frozen.check_in_up_time
			,frozen.check_in_up_user
			,frozen.last_move_time
			,frozen.last_move_user
			,frozen.min_exp_date
			,( CASE WHEN frozen.allocation_out_time > frozen.allocation_in_time THEN frozen.allocation_out_time ELSE frozen.allocation_in_time END ) AS allocationTime
			<[:on_way_quantity]>
			<[:transit_location_quantity]>
			<[:pac_location_quantity]>
            <[:lend_allot_quantity]>
            <[:scrap_allot_quantity]>
        FROM
        	wh_stock T1 LEFT JOIN wh_sku T2 ON T1.sku=T2.sku
        	LEFT JOIN wh_combine_sku T4 ON T1.sku = T4.spu
        	LEFT JOIN wh_sku_sale_statistic_record T3 ON T1.sku = T3.sku
        	LEFT JOIN frozen_stock frozen ON frozen.stock_id = T1.id
        	<[:SKU_MORE_LOCATION_CONDITION_SQL]>
        	<[:LOCATION_COUNT_JOIN_SQL]>
        WHERE
	        1 = 1

	        <[:skus_condition]>
	        <[AND T1.location_number IN (:location_no_list)]>
            <[AND T1.location_number = :location_number]>
	        <[AND T2.no_stock_up = :no_stock_up]>
	        <[ :no_stock_up_not_true]>

	        <[:area_condition]>
	        <[:access_condition]>
	        <[:sku_status]>
	        <[:combine_sku_status]>
	        <[AND (T2.name LIKE :sku_name_condition OR T4.name LIKE :sku_name_condition)]>

	        <[AND (T1.qc_quantity+T1.waiting_up_quantity+T1.up_quantity+T1.allot_quantity+T1.pick_quantity+T1.pick_not_quantity+T1.cancel_quantity+T1.surplus_quantity) >= :thenQuantity]>
	        <[AND (T1.qc_quantity+T1.waiting_up_quantity+T1.up_quantity+T1.allot_quantity+T1.pick_quantity+T1.pick_not_quantity+T1.cancel_quantity+T1.surplus_quantity) <= :lessQuantity]>

	        <[AND T1.surplus_quantity >= :thenSurplusQuantity]>
	        <[AND T1.surplus_quantity <= :lessSurplusQuantity]>

	        <[AND T1.pick_quantity >= :thenPickQuantity]>
	        <[AND T1.pick_quantity <= :lessPickQuantity]>

	        <[AND (SELECT SUM(IF(purchase_item.on_way_quantity=NULL, 0, purchase_item.on_way_quantity)) FROM wh_purchase_item purchase_item WHERE purchase_item.sku=T1.sku AND purchase_item.status NOT IN ('All_Stock_In','Abandon')) >= :thenOnPassageQuantity]>
	        <[AND (SELECT SUM(IF(purchase_item.on_way_quantity=NULL, 0, purchase_item.on_way_quantity)) FROM wh_purchase_item purchase_item WHERE purchase_item.sku=T1.sku AND purchase_item.status NOT IN ('All_Stock_In','Abandon')) <= :lessOnPassageQuantity]>

	        <[:filter_zero_record]>

	        <[AND T1.id IN (:id_list)]>
	        <[AND T1.sku IN (:skus)]>
	        <[AND T1.sku = :sku]>
	        <[AND T1.archive_flag = :archive_flag]>
	        <[AND frozen.last_up_time >= :from_up_time]>
            <[AND frozen.last_up_time <= :to_up_time]>
            <[AND frozen.last_move_time >= :from_move_time]>
            <[AND frozen.last_move_time <= :to_move_time]>
            <[AND ( CASE WHEN frozen.allocation_out_time > frozen.allocation_in_time THEN frozen.allocation_out_time ELSE frozen.allocation_in_time END ) >= :from_out_last_time]>
            <[AND ( CASE WHEN frozen.allocation_out_time > frozen.allocation_in_time THEN frozen.allocation_out_time ELSE frozen.allocation_in_time END ) <= :to_out_last_time]>
            <[AND frozen.check_in_up_time >= :from_check_in_up_time]>
            <[AND frozen.check_in_up_time <= :to_check_in_up_time]>
            <[AND frozen.min_exp_date >= :from_min_exp_date]>
            <[AND frozen.min_exp_date <= :to_min_exp_date]>
            <[:VALUABLE_PRODUCTS_FILTER]>
            <[:TOTAL_SURPLUS_QUANTITY]>
            <[:ON_WAY_QTY_DETAIL_SQL]>
            <[:SKU_MORE_LOCATION_SQL]>
            <[:LOCATION_TAG_SQL]>
            <[:VIRTUAL_SHIPPERS_SQL]>
      ]]>
    </content>
  </sql>

  <sql datasource="dataSource" id="queryWhStockByPrimaryKey" >
    <content >
      <![CDATA[
        SELECT T1.id, T1.sku,T1.location_number,T1.location_tag, T1.quantity, T1.qc_quantity, T1.waiting_up_quantity, T1.up_quantity, T1.surplus_quantity,
        T1.allot_quantity, T1.pick_quantity, T1.pick_return_quantity, T1.pick_not_quantity, T1.cancel_quantity,T1.allocation_on_way_quantity,
        T1.allocation_quantity, T1.order_allocation_quantity, T1.deliver_quantity, T1.warehouse_id, T1.remark,
        T1.creation_date, T1.last_update_date, T1.last_updated_by, T1.last_surplus_date,T1.return_quantity,
        IFNULL(frozen.frozen_quantity, 0) AS frozen_quantity
		,IFNULL(frozen.quantity, 0) AS batch_return_quantity
		,IFNULL(frozen.lend_onway_quantity, 0) AS lend_onway_quantity
		,IFNULL(frozen.lend_quantity, 0) AS lend_quantity
		,IFNULL(frozen.scrap_quantity, 0) AS scrap_quantity
		,IFNULL(frozen.bad_product_quantity, 0) AS bad_product_quantity
        FROM wh_stock T1
        LEFT JOIN frozen_stock frozen ON frozen.stock_id = T1.id
        WHERE 1 = 1
        AND T1.id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryWhStock" >
    <content >
      <![CDATA[
        SELECT T1.id, T1.sku,T1.location_number,T1.location_tag, T1.quantity, T1.qc_quantity, T1.waiting_up_quantity, T1.up_quantity, T1.surplus_quantity,
        T1.allot_quantity, T1.pick_quantity, T1.pick_return_quantity, T1.pick_not_quantity, T1.cancel_quantity,T1.allocation_on_way_quantity,
        T1.allocation_quantity, T1.order_allocation_quantity, T1.deliver_quantity, T1.warehouse_id, T1.remark,
        T1.creation_date, T1.last_update_date, T1.last_updated_by, T1.last_surplus_date,T1.return_quantity,
        IFNULL(frozen.frozen_quantity, 0) AS frozen_quantity
		,IFNULL(frozen.quantity, 0) AS batch_return_quantity
		,IFNULL(frozen.lend_onway_quantity, 0) AS lend_onway_quantity
		,IFNULL(frozen.lend_quantity, 0) AS lend_quantity
		,IFNULL(frozen.scrap_quantity, 0) AS scrap_quantity
		,IFNULL(frozen.bad_product_quantity, 0) AS bad_product_quantity
        FROM wh_stock T1
        LEFT JOIN frozen_stock frozen ON frozen.stock_id = T1.id
        WHERE 1 = 1
        <[AND T1.id = :id]>
        <[AND T1.sku = :sku]>
        <[AND T1.location_number IN (:location_no_list)]>
        <[AND T1.location_number = :location_number]>
        <[AND T1.quantity = :quantity]>
        <[AND T1.qc_quantity = :qc_quantity]>
        <[AND T1.waiting_up_quantity = :waiting_up_quantity]>
        <[AND T1.up_quantity = :up_quantity]>
        <[AND T1.surplus_quantity = :surplus_quantity]>
        <[AND T1.allot_quantity = :allot_quantity]>
        <[AND T1.pick_quantity = :pick_quantity]>
        <[AND T1.pick_return_quantity = :pick_return_quantity]>
        <[AND T1.pick_not_quantity = :pick_not_quantity]>
        <[AND T1.cancel_quantity = :cancel_quantity]>
        <[AND T1.allocation_quantity = :allocation_quantity]>
        <[AND T1.order_allocation_quantity = :order_allocation_quantity]>
        <[AND T1.deliver_quantity = :deliver_quantity]>
        <[AND T1.warehouse_id = :warehouse_id]>
        <[AND T1.remark = :remark]>
        <[AND T1.creation_date = :creation_date]>
        <[AND T1.last_update_date = :last_update_date]>
        <[AND T1.last_updated_by = :last_updated_by]>
        <[AND T1.last_surplus_date = :last_surplus_date]>
        <[AND T1.archive_flag = :archive_flag]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="createWhStock" >
    <content >
      <![CDATA[
        INSERT INTO wh_stock (sku,location_number,location_tag, quantity, qc_quantity, waiting_up_quantity, up_quantity, surplus_quantity,
          allot_quantity, pick_quantity, pick_return_quantity, pick_not_quantity, cancel_quantity, 
          allocation_quantity,allocation_on_way_quantity, order_allocation_quantity, deliver_quantity, warehouse_id, return_quantity,
          remark, creation_date, last_update_date, last_updated_by, last_surplus_date, archive_flag
          )
        VALUES (:sku,:location_number,:location_tag, :quantity, :qc_quantity, :waiting_up_quantity, :up_quantity, :surplus_quantity,
          :allot_quantity, :pick_quantity, :pick_return_quantity, :pick_not_quantity, :cancel_quantity, 
          :allocation_quantity, :allocation_on_way_quantity, :order_allocation_quantity, :deliver_quantity, :warehouse_id, :return_quantity,
          :remark, :creation_date, :last_update_date, :last_updated_by, :last_surplus_date, :archive_flag
          )
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="updateWhStockByPrimaryKey" >
    <content >
      <![CDATA[
        UPDATE wh_stock
        SET <[sku = :sku,]>
          <[location_number = :location_number,]>
          <[location_tag = :location_tag,]>
          <[quantity = :quantity,]>
          <[qc_quantity = :qc_quantity,]>
          <[waiting_up_quantity = :waiting_up_quantity,]>
          <[up_quantity = :up_quantity,]>
          <[surplus_quantity = :surplus_quantity,]>
          <[allot_quantity = :allot_quantity,]>
          <[pick_quantity = :pick_quantity,]>
          <[pick_return_quantity = :pick_return_quantity,]>
          <[return_quantity = :return_quantity,]>
          <[pick_not_quantity = :pick_not_quantity,]>
          <[cancel_quantity = :cancel_quantity,]>
          <[allocation_quantity = :allocation_quantity,]>
          <[allocation_on_way_quantity = :allocation_on_way_quantity,]>
          <[order_allocation_quantity = :order_allocation_quantity,]>
          <[deliver_quantity = :deliver_quantity,]>
          <[warehouse_id = :warehouse_id,]>
          <[remark = :remark,]>
          <[creation_date = :creation_date,]>
          <[last_update_date = :last_update_date,]>
          <[last_updated_by = :last_updated_by,]>
          <[last_surplus_date = :last_surplus_date,]>
          <[archive_flag = :archive_flag,]>
        id = id
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  <sql datasource="dataSource" id="queryStockQuantityBySkuAndType" >
    <content >
      <![CDATA[
        SELECT id,sku,location_number,location_tag
        ,(SELECT `name` FROM wh_sku WHERE sku = wh_stock.sku) AS skuName
        <[:QUANTITY_SQL]>
        FROM wh_stock
        WHERE 1 = 1
        <[AND sku IN (:skus)]>
        <[AND id IN (:stockIds)]>
      ]]>
    </content>
  </sql>

  <sql datasource="dataSource" id="countWhStockGroup" >
    <content >
      <![CDATA[
        SELECT
			SUM(surplus_quantity) AS 'groupSurplusQuantity',
			SUM(allot_quantity) AS 'groupAllotQuantity',
			SUM(frozen_quantity) AS 'groupFrozenQuantity',
			SUM(batch_return_quantity) AS 'groupBatchReturnQuantity',

			SUM(scrap_quantity) AS 'groupScrapQuantity',
			SUM(lend_onway_quantity) AS 'groupLendOnwayQuantity',
			SUM(lend_quantity) AS 'groupLendQuantity',
			SUM(bad_product_quantity) AS 'groupBadProductQuantity',

			SUM(pick_quantity) AS 'groupPickQuantity',
			SUM(allocation_quantity) AS 'groupAllocationQuantity',
			SUM(allocation_on_way_quantity) AS 'groupAllocationOnWayQuantity',
			SUM(order_allocation_quantity) AS 'groupOrderAllocationQuantity',
			SUM(qc_quantity) AS 'groupWaitQcQuantity',
			SUM(waiting_up_quantity) AS 'groupWaitUpQuantity',
			SUM(up_quantity) AS 'groupUpingQuantity',
			SUM(cancel_quantity) AS 'groupCancelQuantity',
			SUM(pick_not_quantity) AS 'groupPickNotQuantity',
			SUM(pick_return_quantity) AS 'groupPickReturnQuantity',
			SUM(return_quantity) AS 'groupReturnQuantity',
			SUM(deliver_quantity) AS 'groupDeliverQuantity',
			SUM(onWayQuantity) AS 'groupOnWayQuantity',
			SUM(transitLocationQuantity) AS 'groupTransitLocationQuantity',
			SUM(lendAllotQuantity) AS 'groupLendAllotQuantity'
		FROM
			(
				SELECT
					T1.sku,
					T1.surplus_quantity,
					T1.allocation_quantity,
					T1.allocation_on_way_quantity,
					T1.order_allocation_quantity,
					T1.allot_quantity,
					fr.frozen_quantity,
					fr.quantity as batch_return_quantity,
					fr.scrap_quantity,
					fr.lend_onway_quantity,
					fr.lend_quantity,
					fr.bad_product_quantity,
					T1.pick_quantity,
					T1.qc_quantity,
					T1.waiting_up_quantity,
					T1.up_quantity,
					T1.pick_not_quantity,
					T1.pick_return_quantity,
					T1.return_quantity,
					T1.deliver_quantity,
					T1.cancel_quantity
					<[:on_way_quantity]>
					<[:transit_location_quantity]>
					<[:pac_location_quantity]>
					<[:lend_allot_quantity]>
				FROM wh_stock T1
					<[:wh_sku]>
					LEFT JOIN frozen_stock fr ON fr.stock_id = T1.id
					<[:SKU_MORE_LOCATION_CONDITION_SQL]>
				WHERE
					1 = 1

			        <[:skus_condition]>
			        <[AND T1.location_number IN (:location_no_list)]>
			        <[AND T1.location_number = :location_number]>
			        <[:area_condition]>
			        <[:access_condition]>
			        <[AND T1.sku IN (SELECT T2.sku FROM wh_sku T2 WHERE T2.name LIKE :sku_name_condition UNION ALL SELECT T4.spu FROM wh_combine_sku T4 WHERE T4.name LIKE :sku_name_condition)]>

			        <[AND (T1.qc_quantity+T1.waiting_up_quantity+T1.up_quantity+T1.allot_quantity+T1.pick_quantity+T1.pick_not_quantity+T1.cancel_quantity+T1.surplus_quantity+T1.pick_return_quantity) >= :thenQuantity]>
	                <[AND (T1.qc_quantity+T1.waiting_up_quantity+T1.up_quantity+T1.allot_quantity+T1.pick_quantity+T1.pick_not_quantity+T1.cancel_quantity+T1.surplus_quantity+T1.pick_return_quantity) <= :lessQuantity]>

			        <[AND T1.surplus_quantity >= :thenSurplusQuantity]>
			        <[AND T1.surplus_quantity <= :lessSurplusQuantity]>

			        <[AND T1.pick_quantity >= :thenPickQuantity]>
			        <[AND T1.pick_quantity <= :lessPickQuantity]>

			        <[AND (SELECT SUM(IF(purchase_item.on_way_quantity=NULL, 0, purchase_item.on_way_quantity)) FROM wh_purchase_item purchase_item WHERE purchase_item.sku=T1.sku AND purchase_item.status NOT IN ('All_Stock_In','Abandon')) >= :thenOnPassageQuantity]>
	        		<[AND (SELECT SUM(IF(purchase_item.on_way_quantity=NULL, 0, purchase_item.on_way_quantity)) FROM wh_purchase_item purchase_item WHERE purchase_item.sku=T1.sku AND purchase_item.status NOT IN ('All_Stock_In','Abandon')) >= :lessOnPassageQuantity]>

			        <[:filter_zero_record]>

			        <[AND T1.id IN (:id_list)]>
			        <[AND T1.sku IN (:skus)]>
			        <[AND T1.archive_flag = :archive_flag]>
			        <[AND frozen.last_up_time >= :from_up_time]>
                    <[AND frozen.last_up_time <= :to_up_time]>
                    <[AND frozen.last_move_time >= :from_move_time]>
                    <[AND frozen.last_move_time <= :to_move_time]>
                    <[AND frozen.check_in_up_time >= :from_check_in_up_time]>
                    <[AND frozen.check_in_up_time <= :to_check_in_up_time]>
                    <[:VALUABLE_PRODUCTS_FILTER]>
                    <[:ON_WAY_QTY_COUNT_SQL]>
                    <[:SKU_MORE_LOCATION_SQL]>
                    <[:LOCATION_TAG_SQL]>
                    <[:VIRTUAL_SHIPPERS_SQL]>
			) T
      ]]>
    </content>
  </sql>

  <sql datasource="dataSource" id="countWhStockGroupTotal" >
    <content >
      <![CDATA[
        SELECT
          SUM(surplus_quantity) AS 'groupSurplusQuantity',
          SUM(allot_quantity) AS 'groupAllotQuantity',
          SUM(pick_quantity) AS 'groupPickQuantity',
          SUM(allocation_quantity) AS 'groupAllocationQuantity'
		FROM wh_stock
      ]]>
    </content>
  </sql>

  <sql datasource="dataSource" id="querySyncPmsStock" >
    <content >
      <![CDATA[
        SELECT
        	whStock.id,
        	whStock.sku,
        	whStock.surplus_quantity,
	        whStock.last_surplus_date
        FROM
        	wh_stock whStock LEFT JOIN wh_sku whSku ON whStock.sku=whSku.sku
        WHERE
        	1 = 1
        	<[AND whStock.last_surplus_date > DATE_SUB(NOW(),INTERVAL :syn_time_hour HOUR)]>
        	<[AND whSku.warehouse_id = :warehouse_id]>
        	AND whSku.location_number IS NOT NULL
      ]]>
    </content>
  </sql>

  <sql datasource="dataSource" id="querySurplusQuantityAndAllotQuantityCount" >
    <content >
      <![CDATA[
        SELECT
			SUM(surplus_quantity) AS 'surplusQuantity',
			SUM(allot_quantity) AS 'allotQuantity',
			COUNT(DISTINCT sku,if(surplus_quantity > 0,TRUE,NULL)) AS 'haveAvailableInventorySkuQuantity'
		FROM
			(
				SELECT
                (T1.allot_quantity + T1.pick_quantity + T1.allocation_quantity + T1.order_allocation_quantity + T1.pick_return_quantity + T1.pick_not_quantity + T1.cancel_quantity + IFNULL(f.frozen_quantity,0)) AS allot_quantity,T1.surplus_quantity, T1.sku
				FROM (
					SELECT  SUM(allot_quantity) AS allot_quantity,SUM(pick_quantity) AS pick_quantity,SUM(allocation_quantity) AS allocation_quantity,
					        SUM(order_allocation_quantity) AS order_allocation_quantity,SUM(pick_return_quantity) AS pick_return_quantity,
					        SUM(pick_not_quantity) AS pick_not_quantity,SUM(cancel_quantity) AS cancel_quantity,SUM(surplus_quantity) AS surplus_quantity,
					        sku
					FROM wh_stock
					GROUP BY sku
				) T1
				LEFT JOIN (
					SELECT sku,SUM(frozen_quantity) AS frozen_quantity
					FROM frozen_stock
					GROUP BY sku
				) f ON  f.sku = T1.sku
				WHERE 1 = 1
			    <[AND T1.sku IN (SELECT T2.sku FROM wh_sku T2 WHERE T1.sku=T2.sku AND T2.warehouse_id = :warehouse_id)]>
			) T
      ]]>
    </content>
  </sql>


  <sql datasource="dataSource" id="queryTransferSkuAndStockCount" >
    <content >
      <![CDATA[
        SELECT
            SUM( surplus_quantity ) AS 'surplusQuantity',
            SUM( allot_quantity ) AS 'allotQuantity',
            COUNT(DISTINCT sku,IF( surplus_quantity > 0, TRUE, NULL )) AS 'haveAvailableInventorySkuQuantity'
        FROM
            (
            SELECT
                SUM(IFNULL( allot_quantity, 0 ) + IFNULL( pick_quantity, 0 ) + IFNULL( pick_return_quantity, 0 )) AS allot_quantity,
                SUM(IFNULL( surplus_quantity, 0 )) AS surplus_quantity,
                sku
            FROM
                wh_transfer_stock
            GROUP BY
            sku
            ) T1
      ]]>
    </content>
  </sql>

  <sql datasource="dataSource" id="deleteWhStockByPrimaryKey" >
    <content >
      <![CDATA[
        DELETE FROM wh_stock
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>

  <sql datasource="dataSource" id="queryPageStocksFullCount" >
    <content >
      <![CDATA[
      select count(*) from(
             select 1
               from (select T1.id as id, T1.sku as sku,
               CASE WHEN INSTR(T1.location_tag,"4") THEN T4.name ELSE T2.name END AS name,
               CASE WHEN INSTR(T1.location_tag,"4") THEN T4.status ELSE T2.status END AS status,
               T1.location_number FROM wh_stock T1 LEFT JOIN wh_sku T2 ON T1.sku=T2.sku
        	  LEFT JOIN wh_combine_sku T4 ON T1.sku = T4.spu)  sk
            <[:wh_stock]>
            <[:wh_transfer_stock]>
            where 1 = 1
            <[AND sk.id IN (:ids)]>
            <[AND sk.sku IN (:skus)]>
            <[:LOCATIONNOS_SQL]>
            <[:access_condition]>
            <[:area_condition]>
            <[:filter_zero_record]>
            <[:group_by]>
      ) counts
      ]]>
    </content>
  </sql>

  <sql datasource="dataSource" id="queryPageStocksFull" >
    <content >
      <![CDATA[
        select
          ws.id,
          ws.sku,
          ws.name,
          ws.status,
          ws.warehouse_id,
          IFNULL(sr.thirty_days_sales_orders,0) AS 'thirtyDaysSalesOrders',
          IFNULL(sr.thirty_days_sales_days,0) AS 'thirtyDaysSalesDays',
          ws.location_number,
          sr.sale_attribute_setting_str,
          (SELECT SUM(IFNULL(purchase_item.quantity, 0)) - SUM(IFNULL(purchase_item.up_quantity, 0)) - SUM(IFNULL(purchase_item.revoked_quantity, 0)) FROM wh_purchase_item purchase_item WHERE purchase_item.sku=stock.sku AND purchase_item.status NOT IN ('All_Stock_In','Abandon')) AS onWayQuantity, -- 在途
          sum(IFNULL(stock.quantity,0)) quantity, -- 仓库
          sum(IFNULL(stock.qc_quantity,0)) qc_quantity, -- 待QC
          sum(IFNULL(stock.waiting_up_quantity,0)) waiting_up_quantity, -- 待上架
          sum(IFNULL(stock.up_quantity,0)) up_quantity, -- 上架中
          sum(IFNULL(stock.surplus_quantity,0)) surplus_quantity, -- 可用
          sum(IFNULL(stock.allot_quantity,0)) allot_quantity, -- 已分配
          sum(IFNULL(stock.pick_quantity,0)) pick_quantity, -- 已捡
          sum(IFNULL(stock.pick_not_quantity,0)) pick_not_quantity, -- 已捡缺货
          sum(IFNULL(stock.pick_return_quantity,0)) pick_return_quantity, -- 已捡返架
          sum(IFNULL(stock.cancel_quantity,0)) cancel_quantity, -- 取消
          sum(IFNULL(stock.order_allocation_quantity,0)) order_allocation_quantity, -- 订单调拨
          sum(IFNULL(stock.allocation_quantity,0)) allocation_quantity, -- 库存调拨
          sum(IFNULL(frozen.frozen_quantity, 0)) AS frozen_quantity, -- 冻结库存
          sum(IFNULL(frozen.quantity, 0)) AS batch_return_quantity, -- 退货在途
          sum(IFNULL(frozen.lend_onway_quantity, 0)) AS lend_onway_quantity -- 外借在途
        from (
              select sk.id,sk.sku,sk.name,sk.status,sk.warehouse_id,sk.location_number
               from (select  T1.id as id, T1.sku as sku,
               CASE WHEN INSTR(T1.location_tag,"4") THEN T4.name ELSE T2.name END AS name,
               CASE WHEN INSTR(T1.location_tag,"4") THEN T4.status ELSE T2.status END AS status,1 as warehouse_id,
               T1.location_number FROM wh_stock T1 LEFT JOIN wh_sku T2 ON T1.sku=T2.sku
        	  LEFT JOIN wh_combine_sku T4 ON T1.sku = T4.spu) sk
                <[:wh_stock]>
                <[:wh_transfer_stock]>
                where 1 = 1
                <[AND sk.id IN (:ids)]>
                <[AND sk.sku IN (:skus)]>
                <[:LOCATIONNOS_SQL]>
                <[:access_condition]>
                <[:area_condition]>
                <[:filter_zero_record]>
                GROUP BY sk.id
                <[:LIMIT]>
            ) ws
        left join wh_stock stock on ws.id = stock.id
        left join wh_sku_sale_statistic_record sr on ws.sku = sr.sku
        LEFT JOIN frozen_stock frozen ON frozen.stock_id = stock.id
        WHERE 1 = 1
        GROUP BY ws.sku
      ]]>
    </content>
  </sql>
  <sql datasource="dataSource" id="queryPageTransferStocksFull" >
    <content >
      <![CDATA[
        select
          ws.sku,
          sum(IFNULL(s.surplus_quantity,0)) transfer_surplus_quantity,  -- 可用库存
          sum(IFNULL(s.allot_quantity,0)) transfer_allot_quantity, 	-- 已分配库存
          sum(IFNULL(s.pick_quantity,0)) transfer_pick_quantity, 	-- 已捡库存
          sum(IFNULL(s.pick_return_quantity,0)) transfer_pick_return_quantity	-- 已捡返架库存
        from (
               select sk.id,sk.sku,sk.name,sk.status,sk.warehouse_id,sk.location_number
               from (select  T1.id as id, T1.sku as sku,
               CASE WHEN INSTR(T1.location_tag,"4") THEN T4.name ELSE T2.name END AS name,
               CASE WHEN INSTR(T1.location_tag,"4") THEN T4.status ELSE T2.status END AS status,1 as warehouse_id,
               T1.location_number FROM wh_stock T1 LEFT JOIN wh_sku T2 ON T1.sku=T2.sku
        	  LEFT JOIN wh_combine_sku T4 ON T1.sku = T4.spu) sk
                <[:wh_stock]>
                <[:wh_transfer_stock]>
                where 1 = 1
                <[AND sk.id IN (:ids)]>
                <[AND sk.sku IN (:skus)]>
                <[:LOCATIONNOS_SQL]>
                <[:access_condition]>
                <[:area_condition]>
                <[:filter_zero_record]>
                GROUP BY sk.id
                <[:LIMIT]>
            ) ws
        left join wh_transfer_stock s on ws.id = s.stock_id
        WHERE 1 = 1
        GROUP BY ws.sku
      ]]>
    </content>
  </sql>

  <sql datasource="dataSource" id="queryStocksFullGroup" >
    <content >
      <![CDATA[
        select
          SUM(gr.onWayQuantity) AS onWayQuantity_group,
          SUM(gr.quantity) AS quantity_group,
          SUM(gr.qc_quantity) AS qc_quantity_group,
          SUM(gr.waiting_up_quantity) AS waiting_up_quantity_group,
          SUM(gr.up_quantity) AS up_quantity_group,
          SUM(gr.surplus_quantity) AS surplus_quantity_group,
          SUM(gr.allot_quantity) AS allot_quantity_group,
          SUM(gr.pick_quantity) AS pick_quantity_group,
          SUM(gr.pick_not_quantity) AS pick_not_quantity_group,
          SUM(gr.pick_return_quantity) AS pick_return_quantity_group,
          SUM(gr.cancel_quantity) AS cancel_quantity_group,
          SUM(gr.order_allocation_quantity) AS order_allocation_quantity_group,
          SUM(gr.allocation_quantity) AS allocation_quantity_group,
          SUM(gr.frozen_quantity) AS frozen_quantity_group,
          SUM(gr.batch_return_quantity) AS batch_return_quantity_group,
          SUM(gr.lend_onway_quantity) AS lend_onway_quantity_group
        from
          (select
            (SELECT SUM(IFNULL(purchase_item.quantity, 0)) - SUM(IFNULL(purchase_item.up_quantity, 0)) - SUM(IFNULL(purchase_item.revoked_quantity, 0)) FROM wh_purchase_item purchase_item WHERE purchase_item.sku=stock.sku AND purchase_item.status NOT IN ('All_Stock_In','Abandon')) AS onWayQuantity, -- 在途
            sum(IFNULL(stock.quantity,0)) quantity, -- 仓库
            sum(IFNULL(stock.qc_quantity,0)) qc_quantity, -- 待QC
            sum(IFNULL(stock.waiting_up_quantity,0)) waiting_up_quantity, -- 待上架
            sum(IFNULL(stock.up_quantity,0)) up_quantity, -- 上架中
            sum(IFNULL(stock.surplus_quantity,0)) surplus_quantity, -- 可用
            sum(IFNULL(stock.allot_quantity,0)) allot_quantity, -- 已分配
            sum(IFNULL(stock.pick_quantity,0)) pick_quantity, -- 已捡
            sum(IFNULL(stock.pick_not_quantity,0)) pick_not_quantity, -- 已捡缺货
            sum(IFNULL(stock.pick_return_quantity,0)) pick_return_quantity, -- 已捡返架
            sum(IFNULL(stock.cancel_quantity,0)) cancel_quantity, -- 取消
            sum(IFNULL(stock.order_allocation_quantity,0)) order_allocation_quantity, -- 订单调拨
            sum(IFNULL(stock.allocation_quantity,0)) allocation_quantity, -- 库存调拨
            sum(IFNULL(frozen.frozen_quantity, 0)) AS frozen_quantity, -- 冻结库存
            sum(IFNULL(frozen.quantity, 0)) AS batch_return_quantity, -- 退货在途
            sum(IFNULL(frozen.lend_onway_quantity, 0)) AS lend_onway_quantity -- 外借在途
          from (
              select sk.id,sk.sku,sk.name,sk.status,sk.warehouse_id,sk.location_number
               from (select  T1.id as id, T1.sku as sku,
               CASE WHEN INSTR(T1.location_tag,"4") THEN T4.name ELSE T2.name END AS name,
               CASE WHEN INSTR(T1.location_tag,"4") THEN T4.status ELSE T2.status END AS status,1 as warehouse_id,
               T1.location_number FROM wh_stock T1 LEFT JOIN wh_sku T2 ON T1.sku=T2.sku
        	  LEFT JOIN wh_combine_sku T4 ON T1.sku = T4.spu) sk
                  <[:wh_stock]>
                  <[:wh_transfer_stock]>
                  where 1 = 1
                  <[AND sk.id IN (:ids)]>
                  <[AND sk.sku IN (:skus)]>
                  <[:LOCATIONNOS_SQL]>
                  <[:access_condition]>
                  <[:area_condition]>
                  <[:filter_zero_record]>
                  GROUP BY sk.id
                  <[:LIMIT]>
              ) ws
          left join wh_stock stock on ws.sku = stock.sku
          LEFT JOIN frozen_stock frozen ON frozen.stock_id = stock.id
          WHERE 1 = 1
          GROUP BY ws.sku
        )gr
      ]]>
    </content>
  </sql>

  <sql datasource="dataSource" id="queryTransferStocksFullGroup" >
    <content >
      <![CDATA[
        select
          SUM(gr.transfer_surplus_quantity) AS transfer_surplus_quantity_group,
          SUM(gr.transfer_allot_quantity) AS transfer_allot_quantity_group,
          SUM(gr.transfer_pick_quantity) AS transfer_pick_quantity_group,
          SUM(gr.transfer_pick_return_quantity) AS transfer_pick_return_quantity_group
        from
          (select
            sum(IFNULL(s.surplus_quantity,0)) transfer_surplus_quantity,  -- 可用库存
            sum(IFNULL(s.allot_quantity,0)) transfer_allot_quantity, 	-- 已分配库存
            sum(IFNULL(s.pick_quantity,0)) transfer_pick_quantity, 	-- 已捡库存
            sum(IFNULL(s.pick_return_quantity,0)) transfer_pick_return_quantity	-- 已捡返架库存
            from (
                   select sk.id,sk.sku,sk.name,sk.status,sk.warehouse_id,sk.location_number
               from (select  T1.id as id, T1.sku as sku,
               CASE WHEN INSTR(T1.location_tag,"4") THEN T4.name ELSE T2.name END AS name,
               CASE WHEN INSTR(T1.location_tag,"4") THEN T4.status ELSE T2.status END AS status,1 as warehouse_id,
               T1.location_number FROM wh_stock T1 LEFT JOIN wh_sku T2 ON T1.sku=T2.sku
        	  LEFT JOIN wh_combine_sku T4 ON T1.sku = T4.spu) sk
                  <[:wh_stock]>
                  <[:wh_transfer_stock]>
                  where 1 = 1
                  <[AND sk.id IN (:ids)]>
                  <[AND sk.sku IN (:skus)]>
                  <[:LOCATIONNOS_SQL]>
                  <[:access_condition]>
                  <[:area_condition]>
                  <[:filter_zero_record]>
                  GROUP BY sk.id
                  <[:LIMIT]>
              ) ws
          left join wh_transfer_stock s on ws.sku = s.sku
          WHERE 1 = 1
          GROUP BY ws.sku
        )gr
      ]]>
    </content>
  </sql>

  <sql datasource="dataSource" id="listFullStockBySku" >
    <content >
      <![CDATA[
        select id,sku, location_number, sum(quantity) as totalLocationQuantity, sum(otherQuantity) as totalOtherQuantity
        from (
            select id,sku, location_number, (ifnull(surplus_quantity, 0) + ifnull(allot_quantity, 0)) as quantity,
                  (ifnull(cancel_quantity, 0) + ifnull(pick_not_quantity, 0) + ifnull(pick_return_quantity, 0)) as otherQuantity
            from wh_stock
            where sku in (:skus) <[AND location_number = (:locationNumber)]> AND archive_flag = 0
            union all
            select stock_id as id,sku, location_number, (sum(ifnull(surplus_quantity, 0)) + sum(ifnull(allot_quantity, 0))) as quantity,
                  (sum(ifnull(pick_return_quantity, 0))) as otherQuantity
            from wh_transfer_stock
            where sku in (:skus) <[AND location_number = (:locationNumber)]>
            AND stock_id in (select id from wh_stock where stock_id = id AND archive_flag = 0)
            group by sku, location_number
        ) stock
        group by sku, location_number
      ]]>
    </content>
  </sql>
  <sql datasource="dataSource" id="queryZeroStockList">
    <content>
      <![CDATA[
          SELECT
              T1.id,T1.sku,T1.location_number,T1.location_tag,l.location_type,
              ( SELECT vendor_name FROM after_sale_settlement WHERE stock_id = T1.id LIMIT 1) vendor_name,
              IFNULL( frozen.quantity, 0 ) AS batch_return_quantity,
              IFNULL( frozen.lend_onway_quantity, 0 ) AS lend_onway_quantity,
              IFNULL((
                  SELECT SUM(IFNULL(quantity, 0)) - SUM(IFNULL(up_quantity, 0)) - SUM(IFNULL(revoked_quantity, 0))
                  FROM wh_purchase_item
                  WHERE sku = T1.sku
                      AND status NOT IN ('All_Stock_In', 'Abandon')
              ),0) AS on_way_quantity,
							(
                IFNULL( T1.surplus_quantity, 0 ) + IFNULL( T1.allot_quantity, 0 ) + IFNULL( T1.pick_quantity, 0 ) + IFNULL( T1.pick_return_quantity, 0 )
                + IFNULL( T1.pick_not_quantity, 0 ) + IFNULL( T1.cancel_quantity, 0 ) + IFNULL( T1.allocation_quantity, 0 ) + IFNULL( T1.allocation_on_way_quantity, 0 )
                + IFNULL( T1.order_allocation_quantity, 0 ) + IFNULL( frozen.frozen_quantity, 0 )
              ) AS qty
              ,(SELECT IFNULL(SUM(IFNULL(allot_quantity,0)+IFNULL(surplus_quantity,0)+IFNULL(pick_quantity,0)+IFNULL(pick_return_quantity,0)),0) FROM wh_transfer_stock WHERE stock_id = T1.id) qty2

          FROM
              wh_stock T1
              LEFT JOIN frozen_stock frozen ON frozen.stock_id = T1.id
              LEFT JOIN wh_location l ON l.location = T1.location_number
          WHERE
              T1.location_number IS NOT NULL AND l.location_type = 1 AND T1.archive_flag = 0
              AND (frozen.last_up_time IS NULL OR TIMESTAMPDIFF(DAY, frozen.last_up_time, NOW()) > 7)
              AND (l.location_region NOT IN ('Y'))
              <[AND T1.sku IN (:skus)]>
          HAVING qty = 0 AND batch_return_quantity = 0 AND lend_onway_quantity = 0 AND on_way_quantity = 0 AND qty2 = 0
      ]]>
    </content>
  </sql>
  <sql datasource="dataSource" id="removeLocationTag" >
    <content >
      <![CDATA[
        UPDATE wh_stock SET location_tag = NULL
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  <sql datasource="dataSource" id="getLocationBySkuAndType">
    <content>
      <![CDATA[
          SELECT
             T1.sku as sku
          FROM wh_stock T1
              lEFT JOIN wh_location l ON l.location = T1.location_number
          WHERE
              T1.location_number IS NOT NULL AND T1.archive_flag = 0
              <[AND l.location_type = :type]>
              <[AND T1.sku IN (:skus)]>
              <[AND T1.id NOT IN (:notIds)]>
      ]]>
    </content>
  </sql>

  <sql datasource="dataSource" id="queryReturnSkuStockList">
    <content>
      <![CDATA[
          SELECT
             ws.sku AS sku,
             ws.location_number AS location_number,
             s.name AS sku_name,
             s.image_url AS img_url,
             ws.surplus_quantity AS 'surplus_quantity',
			 ws.allot_quantity AS 'allot_quantity',
			 ws.pick_quantity  AS 'pick_quantity',
			 ws.cancel_quantity AS 'cancel_quantity',
			 ws.pick_return_quantity AS 'pick_return_quantity',
			 ws.pick_not_quantity AS 'pick_not_quantity',
			 (SELECT SUM( wts.surplus_quantity) FROM wh_transfer_stock wts where wts.sku = ws.sku and  ws.id = wts.stock_id)  AS 'transfer_surplus_quantity',
			 (SELECT SUM( wts.allot_quantity) FROM wh_transfer_stock wts where wts.sku = ws.sku and  ws.id = wts.stock_id)  AS 'transfer_allot_quantity',
			 (SELECT SUM( wts.pick_quantity) FROM wh_transfer_stock wts where wts.sku = ws.sku and  ws.id = wts.stock_id)  AS 'transfer_pick_quantity',
			 (SELECT SUM( wts.pick_return_quantity) FROM wh_transfer_stock wts where wts.sku = ws.sku and  ws.id = wts.stock_id)  AS 'transfer_pick_return_quantity'
          FROM
              wh_stock ws
              LEFT JOIN wh_sku s ON ws.sku = s.sku
          WHERE 1=1
             <[AND ws.sku = :sku]>
             <[AND ws.sku IN (:skus)]>
      ]]>
    </content>
  </sql>

  <sql datasource="dataSource" id="existZfStockSkuList">
    <content>
      <![CDATA[
          SELECT sku FROM wh_stock WHERE SUBSTR(location_number,1,2) = 'ZF' AND (surplus_quantity > 0 OR allot_quantity > 0)
      ]]>
    </content>
  </sql>

  <sql datasource="dataSource" id="getOnWayStockList">
    <content>
      <![CDATA[
          SELECT
              s.sku,
              SUM(IFNULL(s.allocation_on_way_quantity,0)) AS allocationOnWayQty,
              SUM(IFNULL(f.quantity,0)) AS returnOnWayQty
          FROM
              wh_stock s
              LEFT JOIN frozen_stock f ON f.stock_id = s.id
          WHERE
              (s.allocation_on_way_quantity > 0 OR f.quantity > 0)
              <[AND s.sku IN (:skuList)]>
              GROUP BY s.sku
      ]]>
    </content>
  </sql>

  <sql datasource="dataSource" id="queryNotZeroStockList">
    <content>
      <![CDATA[
          SELECT
            T1.id,T1.sku,
            (
                IFNULL( T1.surplus_quantity, 0 ) + IFNULL( T1.allot_quantity, 0 ) + IFNULL( T1.pick_quantity, 0 ) + IFNULL( T1.pick_return_quantity, 0 )
                + IFNULL( T1.pick_not_quantity, 0 ) + IFNULL( T1.cancel_quantity, 0 ) + IFNULL( T1.allocation_quantity, 0 )
                + IFNULL( T1.order_allocation_quantity, 0 ) + IFNULL( frozen.frozen_quantity, 0 )
            ) AS quantity
            ,(SELECT IFNULL(SUM(IFNULL(allot_quantity,0)+IFNULL(surplus_quantity,0)+IFNULL(pick_quantity,0)+IFNULL(pick_return_quantity,0)),0) FROM wh_transfer_stock WHERE stock_id = T1.id) transferQty
            FROM
              wh_stock T1
              LEFT JOIN frozen_stock frozen ON frozen.stock_id = T1.id
          WHERE 1=1
          <[AND T1.sku IN (:skuList)]>
          HAVING quantity > 0 OR transferQty > 0
      ]]>
    </content>
  </sql>

</sqlmap>