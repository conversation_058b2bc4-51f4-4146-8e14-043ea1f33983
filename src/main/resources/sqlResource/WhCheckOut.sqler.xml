<?xml version="1.0" encoding="UTF-8" ?>
<sqlmap >
  <sql datasource="dataSource" id="queryWhCheckOutCount" >
    <content >
      <![CDATA[
        SELECT COUNT(1)
        FROM wh_check_out
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND type = :type]>
        <[AND remark = :remark]>
        <[AND creation_date = :creation_date]>
        <[AND created_by = :created_by]>
        <[AND last_update_date = :last_update_date]>
        <[AND last_update_by = :last_update_by]>
        <[AND warehouse_id = :warehouse_id]>
        <[AND status = :status]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryWhCheckOutList" >
    <content >
      <![CDATA[
        SELECT id, type, remark, creation_date, created_by, last_update_date, last_update_by, 
        warehouse_id, status
        FROM wh_check_out
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND type = :type]>
        <[AND remark = :remark]>
        <[AND creation_date = :creation_date]>
        <[AND created_by = :created_by]>
        <[AND last_update_date = :last_update_date]>
        <[AND last_update_by = :last_update_by]>
        <[AND warehouse_id = :warehouse_id]>
        <[AND status = :status]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryWhCheckOutByPrimaryKey" >
    <content >
      <![CDATA[
        SELECT id, type, remark, creation_date, created_by, last_update_date, last_update_by, 
        warehouse_id, status
        FROM wh_check_out
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryWhCheckOut" >
    <content >
      <![CDATA[
        SELECT id, type, remark, creation_date, created_by, last_update_date, last_update_by, 
        warehouse_id, status
        FROM wh_check_out
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND type = :type]>
        <[AND remark = :remark]>
        <[AND creation_date = :creation_date]>
        <[AND created_by = :created_by]>
        <[AND last_update_date = :last_update_date]>
        <[AND last_update_by = :last_update_by]>
        <[AND warehouse_id = :warehouse_id]>
        <[AND status = :status]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="createWhCheckOut" >
    <content >
      <![CDATA[
        INSERT INTO wh_check_out (type, remark, creation_date, created_by, last_update_date, last_update_by, 
          warehouse_id, status)
        VALUES (:type, :remark, :creation_date, :created_by, :last_update_date, :last_update_by, 
          :warehouse_id, :status)
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="deleteWhCheckOutByPrimaryKey" >
    <content >
      <![CDATA[
        DELETE FROM wh_check_out
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="updateWhCheckOutByPrimaryKey" >
    <content >
      <![CDATA[
        UPDATE wh_check_out
        SET <[type = :type,]>
          <[remark = :remark,]>
          <[creation_date = :creation_date,]>
          <[created_by = :created_by,]>
          <[last_update_date = :last_update_date,]>
          <[last_update_by = :last_update_by,]>
          <[warehouse_id = :warehouse_id,]>
          <[status = :status,]>
        id = id
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
</sqlmap>