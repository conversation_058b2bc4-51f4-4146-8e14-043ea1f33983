<?xml version="1.0" encoding="UTF-8" ?>
<sqlmap >
  <sql datasource="dataSource" id="queryTemuPickBoxItemCount" >
    <content >
      <![CDATA[
        SELECT COUNT(1)
        FROM temu_pick_box_item
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND box_id = :box_id]>
        <[AND relation_item_id = :relation_item_id]>
        <[AND pick_num = :pick_num]>
        <[AND last_update_time = :last_update_time]>
        <[AND pick_by = :pick_by,]>
        <[AND pick_time = :pick_time,]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryTemuPickBoxItemList" >
    <content >
      <![CDATA[
        SELECT id, box_id, relation_item_id, pick_num, last_update_time,pick_by,pick_time
        FROM temu_pick_box_item
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND box_id = :box_id]>
        <[AND relation_item_id = :relation_item_id]>
        <[AND pick_num = :pick_num]>
        <[AND last_update_time = :last_update_time]>
        <[AND pick_by = :pick_by,]>
        <[AND pick_time = :pick_time,]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryTemuPickBoxItemByPrimaryKey" >
    <content >
      <![CDATA[
        SELECT id, box_id, relation_item_id, pick_num, last_update_time,pick_by,pick_time
        FROM temu_pick_box_item
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryTemuPickBoxItem" >
    <content >
      <![CDATA[
        SELECT id, box_id, relation_item_id, pick_num, last_update_time,pick_by,pick_time
        FROM temu_pick_box_item
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND box_id = :box_id]>
        <[AND relation_item_id = :relation_item_id]>
        <[AND pick_num = :pick_num]>
        <[AND last_update_time = :last_update_time]>
        <[AND pick_by = :pick_by,]>
        <[AND pick_time = :pick_time,]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="createTemuPickBoxItem" >
    <content >
      <![CDATA[
        INSERT INTO temu_pick_box_item (box_id, relation_item_id, pick_num, last_update_time,pick_by,pick_time)
        VALUES (:box_id, :relation_item_id, :pick_num, :last_update_time,:pick_by,:pick_time)
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="deleteTemuPickBoxItemByPrimaryKey" >
    <content >
      <![CDATA[
        DELETE FROM temu_pick_box_item
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="updateTemuPickBoxItemByPrimaryKey" >
    <content >
      <![CDATA[
        UPDATE temu_pick_box_item
        SET <[box_id = :box_id,]>
          <[relation_item_id = :relation_item_id,]>
          <[pick_num = :pick_num,]>
          <[last_update_time = :last_update_time,]>
          <[pick_by = :pick_by,]>
          <[pick_time = :pick_time,]>
        id = id
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
</sqlmap>