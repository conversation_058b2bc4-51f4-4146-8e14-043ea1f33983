<?xml version="1.0" encoding="UTF-8" ?>
<sqlmap >

  <sql datasource="dataSource" id="queryBySkusForAli" >
    <content >
      <![CDATA[
        SELECT *
        FROM t_wh_sku
        where sku in (:skus)
      ]]>
    </content>
  </sql>

  <sql datasource="dataSource" id="updateSkuInfo" >
    <content >
      <![CDATA[
        UPDATE t_wh_sku
        SET
          <[name = :name,]>
          <[warehouse_id = :warehouse_id,]>
          <[weight = :weight,]>
          <[net_weight = :net_weight,]>
          <[contain = :contain,]>
          <[color = :color,]>
          <[specification = :specification,]>
          <[length = :length,]>
          <[width = :width,]>
          <[height = :height,]>
          <[last_update_date = :last_update_date,]>
          <[last_updated_by = :last_updated_by,]>
          <[status = :status,]>
          <[attr_json = :attr_json,]>
          sku = sku
        WHERE sku = :sku
      ]]>
    </content>
  </sql>

  <sql datasource="dataSource" id="updateSkuAttr" >
    <content >
      <![CDATA[
        UPDATE t_wh_sku
        SET attr_json = :attr_json
        WHERE sku = :sku
      ]]>
    </content>
  </sql>

  <sql datasource="dataSource" id="saveSkuInfo">
    <content >
      <![CDATA[
        INSERT INTO t_wh_sku (sku, name, warehouse_id, weight, net_weight, contain, color, specification, length, width, height,
                              creation_date, created_by, last_update_date, last_updated_by, status, attr_json)
        VALUES (:sku, :name, :warehouse_id, :weight, :net_weight, :contain, :color, :specification, :length, :width, :height,
                              :creation_date, :created_by, :last_update_date, :last_updated_by, :status, :attr_json)
      ]]>
    </content>
  </sql>

</sqlmap>