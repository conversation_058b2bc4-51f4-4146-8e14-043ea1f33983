<?xml version="1.0" encoding="UTF-8" ?>
<sqlmap >
  <sql datasource="dataSource" id="queryWhAsnPickingTaskCount" >
    <content >
      <![CDATA[
        SELECT COUNT(1)
        FROM wh_asn_picking_task
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND receiving_code = :receiving_code]>
        <[AND task_no = :task_no]>
        <[AND warehouse_code_deliver = :warehouse_code_deliver]>
        <[AND warehouse_code = :warehouse_code]>
        <[AND sku_total = :sku_total]>
        <[AND sku_species = :sku_species]>
        <[AND task_status = :task_status]>
        <[AND receive_person = :receive_person]>
        <[AND created_by = :created_by]>
        <[AND created_date = :created_date]>
        <[AND receive_date = :receive_date]>
        <[AND last_update_date = :last_update_date]>
        <[AND last_update_by = :last_update_by]>
        <[AND picking_end_date = :picking_end_date]>
        <[AND is_printing = :is_printing]>
        <[AND print_user = :print_user]>
        <[AND print_date = :print_date]>
        <[AND is_span = :is_span]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryWhAsnPickingTaskList" >
    <content >
      <![CDATA[
        SELECT id, receiving_code, task_no, warehouse_code_deliver, warehouse_code, sku_total, 
        sku_species, task_status, receive_person, created_by, created_date, receive_date, 
        last_update_date, last_update_by, picking_end_date, is_printing, print_user, print_date, 
        is_span
        FROM wh_asn_picking_task
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND receiving_code = :receiving_code]>
        <[AND task_no = :task_no]>
        <[AND warehouse_code_deliver = :warehouse_code_deliver]>
        <[AND warehouse_code = :warehouse_code]>
        <[AND sku_total = :sku_total]>
        <[AND sku_species = :sku_species]>
        <[AND task_status = :task_status]>
        <[AND receive_person = :receive_person]>
        <[AND created_by = :created_by]>
        <[AND created_date = :created_date]>
        <[AND receive_date = :receive_date]>
        <[AND last_update_date = :last_update_date]>
        <[AND last_update_by = :last_update_by]>
        <[AND picking_end_date = :picking_end_date]>
        <[AND is_printing = :is_printing]>
        <[AND print_user = :print_user]>
        <[AND print_date = :print_date]>
        <[AND is_span = :is_span]>
        <[AND id in (:ids)]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryWhAsnPickingTaskByPrimaryKey" >
    <content >
      <![CDATA[
        SELECT id, receiving_code, task_no, warehouse_code_deliver, warehouse_code, sku_total, 
        sku_species, task_status, receive_person, created_by, created_date, receive_date, 
        last_update_date, last_update_by, picking_end_date, is_printing, print_user, print_date, 
        is_span
        FROM wh_asn_picking_task
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryWhAsnPickingTask" >
    <content >
      <![CDATA[
        SELECT id, receiving_code, task_no, warehouse_code_deliver, warehouse_code, sku_total, 
        sku_species, task_status, receive_person, created_by, created_date, receive_date, 
        last_update_date, last_update_by, picking_end_date, is_printing, print_user, print_date, 
        is_span
        FROM wh_asn_picking_task
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND receiving_code = :receiving_code]>
        <[AND task_no = :task_no]>
        <[AND warehouse_code_deliver = :warehouse_code_deliver]>
        <[AND warehouse_code = :warehouse_code]>
        <[AND sku_total = :sku_total]>
        <[AND sku_species = :sku_species]>
        <[AND task_status = :task_status]>
        <[AND receive_person = :receive_person]>
        <[AND created_by = :created_by]>
        <[AND created_date = :created_date]>
        <[AND receive_date = :receive_date]>
        <[AND last_update_date = :last_update_date]>
        <[AND last_update_by = :last_update_by]>
        <[AND picking_end_date = :picking_end_date]>
        <[AND is_printing = :is_printing]>
        <[AND print_user = :print_user]>
        <[AND print_date = :print_date]>
        <[AND is_span = :is_span]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="createWhAsnPickingTask" >
    <content >
      <![CDATA[
        INSERT INTO wh_asn_picking_task (receiving_code, task_no, warehouse_code_deliver, warehouse_code, sku_total, 
          sku_species, task_status, receive_person, created_by, created_date, receive_date, 
          last_update_date, last_update_by, picking_end_date, is_printing, print_user, 
          print_date, is_span)
        VALUES (:receiving_code, :task_no, :warehouse_code_deliver, :warehouse_code, :sku_total, 
          :sku_species, :task_status, :receive_person, :created_by, :created_date, :receive_date, 
          :last_update_date, :last_update_by, :picking_end_date, :is_printing, :print_user, 
          :print_date, :is_span)
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="deleteWhAsnPickingTaskByPrimaryKey" >
    <content >
      <![CDATA[
        DELETE FROM wh_asn_picking_task
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="updateWhAsnPickingTaskByPrimaryKey" >
    <content >
      <![CDATA[
        UPDATE wh_asn_picking_task
        SET <[receiving_code = :receiving_code,]>
          <[task_no = :task_no,]>
          <[warehouse_code_deliver = :warehouse_code_deliver,]>
          <[warehouse_code = :warehouse_code,]>
          <[sku_total = :sku_total,]>
          <[sku_species = :sku_species,]>
          <[task_status = :task_status,]>
          <[receive_person = :receive_person,]>
          <[created_by = :created_by,]>
          <[created_date = :created_date,]>
          <[receive_date = :receive_date,]>
          <[last_update_date = :last_update_date,]>
          <[last_update_by = :last_update_by,]>
          <[picking_end_date = :picking_end_date,]>
          <[is_printing = :is_printing,]>
          <[print_user = :print_user,]>
          <[print_date = :print_date,]>
          <[is_span = :is_span,]>
        id = id
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>

  <sql datasource="dataSource" id="queryWhAsnPickingTasksAndItem">
    <content>
      <![CDATA[
         SELECT wa.id, wa.receiving_code, wa.task_no, wa.warehouse_code_deliver, wa.warehouse_code, wa.sku_total,
          wa.sku_species, wa.task_status, wa.receive_person, wa.created_by, wa.created_date, wa.receive_date,
          wa.last_update_date, wa.last_update_by, wa.picking_end_date, wa.is_printing, wa.print_user, wa.print_date,
          wa.is_span,wai.id, wai.task_id, wai.sku, wai.sku_name, wai.location, wai.quantity, wai.pick_quantity, wai.status, wai.creation_date,
          wai.last_update_date, wai.remark
          FROM wh_asn_picking_task wa left join wh_asn_picking_task_item wai on wa.id=wai.task_id
		INNER JOIN (
			select wa.id
			from wh_asn_picking_task wa
			WHERE 1 = 1
	         <[AND id = :id]>
            <[AND receiving_code = :receiving_code]>
            <[AND task_no = :task_no]>
            <[AND warehouse_code_deliver = :warehouse_code_deliver]>
            <[AND warehouse_code = :warehouse_code]>
            <[AND sku_total = :sku_total]>
            <[AND sku_species = :sku_species]>
            <[AND task_status = :task_status]>
            <[AND receive_person = :receive_person]>
            <[AND created_by = :created_by]>
            <[AND created_date = :created_date]>
            <[AND receive_date = :receive_date]>
            <[AND last_update_date = :last_update_date]>
            <[AND last_update_by = :last_update_by]>
            <[AND picking_end_date = :picking_end_date]>
            <[AND is_printing = :is_printing]>
            <[AND print_user = :print_user]>
            <[AND print_date = :print_date]>
            <[AND is_span = :is_span]>
	        <[AND wa.receiving_code IN (:receivingCodeList)]>
            <[AND wa.task_no IN (:taskNoList)]>
	        <[AND wa.created_date >= :from_creation_date]>
	        <[AND wa.created_date <= :to_creation_date]>
	        <[AND wa.picking_end_date >= :fromPickingEndDate]>
	        <[AND wa.picking_end_date <= :toPickingEndDate]>
	        <[AND wa.id in (:ids)]>
	        <[AND wa.id IN (select item.task_id from wh_asn_picking_task_item item where item.sku = :sku)]>
	        <[AND wa.id IN (select item.task_id from wh_asn_picking_task_item item where item.sku in (:skuList))]>
	        <[:LIMIT]>
		) whasn on wa.id = whasn.id
  		]]>
    </content>
  </sql>

  <sql datasource="dataSource" id="queryWhAsnPickingTasksAndItemCount">
    <content>
      <![CDATA[
  		select count(distinct wa.id)
		from wh_asn_picking_task wa
  		 WHERE 1 = 1
  		    <[AND id = :id]>
            <[AND receiving_code = :receiving_code]>
            <[AND task_no = :task_no]>
            <[AND warehouse_code_deliver = :warehouse_code_deliver]>
            <[AND warehouse_code = :warehouse_code]>
            <[AND sku_total = :sku_total]>
            <[AND sku_species = :sku_species]>
            <[AND task_status = :task_status]>
            <[AND receive_person = :receive_person]>
            <[AND created_by = :created_by]>
            <[AND created_date = :created_date]>
            <[AND receive_date = :receive_date]>
            <[AND last_update_date = :last_update_date]>
            <[AND last_update_by = :last_update_by]>
            <[AND picking_end_date = :picking_end_date]>
            <[AND is_printing = :is_printing]>
            <[AND print_user = :print_user]>
            <[AND print_date = :print_date]>
            <[AND is_span = :is_span]>
	        <[AND wa.receiving_code IN (:receivingCodeList)]>
            <[AND wa.task_no IN (:taskNoList)]>
	        <[AND wa.created_date >= :from_creation_date]>
	        <[AND wa.created_date <= :to_creation_date]>
	        <[AND wa.picking_end_date >= :fromPickingEndDate]>
	        <[AND wa.picking_end_date <= :toPickingEndDate]>
	        <[AND wa.id in (:ids)]>
	        <[AND wa.id IN (select item.task_id from wh_asn_picking_task_item item where item.sku = :sku)]>
	        <[AND wa.id IN (select item.task_id from wh_asn_picking_task_item item where item.sku in (:skuList))]>
  		]]>
    </content>
  </sql>
</sqlmap>