<?xml version="1.0" encoding="UTF-8" ?>
<sqlmap >
  <sql datasource="dataSource" id="querySmPackingExceptionCount" >
    <content >
      <![CDATA[
        SELECT COUNT(1)
        FROM sm_packing_exception s
        LEFT JOIN wh_apv apv ON apv.apv_no = s.apv_no
        WHERE 1 = 1
        <[AND s.id = :id]>
        <[AND s.apv_no = :apv_no]>
        <[AND s.box_no = :box_no]>
        <[AND s.sku = :sku]>
        <[AND s.quantity = :quantity]>
        <[AND s.scan_quantity = :scan_quantity]>
        <[AND s.less_quantity = :less_quantity]>
        <[AND s.bind_date = :bind_date]>
        <[AND s.bind_user = :bind_user]>
        <[AND apv.status = :order_status]>
        <[AND s.pick_task_no = :pick_task_no]>
        <[AND s.task_type = :task_type]>
        <[AND s.bind_date >= :fromCreatedDate]>
        <[AND s.bind_date <= :toCreatedDate]>
        <[AND s.id IN (:idList)]>
        <[AND s.apv_no IN (:apvNoList)]>
        <[AND s.box_no IN (:boxNoList)]>
        <[AND s.sku IN (:skuNoList)]>
        <[AND apv.status IN (:status_list)]>
        <[AND s.order_type IN (:ORDER_TYPE_LIST)]>
        <[AND s.order_type = :order_type]>
        <[AND s.is_task = :is_task]>
        <[AND s.box_status = :box_status]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="querySmPackingExceptionList" >
    <content >
      <![CDATA[
        SELECT s.id, s.apv_no, box_no, s.sku, quantity, s.stock_id
        , scan_quantity, less_quantity, bind_date, bind_user, apv.`status` AS  apv_status , order_status
        , pick_task_no, task_type, box_status, is_task, order_type, pick_date, pick_quantity, pick_by
        , s.first_pick_by, s.first_pick_date ,(select ws.location_number from wh_stock ws where ws.id=s.stock_id) AS 'location_number'
        FROM sm_packing_exception s
        LEFT JOIN wh_apv apv ON apv.apv_no = s.apv_no
        WHERE 1 = 1
        <[AND s.id = :id]>
        <[AND s.apv_no = :apv_no]>
        <[AND s.box_no = :box_no]>
        <[AND s.sku = :sku]>
        <[AND s.quantity = :quantity]>
        <[AND s.scan_quantity = :scan_quantity]>
        <[AND s.less_quantity = :less_quantity]>
        <[AND s.bind_date = :bind_date]>
        <[AND s.bind_user = :bind_user]>
        <[AND s.pick_task_no = :pick_task_no]>
        <[AND s.task_type = :task_type]>
        <[AND s.bind_date >= :fromCreatedDate]>
        <[AND s.bind_date <= :toCreatedDate]>
        <[AND s.id IN (:idList)]>
        <[AND s.apv_no IN (:apvNoList)]>
        <[AND s.box_no IN (:boxNoList)]>
        <[AND s.sku IN (:skuNoList)]>
        <[AND s.order_type IN (:ORDER_TYPE_LIST)]>
        <[AND s.order_type = :order_type]>
        <[AND s.is_task = :is_task]>
        <[AND s.box_status = :box_status]>
        <[AND apv.status = :order_status]>
        <[AND apv.status IN (:status_list)]>
        ORDER BY s.id DESC
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="querySmPackingExceptionByPrimaryKey" >
    <content >
      <![CDATA[
        SELECT id, apv_no, box_no, sku, quantity, scan_quantity, less_quantity, bind_date, 
        bind_user, order_status, pick_task_no, task_type, box_status, is_task, order_type,
        pick_date,pick_quantity,pick_by, first_pick_by, first_pick_date, stock_id
        FROM sm_packing_exception
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="querySmPackingException" >
    <content >
      <![CDATA[
        SELECT id, apv_no, box_no, sku, quantity, scan_quantity, less_quantity, bind_date, 
        bind_user, order_status, pick_task_no, task_type, box_status, is_task, order_type,
        pick_date,pick_quantity,pick_by, s.first_pick_by, s.first_pick_date,s.stock_id
        FROM sm_packing_exception
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND apv_no = :apv_no]>
        <[AND box_no = :box_no]>
        <[AND sku = :sku]>
        <[AND quantity = :quantity]>
        <[AND scan_quantity = :scan_quantity]>
        <[AND less_quantity = :less_quantity]>
        <[AND bind_date = :bind_date]>
        <[AND bind_user = :bind_user]>
        <[AND order_status = :order_status]>
        <[AND pick_task_no = :pick_task_no]>
        <[AND task_type = :task_type]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="createSmPackingException" >
    <content >
      <![CDATA[
        INSERT INTO sm_packing_exception (apv_no, box_no, sku, quantity, scan_quantity, less_quantity, bind_date, 
          bind_user, order_status, pick_task_no, task_type, box_status, is_task, order_type, pick_date, pick_quantity, pick_by, first_pick_by, first_pick_date, stock_id)
        VALUES (:apv_no, :box_no, :sku, :quantity, :scan_quantity, :less_quantity, :bind_date, 
          :bind_user, :order_status, :pick_task_no, :task_type, :box_status, :is_task, :order_type, :pick_date, :pick_quantity, :pick_by, :first_pick_by, :first_pick_date, :stock_id)
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="deleteSmPackingExceptionByPrimaryKey" >
    <content >
      <![CDATA[
        DELETE FROM sm_packing_exception
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="updateSmPackingExceptionByPrimaryKey" >
    <content >
      <![CDATA[
        UPDATE sm_packing_exception
        SET <[apv_no = :apv_no,]>
          <[box_no = :box_no,]>
          <[sku = :sku,]>
          <[quantity = :quantity,]>
          <[scan_quantity = :scan_quantity,]>
          <[less_quantity = :less_quantity,]>
          <[bind_date = :bind_date,]>
          <[bind_user = :bind_user,]>
          <[order_status = :order_status,]>
          <[pick_task_no = :pick_task_no,]>
          <[task_type = :task_type,]>

          <[is_task = :is_task,]>
          <[box_status = :box_status,]>
          <[order_type = :order_type,]>
          <[pick_by = :pick_by,]>
          <[pick_quantity = :pick_quantity,]>
          <[pick_date = :pick_date,]>
          <[first_pick_by = :first_pick_by,]>
          <[first_pick_date = :first_pick_date,]>
          <[stock_id = :stock_id,]>
        id = id
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>

  <sql datasource="dataSource" id="updateSmPackingExceptionStatus" >
    <content >
      <![CDATA[
        UPDATE sm_packing_exception
        SET order_status = :order_status
        WHERE 1 = 1
        AND box_no = :box_no
        AND apv_no = :apv_no
        AND sku = :sku
      ]]>
    </content>
  </sql>
  <sql datasource="dataSource" id="unbindBoxNo" >
    <content >
      <![CDATA[
        UPDATE sm_packing_exception
        SET  box_status =3
        WHERE 1 = 1
         <[AND id = :id]>
         <[AND box_no IN (:boxNoList)]>
         <[AND box_no = :box_no]>
         <[AND apv_no = :apv_no]>
         <[AND sku = :sku]>
      ]]>
    </content>
  </sql>
</sqlmap>