<?xml version="1.0" encoding="UTF-8" ?>
<sqlmap >
  <sql datasource="dataSource" id="queryTemuPrepareOrderCount" >
    <content >
      <![CDATA[
        SELECT COUNT(1)
        FROM temu_prepare_order
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND id in (:ids)]>
        <[AND account_number = :account_number]>
        <[AND account_number in (:accountNumberList)]>
        <[AND seller = :seller]>
        <[AND prepare_order_no = :prepare_order_no]>
        <[AND prepare_order_no in (:prepareOrderNoList)]>
        <[AND type = :type]>
        <[AND receive_house_id = :receive_house_id]>
        <[AND receive_house_id in (:receiveHouseIdList)]>
        <[AND receive_house = :receive_house]>
        <[AND status = :status]>
        <[AND status in (:statusList)]>
        <[AND deliver_order_no = :deliver_order_no]>
        <[AND deliver_order_no in (:deliverOrderNoList)]>
        <[AND shipping_order_no = :shipping_order_no]>
        <[AND shipping_order_no in (:shippingOrderNoList)]>
        <[AND bag_no = :bag_no]>
        <[AND pick_time = :pick_time]>
        <[AND create_by = :create_by]>
        <[AND update_by = :update_by]>
        <[AND creation_date >= :fromCreationDate]>
        <[AND creation_date <= :toCreationDate]>
        <[AND push_time >= :fromPushTime]>
        <[AND push_time <= :toPushTime]>
        <[AND deliver_time >= :fromDeliverTime]>
        <[AND deliver_time <= :toDeliverTime]>
        <[AND load_time >= :fromLoadTime]>
        <[AND load_time <= :toLoadTime]>
        <[AND express_delivery = :express_delivery]>
        <[AND hot_sale = :hot_sale]>
        <[AND split_region_flag = :split_region_flag]>
        <[AND pick_time >= :fromPickTime]>
        <[AND pick_time <= :toPickTime]>
        <[AND id in (select prepare_order_id from temu_prepare_order_item where sku = :sku)]>
        <[AND id in (select prepare_order_id from temu_prepare_order_item where sku in (:skuList))]>
        <[AND id in (select prepare_order_id from temu_prepare_order_item where id in (:itemIdList))]>
        <[AND id in (select prepare_order_id from temu_prepare_order_item where grid_time >= :fromGirdTime)]>
        <[AND id in (select prepare_order_id from temu_prepare_order_item where grid_time <= :toGirdTime)]>
        <[AND id in (select prepare_order_id from temu_prepare_order_item where package_sn = :packageSn)]>
        <[AND id in (select prepare_order_id from temu_prepare_order_item where package_sn in (:packageSnList))]>
        <[AND id in (select prepare_order_id from temu_prepare_order_item where merge_time >= :fromMergeTime)]>
        <[AND id in (select prepare_order_id from temu_prepare_order_item where merge_time <= :toMergeTime)]>
        <[AND id in (select prepare_order_id from temu_prepare_order_item where merge_time = :merge_time)]>
        <[AND id in (SELECT toi.prepare_order_id from wh_scan_shipment wc INNER JOIN wh_scan_shipment_to_apv wca ON wc.id = wca.scan_shipment_id
         INNER JOIN temu_prepare_order_item toi ON wca.apv_no = toi.package_sn WHERE wc.express_order_no IN (:trackingNumberList))]>
        <[:QUERY_TASK_ID)]>
        <[:QUERY_NULL_RECEIVE)]>
        <[:areaAndAccessCondition]>
        <[:QUERY_LOGISTICS_UPLOADED)]>
      ]]>
    </content>
  </sql>

  <sql datasource="dataSource" id="queryTemuPrepareOrders" >
    <content >
      <![CDATA[
        SELECT *
        FROM temu_prepare_order
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND id in (:ids)]>
        <[AND account_number = :account_number]>
        <[AND account_number in (:accountNumberList)]>
        <[AND seller = :seller]>
        <[AND prepare_order_no = :prepare_order_no]>
        <[AND prepare_order_no in (:prepareOrderNoList)]>
        <[AND type = :type]>
        <[AND receive_house_id = :receive_house_id]>
        <[AND receive_house_id in (:receiveHouseIdList)]>
        <[AND receive_house = :receive_house]>
        <[AND status = :status]>
        <[AND status in (:statusList)]>
        <[AND deliver_order_no = :deliver_order_no]>
        <[AND deliver_order_no in (:deliverOrderNoList)]>
        <[AND shipping_order_no = :shipping_order_no]>
        <[AND shipping_order_no in (:shippingOrderNoList)]>
        <[AND bag_no = :bag_no]>

        <[AND pick_time = :pick_time]>
        <[AND create_by = :create_by]>
        <[AND update_by = :update_by]>
        <[AND creation_date >= :fromCreationDate]>
        <[AND creation_date <= :toCreationDate]>
        <[AND push_time >= :fromPushTime]>
        <[AND push_time <= :toPushTime]>
        <[AND express_delivery = :express_delivery]>
        <[AND hot_sale = :hot_sale]>
         <[AND split_region_flag = :split_region_flag]>
        <[AND pick_time >= :fromPickTime]>
        <[AND pick_time <= :toPickTime]>
        <[AND id in (select prepare_order_id from temu_prepare_order_item where sku = :sku)]>
        <[AND id in (select prepare_order_id from temu_prepare_order_item where sku in (:skuList))]>
        <[AND id in (select prepare_order_id from temu_prepare_order_item where grid_time >= :fromGirdTime)]>
        <[AND id in (select prepare_order_id from temu_prepare_order_item where grid_time <= :toGirdTime)]>
        <[AND id in (select prepare_order_id from temu_prepare_order_item where package_sn = :packageSn)]>
        <[AND id in (select prepare_order_id from temu_prepare_order_item where package_sn in (:packageSnList))]>
        <[AND id in (select prepare_order_id from temu_prepare_order_item where deliver_time >= :fromDeliverTime)]>
        <[AND id in (select prepare_order_id from temu_prepare_order_item where deliver_time <= :toDeliverTime)]>
        <[AND id in (select prepare_order_id from temu_prepare_order_item where load_time >= :fromLoadTime)]>
        <[AND id in (select prepare_order_id from temu_prepare_order_item where load_time <= :toLoadTime)]>
        <[AND id in (select prepare_order_id from temu_prepare_order_item where merge_time >= :fromMergeTime)]>
        <[AND id in (select prepare_order_id from temu_prepare_order_item where merge_time <= :toMergeTime)]>
        <[AND id in (select prepare_order_id from temu_prepare_order_item where merge_time = :merge_time)]>
        <[AND id in (SELECT toi.prepare_order_id from wh_scan_shipment wc INNER JOIN wh_scan_shipment_to_apv wca ON wc.id = wca.scan_shipment_id
         INNER JOIN temu_prepare_order_item toi ON wca.apv_no = toi.package_sn WHERE wc.express_order_no IN (:trackingNumberList))]>
        <[:QUERY_TASK_ID)]>
        <[:QUERY_NULL_RECEIVE)]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryTemuPrepareOrderList" >
    <content >
      <![CDATA[
        select
            tpo.*, tpoi.*,
            (select GROUP_CONCAT(wls.location_number) from wh_apv_out_stock_chain sch inner join wh_transfer_stock sto on sch.stock_id = sto.id
            inner join wh_stock wls on wls.id = sto.stock_id
             where sto.stock_id is not null and sch.relevant_no = tpo.prepare_order_no and sch.sku = tpoi.sku) as location
        from (select * from temu_prepare_order
              where 1 = 1
                <[AND id = :id]>
                <[AND id in (:ids)]>
                <[AND account_number = :account_number]>
                <[AND account_number in (:accountNumberList)]>
                <[AND seller = :seller]>
                <[AND prepare_order_no = :prepare_order_no]>
                <[AND prepare_order_no in (:prepareOrderNoList)]>
                <[AND type = :type]>
                <[AND receive_house_id = :receive_house_id]>
                <[AND receive_house_id in (:receiveHouseIdList)]>
                <[AND receive_house = :receive_house]>
                <[AND status = :status]>
                <[AND status in (:statusList)]>
                <[AND deliver_order_no = :deliver_order_no]>
                <[AND deliver_order_no in (:deliverOrderNoList)]>
                <[AND shipping_order_no = :shipping_order_no]>
                <[AND shipping_order_no in (:shippingOrderNoList)]>
                <[AND bag_no = :bag_no]>
                <[AND pick_time = :pick_time]>
                <[AND create_by = :create_by]>
                <[AND update_by = :update_by]>
                <[AND creation_date >= :fromCreationDate]>
                <[AND creation_date <= :toCreationDate]>
                <[AND push_time >= :fromPushTime]>
                <[AND push_time <= :toPushTime]>
                <[AND express_delivery = :express_delivery]>
                <[AND hot_sale = :hot_sale]>
                <[AND split_region_flag = :split_region_flag]>
                <[AND pick_time >= :fromPickTime]>
                <[AND pick_time <= :toPickTime]>
                <[AND id in (select prepare_order_id from temu_prepare_order_item where sku = :sku)]>
                <[AND id in (select prepare_order_id from temu_prepare_order_item where sku in (:skuList))]>
                <[AND id in (select prepare_order_id from temu_prepare_order_item where id in (:itemIdList))]>
                <[AND id in (select prepare_order_id from temu_prepare_order_item where grid_time >= :fromGirdTime)]>
                <[AND id in (select prepare_order_id from temu_prepare_order_item where grid_time <= :toGirdTime)]>
                <[AND id in (select prepare_order_id from temu_prepare_order_item where package_sn = :packageSn)]>
                <[AND id in (select prepare_order_id from temu_prepare_order_item where package_sn in (:packageSnList))]>
                <[AND id in (select prepare_order_id from temu_prepare_order_item where deliver_time >= :fromDeliverTime)]>
                <[AND id in (select prepare_order_id from temu_prepare_order_item where deliver_time <= :toDeliverTime)]>
                <[AND id in (select prepare_order_id from temu_prepare_order_item where load_time >= :fromLoadTime)]>
                <[AND id in (select prepare_order_id from temu_prepare_order_item where load_time <= :toLoadTime)]>
                <[AND id in (select prepare_order_id from temu_prepare_order_item where merge_time >= :fromMergeTime)]>
                <[AND id in (select prepare_order_id from temu_prepare_order_item where merge_time <= :toMergeTime)]>
                <[AND id in (select prepare_order_id from temu_prepare_order_item where merge_time = :merge_time)]>
                <[AND id in (SELECT toi.prepare_order_id from wh_scan_shipment wc INNER JOIN wh_scan_shipment_to_apv wca ON wc.id = wca.scan_shipment_id
                INNER JOIN temu_prepare_order_item toi ON wca.apv_no = toi.package_sn WHERE wc.express_order_no IN (:trackingNumberList))]>
                <[:QUERY_TASK_ID)]>
                <[:QUERY_NULL_RECEIVE)]>
                <[:QUERY_LOGISTICS_UPLOADED)]>
                <[:areaAndAccessCondition]>
                ORDER BY id DESC
                <[:LIMIT]>
          ) tpo
        inner join temu_prepare_order_item tpoi on tpo.id = tpoi.prepare_order_id
        ORDER BY tpo.id DESC
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryTemuPrepareOrderByPrimaryKey" >
    <content >
      <![CDATA[
        SELECT id, account_number, seller, prepare_order_no, type, receive_house_id, receive_house, 
        status, deliver_order_no,platform_status, bag_no, push_time, merge_time, pick_time, gird_time, deliver_time,
        load_time, creation_date, create_by, update_date, update_by,delivery_method,shipping_order_no,
        shipping_company,driver_phone,receive_time,express_delivery, deliver_by,hot_sale,split_region_flag,virtual_shipping_company
        FROM temu_prepare_order
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryTemuPrepareOrder" >
    <content >
      <![CDATA[
        SELECT id, account_number, seller, prepare_order_no, type, receive_house_id, receive_house, 
        status, deliver_order_no,platform_status, bag_no, push_time, merge_time, pick_time, gird_time, deliver_time,
        load_time, creation_date, create_by, update_date, update_by,delivery_method,shipping_order_no,
        shipping_company,driver_phone,receive_time,express_delivery, deliver_by,hot_sale,split_region_flag,virtual_shipping_company
        FROM temu_prepare_order
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND account_number = :account_number]>
        <[AND seller = :seller]>
        <[AND prepare_order_no = :prepare_order_no]>
        <[AND type = :type]>
        <[AND receive_house_id = :receive_house_id]>
        <[AND receive_house_id in (:receiveHouseIdList)]>
        <[AND receive_house = :receive_house]>
        <[AND status = :status]>
        <[AND deliver_order_no = :deliver_order_no]>
        <[AND shipping_order_no = :shipping_order_no]>
        <[AND shipping_order_no in (:shippingOrderNoList)]>
        <[AND bag_no = :bag_no]>
        <[AND push_time = :push_time]>
        <[AND merge_time = :merge_time]>
        <[AND pick_time = :pick_time]>
        <[AND gird_time = :gird_time]>
        <[AND deliver_time = :deliver_time]>
        <[AND load_time = :load_time]>
        <[AND creation_date = :creation_date]>
        <[AND create_by = :create_by]>
        <[AND update_date = :update_date]>
        <[AND update_by = :update_by]>
        <[AND split_region_flag = :split_region_flag]>
         <[AND hot_sale = :hot_sale]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="createTemuPrepareOrder" >
    <content >
      <![CDATA[
        INSERT INTO temu_prepare_order (account_number, seller, prepare_order_no, type, receive_house_id, receive_house, 
          status, deliver_order_no,platform_status, bag_no, push_time, merge_time, pick_time, gird_time,
          deliver_time, load_time, creation_date, create_by, update_date, update_by, delivery_method,
          shipping_order_no, shipping_company, driver_phone, receive_time, express_delivery, deliver_by,hot_sale,split_region_flag
          )
        VALUES (:account_number, :seller, :prepare_order_no, :type, :receive_house_id, :receive_house, 
          :status, :deliver_order_no,:platform_status, :bag_no, :push_time, :merge_time, :pick_time, :gird_time,
          :deliver_time, :load_time, :creation_date, :create_by, :update_date, :update_by, :delivery_method,
          :shipping_order_no, :shipping_company, :driver_phone, :receive_time, :express_delivery, :deliver_by,:hot_sale,:split_region_flag
          )
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="deleteTemuPrepareOrderByPrimaryKey" >
    <content >
      <![CDATA[
        DELETE FROM temu_prepare_order
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="updateTemuPrepareOrderByPrimaryKey" >
    <content >
      <![CDATA[
        UPDATE temu_prepare_order
        SET <[account_number = :account_number,]>
          <[seller = :seller,]>
          <[prepare_order_no = :prepare_order_no,]>
          <[type = :type,]>
          <[receive_house_id = :receive_house_id,]>
          <[receive_house = :receive_house,]>
          <[status = :status,]>
          <[deliver_order_no = :deliver_order_no,]>
          <[platform_status = :platform_status,]>
          <[bag_no = :bag_no,]>
          <[push_time = :push_time,]>
          <[merge_time = :merge_time,]>
          <[pick_time = :pick_time,]>
          <[gird_time = :gird_time,]>
          <[deliver_time = :deliver_time,]>
          <[load_time = :load_time,]>
          <[creation_date = :creation_date,]>
          <[create_by = :create_by,]>
          <[update_date = :update_date,]>
          <[update_by = :update_by,]>
          <[delivery_method = :delivery_method,]>
          <[shipping_order_no = :shipping_order_no,]>
          <[shipping_company = :shipping_company,]>
          <[driver_phone = :driver_phone,]>
          <[receive_time = :receive_time,]>
          <[express_delivery = :express_delivery,]>
          <[deliver_by = :deliver_by,]>
          <[hot_sale = :hot_sale,]>
          <[split_region_flag = :split_region_flag,]>
          <[virtual_shipping_company = :virtual_shipping_company,]>
        id = id
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>

  <!--拣货查询 -->
  <sql datasource="dataSource" id="queryOrderByPickingTaskId" >
    <content >
      <![CDATA[
        SELECT
            t.id,t.prepare_order_no,t.STATUS,t.push_time,t.account_number,
            ti.id,ti.prepare_order_id,ti.sku,ti.skc,ti.sku_id,ti.sku_name, ti.prepare_quantity,
            ti.grid_quantity, ti.allot_quantity, ti.pick_quantity,ti.grid_status,ti.package_status,ti.package_sn
        FROM
            temu_prepare_order t
            LEFT JOIN temu_prepare_order_item ti ON ti.prepare_order_id = t.id
        WHERE
            1 = 1
            AND ti.id IN ( SELECT task_item.apv_id FROM wh_picking_task_item task_item
            LEFT JOIN wh_picking_task task ON task.id = task_item.task_id WHERE task.id = :task_id )
        ORDER BY t.id ASC
      ]]>
    </content>
  </sql>

  <sql datasource="dataSource" id="queryTemuPackageCount" >
    <content >
      <![CDATA[
       SELECT COUNT(*) FROM temu_prepare_order_item tpoi
       INNER JOIN temu_prepare_order tpo ON  tpo.id = tpoi.prepare_order_id
        WHERE 1 = 1
        <[AND tpo.id = :id]>
        <[AND tpo.id in (:ids)]>
        <[AND tpo.account_number = :account_number]>
        <[AND tpo.account_number in (:accountNumberList)]>
        <[AND tpo.seller = :seller]>
        <[AND tpo.prepare_order_no = :prepare_order_no]>
        <[AND tpo.prepare_order_no in (:prepareOrderNoList)]>
        <[AND tpo.type = :type]>
        <[AND tpo.receive_house_id = :receive_house_id]>
        <[AND tpo.receive_house_id in (:receiveHouseIdList)]>
        <[AND tpo.receive_house = :receive_house]>
        <[AND tpo.status = :status]>
        <[AND tpo.status in (:statusList)]>
        <[AND tpo.deliver_order_no = :deliver_order_no]>
        <[AND tpo.deliver_order_no in (:deliverOrderNoList)]>
        <[AND tpo.shipping_order_no = :shipping_order_no]>
        <[AND tpo.shipping_order_no in (:shippingOrderNoList)]>
        <[AND tpo.bag_no = :bag_no]>
        <[AND tpo.pick_time = :pick_time]>
        <[AND tpo.create_by = :create_by]>
        <[AND tpo.update_by = :update_by]>
        <[AND tpo.creation_date >= :fromCreationDate]>
        <[AND tpo.creation_date <= :toCreationDate]>
        <[AND tpo.push_time >= :fromPushTime]>
        <[AND tpo.push_time <= :toPushTime]>
        <[AND tpo.express_delivery = :express_delivery]>
        <[AND tpo.hot_sale = :hot_sale]>
        <[AND tpoi.merge_time = :merge_time]>
        <[AND tpoi.merge_time >= :fromMergeTime]>
        <[AND tpoi.merge_time <= :toMergeTime]>
        <[AND tpo.pick_time >= :fromPickTime]>
        <[AND tpo.pick_time <= :toPickTime]>
        <[AND tpoi.deliver_time >= :fromDeliverTime]>
        <[AND tpoi.deliver_time <= :toDeliverTime]>
        <[AND tpoi.load_time >= :fromLoadTime]>
        <[AND tpoi.load_time <= :toLoadTime]>
        <[AND tpoi.pick_order_time >= :fromPickOrderTime]>
        <[AND tpoi.pick_order_time <= :toPickOrderTime]>
        <[AND tpoi.sku = :sku]>
        <[AND tpoi.sku in (:skuList)]>
        <[AND tpoi.package_status = :packageStatus]>
        <[AND tpoi.package_status in (:packageStatusList)]>
        <[AND tpoi.id = :itemId]>
        <[AND tpoi.id in (:itemIdList)]>
        <[AND tpoi.grid_time >= :fromGirdTime]>
        <[AND tpoi.grid_time <= :toGirdTime]>
        <[AND tpoi.package_sn = :packageSn]>
        <[AND tpoi.source_from = :source_from]>
        <[AND tpoi.package_sn in (:packageSnList)]>
        <[AND tpoi.package_sn in (SELECT wca.apv_no from wh_scan_shipment wc INNER JOIN wh_scan_shipment_to_apv wca ON  wc.id = wca.scan_shipment_id WHERE wc.express_order_no IN (:trackingNumberList))]>
        <[AND tpoi.id in (SELECT i.apv_id from wh_picking_task_item i  LEFT JOIN wh_picking_task t ON t.id = i.task_id where t.task_no = :taskNo )]>
        <[:CONDITION_BOX_NUMBER]>
        <[:areaAndAccessCondition]>
        <[:APV_TYPE_TEMU]>
      ]]>
    </content>
  </sql>

  <sql datasource="dataSource" id="queryTemuPackageList" >
    <content >
      <![CDATA[
        select
            tpo.*, tpoi.*,
              (SELECT CONCAT(wc.express_company,'<br/>',wc.express_order_no) from wh_scan_shipment wc INNER JOIN wh_scan_shipment_to_apv wca ON wc.id = wca.scan_shipment_id
                WHERE wca.apv_no = tpoi.package_sn order by wc.id desc limit 1 ) as express_info
              , (select b.number from temu_pick_box b inner join temu_pick_box_item bi on b.id = bi.box_id where bi.relation_item_id = tpoi.id) as pickBoxNumber
              ,(select GROUP_CONCAT(wls.location_number) from wh_apv_out_stock_chain sch inner join wh_transfer_stock sto on sch.stock_id = sto.id
            inner join wh_stock wls on wls.id = sto.stock_id
             where sto.stock_id is not null and sch.relevant_no = tpo.prepare_order_no and sch.sku = tpoi.sku) as location
        FROM  temu_prepare_order_item tpoi
        INNER JOIN temu_prepare_order tpo ON  tpo.id = tpoi.prepare_order_id
        WHERE 1 = 1
        <[AND tpo.id = :id]>
        <[AND tpo.id in (:ids)]>
        <[AND tpo.account_number = :account_number]>
        <[AND tpo.account_number in (:accountNumberList)]>
        <[AND tpo.seller = :seller]>
        <[AND tpo.prepare_order_no = :prepare_order_no]>
        <[AND tpo.prepare_order_no in (:prepareOrderNoList)]>
        <[AND tpo.type = :type]>
        <[AND tpo.receive_house_id = :receive_house_id]>
        <[AND tpo.receive_house_id in (:receiveHouseIdList)]>
        <[AND tpo.receive_house = :receive_house]>
        <[AND tpo.status = :status]>
        <[AND tpo.status in (:statusList)]>
        <[AND tpo.deliver_order_no = :deliver_order_no]>
        <[AND tpo.deliver_order_no in (:deliverOrderNoList)]>
        <[AND tpo.shipping_order_no = :shipping_order_no]>
        <[AND tpo.shipping_order_no in (:shippingOrderNoList)]>
        <[AND tpo.bag_no = :bag_no]>
        <[AND tpo.pick_time = :pick_time]>
        <[AND tpo.create_by = :create_by]>
        <[AND tpo.update_by = :update_by]>
        <[AND tpo.creation_date >= :fromCreationDate]>
        <[AND tpo.creation_date <= :toCreationDate]>
        <[AND tpo.push_time >= :fromPushTime]>
        <[AND tpo.push_time <= :toPushTime]>
        <[AND tpo.express_delivery = :express_delivery]>
        <[AND tpo.hot_sale = :hot_sale]>
        <[AND tpoi.merge_time = :merge_time]>
        <[AND tpoi.merge_time >= :fromMergeTime]>
        <[AND tpoi.merge_time <= :toMergeTime]>
        <[AND tpo.pick_time >= :fromPickTime]>
        <[AND tpo.pick_time <= :toPickTime]>
        <[AND tpoi.deliver_time >= :fromDeliverTime]>
        <[AND tpoi.deliver_time <= :toDeliverTime]>
        <[AND tpoi.load_time >= :fromLoadTime]>
        <[AND tpoi.load_time <= :toLoadTime]>
        <[AND tpoi.pick_order_time >= :fromPickOrderTime]>
        <[AND tpoi.pick_order_time <= :toPickOrderTime]>
        <[AND tpoi.sku = :sku]>
        <[AND tpoi.sku in (:skuList)]>
        <[AND tpoi.package_status = :packageStatus]>
        <[AND tpoi.package_status in (:packageStatusList)]>
        <[AND tpoi.id = :itemId]>
        <[AND tpoi.id in (:itemIdList)]>
        <[AND tpoi.grid_time >= :fromGirdTime]>
        <[AND tpoi.grid_time <= :toGirdTime]>
        <[AND tpoi.package_sn = :packageSn]>
        <[AND tpoi.source_from = :source_from]>
        <[AND tpoi.package_sn in (:packageSnList)]>
        <[AND tpoi.package_sn in (SELECT wca.apv_no from wh_scan_shipment wc INNER JOIN wh_scan_shipment_to_apv wca ON  wc.id = wca.scan_shipment_id WHERE wc.express_order_no IN (:trackingNumberList))]>
        <[AND tpoi.id in (SELECT i.apv_id from wh_picking_task_item i  LEFT JOIN wh_picking_task t ON t.id = i.task_id where t.task_no = :taskNo )]>
        <[:CONDITION_BOX_NUMBER]>
        <[:areaAndAccessCondition]>
        <[:APV_TYPE_TEMU]>
        ORDER BY tpo.id DESC
        <[:LIMIT]>
      ]]>
    </content>
  </sql>

  <sql datasource="dataSource" id="queryTemuAccountNumberList" >
    <content >
      <![CDATA[
        SELECT DISTINCT account_number FROM temu_prepare_order
      ]]>
    </content>
  </sql>

  <sql datasource="dataSource" id="scanMaxPriorityGoodsPrintTemu" >
    <content >
      <![CDATA[
       SELECT
        	o.id, o.prepare_order_no,i.id, i.package_status, i.package_sn,IFNULL(i.pick_quantity,0) pick_quantity
		FROM temu_prepare_order o
        LEFT JOIN temu_prepare_order_item i ON i.prepare_order_id = o.id
		WHERE 1 = 1
		<[AND i.sku = :sku]>
		<[AND i.package_status in (:statusList)]>
		<[AND i.package_sn = :packageSn]>
		<[:QUERY_BY_TASK_NO]>
		<[:APV_TYPE_TEMU]>
		ORDER BY o.id,i.id
		LIMIT 1
      ]]>
    </content>
  </sql>
</sqlmap>