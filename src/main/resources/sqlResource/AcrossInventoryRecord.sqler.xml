<?xml version="1.0" encoding="UTF-8" ?>
<sqlmap >
  <sql datasource="dataSource" id="queryAcrossInventoryRecordCount" >
    <content >
      <![CDATA[
        SELECT COUNT(1)
        FROM across_inventory_record
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND apv_no = :apv_no]>
        <[AND match_request_status = :match_request_status]>
        <[AND move_request_status = :move_request_status]>
        <[AND cancle_request_status = :cancle_request_status]>
        <[AND create_time = :create_time]>
        <[AND update_time = :update_time]>
        <[AND sku_option = :sku_option]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryAcrossInventoryRecordList" >
    <content >
      <![CDATA[
        SELECT id, apv_no, match_request_status, move_request_status, cancle_request_status, 
        create_time, update_time, sku_option
        FROM across_inventory_record
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND apv_no = :apv_no]>
        <[AND match_request_status = :match_request_status]>
        <[AND move_request_status = :move_request_status]>
        <[AND cancle_request_status = :cancle_request_status]>
        <[AND create_time = :create_time]>
        <[AND update_time = :update_time]>
        <[AND sku_option = :sku_option]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryAcrossInventoryRecordByPrimaryKey" >
    <content >
      <![CDATA[
        SELECT id, apv_no, match_request_status, move_request_status, cancle_request_status, 
        create_time, update_time, sku_option
        FROM across_inventory_record
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryAcrossInventoryRecord" >
    <content >
      <![CDATA[
        SELECT id, apv_no, match_request_status, move_request_status, cancle_request_status, 
        create_time, update_time, sku_option
        FROM across_inventory_record
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND apv_no = :apv_no]>
        <[AND match_request_status = :match_request_status]>
        <[AND move_request_status = :move_request_status]>
        <[AND cancle_request_status = :cancle_request_status]>
        <[AND create_time = :create_time]>
        <[AND update_time = :update_time]>
        <[AND sku_option = :sku_option]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="createAcrossInventoryRecord" >
    <content >
      <![CDATA[
        INSERT INTO across_inventory_record (apv_no, match_request_status, move_request_status, cancle_request_status, 
          create_time, update_time, sku_option)
        VALUES (:apv_no, :match_request_status, :move_request_status, :cancle_request_status, 
          :create_time, :update_time, :sku_option)
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="deleteAcrossInventoryRecordByPrimaryKey" >
    <content >
      <![CDATA[
        DELETE FROM across_inventory_record
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="updateAcrossInventoryRecordByPrimaryKey" >
    <content >
      <![CDATA[
        UPDATE across_inventory_record
        SET <[apv_no = :apv_no,]>
          <[match_request_status = :match_request_status,]>
          <[move_request_status = :move_request_status,]>
          <[cancle_request_status = :cancle_request_status,]>
          <[create_time = :create_time,]>
          <[update_time = :update_time,]>
          <[sku_option = :sku_option,]>
        id = id
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
</sqlmap>