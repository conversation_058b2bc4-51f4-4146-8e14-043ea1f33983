<?xml version="1.0" encoding="UTF-8" ?>
<sqlmap >
  <sql datasource="dataSource" id="queryWhStockLogCount" >
    <content >
      <![CDATA[
        SELECT COUNT(1)
        FROM <[:table_index]>
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND type = :type]>
        <[AND stock_id = :stock_id]>
        <[AND location_number = :location_number]>
        <[AND step = :step]>
        <[AND step IN (:step_list)]>
        <[AND sku = :sku]>
        <[AND quantity = :quantity]>
        <[AND original_quantity = :original_quantity]>
        <[AND content = :content]>
        <[AND content in (:content_list)]>
        <[AND continuity = :continuity]>
        <[AND creation_date = :creation_date]>
        <[AND create_by = :create_by]>

        <[AND creation_date >= :fromDate]>
        <[AND creation_date <= :toDate]>
        <[:CHANGE_QUANTITY]>
        <[:NOT_CONTINUITY]>

        ORDER BY id DESC

      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryWhStockLogList" >
    <content >
      <![CDATA[
        SELECT id, type,stock_id,location_number, step, sku, quantity, original_quantity, content, continuity, creation_date, create_by
        FROM <[:table_index]>
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND type = :type]>
        <[AND stock_id = :stock_id]>
        <[AND location_number = :location_number]>
        <[AND step = :step]>
        <[AND step IN (:step_list)]>
        <[AND sku = :sku]>
        <[AND quantity = :quantity]>
        <[AND original_quantity = :original_quantity]>
        <[AND content = :content]>
        <[AND content in (:content_list)]>
        <[AND continuity = :continuity]>
        <[AND creation_date = :creation_date]>
        <[AND create_by = :create_by]>

        <[AND creation_date >= :fromDate]>
        <[AND creation_date <= :toDate]>
        <[:CHANGE_QUANTITY]>
        <[:NOT_CONTINUITY]>

        ORDER BY id DESC

      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryWhStockLogByPrimaryKey" >
    <content >
      <![CDATA[
        SELECT id, type,stock_id,location_number,step, sku, quantity, original_quantity, content, continuity, creation_date, create_by
        FROM wh_stock_log
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryWhStockLog" >
    <content >
      <![CDATA[
        SELECT id, type,stock_id,location_number, step, sku, quantity, original_quantity, content, continuity, creation_date, create_by
        FROM wh_stock_log
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND type = :type]>
        <[AND stock_id = :stock_id]>
        <[AND location_number = :location_number]>
        <[AND step = :step]>
        <[AND sku = :sku]>
        <[AND quantity = :quantity]>
        <[AND original_quantity = :original_quantity]>
        <[AND content = :content]>
        <[AND content in (:content_list)]>
        <[AND continuity = :continuity]>
        <[AND creation_date = :creation_date]>
        <[AND create_by = :create_by]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="createWhStockLog" >
    <content >
      <![CDATA[
        INSERT INTO <[:table_index]> (type,stock_id,location_number, step, sku, quantity, original_quantity, content, continuity, creation_date, create_by
          )
        VALUES (:type,:stock_id,:location_number, :step, :sku, :quantity, :original_quantity, :content, :continuity, :creation_date, :create_by
          )
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="deleteWhStockLogByPrimaryKey" >
    <content >
      <![CDATA[
        DELETE FROM wh_stock_log
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="updateWhStockLogByPrimaryKey" >
    <content >
      <![CDATA[
        UPDATE wh_stock_log
        SET <[type = :type,]>
            <[stock_id = :stock_id,]>
            <[location_number = :location_number,]>
            <[step = :step,]>
            <[sku = :sku,]>
            <[quantity = :quantity,]>
            <[original_quantity = :original_quantity,]>
            <[content = :content,]>
            <[continuity = :continuity,]>
            <[creation_date = :creation_date,]>
            <[create_by = :create_by,]>
        id = id
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>

  <!--通过当前日志查询上一条日志记录-->
  <sql datasource="dataSource" id="queryLastWhStockLogByCurrentLog" >
    <content >
      <![CDATA[
        SELECT id, type,stock_id,location_number, step, sku, quantity, original_quantity, content, continuity, creation_date, create_by
        FROM <[:table_index]>
        WHERE 1 = 1
        <[AND id < :id]>
        <[AND type = :type]>
        <[AND sku = :sku]>
        <[AND stock_id = :stock_id]>
        ORDER BY id DESC LIMIT 1
      ]]>
    </content>
  </sql>

   <!--更新库存日志-->
  <sql datasource="dataSource" id="updateWhStockLogByWhStockLog" >
    <content >
      <![CDATA[
        UPDATE <[:table_index]>
        SET <[type = :type,]>
          <[stock_id = :stock_id,]>
          <[location_number = :location_number,]>
          <[sku = :sku,]>
          <[continuity = :continuity,]>
        id = id
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
</sqlmap>