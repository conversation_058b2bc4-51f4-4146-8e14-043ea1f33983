<?xml version="1.0" encoding="UTF-8" ?>
<sqlmap >
  <sql datasource="dataSource" id="querySkuReturnStatisticsCount" >
    <content >
      <![CDATA[
        SELECT COUNT(1)
        FROM sku_return_statistics
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND apv_no = :apv_no]>
        <[AND sku = :sku]>
        <[AND sku_tag = :sku_tag]>
        <[AND platform = :platform]>
        <[AND return_time = :return_time]>
        <[AND return_by = :return_by]>
        <[AND return_time >= :startReturnTime]>
        <[AND return_time <= :endReturnTime]>
        <[AND sku in (:skuList)]>
        <[AND apv_no in (:apvNoList)]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="querySkuReturnStatisticsList" >
    <content >
      <![CDATA[
        SELECT id, apv_no, sku, sku_tag, platform, return_time, return_by
        FROM sku_return_statistics
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND apv_no = :apv_no]>
        <[AND sku = :sku]>
        <[AND sku_tag = :sku_tag]>
        <[AND platform = :platform]>
        <[AND return_time = :return_time]>
        <[AND return_by = :return_by]>
        <[AND return_time >= :startReturnTime]>
        <[AND return_time <= :endReturnTime]>
        <[AND sku in (:skuList)]>
        <[AND apv_no in (:apvNoList)]>
        ORDER BY id DESC
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="querySkuReturnStatisticsByPrimaryKey" >
    <content >
      <![CDATA[
        SELECT id, apv_no, sku, sku_tag, platform, return_time, return_by
        FROM sku_return_statistics
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="querySkuReturnStatistics" >
    <content >
      <![CDATA[
        SELECT id, apv_no, sku, sku_tag, platform, return_time, return_by
        FROM sku_return_statistics
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND apv_no = :apv_no]>
        <[AND sku = :sku]>
        <[AND sku_tag = :sku_tag]>
        <[AND platform = :platform]>
        <[AND return_time = :return_time]>
        <[AND return_by = :return_by]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="createSkuReturnStatistics" >
    <content >
      <![CDATA[
        INSERT INTO sku_return_statistics (apv_no, sku, sku_tag, platform, return_time, return_by)
        VALUES (:apv_no, :sku, :sku_tag, :platform, :return_time, :return_by)
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="deleteSkuReturnStatisticsByPrimaryKey" >
    <content >
      <![CDATA[
        DELETE FROM sku_return_statistics
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="updateSkuReturnStatisticsByPrimaryKey" >
    <content >
      <![CDATA[
        UPDATE sku_return_statistics
        SET <[apv_no = :apv_no,]>
          <[sku = :sku,]>
          <[sku_tag = :sku_tag,]>
          <[platform = :platform,]>
          <[return_time = :return_time,]>
          <[return_by = :return_by,]>
        id = id
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
</sqlmap>