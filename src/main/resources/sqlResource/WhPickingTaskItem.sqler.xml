<?xml version="1.0" encoding="UTF-8" ?>
<sqlmap >
  <sql datasource="dataSource" id="queryWhPickingTaskItemCount" >
    <content >
      <![CDATA[
        SELECT COUNT(1)
        FROM wh_picking_task_item
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND task_id = :task_id]>
        <[AND apv_no = :apv_no]>
        <[AND created_date = :created_date]>
        <[AND create_by = :create_by]>
        <[AND status = :status]>
        <[AND apv_id = :apv_id]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryWhPickingTaskItemList" >
    <content >
      <![CDATA[
        SELECT id, task_id, apv_id, apv_no, created_date, create_by, status
        <[:QUERY_PREPARE_ORDER_NO]>
        FROM wh_picking_task_item
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND task_id = :task_id]>
        <[AND task_id IN (:taskIds)]>
        <[AND apv_no = :apv_no]>
        <[AND created_date = :created_date]>
        <[AND create_by = :create_by]>
        <[AND status = :status]>
        <[AND apv_id = :apv_id]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryWhPickingTaskItemByPrimaryKey" >
    <content >
      <![CDATA[
        SELECT id, task_id, apv_no, created_date, create_by, status, apv_id
        FROM wh_picking_task_item
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryWhPickingTaskItem" >
    <content >
      <![CDATA[
        SELECT id, task_id, apv_no, created_date, create_by, status, apv_id
        FROM wh_picking_task_item
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND task_id = :task_id]>
        <[AND apv_no = :apv_no]>
        <[AND created_date = :created_date]>
        <[AND create_by = :create_by]>
        <[AND status = :status]>
        <[AND apv_id = :apv_id]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="createWhPickingTaskItem" >
    <content >
      <![CDATA[
        INSERT INTO wh_picking_task_item (task_id, apv_no, created_date, create_by, status, apv_id)
        VALUES (:task_id, :apv_no, :created_date, :create_by, :status, :apv_id)
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="deleteWhPickingTaskItemByPrimaryKey" >
    <content >
      <![CDATA[
        DELETE FROM wh_picking_task_item
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="updateWhPickingTaskItemByPrimaryKey" >
    <content >
      <![CDATA[
        UPDATE wh_picking_task_item
        SET <[task_id = :task_id,]>
          <[apv_no = :apv_no,]>
          <[created_date = :created_date,]>
          <[create_by = :create_by,]>
          <[status = :status,]>
          <[apv_id = :apv_id,]>
        id = id
        WHERE 1 = 1
        AND id = :id
        
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="updateWhPickingTaskItemByApvId" >
    <content >
      <![CDATA[
        UPDATE wh_picking_task_item
        SET <[task_id = :task_id,]>
          <[apv_no = :apv_no,]>
          <[apv_id = :apv_id,]>
          <[created_date = :created_date,]>
          <[create_by = :create_by,]>
          <[status = :status,]>
          <[apv_id = :apv_id,]>
        apv_id = apv_id
        WHERE 1 = 1
        AND apv_id = :apv_id
      ]]>
    </content>
  </sql>

  <sql datasource="dataSource" id="updateWhPickingTaskItemMoveAsPrintByApvIdAndTaskId" >
    <content >
      <![CDATA[
        UPDATE wh_picking_task_item
        SET
          <[move_as_print = :move_as_print,]>
        apv_id = apv_id
        WHERE 1 = 1
        AND apv_id = :apv_id
        AND task_id = :task_id
      ]]>
    </content>
  </sql>

  <!--查询拣货任务关联过的APVID-->
  <sql datasource="dataSource" id="queryPickingTaskItemApvIdList" >
    <content >
      <![CDATA[
        SELECT id FROM wh_apv a
        LEFT JOIN wh_picking_task_item i ON i.apv_id = a.id
        LEFT JOIN wh_picking_task t ON t.id = i.task_id
        WHERE 1 = 1
        <[AND t.id = :taskId]>
        <[AND t.task_no = :taskNo]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryPickingTaskItemAndSkuList" >
    <content >
      <![CDATA[
        SELECT wpti.id, wpti.task_id, wpti.apv_no, wpti.created_date, wpti.create_by, wpti.status, wpti.apv_id, wpti.move_as_print,
        wai.sku, wai.sale_quantity sale_quantity, wai.pick_quantity, wai.apv_line_item_id,
        IFNULL(ws.name,(SELECT name FROM WH_COMBINE_SKU wcs WHERE wai.sku = wcs.spu)) AS 'ws.name',
        ws.location_number,ws.warehouse_id,
        wa.paid_date
        FROM wh_picking_task_item wpti
        Left join wh_apv wa on  wa.apv_no = wpti.apv_no
        Left join wh_apv_item wai on  wai.apv_id = wa.id
        Left join wh_sku ws on ws.sku = wai.sku 
        WHERE 1 = 1
        <[AND wpti.id = :id]>
        <[AND wpti.task_id = :task_id]>
        <[AND wpti.apv_no = :apv_no]>
        <[AND wpti.apv_id = :apv_id]>
        <[AND wai.sku = :sku]>
        <[:orderby]>
      ]]>
    </content>
  </sql>

  <sql datasource="dataSource" id="queryPddTaskItemAndSkuList" >
    <content >
      <![CDATA[
        SELECT wpti.id, wpti.task_id, wpti.apv_no, wpti.created_date, wpti.create_by, wpti.status, wpti.apv_id, wpti.move_as_print,
        wai.sku, wai.real_quantity sale_quantity, wai.pick_quantity,
        ws.name, ws.location_number,ws.warehouse_id,wa.prepare_order_no
        FROM wh_picking_task_item wpti
        LEFT JOIN temu_prepare_order_item wai ON wai.id = wpti.apv_id
        LEFT JOIN temu_prepare_order wa ON wa.id = wai.prepare_order_id
        LEFT JOIN wh_sku ws ON ws.sku = wai.sku
        WHERE 1 = 1
        <[AND wpti.id = :id]>
        <[AND wpti.task_id = :task_id]>
        <[AND wpti.apv_no = :apv_no]>
        <[AND wpti.apv_id = :apv_id]>
        <[AND wai.sku = :sku]>
        <[:orderby]>
      ]]>
    </content>
  </sql>

  <sql datasource="dataSource" id="queryTaskItemAndSkuList" >
    <content >
      <![CDATA[
        SELECT wpti.id, wpti.task_id, wpti.apv_no, wpti.created_date, wpti.create_by, wpti.status, wpti.apv_id, wpti.move_as_print,
        wai.product_sku sku, wai.sku_quantity sale_quantity, wai.pick_quantity,
        ws.name, ws.location_number,ws.warehouse_id
        FROM wh_picking_task_item wpti
        LEFT JOIN wh_fba_allocation wa ON wa.fba_no = wpti.apv_no
        LEFT JOIN wh_fba_allocation_item wai ON wai.fba_id = wa.id
        LEFT JOIN wh_sku ws ON ws.sku = wai.product_sku
        WHERE 1 = 1
        <[AND wpti.id = :id]>
        <[AND wpti.task_id = :task_id]>
        <[AND wpti.apv_no = :apv_no]>
        <[AND wpti.apv_id = :apv_id]>
        <[AND wai.product_sku = :sku]>
        <[:orderby]>
      ]]>
    </content>
  </sql>
</sqlmap>