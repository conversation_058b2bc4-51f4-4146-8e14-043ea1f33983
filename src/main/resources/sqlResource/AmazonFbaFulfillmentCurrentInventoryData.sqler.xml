<?xml version="1.0" encoding="UTF-8" ?>
<sqlmap >
  <sql datasource="dataSource" id="queryAmazonFbaFulfillmentCurrentInventoryDataCount" >
    <content >
      <![CDATA[
        SELECT COUNT(1)
        FROM amazon_fba_fulfillment_current_inventory_data
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND account_number = :account_number]>
        <[AND snapshot_date = :snapshot_date]>
        <[AND fnsku = :fnsku]>
        <[AND sku = :sku]>
        <[AND product_name = :product_name]>
        <[AND quantity = :quantity]>
        <[AND fulfillment_center_id = :fulfillment_center_id]>
        <[AND detailed_disposition = :detailed_disposition]>
        <[AND country = :country]>
        <[AND created_by = :created_by]>
        <[AND creation_date = :creation_date]>
        <[AND last_update_user = :last_update_user]>
        <[AND last_update_date = :last_update_date]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryAmazonFbaFulfillmentCurrentInventoryDataList" >
    <content >
      <![CDATA[
        SELECT id, account_number, snapshot_date, fnsku, sku, product_name, quantity, fulfillment_center_id, 
        detailed_disposition, country, created_by, creation_date, last_update_user, last_update_date
        FROM amazon_fba_fulfillment_current_inventory_data
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND account_number = :account_number]>
        <[AND snapshot_date = :snapshot_date]>
        <[AND fnsku = :fnsku]>
        <[AND sku = :sku]>
        <[AND product_name = :product_name]>
        <[AND quantity = :quantity]>
        <[AND fulfillment_center_id = :fulfillment_center_id]>
        <[AND detailed_disposition = :detailed_disposition]>
        <[AND country = :country]>
        <[AND created_by = :created_by]>
        <[AND creation_date = :creation_date]>
        <[AND last_update_user = :last_update_user]>
        <[AND last_update_date = :last_update_date]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryAmazonFbaFulfillmentCurrentInventoryDataByPrimaryKey" >
    <content >
      <![CDATA[
        SELECT id, account_number, snapshot_date, fnsku, sku, product_name, quantity, fulfillment_center_id, 
        detailed_disposition, country, created_by, creation_date, last_update_user, last_update_date
        FROM amazon_fba_fulfillment_current_inventory_data
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryAmazonFbaFulfillmentCurrentInventoryData" >
    <content >
      <![CDATA[
        SELECT id, account_number, snapshot_date, fnsku, sku, product_name, quantity, fulfillment_center_id, 
        detailed_disposition, country, created_by, creation_date, last_update_user, last_update_date
        FROM amazon_fba_fulfillment_current_inventory_data
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND account_number = :account_number]>
        <[AND snapshot_date = :snapshot_date]>
        <[AND fnsku = :fnsku]>
        <[AND sku = :sku]>
        <[AND product_name = :product_name]>
        <[AND quantity = :quantity]>
        <[AND fulfillment_center_id = :fulfillment_center_id]>
        <[AND detailed_disposition = :detailed_disposition]>
        <[AND country = :country]>
        <[AND created_by = :created_by]>
        <[AND creation_date = :creation_date]>
        <[AND last_update_user = :last_update_user]>
        <[AND last_update_date = :last_update_date]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="createAmazonFbaFulfillmentCurrentInventoryData" >
    <content >
      <![CDATA[
        INSERT INTO amazon_fba_fulfillment_current_inventory_data (account_number, snapshot_date, fnsku, sku, product_name, quantity, 
          fulfillment_center_id, detailed_disposition, country, created_by, creation_date, 
          last_update_user, last_update_date)
        VALUES (:account_number, :snapshot_date, :fnsku, :sku, :product_name, :quantity, 
          :fulfillment_center_id, :detailed_disposition, :country, :created_by, :creation_date, 
          :last_update_user, :last_update_date)
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="deleteAmazonFbaFulfillmentCurrentInventoryDataByPrimaryKey" >
    <content >
      <![CDATA[
        DELETE FROM amazon_fba_fulfillment_current_inventory_data
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  <sql datasource="dataSource" id="batchInsertOrUpdateAmazonFbaFulfillmentCurrentInventoryData">
    <![CDATA[
       INSERT INTO amazon_fba_fulfillment_current_inventory_data (account_number,market_place,merchant_id, snapshot_date, fnsku, sku, product_name, quantity,
          fulfillment_center_id, detailed_disposition, country, created_by, creation_date,
          last_update_user, last_update_date)
        VALUES
        <[:INSERT_SQL]>
        ON DUPLICATE KEY UPDATE
        <[:UPDATE_SQL]>
      ]]>
  </sql>
  <sql datasource="dataSource" id="updateAmazonFbaFulfillmentCurrentInventoryDataByPrimaryKey" >
    <content >
      <![CDATA[
        UPDATE amazon_fba_fulfillment_current_inventory_data
        SET <[account_number = :account_number,]>
          <[snapshot_date = :snapshot_date,]>
          <[fnsku = :fnsku,]>
          <[sku = :sku,]>
          <[product_name = :product_name,]>
          <[quantity = :quantity,]>
          <[fulfillment_center_id = :fulfillment_center_id,]>
          <[detailed_disposition = :detailed_disposition,]>
          <[country = :country,]>
          <[created_by = :created_by,]>
          <[creation_date = :creation_date,]>
          <[last_update_user = :last_update_user,]>
          <[last_update_date = :last_update_date,]>
        id = id
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
</sqlmap>