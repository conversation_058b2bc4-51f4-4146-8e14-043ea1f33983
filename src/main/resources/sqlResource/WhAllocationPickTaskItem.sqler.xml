<?xml version="1.0" encoding="UTF-8" ?>
<sqlmap >
  <sql datasource="dataSource" id="queryWhAllocationPickTaskItemCount" >
    <content >
      <![CDATA[
        SELECT COUNT(1)
        FROM wh_allocation_pick_task_item
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND task_id = :task_id]>
        <[AND allocation_item_id = :allocation_item_id]>
        <[AND sku = :sku]>
        <[AND quantity = :quantity]>
        <[AND pick_quantity = :pick_quantity]>
        <[AND create_by = :create_by]>
        <[AND create_time = :create_time]>
        <[AND update_by = :update_by]>
        <[AND update_time = :update_time]>
        <[AND pick_status = :pick_status]>
        <[AND sku IN (:pick_sku_list)]>
        <[AND allocation_item_id IN (SELECT allocation_item_id FROM wh_apv_allocation_item WHERE allocation_item_id=T.allocation_item_id AND allocation_id = :allocationId)]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryWhAllocationPickTaskItemList" >
    <content >
      <![CDATA[
        SELECT id, task_id, allocation_item_id, sku, quantity, pick_quantity, create_by, 
        create_time, update_by, update_time, pick_status,
        (SELECT waai.stock_id FROM  wh_apv_allocation_item waai where waai.allocation_item_id = T.allocation_item_id) AS 'stock_id'
        FROM wh_allocation_pick_task_item T
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND task_id = :task_id]>
        <[AND allocation_item_id = :allocation_item_id]>
        <[AND sku = :sku]>
        <[AND quantity = :quantity]>
        <[AND pick_quantity = :pick_quantity]>
        <[AND create_by = :create_by]>
        <[AND create_time = :create_time]>
        <[AND update_by = :update_by]>
        <[AND update_time = :update_time]>
        <[AND pick_status = :pick_status]>
        <[AND sku IN (:pick_sku_list)]>
        <[AND allocation_item_id IN (SELECT allocation_item_id FROM wh_apv_allocation_item WHERE allocation_item_id=T.allocation_item_id AND allocation_id = :allocationId)]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryWhAllocationPickTaskItemByPrimaryKey" >
    <content >
      <![CDATA[
        SELECT id, task_id, allocation_item_id, sku, quantity, pick_quantity, create_by, 
        create_time, update_by, update_time, pick_status,
        (SELECT waai.stock_id FROM  wh_apv_allocation_item waai where waai.allocation_item_id = wh_allocation_pick_task_item.allocation_item_id) AS 'stock_id'
        FROM wh_allocation_pick_task_item
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryWhAllocationPickTaskItem" >
    <content >
      <![CDATA[
        SELECT id, task_id, allocation_item_id, sku, quantity, pick_quantity, create_by, 
        create_time, update_by, update_time, pick_status,
         (SELECT waai.stock_id FROM  wh_apv_allocation_item waai where waai.allocation_item_id = wh_allocation_pick_task_item.allocation_item_id) AS 'stock_id'
        FROM wh_allocation_pick_task_item
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND task_id = :task_id]>
        <[AND allocation_item_id = :allocation_item_id]>
        <[AND sku = :sku]>
        <[AND quantity = :quantity]>
        <[AND pick_quantity = :pick_quantity]>
        <[AND create_by = :create_by]>
        <[AND create_time = :create_time]>
        <[AND update_by = :update_by]>
        <[AND update_time = :update_time]>
        <[AND pick_status = :pick_status]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="createWhAllocationPickTaskItem" >
    <content >
      <![CDATA[
        INSERT INTO wh_allocation_pick_task_item (task_id, allocation_item_id, sku, quantity, pick_quantity, create_by, 
          create_time, update_by, update_time, pick_status)
        VALUES (:task_id, :allocation_item_id, :sku, :quantity, :pick_quantity, :create_by, 
          :create_time, :update_by, :update_time, :pick_status)
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="deleteWhAllocationPickTaskItemByPrimaryKey" >
    <content >
      <![CDATA[
        DELETE FROM wh_allocation_pick_task_item
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="updateWhAllocationPickTaskItemByPrimaryKey" >
    <content >
      <![CDATA[
        UPDATE wh_allocation_pick_task_item
        SET <[task_id = :task_id,]>
          <[allocation_item_id = :allocation_item_id,]>
          <[sku = :sku,]>
          <[quantity = :quantity,]>
          <[pick_quantity = :pick_quantity,]>
          <[create_by = :create_by,]>
          <[create_time = :create_time,]>
          <[update_by = :update_by,]>
          <[update_time = :update_time,]>
          <[pick_status = :pick_status,]>
        id = id
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  
  <sql datasource="dataSource" id="queryWhAllocationPickTaskItemAndTaskCount" >
    <content >
      <![CDATA[
        SELECT COUNT(1)
        FROM wh_allocation_pick_task_item wapti
		LEFT JOIN wh_allocation_pick_task wapt on wapt.task_id = wapti.task_id
		LEFT JOIN wh_sku ws on ws.sku = wapti.sku
        WHERE 1 = 1
        <[AND wapti.id = :id]>
        <[AND wapti.task_id = :task_id]>
        <[AND wapti.allocation_item_id = :allocation_item_id]>
        <[AND wapti.sku = :sku]>
        <[AND wapti.quantity = :quantity]>
        <[AND wapti.pick_quantity = :pick_quantity]>
        <[AND wapti.create_by = :create_by]>
        <[AND wapti.create_time = :create_time]>
        <[AND wapti.update_by = :update_by]>
        <[AND wapti.update_time = :update_time]>
        <[AND wapti.pick_status = :pick_status]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryWhAllocationPickTaskItemAndTaskList" >
    <content >
      <![CDATA[
        SELECT wapti.id, 
			wapti.task_id, 
			wapti.allocation_item_id, 
			wapti.sku, 
			wapti.quantity, 
			wapti.pick_quantity, 
			wapti.create_by, 
       		wapti.create_time, 
			wapti.update_by, 
			wapti.update_time,
			wapti.pick_status,
			wapt.task_no,
			wapt.allocation_no,
			wapt.allocation_id,
			wapt.task_status,
			wapt.print_status,
			wapt.receive_by,
			(SELECT s.location_number FROM wh_stock s LEFT JOIN wh_apv_allocation_item waai ON s.id = waai.stock_id WHERE waai.allocation_item_id = wapti.allocation_item_id) AS 'ws.location_number',
			ws.name,
			ws.warehouse_id
			<[:QUERY_WAREHOUSE]>
       		FROM wh_allocation_pick_task_item wapti
			LEFT JOIN wh_allocation_pick_task wapt on wapt.task_id = wapti.task_id
			LEFT JOIN wh_sku ws on ws.sku = wapti.sku
			<[:LEFT_APV_ALLOCATION]>
	        WHERE 1 = 1
	        <[AND wapti.id = :id]>
	        <[AND wapti.task_id = :task_id]>
	        <[AND wapti.allocation_item_id = :allocation_item_id]>
	        <[AND wapti.sku = :sku]>
	        <[AND wapti.quantity = :quantity]>
	        <[AND wapti.pick_quantity = :pick_quantity]>
	        <[AND wapti.create_by = :create_by]>
	        <[AND wapti.create_time = :create_time]>
	        <[AND wapti.update_by = :update_by]>
	        <[AND wapti.update_time = :update_time]>
	        <[AND wapti.pick_status = :pick_status]>
	        <[AND wapt.task_no = :TASK_NO]>
	        
      ]]>
    </content>
  </sql>
  
  
  <sql datasource="dataSource" id="updatePickTaskItemStatusAndPickQuantity" >
    <content >
      <![CDATA[
        UPDATE wh_allocation_pick_task_item
        SET <[pick_status = :pick_status,]>
          <[pick_quantity = :pick_quantity,]>
          <[update_by = :update_by,]>
          <[update_time = :update_time,]>
        id = id
        WHERE 1 = 1
        AND id = :id
        AND pick_status = :beforStatus
      ]]>
    </content>
  </sql>
</sqlmap>