<?xml version="1.0" encoding="UTF-8" ?>
<sqlmap >
  <sql datasource="dataSource" id="queryAllocateApvLoadItemCount" >
    <content >
      <![CDATA[
        SELECT COUNT(1)
        FROM allocate_apv_load_item
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND task_no = :task_no]>
        <[AND apv_no = :apv_no]>
        <[AND apv_no IN (:apvNoList)]>
        <[AND sku = :sku]>
        <[AND quantity = :quantity]>
        <[AND picke_quantity = :picke_quantity]>
        <[AND sow_quantity = :sow_quantity]>
        <[AND creation_date = :creation_date]>
        <[AND create_by = :create_by]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryAllocateApvLoadItemList" >
    <content >
      <![CDATA[
        SELECT id, task_no, apv_no, sku, quantity, picke_quantity, sow_quantity, creation_date, 
        create_by
        FROM allocate_apv_load_item
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND task_no = :task_no]>
        <[AND apv_no = :apv_no]>
        <[AND apv_no IN (:apvNoList)]>
        <[AND sku = :sku]>
        <[AND quantity = :quantity]>
        <[AND picke_quantity = :picke_quantity]>
        <[AND sow_quantity = :sow_quantity]>
        <[AND creation_date = :creation_date]>
        <[AND create_by = :create_by]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryAllocateApvLoadItemByPrimaryKey" >
    <content >
      <![CDATA[
        SELECT id, task_no, apv_no, sku, quantity, picke_quantity, sow_quantity, creation_date, 
        create_by
        FROM allocate_apv_load_item
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryAllocateApvLoadItem" >
    <content >
      <![CDATA[
        SELECT id, task_no, apv_no, sku, quantity, picke_quantity, sow_quantity, creation_date, 
        create_by
        FROM allocate_apv_load_item
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND task_no = :task_no]>
        <[AND apv_no = :apv_no]>
        <[AND sku = :sku]>
        <[AND quantity = :quantity]>
        <[AND picke_quantity = :picke_quantity]>
        <[AND sow_quantity = :sow_quantity]>
        <[AND creation_date = :creation_date]>
        <[AND create_by = :create_by]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="createAllocateApvLoadItem" >
    <content >
      <![CDATA[
        INSERT INTO allocate_apv_load_item (task_no, apv_no, sku, quantity, picke_quantity, sow_quantity, creation_date, 
          create_by)
        VALUES (:task_no, :apv_no, :sku, :quantity, :picke_quantity, :sow_quantity, :creation_date, 
          :create_by)
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="deleteAllocateApvLoadItemByPrimaryKey" >
    <content >
      <![CDATA[
        DELETE FROM allocate_apv_load_item
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="updateAllocateApvLoadItemByPrimaryKey" >
    <content >
      <![CDATA[
        UPDATE allocate_apv_load_item
        SET <[task_no = :task_no,]>
          <[apv_no = :apv_no,]>
          <[sku = :sku,]>
          <[quantity = :quantity,]>
          <[picke_quantity = :picke_quantity,]>
          <[sow_quantity = :sow_quantity,]>
          <[creation_date = :creation_date,]>
          <[create_by = :create_by,]>
        id = id
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
</sqlmap>