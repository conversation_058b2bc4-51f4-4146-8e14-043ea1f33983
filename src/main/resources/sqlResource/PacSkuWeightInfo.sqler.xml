<?xml version="1.0" encoding="UTF-8" ?>
<sqlmap >
  <sql datasource="dataSource" id="queryPacSkuWeightInfoCount" >
    <content >
      <![CDATA[
        SELECT COUNT(1)
        FROM pac_sku_weight_info
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND id IN (:ids)]>
        <[AND item_id = :item_id]>
        <[AND item_id IN (:itemIds)]>
        <[AND item_code = :item_code]>
        <[AND item_code IN (:itemCodeList)]>
        <[AND owner_user_id = :owner_user_id]>
        <[AND weight = :weight]>
        <[AND net_weight = :net_weight]>
        <[AND length = :length]>
        <[AND width = :width]>
        <[AND height = :height]>
        <[AND volume = :volume]>
        <[AND create_time = :create_time]>
        <[AND update_time = :update_time]>
        <[AND send_time = :send_time]>
        <[AND send_status = :send_status]>
        <[AND send_status != :notStatus]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryPacSkuWeightInfoList" >
    <content >
      <![CDATA[
        SELECT id, item_id, item_code, owner_user_id, weight, net_weight, length, width, 
        height, volume, create_time, update_time, send_time, send_status
        FROM pac_sku_weight_info
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND id IN (:ids)]>
        <[AND item_id = :item_id]>
        <[AND item_id IN (:itemIds)]>
        <[AND item_code = :item_code]>
        <[AND item_code IN (:itemCodeList)]>
        <[AND owner_user_id = :owner_user_id]>
        <[AND weight = :weight]>
        <[AND net_weight = :net_weight]>
        <[AND length = :length]>
        <[AND width = :width]>
        <[AND height = :height]>
        <[AND volume = :volume]>
        <[AND create_time = :create_time]>
        <[AND update_time = :update_time]>
        <[AND send_time = :send_time]>
        <[AND send_status = :send_status]>
        <[AND send_status != :notStatus]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryPacSkuWeightInfoByPrimaryKey" >
    <content >
      <![CDATA[
        SELECT id, item_id, item_code, owner_user_id, weight, net_weight, length, width, 
        height, volume, create_time, update_time, send_time, send_status
        FROM pac_sku_weight_info
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryPacSkuWeightInfo" >
    <content >
      <![CDATA[
        SELECT id, item_id, item_code, owner_user_id, weight, net_weight, length, width, 
        height, volume, create_time, update_time, send_time, send_status
        FROM pac_sku_weight_info
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND id IN (:ids)]>
        <[AND item_id = :item_id]>
        <[AND item_id IN (:itemIds)]>
        <[AND item_code = :item_code]>
        <[AND item_code IN (:itemCodeList)]>
        <[AND owner_user_id = :owner_user_id]>
        <[AND weight = :weight]>
        <[AND net_weight = :net_weight]>
        <[AND length = :length]>
        <[AND width = :width]>
        <[AND height = :height]>
        <[AND volume = :volume]>
        <[AND create_time = :create_time]>
        <[AND update_time = :update_time]>
        <[AND send_time = :send_time]>
        <[AND send_status = :send_status]>
        <[AND send_status != :notStatus]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="createPacSkuWeightInfo" >
    <content >
      <![CDATA[
        INSERT INTO pac_sku_weight_info (item_id, item_code, owner_user_id, weight, net_weight, length, width, 
          height, volume, create_time, update_time, send_time, send_status)
        VALUES (:item_id, :item_code, :owner_user_id, :weight, :net_weight, :length, :width, 
          :height, :volume, :create_time, :update_time, :send_time, :send_status)
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="deletePacSkuWeightInfoByPrimaryKey" >
    <content >
      <![CDATA[
        DELETE FROM pac_sku_weight_info
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="updatePacSkuWeightInfoByPrimaryKey" >
    <content >
      <![CDATA[
        UPDATE pac_sku_weight_info
        SET <[item_id = :item_id,]>
          <[item_code = :item_code,]>
          <[owner_user_id = :owner_user_id,]>
          <[weight = :weight,]>
          <[net_weight = :net_weight,]>
          <[length = :length,]>
          <[width = :width,]>
          <[height = :height,]>
          <[volume = :volume,]>
          <[create_time = :create_time,]>
          <[update_time = :update_time,]>
          <[send_time = :send_time,]>
          <[send_status = :send_status,]>
        id = id
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>

  <sql datasource="dataSource" id="getIdByQuery" >
    <content >
      <![CDATA[
        SELECT id
        FROM pac_sku_weight_info
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND id IN (:ids)]>
        <[AND item_id = :item_id]>
        <[AND item_id IN (:itemIds)]>
        <[AND item_code = :item_code]>
        <[AND item_code IN (:itemCodeList)]>
        <[AND owner_user_id = :owner_user_id]>
        <[AND weight = :weight]>
        <[AND net_weight = :net_weight]>
        <[AND length = :length]>
        <[AND width = :width]>
        <[AND height = :height]>
        <[AND volume = :volume]>
        <[AND create_time = :create_time]>
        <[AND update_time = :update_time]>
        <[AND send_time = :send_time]>
        <[AND send_status = :send_status]>
        <[AND send_status NOT IN (:notStatus)]>
      ]]>
    </content>
  </sql>
</sqlmap>