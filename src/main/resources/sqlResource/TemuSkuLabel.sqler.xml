<?xml version="1.0" encoding="UTF-8" ?>
<sqlmap >
  <sql datasource="dataSource" id="queryTemuSkuLabelCount" >
    <content >
      <![CDATA[
        SELECT COUNT(1)
        FROM temu_sku_label
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND sku = :sku]>
        <[AND product_skc_id = :product_skc_id]>
        <[AND product_sku_id = :product_sku_id]>
        <[AND spec_name1 = :spec_name1]>
        <[AND spec_name2 = :spec_name2]>
        <[AND label_code = :label_code]>
        <[AND product_origin = :product_origin]>
        <[AND sku IN (:sku_list)]>
        <[AND product_sku_id IN (:sku_id_list)]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryTemuSkuLabelList" >
    <content >
      <![CDATA[
        SELECT id, sku, product_skc_id, product_sku_id, spec_name1, spec_name2, label_code, 
        product_origin
        FROM temu_sku_label
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND sku = :sku]>
        <[AND product_skc_id = :product_skc_id]>
        <[AND product_sku_id = :product_sku_id]>
        <[AND spec_name1 = :spec_name1]>
        <[AND spec_name2 = :spec_name2]>
        <[AND label_code = :label_code]>
        <[AND product_origin = :product_origin]>
        <[AND sku IN (:sku_list)]>
        <[AND product_sku_id IN (:sku_id_list)]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryTemuSkuLabelByPrimaryKey" >
    <content >
      <![CDATA[
        SELECT id, sku, product_skc_id, product_sku_id, spec_name1, spec_name2, label_code, 
        product_origin
        FROM temu_sku_label
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryTemuSkuLabel" >
    <content >
      <![CDATA[
        SELECT id, sku, product_skc_id, product_sku_id, spec_name1, spec_name2, label_code, 
        product_origin
        FROM temu_sku_label
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND sku = :sku]>
        <[AND product_skc_id = :product_skc_id]>
        <[AND product_sku_id = :product_sku_id]>
        <[AND spec_name1 = :spec_name1]>
        <[AND spec_name2 = :spec_name2]>
        <[AND label_code = :label_code]>
        <[AND product_origin = :product_origin]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="createTemuSkuLabel" >
    <content >
      <![CDATA[
        INSERT INTO temu_sku_label (sku, product_skc_id, product_sku_id, spec_name1, spec_name2, label_code, 
          product_origin)
        VALUES (:sku, :product_skc_id, :product_sku_id, :spec_name1, :spec_name2, :label_code, 
          :product_origin)
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="deleteTemuSkuLabelByPrimaryKey" >
    <content >
      <![CDATA[
        DELETE FROM temu_sku_label
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="updateTemuSkuLabelByPrimaryKey" >
    <content >
      <![CDATA[
        UPDATE temu_sku_label
        SET <[sku = :sku,]>
          <[product_skc_id = :product_skc_id,]>
          <[product_sku_id = :product_sku_id,]>
          <[spec_name1 = :spec_name1,]>
          <[spec_name2 = :spec_name2,]>
          <[label_code = :label_code,]>
          <[product_origin = :product_origin,]>
        id = id
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
</sqlmap>