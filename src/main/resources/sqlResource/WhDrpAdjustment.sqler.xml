<?xml version="1.0" encoding="UTF-8" ?>
<sqlmap >
  <sql datasource="dataSource" id="queryWhDrpAdjustmentCount" >
    <content >
      <![CDATA[
        SELECT COUNT(1)
        FROM wh_drp_adjustment
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND id IN (:idList)]>
        <[AND sku = :sku]>
        <[AND sku IN (:skuList)]>
        <[AND store_code = :store_code]>
        <[AND adjust_money = :adjust_money]>
        <[AND adjust_type = :adjust_type]>
        <[AND adjust_remark = :adjust_remark]>
        <[AND adjust_time >= :from_adjust_time]>
        <[AND adjust_time <= :to_adjust_time]>
        <[AND create_time >= :from_create_time]>
        <[AND create_time <= :to_create_time]>
        <[AND create_time = :create_time]>
        <[AND update_time = :update_time]>
        <[:QUERY_NOT_ADJ]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryWhDrpAdjustmentList" >
    <content >
      <![CDATA[
        SELECT id, sku, store_code, adjust_money, adjust_type, adjust_remark, adjust_time, 
        create_time, update_time
        FROM wh_drp_adjustment
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND id IN (:idList)]>
        <[AND sku = :sku]>
        <[AND sku IN (:skuList)]>
        <[AND store_code = :store_code]>
        <[AND store_code IN (:storeCodeList)]>
        <[AND adjust_money = :adjust_money]>
        <[AND adjust_type = :adjust_type]>
        <[AND adjust_remark = :adjust_remark]>
        <[AND adjust_time >= :from_adjust_time]>
        <[AND adjust_time <= :to_adjust_time]>
        <[AND create_time >= :from_create_time]>
        <[AND create_time <= :to_create_time]>
        <[AND create_time = :create_time]>
        <[AND update_time = :update_time]>
        <[:QUERY_NOT_ADJ]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryWhDrpAdjustmentByPrimaryKey" >
    <content >
      <![CDATA[
        SELECT id, sku, store_code, adjust_money, adjust_type, adjust_remark, adjust_time, 
        create_time, update_time
        FROM wh_drp_adjustment
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryWhDrpAdjustment" >
    <content >
      <![CDATA[
        SELECT id, sku, store_code, adjust_money, adjust_type, adjust_remark, adjust_time, 
        create_time, update_time
        FROM wh_drp_adjustment
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND sku = :sku]>
        <[AND store_code = :store_code]>
        <[AND adjust_money = :adjust_money]>
        <[AND adjust_type = :adjust_type]>
        <[AND adjust_remark = :adjust_remark]>
        <[AND adjust_time = :adjust_time]>
        <[AND create_time = :create_time]>
        <[AND update_time = :update_time]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="createWhDrpAdjustment" >
    <content >
      <![CDATA[
        INSERT INTO wh_drp_adjustment (sku, store_code, adjust_money, adjust_type, adjust_remark, adjust_time, 
          create_time, update_time)
        VALUES (:sku, :store_code, :adjust_money, :adjust_type, :adjust_remark, :adjust_time, 
          :create_time, :update_time)
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="deleteWhDrpAdjustmentByPrimaryKey" >
    <content >
      <![CDATA[
        DELETE FROM wh_drp_adjustment
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="updateWhDrpAdjustmentByPrimaryKey" >
    <content >
      <![CDATA[
        UPDATE wh_drp_adjustment
        SET <[sku = :sku,]>
          <[store_code = :store_code,]>
          <[adjust_money = :adjust_money,]>
          <[adjust_type = :adjust_type,]>
          <[adjust_remark = :adjust_remark,]>
          <[adjust_time = :adjust_time,]>
          <[create_time = :create_time,]>
          <[update_time = :update_time,]>
        id = id
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
</sqlmap>