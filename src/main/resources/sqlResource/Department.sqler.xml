<?xml version="1.0" encoding="UTF-8" ?>
<sqlmap >
  <sql datasource="dataSource" id="queryDepartmentCount" >
    <content >
      <![CDATA[
        SELECT COUNT(1)
        FROM t_department
        WHERE 1 = 1
        <[AND department_id = :department_id]>
        <[AND department_name = :department_name]>
        <[AND department_code = :department_code]>
        <[AND parent_id = :parent_id]>
        <[AND level = :level]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryDepartmentList" >
    <content >
      <![CDATA[
        SELECT department_id, department_name, department_code, parent_id, level
        FROM t_department
        WHERE 1 = 1
        <[AND department_id = :department_id]>
        <[AND department_name = :department_name]>
        <[AND department_code = :department_code]>
        <[AND parent_id = :parent_id]>
        <[AND level = :level]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryDepartmentByPrimaryKey" >
    <content >
      <![CDATA[
        SELECT department_id, department_name, department_code, parent_id, level
        FROM t_department
        WHERE 1 = 1
        AND department_id = :department_id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryDepartment" >
    <content >
      <![CDATA[
        SELECT department_id, department_name, department_code, parent_id, level
        FROM t_department
        WHERE 1 = 1
        <[AND department_id = :department_id]>
        <[AND department_name = :department_name]>
        <[AND department_code = :department_code]>
        <[AND parent_id = :parent_id]>
        <[AND level = :level]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="createDepartment" >
    <content >
      <![CDATA[
        INSERT INTO t_department (department_name, department_code, parent_id, level)
        VALUES (:department_name, :department_code, :parent_id, :level)
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="deleteDepartmentByPrimaryKey" >
    <content >
      <![CDATA[
        DELETE FROM t_department
        WHERE 1 = 1
        AND department_id = :department_id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="updateDepartmentByPrimaryKey" >
    <content >
      <![CDATA[
        UPDATE t_department
        SET <[department_name = :department_name,]>
          <[department_code = :department_code,]>
          <[parent_id = :parent_id,]>
          <[level = :level,]>
        department_id = department_id
        WHERE 1 = 1
        AND department_id = :department_id
      ]]>
    </content>
  </sql>
</sqlmap>