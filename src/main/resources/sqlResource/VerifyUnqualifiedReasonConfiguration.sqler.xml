<?xml version="1.0" encoding="UTF-8" ?>
<sqlmap >
  <sql datasource="dataSource" id="queryVerifyUnqualifiedReasonConfigurationCount" >
    <content >
      <![CDATA[
        SELECT COUNT(1)
        FROM verify_unqualified_reason_configuration
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND reason = :reason]>
        <[AND creation_by = :creation_by]>
        <[AND creation_date = :creation_date]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryVerifyUnqualifiedReasonConfigurationList" >
    <content >
      <![CDATA[
        SELECT id, reason, creation_by, creation_date
        FROM verify_unqualified_reason_configuration
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND reason = :reason]>
        <[AND creation_by = :creation_by]>
        <[AND creation_date = :creation_date]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryVerifyUnqualifiedReasonConfigurationByPrimaryKey" >
    <content >
      <![CDATA[
        SELECT id, reason, creation_by, creation_date
        FROM verify_unqualified_reason_configuration
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryVerifyUnqualifiedReasonConfiguration" >
    <content >
      <![CDATA[
        SELECT id, reason, creation_by, creation_date
        FROM verify_unqualified_reason_configuration
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND reason = :reason]>
        <[AND creation_by = :creation_by]>
        <[AND creation_date = :creation_date]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="createVerifyUnqualifiedReasonConfiguration" >
    <content >
      <![CDATA[
        INSERT INTO verify_unqualified_reason_configuration (reason, creation_by, creation_date)
        VALUES (:reason, :creation_by, :creation_date)
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="deleteVerifyUnqualifiedReasonConfigurationByPrimaryKey" >
    <content >
      <![CDATA[
        DELETE FROM verify_unqualified_reason_configuration
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="updateVerifyUnqualifiedReasonConfigurationByPrimaryKey" >
    <content >
      <![CDATA[
        UPDATE verify_unqualified_reason_configuration
        SET <[reason = :reason,]>
          <[creation_by = :creation_by,]>
          <[creation_date = :creation_date,]>
        id = id
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
</sqlmap>