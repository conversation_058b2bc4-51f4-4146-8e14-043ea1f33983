<?xml version="1.0" encoding="UTF-8" ?>
<sqlmap >
  <sql datasource="dataSource" id="querySmtOmsOrderCount" >
    <content >
      <![CDATA[
        SELECT COUNT(1)
        FROM smt_oms_order
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND order_type = :order_type]>
        <[AND account_number = :account_number]>
        <[AND platform_order = :platform_order]>
        <[AND sku = :sku]>
        <[AND quantity = :quantity]>
        <[AND pay_time = :pay_time]>
        <[AND deliver_time = :deliver_time]>
        <[AND create_time = :create_time]>
        <[AND status_name = :status_name]>
        <[AND deliver_time >= :fromDeliveryDate]>
        <[AND deliver_time <= :toDeliveryDate]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="querySmtOmsOrderList" >
    <content >
      <![CDATA[
        SELECT id, order_type, account_number, platform_order, sku, quantity, pay_time, deliver_time, status_name,
        create_time
        FROM smt_oms_order
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND order_type = :order_type]>
        <[AND account_number = :account_number]>
        <[AND platform_order = :platform_order]>
        <[AND sku = :sku]>
        <[AND quantity = :quantity]>
        <[AND pay_time = :pay_time]>
        <[AND deliver_time = :deliver_time]>
        <[AND create_time = :create_time]>
        <[AND status_name = :status_name]>
        <[AND deliver_time >= :fromDeliveryDate]>
        <[AND deliver_time <= :toDeliveryDate]>
        <[AND sku IN (:skuList)]>
        <[AND account_number IN (:accountNumberList)]>
        <[AND platform_order IN (:platformOrderList)]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="querySmtOmsOrderByPrimaryKey" >
    <content >
      <![CDATA[
        SELECT id, order_type, account_number, platform_order, sku, quantity, pay_time, deliver_time, status_name,
        create_time
        FROM smt_oms_order
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="querySmtOmsOrder" >
    <content >
      <![CDATA[
        SELECT id, order_type, account_number, platform_order, sku, quantity, pay_time, deliver_time, status_name,
        create_time
        FROM smt_oms_order
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND order_type = :order_type]>
        <[AND account_number = :account_number]>
        <[AND platform_order = :platform_order]>
        <[AND sku = :sku]>
        <[AND status_name = :status_name]>
        <[AND quantity = :quantity]>
        <[AND pay_time = :pay_time]>
        <[AND deliver_time = :deliver_time]>
        <[AND create_time = :create_time]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="createSmtOmsOrder" >
    <content >
      <![CDATA[
        INSERT INTO smt_oms_order (order_type, account_number, platform_order, sku, quantity, pay_time, 
          deliver_time, create_time,status_name)
        VALUES (:order_type, :account_number, :platform_order, :sku, :quantity, :pay_time, 
          :deliver_time, :create_time,:status_name)
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="deleteSmtOmsOrderByPrimaryKey" >
    <content >
      <![CDATA[
        DELETE FROM smt_oms_order
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="updateSmtOmsOrderByPrimaryKey" >
    <content >
      <![CDATA[
        UPDATE smt_oms_order
        SET <[order_type = :order_type,]>
          <[account_number = :account_number,]>
          <[platform_order = :platform_order,]>
          <[sku = :sku,]>
          <[quantity = :quantity,]>
          <[pay_time = :pay_time,]>
          <[deliver_time = :deliver_time,]>
          <[create_time = :create_time,]>
          <[status_name = :status_name,]>
        id = id
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
</sqlmap>