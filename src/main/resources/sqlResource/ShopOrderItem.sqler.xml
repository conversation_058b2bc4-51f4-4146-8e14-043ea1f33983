<?xml version="1.0" encoding="UTF-8" ?>
<sqlmap >
  <sql datasource="dataSource" id="queryShopOrderItemCount" >
    <content >
      <![CDATA[
        SELECT COUNT(1)
        FROM shop_order_item
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND order_id = :order_id]>
        <[AND sku = :sku]>
        <[AND image_url = :image_url]>
        <[AND name = :name]>
        <[AND discounted_price = :discounted_price]>
        <[AND cost_price = :cost_price]>
        <[AND quantity = :quantity]>
        <[AND allot_qty = :allot_qty]>
        <[AND pick_qty = :pick_qty]>
        <[AND grid_qty = :grid_qty]>
        <[AND location = :location]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryShopOrderItemList" >
    <content >
      <![CDATA[
        SELECT id, order_id, sku, image_url, name, discounted_price, cost_price, quantity, 
        allot_qty, pick_qty, grid_qty,location
        FROM shop_order_item
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND order_id = :order_id]>
        <[AND sku = :sku]>
        <[AND image_url = :image_url]>
        <[AND name = :name]>
        <[AND discounted_price = :discounted_price]>
        <[AND cost_price = :cost_price]>
        <[AND quantity = :quantity]>
        <[AND allot_qty = :allot_qty]>
        <[AND pick_qty = :pick_qty]>
        <[AND grid_qty = :grid_qty]>
        <[AND location = :location]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryShopOrderItemByPrimaryKey" >
    <content >
      <![CDATA[
        SELECT id, order_id, sku, image_url, name, discounted_price, cost_price, quantity, 
        allot_qty, pick_qty, grid_qty, location
        FROM shop_order_item
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryShopOrderItem" >
    <content >
      <![CDATA[
        SELECT id, order_id, sku, image_url, name, discounted_price, cost_price, quantity, 
        allot_qty, pick_qty, grid_qty, location
        FROM shop_order_item
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND order_id = :order_id]>
        <[AND sku = :sku]>
        <[AND image_url = :image_url]>
        <[AND name = :name]>
        <[AND discounted_price = :discounted_price]>
        <[AND cost_price = :cost_price]>
        <[AND quantity = :quantity]>
        <[AND allot_qty = :allot_qty]>
        <[AND pick_qty = :pick_qty]>
        <[AND grid_qty = :grid_qty]>
        <[AND location = :location]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="createShopOrderItem" >
    <content >
      <![CDATA[
        INSERT INTO shop_order_item (order_id, sku, image_url, name, discounted_price, cost_price, quantity, 
          allot_qty, pick_qty, grid_qty, location)
        VALUES (:order_id, :sku, :image_url, :name, :discounted_price, :cost_price, :quantity, 
          :allot_qty, :pick_qty, :grid_qty, :location)
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="deleteShopOrderItemByPrimaryKey" >
    <content >
      <![CDATA[
        DELETE FROM shop_order_item
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="updateShopOrderItemByPrimaryKey" >
    <content >
      <![CDATA[
        UPDATE shop_order_item
        SET <[order_id = :order_id,]>
          <[sku = :sku,]>
          <[image_url = :image_url,]>
          <[name = :name,]>
          <[discounted_price = :discounted_price,]>
          <[cost_price = :cost_price,]>
          <[quantity = :quantity,]>
          <[allot_qty = :allot_qty,]>
          <[pick_qty = :pick_qty,]>
          <[grid_qty = :grid_qty,]>
          <[location = :location,]>
          <[remark = :remark,]>
        id = id
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>

  <sql datasource="dataSource" id="updateShopOrderItemByOrderIdAndSku" >
    <content >
      <![CDATA[
        UPDATE shop_order_item
        SET <[order_id = :order_id,]>
          <[sku = :sku,]>
          <[image_url = :image_url,]>
          <[name = :name,]>
          <[discounted_price = :discounted_price,]>
          <[cost_price = :cost_price,]>
          <[quantity = :quantity,]>
          <[allot_qty = :allot_qty,]>
          <[pick_qty = :pick_qty,]>
          <[grid_qty = :grid_qty,]>
          <[location = :location,]>
        id = id
        WHERE 1 = 1
        AND order_id = :order_id
        AND sku = :sku
      ]]>
    </content>
  </sql>
</sqlmap>