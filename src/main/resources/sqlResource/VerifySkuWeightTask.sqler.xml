<?xml version="1.0" encoding="UTF-8" ?>
<sqlmap >
  <sql datasource="dataSource" id="queryVerifySkuWeightTaskCount" >
    <content >
      <![CDATA[
        SELECT COUNT(1)
        FROM verify_sku_weight_task
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND id IN (:taskIds)]>
        <[AND task_no = :task_no]>
        <[AND task_status = :task_status]>
        <[AND task.task_status IN (:task_status_list)]>
        <[AND task_level = :task_level]>
        <[AND created_by = :created_by]>
        <[AND created_date >= :from_created_date]>
        <[AND created_date <= :to_created_date]>
        <[AND receive_by = :receive_by]>
        <[AND receive_date >= :from_receive_date]>
        <[AND receive_date <= :to_receive_date]>
        <[AND finish_verify_by = :finish_verify_by]>
        <[AND finish_verify_date >= :from_finish_verify_date]>
        <[AND finish_verify_date <= :to_finish_verify_date]>
        <[AND last_update_date = :last_update_date]>
        <[AND last_update_by = :last_update_by]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryVerifySkuWeightTaskList" >
    <content >
      <![CDATA[
        SELECT id, task_no, task_status, task_level, created_by, created_date, receive_by, 
        receive_date, last_update_date, last_update_by, finish_verify_date, finish_verify_by
        FROM verify_sku_weight_task
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND id IN (:taskIds)]>
        <[AND task_no = :task_no]>
        <[AND task_status = :task_status]>
        <[AND task.task_status IN (:task_status_list)]>
        <[AND task_level = :task_level]>
        <[AND created_by = :created_by]>
        <[AND created_date >= :from_created_date]>
        <[AND created_date <= :to_created_date]>
        <[AND receive_by = :receive_by]>
        <[AND receive_date >= :from_receive_date]>
        <[AND receive_date <= :to_receive_date]>
        <[AND finish_verify_by = :finish_verify_by]>
        <[AND finish_verify_date >= :from_finish_verify_date]>
        <[AND finish_verify_date <= :to_finish_verify_date]>
        <[AND last_update_date = :last_update_date]>
        <[AND last_update_by = :last_update_by]>
      ]]>
    </content>
  </sql>

  <sql datasource="dataSource" id="queryVerifySkuWeightTaskAndItemCount" >
    <content >
      <![CDATA[
        SELECT COUNT(1)
        FROM verify_sku_weight_task task
        WHERE 1 = 1
        <[AND task.id = :id]>
        <[AND id IN (:taskIds)]>
        <[AND task.task_no = :task_no]>
        <[AND task.task_status = :task_status]>
        <[AND task.task_status IN (:task_status_list)]>
        <[AND task.task_level = :task_level]>
        <[AND task.created_by = :created_by]>
        <[AND task.created_date >= :from_created_date]>
        <[AND task.created_date <= :to_created_date]>
        <[AND task.receive_by = :receive_by]>
        <[AND task.receive_date >= :from_receive_date]>
        <[AND task.receive_date <= :to_receive_date]>
        <[AND task.finish_verify_by = :finish_verify_by]>
        <[AND task.finish_verify_date >= :from_finish_verify_date]>
        <[AND task.finish_verify_date <= :to_finish_verify_date]>
        <[AND task.last_update_date = :last_update_date]>
        <[AND task.last_update_by = :last_update_by]>
        <[AND task.id IN (SELECT i.task_id from verify_sku_weight_task_item i WHERE i.sku = :sku)]>
	    <[AND task.id IN (SELECT i.task_id from verify_sku_weight_task_item i WHERE i.sku in (:sku_list))]>
      ]]>
    </content>
  </sql>

  <sql datasource="dataSource" id="queryVerifySkuWeightTaskAndItemList" >
    <content >
      <![CDATA[
        SELECT task.id, task.task_no, task.task_status, task.task_level, task.created_by, task.created_date, task.receive_by,
        task.receive_date, task.last_update_date, task.last_update_by, task.finish_verify_date, task.finish_verify_by,
        item.id, item.task_id, item.sku, item.status, item.weight, item.weighing_weight, item.weight_difference,
        item.created_by, item.created_date, item.last_update_date, item.last_update_by, item.weighing_date, item.weighing_by,
        item.specification,item.packaging_attribute, sku.id, sku.name, sku.location_number
        FROM verify_sku_weight_task task
        LEFT JOIN verify_sku_weight_task_item item ON item.task_id = task.id
        LEFT JOIN wh_sku sku ON sku.sku = item.sku
        INNER JOIN (
        	SELECT t.id
        	FROM verify_sku_weight_task t
	        WHERE 1 = 1
            <[AND t.id = :id]>
            <[AND id IN (:taskIds)]>
            <[AND t.task_no = :task_no]>
            <[AND t.task_status = :task_status]>
            <[AND t.task_status IN (:task_status_list)]>
            <[AND t.task_level = :task_level]>
            <[AND t.created_by = :created_by]>
            <[AND t.created_date >= :from_created_date]>
            <[AND t.created_date <= :to_created_date]>
            <[AND t.receive_by = :receive_by]>
            <[AND t.receive_date >= :from_receive_date]>
            <[AND t.receive_date <= :to_receive_date]>
            <[AND t.finish_verify_by = :finish_verify_by]>
            <[AND t.finish_verify_date >= :from_finish_verify_date]>
            <[AND t.finish_verify_date <= :to_finish_verify_date]>
            <[AND t.last_update_date = :last_update_date]>
            <[AND t.last_update_by = :last_update_by]>

	        <[AND t.id IN (SELECT i.task_id from verify_sku_weight_task_item i WHERE i.sku = :sku)]>
	        <[AND t.id IN (SELECT i.task_id from verify_sku_weight_task_item i WHERE i.sku in (:sku_list))]>
            ORDER BY t.task_level DESC, t.id DESC
	        <[:LIMIT]>
	     ) temp on temp.id = task.id
	     WHERE 1 = 1
	     <[:ORDER_BY]>
      ]]>
    </content>
  </sql>

  <sql datasource="dataSource" id="queryVerifySkuWeightTaskByPrimaryKey" >
    <content >
      <![CDATA[
        SELECT id, task_no, task_status, task_level, created_by, created_date, receive_by, 
        receive_date, last_update_date, last_update_by, finish_verify_date, finish_verify_by
        FROM verify_sku_weight_task
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryVerifySkuWeightTask" >
    <content >
      <![CDATA[
        SELECT id, task_no, task_status, task_level, created_by, created_date, receive_by, 
        receive_date, last_update_date, last_update_by, finish_verify_date, finish_verify_by
        FROM verify_sku_weight_task
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND task_no = :task_no]>
        <[AND task_status = :task_status]>
        <[AND task.task_status IN (:task_status_list)]>
        <[AND task_level = :task_level]>
        <[AND created_by = :created_by]>
        <[AND created_date = :created_date]>
        <[AND receive_by = :receive_by]>
        <[AND receive_date = :receive_date]>
        <[AND last_update_date = :last_update_date]>
        <[AND last_update_by = :last_update_by]>
        <[AND finish_verify_date = :finish_verify_date]>
        <[AND finish_verify_by = :finish_verify_by]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="createVerifySkuWeightTask" >
    <content >
      <![CDATA[
        INSERT INTO verify_sku_weight_task (task_no, task_status, task_level, created_by, created_date, receive_by, 
          receive_date, last_update_date, last_update_by, finish_verify_date, finish_verify_by)
        VALUES (:task_no, :task_status, :task_level, :created_by, :created_date, :receive_by, 
          :receive_date, :last_update_date, :last_update_by, :finish_verify_date, :finish_verify_by)
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="deleteVerifySkuWeightTaskByPrimaryKey" >
    <content >
      <![CDATA[
        DELETE FROM verify_sku_weight_task
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="updateVerifySkuWeightTaskByPrimaryKey" >
    <content >
      <![CDATA[
        UPDATE verify_sku_weight_task
        SET <[task_no = :task_no,]>
          <[task_status = :task_status,]>
          <[task_level = :task_level,]>
          <[created_by = :created_by,]>
          <[created_date = :created_date,]>
          <[receive_by = :receive_by,]>
          <[receive_date = :receive_date,]>
          <[last_update_date = :last_update_date,]>
          <[last_update_by = :last_update_by,]>
          <[finish_verify_date = :finish_verify_date,]>
          <[finish_verify_by = :finish_verify_by,]>
        id = id
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  <sql datasource="dataSource" id="queryVerifySkuWeightTaskMaxLevel" >
    <content >
      <![CDATA[
        SELECT MAX(task_level)
        FROM verify_sku_weight_task
      ]]>
    </content>
  </sql>
</sqlmap>