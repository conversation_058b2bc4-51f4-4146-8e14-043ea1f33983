<?xml version="1.0" encoding="UTF-8" ?>
<sqlmap >
  <sql datasource="dataSource" id="queryOwnerUserChangeCount" >
    <content >
      <![CDATA[
        SELECT
            count( ouc.id )
        FROM
            owner_user_change ouc
        WHERE 1 = 1
        <[AND ouc.id = :id]>
        <[AND ouc.old_owner_user_id = :old_owner_user_id]>
        <[AND ouc.new_owner_user_id = :new_owner_user_id]>
        <[AND ouc.sku_num = :sku_num]>
        <[AND ouc.total = :total]>
        <[AND ouc.stock_type in (:typeList)]>
        <[AND ouc.create_by = :create_by]>
        <[AND ouc.creation_date >= :from_create_date]>
        <[AND ouc.creation_date <= :to_create_date]>

        <[AND ouc.last_updated_by = :last_updated_by]>
        <[AND ouc.last_update_date = :last_update_date]>

        <[AND ouc.id in (:ids)]>

        <[AND ouc.id IN ( SELECT owner_user_change_id FROM owner_user_change_item WHERE sku = :sku ) ]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryOwnerUserChangeList" >
    <content >
      <![CDATA[
        SELECT
            ouc.id,
            ouc.old_owner_user_id,
            ouc.new_owner_user_id,
            ouc.sku_num,
            ouc.total,
            ouc.stock_type,
            ouc.create_by,
            ouc.creation_date,
            ouc.last_updated_by,
            ouc.last_update_date
        FROM
            owner_user_change ouc

        WHERE 1 = 1
        <[AND ouc.id = :id]>
        <[AND ouc.old_owner_user_id = :old_owner_user_id]>
        <[AND ouc.new_owner_user_id = :new_owner_user_id]>
        <[AND ouc.sku_num = :sku_num]>
        <[AND ouc.total = :total]>
        <[AND ouc.stock_type in (:typeList)]>
        <[AND ouc.create_by = :create_by]>
        <[AND ouc.creation_date >= :from_create_date]>
        <[AND ouc.creation_date <= :to_create_date]>

        <[AND ouc.last_updated_by = :last_updated_by]>
        <[AND ouc.last_update_date = :last_update_date]>
        <[AND ouc.id in (:ids)]>

        <[AND ouc.id IN ( SELECT owner_user_change_id FROM owner_user_change_item WHERE sku = :sku ) ]>

        ORDER BY creation_date DESC
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryOwnerUserChangeByPrimaryKey" >
    <content >
      <![CDATA[
        SELECT id, old_owner_user_id, new_owner_user_id, sku_num, total, stock_type, create_by,
        creation_date, last_updated_by, last_update_date
        FROM owner_user_change
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryOwnerUserChange" >
    <content >
      <![CDATA[
        SELECT id, old_owner_user_id, new_owner_user_id, sku_num, total, stock_type, create_by,
        creation_date, last_updated_by, last_update_date
        FROM owner_user_change
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND old_owner_user_id = :old_owner_user_id]>
        <[AND new_owner_user_id = :new_owner_user_id]>
        <[AND sku_num = :sku_num]>
        <[AND total = :total]>
        <[AND stock_type = :stock_type]>
        <[AND create_by = :create_by]>
        <[AND creation_date = :creation_date]>
        <[AND last_updated_by = :last_updated_by]>
        <[AND last_update_date = :last_update_date]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="createOwnerUserChange" >
    <content >
      <![CDATA[
        INSERT INTO owner_user_change (old_owner_user_id, new_owner_user_id, sku_num, total, stock_type, create_by,
          creation_date, last_updated_by, last_update_date)
        VALUES (:old_owner_user_id, :new_owner_user_id, :sku_num, :total, :stock_type, :create_by,
          :creation_date, :last_updated_by, :last_update_date)
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="deleteOwnerUserChangeByPrimaryKey" >
    <content >
      <![CDATA[
        DELETE FROM owner_user_change
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="updateOwnerUserChangeByPrimaryKey" >
    <content >
      <![CDATA[
        UPDATE owner_user_change
        SET <[old_owner_user_id = :old_owner_user_id,]>
          <[new_owner_user_id = :new_owner_user_id,]>
          <[sku_num = :sku_num,]>
          <[total = :total,]>
          <[stock_type = :stock_type,]>
          <[create_by = :create_by,]>
          <[creation_date = :creation_date,]>
          <[last_updated_by = :last_updated_by,]>
          <[last_update_date = :last_update_date,]>
        id = id
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
</sqlmap>