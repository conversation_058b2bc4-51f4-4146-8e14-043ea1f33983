<?xml version="1.0" encoding="UTF-8" ?>
<sqlmap >
  <sql datasource="dataSource" id="queryMonthPurchaseCostDiffCount" >
    <content >
      <![CDATA[
        SELECT COUNT(1)
        FROM month_purchase_cost_diff
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND p_id = :p_id]>
        <[AND purchase_order_no = :purchase_order_no]>
        <[AND purchase_order_no IN (:purchaseOrderNoList)]>
        <[AND cost = :cost]>
        <[AND apportion_cost = :apportion_cost]>
        <[AND cost_diff = :cost_diff]>
        <[AND create_time = :create_time]>
        <[AND finish_time = :finish_time]>
        <[AND count_month = :count_month]>
        <[:FILTER_ZERO]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryMonthPurchaseCostDiffList" >
    <content >
      <![CDATA[
        SELECT id, p_id, purchase_order_no, cost, apportion_cost, cost_diff, create_time, 
        finish_time, count_month
        FROM month_purchase_cost_diff
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND p_id = :p_id]>
        <[AND purchase_order_no = :purchase_order_no]>
        <[AND purchase_order_no IN (:purchaseOrderNoList)]>
        <[AND cost = :cost]>
        <[AND apportion_cost = :apportion_cost]>
        <[AND cost_diff = :cost_diff]>
        <[AND create_time = :create_time]>
        <[AND finish_time = :finish_time]>
        <[AND count_month = :count_month]>
        <[:FILTER_ZERO]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryMonthPurchaseCostDiffByPrimaryKey" >
    <content >
      <![CDATA[
        SELECT id, p_id, purchase_order_no, cost, apportion_cost, cost_diff, create_time, 
        finish_time, count_month
        FROM month_purchase_cost_diff
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryMonthPurchaseCostDiff" >
    <content >
      <![CDATA[
        SELECT id, p_id, purchase_order_no, cost, apportion_cost, cost_diff, create_time, 
        finish_time, count_month
        FROM month_purchase_cost_diff
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND p_id = :p_id]>
        <[AND purchase_order_no = :purchase_order_no]>
        <[AND cost = :cost]>
        <[AND apportion_cost = :apportion_cost]>
        <[AND cost_diff = :cost_diff]>
        <[AND create_time = :create_time]>
        <[AND finish_time = :finish_time]>
        <[AND count_month = :count_month]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="createMonthPurchaseCostDiff" >
    <content >
      <![CDATA[
        INSERT INTO month_purchase_cost_diff (p_id, purchase_order_no, cost, apportion_cost, cost_diff, create_time, 
          finish_time, count_month)
        VALUES (:p_id, :purchase_order_no, :cost, :apportion_cost, :cost_diff, :create_time, 
          :finish_time, :count_month)
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="deleteMonthPurchaseCostDiffByPrimaryKey" >
    <content >
      <![CDATA[
        DELETE FROM month_purchase_cost_diff
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="updateMonthPurchaseCostDiffByPrimaryKey" >
    <content >
      <![CDATA[
        UPDATE month_purchase_cost_diff
        SET <[p_id = :p_id,]>
          <[purchase_order_no = :purchase_order_no,]>
          <[cost = :cost,]>
          <[apportion_cost = :apportion_cost,]>
          <[cost_diff = :cost_diff,]>
          <[create_time = :create_time,]>
          <[finish_time = :finish_time,]>
          <[count_month = :count_month,]>
        id = id
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>

  <sql datasource="dataSource" id="queryMonthPurchaseCostDiffTotal" >
    <content >
      <![CDATA[
        SELECT
        COUNT(purchase_order_no) orderTotal,
        SUM(cost_diff) diffTotal
        FROM month_purchase_cost_diff
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND p_id = :p_id]>
        <[AND purchase_order_no = :purchase_order_no]>
        <[AND purchase_order_no IN (:purchaseOrderNoList)]>
        <[AND cost = :cost]>
        <[AND apportion_cost = :apportion_cost]>
        <[AND cost_diff = :cost_diff]>
        <[AND create_time = :create_time]>
        <[AND finish_time = :finish_time]>
        <[AND count_month = :count_month]>
        <[:FILTER_ZERO]>
      ]]>
    </content>
  </sql>
</sqlmap>