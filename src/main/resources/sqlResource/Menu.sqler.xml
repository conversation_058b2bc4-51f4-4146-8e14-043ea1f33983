<?xml version="1.0" encoding="UTF-8" ?>
<sqlmap >
  <sql datasource="dataSource" id="queryMenuCount" >
    <content >
      <![CDATA[
        SELECT COUNT(1)
        FROM t_menu
        WHERE 1 = 1
        <[AND menu_id = :menu_id]>
        <[AND menu_name = :menu_name]>
        <[AND menu_code = :menu_code]>
        <[AND parent_id = :parent_id]>
        <[AND url = :url]>
        <[AND level = :level]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryMenuList" >
    <content >
      <![CDATA[
        SELECT menu_id, menu_name, menu_code, parent_id, url, level, priority,old_or_new
        FROM t_menu
        WHERE 1 = 1
        <[AND menu_id = :menu_id]>
        <[AND menu_name = :menu_name]>
        <[AND menu_code = :menu_code]>
        <[AND menu_code IN (:menu_code_list)]>
        
        <[AND menu_code IN (SELECT DISTINCT role_per.permission_code
			FROM t_role_permission role_per
			LEFT JOIN t_role role ON role_per.role_id = role.role_id	
			LEFT JOIN t_user_role user_role ON user_role.role_id = role.role_id
			WHERE user_role.user_id = :user_id)]>
			
		<[AND menu_code IN (SELECT DISTINCT role_per.permission_code
			FROM t_role_permission role_per
			WHERE role_per.role_id = :role_id)]>
        
        <[AND parent_id = :parent_id]>
        <[AND url = :url]>
        <[AND level = :level]>
        <[AND level NOT IN (:level_list)]>
        <[:ORDER_BY]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryMenuByPrimaryKey" >
    <content >
      <![CDATA[
        SELECT menu_id, menu_name, menu_code, parent_id, url, level, priority,old_or_new
        FROM t_menu
        WHERE 1 = 1
        AND menu_id = :menu_id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryMenu" >
    <content >
      <![CDATA[
        SELECT menu_id, menu_name, menu_code, parent_id, url, level, priority,old_or_new
        FROM t_menu
        WHERE 1 = 1
        <[AND menu_id = :menu_id]>
        <[AND menu_name = :menu_name]>
        <[AND menu_code = :menu_code]>
        <[AND parent_id = :parent_id]>
        <[AND url = :url]>
        <[AND level = :level]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="createMenu" >
    <content >
      <![CDATA[
        INSERT INTO t_menu (menu_name, menu_code, parent_id, url, level, priority,old_or_new)
        VALUES (:menu_name, :menu_code, :parent_id, :url, :level, :priority, :old_or_new)
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="deleteMenuByPrimaryKey" >
    <content >
      <![CDATA[
        DELETE FROM t_menu
        WHERE 1 = 1
        AND menu_id = :menu_id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="deleteMenuByCode" >
    <content >
      <![CDATA[
        DELETE FROM t_menu
        WHERE 1 = 1
        AND menu_code = :menu_code
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="updateMenuByPrimaryKey" >
    <content >
      <![CDATA[
        UPDATE t_menu
        SET <[menu_name = :menu_name,]>
          <[menu_code = :menu_code,]>
          <[parent_id = :parent_id,]>
          <[url = :url,]>
          <[level = :level,]>
          <[priority = :priority,]>
          <[old_or_new = :old_or_new,]>
        menu_id = menu_id
        WHERE 1 = 1
        AND menu_id = :menu_id
      ]]>
    </content>
  </sql>

  <sql datasource="dataSource" id="queryMenuListMap" >
    <content >
      <![CDATA[
        select role_per.role_id,t_menu.menu_code,t_menu.url,t_menu.level, t_menu.menu_name,t_menu.old_or_new
        from t_role_permission role_per
        LEFT JOIN t_menu t_menu ON t_menu.menu_code=role_per.permission_code
        where t_menu.level=3 and t_menu.url is not null and t_menu.url != ''
        <[AND role_per.role_id IN (
            select role_id from t_role_permission
            LEFT JOIN t_menu ON menu_code=permission_code
            where menu_id = :menuId )
        ]>
        <[AND role_per.role_id IN (
            select role_id from t_role_permission
            LEFT JOIN t_menu ON menu_code=permission_code
            where menu_code = :menuCode )
        ]>
        <[AND role_per.role_id = :roleId]>
        <[AND role_per.role_id IN ( :roleIdList)]>
        ORDER BY role_per.role_id,t_menu.menu_code
      ]]>
    </content>
  </sql>

</sqlmap>