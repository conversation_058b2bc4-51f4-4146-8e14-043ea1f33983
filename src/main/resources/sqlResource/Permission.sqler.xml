<?xml version="1.0" encoding="UTF-8" ?>
<sqlmap >
  <sql datasource="dataSource" id="queryPermissionCount" >
    <content >
      <![CDATA[
        SELECT COUNT(1)
        FROM t_permission
        WHERE 1 = 1
        <[AND permission_id = :permission_id]>
        <[AND permission_name = :permission_name]>
        <[AND permission_code = :permission_code]>
        <[AND assign_type = :assign_type]>
        <[AND parent_id = :parent_id]>
        <[AND resource_type = :resource_type]>
        <[AND resource_key = :resource_key]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryPermissionList" >
    <content >
      <![CDATA[
        SELECT permission_id, permission_name, permission_code, assign_type, parent_id, resource_type, 
        resource_key, priority
        FROM t_permission
        WHERE 1 = 1
        <[AND permission_id = :permission_id]>
        <[AND permission_name = :permission_name]>
        <[AND permission_code = :permission_code]>
        <[AND assign_type = :assign_type]>
        <[AND parent_id = :parent_id]>
        <[AND resource_type = :resource_type]>
        <[AND resource_key = :resource_key]>
        <[AND resource_type IN (:resource_type_list)]>
        <[:ORDER_BY]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryPermissionByPrimaryKey" >
    <content >
      <![CDATA[
        SELECT permission_id, permission_name, permission_code, assign_type, parent_id, resource_type, 
        resource_key, priority
        FROM t_permission
        WHERE 1 = 1
        AND permission_id = :permission_id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryPermission" >
    <content >
      <![CDATA[
        SELECT permission_id, permission_name, permission_code, assign_type, parent_id, resource_type, 
        resource_key, priority
        FROM t_permission
        WHERE 1 = 1
        <[AND permission_id = :permission_id]>
        <[AND permission_name = :permission_name]>
        <[AND permission_code = :permission_code]>
        <[AND assign_type = :assign_type]>
        <[AND parent_id = :parent_id]>
        <[AND resource_type = :resource_type]>
        <[AND resource_key = :resource_key]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="createPermission" >
    <content >
      <![CDATA[
        INSERT INTO t_permission (permission_name, permission_code, assign_type, parent_id, resource_type, 
          resource_key, priority)
        VALUES (:permission_name, :permission_code, :assign_type, :parent_id, :resource_type, 
          :resource_key, :priority)
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="deletePermissionByPrimaryKey" >
    <content >
      <![CDATA[
        DELETE FROM t_permission
        WHERE 1 = 1
        AND permission_id = :permission_id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="deletePermissionByCode" >
    <content >
      <![CDATA[
        DELETE FROM t_permission
        WHERE 1 = 1
        AND permission_code = :permission_code
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="updatePermissionByPrimaryKey" >
    <content >
      <![CDATA[
        UPDATE t_permission
        SET <[permission_name = :permission_name,]>
          <[permission_code = :permission_code,]>
          <[assign_type = :assign_type,]>
          <[parent_id = :parent_id,]>
          <[resource_type = :resource_type,]>
          <[resource_key = :resource_key,]>
          <[priority = :priority,]>
        permission_id = permission_id
        WHERE 1 = 1
        AND permission_id = :permission_id
      ]]>
    </content>
  </sql>
  
  
  <sql datasource="dataSource" id="updatePermissionByCode" >
    <content >
      <![CDATA[
        UPDATE t_permission
        SET <[permission_name = :permission_name,]>
          <[assign_type = :assign_type,]>
          <[parent_id = :parent_id,]>
          <[resource_type = :resource_type,]>
          <[resource_key = :resource_key,]>
          <[priority = :priority,]>
        permission_id = permission_id
        WHERE 1 = 1
        AND permission_code = :permission_code
      ]]>
    </content>
  </sql>
  
  
  <!-- 角色操作权限 -->
	<sql id="queryAllRolePermissionMap" datasource="dataSource">
		<content> 
			<![CDATA[
				SELECT DISTINCT role_per.role_id, per.*
				FROM t_role_permission role_per, t_permission per
				WHERE 1 = 1
				AND role_per.permission_code = per.permission_code
				<[AND role_per.role_id = :role_id]>
				<[AND role_per.role_id IN (:role_id_list)]>
			]]>
		</content>
	</sql>
	
	<!-- 删除角色权限 -->
	<sql id="deleteRolePermission" datasource="dataSource">
		<content> 
			<![CDATA[
				DELETE T2.* FROM t_permission T1, t_role_permission T2
				WHERE 1 = 1 
				AND T1.permission_code = T2.permission_code
				AND T1.resource_type IN (1,3)
				AND role_id = :role_id
			]]>
		</content>
	</sql>
	
	<!-- 删除角色按钮权限 -->
	<sql id="deleteRoleAuthPermission" datasource="dataSource">
		<content> 
			<![CDATA[
				DELETE T2.* FROM t_permission T1, t_role_permission T2
				WHERE 1 = 1 
				AND T1.permission_code = T2.permission_code
				AND T1.resource_type = 2
				AND T2.role_id = :role_id
				AND T1.parent_id= :parent_code
			]]>
		</content>
	</sql>
	
	<!-- 根据parentId删除角色按钮权限 -->
	<sql id="deleteRolePermissionByParentIds" datasource="dataSource">
		<content> 
			<![CDATA[
				DELETE T2.* FROM t_permission T1, t_role_permission T2
				WHERE 1 = 1 
				AND T1.permission_code = T2.permission_code
				AND T1.resource_type = 2
				AND T2.role_id = :role_id
				AND T1.parent_id IN (:parent_id_list)
			]]>
		</content>
	</sql>
	
	<!-- 删除角色权限 -->
	<sql id="deleteRolePermissionByCode" datasource="dataSource">
		<content> 
			<![CDATA[
				DELETE FROM t_role_permission
				WHERE 1 = 1
				AND permission_code = :permission_code
			]]>
		</content>
	</sql>
	
	<!-- 创建角色权限 -->
	<sql id="createRolePermission" datasource="dataSource">
		<content> 
			<![CDATA[
				INSERT INTO t_role_permission(role_id, permission_code) VALUES(:role_id, :permission_code)
			]]>
		</content>
	</sql>
	
	<sql datasource="dataSource" id="queryAuthPermissions" >
    <content >
      <![CDATA[
        SELECT 
        	T5.permission_id, 
        	T5.permission_name, 
        	T5.permission_code, 
        	T5.assign_type, 
        	T5.parent_id, 
        	T5.resource_type, 
        	T5.resource_key, 
        	T5.priority
        FROM 
        	t_user T1, t_user_role T2, t_role T3, t_role_permission T4, t_permission T5, t_menu T6
        WHERE 
        	T1.user_id = T2.user_id 
        	AND T2.role_id = T3.role_id
        	AND T3.role_id = T4.role_id
        	AND T4.permission_code = T5.permission_code
        	AND T5.permission_code = T6.menu_code
        	<[AND T1.user_id = :user_id]>
        	<[AND T3.role_id = :role_id]>
        	<[AND T5.resource_type = :resource_type]>
        	<[AND T5.parent_id = :parent_code]>
        	<[AND T5.permission_code = :auth_code]>
        	<[AND T6.url = :auth_url]>
        	<[AND T3.role_id IN (:role_id_list)]>
        GROUP BY T5.permission_id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryAuthPDAPermissions" >
    <content >
      <![CDATA[
        SELECT 
        	T5.permission_id, 
        	T5.permission_name, 
        	T5.permission_code, 
        	T5.assign_type, 
        	T5.parent_id, 
        	T5.resource_type, 
        	T5.resource_key, 
        	T5.priority
        FROM 
        	t_user T1, t_user_role T2, t_role T3, t_role_permission T4, t_permission T5
        WHERE 
        	T1.user_id = T2.user_id 
        	AND T2.role_id = T3.role_id
        	AND T3.role_id = T4.role_id
        	AND T4.permission_code = T5.permission_code
        	<[AND T1.user_id = :user_id]>
        	<[AND T3.role_id = :role_id]>
        	<[AND T5.resource_type = :resource_type]>
        	<[AND T5.parent_id = :parent_code]>
        	<[AND T5.permission_code = :auth_code]>
        	<[AND T3.role_id IN (:role_id_list)]>
        GROUP BY T5.permission_id
      ]]>
    </content>
  </sql>
  
</sqlmap>