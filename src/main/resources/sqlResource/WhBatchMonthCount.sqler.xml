<?xml version="1.0" encoding="UTF-8" ?>
<sqlmap >
  <sql datasource="dataSource" id="queryWhBatchMonthCountCount" >
    <content >
      <![CDATA[
        SELECT COUNT(1)
        FROM wh_batch_month_count
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND batch_no = :batch_no]>
        <[AND store_code = :store_code]>
        <[AND sku = :sku]>
        <[AND quantity = :quantity]>
        <[AND match_quantity = :match_quantity]>
        <[AND purchase_cost_price = :purchase_cost_price]>
        <[AND purchase_freight_price = :purchase_freight_price]>
        <[AND product_freight_price = :product_freight_price]>
        <[AND product_tax_price = :product_tax_price]>
        <[AND count_date = :count_date]>
        <[AND report_time = :report_time]>
        <[AND merchant_id = :merchant_id]>

        <[AND id IN (:idList)]>
        <[AND store_code IN (:storeCodeList)]>
        <[AND sku IN (:skuList)]>
        <[AND count_date >= :from_stock_date]>
        <[AND count_date <= :to_stock_date]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryWhBatchMonthCountList" >
    <content >
      <![CDATA[
        SELECT id, batch_no, store_code, sku, quantity, match_quantity, purchase_cost_price, end_stock,
        purchase_freight_price, product_freight_price, product_tax_price, count_date, report_time, 
        merchant_id
        FROM wh_batch_month_count
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND batch_no = :batch_no]>
        <[AND store_code = :store_code]>
        <[AND sku = :sku]>
        <[AND quantity = :quantity]>
        <[AND match_quantity = :match_quantity]>
        <[AND purchase_cost_price = :purchase_cost_price]>
        <[AND purchase_freight_price = :purchase_freight_price]>
        <[AND product_freight_price = :product_freight_price]>
        <[AND product_tax_price = :product_tax_price]>
        <[AND count_date = :count_date]>
        <[AND report_time = :report_time]>
        <[AND merchant_id = :merchant_id]>

        <[AND id IN (:idList)]>
        <[AND store_code IN (:storeCodeList)]>
        <[AND sku IN (:skuList)]>
        <[AND count_date >= :from_stock_date]>
        <[AND count_date <= :to_stock_date]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryWhBatchMonthCountByPrimaryKey" >
    <content >
      <![CDATA[
        SELECT id, batch_no, store_code, sku, quantity, match_quantity, purchase_cost_price, end_stock,
        purchase_freight_price, product_freight_price, product_tax_price, count_date, report_time, 
        merchant_id
        FROM wh_batch_month_count
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryWhBatchMonthCount" >
    <content >
      <![CDATA[
        SELECT id, batch_no, store_code, sku, quantity, match_quantity, purchase_cost_price, end_stock,
        purchase_freight_price, product_freight_price, product_tax_price, count_date, report_time, 
        merchant_id
        FROM wh_batch_month_count
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND batch_no = :batch_no]>
        <[AND store_code = :store_code]>
        <[AND sku = :sku]>
        <[AND quantity = :quantity]>
        <[AND match_quantity = :match_quantity]>
        <[AND purchase_cost_price = :purchase_cost_price]>
        <[AND purchase_freight_price = :purchase_freight_price]>
        <[AND product_freight_price = :product_freight_price]>
        <[AND product_tax_price = :product_tax_price]>
        <[AND count_date = :count_date]>
        <[AND report_time = :report_time]>
        <[AND merchant_id = :merchant_id]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="createWhBatchMonthCount" >
    <content >
      <![CDATA[
        INSERT INTO wh_batch_month_count (batch_no, store_code, sku, quantity, match_quantity, purchase_cost_price, 
          purchase_freight_price, product_freight_price, product_tax_price, count_date, end_stock,
          report_time, merchant_id)
        VALUES (:batch_no, :store_code, :sku, :quantity, :match_quantity, :purchase_cost_price, 
          :purchase_freight_price, :product_freight_price, :product_tax_price, :count_date, :end_stock,
          :report_time, :merchant_id)
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="deleteWhBatchMonthCountByPrimaryKey" >
    <content >
      <![CDATA[
        DELETE FROM wh_batch_month_count
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>

  <sql datasource="dataSource" id="deleteWhBatchMonthCountByQuery" >
    <content >
      <![CDATA[
        DELETE FROM wh_batch_month_count
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND batch_no = :batch_no]>
        <[AND store_code = :store_code]>
        <[AND sku = :sku]>
        <[AND count_date = :count_date]>
        <[AND report_time = :report_time]>
        <[AND merchant_id = :merchant_id]>
        <[AND id IN (:idList)]>
        <[AND store_code IN (:storeCodeList)]>
        <[AND sku IN (:skuList)]>
        <[AND count_date >= :from_stock_date]>
        <[AND count_date <= :to_stock_date]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="updateWhBatchMonthCountByPrimaryKey" >
    <content >
      <![CDATA[
        UPDATE wh_batch_month_count
        SET <[batch_no = :batch_no,]>
          <[store_code = :store_code,]>
          <[sku = :sku,]>
          <[quantity = :quantity,]>
          <[match_quantity = :match_quantity,]>
          <[purchase_cost_price = :purchase_cost_price,]>
          <[purchase_freight_price = :purchase_freight_price,]>
          <[product_freight_price = :product_freight_price,]>
          <[product_tax_price = :product_tax_price,]>
          <[count_date = :count_date,]>
          <[report_time = :report_time,]>
          <[merchant_id = :merchant_id,]>
          <[end_stock = :end_stock,]>
        id = id
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>

  <sql datasource="dataSource" id="queryWhBatchMonthCountSum" >
    <content >
      <![CDATA[
        SELECT
            SUM(quantity) as totalStock,
            SUM(IFNULL(end_stock,0)*IFNULL(purchase_cost_price,0)) as totalEndAmount
        FROM wh_batch_month_count
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND batch_no = :batch_no]>
        <[AND store_code = :store_code]>
        <[AND sku = :sku]>
        <[AND quantity = :quantity]>
        <[AND match_quantity = :match_quantity]>
        <[AND purchase_cost_price = :purchase_cost_price]>
        <[AND purchase_freight_price = :purchase_freight_price]>
        <[AND product_freight_price = :product_freight_price]>
        <[AND product_tax_price = :product_tax_price]>
        <[AND count_date = :count_date]>
        <[AND report_time = :report_time]>
        <[AND merchant_id = :merchant_id]>

        <[AND id IN (:idList)]>
        <[AND store_code IN (:storeCodeList)]>
        <[AND sku IN (:skuList)]>
        <[AND count_date >= :from_stock_date]>
        <[AND count_date <= :to_stock_date]>
      ]]>
    </content>
  </sql>
</sqlmap>