<?xml version="1.0" encoding="UTF-8" ?>
<sqlmap >
  <sql datasource="dataSource" id="queryWhBatchReturnSkuCount" >
    <content >
      <![CDATA[
        SELECT COUNT(1)
        FROM wh_batch_return_sku
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND order_no = :order_no]>
        <[AND sku = :sku]>
        <[AND status = :status]>
        <[AND grid_by = :grid_by]>
        <[AND return_by = :return_by]>
        <[AND up_by = :up_by]>
        <[AND quantity = :quantity]>
        <[AND grid_quantity = :grid_quantity]>
        <[AND returnning_quantity = :returnning_quantity]>
        <[AND complete_quantity = :complete_quantity]>
        <[AND created_by = :created_by]>
        <[AND creation_date = :creation_date]>
        <[AND last_updated_by = :last_updated_by]>
        <[AND last_update_date = :last_update_date]>
        <[AND area = :area]>
        <[AND area in  (:areas)]>
        <[AND warehouse_id = :warehouse_id]>
        <[:IS_NULL_AREA]>
        <[:IS_VIRTUAL_LOCATION]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryWhBatchReturnSkuList" >
    <content >
      <![CDATA[
        SELECT id, order_no, sku, status, grid_by, return_by, up_by, quantity, grid_quantity, exp_quantity,
        returnning_quantity, complete_quantity, created_by, creation_date, last_updated_by, zf_qty,
        last_update_date, area, warehouse_id,location_type, location
        FROM wh_batch_return_sku
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND order_no = :order_no]>
        <[AND order_no in (:orderNos)]>
        <[AND sku = :sku]>
        <[AND status = :status]>
        <[AND grid_by = :grid_by]>
        <[AND return_by = :return_by]>
        <[AND up_by = :up_by]>
        <[AND quantity = :quantity]>
        <[AND grid_quantity = :grid_quantity]>
        <[AND returnning_quantity = :returnning_quantity]>
        <[AND complete_quantity = :complete_quantity]>
        <[AND created_by = :created_by]>
        <[AND creation_date = :creation_date]>
        <[AND last_updated_by = :last_updated_by]>
        <[AND last_update_date = :last_update_date]>
        <[AND area = :area]>
        <[AND area in  (:areas)]>
        <[AND warehouse_id = :warehouse_id]>
        <[AND location_type = :location_type]>
        <[:IS_NULL_AREA]>
        <[:IS_VIRTUAL_LOCATION]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryWhBatchReturnSkuByPrimaryKey" >
    <content >
      <![CDATA[
        SELECT id, order_no, sku, status, grid_by, return_by, up_by, quantity, grid_quantity, exp_quantity,
        returnning_quantity, complete_quantity, created_by, creation_date, last_updated_by, zf_qty,
        last_update_date, area, warehouse_id,location_type, location
        FROM wh_batch_return_sku
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryWhBatchReturnSku" >
    <content >
      <![CDATA[
        SELECT id, order_no, sku, status, grid_by, return_by, up_by, quantity, grid_quantity, exp_quantity,
        returnning_quantity, complete_quantity, created_by, creation_date, last_updated_by, zf_qty,
        last_update_date, area, warehouse_id,location_type, location
        FROM wh_batch_return_sku
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND order_no = :order_no]>
        <[AND sku = :sku]>
        <[AND status = :status]>
        <[AND grid_by = :grid_by]>
        <[AND return_by = :return_by]>
        <[AND up_by = :up_by]>
        <[AND quantity = :quantity]>
        <[AND grid_quantity = :grid_quantity]>
        <[AND returnning_quantity = :returnning_quantity]>
        <[AND complete_quantity = :complete_quantity]>
        <[AND created_by = :created_by]>
        <[AND creation_date = :creation_date]>
        <[AND last_updated_by = :last_updated_by]>
        <[AND last_update_date = :last_update_date]>
        <[AND area = :area]>
        <[AND warehouse_id = :warehouse_id]>
        <[AND location_type = :location_type]>
        <[:IS_NULL_AREA]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="createWhBatchReturnSku" >
    <content >
      <![CDATA[
        INSERT INTO wh_batch_return_sku (order_no, sku, status, grid_by, return_by, up_by, quantity, grid_quantity, exp_quantity,
          returnning_quantity, complete_quantity, created_by, creation_date, last_updated_by, zf_qty,
          last_update_date, area, warehouse_id,location_type, location)
        VALUES (:order_no, :sku, :status, :grid_by, :return_by, :up_by, :quantity, :grid_quantity, :exp_quantity,
          :returnning_quantity, :complete_quantity, :created_by, :creation_date, :last_updated_by, :zf_qty,
          :last_update_date, :area, :warehouse_id, :location_type, :location)
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="deleteWhBatchReturnSkuByPrimaryKey" >
    <content >
      <![CDATA[
        DELETE FROM wh_batch_return_sku
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="updateWhBatchReturnSkuByPrimaryKey" >
    <content >
      <![CDATA[
        UPDATE wh_batch_return_sku
        SET <[order_no = :order_no,]>
          <[sku = :sku,]>
          <[status = :status,]>
          <[grid_by = :grid_by,]>
          <[return_by = :return_by,]>
          <[up_by = :up_by,]>
          <[quantity = :quantity,]>
          <[zf_qty = :zf_qty,]>
          <[grid_quantity = :grid_quantity,]>
          <[exp_quantity = :exp_quantity,]>
          <[returnning_quantity = :returnning_quantity,]>
          <[complete_quantity = :complete_quantity,]>
          <[created_by = :created_by,]>
          <[creation_date = :creation_date,]>
          <[last_updated_by = :last_updated_by,]>
          <[last_update_date = :last_update_date,]>
          <[area = :area,]>
          <[warehouse_id = :warehouse_id,]>
          <[location_type = :location_type,]>
          <[location = :location,]>
        id = id
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>

  <sql datasource="dataSource" id="updateWhBatchReturnSkuGridByPrimaryKey" >
    <content >
      <![CDATA[
        UPDATE wh_batch_return_sku
        SET
          <[status = :status,]>
          <[grid_by = :grid_by,]>
          <[location_type = :location_type,]>
          <[location = :location,]>
          <[last_updated_by = :last_updated_by,]>
          <[last_update_date = :last_update_date,]>
          <[exp_quantity = :exp_quantity,]>
          grid_quantity = IFNULL(grid_quantity,0)+1
        WHERE 1 = 1
        AND id = :id
        AND IFNULL(grid_quantity,0) < quantity
      ]]>
    </content>
  </sql>

  <sql datasource="dataSource" id="createOldWarehouseWhBatchReturnSku" >
    <content >
      <![CDATA[
        INSERT INTO wh_batch_return_sku (id, order_no, sku, status, grid_by, return_by, up_by, quantity, grid_quantity, exp_quantity,
          returnning_quantity, complete_quantity, created_by, creation_date, last_updated_by,zf_qty,
          last_update_date, area, warehouse_id,location_type,location)
        VALUES (:id, :order_no, :sku, :status, :grid_by, :return_by, :up_by, :quantity, :grid_quantity, :exp_quantity,
          :returnning_quantity, :complete_quantity, :created_by, :creation_date, :last_updated_by,:zf_qty,
          :last_update_date, :area, :warehouse_id, :location_type,:location)
      ]]>
    </content>
  </sql>
</sqlmap>