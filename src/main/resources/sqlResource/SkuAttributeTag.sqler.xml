<?xml version="1.0" encoding="UTF-8" ?>
<sqlmap >
  <sql datasource="dataSource" id="querySkuAttributeTagCount" >
    <content >
      <![CDATA[
        SELECT COUNT(1)
        FROM sku_attribute_tag
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND value = :value]>
        <[AND prompt_page = :prompt_page]>
        <[AND create_by = :create_by]>
        <[AND creation_time = :creation_time]>
        <[AND last_update_time = :last_update_time]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="querySkuAttributeTagList" >
    <content >
      <![CDATA[
        SELECT id, value, prompt_page, create_by, creation_time, last_update_time, editable, removable
        FROM sku_attribute_tag
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND value = :value]>
        <[AND prompt_page = :prompt_page]>
        <[AND create_by = :create_by]>
        <[AND creation_time = :creation_time]>
        <[AND last_update_time = :last_update_time]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="querySkuAttributeTagByPrimaryKey" >
    <content >
      <![CDATA[
        SELECT id, value, prompt_page, create_by, creation_time, last_update_time, editable, removable
        FROM sku_attribute_tag
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="querySkuAttributeTag" >
    <content >
      <![CDATA[
        SELECT id, value, prompt_page, create_by, creation_time, last_update_time, editable, removable
        FROM sku_attribute_tag
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND value = :value]>
        <[AND prompt_page = :prompt_page]>
        <[AND create_by = :create_by]>
        <[AND creation_time = :creation_time]>
        <[AND last_update_time = :last_update_time]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="createSkuAttributeTag" >
    <content >
      <![CDATA[
        INSERT INTO sku_attribute_tag (value, prompt_page, create_by, creation_time, last_update_time, editable, removable)
        VALUES (:value, :prompt_page, :create_by, :creation_time, :last_update_time, :editable, :removable)
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="deleteSkuAttributeTagByPrimaryKey" >
    <content >
      <![CDATA[
        DELETE FROM sku_attribute_tag
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="updateSkuAttributeTagByPrimaryKey" >
    <content >
      <![CDATA[
        UPDATE sku_attribute_tag
        SET <[value = :value,]>
          <[prompt_page = :prompt_page,]>
          <[create_by = :create_by,]>
          <[creation_time = :creation_time,]>
          <[last_update_time = :last_update_time,]>
          <[editable = :editable,]>
          <[removable = :removable,]>
        id = id
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>

  <sql datasource="dataSource" id="getTagValueList" >
    <content >
      <![CDATA[
        SELECT DISTINCT `value` FROM sku_attribute_tag
      ]]>
    </content>
  </sql>
</sqlmap>