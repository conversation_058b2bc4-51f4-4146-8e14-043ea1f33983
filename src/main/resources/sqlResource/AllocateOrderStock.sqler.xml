<?xml version="1.0" encoding="UTF-8" ?>
<sqlmap >
  <sql datasource="dataSource" id="queryAllocateOrderStockCount" >
    <content >
      <![CDATA[
        SELECT COUNT(1)
        FROM allocate_order_stock
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND sku = :sku]>
        <[AND sku IN (:skuList)]>
        <[AND quantity = :quantity]>
        <[AND allocate_return_quantity = :allocate_return_quantity]>
        <[AND remark = :remark]>
        <[AND creation_date = :creation_date]>
        <[AND create_by = :create_by]>
        <[AND last_update_date = :last_update_date]>
        <[AND last_updated_by = :last_updated_by]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryAllocateOrderStockList" >
    <content >
      <![CDATA[
        SELECT id, sku, quantity, allocate_return_quantity, remark, creation_date, create_by, 
        last_update_date, last_updated_by
        FROM allocate_order_stock
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND sku = :sku]>
        <[AND sku IN (:skuList)]>
        <[AND quantity = :quantity]>
        <[AND allocate_return_quantity = :allocate_return_quantity]>
        <[AND remark = :remark]>
        <[AND creation_date = :creation_date]>
        <[AND create_by = :create_by]>
        <[AND last_update_date = :last_update_date]>
        <[AND last_updated_by = :last_updated_by]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryAllocateOrderStockByPrimaryKey" >
    <content >
      <![CDATA[
        SELECT id, sku, quantity, allocate_return_quantity, remark, creation_date, create_by, 
        last_update_date, last_updated_by
        FROM allocate_order_stock
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryAllocateOrderStock" >
    <content >
      <![CDATA[
        SELECT id, sku, quantity, allocate_return_quantity, remark, creation_date, create_by, 
        last_update_date, last_updated_by
        FROM allocate_order_stock
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND sku = :sku]>
        <[AND sku IN (:skuList)]>
        <[AND quantity = :quantity]>
        <[AND allocate_return_quantity = :allocate_return_quantity]>
        <[AND remark = :remark]>
        <[AND creation_date = :creation_date]>
        <[AND create_by = :create_by]>
        <[AND last_update_date = :last_update_date]>
        <[AND last_updated_by = :last_updated_by]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="createAllocateOrderStock" >
    <content >
      <![CDATA[
        INSERT INTO allocate_order_stock (sku, quantity, allocate_return_quantity, remark, creation_date, create_by, 
          last_update_date, last_updated_by)
        VALUES (:sku, :quantity, :allocate_return_quantity, :remark, :creation_date, :create_by, 
          :last_update_date, :last_updated_by)
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="deleteAllocateOrderStockByPrimaryKey" >
    <content >
      <![CDATA[
        DELETE FROM allocate_order_stock
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="updateAllocateOrderStockByPrimaryKey" >
    <content >
      <![CDATA[
        UPDATE allocate_order_stock
        SET <[sku = :sku,]>
          <[quantity = :quantity,]>
          <[allocate_return_quantity = :allocate_return_quantity,]>
          <[remark = :remark,]>
          <[creation_date = :creation_date,]>
          <[create_by = :create_by,]>
          <[last_update_date = :last_update_date,]>
          <[last_updated_by = :last_updated_by,]>
        id = id
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
</sqlmap>