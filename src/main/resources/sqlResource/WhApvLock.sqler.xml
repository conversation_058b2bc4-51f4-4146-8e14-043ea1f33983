<?xml version="1.0" encoding="UTF-8" ?>
<sqlmap >
  <sql datasource="dataSource" id="queryWhApvLockCount" >
    <content >
      <![CDATA[
        SELECT COUNT(1)
        FROM wh_apv_lock
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND apv_id = :apv_id]>
        <[AND status = :status]>
        <[AND assigner = :assigner]>
        <[AND picker = :picker]>
        <[AND last_update_date = :last_update_date]>
        <[AND last_updated_by = :last_updated_by]>
        <[AND creation_date = :creation_date]>
        <[AND created_by = :created_by]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryWhApvLockList" >
    <content >
      <![CDATA[
        SELECT id, apv_id, status, assigner, picker, last_update_date, last_updated_by, 
        creation_date, created_by
        FROM wh_apv_lock
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND apv_id = :apv_id]>
        <[AND status = :status]>
        <[AND assigner = :assigner]>
        <[AND picker = :picker]>
        <[AND last_update_date = :last_update_date]>
        <[AND last_updated_by = :last_updated_by]>
        <[AND creation_date = :creation_date]>
        <[AND created_by = :created_by]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryWhApvLockByPrimaryKey" >
    <content >
      <![CDATA[
        SELECT id, apv_id, status, assigner, picker, last_update_date, last_updated_by, 
        creation_date, created_by
        FROM wh_apv_lock
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryWhApvLock" >
    <content >
      <![CDATA[
        SELECT id, apv_id, status, assigner, picker, last_update_date, last_updated_by, 
        creation_date, created_by
        FROM wh_apv_lock
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND apv_id = :apv_id]>
        <[AND status = :status]>
        <[AND assigner = :assigner]>
        <[AND picker = :picker]>
        <[AND last_update_date = :last_update_date]>
        <[AND last_updated_by = :last_updated_by]>
        <[AND creation_date = :creation_date]>
        <[AND created_by = :created_by]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="createWhApvLock" >
    <content >
      <![CDATA[
        INSERT INTO wh_apv_lock (apv_id, status, assigner, picker, last_update_date, last_updated_by, 
          creation_date, created_by)
        VALUES (:apv_id, :status, :assigner, :picker, :last_update_date, :last_updated_by, 
          :creation_date, :created_by)
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="deleteWhApvLockByPrimaryKey" >
    <content >
      <![CDATA[
        DELETE FROM wh_apv_lock
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="updateWhApvLockByPrimaryKey" >
    <content >
      <![CDATA[
        UPDATE wh_apv_lock
        SET <[apv_id = :apv_id,]>
          <[status = :status,]>
          <[assigner = :assigner,]>
          <[picker = :picker,]>
          <[last_update_date = :last_update_date,]>
          <[last_updated_by = :last_updated_by,]>
          <[creation_date = :creation_date,]>
          <[created_by = :created_by,]>
        id = id
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="deleteWhApvLockByapvId" >
    <content >
      <![CDATA[
        DELETE FROM wh_apv_lock
        WHERE 1 = 1
        AND apv_id = :apv_id
      ]]>
    </content>
  </sql>
  <sql datasource="dataSource" id="queryLessApvList" >
    <content >
      <![CDATA[
        SELECT
          apv.id,
          apv.apv_no,
          apv.apv_status,
          apv.tracking_number,
          apv.STATUS,
          apv_item.sku_title,
          apv_item.sku,
          apv_item.sale_quantity,
          IFNULL(c.quantity,0) as quantity,
          IFNULL(c.pick_quantity,0) as pick_quantity,
          IFNULL(c.gird_quantity,0)as gird_quantity,
          stock.location_number
      FROM
          wh_apv apv
          LEFT JOIN wh_apv_item apv_item ON apv.id = apv_item.apv_id
          LEFT JOIN wh_apv_out_stock_chain c ON c.relevant_no = apv.apv_no AND c.sku = apv_item.sku
          LEFT JOIN wh_stock stock ON stock.id = c.stock_id
          INNER JOIN (
            SELECT id FROM wh_apv apv
            WHERE 1 = 1
            <[AND apv.id = :id]>
	        <[AND apv.id IN (:apv_id_list)]>
	        <[AND apv.apv_no = :apv_no]>
	        <[AND apv.apv_no IN (:apv_no_list)]>
	        <[AND apv.status = :status]>
            <[AND apv.status IN (:status_list)]>
          ) whapv ON apv.id = whapv.id
      ]]>
    </content>
  </sql>
</sqlmap>