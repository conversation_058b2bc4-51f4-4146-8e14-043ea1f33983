<?xml version="1.0" encoding="UTF-8" ?>
<sqlmap >
  <sql datasource="dataSource" id="queryWhApvStatisticsCount" >
    <content >
      <![CDATA[
        SELECT COUNT(1)
        FROM wh_apv_statistics
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND creation_date = :creation_date]>
        <[AND created_by = :created_by]>
        <[AND count_date = :count_date]>
        <[AND warehouse_id = :warehouse_id]>
        <[AND smt_push_qty = :smt_push_qty]>
        <[AND shopee_push_qty = :shopee_push_qty]>
        <[AND amazon_push_qty = :amazon_push_qty]>
        <[AND lazada_push_qty = :lazada_push_qty]>
        <[AND ebay_push_qty = :ebay_push_qty]>
        <[AND wish_push_qty = :wish_push_qty]>
        <[AND joom_push_qty = :joom_push_qty]>
        <[AND walmart_push_qty = :walmart_push_qty]>
        <[AND other_push_qty = :other_push_qty]>
        <[AND smt_deliver_qty = :smt_deliver_qty]>
        <[AND shopee_deliver_qty = :shopee_deliver_qty]>
        <[AND amazon_deliver_qty = :amazon_deliver_qty]>
        <[AND lazada_deliver_qty = :lazada_deliver_qty]>
        <[AND ebay_deliver_qty = :ebay_deliver_qty]>
        <[AND wish_deliver_qty = :wish_deliver_qty]>
        <[AND joom_deliver_qty = :joom_deliver_qty]>
        <[AND walmart_deliver_qty = :walmart_deliver_qty]>
        <[AND other_deliver_qty = :other_deliver_qty]>
        <[AND count_date >= :start_time]>
        <[AND count_date <= :end_time]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryWhApvStatisticsList" >
    <content >
      <![CDATA[
        SELECT id, creation_date, created_by, count_date, warehouse_id, smt_push_qty, shopee_push_qty, 
        amazon_push_qty, lazada_push_qty, ebay_push_qty, wish_push_qty, joom_push_qty, walmart_push_qty, 
        other_push_qty, smt_deliver_qty, shopee_deliver_qty, amazon_deliver_qty, lazada_deliver_qty, 
        ebay_deliver_qty, wish_deliver_qty, joom_deliver_qty, walmart_deliver_qty, other_deliver_qty,
        ozon_push_qty, tiktok_push_qty, coupan_push_qty, smt_jit_push_qty, ozon_deliver_qty, coupan_deliver_qty,
        tiktok_deliver_qty, smt_jit_deliver_qty, transfer_deliver_qty, transfer_push_qty, ow_push_qty,
        ow_deliver_qty, temu_push_qty, temu_deliver_qty, fba_push_qty, fba_deliver_qty,smt_jit_asn_push_qty,
        smt_jit_asn_deliver_qty
        FROM wh_apv_statistics
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND creation_date = :creation_date]>
        <[AND created_by = :created_by]>
        <[AND count_date = :count_date]>
        <[AND warehouse_id = :warehouse_id]>
        <[AND smt_push_qty = :smt_push_qty]>
        <[AND shopee_push_qty = :shopee_push_qty]>
        <[AND amazon_push_qty = :amazon_push_qty]>
        <[AND lazada_push_qty = :lazada_push_qty]>
        <[AND ebay_push_qty = :ebay_push_qty]>
        <[AND wish_push_qty = :wish_push_qty]>
        <[AND joom_push_qty = :joom_push_qty]>
        <[AND walmart_push_qty = :walmart_push_qty]>
        <[AND other_push_qty = :other_push_qty]>
        <[AND smt_deliver_qty = :smt_deliver_qty]>
        <[AND shopee_deliver_qty = :shopee_deliver_qty]>
        <[AND amazon_deliver_qty = :amazon_deliver_qty]>
        <[AND lazada_deliver_qty = :lazada_deliver_qty]>
        <[AND ebay_deliver_qty = :ebay_deliver_qty]>
        <[AND wish_deliver_qty = :wish_deliver_qty]>
        <[AND joom_deliver_qty = :joom_deliver_qty]>
        <[AND walmart_deliver_qty = :walmart_deliver_qty]>
        <[AND other_deliver_qty = :other_deliver_qty]>
        <[AND count_date >= :start_time]>
        <[AND count_date <= :end_time]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryWhApvStatisticsByPrimaryKey" >
    <content >
      <![CDATA[
        SELECT id, creation_date, created_by, count_date, warehouse_id, smt_push_qty, shopee_push_qty, 
        amazon_push_qty, lazada_push_qty, ebay_push_qty, wish_push_qty, joom_push_qty, walmart_push_qty, 
        other_push_qty, smt_deliver_qty, shopee_deliver_qty, amazon_deliver_qty, lazada_deliver_qty, 
        ebay_deliver_qty, wish_deliver_qty, joom_deliver_qty, walmart_deliver_qty, other_deliver_qty,
        ozon_push_qty, tiktok_push_qty, coupan_push_qty, smt_jit_push_qty, ozon_deliver_qty, coupan_deliver_qty,
        tiktok_deliver_qty, smt_jit_deliver_qty, transfer_deliver_qty, transfer_push_qty, ow_push_qty,
        ow_deliver_qty, temu_push_qty, temu_deliver_qty, fba_push_qty, fba_deliver_qty,smt_jit_asn_push_qty,
        smt_jit_asn_deliver_qty
        FROM wh_apv_statistics
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryWhApvStatistics" >
    <content >
      <![CDATA[
        SELECT id, creation_date, created_by, count_date, warehouse_id, smt_push_qty, shopee_push_qty, 
        amazon_push_qty, lazada_push_qty, ebay_push_qty, wish_push_qty, joom_push_qty, walmart_push_qty, 
        other_push_qty, smt_deliver_qty, shopee_deliver_qty, amazon_deliver_qty, lazada_deliver_qty, 
        ebay_deliver_qty, wish_deliver_qty, joom_deliver_qty, walmart_deliver_qty, other_deliver_qty,
        ozon_push_qty, tiktok_push_qty, coupan_push_qty, smt_jit_push_qty, ozon_deliver_qty, coupan_deliver_qty,
        tiktok_deliver_qty, smt_jit_deliver_qty, transfer_deliver_qty, transfer_push_qty, ow_push_qty,
        ow_deliver_qty, temu_push_qty, temu_deliver_qty, fba_push_qty, fba_deliver_qty,smt_jit_asn_push_qty,
        smt_jit_asn_deliver_qty
        FROM wh_apv_statistics
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND creation_date = :creation_date]>
        <[AND created_by = :created_by]>
        <[AND count_date = :count_date]>
        <[AND warehouse_id = :warehouse_id]>
        <[AND smt_push_qty = :smt_push_qty]>
        <[AND shopee_push_qty = :shopee_push_qty]>
        <[AND amazon_push_qty = :amazon_push_qty]>
        <[AND lazada_push_qty = :lazada_push_qty]>
        <[AND ebay_push_qty = :ebay_push_qty]>
        <[AND wish_push_qty = :wish_push_qty]>
        <[AND joom_push_qty = :joom_push_qty]>
        <[AND walmart_push_qty = :walmart_push_qty]>
        <[AND other_push_qty = :other_push_qty]>
        <[AND smt_deliver_qty = :smt_deliver_qty]>
        <[AND shopee_deliver_qty = :shopee_deliver_qty]>
        <[AND amazon_deliver_qty = :amazon_deliver_qty]>
        <[AND lazada_deliver_qty = :lazada_deliver_qty]>
        <[AND ebay_deliver_qty = :ebay_deliver_qty]>
        <[AND wish_deliver_qty = :wish_deliver_qty]>
        <[AND joom_deliver_qty = :joom_deliver_qty]>
        <[AND walmart_deliver_qty = :walmart_deliver_qty]>
        <[AND other_deliver_qty = :other_deliver_qty]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="createWhApvStatistics" >
    <content >
      <![CDATA[
        INSERT INTO wh_apv_statistics (creation_date, created_by, count_date, warehouse_id, smt_push_qty, shopee_push_qty, 
          amazon_push_qty, lazada_push_qty, ebay_push_qty, wish_push_qty, joom_push_qty, 
          walmart_push_qty, other_push_qty, smt_deliver_qty, shopee_deliver_qty, amazon_deliver_qty, 
          lazada_deliver_qty, ebay_deliver_qty, wish_deliver_qty, joom_deliver_qty, walmart_deliver_qty, 
          other_deliver_qty, ozon_push_qty , tiktok_push_qty, coupan_push_qty, smt_jit_push_qty,
          ozon_deliver_qty, coupan_deliver_qty, tiktok_deliver_qty, smt_jit_deliver_qty,
          transfer_deliver_qty, transfer_push_qty, ow_push_qty, ow_deliver_qty, temu_push_qty, temu_deliver_qty, fba_push_qty, fba_deliver_qty,
          smt_jit_asn_push_qty, smt_jit_asn_deliver_qty)
        VALUES (:creation_date, :created_by, :count_date, :warehouse_id, :smt_push_qty, :shopee_push_qty, 
          :amazon_push_qty, :lazada_push_qty, :ebay_push_qty, :wish_push_qty, :joom_push_qty, 
          :walmart_push_qty, :other_push_qty, :smt_deliver_qty, :shopee_deliver_qty, :amazon_deliver_qty, 
          :lazada_deliver_qty, :ebay_deliver_qty, :wish_deliver_qty, :joom_deliver_qty, :walmart_deliver_qty, 
          :other_deliver_qty, :ozon_push_qty , :tiktok_push_qty, :coupan_push_qty, :smt_jit_push_qty,
          :ozon_deliver_qty , :coupan_deliver_qty, :tiktok_deliver_qty, :smt_jit_deliver_qty,
          :transfer_deliver_qty, :transfer_push_qty, :ow_push_qty, :ow_deliver_qty, :temu_push_qty, :temu_deliver_qty, :fba_push_qty, :fba_deliver_qty,
          :smt_jit_asn_push_qty, :smt_jit_asn_deliver_qty)
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="deleteWhApvStatisticsByPrimaryKey" >
    <content >
      <![CDATA[
        DELETE FROM wh_apv_statistics
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="updateWhApvStatisticsByPrimaryKey" >
    <content >
      <![CDATA[
        UPDATE wh_apv_statistics
        SET <[creation_date = :creation_date,]>
          <[created_by = :created_by,]>
          <[count_date = :count_date,]>
          <[warehouse_id = :warehouse_id,]>
          <[smt_push_qty = :smt_push_qty,]>
          <[shopee_push_qty = :shopee_push_qty,]>
          <[amazon_push_qty = :amazon_push_qty,]>
          <[lazada_push_qty = :lazada_push_qty,]>
          <[ebay_push_qty = :ebay_push_qty,]>
          <[wish_push_qty = :wish_push_qty,]>
          <[joom_push_qty = :joom_push_qty,]>
          <[walmart_push_qty = :walmart_push_qty,]>
          <[other_push_qty = :other_push_qty,]>
          <[smt_deliver_qty = :smt_deliver_qty,]>
          <[shopee_deliver_qty = :shopee_deliver_qty,]>
          <[amazon_deliver_qty = :amazon_deliver_qty,]>
          <[lazada_deliver_qty = :lazada_deliver_qty,]>
          <[ebay_deliver_qty = :ebay_deliver_qty,]>
          <[wish_deliver_qty = :wish_deliver_qty,]>
          <[joom_deliver_qty = :joom_deliver_qty,]>
          <[walmart_deliver_qty = :walmart_deliver_qty,]>
          <[other_deliver_qty = :other_deliver_qty,]>
          <[ozon_push_qty = :ozon_push_qty,]>
          <[tiktok_push_qty = :tiktok_push_qty,]>
          <[coupan_push_qty = :coupan_push_qty,]>
          <[smt_jit_push_qty = :smt_jit_push_qty,]>
          <[ozon_deliver_qty = :ozon_deliver_qty,]>
          <[coupan_deliver_qty = :coupan_deliver_qty,]>
          <[tiktok_deliver_qty = :tiktok_deliver_qty,]>
          <[smt_jit_deliver_qty =:smt_jit_deliver_qty,]>
          <[transfer_deliver_qty = :transfer_deliver_qty,]>
          <[transfer_push_qty = :transfer_push_qty,]>
          <[ow_push_qty = :ow_push_qty,]>
          <[ow_deliver_qty = :ow_deliver_qty,]>
          <[temu_push_qty = :temu_push_qty,]>
          <[temu_deliver_qty = :temu_deliver_qty,]>
          <[fba_push_qty = :fba_push_qty,]>
          <[fba_deliver_qty = :fba_deliver_qty,]>
          <[smt_jit_asn_push_qty = :smt_jit_asn_push_qty,]>
          <[smt_jit_asn_deliver_qty = :smt_jit_asn_deliver_qty,]>
        id = id
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>

  <!--推单量 -->
  <sql datasource="dataSource" id="queryApvPushed">
    <content>
      <![CDATA[
			SELECT
              COUNT(IF(s.code='SMT' and apv.ship_status not in (22,23),1,null)) AS smtPushQty,
              COUNT(IF(s.code='Shopee',1,null)) AS shopeePushQty,
              COUNT(IF(s.code='Amazon',1,null)) AS amazonPushQty,
              COUNT(IF(s.code='Ebay',1,null)) AS ebayPushQty,
              COUNT(IF(s.code='Lazada',1,null)) AS lazadaPushQty,
              COUNT(IF(s.code='Wish',1,null)) AS wishPushQty,
              COUNT(IF(s.code='Joom',1,null)) AS joomPushQty,
              COUNT(IF(s.code='Walmart',1,null)) AS walmartPushQty,
              COUNT(IF(s.code='Ozon',1,null)) AS ozonPushQty,
              COUNT(IF(s.code='Coupang',1,null)) AS coupanPushQty,
              COUNT(IF(s.code='Tiktok',1,null)) AS tiktokPushQty,
              COUNT(IF(s.code='SMT' and apv.ship_status in (22,23),1,null)) AS smtJitPushQty,
              COUNT(IF(s.code not in ('Shopee','SMT','Amazon','Ebay','Lazada','Wish','Joom','Walmart','Ozon','Coupang','Tiktok'),1,null)) AS otherPushQty
              FROM wh_apv apv
              LEFT JOIN salechannel s ON apv.platform=s.id
              WHERE 1 = 1
        	<[AND apv.creation_date >= :start_time]>
	    	<[AND apv.creation_date < :end_time]>
      ]]>
    </content>
  </sql>

  <!--发货订单量 -->
  <sql datasource="dataSource" id="queryApvShiped">
    <content>
      <![CDATA[
            SELECT
      		  COUNT(IF(s.code='SMT' and apv.ship_status not in (22,23),1,null)) AS smtDeliverQty,
              COUNT(IF(s.code='Shopee',1,null)) AS shopeeDeliverQty,
              COUNT(IF(s.code='Amazon',1,null)) AS amazonDeliverQty,
              COUNT(IF(s.code='Ebay',1,null)) AS ebayDeliverQty,
              COUNT(IF(s.code='Lazada',1,null)) AS lazadaDeliverQty,
              COUNT(IF(s.code='Wish',1,null)) AS wishDeliverQty,
              COUNT(IF(s.code='Joom',1,null)) AS joomDeliverQty,
              COUNT(IF(s.code='Walmart',1,null)) AS walmartDeliverQty,
              COUNT(IF(s.code='Ozon',1,null)) AS ozonDeliverQty,
              COUNT(IF(s.code='Coupang',1,null)) AS coupanDeliverQty,
              COUNT(IF(s.code='Tiktok',1,null)) AS tiktokDeliverQty,
              COUNT(IF(s.code='SMT' and apv.ship_status in (22,23),1,null)) AS smtJitDeliverQty,
              COUNT(IF(s.code not in ('Shopee','SMT','Amazon','Ebay','Lazada','Wish','Joom','Walmart','Ozon','Coupang','Tiktok'),1,null)) AS otherDeliverQty
              FROM wh_apv apv
              LEFT JOIN salechannel s ON apv.platform=s.id
              INNER JOIN apv_track t on apv.apv_no = t.apv_no
              WHERE apv.status in (17,18)
			<[AND t.deliver_time >= :start_time]>
			<[AND t.deliver_time < :end_time]>
      ]]>
    </content>
  </sql>
  <sql datasource="dataSource" id="queryFbaPushData">
    <content>
      <![CDATA[
            SELECT
      		  COUNT(IF((apv.purpose_house, ae.package_method) IN (('Shein', 8), ('Shein', 9)),1,null)) AS transferPushQty,
      		  COUNT(IF((apv.purpose_house, ae.package_method) IN (('SMT', 4), ('SMT', 7)),1,null)) AS smtJitAsnPushQty,
      		  COUNT(IF(<[apv.purpose_house NOT IN (:purposeHouseNotInList)]>,1,null)) AS fbaPushQty,
              COUNT(IF(<[apv.purpose_house IN (:purposeHouseNotInList)]> AND (apv.purpose_house, ae.package_method) NOT IN (('SMT', 4), ('SMT', 7), ('Shein', 8), ('Shein', 9)),1,null)) AS owPushQty
              FROM wh_fba_allocation apv
              LEFT JOIN wh_asn_extra ae ON ae.wh_asn_id=apv.id
              WHERE 1 = 1
			<[AND apv.push_time >= :start_time]>
			<[AND apv.push_time < :end_time]>
      ]]>
    </content>
  </sql>

  <sql datasource="dataSource" id="queryFbaDeliverData">
    <content>
      <![CDATA[
            SELECT
      		  COUNT(IF((apv.purpose_house, ae.package_method) IN (('Shein', 8), ('Shein', 9)),1,null)) AS transferDeliverQty,
      		  COUNT(IF((apv.purpose_house, ae.package_method) IN (('SMT', 4), ('SMT', 7)),1,null)) AS smtJitAsnDeliverQty,
      		  COUNT(IF(<[apv.purpose_house NOT IN (:purposeHouseNotInList)]>,1,null)) AS fbaDeliverQty,
              COUNT(IF(<[apv.purpose_house IN (:purposeHouseNotInList)]> AND (apv.purpose_house, ae.package_method) NOT IN (('SMT', 4), ('SMT', 7), ('Shein', 8), ('Shein', 9)),1,null)) AS owDeliverQty
              FROM wh_fba_allocation apv
              LEFT JOIN wh_asn_extra ae ON ae.wh_asn_id=apv.id
              WHERE 1 = 1
			<[AND apv.push_time >= :start_time]>
			<[AND apv.push_time < :end_time]>
			<[AND apv.status in (:statusList)]>
      ]]>
    </content>
  </sql>

  <sql datasource="dataSource" id="queryTemuPushData">
    <content>
      <![CDATA[
            SELECT
      		  COUNT(IF(i.sku IS NOT NULL,1,null)) AS temuPushQty
              FROM temu_prepare_order t
              LEFT JOIN temu_prepare_order_item i ON t.id=i.prepare_order_id
              WHERE 1 = 1
			<[AND t.push_time >= :start_time]>
			<[AND t.push_time < :end_time]>
      ]]>
    </content>
  </sql>


  <sql datasource="dataSource" id="queryTemuDeliverData">
    <content>
      <![CDATA[
            SELECT
      		  COUNT(IF(i.sku IS NOT NULL,1,null)) AS temuDeliverQty
      		  FROM temu_prepare_order t
              LEFT JOIN temu_prepare_order_item i ON t.id=i.prepare_order_id
              WHERE 1 = 1
			<[AND i.deliver_time >= :start_time]>
			<[AND i.deliver_time < :end_time]>
			<[AND i.package_status in (:statusList)]>
      ]]>
    </content>
  </sql>

</sqlmap>