<?xml version="1.0" encoding="UTF-8" ?>
<sqlmap >
  <sql datasource="dataSource" id="queryWhMergeApvListCount" >
    <content >
      <![CDATA[
        SELECT COUNT(1)
        FROM wh_apv wa
        WHERE 1 = 1
        <[AND wa.id = :id]>
        <[AND wa.id IN (:apv_id_list)]>
        <[AND wa.status = :status]>
        <[AND wa.status IN (:status_list)]>
        <[AND wa.buyer_checkout = :buyer_checkout]>
        <[:warehouse_type]>
        <[AND wa.id IN (SELECT inner_item.apv_id from wh_apv_item inner_item 
							LEFT JOIN wh_sku inner_sku ON inner_item.sku = inner_sku.sku 
							where inner_sku.warehouse_id = :warehouse_id)]>
	    <[AND wa.logistics_company IN (:big_waybill_type)]>
        <[AND wa.logistics_company NOT IN (:small_waybill_type)]>
 		<[:whapv_lock]>
 		<[:create_task]>
 		<[AND wa.id IN (SELECT inner_item.apv_id from wh_apv_item inner_item where inner_item.sku = :sku)]>
 		<[AND wa.ship_status = :ship_status]>
 		<[AND wa.ship_status IN (:ship_status_list)]>
 		<[AND wa.ship_status NOT IN (:exclude_ship_status_list)]>
 		<[AND wa.apv_no = :apv_no]>
 		<[AND wa.apv_no IN (:apv_no_list)]>
 		<[AND wa.logistics_company = :logistics_company]>
 		<[AND wa.platform = :platform]>
	    <[AND wa.platform IN (:platform_list)]>
	    <[AND wa.creation_date >= :from_create_date]>
	    <[AND wa.creation_date <= :to_create_date]>
 		<[:is_exist_tracking_number]>
 		<[AND wa.original_order_id >= :start_frequency]>
	    <[AND wa.original_order_id <= :end_frequency]>
	    <[AND wa.id IN (SELECT task_item.apv_id from wh_picking_task_item task_item 
	    				LEFT JOIN wh_picking_task task ON task.id = task_item.task_id
						LEFT JOIN wh_box box ON box.relation_no = task.id
						where box.box_no = :box_no )]>
		<[AND wa.id IN (SELECT inner_item.apv_id from wh_apv_item inner_item where inner_item.sku = :sku_reissue_parts)]>
		<[AND wa.seller_id IN (:SHOP_LIST)]>
		<[:EXCLUDE_SHOP_LIST]>
      	<[:ORDER_ORIGIN]>
      	<[:area_condition]>
      	<[:areaAndAccessCondition]>
   		<[:access_condition]>
   		<[:location_number_not_null]>
   		<[:buyer_checkout_sql]>
   		<[:OVERTIME_SQL]>
        <[:FROM_REMAIN_TIME_SQL]>
        <[:TO_REMAIN_TIME_SQL]>
        <[:EXCLUDE_GPSR]>
      ]]>
    </content>
  </sql>  
  
  <sql datasource="dataSource" id="queryWhMergeApvList" >
    <content >
      <![CDATA[
        SELECT 
        	wa.id,
        	wa.apv_no,
        	wa.original_order_id,
        	wa.status,
        	wa.sign_payment,
        	wa.platform_order_id,
        	wa.buyer_id,
        	wa.buyer_name,
        	wa.buyer_email,
        	wa.buyer_country,
        	wa.ship_status,
        	wa.ship_service,
        	wa.apv_desc,
        	wa.logistics_company,
        	wa.tracking_number,
        	wa.sales_record_number,
        	wa.buyer_checkout,
        	wa.paid_date,
        	wa.platform,
        	wa.last_update_date,
        	wa.last_modified_time,
        	(SELECT load_date FROM apv_track WHERE apv_track.apv_no = wa.apv_no) AS 'apvTrack.load_date',
        	wal.status,
        	
        	wai.id,
        	wai.sku,
        	wai.sale_quantity,
        	
		   	ws.sku, 
		   	ws.name, 
		   	ws.warehouse_id,
		   	ws.location_number
        
        FROM wh_apv wa
        LEFT JOIN wh_apv_lock wal ON wa.id = wal.apv_id
        LEFT JOIN wh_apv_item wai ON wa.id = wai.apv_id
		LEFT JOIN wh_sku ws ON wai.sku = ws.sku 
		INNER JOIN (
			SELECT wa.id
			FROM wh_apv wa
			WHERE 1=1
			<[AND wa.id = :id]>
        	<[AND wa.id IN (:apv_id_list)]>
			<[AND wa.status = :status]>
			<[AND wa.status IN (:status_list)]>
			<[AND wa.buyer_checkout = :buyer_checkout]>
			<[:warehouse_type]>
			<[AND wa.id IN (SELECT inner_item.apv_id from wh_apv_item inner_item 
							LEFT JOIN wh_sku inner_sku ON inner_item.sku = inner_sku.sku 
							where inner_sku.warehouse_id = :warehouse_id)]>
		    <[AND wa.logistics_company IN (:big_waybill_type)]>
	        <[AND wa.logistics_company NOT IN (:small_waybill_type)]>
	 		<[:whapv_lock]>
	 		<[:create_task]>
	 		<[AND wa.id IN (SELECT inner_item.apv_id from wh_apv_item inner_item where inner_item.sku = :sku)]>
	 		<[AND wa.ship_status = :ship_status]>
	 		<[AND wa.ship_status IN (:ship_status_list)]>
	 		<[AND wa.ship_status NOT IN (:exclude_ship_status_list)]>
	 		<[AND wa.apv_no = :apv_no]>
	 		<[AND wa.apv_no IN (:apv_no_list)]>
	 		<[AND wa.logistics_company = :logistics_company]>
	 		<[AND wa.platform = :platform]>
		    <[AND wa.platform IN (:platform_list)]>
		    <[AND wa.creation_date >= :from_create_date]>
		    <[AND wa.creation_date <= :to_create_date]>
	 		<[:is_exist_tracking_number]>
	 		<[AND wa.original_order_id >= :start_frequency]>
		    <[AND wa.original_order_id <= :end_frequency]>
		    <[AND wa.id IN (SELECT task_item.apv_id from wh_picking_task_item task_item 
		    				LEFT JOIN wh_picking_task task ON task.id = task_item.task_id
							LEFT JOIN wh_box box ON box.relation_no = task.id
							where box.box_no = :box_no )]>	
			<[AND wa.id IN (SELECT inner_item.apv_id from wh_apv_item inner_item where inner_item.sku = :sku_reissue_parts)]>
			<[AND wa.seller_id IN (:SHOP_LIST)]>
			<[:EXCLUDE_SHOP_LIST]>
			<[:ORDER_ORIGIN]>
			<[:area_condition]>
			<[:areaAndAccessCondition]>
			<[:access_condition]>
			<[:location_number_not_null]>
			<[:buyer_checkout_sql]>
			<[:OVERTIME_SQL]>
            <[:FROM_REMAIN_TIME_SQL]>
            <[:TO_REMAIN_TIME_SQL]>
            <[:EXCLUDE_GPSR]>
			<[:LIMIT]>
			)  whapv on wa.id = whapv.id
        WHERE 1 = 1
        <[:ORDER_BY]>
						
      ]]>
    </content>
  </sql> 
  
  <sql datasource="dataSource" id="getSaleChannels" >
    <content >
      <![CDATA[
      	select 
      		id, name, code, deliverypriority
      	from salechannel 
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryWhMergeApvIdList" >
    <content >
      <![CDATA[
        SELECT wa.id
        FROM wh_apv wa
        LEFT JOIN wh_apv_lock wal ON wa.id = wal.apv_id
        LEFT JOIN wh_apv_item wai ON wa.id = wai.apv_id
		LEFT JOIN wh_sku ws ON wai.sku = ws.sku 
        WHERE 1 = 1
        <[AND wa.id = :id]>
       	<[AND wa.id IN (:apv_id_list)]>
        <[AND wa.status = :status]>
        <[:warehouse_type]>
        <[AND wa.id IN (SELECT inner_item.apv_id from wh_apv_item inner_item 
							LEFT JOIN wh_sku inner_sku ON inner_item.sku = inner_sku.sku 
							where inner_sku.warehouse_id = :warehouse_id)]>
	    <[AND wa.logistics_company IN (:big_waybill_type)]>
        <[AND wa.logistics_company NOT IN (:small_waybill_type)]>
 		<[:whapv_lock]>
 		<[:create_task]>
 		<[AND wa.ship_status = :ship_status]>
 		<[AND wa.ship_status IN (:ship_status_list)]>
 		<[AND wa.ship_status NOT IN (:exclude_ship_status_list)]>
 		<[AND wa.apv_no = :apv_no]>
 		<[AND wa.apv_no IN (:apv_no_list)]>
 		<[AND wa.logistics_company = :logistics_company]>
 		<[AND wa.platform = :platform]>
	    <[AND wa.platform IN (:platform_list)]>
	    <[AND wa.creation_date >= :from_create_date]>
	    <[AND wa.creation_date <= :to_create_date]>
 		<[:is_exist_tracking_number]>
 		<[AND wa.original_order_id >= :start_frequency]>
	    <[AND wa.original_order_id <= :end_frequency]>
	    <[AND wa.id IN (SELECT task_item.apv_id from wh_picking_task_item task_item 
	    				LEFT JOIN wh_picking_task task ON task.id = task_item.task_id
						LEFT JOIN wh_box box ON box.relation_no = task.id
						where box.box_no = :box_no )]>				
      ]]>
    </content>
  </sql>  
  
  
  
  <!-- 根据销售数量获取sku集合 -->
  <sql datasource="dataSource" id="getSKUListBySaleQuatity" >
    <content >
      <![CDATA[
        SELECT wai.sku as sku,COUNT(wai.sale_quantity) as sale_quantity 
        		from wh_apv wa 
                LEFT JOIN wh_apv_item wai on wai.apv_id = wa.id
				where 1 = 1 
				<[AND wa.id = :id]>
	        	<[AND wa.id IN (:apv_id_list)]>
				<[AND wa.status = :status]>
				<[:warehouse_type]>
				<[AND wa.id IN (SELECT inner_item.apv_id from wh_apv_item inner_item 
								LEFT JOIN wh_sku inner_sku ON inner_item.sku = inner_sku.sku 
								where inner_sku.warehouse_id = :warehouse_id)]>
			    <[AND wa.logistics_company IN (:big_waybill_type)]>
		        <[AND wa.logistics_company NOT IN (:small_waybill_type)]>
		 		<[:whapv_lock]>
		 		<[:create_task]>
		 		<[AND wa.ship_status = :ship_status]>
		 		<[AND wa.ship_status IN (:ship_status_list)]>
		 		<[AND wa.ship_status NOT IN (:exclude_ship_status_list)]>
		 		<[AND wa.apv_no = :apv_no]>
		 		<[AND wa.apv_no IN (:apv_no_list)]>
		 		<[AND wa.logistics_company = :logistics_company]>
		 		<[AND wa.platform = :platform]>
			    <[AND wa.platform IN (:platform_list)]>
			    <[AND wa.creation_date >= :from_create_date]>
			    <[AND wa.creation_date <= :to_create_date]>
		 		<[:is_exist_tracking_number]>
		 		<[AND wa.original_order_id >= :start_frequency]>
			    <[AND wa.original_order_id <= :end_frequency]>
			    <[AND wa.id IN (SELECT task_item.apv_id from wh_picking_task_item task_item 
			    				LEFT JOIN wh_picking_task task ON task.id = task_item.task_id
								LEFT JOIN wh_box box ON box.relation_no = task.id
								where box.box_no = :box_no )]>
				<[AND wai.sku in (:skus)]>				
				<[:rx_status]>					
				GROUP BY wai.sku
				<[HAVING COUNT(wai.sale_quantity) >= :startSalesQuatity ORDER BY COUNT(wai.sale_quantity) DESC]>
  	  ]]>
    </content>
  </sql>
  
  <!-- 根据SKU获取apv和sku集合 -->
  <sql datasource="dataSource" id="getApvListBySku" >
    <content >
      <![CDATA[
        select 
        wa.id,
        wa.apv_no,
        wa.original_order_id,
        wa.ship_status,
        wai.sku,
        wai.sale_quantity,
        ws.location_number 
        from wh_apv wa
		LEFT JOIN wh_apv_item wai on wai.apv_id = wa.id
		LEFT JOIN wh_sku ws on ws.sku = wai.sku 
		where 1 = 1 
		<[AND wa.id = :id]>
       	<[AND wa.id IN (:apv_id_list)]>
		<[AND wa.status = :status]>
		<[:warehouse_type]>
		<[AND wa.id IN (SELECT inner_item.apv_id from wh_apv_item inner_item 
						LEFT JOIN wh_sku inner_sku ON inner_item.sku = inner_sku.sku 
						where inner_sku.warehouse_id = :warehouse_id)]>
	    <[AND wa.logistics_company IN (:big_waybill_type)]>
        <[AND wa.logistics_company NOT IN (:small_waybill_type)]>
 		<[:whapv_lock]>
 		<[:create_task]>
 		<[AND wa.ship_status = :ship_status]>
 		<[AND wa.ship_status IN (:ship_status_list)]>
 		<[AND wa.ship_status NOT IN (:exclude_ship_status_list)]>
 		<[AND wa.apv_no = :apv_no]>
 		<[AND wa.apv_no IN (:apv_no_list)]>
 		<[AND wa.logistics_company = :logistics_company]>
 		<[AND wa.platform = :platform]>
	    <[AND wa.platform IN (:platform_list)]>
	    <[AND wa.creation_date >= :from_create_date]>
	    <[AND wa.creation_date <= :to_create_date]>
 		<[:is_exist_tracking_number]>
 		<[AND wa.original_order_id >= :start_frequency]>
	    <[AND wa.original_order_id <= :end_frequency]>
	    <[AND wa.id IN (SELECT task_item.apv_id from wh_picking_task_item task_item 
	    				LEFT JOIN wh_picking_task task ON task.id = task_item.task_id
						LEFT JOIN wh_box box ON box.relation_no = task.id
						where box.box_no = :box_no )]>	
		<[AND wai.sku in (:skus)]>
		<[:rx_status]>
		<[:area_condition]>
		<[:areaAndAccessCondition]>
   		<[:access_condition]>
   		<[:location_number_not_null]>
   		<[:EXCLUDE_GPSR]>
  	  ]]>
    </content>
  </sql>
</sqlmap>