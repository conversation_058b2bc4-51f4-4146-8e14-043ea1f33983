<?xml version="1.0" encoding="UTF-8" ?>
<sqlmap >
  <sql datasource="dataSource" id="queryWhApvBatchCount" >
    <content >
      <![CDATA[
        SELECT COUNT(1)
        FROM wh_apv_batch
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND batch_type = :batch_type]>
        <[AND serial_number = :serial_number]>
        <[AND batch_status = :batch_status]>
        <[AND is_locked = :is_locked]>
        <[AND floor = :floor]>
        <[AND floor_type = :floor_type]>
        <[AND picker = :picker]>
        <[AND created_by = :created_by]>
        <[AND creation_date = :creation_date]>
      ]]>
    </content>
  </sql>
  
  
  <sql datasource="dataSource" id="searchWhApvBatchCount" >
    <content >
      <![CDATA[
        SELECT 
		    COUNT(1) FROM (
		SELECT DISTINCT apv_batch.id
        FROM wh_apv_batch apv_batch
        LEFT JOIN wh_apv_batch_item batch_item ON apv_batch.id = batch_item.batch_id
        WHERE 1 = 1
       	<[AND apv_batch.id = :id]>
       	<[AND apv_batch.id IN (:batch_id_list)]>
        <[AND apv_batch.batch_type = :batch_type]>
        <[AND apv_batch.serial_number = :serial_number]>
        <[AND apv_batch.batch_status = :batch_status]>
        <[AND apv_batch.is_locked = :is_locked]>
        <[AND apv_batch.floor = :floor]>
        <[AND apv_batch.floor_type = :floor_type]>
        <[AND apv_batch.picker = :picker]>
        <[AND apv_batch.created_by = :created_by]>
        <[AND apv_batch.creation_date = :creation_date]>
        <[AND apv_batch.creation_date >= :from_creation_date]>
        <[AND apv_batch.creation_date <= :to_creation_date]>
        
        <[AND apv_batch.id IN (SELECT apv_batch_detail.batch_id FROM wh_apv_batch_detail apv_batch_detail WHERE apv_batch_detail.apv_id = :apv_id)]>
        <[AND apv_batch.id IN (SELECT apv_batch_detail.batch_id FROM wh_apv_batch_detail apv_batch_detail WHERE apv_batch_detail.apv_id IN (:apv_id_list))]>
        
        
        ) TEMP
      ]]>
    </content>
  </sql>
  
  
  <sql datasource="dataSource" id="queryWhApvBatchList" >
    <content >
      <![CDATA[
        SELECT id, batch_type, serial_number, batch_status, is_locked, floor, floor_type, 
        picker, created_by, creation_date
        FROM wh_apv_batch
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND batch_type = :batch_type]>
        <[AND serial_number = :serial_number]>
        <[AND batch_status = :batch_status]>
        <[AND is_locked = :is_locked]>
        <[AND floor = :floor]>
        <[AND floor_type = :floor_type]>
        <[AND picker = :picker]>
        <[AND created_by = :created_by]>
        <[AND creation_date = :creation_date]>
        Order by creation_date DESC
      ]]>
    </content>
  </sql>
  
  
   <sql datasource="dataSource" id="searchWhApvBatchs" >
    <content >
      <![CDATA[
        SELECT 
        	apv_batch.id, apv_batch.batch_type, apv_batch.serial_number, apv_batch.batch_status, apv_batch.is_locked, apv_batch.floor, 
        	apv_batch.floor_type, apv_batch.picker, apv_batch.created_by, apv_batch.creation_date,
        	
        	batch_item.id, batch_item.warehouse_id, batch_item.item_status, 
	        SUM(CASE WHEN(apv.id) THEN 1 ELSE 0 END) AS apv_count, 
			SUM(CASE WHEN(apv.`status`=14) THEN 1 ELSE 0 END) AS apv_status_14, 
			SUM(CASE WHEN(apv.`status`=16) THEN 1 ELSE 0 END) AS apv_status_16, 
			SUM(CASE WHEN(apv.`status`=17) THEN 1 ELSE 0 END) AS apv_status_17
        FROM wh_apv_batch apv_batch 
        INNER JOIN(
         SELECT id FROM wh_apv_batch inner_apv_batch
        WHERE 1 = 1
        <[AND inner_apv_batch.id = :id]>
       	<[AND inner_apv_batch.id IN (:batch_id_list)]>
        <[AND inner_apv_batch.batch_type = :batch_type]>
        <[AND inner_apv_batch.serial_number = :serial_number]>
        <[AND inner_apv_batch.batch_status = :batch_status]>
        <[AND inner_apv_batch.is_locked = :is_locked]>
        <[AND inner_apv_batch.floor = :floor]>
        <[AND inner_apv_batch.floor_type = :floor_type]>
        <[AND inner_apv_batch.picker = :picker]>
        <[AND inner_apv_batch.created_by = :created_by]>
        <[AND inner_apv_batch.creation_date = :creation_date]>
        <[AND inner_apv_batch.creation_date >= :from_creation_date]>
        <[AND inner_apv_batch.creation_date <= :to_creation_date]>
        
        <[AND inner_apv_batch.id IN (SELECT apv_batch_detail.batch_id FROM wh_apv_batch_detail apv_batch_detail WHERE apv_batch_detail.apv_id = :apv_id)]>
        <[AND inner_apv_batch.id IN (SELECT apv_batch_detail.batch_id FROM wh_apv_batch_detail apv_batch_detail WHERE apv_batch_detail.apv_id IN (:apv_id_list))]>
        
        <[:LIMIT]>
        ) 
        X ON (apv_batch.id = X.id)
        LEFT JOIN wh_apv_batch_item batch_item ON apv_batch.id = batch_item.batch_id
        LEFT JOIN wh_apv_batch_detail batch_detail ON batch_detail.batch_id = apv_batch.id
		LEFT JOIN wh_apv apv ON apv.id = batch_detail.apv_id
			
		GROUP BY apv_batch.serial_number
		Order by apv_batch.creation_date DESC
      ]]>
    </content>
  </sql>
  
  
  <sql datasource="dataSource" id="queryWhApvBatchByPrimaryKey" >
    <content >
      <![CDATA[
        SELECT id, batch_type, serial_number, batch_status, is_locked, floor, floor_type, 
        picker, created_by, creation_date
        FROM wh_apv_batch
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryWhApvBatch" >
    <content >
      <![CDATA[
        SELECT id, batch_type, serial_number, batch_status, is_locked, floor, floor_type, 
        picker, created_by, creation_date
        FROM wh_apv_batch
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND batch_type = :batch_type]>
        <[AND serial_number = :serial_number]>
        <[AND batch_status = :batch_status]>
        <[AND is_locked = :is_locked]>
        <[AND floor = :floor]>
        <[AND floor_type = :floor_type]>
        <[AND picker = :picker]>
        <[AND created_by = :created_by]>
        <[AND creation_date = :creation_date]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="createWhApvBatch" >
    <content >
      <![CDATA[
        INSERT INTO wh_apv_batch (batch_type, serial_number, batch_status, is_locked, floor, floor_type, 
          picker, created_by, creation_date)
        VALUES (:batch_type, :serial_number, :batch_status, :is_locked, :floor, :floor_type, 
          :picker, :created_by, :creation_date)
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="deleteWhApvBatchByPrimaryKey" >
    <content >
      <![CDATA[
        DELETE FROM wh_apv_batch
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="updateWhApvBatchByPrimaryKey" >
    <content >
      <![CDATA[
        UPDATE wh_apv_batch
        SET <[batch_type = :batch_type,]>
          <[serial_number = :serial_number,]>
          <[batch_status = :batch_status,]>
          <[is_locked = :is_locked,]>
          <[floor = :floor,]>
          <[floor_type = :floor_type,]>
          <[picker = :picker,]>
          <[created_by = :created_by,]>
          <[creation_date = :creation_date,]>
        id = id
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  
  <!--   查询批次订单  -->
  <sql datasource="dataSource" id="queryWhApvBatchWhApvList" >
    <content >
      <![CDATA[
        SELECT 
        	apv.id, apv.apv_no, apv.platform, apv.platform_order_id, apv.original_order_id, apv.copy_type, apv.resend_type, 
	        apv.split_type, apv.apv_type, apv.apv_status, apv.total_currency, apv.total_price, apv.fee_or_credit, 
	        apv.sale_date, apv.paid_date, apv.print_date, apv.deliver_date, apv.last_modified_time, apv.seller_id, apv.seller_email, 
	        apv.seller_pay, apv.buyer_id, apv.buyer_name, apv.buyer_mobile, apv.buyer_tel, apv.buyer_country_code, apv.buyer_country, 
	        apv.buyer_postal_code, apv.buyer_street, apv.buyer_alley, apv.buyer_city, apv.buyer_state_or_province, 
	        apv.buyer_email, apv.buyer_checkout, apv.third_party_transaction_id, apv.payment_status, apv.is_payment, 
	        apv.is_refund, apv.complete_status, apv.sales_record_number, apv.logistics_company, apv.ship_service, 
	        apv.ship_freight, apv.ship_status, apv.ship_freight_cost, apv.is_shipment, apv.shipment_time, apv.logistics_type, 
	        apv.tracking_number, apv.service_provider_no, apv.sign_deliver_goods, apv.sign_out_treasury, apv.sign_transport, 
	        apv.sign_contact_buyers, apv.sign_customer_reviews, apv.sign_payment, apv.sign_refund, apv.sign_press_money, 
	        apv.sign_distribution_goods, apv.actual_weight, apv.system_price, apv.status, apv.extends_status, apv.creation_date, 
	        apv.created_by, apv.last_update_date, apv.last_updated_by, apv.apv_desc,
        	
        	apv_item.id, apv_item.apv_id, apv_item.apv_line_item_id, apv_item.transaction_id, apv_item.site, apv_item.sku_id, apv_item.sku_title, apv_item.sku, 
	        apv_item.salesperson, apv_item.currency, apv_item.sku_price, apv_item.sale_quantity, apv_item.pick_quantity, apv_item.sale_price, apv_item.transaction_cost, apv_item.final_transaction_cost, 
	        apv_item.sales_record_number, apv_item.logistics_tracking_number, apv_item.shipping_carrier_used, apv_item.multi_attr, 
	        apv_item.buyer_checkout, apv_item.created_date, apv_item.last_update_date, apv_item.last_updated_by, apv_item.item_desc, apv_item.salesperson,
        	
        	sku.id, sku.sku, sku.image_url, sku.weight, sku.name, sku.warehouse_id, sku.location_number, sku.floor_location,
        	
        	(SELECT name FROM wh_packaging_material_management WHERE id= sku.packaging_id) as material,
			(SELECT GROUP_CONCAT(DISTINCT(name)) FROM wh_packaging_material_management WHERE FIND_IN_SET(id,sku.match_materials_code)) as matchMaterials
		   	
        FROM wh_apv_batch apv_batch 
       	LEFT JOIN wh_apv_batch_detail batch_detail ON apv_batch.id = batch_detail.batch_id
        LEFT JOIN wh_apv apv ON batch_detail.apv_id = apv.id
        LEFT JOIN wh_apv_item apv_item ON apv.id = apv_item.apv_id
		LEFT JOIN wh_sku sku ON apv_item.sku = sku.sku 

        WHERE 1 = 1 
        <[AND apv_batch.serial_number = :serial_number]>
        <[AND apv.id = :id]>
        <[AND apv.id IN (:apv_id_list)]>
        <[AND apv.apv_no = :apv_no]>
        <[AND apv.platform = :platform]>
        <[AND apv.platform_order_id = :platform_order_id]>
        <[AND apv.original_order_id = :original_order_id]>
        <[AND apv.original_order_id >= :then_hz]>
        <[AND apv.original_order_id <= :less_hz]>
        <[AND apv.copy_type = :copy_type]>
        <[AND apv.resend_type = :resend_type]>
        <[AND apv.split_type = :split_type]>
        <[AND apv.order_type = :order_type]>
        <[AND apv.order_status = :order_status]>
        <[AND apv.total_currency = :total_currency]>
        <[AND apv.total_price = :total_price]>
        <[AND apv.fee_or_credit = :fee_or_credit]>
        <[AND apv.sale_date = :sale_date]>
        <[AND apv.paid_date = :paid_date]>
        <[AND apv.print_date = :print_date]>
        <[AND apv.deliver_date = :deliver_date]>
        <[AND apv.last_modified_time = :last_modified_time]>
        <[AND apv.seller_id = :seller_id]>
        <[AND apv.seller_email = :seller_email]>
        <[AND apv.seller_pay = :seller_pay]>
        <[AND apv.buyer_id = :buyer_id]>
        <[AND apv.buyer_name = :buyer_name]>
        <[AND apv.buyer_mobile = :buyer_mobile]>
        <[AND apv.buyer_tel = :buyer_tel]>
        <[AND apv.buyer_country_code = :buyer_country_code]>
        <[AND apv.buyer_country = :buyer_country]>
        <[AND apv.buyer_postal_code = :buyer_postal_code]>
        <[AND apv.buyer_street = :buyer_street]>
        <[AND apv.buyer_alley = :buyer_alley]>
        <[AND apv.buyer_city = :buyer_city]>
        <[AND apv.buyer_state_or_province = :buyer_state_or_province]>
        <[AND apv.buyer_email = :buyer_email]>
        <[AND apv.buyer_checkout = :buyer_checkout]>
        <[AND apv.third_party_transaction_id = :third_party_transaction_id]>
        <[AND apv.payment_status = :payment_status]>
        <[AND apv.is_payment = :is_payment]>
        <[AND apv.is_refund = :is_refund]>
        <[AND apv.complete_status = :complete_status]>
        <[AND apv.sales_record_number = :sales_record_number]>
        <[AND apv.logistics_company = :logistics_company]>
        <[AND apv.ship_service = :ship_service]>
        <[AND apv.ship_freight = :ship_freight]>
        <[AND apv.ship_status = :ship_status]>
        <[AND apv.ship_freight_cost = :ship_freight_cost]>
        <[AND apv.is_shipment = :is_shipment]>
        <[AND apv.shipment_time = :shipment_time]>
        <[AND apv.logistics_type = :logistics_type]>
        <[AND apv.tracking_number = :tracking_number]>
        <[AND apv.service_provider_no = :service_provider_no]>
        <[AND apv.sign_deliver_goods = :sign_deliver_goods]>
        <[AND apv.sign_out_treasury = :sign_out_treasury]>
        <[AND apv.sign_transport = :sign_transport]>
        <[AND apv.sign_contact_buyers = :sign_contact_buyers]>
        <[AND apv.sign_customer_reviews = :sign_customer_reviews]>
        <[AND apv.sign_payment = :sign_payment]>
        <[AND apv.sign_refund = :sign_refund]>
        <[AND apv.sign_press_money = :sign_press_money]>
        <[AND apv.sign_distribution_goods = :sign_distribution_goods]>
        <[AND apv.actual_weight = :actual_weight]>
        <[AND apv.system_price = :system_price]>
        <[AND apv.status = :status]>
        <[AND apv.status in(:status_list)]>
        <[AND apv.extends_status = :extends_status]>
        <[AND apv.creation_date = :creation_date]>
        <[AND apv.created_by = :created_by]>
        <[AND apv.last_update_date = :last_update_date]>
        <[AND apv.last_updated_by = :last_updated_by]>
        <[AND apv.apv_desc = :apv_desc]>
		<[:PAID_BUT_NOT_SIGN_SHIPPED]>
        <[:PAID_BUT_NOT_OUTSTOCK]>
        <[:NOT_IN_APV_ID_LIST]>
        
        <[:SKUS_AND]>
        
        <[:NO_DELIVER]>
        <[:lock_status]>
        
        <[AND apv.id IN (SELECT inner_item.apv_id from wh_apv_item inner_item where inner_item.apv_id = apv.id GROUP BY inner_item.apv_id HAVING SUM(inner_item.sale_quantity) = :single_order_item)]>
	    <[AND apv.id NOT IN (SELECT inner_item.apv_id from wh_apv_item inner_item where inner_item.apv_id = apv.id GROUP BY inner_item.apv_id HAVING SUM(inner_item.sale_quantity) = :multi_order_item)]>
	    
	    <[AND apv.id IN (SELECT inner_item.apv_id from wh_apv_item inner_item LEFT JOIN wh_sku inner_sku ON inner_item.sku = inner_sku.sku where inner_sku.warehouse_id IN (:warehouse_id_list))]>
	    <[AND apv.id IN (SELECT inner_item.apv_id from wh_apv_item inner_item LEFT JOIN wh_sku inner_sku ON inner_item.sku = inner_sku.sku where inner_sku.warehouse_id = :warehouse_id)]>
        
        <[AND apv.id IN (SELECT inner_item.apv_id FROM wh_apv_item inner_item WHERE inner_item.apv_id = apv.id AND inner_item.sku = :sku)]>
        <[:SINGLE_ITEM_MORE_QUANTITY]>
        <[:MULTI_ITEM_ONE_QUANTITY]>
        <[:MULTI_ITEM_MORE_QUANTITY]>
        
        <[:is_exist_tracking_number]>
        
        <[:allot_task]>
        
        <[:ORDER_BY]>
      ]]>
    </content>
  </sql>

  <!--   查询批次订单  -->
  <sql datasource="dataSource" id="queryBatchWhFbaAllocationList" >
    <content >
      <![CDATA[
        SELECT fa.id, fa.fba_no, fa.bat_no, fa.status, fa.push_time,fa.purpose_house, fai.id, fai.fba_id,fai.sku_quantity,fai.grid_status,fai.suit_flag,
        fai.product_sku, fai.store, fai.fn_sku, fai.sell_sku,fai.quantity, fai.pick_quantity, fai.site, fai.grid_quantity, fai.allot_quantity
        FROM wh_apv_batch apv_batch
        LEFT JOIN wh_apv_batch_detail batch_detail ON apv_batch.id = batch_detail.batch_id
        LEFT JOIN wh_fba_allocation fa ON fa.id = batch_detail.apv_id
        LEFT JOIN wh_fba_allocation_item fai ON fai.fba_id = fa.id
        WHERE 1 = 1
        <[AND fa.id = :id]>
        <[AND fa.id IN (:apv_id_list)]>
        <[AND fa.fba_no = :apv_no]>
        <[AND fa.status = :status]>
        <[AND apv_batch.serial_number = :serial_number]>
        <[:NOT_IN_APV_ID_LIST]>

      ]]>
    </content>
  </sql>
  
</sqlmap>