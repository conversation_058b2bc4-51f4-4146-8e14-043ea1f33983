<?xml version="1.0" encoding="UTF-8" ?>
<sqlmap >
  <sql datasource="dataSource" id="queryApvTrackCount" >
    <content >
      <![CDATA[
        SELECT COUNT(1)
        FROM apv_track
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND apv_no = :apv_no]>
        <[AND apv_no IN (:apv_no_list)]>
        <[AND task_no = :task_no]>
        <[AND pick_user = :pick_user]>
        <[AND pick_finish_time = :pick_finish_time]>
        <[AND sow_user = :sow_user]>
        <[AND sow_finish_time = :sow_finish_time]>
        <[AND pack_user = :pack_user]>
        <[AND pack_finish_time = :pack_finish_time]>
        <[AND deliver_user = :deliver_user]>
        <[AND deliver_time = :deliver_time]>
        <[AND deliver_time >= :from_deliver_time]>
        <[AND deliver_time <= :to_deliver_time]>
        <[AND merge_user = :merge_user]>
        <[AND merge_time = :merge_time]>
        <[AND remark = :remark]>
        <[AND creation_date = :creation_date]>
        <[AND load_user = :load_user]>
        <[AND load_date = :load_date]>
        
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryApvTrackList" >
    <content >
      <![CDATA[
        SELECT id, apv_no, task_no, pick_user, pick_finish_time, sow_user, sow_finish_time, 
        pack_user, pack_finish_time, deliver_user, deliver_time, merge_user, merge_time, remark, creation_date,
        load_user, load_date, cancel_deliver_time, split_apv_no
        FROM apv_track
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND apv_no = :apv_no]>
        <[AND apv_no IN (:apv_no_list)]>
        <[AND task_no = :task_no]>
        <[AND pick_user = :pick_user]>
        <[AND pick_finish_time = :pick_finish_time]>
        <[AND sow_user = :sow_user]>
        <[AND sow_finish_time = :sow_finish_time]>
        <[AND pack_user = :pack_user]>
        <[AND pack_finish_time = :pack_finish_time]>
        <[AND deliver_user = :deliver_user]>
        <[AND deliver_time = :deliver_time]>
        <[AND deliver_time >= :from_deliver_time]>
        <[AND deliver_time <= :to_deliver_time]>
        <[AND merge_user = :merge_user]>
        <[AND merge_time = :merge_time]>
        <[AND remark = :remark]>
        <[AND creation_date = :creation_date]>
        <[AND load_user = :load_user]>
        <[AND load_date = :load_date]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryApvTrackByPrimaryKey" >
    <content >
      <![CDATA[
        SELECT id, apv_no, task_no, pick_user, pick_finish_time, sow_user, sow_finish_time, 
        pack_user, pack_finish_time, deliver_user, deliver_time, remark, creation_date,
        load_user, load_date, merge_user, merge_time, cancel_deliver_time, split_apv_no
        FROM apv_track
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryApvTrack" >
    <content >
      <![CDATA[
        SELECT id, apv_no, task_no, pick_user, pick_finish_time, sow_user, sow_finish_time, 
        pack_user, pack_finish_time, deliver_user, deliver_time, remark, creation_date,
        load_user, load_date, merge_user, merge_time, cancel_deliver_time, split_apv_no
        FROM apv_track
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND apv_no = :apv_no]>
        <[AND task_no = :task_no]>
        <[AND pick_user = :pick_user]>
        <[AND pick_finish_time = :pick_finish_time]>
        <[AND sow_user = :sow_user]>
        <[AND sow_finish_time = :sow_finish_time]>
        <[AND pack_user = :pack_user]>
        <[AND pack_finish_time = :pack_finish_time]>
        <[AND deliver_user = :deliver_user]>
        <[AND deliver_time = :deliver_time]>
        <[AND deliver_time >= :from_deliver_time]>
        <[AND deliver_time <= :to_deliver_time]>
        <[AND remark = :remark]>
        <[AND creation_date = :creation_date]>
        <[AND load_user = :load_user]>
        <[AND load_date = :load_date]>
        <[AND merge_user = :merge_user]>
        <[AND merge_time = :merge_time]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="createApvTrack" >
    <content >
      <![CDATA[
        INSERT INTO apv_track (apv_no, task_no, pick_user, pick_finish_time, sow_user, sow_finish_time, 
          pack_user, pack_finish_time, deliver_user, deliver_time, remark, creation_date, load_user, load_date, merge_user, merge_time,
          bill_weight_count,bill_weight_diff,estimated_weight_count,standard_weight_full, cancel_deliver_time, split_apv_no
          )
        VALUES (:apv_no, :task_no, :pick_user, :pick_finish_time, :sow_user, :sow_finish_time, 
          :pack_user, :pack_finish_time, :deliver_user, :deliver_time, :remark, :creation_date, :load_user, :load_date, :merge_user, :merge_time,
          :bill_weight_count, :bill_weight_diff, :estimated_weight_count,:standard_weight_full, :cancel_deliver_time, :split_apv_no
          )
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="deleteApvTrackByPrimaryKey" >
    <content >
      <![CDATA[
        DELETE FROM apv_track
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="deleteApvTrackByApvNo" >
    <content >
      <![CDATA[
        DELETE FROM apv_track
        WHERE 1 = 1
        AND apv_no = :apv_no
      ]]>
    </content>
  </sql>

  <sql datasource="dataSource" id="deleteApvTrackByApvNos" >
    <content >
      <![CDATA[
        DELETE FROM apv_track
        WHERE 1 = 1
        AND apv_no IN <[:ApvNos]>
      ]]>
    </content>
  </sql>

  <sql datasource="dataSource" id="deleteApvTrackByIds" >
    <content >
      <![CDATA[
        DELETE FROM apv_track
        WHERE 1 = 1
        AND id IN <[:Ids]>
      ]]>
    </content>
  </sql>

  <sql datasource="dataSource" id="updateApvTrackByPrimaryKey" >
    <content >
      <![CDATA[
        UPDATE apv_track
        SET <[apv_no = :apv_no,]>
          <[task_no = :task_no,]>
          <[pick_user = :pick_user,]>
          <[pick_finish_time = :pick_finish_time,]>
          <[sow_user = :sow_user,]>
          <[sow_finish_time = :sow_finish_time,]>
          <[pack_user = :pack_user,]>
          <[pack_finish_time = :pack_finish_time,]>
          <[deliver_user = :deliver_user,]>
          <[deliver_time = :deliver_time,]>
          <[remark = :remark,]>
          <[creation_date = :creation_date,]>
          <[load_user = :load_user,]>
          <[load_date = :load_date,]>
          <[merge_user = :merge_user,]>
          <[merge_time = :merge_time,]>
          <[length = :length,]>
          <[width = :width,]>
          <[height = :height,]>
          <[estimated_weight_count = :estimated_weight_count,]>
          <[bill_weight_count = :bill_weight_count,]>
          <[bill_weight_diff = :bill_weight_diff,]>
          <[retry_number= :retry_number,]>
          <[standard_weight_full = :standard_weight_full,]>
          <[cancel_deliver_time = :cancel_deliver_time,]>
          <[split_apv_no = :split_apv_no,]>
        id = id
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  
  <sql datasource="dataSource" id="updateApvTrackByByApvNo" >
    <content >
      <![CDATA[
        UPDATE apv_track
        SET 
          <[task_no = :task_no,]>
          <[pick_user = :pick_user,]>
          <[pick_finish_time = :pick_finish_time,]>
          <[sow_user = :sow_user,]>
          <[sow_finish_time = :sow_finish_time,]>
          <[pack_user = :pack_user,]>
          <[pack_finish_time = :pack_finish_time,]>
          <[deliver_user = :deliver_user,]>
          <[deliver_time = :deliver_time,]>
          <[remark = :remark,]>
          <[creation_date = :creation_date,]>
          <[load_user = :load_user,]>
          <[load_date = :load_date,]>
          <[merge_user = :merge_user,]>
          <[merge_time = :merge_time,]>
          <[is_over_standard_weight_diff = :is_over_standard_weight_diff,]>
          <[standard_weight_count = :standard_weight_count,]>
          <[standard_weight_diff = :standard_weight_diff,]>
          <[goods_weight_count = :goods_weight_count,]>
          <[goods_weight_diff = :goods_weight_diff,]>
          <[calculated_standard_weight = :calculated_standard_weight,]>
          <[length = :length,]>
          <[width = :width,]>
          <[height = :height,]>
          <[weighing_intercept_scan_user = :weighing_intercept_scan_user,]>
          <[weighing_intercept_scan_time = :weighing_intercept_scan_time,]>
          <[bill_weight_count = :bill_weight_count,]>
          <[bill_weight_diff = :bill_weight_diff,]>
          <[standard_weight_full = :standard_weight_full,]>
          <[cancel_deliver_time = :cancel_deliver_time,]>
          <[split_apv_no = :split_apv_no,]>
        id = id
        WHERE 1 = 1
        AND apv_no = :apv_no
      ]]>
    </content>
  </sql>
  
  
  <sql datasource="dataSource" id="queryDeliverQuantityByDate">
  	<content>
  		<![CDATA[
  			select count(distinct z.apv_id)
			from(
			select apvi.apv_id,sku.warehouse_id
			from wh_apv_item  apvi
			inner join wh_apv apv on apvi.apv_id=apv.id
			left join wh_sku sku on apvi.sku=sku.sku
			inner join (
				select distinct apvt.apv_no from apv_track apvt 
			  where 1=1
			  <[AND deliver_time >= :from_deliver_time]>
        	  <[AND deliver_time <= :to_deliver_time]>
			) apvc on apv.apv_no=apvc.apv_no
			where 1=1
			<[AND apv.status = :status]>
			group by apvi.apv_id having  COUNT(DISTINCT sku.warehouse_id) <[:quantity]>)z
			where 1=1 
			<[AND z.warehouse_id = :warehouseId]>
  		]]>
  	</content>
  </sql>
  
  <sql datasource="dataSource" id="queryDeliverQuantityByWarehouse">
  	<content>
  		<![CDATA[
			select count(distinct apvt.apv_no) 
			from apv_track apvt 
			left join wh_apv apv on apvt.apv_no=apv.apv_no
			where 1=1
		    <[AND deliver_time >= :from_deliver_time]>
       	    <[AND deliver_time <= :to_deliver_time]>
            <[AND apv.status = :status]>		
  		]]>
  	</content>
  </sql>

    <!-- 首页获取wms推送量，交运量-->
  <sql datasource="dataSource" id="getWmsPushQuantityAndDeliverQuantityByCondition">
      <content>
          <![CDATA[
            select COUNT(apv.apv_no) as createQty,
            COUNT(if(apv.status = 17,true,null)) as deliverQty,
            COUNT(if(apv.status = 2,true,null)) as cancelQty,
            COUNT(if(apv.status = 18, true, NULL)) AS loadedQty,
            DATE(apv.creation_date) as date
            FROM wh_apv apv
            WHERE 1 = 1
            AND creation_date BETWEEN :from_creation_date AND :to_creation_date
            <[AND apv.id IN (SELECT inner_item.apv_id from wh_apv_item inner_item LEFT JOIN wh_sku inner_sku ON inner_item.sku = inner_sku.sku where inner_sku.warehouse_id = :warehouse_id)]>
            <[:SINGLE_WAREHOUSE]>
            <[:MULTI_WAREHOUSE]>
            GROUP by date
          ]]>
      </content>
  </sql>
  <!-- 获取wms 已交运 apvNo   -->
   <sql datasource="dataSource" id="queryWmsDeliverApvNo">
  	<content>
  		<![CDATA[
  			select distinct z.apv_no
			from(
			select apv.apv_no,sku.warehouse_id
			from wh_apv_item  apvi
			inner join wh_apv apv on apvi.apv_id=apv.id
			left join wh_sku sku on apvi.sku=sku.sku
			inner join (
				select distinct apvt.apv_no from apv_track apvt 
			  where 1=1
			  <[AND deliver_time >= :from_deliver_time]>
        	  <[AND deliver_time <= :to_deliver_time]>
			) apvc on apv.apv_no=apvc.apv_no
			where 1=1
			<[AND apv.status = :status]>
			<[:quantity]>)z
			where 1=1 
			<[AND z.warehouse_id = :warehouseId]>
  		]]>
  	</content>
  </sql>

  <!-- 获取wms 已交运 apvNo   -->
  <sql datasource="dataSource" id="queryApvTrackBillList">
    <content>
      <![CDATA[
		  SELECT
              track.id,
              track.apv_no,
              track.bill_weight_count,
              apv.tracking_number,
              apv.actual_weight
          FROM
              apv_track track
              LEFT JOIN wh_apv apv ON apv.apv_no = track.apv_no
          WHERE 1=1
          <[AND apv.status IN (:statusList)]>
           <[AND track.apv_no IN (:apvNoList)]>
          <[AND deliver_time >= :from_deliver_time]>
          <[AND deliver_time <= :to_deliver_time]>
  		]]>
    </content>
  </sql>

  <sql datasource="dataSource" id="queryApvTrackBillCount">
    <content>
      <![CDATA[
		  SELECT
              track.apv_no
          FROM
              apv_track track
              LEFT JOIN wh_apv apv ON apv.apv_no = track.apv_no
          WHERE 1=1
          <[AND apv.status IN (:statusList)]>
          <[AND deliver_time >= :from_deliver_time]>
          <[AND deliver_time <= :to_deliver_time]>
  		]]>
    </content>
  </sql>

    <sql datasource="dataSource" id="queryApvTrackByTrackingNumber">
        <content>
            <![CDATA[
                SELECT
                    track.id,
                    track.apv_no,
                    track.task_no,
                    track.pick_user,
                    track.pick_finish_time,
                    track.sow_user,
                    track.sow_finish_time,
                    track.pack_user,
                    track.pack_finish_time,
                    track.deliver_user,
                    track.deliver_time,
                    track.remark,
                    track.creation_date,
                    track.load_user,
                    track.load_date,
                    track.merge_user,
                    track.merge_time,
                    track.cancel_deliver_time
                FROM
                    wh_apv apv
                    LEFT JOIN apv_track track ON apv.apv_no = track.apv_no
                WHERE 1=1
                <[AND apv.tracking_number = :trackingNumber]>
  		    ]]>
        </content>
    </sql>

  <sql datasource="dataSource" id="queryApvTrackByTrackingNumberList">
    <content>
      <![CDATA[
                SELECT
                    track.id,
                    track.apv_no,
                    track.bill_weight_count,
                    apv.tracking_number,
                    apv.actual_weight
                FROM
                    apv_track track
                    LEFT JOIN wh_apv apv ON apv.apv_no = track.apv_no
                WHERE 1=1
                <[AND apv.tracking_number in (:trackingNumberList)]>
                <[:SALE_CHANNEL]>
  		    ]]>
    </content>
  </sql>

  <sql datasource="dataSource" id="updateOverseaReturnTimeBatch">
    <content>
      <![CDATA[
        update apv_track
        set oversea_return_time = :oversea_return_time
        where apv_no in (:apvNoList)
      ]]>
    </content>
  </sql>
  
</sqlmap>